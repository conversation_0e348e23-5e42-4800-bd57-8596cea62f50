# build
FROM public.ecr.aws/z5y1f1y8/maven:3.8.7-amazoncorretto-17 as builder

ARG TARGETPLATFORM
ARG BUILDPLATFORM
RUN echo "Building on $BUILDPLATFORM, for $TARGETPLATFORM"

COPY ./pom.xml /return-api/
WORKDIR /return-api/
COPY ./.mvn /return-api/.mvn
RUN aws s3 cp s3://deployment.lenskartprod.internal/settings.xml /root/.m2/


RUN aws s3 cp s3://deployment.lenskartprod.internal/newrelic-java-latest.zip .
RUN unzip newrelic-java-latest.zip


COPY . .
RUN --mount=type=cache,target=/root/.m2 mvn clean install -U -DskipTests --settings .mvn/custom-settings.xml

# run
FROM public.ecr.aws/z5y1f1y8/maven:3.8.7-amazoncorretto-17

RUN mkdir /return-api

COPY --from=builder /return-api/target/*.jar /return-api/
COPY --from=builder /return-api/newrelic/ /return-api/newrelic/
RUN chown -R 1000 /return-api && chmod 777 return-api

EXPOSE 8080
WORKDIR /return-api/
ENTRYPOINT ["sh", "-c", "java -Djava.security.egd=file:/dev/./urandom -javaagent:/return-api/newrelic/newrelic.jar -Dserver.port=8080 -Dspring.profiles.active=$PROFILE $JVM_ARGS -Duser.timezone=Asia/Kolkata -jar /return-api/*.jar"]
