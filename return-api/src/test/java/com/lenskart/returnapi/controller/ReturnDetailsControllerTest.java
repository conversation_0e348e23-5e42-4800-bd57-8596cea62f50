package com.lenskart.returnapi.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import ch.qos.logback.core.util.COWArrayList;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returncommon.model.dto.FilterOptionsDTO;
import com.lenskart.returncommon.model.dto.GetDelayedPickupOrdersRequest;
import com.lenskart.returncommon.model.dto.GetDelayedPickupOrdersResponse;
import com.lenskart.returncommon.model.dto.GetReturnRefundMappingResponse;
import com.lenskart.returncommon.model.dto.NeedApprovalRequest;
import com.lenskart.returncommon.model.dto.RefundMethodRequestByMagentoDto;
import com.lenskart.returncommon.model.dto.RefundMethodsResponse;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.insurance.InsuranceEligibilityRequest;
import com.lenskart.returncommon.model.dto.insurance.InsuranceEligibilityResponse;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.GetReturnDetailsRequest;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequest;
import com.lenskart.returncommon.model.response.D365ReturnTrackingResponse;
import com.lenskart.returncommon.model.response.GetFiltersResponse;
import com.lenskart.returncommon.model.response.ItemResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returncommon.model.response.ReturnReasonErrorResponse;
import com.lenskart.returncommon.model.response.ReturnReasonsResponse;
import com.lenskart.returncommon.model.response.ReturnRefundEligibilityResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.ReturnOrderAddressUpdateRepository;
import com.lenskart.returnrepository.repository.ReversePickupPincodeRepository;
import com.lenskart.returnservice.service.IAwaitedRtoService;
import com.lenskart.returnservice.service.ID365ReturnTrackingService;
import com.lenskart.returnservice.service.IExchangeRefundMethodService;
import com.lenskart.returnservice.service.INeedApprovalProcessorService;
import com.lenskart.returnservice.service.IRefundUtilsService;
import com.lenskart.returnservice.service.IReturnDetailsService;
import com.lenskart.returnservice.service.IReturnFlowResponseService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReturnOrderAddressUpdateService;
import com.lenskart.returnservice.service.IReturnOrderService;
import com.lenskart.returnservice.service.IReturnReasonService;
import com.lenskart.returnservice.service.IReturnRefundEligibilityService;
import com.lenskart.returnservice.service.IReturnRefundRuleService;
import com.lenskart.returnservice.service.IReturnSubHeaderTimelineDateFetchService;
import com.lenskart.returnservice.service.IReturnUpdateService;
import com.lenskart.returnservice.service.IReverseCourierDetailService;
import com.lenskart.returnservice.service.IReversePickUpService;
import com.lenskart.returnservice.service.impl.InsuranceServiceImpl;
import com.lenskart.returnservice.service.impl.NeedApprovalProcessorServiceImpl;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {ReturnDetailsController.class})
@ExtendWith(SpringExtension.class)
public class ReturnDetailsControllerTest {

    @Mock
    private IReturnOrderService returnOrderService;

    @InjectMocks
    private ReturnDetailsController returnDetailsController;

    @Mock
    private InsuranceServiceImpl insuranceService;

    @Mock
    private IExchangeRefundMethodService exchangeRefundMethodService;

    @Mock
    private NeedApprovalProcessorServiceImpl needApprovalProcessorService;

    private RefundMethodsResponse refundMethodsResponse;

    private ReturnRefundEligibilityRequest eligibilityRequest;

    private ReturnRefundEligibilityResponse eligibilityResponse;

    private List<ItemResponse> itemResponses;

    private ReturnEvent returnEvent;

    @MockBean
    private IReversePickUpService iReversePickUpService;

    @MockBean
    private IReverseCourierDetailService iReverseCourierDetailService;

    @MockBean
    private ID365ReturnTrackingService iD365ReturnTrackingService;

    @MockBean
    private IReturnDetailsService iReturnDetailsService;

    @MockBean
    private IReturnReasonService iReturnReasonService;

    @MockBean
    private IReturnOrderActionService iReturnOrderActionService;

    @MockBean
    private IAwaitedRtoService iAwaitedRtoService;

    @MockBean
    private IExchangeRefundMethodService iExchangeRefundMethodService;

    @MockBean
    private INeedApprovalProcessorService iNeedApprovalProcessorService;

    @MockBean
    private IRefundUtilsService iRefundUtilsService;

    @MockBean
    private IReturnFlowResponseService iReturnFlowResponseService;

    @MockBean
    private IReturnOrderAddressUpdateService iReturnOrderAddressUpdateService;

    @MockBean
    private IReturnOrderService iReturnOrderService;

    @MockBean
    private IReturnRefundEligibilityService iReturnRefundEligibilityService;

    @MockBean
    private IReturnRefundRuleService iReturnRefundRuleService;

    @MockBean
    private IReturnSubHeaderTimelineDateFetchService iReturnSubHeaderTimelineDateFetchService;

    @MockBean
    private IReturnUpdateService iReturnUpdateService;

    @MockBean
    private InsuranceServiceImpl insuranceServiceImpl;

    @MockBean
    private ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @MockBean
    private ReversePickupPincodeRepository reversePickupPincodeRepository;

    /**
     * Tests the {@link ReturnDetailsController#getReturnOrder(String)} method.
     */
    @Test
    void testGetReturnOrder_WithValidOrderId() {
        Integer validOrderId = 1;
        ReturnOrderDTO expectedReturnOrder = new ReturnOrderDTO();
        List<ReturnOrderDTO> expectedReturnOrders = Arrays.asList(expectedReturnOrder);

        when(returnOrderService.getReturnDetails(validOrderId)).thenReturn(expectedReturnOrders);

        ResponseEntity<List<ReturnOrderDTO>> actualResponse = returnDetailsController
                .getReturnOrder(String.valueOf(validOrderId));

        assertNotNull(actualResponse.getBody());
        assertEquals(HttpStatus.OK, actualResponse.getStatusCode());
        assertEquals(expectedReturnOrders.size(), actualResponse.getBody().size());
        verify(returnOrderService).getReturnDetails(validOrderId);
    }

    @Test
    void testGetReturnOrder_WithBlankOrderId() {
        String blankOrderId = "";

        ResponseEntity<List<ReturnOrderDTO>> actualResponse = returnDetailsController.getReturnOrder(blankOrderId);

        assertNull(actualResponse.getBody());
        assertEquals(HttpStatus.BAD_REQUEST, actualResponse.getStatusCode());
    }

    @Test
    void testGetReturnOrder_WithExceptionThrownByService() {

        when(returnOrderService.getReturnDetails(111)).thenThrow(new IllegalArgumentException("Error"));
        ResponseEntity<List<ReturnOrderDTO>> actualResponse = returnDetailsController.getReturnOrder("111");
        assertNull(actualResponse.getBody());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, actualResponse.getStatusCode());

    }

    /**
     * Tests the {@link ReturnDetailsController#verifyInsuranceEligibilty(InsuranceEligibilityRequest, Integer)} method.
     */
    @Test
    void testVerifyInsuranceEligibilty_WithValidRequest() {
        int validIncrementId = 1;
        InsuranceEligibilityRequest insuranceEligibilityRequest = new InsuranceEligibilityRequest();
        InsuranceEligibilityResponse insuranceEligibilityResponse = new InsuranceEligibilityResponse();
        insuranceEligibilityResponse.setIncrementId(validIncrementId);
        when(insuranceService.getInsurancEligibilityDetailsForItems(validIncrementId, insuranceEligibilityRequest))
                .thenReturn(insuranceEligibilityResponse);
        ResponseEntity<InsuranceEligibilityResponse> actualResponse = returnDetailsController
                .verifyInsuranceEligibilty(insuranceEligibilityRequest, validIncrementId);

        assertNotNull(actualResponse.getBody());
        assertEquals(HttpStatus.OK, actualResponse.getStatusCode());
        verify(insuranceService).getInsurancEligibilityDetailsForItems(validIncrementId, insuranceEligibilityRequest);
    }

    @Test
    void testVerifyInsuranceEligibilty_WithBadRequest() {
        int validIncrementId = 1;
        InsuranceEligibilityRequest insuranceEligibilityRequest = new InsuranceEligibilityRequest();
        InsuranceEligibilityResponse insuranceEligibilityResponse = new InsuranceEligibilityResponse();
        insuranceEligibilityResponse
                .setErrorMessage("Invalid Request: IncrementId or MagentoItemIds are not in the request");
        when(insuranceService.getInsurancEligibilityDetailsForItems(validIncrementId, insuranceEligibilityRequest))
                .thenReturn(insuranceEligibilityResponse);
        ResponseEntity<InsuranceEligibilityResponse> actualResponse = returnDetailsController
                .verifyInsuranceEligibilty(insuranceEligibilityRequest, validIncrementId);

        assertNotNull(actualResponse.getBody());
        assertEquals(HttpStatus.BAD_REQUEST, actualResponse.getStatusCode());
        verify(insuranceService).getInsurancEligibilityDetailsForItems(validIncrementId, insuranceEligibilityRequest);
    }

    @Test
    public void testGetDelayedPickupOrdersEmptyRequest() {

        GetDelayedPickupOrdersRequest delayedRequest = new GetDelayedPickupOrdersRequest();
        delayedRequest.setIncrementIds(Collections.emptyList());
        ResponseEntity<GetDelayedPickupOrdersResponse> actualResponse = returnDetailsController
                .getDelayedPickupOrders(delayedRequest);
        assertNull(actualResponse.getBody());
        assertEquals(HttpStatus.BAD_REQUEST, actualResponse.getStatusCode());

    }

    @Test
    public void testGetDelayedPickupOrdersSuccess() throws Exception {
        GetDelayedPickupOrdersRequest delayedRequest = new GetDelayedPickupOrdersRequest();
        delayedRequest.setIncrementIds(List.of(1, 2));

        GetDelayedPickupOrdersResponse delayedResponse = new GetDelayedPickupOrdersResponse();

        when(returnOrderService.getDelayedPickupOrders(any(GetDelayedPickupOrdersRequest.class)))
                .thenReturn(delayedResponse);
        ResponseEntity<GetDelayedPickupOrdersResponse> actualResponse = returnDetailsController
                .getDelayedPickupOrders(delayedRequest);
        assertEquals(HttpStatus.OK, actualResponse.getStatusCode());

    }

    /**
     * Test {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()} add two.</li>
     * </ul>
     * <p>
     * Method under test: {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}
     */
    @Test
    @DisplayName("Test getDelayedPickupOrders(GetDelayedPickupOrdersRequest); given ArrayList() add two")
    @Tag("MaintainedByDiffblue")
    void testGetDelayedPickupOrders_givenArrayListAddTwo() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
        //   Run dcover create --keep-partial-tests to gain insights into why
        //   a non-Spring test was created.

        // Arrange
        ReturnDetailsController returnDetailsController = new ReturnDetailsController();

        ArrayList<Integer> incrementIds = new ArrayList<>();
        incrementIds.add(2);

        GetDelayedPickupOrdersRequest delayedRequest = new GetDelayedPickupOrdersRequest();
        delayedRequest.setIncrementIds(incrementIds);

        // Act
        ResponseEntity<GetDelayedPickupOrdersResponse> actualDelayedPickupOrders = returnDetailsController
                .getDelayedPickupOrders(delayedRequest);

        // Assert
        HttpStatusCode statusCode = actualDelayedPickupOrders.getStatusCode();
        assertTrue(statusCode instanceof HttpStatus);
        assertNull(actualDelayedPickupOrders.getBody());
        assertEquals(500, actualDelayedPickupOrders.getStatusCodeValue());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, statusCode);
        assertFalse(actualDelayedPickupOrders.hasBody());
        assertTrue(actualDelayedPickupOrders.getHeaders().isEmpty());
    }

    /**
     * Test {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}.
     * <ul>
     *   <li>Given {@link ArrayList#ArrayList()}.</li>
     *   <li>Then return StatusCodeValue is four hundred.</li>
     * </ul>
     * <p>
     * Method under test: {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}
     */
    @Test
    @DisplayName("Test getDelayedPickupOrders(GetDelayedPickupOrdersRequest); given ArrayList(); then return StatusCodeValue is four hundred")
    @Tag("MaintainedByDiffblue")
    void testGetDelayedPickupOrders_givenArrayList_thenReturnStatusCodeValueIsFourHundred() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
        //   Run dcover create --keep-partial-tests to gain insights into why
        //   a non-Spring test was created.

        // Arrange
        ReturnDetailsController returnDetailsController = new ReturnDetailsController();

        GetDelayedPickupOrdersRequest delayedRequest = new GetDelayedPickupOrdersRequest();
        delayedRequest.setIncrementIds(new ArrayList<>());

        // Act
        ResponseEntity<GetDelayedPickupOrdersResponse> actualDelayedPickupOrders = returnDetailsController
                .getDelayedPickupOrders(delayedRequest);

        // Assert
        HttpStatusCode statusCode = actualDelayedPickupOrders.getStatusCode();
        assertTrue(statusCode instanceof HttpStatus);
        assertNull(actualDelayedPickupOrders.getBody());
        assertEquals(400, actualDelayedPickupOrders.getStatusCodeValue());
        assertEquals(HttpStatus.BAD_REQUEST, statusCode);
        assertFalse(actualDelayedPickupOrders.hasBody());
        assertTrue(actualDelayedPickupOrders.getHeaders().isEmpty());
    }

    /**
     * Test {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}.
     * <ul>
     *   <li>Given {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}
     */
    @Test
    @DisplayName("Test getDelayedPickupOrders(GetDelayedPickupOrdersRequest); given 'null'")
    @Tag("MaintainedByDiffblue")
    void testGetDelayedPickupOrders_givenNull() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
        //   Run dcover create --keep-partial-tests to gain insights into why
        //   a non-Spring test was created.

        // Arrange
        ReturnDetailsController returnDetailsController = new ReturnDetailsController();

        GetDelayedPickupOrdersRequest delayedRequest = new GetDelayedPickupOrdersRequest();
        delayedRequest.setIncrementIds(null);

        // Act
        ResponseEntity<GetDelayedPickupOrdersResponse> actualDelayedPickupOrders = returnDetailsController
                .getDelayedPickupOrders(delayedRequest);

        // Assert
        HttpStatusCode statusCode = actualDelayedPickupOrders.getStatusCode();
        assertTrue(statusCode instanceof HttpStatus);
        assertNull(actualDelayedPickupOrders.getBody());
        assertEquals(500, actualDelayedPickupOrders.getStatusCodeValue());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, statusCode);
        assertFalse(actualDelayedPickupOrders.hasBody());
        assertTrue(actualDelayedPickupOrders.getHeaders().isEmpty());
    }

    /**
     * Test {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}.
     * <ul>
     *   <li>Then calls {@link COWArrayList#isEmpty()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)}
     */
    @Test
    @DisplayName("Test getDelayedPickupOrders(GetDelayedPickupOrdersRequest); then calls isEmpty()")
    @Tag("MaintainedByDiffblue")
    void testGetDelayedPickupOrders_thenCallsIsEmpty() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
        //   Run dcover create --keep-partial-tests to gain insights into why
        //   a non-Spring test was created.

        // Arrange
        ReturnDetailsController returnDetailsController = new ReturnDetailsController();
        COWArrayList<Integer> incrementIds = mock(COWArrayList.class);
        when(incrementIds.isEmpty()).thenThrow(new RuntimeException("foo"));

        GetDelayedPickupOrdersRequest delayedRequest = new GetDelayedPickupOrdersRequest();
        delayedRequest.setIncrementIds(incrementIds);

        // Act
        ResponseEntity<GetDelayedPickupOrdersResponse> actualDelayedPickupOrders = returnDetailsController
                .getDelayedPickupOrders(delayedRequest);

        // Assert
        verify(incrementIds).isEmpty();
        HttpStatusCode statusCode = actualDelayedPickupOrders.getStatusCode();
        assertTrue(statusCode instanceof HttpStatus);
        assertNull(actualDelayedPickupOrders.getBody());
        assertEquals(500, actualDelayedPickupOrders.getStatusCodeValue());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, statusCode);
        assertFalse(actualDelayedPickupOrders.hasBody());
        assertTrue(actualDelayedPickupOrders.getHeaders().isEmpty());
    }

    @Test
    public void testFindExchangeRefundMethod_Success() throws Exception {
        initializeRefundMethodMocks();
        when(exchangeRefundMethodService.findExchangeRefundMethod(123, "source", null)).thenReturn(refundMethodsResponse);

        ResponseEntity<RefundMethodsResponse> response = returnDetailsController.findExchangeRefundMethod(123, "source");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(refundMethodsResponse, response.getBody());
    }

    /**
     * Test {@link ReturnDetailsController#findExchangeRefundMethod(Integer, String)}.
     * <ul>
     *   <li>When {@code null}.</li>
     *   <li>Then return StatusCodeValue is four hundred.</li>
     * </ul>
     * <p>
     * Method under test: {@link ReturnDetailsController#findExchangeRefundMethod(Integer, String)}
     */
    @Test
    @DisplayName("Test findExchangeRefundMethod(Integer, String); when 'null'; then return StatusCodeValue is four hundred")
    @Tag("MaintainedByDiffblue")
    void testFindExchangeRefundMethod_whenNull_thenReturnStatusCodeValueIsFourHundred() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
        //   Run dcover create --keep-partial-tests to gain insights into why
        //   a non-Spring test was created.

        // Arrange and Act
        ResponseEntity<RefundMethodsResponse> actualFindExchangeRefundMethodResult = (new ReturnDetailsController())
                .findExchangeRefundMethod(null, "Source");

        // Assert
        HttpStatusCode statusCode = actualFindExchangeRefundMethodResult.getStatusCode();
        assertTrue(statusCode instanceof HttpStatus);
        RefundMethodsResponse body = actualFindExchangeRefundMethodResult.getBody();
        assertNull(body.getIsExchange());
        assertNull(body.getMagentoItemId());
        assertNull(body.getRefundMethods());
        assertEquals(400, actualFindExchangeRefundMethodResult.getStatusCodeValue());
        assertEquals(HttpStatus.BAD_REQUEST, statusCode);
        assertTrue(actualFindExchangeRefundMethodResult.hasBody());
        assertTrue(actualFindExchangeRefundMethodResult.getHeaders().isEmpty());
    }

    /**
     * Test {@link ReturnDetailsController#findExchangeRefundMethod(Integer, String)}.
     * <ul>
     *   <li>When one.</li>
     *   <li>Then return StatusCodeValue is five hundred.</li>
     * </ul>
     * <p>
     * Method under test: {@link ReturnDetailsController#findExchangeRefundMethod(Integer, String)}
     */
    @Test
    @DisplayName("Test findExchangeRefundMethod(Integer, String); when one; then return StatusCodeValue is five hundred")
    @Tag("MaintainedByDiffblue")
    void testFindExchangeRefundMethod_whenOne_thenReturnStatusCodeValueIsFiveHundred() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.
        //   Run dcover create --keep-partial-tests to gain insights into why
        //   a non-Spring test was created.

        // Arrange and Act
        ResponseEntity<RefundMethodsResponse> actualFindExchangeRefundMethodResult = (new ReturnDetailsController())
                .findExchangeRefundMethod(1, "Source");

        // Assert
        HttpStatusCode statusCode = actualFindExchangeRefundMethodResult.getStatusCode();
        assertTrue(statusCode instanceof HttpStatus);
        RefundMethodsResponse body = actualFindExchangeRefundMethodResult.getBody();
        assertNull(body.getIsExchange());
        assertNull(body.getMagentoItemId());
        assertNull(body.getRefundMethods());
        assertEquals(500, actualFindExchangeRefundMethodResult.getStatusCodeValue());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, statusCode);
        assertTrue(actualFindExchangeRefundMethodResult.hasBody());
        assertTrue(actualFindExchangeRefundMethodResult.getHeaders().isEmpty());
    }

    @Test
    public void testFindExchangeRefundMethod_BadRequest() {
        ResponseEntity<RefundMethodsResponse> response = returnDetailsController.findExchangeRefundMethod(null, "source");

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(new RefundMethodsResponse(), response.getBody());
    }

//    @Test
//    public void testFindExchangeRefundMethod_InternalServerError() throws Exception {
//        initializeRefundMethodMocks();
//
//        when(exchangeRefundMethodService.findExchangeRefundMethod(anyInt(), anyString(), null))
//                .thenThrow(new RuntimeException("Test Exception"));
//
//        ResponseEntity<RefundMethodsResponse> response = returnDetailsController.findExchangeRefundMethod(123, "source");
//
//        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
//        assertEquals(refundMethodsResponse, response.getBody());
//    }

    /**
     * Tests the {@link ReturnDetailsController#getReturnOrder(String)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#getReturnOrder(String)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#verifyInsuranceEligibilty(InsuranceEligibilityRequest, Integer)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#getDelayedPickupOrders(GetDelayedPickupOrdersRequest)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#findExchangeRefundMethod(Integer, String)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#findExchangeRefundMethod(Integer, String)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#findExchangeRefundMethod(Integer, String)} method.
     */
    private void initializeRefundMethodMocks() {
        refundMethodsResponse = new RefundMethodsResponse();
        eligibilityRequest = new ReturnRefundEligibilityRequest();
        eligibilityResponse = new ReturnRefundEligibilityResponse();
        ItemResponse itemResponse = new ItemResponse();
        itemResponse.setRefundMethods(Collections.emptyList());
        itemResponse.setExchange(true);
        itemResponses = Collections.singletonList(itemResponse);
        eligibilityResponse.setItemsResponseList(itemResponses);
    }

    @Test
    public void testGetReturnRefundMappingSuccess() {
        Integer validIncrementId = 1;
        GetReturnRefundMappingResponse getReturnRefundMappingResponse = new GetReturnRefundMappingResponse();
        when(returnOrderService.getReturnRefundMapping(validIncrementId)).thenReturn(getReturnRefundMappingResponse);
        ResponseEntity<GetReturnRefundMappingResponse> actualResponse = returnDetailsController
                .getReturnRefundMapping(validIncrementId);
        assertEquals(HttpStatus.OK, actualResponse.getStatusCode());

    }

    @Test
    public void testGetReturnRefundMappingEmptyRequest() {
        Integer InvalidIncrementId = 0;
        ResponseEntity<GetReturnRefundMappingResponse> actualResponse = returnDetailsController
                .getReturnRefundMapping(InvalidIncrementId);
        assertEquals(HttpStatus.BAD_REQUEST, actualResponse.getStatusCode());

    }

    @Test
    public void testGetReturnRefundMapping_Exception() {
        Integer incrementId = 12345;
        when(returnOrderService.getReturnRefundMapping(incrementId)).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<GetReturnRefundMappingResponse> response = returnDetailsController
                .getReturnRefundMapping(incrementId);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    public void testProcessDelightActionUpdateOperations_Success() throws Exception {

        ReturnDetailsUpdateResponse returnDetailsResponse = new ReturnDetailsUpdateResponse();
        returnDetailsResponse.setStatus("Failure");
        returnEvent = new ReturnEvent();
        returnEvent.setReturnId(1);

        NeedApprovalRequest needApprovalRequest = new NeedApprovalRequest();
        ResponseEntity<Boolean> response = returnDetailsController
                .processDelightActionUpdateOperations(needApprovalRequest);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testProcessDelightActionUpdateOperations_Failure() throws Exception {
        NeedApprovalRequest needApprovalRequest = new NeedApprovalRequest();
        needApprovalRequest.setReturnId(1);
        needApprovalRequest.setStatus(ReturnStatus.RETURN_NEED_APPROVAL.getStatus());
        needApprovalRequest.setDelightAction(Constant.DELIGHT_ACTION_REJECT);
        ApprovalStatusRequest approvalStatusRequest = new ApprovalStatusRequest();
        needApprovalRequest.setApprovalStatusRequest(approvalStatusRequest);

        doThrow(new Exception()).when(needApprovalProcessorService).isSaveDelightActionProcessed(needApprovalRequest);

        ResponseEntity<Boolean> response = returnDetailsController
                .processDelightActionUpdateOperations(needApprovalRequest);

        assertEquals(HttpStatus.EXPECTATION_FAILED, response.getStatusCode());
    }

    /**
     * Test {@link ReturnDetailsController#getItemDetailsByReturnId(Integer)}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getItemDetailsByReturnId(Integer)}
     */
    @Test
    @DisplayName("Test getItemDetailsByReturnId(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetItemDetailsByReturnId() throws Exception {
        // Arrange
        when(iReturnOrderActionService.getItemDetailsByReturnId(Mockito.<Integer>any())).thenReturn(new HashMap<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/return/details/v1.0/getItemDetails/{return-id}", 1);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    /**
     * Test {@link ReturnDetailsController#getFollowReasonsList()}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getFollowReasonsList()}
     */
    @Test
    @DisplayName("Test getFollowReasonsList()")
    @Tag("MaintainedByDiffblue")
    void testGetFollowReasonsList() throws Exception {
        // Arrange
        ReturnReasonErrorResponse error_response = new ReturnReasonErrorResponse();
        error_response.setError_msg("An error occurred");

        ReturnReasonsResponse returnReasonsResponse = new ReturnReasonsResponse();
        returnReasonsResponse.setCategory("Category");
        returnReasonsResponse.setError_response(error_response);
        returnReasonsResponse.setPlatform("Platform");
        returnReasonsResponse.setResponse_status(HttpStatus.CONTINUE);
        returnReasonsResponse.setReturn_reasons(new ArrayList<>());
        when(iReturnReasonService.getApprovalReasons()).thenReturn(returnReasonsResponse);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/return/details/v1.0/api-1.0/reasons/approval");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"platform\":\"Platform\",\"category\":\"Category\",\"return_reasons\":[],\"error_response\":{\"error_msg\":\"An"
                                        + " error occurred\"},\"response_status\":\"CONTINUE\"}"));
    }

    /**
     * Test {@link ReturnDetailsController#getActiveLkartOrReversePickupCourier(Integer, boolean)}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getActiveLkartOrReversePickupCourier(Integer, boolean)}
     */
    @Test
    @DisplayName("Test getActiveLkartOrReversePickupCourier(Integer, boolean)")
    @Tag("MaintainedByDiffblue")
    void testGetActiveLkartOrReversePickupCourier() throws Exception {
        // Arrange
        when(iReversePickUpService.getActiveLkartOrReversePickupCourier(Mockito.<Integer>any(), anyBoolean()))
                .thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder getResult = MockMvcRequestBuilders
                .get("/return/details/v1.0/getReversePickupCouriers/{pin-code}", 1);
        MockHttpServletRequestBuilder requestBuilder = getResult.param("isActiveLkartCourierRequired",
                String.valueOf(true));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link ReturnDetailsController#getIsReturnFromNewFlow(String, String)} with {@code identifierType}, {@code identifierValue}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getIsReturnFromNewFlow(String, String)}
     */
    @Test
    @DisplayName("Test getIsReturnFromNewFlow(String, String) with 'identifierType', 'identifierValue'")
    @Tag("MaintainedByDiffblue")
    void testGetIsReturnFromNewFlowWithIdentifierTypeIdentifierValue() throws Exception {
        // Arrange
        when(iReturnOrderActionService.getIsReturnFromNewFlow(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new HashMap<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/return/details/v1.0/return-switch-active/{identifierType}/{identifierValue}", "Identifier Type", "42");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    /**
     * Test {@link ReturnDetailsController#getIsReturnFromNewFlow(List)} with {@code uwItemIds}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getIsReturnFromNewFlow(List)}
     */
    @Test
    @DisplayName("Test getIsReturnFromNewFlow(List) with 'uwItemIds'")
    @Tag("MaintainedByDiffblue")
    void testGetIsReturnFromNewFlowWithUwItemIds() throws Exception {
        // Arrange
        when(iReturnOrderActionService.getReturnReason(Mockito.<List<Integer>>any())).thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder contentTypeResult = MockMvcRequestBuilders
                .post("/return/details/v1.0/get/return-reason/")
                .contentType(MediaType.APPLICATION_JSON);

        ObjectMapper objectMapper = new ObjectMapper();
        MockHttpServletRequestBuilder requestBuilder = contentTypeResult
                .content(objectMapper.writeValueAsString(new ArrayList<>()));

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link ReturnDetailsController#getIdentifierValuesMappingWithNewFlow(GetReturnDetailsRequest, BindingResult)}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getIdentifierValuesMappingWithNewFlow(GetReturnDetailsRequest, BindingResult)}
     */
    @Test
    @DisplayName("Test getIdentifierValuesMappingWithNewFlow(GetReturnDetailsRequest, BindingResult)")
    @Tag("MaintainedByDiffblue")
    void testGetIdentifierValuesMappingWithNewFlow() throws Exception {
        // Arrange
        when(iReturnOrderActionService.getIdentifierValuesMappingWithNewFlow(Mockito.<String>any(),
                Mockito.<List<String>>any())).thenReturn(new HashMap<>());

        ArrayList<String> identifierValues = new ArrayList<>();
        identifierValues.add("Invalid Request");

        GetReturnDetailsRequest getReturnDetailsRequest = new GetReturnDetailsRequest();
        getReturnDetailsRequest.setIdentifierType("Identifier Type");
        getReturnDetailsRequest.setIdentifierValues(identifierValues);
        String content = (new ObjectMapper()).writeValueAsString(getReturnDetailsRequest);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/return/details/v1.0/get/identifier-values-mapping")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    /**
     * Test {@link ReturnDetailsController#getFilters()}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getFilters()}
     */
    @Test
    @DisplayName("Test getFilters()")
    @Tag("MaintainedByDiffblue")
    void testGetFilters() throws Exception {
        // Arrange
        FilterOptionsDTO filters = new FilterOptionsDTO();
        filters.setAgentEmail(new ArrayList<>());
        filters.setCategory(new ArrayList<>());
        filters.setCountry(new ArrayList<>());
        filters.setInsuranceOrderType(new ArrayList<>());
        filters.setOrderType(new ArrayList<>());
        filters.setQcStatus(new ArrayList<>());
        filters.setReturnReasonsPrimary(new ArrayList<>());
        filters.setReturnReasonsSecondary(new ArrayList<>());
        filters.setReturnStatus(new ArrayList<>());
        filters.setReturnType(new ArrayList<>());
        filters.setStoreType(new ArrayList<>());

        GetFiltersResponse getFiltersResponse = new GetFiltersResponse();
        getFiltersResponse.setError("An error occurred");
        getFiltersResponse.setFilters(filters);
        getFiltersResponse.setMessage("Not all who wander are lost");
        getFiltersResponse.setStatus(1);
        getFiltersResponse.setSuccess(true);
        when(iReturnDetailsService.getFilters()).thenReturn(getFiltersResponse);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/return/details/v1.0/get/filters");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"success\":true,\"status\":1,\"error\":\"An error occurred\",\"message\":\"Not all who wander are lost\","
                                        + "\"filters\":{\"returnType\":[],\"orderType\":[],\"returnStatus\":[],\"agentEmail\":[],\"returnReasonsPrimary\""
                                        + ":[],\"returnReasonsSecondary\":[],\"storeType\":[],\"insuranceOrderType\":[],\"country\":[],\"qcStatus\":[],"
                                        + "\"category\":[]}}"));
    }

    /**
     * Test {@link ReturnDetailsController#getCourierDetails(String, String)}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getCourierDetails(String, String)}
     */
    @Test
    @DisplayName("Test getCourierDetails(String, String)")
    @Tag("MaintainedByDiffblue")
    void testGetCourierDetails() throws Exception {
        // Arrange
        when(iReverseCourierDetailService.getCourierDetails(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get(
                "/return/details/v1.0/courier/details/{IDENTIFIER_TYPE}/{IDENTIFIER_VALUE}", "IDENTIFIER TYPE",
                "IDENTIFIER VALUE");

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Test {@link ReturnDetailsController#getD365ReturnTrackingDetails(Integer)}.
     * <p>
     * Method under test: {@link ReturnDetailsController#getD365ReturnTrackingDetails(Integer)}
     */
    @Test
    @DisplayName("Test getD365ReturnTrackingDetails(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetD365ReturnTrackingDetails() throws Exception {
        // Arrange
        D365ReturnTrackingResponse d365ReturnTrackingResponse = new D365ReturnTrackingResponse();
        d365ReturnTrackingResponse.setD365ReturnTracking(new ArrayList<>());
        when(iD365ReturnTrackingService.findByReturnId(Mockito.<Integer>any())).thenReturn(d365ReturnTrackingResponse);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/return/details/v1.0/d365/return-tracking/{return_id}", 1);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{\"d365ReturnTracking\":[]}"));
    }

    /**
     * Test {@link ReturnDetailsController#findExchangeRefundMethodUtil(RefundMethodRequestByMagentoDto)}.
     * <p>
     * Method under test: {@link ReturnDetailsController#findExchangeRefundMethodUtil(RefundMethodRequestByMagentoDto)}
     */
    @Test
    @DisplayName("Test findExchangeRefundMethodUtil(RefundMethodRequestByMagentoDto)")
    @Tag("MaintainedByDiffblue")
    void testFindExchangeRefundMethodUtil() throws Exception {
        // Arrange
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
        uwOrderDTO.setBarcode("Barcode");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setChannel("Channel");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
        uwOrderDTO.setFitting("Fitting");
        uwOrderDTO.setIncrementId(1);
        uwOrderDTO.setIsFranchise("Is Franchise");
        uwOrderDTO.setIsLocalFittingRequired(true);
        uwOrderDTO.setItemId(1);
        uwOrderDTO.setLensPackage("java.text");
        uwOrderDTO.setMethod("Method");
        uwOrderDTO.setNavChannel("Nav Channel");
        uwOrderDTO.setParentUw(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
        uwOrderDTO.setShipToStoreRequired(true);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
        uwOrderDTO.setShippingPackageId("42");
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
        uwOrderDTO.setUwItemId(1);
        uwOrderDTO.setVsmStockout(1);

        RefundMethodRequestByMagentoDto refundMethodRequestByMagentoDto = new RefundMethodRequestByMagentoDto();
        refundMethodRequestByMagentoDto.setSource("Source");
        refundMethodRequestByMagentoDto.setUwOrderDTO(uwOrderDTO);
        String content = (new ObjectMapper()).writeValueAsString(refundMethodRequestByMagentoDto);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/details/v1.0/refund/methods")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act
        ResultActions actualPerformResult = MockMvcBuilders.standaloneSetup(returnDetailsController)
                .build()
                .perform(requestBuilder);

        // Assert
        actualPerformResult.andExpect(MockMvcResultMatchers.status().is(400))
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string("{\"magentoItemId\":null,\"isExchange\":null,\"refundMethods\":null}"));
    }
    /**
     * Tests the {@link ReturnDetailsController#getReturnRefundMapping(Integer)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#getReturnRefundMapping(Integer)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#getReturnRefundMapping(Integer)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#processDelightActionUpdateOperations(NeedApprovalRequest)} method.
     */
    /**
     * Tests the {@link ReturnDetailsController#processDelightActionUpdateOperations(NeedApprovalRequest)} method.
     */
}
