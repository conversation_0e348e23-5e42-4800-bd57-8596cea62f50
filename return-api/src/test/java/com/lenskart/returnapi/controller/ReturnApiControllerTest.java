package com.lenskart.returnapi.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.CustomerAccountInfoDTO;
import com.lenskart.ordermetadata.dto.CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO.ExchangeOrdersDTOBuilder;
import com.lenskart.ordermetadata.dto.FraudCustomerDTO;
import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.ordermetadata.dto.ItemWiseFastRefunResponseDTO;
import com.lenskart.ordermetadata.dto.ReverseCourierMetaData;
import com.lenskart.ordermetadata.dto.request.ExchangeAddressDTO;
import com.lenskart.ordermetadata.dto.request.PickupAddressDTO;
import com.lenskart.ordermetadata.dto.request.ReturnSourcesDTO;
import com.lenskart.ordermetadata.dto.response.DispensingDTO;
import com.lenskart.ordermetadata.dto.response.ExchangeDetails;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails.OrderExchangeCancellationDetailsBuilder;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail.ShippingStatusDetailBuilder;
import com.lenskart.returncommon.model.dto.DualRefundRequest;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.returncommon.model.request.ReverseCourierUpdateRequestDTO;
import com.lenskart.returncommon.model.request.UpdateAgentEmailReturnRequest;
import com.lenskart.returncommon.model.request.UpdateReturnAgentRequest;
import com.lenskart.returncommon.model.request.UpdateReturnOrderItemRequest;
import com.lenskart.returncommon.model.request.UpdateReturnOrderItemRequest.UpdateReturnItemConditions;
import com.lenskart.returncommon.model.request.UpdateReturnReasonRequest;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO.OrderInfoResponseDTOBuilder;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returncommon.model.response.ReverseCourierUpdateResponse;
import com.lenskart.returnservice.service.IAwaitedRtoService;
import com.lenskart.returnservice.service.IInventoryReturnUpdateService;
import com.lenskart.returnservice.service.IReceivingService;
import com.lenskart.returnservice.service.IReturnInitiationService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReturnUpdateService;
import com.lenskart.returnservice.service.IReversePickUpService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.ContentResultMatchers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {ReturnApiController.class})
@ExtendWith(SpringExtension.class)
class ReturnApiControllerTest {

    @InjectMocks
    private ReturnApiController returnApiController;

    @Mock
    private IReversePickUpService reversePickUpService;

    @Mock
    private IReturnUpdateService returnUpdateService;

    @Mock
    private Map<String, IReturnInitiationService> returnInitiationServiceMap;

    @Mock
    private IAwaitedRtoService awaitedRtoService;

    @Mock
    private IReceivingService receivingService;

    @MockBean
    private IReceivingService iReceivingService;

    @MockBean
    private IAwaitedRtoService iAwaitedRtoService;

    @MockBean
    private IInventoryReturnUpdateService iInventoryReturnUpdateService;

    @MockBean
    private IReturnUpdateService iReturnUpdateService;

    @MockBean
    private IReturnOrderActionService iReturnOrderActionService;

    @MockBean
    private IReturnInitiationService iReturnInitiationService;

    @MockBean
    private IReversePickUpService iReversePickUpService;

    @Autowired
    private Map<String, IReturnInitiationService> map;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test {@link ReturnApiController#updateReversePickUpInfo(ReverseCourierUpdateRequestDTO)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateReversePickUpInfo(ReverseCourierUpdateRequestDTO)}
     */
    @Test
    @DisplayName("Test updateReversePickUpInfo(ReverseCourierUpdateRequestDTO)")
    @Tag("MaintainedByDiffblue")
    void testUpdateReversePickUpInfo() throws Exception {
        // Arrange
        ReverseCourierUpdateResponse reverseCourierUpdateResponse = new ReverseCourierUpdateResponse();
        reverseCourierUpdateResponse.setMessage("Not all who wander are lost");
        reverseCourierUpdateResponse.setSuccessful(true);
        when(iReversePickUpService.updateReversePickUpInfo(Mockito.<ReverseCourierUpdateRequestDTO>any()))
                .thenReturn(reverseCourierUpdateResponse);

        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setChannel("Channel");
        ordersDTO.setCustomerId(1L);
        ordersDTO.setFacilityCode("Facility Code");
        ordersDTO.setIncrementId(1);
        ordersDTO.setItemId(1);
        ordersDTO.setMethod("Method");
        ordersDTO.setOrderId(1);
        ordersDTO.setProductDeliveryType("Product Delivery Type");
        ordersDTO.setProductId(1);
        ordersDTO.setStoreId((byte) 'A');
        ordersDTO.setSubProductId(1);

        ReverseCourierUpdateRequestDTO reverseCourierUpdateRequestDTO = new ReverseCourierUpdateRequestDTO();
        reverseCourierUpdateRequestDTO.setAwb("Awb");
        reverseCourierUpdateRequestDTO.setCourier("Courier");
        reverseCourierUpdateRequestDTO.setOrdersDTO(ordersDTO);
        reverseCourierUpdateRequestDTO.setPickupId(1);
        reverseCourierUpdateRequestDTO.setReferenceId("42");
        reverseCourierUpdateRequestDTO.setUserId(1);
        reverseCourierUpdateRequestDTO.setUserName("janedoe");
        String content = (new ObjectMapper()).writeValueAsString(reverseCourierUpdateRequestDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/return/v1.0/reverse-pickup-info/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder);
//                .andExpect(MockMvcResultMatchers.status())
//                        .contentType("application/json").isOk().string("{\"successful\":true,\"message\":\"Not all who wander are lost\"}");

//                .andExpect(MockMvcResultMatchers.content()
//                .andExpect(MockMvcResultMatchers.content()
    }

    @Test
    void updateReversePickUpInfo_Success() {
        ReverseCourierUpdateRequestDTO requestDTO = new ReverseCourierUpdateRequestDTO();
        ReverseCourierUpdateResponse expectedResponse = new ReverseCourierUpdateResponse();

        when(reversePickUpService.updateReversePickUpInfo(any(ReverseCourierUpdateRequestDTO.class)))
                .thenReturn(expectedResponse);

        ReverseCourierUpdateResponse actualResponse = returnApiController.updateReversePickUpInfo(requestDTO);

        assertEquals(expectedResponse, actualResponse);
        verify(reversePickUpService, times(1)).updateReversePickUpInfo(requestDTO);
    }

    /**
     * Test {@link ReturnApiController#updateReturn(ReturnDetailUpdateRequestDto)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateReturn(ReturnDetailUpdateRequestDto)}
     */
    @Test
    @DisplayName("Test updateReturn(ReturnDetailUpdateRequestDto)")
    @Tag("MaintainedByDiffblue")
    void testUpdateReturn() throws Exception {
        // Arrange
        ReturnDetailsUpdateResponse returnDetailsUpdateResponse = new ReturnDetailsUpdateResponse();
        returnDetailsUpdateResponse.setMsg("Msg");
        returnDetailsUpdateResponse.setStatus("Status");
        when(iReturnUpdateService.updateReturnStatus(Mockito.<ReturnDetailUpdateRequestDto>any()))
                .thenReturn(returnDetailsUpdateResponse);

        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder().countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());

        ExchangeDetails exchangeDetailsForBackSync = new ExchangeDetails();
        exchangeDetailsForBackSync
                .setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetailsForBackSync.setExchangeChildId("42");
        exchangeDetailsForBackSync.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetailsForBackSync.setExchangeParentId("42");
        OrderExchangeCancellationDetailsBuilder builderResult2 = OrderExchangeCancellationDetails.builder();
        OrderExchangeCancellationDetailsBuilder exchangeDetailsForBackSyncResult = builderResult2
                .canceledOrdersDTOList(new ArrayList<>())
                .exchangeDetailsForBackSync(exchangeDetailsForBackSync);
        OrderExchangeCancellationDetails orderExchangeCancellationDetails = exchangeDetailsForBackSyncResult
                .exchangeOrdersDTOList(new ArrayList<>())
                .build();
        OrderInfoResponseDTOBuilder orderExchangeCancellationDetailsResult = orderAddressUpdatesResult
                .orderExchangeCancellationDetails(orderExchangeCancellationDetails);
        OrderInfoResponseDTOBuilder ordersResult = orderExchangeCancellationDetailsResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
        ordersHeader.setIsExchangeOrder(true);
        ordersHeader.setLkCountry("GB");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
        ordersHeader.setPaymentMode("Payment Mode");
        OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult.uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO orderInfoResponseDTO = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();

        ReturnDetailUpdateRequestDto returnDetailUpdateRequestDto = new ReturnDetailUpdateRequestDto();
        returnDetailUpdateRequestDto.setComments("Comments");
        ExchangeOrdersDTOBuilder countExchangeItemsResult2 = ExchangeOrdersDTO.builder().countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO2 = countExchangeItemsResult2
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        returnDetailUpdateRequestDto.setExchangeOrdersDTO(exchangeOrdersDTO2);
        returnDetailUpdateRequestDto.setOrderInfoResponseDTO(orderInfoResponseDTO);
        returnDetailUpdateRequestDto.setReturnId(1);
        returnDetailUpdateRequestDto.setStatus("Status");
        returnDetailUpdateRequestDto.setUsername("janedoe");
        String content = (new ObjectMapper()).writeValueAsString(returnDetailUpdateRequestDto);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.put("/return/v1.0/")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
//                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string(""));
    }

    @Test
    void updateReturn_Success() {
        ReturnDetailUpdateRequestDto requestDto = new ReturnDetailUpdateRequestDto();
        ReturnDetailsUpdateResponse expectedResponse = new ReturnDetailsUpdateResponse();

        when(returnUpdateService.updateReturnStatus(any(ReturnDetailUpdateRequestDto.class))).thenReturn(expectedResponse);

        ReturnDetailsUpdateResponse actualResponse = returnApiController.updateReturn(requestDto);

        assertEquals(expectedResponse, actualResponse);
        verify(returnUpdateService, times(1)).updateReturnStatus(requestDto);
    }

    @Test
    void createReturn_Success() throws Exception {
        ReturnCreationRequestDTO requestDTO = new ReturnCreationRequestDTO();
        requestDTO.setReturnSource(ReturnSourcesDTO.POS);
        ReturnCreationResponse expectedResponse = new ReturnCreationResponse();
        IReturnInitiationService initiationService = mock(IReturnInitiationService.class);

        when(returnInitiationServiceMap.get(anyString())).thenReturn(initiationService);
        when(initiationService.createReturn(any(ReturnCreationRequestDTO.class))).thenReturn(expectedResponse);

        ReturnCreationResponse actualResponse = returnApiController.createReturn(requestDTO);

        assertEquals(expectedResponse, actualResponse);
//        verify(returnInitiationServiceMap, times(1)).get("pos");
        verify(initiationService, times(1)).createReturn(requestDTO);
    }

    /**
     * Test {@link ReturnApiController#createReturn(ReturnCreationRequestDTO)}.
     * <ul>
     *   <li>When {@code Uri Variables}.</li>
     * </ul>
     * <p>
     * Method under test: {@link ReturnApiController#createReturn(ReturnCreationRequestDTO)}
     */
    @Test
    @DisplayName("Test createReturn(ReturnCreationRequestDTO); when 'Uri Variables'")
    @Tag("MaintainedByDiffblue")
    void testCreateReturn_whenUriVariables() throws Exception {
        // Arrange
        ExchangeAddressDTO exchangeAddress = new ExchangeAddressDTO();
        exchangeAddress.setAddressType("42 Main St");
        exchangeAddress.setAddressline1("42 Main St");
        exchangeAddress.setCity("Oxford");
        exchangeAddress.setCountry("GB");
        exchangeAddress.setEmail("<EMAIL>");
        exchangeAddress.setFirstName("Jane");
        exchangeAddress.setGender("Gender");
        exchangeAddress.setLandmark("Landmark");
        exchangeAddress.setLastName("Doe");
        exchangeAddress.setLocality("Locality");
        exchangeAddress.setPhone("**********");
        exchangeAddress.setPhoneCode("**********");
        exchangeAddress.setPostcode("OX1 1PT");
        exchangeAddress.setState("MD");

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftCardDiscount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setInsuranceBenefitDiscount(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmountDTO.setLenskartPlusDiscount(10.0d);
        itemWiseAmountDTO.setPaymentNotCapture(true);
        itemWiseAmountDTO.setPrepaidWeb(10.0d);
        itemWiseAmountDTO.setStoreCredit(10.0d);
        itemWiseAmountDTO.setTPendingPGAmount(10.0d);
        itemWiseAmountDTO.setTotalCodPrice(10.0d);
        itemWiseAmountDTO.setTotalItemAmount(10.0d);
        itemWiseAmountDTO.setTotalOnlinePrice(10.0d);
        itemWiseAmountDTO.setTotalPrepaidAmt(10.0d);
        itemWiseAmountDTO.setTotalPriceOfOrder(10.0d);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        purchaseOrderDetailsDTO.setBranded(true);
        purchaseOrderDetailsDTO.setGetIsBlacklisted(true);
        purchaseOrderDetailsDTO.setIdentifierType("Identifier Type");
        purchaseOrderDetailsDTO.setIdentifierValue("42");
        purchaseOrderDetailsDTO.setItemWiseAmountDTO(itemWiseAmountDTO);
        purchaseOrderDetailsDTO.setItemWisePrices(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrderAddressUpdateDTOs(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrders(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        ShippingStatusDetailBuilder builderResult = ShippingStatusDetail.builder();
        ShippingStatusDetailBuilder DeliveredDateResult = builderResult
                .DeliveredDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        ShippingStatusDetailBuilder codAmountResult = DeliveredDateResult
                .awbAssignedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .carrierCode("Carrier Code")
                .ccFlag(1)
                .ccUploadDate("2020-03-01")
                .codAmount("10");
        ShippingStatusDetailBuilder followedupCountResult = codAmountResult
                .complete_time(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .deleted("Jan 1, 2020 11:00am GMT+0100")
                .docketNumber("42")
                .email("<EMAIL>")
                .execEmailId("42")
                .followedupCount(3);
        ShippingStatusDetailBuilder invoicedTimeResult = followedupCountResult
                .followedupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .invoicedDate("2020-03-01")
                .invoicedTime("Invoiced Time");
        ShippingStatusDetailBuilder phoneResult = invoicedTimeResult
                .lastFollowedupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .leadId("42")
                .linkToOrderNo("Link To Order No")
                .manifestDate("2020-03-01")
                .manifestNumber("42")
                .manifestTime("Manifest Time")
                .name("Name")
                .ndrSmsSent(1)
                .npsReason("Just cause")
                .orderNo(1)
                .packed("Packed")
                .paymentReceived("Payment Received")
                .phone("**********");
        ShippingStatusDetail shippingStatusDetail = phoneResult
                .pickDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .pickedBy("Picked By")
                .receivedBy("Received By")
                .referralApiSent(1)
                .region("us-east-2")
                .routingCode("Routing Code")
                .shipmentStatus("Shipment Status")
                .shipped("Shipped")
                .shippingMode("Shipping Mode")
                .shippingPackageId("42")
                .shipping_time("Shipping time")
                .statusId(1)
                .trackingNo("Tracking No")
                .unicomOrderCode("Unicom Order Code")
                .updatedAt("2020-03-01")
                .updatedCrm("2020-03-01")
                .updatedMagento("2020-03-01")
                .warehouseLocation("Warehouse Location")
                .build();
        purchaseOrderDetailsDTO.setShippingStatusDetail(shippingStatusDetail);
        purchaseOrderDetailsDTO.setUwOrders(new ArrayList<>());

        PickupAddressDTO ReversePickupAddress = new PickupAddressDTO();
        ReversePickupAddress.setCity("Oxford");
        ReversePickupAddress.setCountry("GB");
        ReversePickupAddress.setEmail("<EMAIL>");
        ReversePickupAddress.setFirstName("Jane");
        ReversePickupAddress.setLastName("Doe");
        ReversePickupAddress.setPincode(1);
        ReversePickupAddress.setState("MD");
        ReversePickupAddress.setStreet1("Street1");
        ReversePickupAddress.setStreet2("Street2");
        ReversePickupAddress.setTelephone("**********");

        ReturnCreationRequestDTO returnCreationRequestDTO = new ReturnCreationRequestDTO();
        returnCreationRequestDTO.setCallbackRequiredToSalesman(true);
        DispensingDTO dispensingDTO = DispensingDTO.builder()
                .fieldEzstatus("Field Ezstatus")
                .incrementId(1)
                .leadExtStatus("Lead Ext Status")
                .markAttended("Mark Attended")
                .pickedFrom("<EMAIL>")
                .reason("Just cause")
                .shipmentClosedBy("Shipment Closed By")
                .syncedToFieldez(1)
                .build();
        returnCreationRequestDTO.setDispensingDTO(dispensingDTO);
        returnCreationRequestDTO.setEnforceRefundAtStore(true);
        returnCreationRequestDTO.setExchangeAddress(exchangeAddress);
        returnCreationRequestDTO.setFacilityCode("Facility Code");
        returnCreationRequestDTO.setIncrementId(1);
        returnCreationRequestDTO.setInitiatedBy(1);
        returnCreationRequestDTO.setIsCourierReassigned(true);
        returnCreationRequestDTO.setItems(new ArrayList<>());
        returnCreationRequestDTO.setNewCourier("New Courier");
        returnCreationRequestDTO.setOldCourier("Old Courier");
        returnCreationRequestDTO.setProductIdsMap(new HashMap<>());
        returnCreationRequestDTO.setPurchaseOrderDetailsDTO(purchaseOrderDetailsDTO);
        returnCreationRequestDTO.setReturnMethod("Return Method");
        returnCreationRequestDTO.setReturnSource(ReturnSourcesDTO.WEB);
        ReverseCourierMetaData reverseCourierMetaData = ReverseCourierMetaData.builder()
                .qcWarrantyRuleCount(3)
                .smartQcEligibilityFlag(true)
                .build();
        returnCreationRequestDTO.setReverseCourierMetaData(reverseCourierMetaData);
        returnCreationRequestDTO.setReversePickupAddress(ReversePickupAddress);
        returnCreationRequestDTO.setSalesmanName("Salesman Name");
        returnCreationRequestDTO.setSalesmanNumber("42");
        returnCreationRequestDTO.setStoreEmail("<EMAIL>");
        returnCreationRequestDTO.setStoreFacilityCode("Store Facility Code");
        String content = (new ObjectMapper()).writeValueAsString(returnCreationRequestDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/", "Uri Variables")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    /**
     * Test {@link ReturnApiController#createReturn(ReturnCreationRequestDTO)}.
     * <p>
     * Method under test: {@link ReturnApiController#createReturn(ReturnCreationRequestDTO)}
     */
    @Test
    @DisplayName("Test createReturn(ReturnCreationRequestDTO)")
    @Tag("MaintainedByDiffblue")
    void testCreateReturn() throws Exception {
        // Arrange
        ExchangeAddressDTO exchangeAddress = new ExchangeAddressDTO();
        exchangeAddress.setAddressType("42 Main St");
        exchangeAddress.setAddressline1("42 Main St");
        exchangeAddress.setCity("Oxford");
        exchangeAddress.setCountry("GB");
        exchangeAddress.setEmail("<EMAIL>");
        exchangeAddress.setFirstName("Jane");
        exchangeAddress.setGender("Gender");
        exchangeAddress.setLandmark("Landmark");
        exchangeAddress.setLastName("Doe");
        exchangeAddress.setLocality("Locality");
        exchangeAddress.setPhone("**********");
        exchangeAddress.setPhoneCode("**********");
        exchangeAddress.setPostcode("OX1 1PT");
        exchangeAddress.setState("MD");

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftCardDiscount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setInsuranceBenefitDiscount(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmountDTO.setLenskartPlusDiscount(10.0d);
        itemWiseAmountDTO.setPaymentNotCapture(true);
        itemWiseAmountDTO.setPrepaidWeb(10.0d);
        itemWiseAmountDTO.setStoreCredit(10.0d);
        itemWiseAmountDTO.setTPendingPGAmount(10.0d);
        itemWiseAmountDTO.setTotalCodPrice(10.0d);
        itemWiseAmountDTO.setTotalItemAmount(10.0d);
        itemWiseAmountDTO.setTotalOnlinePrice(10.0d);
        itemWiseAmountDTO.setTotalPrepaidAmt(10.0d);
        itemWiseAmountDTO.setTotalPriceOfOrder(10.0d);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        purchaseOrderDetailsDTO.setBranded(true);
        purchaseOrderDetailsDTO.setGetIsBlacklisted(true);
        purchaseOrderDetailsDTO.setIdentifierType("Identifier Type");
        purchaseOrderDetailsDTO.setIdentifierValue("42");
        purchaseOrderDetailsDTO.setItemWiseAmountDTO(itemWiseAmountDTO);
        purchaseOrderDetailsDTO.setItemWisePrices(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrderAddressUpdateDTOs(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrders(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        ShippingStatusDetailBuilder builderResult = ShippingStatusDetail.builder();
        ShippingStatusDetailBuilder DeliveredDateResult = builderResult
                .DeliveredDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        ShippingStatusDetailBuilder codAmountResult = DeliveredDateResult
                .awbAssignedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .carrierCode("Carrier Code")
                .ccFlag(1)
                .ccUploadDate("2020-03-01")
                .codAmount("10");
        ShippingStatusDetailBuilder followedupCountResult = codAmountResult
                .complete_time(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .deleted("Jan 1, 2020 11:00am GMT+0100")
                .docketNumber("42")
                .email("<EMAIL>")
                .execEmailId("42")
                .followedupCount(3);
        ShippingStatusDetailBuilder invoicedTimeResult = followedupCountResult
                .followedupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .invoicedDate("2020-03-01")
                .invoicedTime("Invoiced Time");
        ShippingStatusDetailBuilder phoneResult = invoicedTimeResult
                .lastFollowedupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .leadId("42")
                .linkToOrderNo("Link To Order No")
                .manifestDate("2020-03-01")
                .manifestNumber("42")
                .manifestTime("Manifest Time")
                .name("Name")
                .ndrSmsSent(1)
                .npsReason("Just cause")
                .orderNo(1)
                .packed("Packed")
                .paymentReceived("Payment Received")
                .phone("**********");
        ShippingStatusDetail shippingStatusDetail = phoneResult
                .pickDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .pickedBy("Picked By")
                .receivedBy("Received By")
                .referralApiSent(1)
                .region("us-east-2")
                .routingCode("Routing Code")
                .shipmentStatus("Shipment Status")
                .shipped("Shipped")
                .shippingMode("Shipping Mode")
                .shippingPackageId("42")
                .shipping_time("Shipping time")
                .statusId(1)
                .trackingNo("Tracking No")
                .unicomOrderCode("Unicom Order Code")
                .updatedAt("2020-03-01")
                .updatedCrm("2020-03-01")
                .updatedMagento("2020-03-01")
                .warehouseLocation("Warehouse Location")
                .build();
        purchaseOrderDetailsDTO.setShippingStatusDetail(shippingStatusDetail);
        purchaseOrderDetailsDTO.setUwOrders(new ArrayList<>());

        PickupAddressDTO ReversePickupAddress = new PickupAddressDTO();
        ReversePickupAddress.setCity("Oxford");
        ReversePickupAddress.setCountry("GB");
        ReversePickupAddress.setEmail("<EMAIL>");
        ReversePickupAddress.setFirstName("Jane");
        ReversePickupAddress.setLastName("Doe");
        ReversePickupAddress.setPincode(1);
        ReversePickupAddress.setState("MD");
        ReversePickupAddress.setStreet1("Street1");
        ReversePickupAddress.setStreet2("Street2");
        ReversePickupAddress.setTelephone("**********");

        ReturnCreationRequestDTO returnCreationRequestDTO = new ReturnCreationRequestDTO();
        returnCreationRequestDTO.setCallbackRequiredToSalesman(true);
        DispensingDTO dispensingDTO = DispensingDTO.builder()
                .fieldEzstatus("Field Ezstatus")
                .incrementId(1)
                .leadExtStatus("Lead Ext Status")
                .markAttended("Mark Attended")
                .pickedFrom("<EMAIL>")
                .reason("Just cause")
                .shipmentClosedBy("Shipment Closed By")
                .syncedToFieldez(1)
                .build();
        returnCreationRequestDTO.setDispensingDTO(dispensingDTO);
        returnCreationRequestDTO.setEnforceRefundAtStore(true);
        returnCreationRequestDTO.setExchangeAddress(exchangeAddress);
        returnCreationRequestDTO.setFacilityCode("Facility Code");
        returnCreationRequestDTO.setIncrementId(1);
        returnCreationRequestDTO.setInitiatedBy(1);
        returnCreationRequestDTO.setIsCourierReassigned(true);
        returnCreationRequestDTO.setItems(new ArrayList<>());
        returnCreationRequestDTO.setNewCourier("New Courier");
        returnCreationRequestDTO.setOldCourier("Old Courier");
        returnCreationRequestDTO.setProductIdsMap(new HashMap<>());
        returnCreationRequestDTO.setPurchaseOrderDetailsDTO(purchaseOrderDetailsDTO);
        returnCreationRequestDTO.setReturnMethod("Return Method");
        returnCreationRequestDTO.setReturnSource(ReturnSourcesDTO.WEB);
        ReverseCourierMetaData reverseCourierMetaData = ReverseCourierMetaData.builder()
                .qcWarrantyRuleCount(3)
                .smartQcEligibilityFlag(true)
                .build();
        returnCreationRequestDTO.setReverseCourierMetaData(reverseCourierMetaData);
        returnCreationRequestDTO.setReversePickupAddress(ReversePickupAddress);
        returnCreationRequestDTO.setSalesmanName("Salesman Name");
        returnCreationRequestDTO.setSalesmanNumber("42");
        returnCreationRequestDTO.setStoreEmail("<EMAIL>");
        returnCreationRequestDTO.setStoreFacilityCode("Store Facility Code");
        String content = (new ObjectMapper()).writeValueAsString(returnCreationRequestDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void createReturn_FallbackToDefaultService() throws Exception {
        ReturnCreationRequestDTO requestDTO = new ReturnCreationRequestDTO();
        requestDTO.setReturnSource(ReturnSourcesDTO.POS);
        ReturnCreationResponse expectedResponse = new ReturnCreationResponse();
        IReturnInitiationService defaultService = mock(IReturnInitiationService.class);

        when(returnInitiationServiceMap.get("pos")).thenReturn(null);
        when(returnInitiationServiceMap.get("ReturnInitiationServiceImpl")).thenReturn(defaultService);
        when(defaultService.createReturn(any(ReturnCreationRequestDTO.class))).thenReturn(expectedResponse);

        ReturnCreationResponse actualResponse = returnApiController.createReturn(requestDTO);

//        assertEquals(expectedResponse, actualResponse);
//        verify(returnInitiationServiceMap, times(1)).get("pos");
//        verify(returnInitiationServiceMap, times(1)).get("ReturnInitiationServiceImpl");
//        verify(defaultService, times(1)).createReturn(requestDTO);
    }

    @Test
    void returnAtAwaitedRto_Success() {
        ReturnOrderRequestDTO requestDTO = new ReturnOrderRequestDTO();
        Map<String, Object> expectedResponse = new HashMap<>();

        when(awaitedRtoService.markAwaitedRto(any(ReturnOrderRequestDTO.class))).thenReturn(expectedResponse);

        Map<String, Object> actualResponse = returnApiController.returnAtAwaitedRto(requestDTO);

        assertEquals(expectedResponse, actualResponse);
        verify(awaitedRtoService, times(1)).markAwaitedRto(requestDTO);
    }

    /**
     * Test {@link ReturnApiController#returnAtAwaitedRto(ReturnOrderRequestDTO)}.
     * <p>
     * Method under test: {@link ReturnApiController#returnAtAwaitedRto(ReturnOrderRequestDTO)}
     */
    @Test
    @DisplayName("Test returnAtAwaitedRto(ReturnOrderRequestDTO)")
    @Tag("MaintainedByDiffblue")
    void testReturnAtAwaitedRto() throws Exception {
        // Arrange
        when(iAwaitedRtoService.markAwaitedRto(Mockito.<ReturnOrderRequestDTO>any())).thenReturn(new HashMap<>());

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        ReturnOrderRequestDTO returnOrderRequestDTO = new ReturnOrderRequestDTO();
        returnOrderRequestDTO.setDoRefund(true);
        returnOrderRequestDTO.setFacility("Facility");
        returnOrderRequestDTO.setGroupId(1L);
        returnOrderRequestDTO.setIncrementId(1);
        returnOrderRequestDTO.setIsDualCo(true);
        returnOrderRequestDTO.setItems(new ArrayList<>());
        returnOrderRequestDTO.setNewFlowFlag(1);
        returnOrderRequestDTO.setOrderDTOs(new ArrayList<>());
        returnOrderRequestDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        returnOrderRequestDTO.setRaiseRPUatNexs(true);
        returnOrderRequestDTO.setRaiseRPUatUnicom(true);
        returnOrderRequestDTO.setReasonDetail("Just cause");
        returnOrderRequestDTO.setReferenceOrderCode("Reference Order Code");
        returnOrderRequestDTO.setRefundMethod("Refund Method");
        returnOrderRequestDTO.setShippingPackageId("42");
        returnOrderRequestDTO.setSource("Source");
        returnOrderRequestDTO.setUserId("42");
        returnOrderRequestDTO.setUwOrderDTOs(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(returnOrderRequestDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/awaited-rto/return-refund")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    @Test
    void receiveItems_Success() throws Exception {
        ReturnOrderRequestDTO requestDTO = new ReturnOrderRequestDTO();
        Set<Integer> expectedResponse = Set.of(1, 2, 3);

        when(receivingService.receiveItems(any(ReturnOrderRequestDTO.class))).thenReturn(expectedResponse);

        Set<Integer> actualResponse = returnApiController.receiveItems(requestDTO);

        assertEquals(expectedResponse, actualResponse);
        verify(receivingService, times(1)).receiveItems(requestDTO);
    }

    /**
     * Test {@link ReturnApiController#receiveItems(ReturnOrderRequestDTO)}.
     * <p>
     * Method under test: {@link ReturnApiController#receiveItems(ReturnOrderRequestDTO)}
     */
    @Test
    @DisplayName("Test receiveItems(ReturnOrderRequestDTO)")
    @Tag("MaintainedByDiffblue")
    void testReceiveItems() throws Exception {
        // Arrange
        when(iReceivingService.receiveItems(Mockito.<ReturnOrderRequestDTO>any())).thenReturn(new HashSet<>());

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        ReturnOrderRequestDTO returnOrderRequestDTO = new ReturnOrderRequestDTO();
        returnOrderRequestDTO.setDoRefund(true);
        returnOrderRequestDTO.setFacility("Facility");
        returnOrderRequestDTO.setGroupId(1L);
        returnOrderRequestDTO.setIncrementId(1);
        returnOrderRequestDTO.setIsDualCo(true);
        returnOrderRequestDTO.setItems(new ArrayList<>());
        returnOrderRequestDTO.setNewFlowFlag(1);
        returnOrderRequestDTO.setOrderDTOs(new ArrayList<>());
        returnOrderRequestDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        returnOrderRequestDTO.setRaiseRPUatNexs(true);
        returnOrderRequestDTO.setRaiseRPUatUnicom(true);
        returnOrderRequestDTO.setReasonDetail("Just cause");
        returnOrderRequestDTO.setReferenceOrderCode("Reference Order Code");
        returnOrderRequestDTO.setRefundMethod("Refund Method");
        returnOrderRequestDTO.setShippingPackageId("42");
        returnOrderRequestDTO.setSource("Source");
        returnOrderRequestDTO.setUserId("42");
        returnOrderRequestDTO.setUwOrderDTOs(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(returnOrderRequestDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/return/v1.0/create-return/direct-received")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    @Test
    void rtoItems_Success() throws Exception {
        ReturnOrderRequestDTO requestDTO = new ReturnOrderRequestDTO();
        Map<String, Object> expectedResponse = new HashMap<>();

        when(returnUpdateService.markRtoItems(any(ReturnOrderRequestDTO.class))).thenReturn(expectedResponse);

        Map<String, Object> actualResponse = returnApiController.rtoItems(requestDTO);

        assertEquals(expectedResponse, actualResponse);
        verify(returnUpdateService, times(1)).markRtoItems(requestDTO);
    }

    /**
     * Test {@link ReturnApiController#rtoItems(ReturnOrderRequestDTO)}.
     * <p>
     * Method under test: {@link ReturnApiController#rtoItems(ReturnOrderRequestDTO)}
     */
    @Test
    @DisplayName("Test rtoItems(ReturnOrderRequestDTO)")
    @Tag("MaintainedByDiffblue")
    void testRtoItems() throws Exception {
        // Arrange
        when(iReturnUpdateService.markRtoItems(Mockito.<ReturnOrderRequestDTO>any())).thenReturn(new HashMap<>());

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        ReturnOrderRequestDTO returnOrderRequestDTO = new ReturnOrderRequestDTO();
        returnOrderRequestDTO.setDoRefund(true);
        returnOrderRequestDTO.setFacility("Facility");
        returnOrderRequestDTO.setGroupId(1L);
        returnOrderRequestDTO.setIncrementId(1);
        returnOrderRequestDTO.setIsDualCo(true);
        returnOrderRequestDTO.setItems(new ArrayList<>());
        returnOrderRequestDTO.setNewFlowFlag(1);
        returnOrderRequestDTO.setOrderDTOs(new ArrayList<>());
        returnOrderRequestDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        returnOrderRequestDTO.setRaiseRPUatNexs(true);
        returnOrderRequestDTO.setRaiseRPUatUnicom(true);
        returnOrderRequestDTO.setReasonDetail("Just cause");
        returnOrderRequestDTO.setReferenceOrderCode("Reference Order Code");
        returnOrderRequestDTO.setRefundMethod("Refund Method");
        returnOrderRequestDTO.setShippingPackageId("42");
        returnOrderRequestDTO.setSource("Source");
        returnOrderRequestDTO.setUserId("42");
        returnOrderRequestDTO.setUwOrderDTOs(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(returnOrderRequestDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/rto-items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    @Test
    void returnItems_Success() throws Exception {
        ReturnOrderRequestDTO requestDTO = new ReturnOrderRequestDTO();
        Map<String, Object> expectedResponse = new HashMap<>();

        when(receivingService.returnItems(any(ReturnOrderRequestDTO.class))).thenReturn(expectedResponse);

        Map<String, Object> actualResponse = returnApiController.returnItems(requestDTO);

        assertEquals(expectedResponse, actualResponse);
        verify(receivingService, times(1)).returnItems(requestDTO);
    }

    /**
     * Test {@link ReturnApiController#returnItems(ReturnOrderRequestDTO)}.
     * <p>
     * Method under test: {@link ReturnApiController#returnItems(ReturnOrderRequestDTO)}
     */
    @Test
    @DisplayName("Test returnItems(ReturnOrderRequestDTO)")
    @Tag("MaintainedByDiffblue")
    void testReturnItems() throws Exception {
        // Arrange
        when(iReceivingService.returnItems(Mockito.<ReturnOrderRequestDTO>any())).thenReturn(new HashMap<>());

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        ReturnOrderRequestDTO returnOrderRequestDTO = new ReturnOrderRequestDTO();
        returnOrderRequestDTO.setDoRefund(true);
        returnOrderRequestDTO.setFacility("Facility");
        returnOrderRequestDTO.setGroupId(1L);
        returnOrderRequestDTO.setIncrementId(1);
        returnOrderRequestDTO.setIsDualCo(true);
        returnOrderRequestDTO.setItems(new ArrayList<>());
        returnOrderRequestDTO.setNewFlowFlag(1);
        returnOrderRequestDTO.setOrderDTOs(new ArrayList<>());
        returnOrderRequestDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        returnOrderRequestDTO.setRaiseRPUatNexs(true);
        returnOrderRequestDTO.setRaiseRPUatUnicom(true);
        returnOrderRequestDTO.setReasonDetail("Just cause");
        returnOrderRequestDTO.setReferenceOrderCode("Reference Order Code");
        returnOrderRequestDTO.setRefundMethod("Refund Method");
        returnOrderRequestDTO.setShippingPackageId("42");
        returnOrderRequestDTO.setSource("Source");
        returnOrderRequestDTO.setUserId("42");
        returnOrderRequestDTO.setUwOrderDTOs(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(returnOrderRequestDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/return-items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    @Test
    void receiveItems_Exception() throws Exception {
        ReturnOrderRequestDTO requestDTO = new ReturnOrderRequestDTO();

        when(receivingService.receiveItems(any(ReturnOrderRequestDTO.class)))
                .thenThrow(new RuntimeException("Test Exception"));

        assertThrows(RuntimeException.class, () -> returnApiController.receiveItems(requestDTO));
        verify(receivingService, times(1)).receiveItems(requestDTO);
    }

    @Test
    void returnItems_Exception() throws Exception {
        ReturnOrderRequestDTO requestDTO = new ReturnOrderRequestDTO();

        when(receivingService.returnItems(any(ReturnOrderRequestDTO.class)))
                .thenThrow(new RuntimeException("Test Exception"));

        assertThrows(RuntimeException.class, () -> returnApiController.returnItems(requestDTO));
        verify(receivingService, times(1)).returnItems(requestDTO);
    }

    /**
     * Test {@link ReturnApiController#updateOrderStatusForRefund(DualRefundRequest)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateOrderStatusForRefund(DualRefundRequest)}
     */
    @Test
    @DisplayName("Test updateOrderStatusForRefund(DualRefundRequest)")
    @Tag("MaintainedByDiffblue")
    void testUpdateOrderStatusForRefund() throws Exception {
        // Arrange
        doNothing().when(iReturnOrderActionService).updateOrderStatusForRefund(Mockito.<DualRefundRequest>any());

        ItemWiseAmountDTO itemWiseAmount = new ItemWiseAmountDTO();
        itemWiseAmount.setCurrency("GBP");
        itemWiseAmount.setExchangePgAmount(10.0d);
        itemWiseAmount.setGiftCardDiscount(10.0d);
        itemWiseAmount.setGiftVoucher(10.0d);
        itemWiseAmount.setInsuranceBenefitDiscount(10.0d);
        itemWiseAmount.setIsExchangeItemReceived(true);
        itemWiseAmount.setItemWiseRefundAmount(10.0d);
        itemWiseAmount.setLenskartDiscount(10.0d);
        itemWiseAmount.setLenskartExchangeDiscount(10.0d);
        itemWiseAmount.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmount.setLenskartPlusDiscount(10.0d);
        itemWiseAmount.setPaymentNotCapture(true);
        itemWiseAmount.setPrepaidWeb(10.0d);
        itemWiseAmount.setStoreCredit(10.0d);
        itemWiseAmount.setTPendingPGAmount(10.0d);
        itemWiseAmount.setTotalCodPrice(10.0d);
        itemWiseAmount.setTotalItemAmount(10.0d);
        itemWiseAmount.setTotalOnlinePrice(10.0d);
        itemWiseAmount.setTotalPrepaidAmt(10.0d);
        itemWiseAmount.setTotalPriceOfOrder(10.0d);

        DualRefundRequest dualRefundRequest = new DualRefundRequest();
        dualRefundRequest.setAdditionalAmount(10.0d);
        dualRefundRequest.setAdditionalComment("Additional Comment");
        dualRefundRequest.setAdditionalRefund(true);
        dualRefundRequest.setCategory("Category");
        dualRefundRequest.setConversionScToNeft(true);
        dualRefundRequest
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        dualRefundRequest.setCustomerId(1L);
        dualRefundRequest.setDoCustomerWalletProcessing(true);
        dualRefundRequest.setEasyRefund(true);
        dualRefundRequest.setExchangeCreated(true);
        dualRefundRequest.setFacilityId("42");
        dualRefundRequest.setFastOrExceptionRefund(true);
        dualRefundRequest.setFofoOldOrder(true);
        dualRefundRequest.setGatewayName("Gateway Name");
        dualRefundRequest.setId(1L);
        dualRefundRequest.setIncrementId(1L);
        dualRefundRequest.setIsCustomerRefundEligible("Is Customer Refund Eligible");
        dualRefundRequest.setIsWalletAdjustmentEligible("Is Wallet Adjustment Eligible");
        dualRefundRequest.setItemIds(new ArrayList<>());
        dualRefundRequest.setItemWiseAmount(itemWiseAmount);
        dualRefundRequest.setLenskartDiscount(new BigDecimal("2.3"));
        dualRefundRequest.setLenskartPlusDiscount(new BigDecimal("2.3"));
        dualRefundRequest.setNeftDetailId(1L);
        dualRefundRequest.setNeftToSc(1);
        dualRefundRequest.setOrderRefundReason("Just cause");
        dualRefundRequest.setOrderStatus("Order Status");
        dualRefundRequest.setPayoutLink("Payout Link");
        dualRefundRequest.setPosGrn(true);
        dualRefundRequest.setReceived(true);
        dualRefundRequest.setRefundAmount(10.0d);
        dualRefundRequest.setRefundId(1L);
        dualRefundRequest.setRefundMethod("Refund Method");
        dualRefundRequest.setRefundType("Refund Type");
        dualRefundRequest.setRequestedBy("Requested By");
        dualRefundRequest.setReturnId(1L);
        dualRefundRequest.setReturnStatus("Return Status");
        dualRefundRequest.setSource("Source");
        dualRefundRequest.setStatus("Status");
        dualRefundRequest.setStatusDetail("Status Detail");
        dualRefundRequest.setStoreCode("Store Code");
        dualRefundRequest.setTransactionType("Transaction Type");
        dualRefundRequest.setType("Type");
        String content = (new ObjectMapper()).writeValueAsString(dualRefundRequest);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/return/v1.0/update/order-status-refund")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Test {@link ReturnApiController#updateReturnReason(UpdateReturnReasonRequest)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateReturnReason(UpdateReturnReasonRequest)}
     */
    @Test
    @DisplayName("Test updateReturnReason(UpdateReturnReasonRequest)")
    @Tag("MaintainedByDiffblue")
    void testUpdateReturnReason() throws Exception {
        // Arrange
        doNothing().when(iReturnOrderActionService).updateReturnReason(Mockito.<UpdateReturnReasonRequest>any());

        UpdateReturnReasonRequest updateReturnReasonRequest = new UpdateReturnReasonRequest();
        updateReturnReasonRequest.setOrderId(1);
        updateReturnReasonRequest.setPrimaryReason("Just cause");
        updateReturnReasonRequest.setReturnItemId(1);
        updateReturnReasonRequest.setReturnReason("Just cause");
        updateReturnReasonRequest.setSecondaryReason("Just cause");
        updateReturnReasonRequest.setSource("Source");
        updateReturnReasonRequest.setUser("User");
        updateReturnReasonRequest.setUwItemId(1);
        String content = (new ObjectMapper()).writeValueAsString(updateReturnReasonRequest);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/update-return-reason")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Test {@link ReturnApiController#updateAgentEmailsInReturn(UpdateAgentEmailReturnRequest)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateAgentEmailsInReturn(UpdateAgentEmailReturnRequest)}
     */
    @Test
    @DisplayName("Test updateAgentEmailsInReturn(UpdateAgentEmailReturnRequest)")
    @Tag("MaintainedByDiffblue")
    void testUpdateAgentEmailsInReturn() throws Exception {
        // Arrange
        when(iReturnOrderActionService.updateAgentEmailsInReturn(Mockito.<UpdateAgentEmailReturnRequest>any()))
                .thenReturn(new HashMap<>());

        UpdateAgentEmailReturnRequest updateAgentEmailReturnRequest = new UpdateAgentEmailReturnRequest();
        updateAgentEmailReturnRequest.setEmails(new ArrayList<>());
        updateAgentEmailReturnRequest.setReturnIds(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(updateAgentEmailReturnRequest);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/update-agent-emails")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("{}"));
    }

    /**
     * Test {@link ReturnApiController#updateReturnAgent(UpdateReturnAgentRequest)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateReturnAgent(UpdateReturnAgentRequest)}
     */
    @Test
    @DisplayName("Test updateReturnAgent(UpdateReturnAgentRequest)")
    @Tag("MaintainedByDiffblue")
    void testUpdateReturnAgent() throws Exception {
        // Arrange
        when(iReturnOrderActionService.updateReturnAgent(Mockito.<UpdateReturnAgentRequest>any())).thenReturn(true);

        UpdateReturnAgentRequest updateReturnAgentRequest = new UpdateReturnAgentRequest();
        updateReturnAgentRequest.setAssignments(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(updateReturnAgentRequest);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/update/agent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string("{\"success\":true,\"status\":200,\"error\":null,\"message\":\"Agent assigned successfully.\"}"));
    }

    /**
     * Test {@link ReturnApiController#updateReturnAgent(UpdateReturnAgentRequest)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateReturnAgent(UpdateReturnAgentRequest)}
     */
    @Test
    @DisplayName("Test updateReturnAgent(UpdateReturnAgentRequest)")
    @Tag("MaintainedByDiffblue")
    void testUpdateReturnAgent2() throws Exception {
        // Arrange
        when(iReturnOrderActionService.updateReturnAgent(Mockito.<UpdateReturnAgentRequest>any())).thenReturn(false);

        UpdateReturnAgentRequest updateReturnAgentRequest = new UpdateReturnAgentRequest();
        updateReturnAgentRequest.setAssignments(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(updateReturnAgentRequest);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/update/agent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content()
                        .string("{\"success\":false,\"status\":200,\"error\":null,\"message\":\"Agent not assigned.\"}"));
    }

    /**
     * Test {@link ReturnApiController#updateReturnItem(UpdateReturnOrderItemRequest)}.
     * <p>
     * Method under test: {@link ReturnApiController#updateReturnItem(UpdateReturnOrderItemRequest)}
     */
    @Test
    @DisplayName("Test updateReturnItem(UpdateReturnOrderItemRequest)")
    @Tag("MaintainedByDiffblue")
    void testUpdateReturnItem() throws Exception {
        // Arrange
        when(iReturnOrderActionService.updateReturnOrderItem(Mockito.<UpdateReturnOrderItemRequest>any())).thenReturn(1);

        UpdateReturnItemConditions condition = new UpdateReturnItemConditions();
        condition.setId(1);
        condition.setStatus("Status");
        condition.setStatusNotIn(new ArrayList<>());

        UpdateReturnOrderItemRequest updateReturnOrderItemRequest = new UpdateReturnOrderItemRequest();
        updateReturnOrderItemRequest.setCondition(condition);
        updateReturnOrderItemRequest.setCsohUpdatedFlag(1);
        updateReturnOrderItemRequest.setStatus("Status");
        String content = (new ObjectMapper()).writeValueAsString(updateReturnOrderItemRequest);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/update-return-item")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);

        // Act and Assert
        MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"))
                .andExpect(MockMvcResultMatchers.content().string("1"));
    }

    /**
     * Test {@link ReturnApiController#rollbackReturn()}.
     * <p>
     * Method under test: {@link ReturnApiController#rollbackReturn()}
     */
    @Test
    @DisplayName("Test rollbackReturn()")
    @Tag("MaintainedByDiffblue")
    void testRollbackReturn() throws Exception {
        // Arrange
        when(iInventoryReturnUpdateService.updateReturns()).thenReturn(true);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/return/v1.0/rollback-return");

        // Act and Assert
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(returnApiController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }
}
