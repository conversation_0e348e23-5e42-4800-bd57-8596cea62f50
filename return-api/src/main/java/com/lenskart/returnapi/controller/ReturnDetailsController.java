package com.lenskart.returnapi.controller;

import com.lenskart.ordermetadata.dto.OrderTelephoneMapperRequestDTO;
import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.ordermetadata.dto.ReturnHistoryDTO;
import com.lenskart.ordermetadata.dto.request.RefundDispatchRequest;
import com.lenskart.ordermetadata.dto.response.RefundDispatchResponse;
import com.lenskart.returncommon.model.AwbReverseDto;
import com.lenskart.returncommon.model.dto.ExchangeItemDispatchableDTO;
import com.lenskart.returncommon.model.dto.RefundMethodRequestByMagentoDto;
import com.lenskart.returncommon.model.request.*;
import com.lenskart.returncommon.annotations.HandleException;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.dto.insurance.InsuranceEligibilityRequest;
import com.lenskart.returncommon.model.dto.insurance.InsuranceEligibilityResponse;
import com.lenskart.returncommon.model.response.*;
import com.lenskart.returncommon.utils.CsvUtils;
import com.lenskart.returncommon.utils.DateUtil;
import com.lenskart.returnrepository.entity.DelightAction;
import com.lenskart.returnrepository.entity.ReturnDetailAddressUpdate;
import com.lenskart.returnrepository.repository.ReturnOrderAddressUpdateRepository;
import com.lenskart.returnrepository.repository.ReversePickupPincodeRepository;
import com.lenskart.returnservice.service.*;
import com.lenskart.returnservice.service.impl.InsuranceServiceImpl;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsondoc.core.annotation.ApiBodyObject;
import org.jsondoc.core.annotation.ApiMethod;
import org.jsondoc.core.annotation.ApiPathParam;
import org.jsondoc.core.annotation.ApiResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.*;
import com.lenskart.returncommon.model.response.ReturnPolicyResponse;

import com.lenskart.returncommon.model.dto.ReturnPolicyInputDTO;

import javax.validation.Valid;
import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;

@Slf4j
@RestController
@RequestMapping("return/details/v1.0/")
public class ReturnDetailsController {

    @Autowired
    private IReturnOrderService returnOrderService;

    @Autowired
    InsuranceServiceImpl insuranceService;

    @Autowired
    private IReturnRefundEligibilityService returnRefundEligibilityService;

    @Autowired
    private IExchangeRefundMethodService exchangeRefundMethodService;

    @Autowired
    private IReturnReasonService returnReasonService;
    @Autowired
    private IReturnUpdateService returnUpdateService;

    @Autowired
    INeedApprovalProcessorService needApprovalProcessorService;

    @Autowired
    IAwaitedRtoService awaitedRtoService;

    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;

    @Autowired
    IReturnSubHeaderTimelineDateFetchService returnSubHeaderTimelineDateFetchService;

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired
    IReturnFlowResponseService returnFlowResponseService;

    @Autowired
    IReturnOrderActionService returnOrderActionService;
    @Autowired
    IReversePickUpService reversePickUpService;

    @Autowired
    ReversePickupPincodeRepository reversePickupPincodeRepository;

    @Autowired
    ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Autowired
    IReturnOrderAddressUpdateService returnOrderAddressUpdateService;

    @Autowired
    IReturnDetailsService returnDetailsService;

    @Autowired
    private IReverseCourierDetailService reverseCourierDetailService;

    @Autowired
    private ID365ReturnTrackingService d365ReturnTrackingService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IDelightActionService delightActionService;

    @Autowired
    private IJunoService junoService;

    @Autowired
    private ICancelAndConvertService cancelAndConvertService;


    @ApiMethod(path = "/check/eligibility", description = "Unified API for return rule engine resolution before return creation through all channels-- POS/MyAccount/VSM")
    @PostMapping(value = "/check/eligibility")
    public @ApiResponseObject
    ReturnRefundEligibilityResponse returnRefundEligibility(@ApiBodyObject @RequestBody ReturnRefundEligibilityRequestDTO returnRefundEligibilityRequest) throws Exception {
        log.info("[ReturnDetailsController][returnRefundEligibility]" + returnRefundEligibilityRequest.toString());
        return returnRefundEligibilityService.getReturnRefundEligibility(returnRefundEligibilityRequest);
    }

    @ApiMethod(path = "/store-appointment/extend", description = "Unified API to book store appointment when under returnPeriod through all channels-- POS/MyAccount/VSM")
    @PostMapping(value = "/store-appointment/extend")
    @HandleException
    public @ApiResponseObject ResponseEntity<Boolean> addStoreAppointments(@ApiBodyObject @RequestBody ReturnRefundEligibilityRequest returnRefundEligibilityRequest) throws Exception {
        log.info("[ReturnDetailsController][addStoreAppointments] {}", returnRefundEligibilityRequest.toString());

        boolean isStoreAppointmentBooked;
        try {
            isStoreAppointmentBooked = returnRefundEligibilityService.bookStoreAppointment(returnRefundEligibilityRequest);
            log.info("[bookStoreAppointments] store appointment action:{}", isStoreAppointmentBooked);
        } catch (Exception exception) {
            log.error("[bookStoreAppointments] exception while processing store appointment action :{}", exception.getMessage());
            return new ResponseEntity<>(false, HttpStatus.EXPECTATION_FAILED);
        }
        return new ResponseEntity<>(isStoreAppointmentBooked, HttpStatus.OK);
    }

    @PostMapping(value = "/lite/return/eligibility")
    public Map<Integer, ReturnPolicyResponse> getReturnPolicyBulk(@RequestBody List<ReturnPolicyInputDTO> returnPolicyInputDTOList) {
        long startTime = System.currentTimeMillis(); // Start time

        log.info("[ReturnDetailsController][getReturnPolicyBulk] Received request: {}", returnPolicyInputDTOList);

        Map<Integer, ReturnPolicyResponse> response = returnRefundEligibilityService.fetchReturnPolicyInBulk(returnPolicyInputDTOList);

        long endTime = System.currentTimeMillis(); // End time
        log.info("[ReturnDetailsController][getReturnPolicyBulk] Response generated in {} ms", (endTime - startTime));

        return response;
    }

    @GetMapping("/returns/{orderId}")
    public ResponseEntity<List<ReturnOrderDTO>> getReturnOrder(@PathVariable("orderId") String orderId) {
        ResponseEntity<List<ReturnOrderDTO>> response = null;
        List<ReturnOrderDTO> returnOrders = null;
        if (StringUtils.isBlank(orderId)) {
            log.error("orderId is blank and hence returns can not be fetched");
            return new ResponseEntity<>(returnOrders, HttpStatus.BAD_REQUEST);
        }

        try {
            log.info("Getting all returns linked to orderId: {}", orderId);
            returnOrders = returnOrderService.getReturnDetails(Integer.parseInt(orderId));
            response = new ResponseEntity<>(returnOrders, HttpStatus.OK);
        } catch (Exception e) {
            response = new ResponseEntity<>(returnOrders, HttpStatus.INTERNAL_SERVER_ERROR);
            log.error("[getReturnOrder] Exception Occurred ", e);
        }

        log.info("ReturnOrdersList: {}", returnOrders);
        return response;

    }

    @ApiMethod(path = "insurance/eligibility/{increment_id}", description = "Verifies if insurance is eligible", consumes = {
            MediaType.ALL_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @RequestMapping(value = "insurance/eligibility/{increment_id}", method = RequestMethod.POST)
    public @ApiResponseObject
    ResponseEntity<InsuranceEligibilityResponse> verifyInsuranceEligibilty(@ApiPathParam @RequestBody InsuranceEligibilityRequest insuranceEligibilityRequest, @PathVariable @ApiPathParam(name = "increment_id", description = "increment Id of order") Integer increment_id) {
        log.info("[ReturnController][verifyInsuranceEligibilty] start executing for incrementId: {}", increment_id);
        ResponseEntity<InsuranceEligibilityResponse> response = null;
        InsuranceEligibilityResponse insuranceEligibilityResponse = null;
        try {
            insuranceEligibilityResponse = insuranceService.getInsurancEligibilityDetailsForItems(increment_id, insuranceEligibilityRequest);
            if (insuranceEligibilityResponse != null && insuranceEligibilityResponse.getErrorMessage() != null) {
                response = new ResponseEntity<>(insuranceEligibilityResponse, HttpStatus.BAD_REQUEST);
            } else {
                log.info("[ReturnController][verifyInsuranceEligibilty] Insurance eligibility verified successfully." + insuranceEligibilityResponse.toString());
                response = new ResponseEntity<>(insuranceEligibilityResponse, HttpStatus.OK);
            }
        } catch (Exception ex) {
            log.error("[ReturnController][verifyInsuranceEligibilty]  Exception occured while verifying insurance eligibility of order", ex.getStackTrace());
            response = new ResponseEntity<>(insuranceEligibilityResponse, HttpStatus.UNPROCESSABLE_ENTITY);
        }
        log.info("InsuranceEligibilityResponse: {}", insuranceEligibilityResponse);
        return response;
    }

    @RequestMapping(value = "/delayed-pickup-orders", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<GetDelayedPickupOrdersResponse> getDelayedPickupOrders(@ApiPathParam(description = "Delayed orders info request") @RequestBody GetDelayedPickupOrdersRequest delayedRequest) {
        log.info("[ReturnController][getDelayedPickupOrders] Received req " + delayedRequest);
        GetDelayedPickupOrdersResponse getDelayedPickupOrdersResponse = null;
        ResponseEntity<GetDelayedPickupOrdersResponse> response = null;
        try {
            if (delayedRequest.getIncrementIds() != null && delayedRequest.getIncrementIds().isEmpty()) {
                log.info("[ReturnController][getDelayedPickupOrders] request is empty");
                return new ResponseEntity<>(getDelayedPickupOrdersResponse, HttpStatus.BAD_REQUEST);
            }

            getDelayedPickupOrdersResponse = returnOrderService.getDelayedPickupOrders(delayedRequest);
            response = new ResponseEntity<>(getDelayedPickupOrdersResponse, HttpStatus.OK);
        } catch (Exception e) {
            log.error("[ReturnController][getDelayedPickupOrders] Exception occurred while fetching recent orders info " + e.getMessage(), e);
            response = new ResponseEntity<>(getDelayedPickupOrdersResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return response;
    }

    @RequestMapping(value = "{incrementId}/return-refund-mapping", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<GetReturnRefundMappingResponse> getReturnRefundMapping(
            @PathVariable @ApiPathParam(name = "incrementId", description = "incrementId Id of order") Integer incrementId) {
        log.info("[ReturnController][getReturnRefundMapping] Received req " + incrementId);
        GetReturnRefundMappingResponse getReturnRefundMappingResponse = null;
        ResponseEntity<GetReturnRefundMappingResponse> response = null;
        try {
            if (incrementId == null || incrementId == 0) {
                log.info("[ReturnController][getReturnRefundMapping] Invalid request");
                return new ResponseEntity<>(getReturnRefundMappingResponse, HttpStatus.BAD_REQUEST);
            }

            getReturnRefundMappingResponse = returnOrderService.getReturnRefundMapping(incrementId);
            response = new ResponseEntity<>(getReturnRefundMappingResponse, HttpStatus.OK);
        } catch (Exception e) {
            log.error("[ReturnController][getReturnRefundMapping] Exception occurred while fetching return refund mapping " + e.getMessage(), e);
            response = new ResponseEntity<>(getReturnRefundMappingResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return response;
    }

    @RequestMapping(value = "/refund/methods/{magento_item_id}", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<RefundMethodsResponse> findExchangeRefundMethod(@PathVariable("magento_item_id") Integer magentoItemId, @RequestParam("source") String source) {
        RefundMethodsResponse refundMethodsResponse = new RefundMethodsResponse();
        ResponseEntity<RefundMethodsResponse> response = null;
        try {
            log.info("[findExchangeRefundMethod] magento : {}, source : {}", magentoItemId, source);
            if (magentoItemId == null)
                return new ResponseEntity<>(new RefundMethodsResponse(), HttpStatus.BAD_REQUEST);
            refundMethodsResponse = exchangeRefundMethodService.findExchangeRefundMethod(magentoItemId,source, null);
            response = new ResponseEntity<>(refundMethodsResponse, HttpStatus.OK);
        } catch (Exception e) {
            response = new ResponseEntity<>(refundMethodsResponse, HttpStatus.INTERNAL_SERVER_ERROR);
            log.error("[findExchangeRefundMethod] Exception Occurred "+ e);

        }
        return response;
    }

    @RequestMapping(value = "/api-1.0/reasons", method = RequestMethod.GET)
    public ReturnReasonsResponse getReturnReasonList(@RequestParam("platform") String platform, @RequestParam("category") String category) {
        if (StringUtils.isEmpty(platform) || StringUtils.isEmpty(category)) {
            return new ReturnReasonsResponse();
        }
        return returnReasonService.getReturnReasons(platform, category);
    }


    @ApiMethod(path = "/save-delight-action", description = "called to notify VSM if all action related to need approval pending return taken at SCM side or not")
    @RequestMapping(value = "/save-delight-action", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<Boolean> processDelightActionUpdateOperations(@ApiBodyObject @RequestBody NeedApprovalRequest needApprovalRequest) throws Exception {
        log.info("[saveDelightAction] needApprovalStatusRequest :{}", needApprovalRequest.toString());
        boolean isSaveDelightActionOperationExecuted;
        try {
            isSaveDelightActionOperationExecuted = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
            log.info("[saveDelightAction] save delight action:" + isSaveDelightActionOperationExecuted);
        } catch (Exception exception) {
            log.error("[saveDelightAction] exception while processing save delight action :" +exception.getMessage());
            return new ResponseEntity<>(false, HttpStatus.EXPECTATION_FAILED);
        }
        return new ResponseEntity<>(isSaveDelightActionOperationExecuted, HttpStatus.OK);
    }

    @ApiMethod(path = "/item/{magento_item_id}/return-details", description = "Get return details using magento ID.", produces = {
            MediaType.APPLICATION_JSON_VALUE}, responsestatuscode = "200")
    @GetMapping(value = "/item/{magento_item_id}/return-details")
    @HandleException
    public @ApiResponseObject ResponseEntity<ReturnDetailsResponse> getReturnDetailsByMagentoId(@PathVariable(value = "magento_item_id") Long magentoItemId,
                                                                                                @RequestParam(name = "order-id", required = false) Integer orderId) {
        if (magentoItemId == null) {
            return ResponseEntity.badRequest().build();
        }
        return ResponseEntity.ok(returnDetailsService.getReturnDetailsByMagentoId(magentoItemId, null));
    }

    @ApiMethod(path = "/item/{item-identifier}/cancellability", description = "Returns whether a cancellable return exists against a particular item. item-identifier can be either of magento item id or uw item id", produces = {
            MediaType.APPLICATION_JSON_VALUE}, responsestatuscode = "200")
    @GetMapping(value = "/item/{item-identifier}/cancellability")
    public @ApiResponseObject ResponseEntity<ReturnCancellabilityResponse> getReturnCancellabilityForItem(@PathVariable(value = "item-identifier") Integer itemId) {
        if (itemId == null) {
            return ResponseEntity.badRequest().build();
        }
        log.info("[ReturnServiceImpl][getReturnCancellabilityForItem]: Fetching return cancellability for item id {}", itemId);
        return ResponseEntity.ok(returnDetailsService.getReturnCancellabilityForItem(itemId));
    }

    @RequestMapping(value ="/disable-auto-cancel-flag/{return-id}",method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<String> updateAutoCancelFlagFalseInReturnOrder(@PathVariable(value = "return-id") Integer returnId) {
        boolean isUpdated = returnUpdateService.setAutoCancelFlagAsFalse(returnId);
        return (isUpdated ? new ResponseEntity<>("Return_Order_Auto_Cancel_flag disabled successfully for return-id: " + returnId, HttpStatus.OK) : new ResponseEntity<>("Return_order_not_found for return_id: " + returnId, HttpStatus.NOT_FOUND));
    }


    @RequestMapping(value = "/flow/{incrementId}", method = RequestMethod.GET)
    public ReturnFlowResponse getReturnFlowResponseList(@PathVariable("incrementId") Integer incrementId, @RequestParam(value="checkScmEligibility", required = false,defaultValue = "false") boolean checkScmEligibility) {
        log.info(" [ReturnDetailsController getReturnFlowResponseList] " + " " + "incrementId" + incrementId);
        return returnFlowResponseService.getReturnFlowResponse(incrementId,checkScmEligibility);
    }

    @RequestMapping(value = "/flow/result/{incrementId}", method = RequestMethod.GET)
    public Boolean getReturnFlowResult(@PathVariable("incrementId") Integer incrementId) {
        log.info(" [ReturnDetailsController getReturnFlowResponseList] " + " " + "incrementId" + incrementId);
        return returnFlowResponseService.getReturnFlowResponseResult(incrementId,false);
    }

    @RequestMapping(value = "/fraud/threshold/reached/{incrementId}", method = RequestMethod.GET)
    public ThresholdCheckResponse isFraudThresholdReached(@PathVariable("incrementId") Integer incrementId, @RequestBody List<Integer> uwItemIds) {
        log.info(" [ReturnDetailsController isFraudThresholdReached] " + " " + "incrementId" + incrementId);
        //call the service to calculate if the current count is (threshold - 1), if yes return true else false
        return returnFlowResponseService.isFraudThresholdReached(incrementId, uwItemIds, null);
    }

    @RequestMapping(value = "/exchange-item/dispatchable", method = RequestMethod.POST)
    public  Boolean isExchangeItemDispatchable(@RequestBody ExchangeItemDispatchableDTO exchangeItemDispatchableDTO){
        log.info("[isExchangeItemDispatchable] request for is dispatchable exchange item: "+exchangeItemDispatchableDTO);
        return  returnRefundRuleService.isExchangeItemDispatchable(exchangeItemDispatchableDTO);
    }

    @RequestMapping(value = "/is/dispatchable", method = RequestMethod.POST)
    public Boolean isDispatchable(Integer returnId, String ruleEngineStatusForDispatch){
        log.info("[isDispatchable] request for is dispatchable : {}, ruleEngineStatusForDispatch : {}", returnId, ruleEngineStatusForDispatch);
        return  returnRefundRuleService.isDispatchable(returnId, ruleEngineStatusForDispatch);
    }

    @RequestMapping(value = "/api-1.0/reasons/approval", method = RequestMethod.GET)
    public ReturnReasonsResponse getFollowReasonsList(){
        return returnReasonService.getApprovalReasons();
    }

    @RequestMapping(value ="get-return-info/{return-id}",method = RequestMethod.GET)
    public ReturnReverseInfoResponse getReturnInfo(@PathVariable(value = "return-id") Integer returnId){
        return returnOrderService.getReturnRefundInfo(returnId);
    }

    @HandleException
    @ApiMethod(path = "/get", description = "Get return details based on identifier type and value", produces = {
            MediaType.APPLICATION_JSON_VALUE }, responsestatuscode = "200")
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<ReturnDetailsDTO> getReturnDetails(@Valid @RequestBody GetReturnDetailsRequest request, BindingResult bindingResult) {

        log.info("[ReturnDetailsController][getReturnDetails] request : {}", request);
        if(bindingResult.hasErrors()){
            throw new RuntimeException("Invalid Request");
        }

        ReturnDetailsDTO returnDetailsDTO = returnOrderActionService.getReturnDetailsByIdentifier(request.getIdentifierType(), request.getIdentifierValues(), true);

        return ResponseEntity.ok().body(returnDetailsDTO);
    }

    @HandleException
    @GetMapping("/getReturnDetails/{identifierType}/{identifierValue}")
    public ResponseEntity<com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO> getReturnOrderDetails(@PathVariable("identifierType") String identifierType, @PathVariable("identifierValue") String identifierValue) {
        com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO returnDetailsDTO = returnOrderActionService.getReturnOrderDetails(identifierType, identifierValue);

        return ResponseEntity.ok().body(returnDetailsDTO);
    }

    @HandleException
    @GetMapping("/getReturnIdByUwItemId/{uwItemId}")
    public ResponseEntity<Integer> getReturnId(@PathVariable("uwItemId") Integer uwItemId) {
        Integer returnId = returnOrderActionService.getReturnId(uwItemId);
        return ResponseEntity.ok().body(returnId);
    }

    @RequestMapping(value="/getUwItemId/{return-id}",method = RequestMethod.GET)
    public ResponseEntity<Integer> getUwItemIdByReturnId(@PathVariable("return-id") Integer returnId){
        return new ResponseEntity<>(null!=returnId ? returnOrderActionService.getUwItemIdByReturnId(returnId): null,HttpStatus.OK);
    }

    @RequestMapping(value="/getItemDetails/{return-id}",method = RequestMethod.GET)
    public ResponseEntity<Map<String, Integer>> getItemDetailsByReturnId(@PathVariable("return-id") Integer returnId){
        return new ResponseEntity<>(null!=returnId ? returnOrderActionService.getItemDetailsByReturnId(returnId): null,HttpStatus.OK);
    }

    @RequestMapping(value = "/getReversePickupCouriers/{pin-code}",method = RequestMethod.GET)
    public ResponseEntity<List<ReversePickupPincodeDto>> getActiveLkartOrReversePickupCourier(@PathVariable("pin-code") Integer pinCode , @RequestParam(defaultValue = "false") boolean isActiveLkartCourierRequired){
        return new ResponseEntity<>(reversePickUpService.getActiveLkartOrReversePickupCourier(pinCode,isActiveLkartCourierRequired),HttpStatus.OK);
    }
    @RequestMapping(value = "return-switch-active/{identifierType}/{identifierValue}",method = RequestMethod.GET)
    public ResponseEntity<Map<String,Boolean>> getIsReturnFromNewFlow(@PathVariable("identifierType") String identifierType , @PathVariable( "identifierValue") String identifierValue){
        log.info("[getIsReturnFromNewFlow] identifierType : {}, identifierValue : {}", identifierType, identifierValue);
        return new ResponseEntity<>(returnOrderActionService.getIsReturnFromNewFlow(identifierType,identifierValue),HttpStatus.OK);
    }

    @RequestMapping(value ="get-order-info/{group-id}",method = RequestMethod.GET)
    public Integer getOrderInfo(@PathVariable(value = "group-id") Integer groupId){
        log.info("[getOrderInfo] groupId : {}", groupId);
        return returnOrderService.getOrderDetailsForGroupId(groupId);
    }

    @RequestMapping(value = "get/return-reason/",method = RequestMethod.POST)
    public ResponseEntity<List<com.lenskart.ordermetadata.dto.response.ReturnReasonTableDTO>> getIsReturnFromNewFlow(@RequestBody List<Integer> uwItemIds){
        log.info("[getIsReturnFromNewFlow] uwItemId  : {}", uwItemIds);
        return new ResponseEntity<>(returnOrderActionService.getReturnReason(uwItemIds),HttpStatus.OK);
    }

    @RequestMapping(value = "get/return-source/{uw_item_id}",method = RequestMethod.GET)
    public ResponseEntity<String> getReturnSource(@PathVariable("uw_item_id") Integer uwItemId){
        log.info("[getReturnSource] uwItemId : {}", uwItemId);
        return new ResponseEntity<>(returnOrderActionService.getReturnSource(uwItemId),HttpStatus.OK);
    }

    @RequestMapping(value = "update-receiving-flag/{flag}/{returnId}/{incrementId}",method = RequestMethod.POST)
    public void updateReceivingFlag(@PathVariable String flag, @PathVariable Integer returnId, @PathVariable Integer incrementId){
        log.info("[updateReceivingFlag] flag : {}, returnId : {}, incrementId : {}", flag, returnId, incrementId);
        returnOrderActionService.updateReceivingFlag(flag,returnId,incrementId);
    }

    @RequestMapping(value = "returnOrderItem/{uwItemId}",method = RequestMethod.GET)
    public ResponseEntity<ReturnOrderItemDTO> getReturnOrderItemByUwItemId(@PathVariable("uwItemId") Integer uwItemId){
        log.info("[getReturnOrderItemByUwItemId] uwItemId : {}", uwItemId);
        return new ResponseEntity<>(returnOrderActionService.getReturnOrderItemByUwItemId(uwItemId),HttpStatus.OK);
    }

    @RequestMapping(value = "get/identifier-values-mapping",method = RequestMethod.POST)
    public ResponseEntity<Map<String,Boolean>> getIdentifierValuesMappingWithNewFlow(@Valid @RequestBody GetReturnDetailsRequest request, BindingResult bindingResult){
        if(bindingResult.hasErrors()){
            throw new RuntimeException("Invalid Request");
        }
        Map<String,Boolean> identifierValuesMappingWithNewFlow = returnOrderActionService.getIdentifierValuesMappingWithNewFlow(request.getIdentifierType(), request.getIdentifierValues());
        return ResponseEntity.ok().body(identifierValuesMappingWithNewFlow);
    }
    @RequestMapping(value = "get-order-id/{return-id}",method = RequestMethod.GET)
    public Integer getOrderIdByReturnId(@PathVariable(value = "return-id") Integer returnId){
        return returnOrderService.getOrderIdByReturnId(returnId);
    }

    @ApiMethod(path = "/item/return-details", description = "Get return details using magento ID.", produces = {
            MediaType.APPLICATION_JSON_VALUE}, responsestatuscode = "200")
    @PostMapping(value = "/item/return-details")
    @HandleException
    public @ApiResponseObject ResponseEntity<ReturnDetailsResponse> getReturnDetailsByMagentoIdUtil(@RequestBody ReturnDetailInfoUtilRequestDto returnDetailInfoUtilRequestDto) {
        if (Objects.isNull(returnDetailInfoUtilRequestDto) || Objects.isNull(returnDetailInfoUtilRequestDto.getMagentoItemId())) {
            return ResponseEntity.badRequest().build();
        }
        log.info("[getReturnDetailsByMagentoIdUtil] request : {}",returnDetailInfoUtilRequestDto);
        return ResponseEntity.ok(returnDetailsService.getReturnDetailsByMagentoId(returnDetailInfoUtilRequestDto.getMagentoItemId(),returnDetailInfoUtilRequestDto));
    }

    @GetMapping("/reverse/pickup/pincode/courier")
    List<String> getReversePickupPincodeCourier(){
        return reversePickupPincodeRepository.getReversePickupPincodeCourier();
    }

    @GetMapping("/reverse/order/address/update/{group_id}")
    ReturnDetailAddressUpdate getReturnOrderAddressUpdate(@PathVariable("group_id") Long groupId){
        return returnOrderAddressUpdateRepository.getReturnOrderAddressUpdate(groupId, "reverse");
    }

    @PostMapping("/return-info/get")
    List<ReturnDTO> getReturnDetails(@RequestBody Map<String, Object> filterParams) throws ParseException {
        log.info("[getReturnDetails] filterparams : {}", filterParams);
        try {
            return returnDetailsService.getReturnDetails(filterParams);
        }
        catch (Exception e) {
            log.error("[getReturnDetails] return-info error :{}", e.getMessage(), e);
            throw e;
        }
    }

    @RequestMapping(value = "/refund/methods", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<RefundMethodsResponse> findExchangeRefundMethodUtil(@RequestBody RefundMethodRequestByMagentoDto request) {
        RefundMethodsResponse refundMethodsResponse = new RefundMethodsResponse();
        ResponseEntity<RefundMethodsResponse> response = null;
        try {
            log.info("[findExchangeRefundMethodUtil] : {}", request);
            if (Objects.isNull(request) || request.getMagentoItemId() == null)
                return new ResponseEntity<>(new RefundMethodsResponse(), HttpStatus.BAD_REQUEST);
            refundMethodsResponse = exchangeRefundMethodService.findExchangeRefundMethod(request.getMagentoItemId(), request.getSource(), request);
            response = new ResponseEntity<>(refundMethodsResponse, HttpStatus.OK);
        } catch (Exception e) {
            response = new ResponseEntity<>(refundMethodsResponse, HttpStatus.INTERNAL_SERVER_ERROR);
            log.error("[findExchangeRefundMethodUtil] Exception Occurred "+ e);

        }
        return response;
    }

    @GetMapping("/return-info/status/{return_id}/get")
    String getReturnDetails(@PathVariable("return_id") Integer returnId){
        return returnOrderActionService.getReturnOrderStatusById(returnId);
    }

    //called from VSM for returns to be created in new return service
    @RequestMapping(value ="/return-tracker/{increment-id}",method = RequestMethod.GET)
    public ReturnTrackerResponse getReturnTrackerResponse(@PathVariable(name = "increment-id") Integer incrementId){
        ReturnTrackerResponse response = null;
        if(null!=incrementId && incrementId!=0) {
            response = returnOrderService.getReturnTrackerInfoByIncrementId(incrementId);
        }
        return response;
    }

    //called from VSM for returns to be created in new return service
    @RequestMapping(value ="/return-id-status-mapping/{increment-id}",method = RequestMethod.GET)
    public Map<Integer, String> getReturnIdByStatusMapping(@PathVariable(name = "increment-id") Integer incrementId){
        Map<Integer, String> returnIdStatusMap = null;
        if(null!=incrementId && incrementId!=0) {
            returnIdStatusMap = returnOrderActionService.getReturnIdByStatusMapping(incrementId);
        }
        return returnIdStatusMap;
    }

    @RequestMapping(value ="/get-returns-for-vsm",method = RequestMethod.POST)
    public GetReturnsResponse getReturnDetails(@RequestBody GetReturnsRequest returnsRequest){
        log.info("[getReturnDetails] get-returns-for-vsm api request :{}", returnsRequest);
        return returnOrderActionService.getReturns(returnsRequest);
    }

    //called from order-ops
    @RequestMapping(value = "get-refund-dispatch-point", method = RequestMethod.POST)
    public @ApiResponseObject @ResponseBody
    ResponseEntity<RefundDispatchResponse> getRefundDispatchPoint(@RequestBody RefundDispatchRequest request) {
        log.info(" [getRefundDispatchPoint] request :{}", request);
        try {
            if (request == null || request.getUwItemId() == null && request.getReturnId() == null) {
                return ResponseEntity.badRequest().build();
            }
            return ResponseEntity.ok().body(refundUtilsService.getRefundDispatchPoint(request));
        } catch (Exception e) {
            log.error(Level.SEVERE.getName(),"[ReturnRulesController][fetchReturnRule][Error] :" , e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    //called from VSM for returns to be created in new return service
    @RequestMapping(value ="/return-address-update/{increment-id}",method = RequestMethod.GET)
    public ReturnDetailAddressUpdateDTO getReturnAddressUpdate(@PathVariable(name = "increment-id") Integer incrementId){
        ReturnDetailAddressUpdateDTO response = null;
        if(null!=incrementId && incrementId!=0) {
            response = returnOrderAddressUpdateService.getReturnAddressUpdate(incrementId);
        }
        return response;
    }

    @GetMapping("/get/filters")
    @HandleException
    @CrossOrigin(origins = {"https://vsm.ui.local.lenskart.com:4000", "https://vsm.scm.preprod.lenskart.com", "https://vsm.scm.lenskart.com", "https://vsm-ui-preprod.lenskart.com", "https://vsm-ui.ces.lenskart.com" })
    ResponseEntity<GetFiltersResponse> getFilters(){
        return ResponseEntity.ok(returnDetailsService.getFilters());
    }

    @RequestMapping(value ="/get/report",method = RequestMethod.POST)
    @CrossOrigin(origins = {"https://vsm.ui.local.lenskart.com:4000", "https://vsm.scm.preprod.lenskart.com", "https://vsm.scm.lenskart.com", "https://vsm-ui-preprod.lenskart.com", "https://vsm-ui.ces.lenskart.com" })
    public GetReturnReportResponse getReturnReportDetails(@RequestBody GetReturnsRequest returnsRequest){
        log.info("[getReturnDetails] getReturnReportDetails api request :{}", returnsRequest);
        return returnOrderActionService.getReturnReportDetails(returnsRequest);
    }

    @RequestMapping(value ="/get/report/export",method = RequestMethod.POST)
    @CrossOrigin(origins = {"https://vsm.ui.local.lenskart.com:4000", "https://vsm.scm.preprod.lenskart.com", "https://vsm.scm.lenskart.com", "https://vsm-ui-preprod.lenskart.com", "https://vsm-ui.ces.lenskart.com" })
    public void getReturnReportDetailsExport(@RequestBody GetReturnsRequest returnsRequest, HttpServletResponse response) throws CsvRequiredFieldEmptyException, CsvDataTypeMismatchException, IOException {
        log.info("[getReturnDetails] getReturnReportDetails api request :{}", returnsRequest);
        GetReturnReportResponse returnReportDetails = returnOrderActionService.getReturnReportDetails(returnsRequest);
        String dateFormat = DateUtil.getCurrentDateTime("dd-MM-yyyy_hh_mma");
        String filename = "Query-DATA_" + dateFormat + ".csv";
        CsvUtils.exportCsv(filename, response, returnReportDetails.getReturnDetails(), ReturnDetailVSM.class);
    }

    //called from VSM for returns to be created in new return service
    @RequestMapping(value ="return-address-update/{IDENTIFIER_TYPE}/{IDENTIFIER_VALUE}",method = RequestMethod.GET)
    public ReturnDetailAddressUpdateDTO getReturnAddressUpdate(@PathVariable(name = "IDENTIFIER_TYPE") String identifierType, @PathVariable("IDENTIFIER_VALUE") String identifierValue){
        ReturnDetailAddressUpdateDTO response = null;
        if("INCREMENT_ID".equalsIgnoreCase(identifierType)){
            response = returnOrderAddressUpdateService.getReturnAddressUpdate(Integer.valueOf(identifierValue));
        }
        if("GROUP_ID".equalsIgnoreCase(identifierType)){
            response = returnOrderAddressUpdateService.getReturnAddressUpdate(Long.valueOf(identifierValue));
        }
        return response;
    }

    //called from VSM for returns to be created in new return service
    @RequestMapping(value ="courier/details/{IDENTIFIER_TYPE}/{IDENTIFIER_VALUE}",method = RequestMethod.GET)
    public List<Map<String, Object>> getCourierDetails(@PathVariable("IDENTIFIER_TYPE") String identifierType, @PathVariable("IDENTIFIER_VALUE") String identifierValue) throws ParseException {
        log.info("[getCourierDetails] identifierType : {}, identifierValue : {}",identifierType,identifierValue);
        return reverseCourierDetailService.getCourierDetails(identifierType, identifierValue);
    }

    @RequestMapping(value ="d365/return-tracking/{return_id}",method = RequestMethod.GET)
    public D365ReturnTrackingResponse getD365ReturnTrackingDetails(@PathVariable("return_id") Integer returnId) {
        log.info("[getD365ReturnTrackingDetails] returnId : {}",returnId);
        return d365ReturnTrackingService.findByReturnId(returnId);
    }

    @GetMapping("get/exchange-dispatch-point")
    ResponseEntity<String> getRuleEngineResponseForDispatch(@RequestParam Integer uwItemId, @RequestParam Integer returnId) {
        return ResponseEntity.ok(returnRefundRuleService.getRuleEngineResponseForDispatch(uwItemId, returnId));
    }

    @GetMapping("get/refund-dispatch-point")
    ResponseEntity<String> getRuleEngineResponseForRefundDispatch(@RequestParam Integer uwItemId, @RequestParam Integer returnId) {
        return ResponseEntity.ok(returnRefundRuleService.getRuleEngineResponseForRefundDispatch(uwItemId, returnId));
    }

    @PostMapping("get/return-status-heading-detail")
    ResponseEntity<ReturnStatusHeadingDetail> updateRefundHeading(@RequestBody UpdateRefundHeadingRequest refundHeadingRequest) {
        return ResponseEntity.ok(returnSubHeaderTimelineDateFetchService.updateRefundHeading(refundHeadingRequest));
    }

    @PostMapping(value = "get/reverse-tat-info")
    ResponseEntity<ReverseTatDTO> getReverseTatInfo(@Valid @RequestBody ReverseTatRequest reverseTatRequest, BindingResult bindingResult) {
        if (bindingResult != null && bindingResult.hasErrors()) {
            String errors = String.join(", ",bindingResult.getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).toList());
            log.error("Binding errors in getReverseTatInfo: {}", errors);
            return ResponseEntity.badRequest().build();
        }
        return ResponseEntity.ok(reversePickUpService.getReverseTatInfo(reverseTatRequest));
    }
    @PostMapping(value = "return/validateAwbReverse")
    public ResponseEntity<AwbReverseDto> validateReturnTypeReverse(@RequestBody AwbNumberValidationRequest validationRequest) {
        AwbReverseDto awbReverseDto = returnDetailsService.validateReturnTypeReverse(validationRequest);
        return ResponseEntity.ok(awbReverseDto);
    }

    @GetMapping(value = "get/return-history/{return_id}")
    ResponseEntity<List<ReturnHistoryDTO>> getReturnHistory(@PathVariable("return_id") Integer returnId) {
        return ResponseEntity.ok(returnEventService.getReturnHistory(returnId));
    }

    @GetMapping(value = "get/delight-action/{return_id}")
    ResponseEntity<DelightAction> getDelightAction(@PathVariable("return_id") Integer returnId) {
        return ResponseEntity.ok(delightActionService.getDelightAction(returnId));
    }

    @PostMapping(value = "get/bulk-cancellation-history")
    ResponseEntity<List<BulkCancellationEvent>> getBulkCancellationHistory(@RequestBody GetBulkCancellationEventsRequest request) {
        return ResponseEntity.ok(returnEventService.getBulkCancellationHistory(request));
    }

    @GetMapping(value = "get/delight-action-details/{return_id}")
    ResponseEntity<ReturnResponse> getDelightActionDetails(@PathVariable("return_id") Integer returnId) {
        return ResponseEntity.ok(delightActionService.getReturnDelightDetails(returnId));
    }

    @RequestMapping(value = "exchange-refund-dispatch-point/{uwItemId}/{returnId}", method = RequestMethod.GET)
    ResponseEntity<?> getExchangeRefundDispatchPoint(@PathVariable("uwItemId") Integer uwItemId,@PathVariable("returnId") Integer returnId) {
        log.info(" [getExchangeRefundDispatchPoint] " + "uwItemId" + uwItemId + ",returnId" + returnId);
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (uwItemId != null && uwItemId > 0 && returnId != null && returnId  > 0 ) {
                String exchangeDispatchPoint = returnRefundRuleService.getRuleEngineResponseForDispatch(uwItemId,returnId);
                String refundDispatchPoint = returnRefundRuleService.getRuleEngineResponseForRefundDispatch(uwItemId,returnId);
                result.put("success", true);
                result.put("exchange_dispatch_point",exchangeDispatchPoint);
                result.put("refund_dispatch_point",refundDispatchPoint);
                return new ResponseEntity<Object>(result, HttpStatus.OK);
            }
            else
            {
                return ResponseEntity.badRequest().body(uwItemId + "/"+returnId + " uwItemId/returnId is not valid input. Valid input");
            }
        } catch (Exception e) {
            log.error(Level.SEVERE.getName(),"[ReturnRulesController][fetchReturnRule][Error] :" , e);
            result.put("success", false);
            return new ResponseEntity<Object>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "get/return_reasons/{return_id}/{uw_item_id}",method = RequestMethod.GET)
    public ResponseEntity<ReturnReasonDTO> getReturnReasons(@PathVariable("return_id") Integer returnId, @PathVariable("uw_item_id") Integer uwItemId){
        log.info("[getIsReturnFromNewFlow] returnId  : {} uwItemId : {}", returnId, uwItemId);
        return new ResponseEntity<>(returnReasonService.getReturnReason(uwItemId, returnId),HttpStatus.OK);
    }

    @RequestMapping(value = "get/reverse_awb_courier/mapping",method = RequestMethod.POST)
    public ResponseEntity<List<ReverseAwbCourierMapperDTO>> getReverseAwbCourierMap(@RequestBody List<String> reverseAwbs){
        log.info("[getReverseAwbCourierMap] reverseAwbs : {}", reverseAwbs);
        return new ResponseEntity<>(reverseCourierDetailService.getReverseAwbCourierMap(reverseAwbs),HttpStatus.OK);
    }

    @RequestMapping(value = "get/return_reasons",method = RequestMethod.POST)
    public ResponseEntity<List<ReturnReasonDTO>> getReturnReasons(@RequestParam("order_id") Integer orderId){
        log.info("[getReturnReasons] orderId : {}", orderId);
        return new ResponseEntity<>(returnReasonService.getReturnReasonsList(orderId),HttpStatus.OK);
    }

    @GetMapping("get/frame-broken/flag/{incrementId}")
    public ResponseEntity<String> getFrameBrokenOrderFlag(@PathVariable("incrementId") Integer incrementId) {
        log.info("[getFrameBrokenOrderFlag] incrementId {}", incrementId);
        if (incrementId == null) {
            return ResponseEntity.badRequest().body(null);
        }
        return ResponseEntity.ok(junoService.getFrameBrokenOrderFlag(incrementId));
    }

    @PostMapping("fetch-return-orders")
    ResponseEntity<List<ReturnDetailedResponse>> getOrderDetailsByTelephone(@RequestBody OrderTelephoneMapperRequestDTO returnDetailsRequest) {
        List<ReturnDetailedResponse> returnDetailedResponses = null;
        try{
            log.info("[getOrderDetailsByTelephone] request : {}", returnDetailsRequest);
            returnDetailedResponses = returnDetailsService.getReturnsDetailedResponse(returnDetailsRequest);
        }catch (Exception exception){
            log.error("[getOrderDetailsByTelephone] error : {}", exception.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
        return ResponseEntity.ok().body(returnDetailedResponses);
    }

    @GetMapping("get/store-credit-details/{magento}")
    public ResponseEntity<StoreCreditRefundDetails> getStoreCreditRefundDetails(@PathVariable("magento") Long magentoId) {
        log.info("[getStoreCreditRefundDetails] magentoId {}", magentoId);
        if (magentoId == null) {
            return ResponseEntity.badRequest().body(null);
        }
        StoreCreditRefundDetails scRefundDetails = cancelAndConvertService.getScRefundDetails(magentoId);
        log.info("[getStoreCreditRefundDetails] magentoId : {}, scRefundDetails : {}", magentoId, scRefundDetails);
        return ResponseEntity.ok(scRefundDetails);
    }

    @PostMapping("get/exchange_details")
    public ResponseEntity<List<Long>> getExchangeDetails(@RequestBody List<Long> magentoIdList) {
        log.info("[getExchangeDetails] magentoIdList {}", magentoIdList);
        if (CollectionUtils.isEmpty(magentoIdList)) {
            return ResponseEntity.badRequest().body(null);
        }
        List<Long> exchangeCreatedButNotCancelledDetails = null;
        try{
            exchangeCreatedButNotCancelledDetails = cancelAndConvertService.getExchangeCreatedButNotCancelledDetails(magentoIdList);
        }catch (Exception exception){
            log.error("[getExchangeDetails] magentoIdList : {}, exception : {}", magentoIdList, exception.getMessage());
            return ResponseEntity.internalServerError().body(null);
        }
        return ResponseEntity.ok(exchangeCreatedButNotCancelledDetails);
    }

    @PostMapping("get/exchange_master_mapping")
    public ResponseEntity<ExchangeMasterMapping> getExchangeMasterMapping(@RequestBody List<Long> magentoIdList) {
        log.info("[getExchangeMasterMapping] magentoIdList {}", magentoIdList);
        if (CollectionUtils.isEmpty(magentoIdList)) {
            return ResponseEntity.badRequest().body(null);
        }
        ExchangeMasterMapping exchangeMasterMapping = null;
        try{
            exchangeMasterMapping = cancelAndConvertService.getExchangeMasterMapping(magentoIdList);
        }catch (Exception exception){
            log.error("[getExchangeMasterMapping] magentoIdList : {}, exception : {}", magentoIdList, exception.getMessage());
            return ResponseEntity.internalServerError().body(null);
        }
        return ResponseEntity.ok().body(exchangeMasterMapping);
    }

    @GetMapping(value = "{magentoItemId}/details")
    ResponseEntity<ReturnStatusResponse> getActiveReturns(@PathVariable("magentoItemId") Integer magentoItem ) {
        try{
            log.info("Calling getReturnDetails for magentoItemId {}", magentoItem);
            ReturnStatusResponse returnStatusResponse = returnOrderActionService.getReturnResponse(magentoItem);
            return ResponseEntity.ok(returnStatusResponse);
        } catch (Exception e){
            log.error("Exception caught in getReturnDetails for magentoItemId {} for {}", e.getMessage(), magentoItem, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


}
