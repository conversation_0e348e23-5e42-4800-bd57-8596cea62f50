package com.lenskart.returnapi.controller;

import com.lenskart.returnservice.cache.RefundRuleCacheLoader;
import com.lenskart.returnservice.config.EventRuleConfiguration;
import com.lenskart.returnservice.service.ICancelAndConvertRuleService;
import com.lenskart.returnservice.service.ICommunicationTemplateService;
import com.lenskart.returnservice.service.IKafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;
import java.util.TimeZone;

@RestController
@RequestMapping("")
@Slf4j
public class HomeController {

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private RefundRuleCacheLoader refundRuleCacheLoader;

    @Autowired
    private ICommunicationTemplateService communicationTemplateService;

    @Autowired
    ICancelAndConvertRuleService cancelAndConvertRuleService;

    @Autowired
    RedisTemplate redisTemplate;

    @GetMapping("")
    public String serviceUp() {
        Date date = new Date();
        return "Hello from Headless Return API!, " + "JVM TimeZone: " + TimeZone.getDefault().getID() + " Date : " + date;
    }

    @RequestMapping(value = "/push-data/{queue}", method = RequestMethod.POST)
    public String pushData(@RequestBody Map<String, Object> data, @PathVariable("queue") String queue) {
        kafkaService.pushToKafka(queue, null, data);
        return "success";
    }

    @RequestMapping(value = "/push-data-v2/{queue}", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    public String pushDataV2(@RequestBody Object data, @PathVariable("queue") String queue) {
        kafkaService.pushToKafka(queue, null, data);
        return "success";
    }

    @GetMapping("/load-refund-rules-cache")
    public String loadRefundRulesCache() {
        log.info("[load-refund-rules-cache] loading refund rules cache via api");
        refundRuleCacheLoader.loadCache();
        log.info("[load-refund-rules-cache] success");
        return "success";
    }

    @GetMapping("/reload-communication-template-cache")
    public String reloadCommunicationTemplateCache() {
        log.info("[reloadCommunicationTemplateCache] reloadCommunicationTemplateCache api");
        communicationTemplateService.reloadCommunicationTemplateCache();
        log.info("[reloadCommunicationTemplateCache] success");
        return "success";
    }

    @GetMapping("/reload/event-rule")
    public String reloadEventRule() {
        try {
            EventRuleConfiguration ruleConfiguration = EventRuleConfiguration.getInstance();
            ruleConfiguration.setEventRules(cancelAndConvertRuleService.fetchAndPopulateRules());
        }catch (Exception e){
            return e.getMessage();
        }
        return "reload done";
    }

    @RequestMapping(value = "/reload-system-preference-cache/{key}", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    public String reloadSystemPreference(@PathVariable("key") String key) {
        log.info("[reloadSystemPreferenceCache] reloadSystemPreferenceCache api {}", key);
        try{
            redisTemplate.delete(key);
            log.info("success");
        }catch (Exception e){
            log.error("exception " + e);
        }
        return "success";
    }

}
