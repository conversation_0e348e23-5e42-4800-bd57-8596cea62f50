package com.lenskart.returnapi.controller;

import com.lenskart.returncommon.annotations.HandleException;
import com.lenskart.returncommon.model.dto.ReturnDetailsDTO;
import com.lenskart.returncommon.model.request.GetReturnDetailsRequest;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.jsondoc.core.annotation.ApiMethod;
import org.jsondoc.core.annotation.ApiResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("return/details/v2.0/")
public class ReturnDetailsV2Controller {

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @HandleException
    @ApiMethod(path = "/get", description = "Get return details based on identifier type and value", produces = {
            MediaType.APPLICATION_JSON_VALUE }, responsestatuscode = "200")
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<ReturnDetailsDTO> getReturnDetailsV2(@Valid @RequestBody GetReturnDetailsRequest request, BindingResult bindingResult) {

        log.info("[ReturnDetailsController][getReturnDetailsV2] request : {}", request);
        if(bindingResult.hasErrors()){
            throw new RuntimeException("Invalid Request");
        }
        ReturnDetailsDTO returnDetailsDTO = returnOrderActionService.getReturnDetailsByIdentifier(request.getIdentifierType(), request.getIdentifierValues(), false);

        return ResponseEntity.ok().body(returnDetailsDTO);
    }
}
