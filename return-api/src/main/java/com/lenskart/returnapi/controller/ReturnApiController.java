package com.lenskart.returnapi.controller;

import com.lenskart.returncommon.annotations.HandleException;
import com.lenskart.returncommon.model.dto.DualRefundRequest;
import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.request.*;
import com.lenskart.returncommon.model.response.*;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.jsondoc.core.annotation.ApiBodyObject;
import org.jsondoc.core.annotation.ApiMethod;
import org.jsondoc.core.annotation.ApiResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.util.Map;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("return/v1.0/")
public class ReturnApiController {

    @Autowired
    private IReversePickUpService reversePickUpService;
    @Autowired
    IReturnUpdateService returnUpdateService;

    @Autowired
    private Map<String, IReturnInitiationService> returnInitiationServiceMap;

    @Autowired
    private IAwaitedRtoService awaitedRtoService;
    @Autowired
    private IReceivingService receivingService;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnsReplicaHandlerService returnsReplicaHandlerService;

    @Autowired
    private IInventoryReturnUpdateService inventoryReturnUpdateService;

    @Autowired
    private InsuranceService insuranceService;

    @Autowired
    private IJunoService junoService;

    @ApiMethod(path = "reverse-pickup-info/update", description = "called when reference_id_issued and awb_assigned imported from VSM")
    @RequestMapping(value = "reverse-pickup-info/update", method = RequestMethod.POST)
    @HandleException
    public @ApiResponseObject ReverseCourierUpdateResponse updateReversePickUpInfo(@ApiBodyObject @RequestBody ReverseCourierUpdateRequestDTO reverseCourierUpdateRequest) {
        log.info("[ReturnController][updateReversePickUpInfo] reverseCourierUpdateRequest : " + reverseCourierUpdateRequest);
        return reversePickUpService.updateReversePickUpInfo(reverseCourierUpdateRequest);
    }

    @RequestMapping(method = RequestMethod.PUT)
    public @ApiResponseObject
    ReturnDetailsUpdateResponse updateReturn(@RequestBody ReturnDetailUpdateRequestDto returnUpdateRequest) {
        log.info("[returnApiController] returnUpdateRequest is : " + returnUpdateRequest);
        return returnUpdateService.updateReturnStatus(returnUpdateRequest);
    }

    @ApiMethod(description = "Unified API for return creation through all channels-- POS/MyAccount/VSM")
    @RequestMapping(method = RequestMethod.POST)
    public @ApiResponseObject
    ReturnCreationResponse createReturn(@ApiBodyObject @RequestBody ReturnCreationRequestDTO returnCreationRequest) throws Exception {
        log.info("[createReturn] return request : {}, startTime : {}",returnCreationRequest, new Date(System.currentTimeMillis()));
        ReturnCreationResponse returnCreationResponse = null;
        try{
            String source = returnCreationRequest.getReturnSource() != null ? returnCreationRequest.getReturnSource().getSource().toUpperCase() : "VSM";
            log.info("[createReturn] order: {} , source : {}", returnCreationRequest.getIncrementId(), source);
            IReturnInitiationService iReturnInitiationService = returnInitiationServiceMap.get(source+"_ReturnInitiationServiceImpl");
            if(iReturnInitiationService == null){
                if(StringUtils.isNotEmpty(returnCreationRequest.getRequestApprover()) && "VSM".equalsIgnoreCase(source)){
                    iReturnInitiationService = returnInitiationServiceMap.get(source+"_ReturnApprovalServiceImpl");
                } else {
                    iReturnInitiationService = returnInitiationServiceMap.get("ReturnInitiationServiceImpl");
                }
            }
                returnCreationResponse = iReturnInitiationService.createReturn(returnCreationRequest);
        }catch (Exception exception){
            log.error("[createReturn] error occurred : "+ exception);
        }
        log.info("returnCreationResponse : {}, endTime : {}", returnCreationResponse, new Date(System.currentTimeMillis()));
        return returnCreationResponse;
    }

    @RequestMapping(value = "awaited-rto/return-refund", method = RequestMethod.POST)
    public Map<String, Object> returnAtAwaitedRto(@RequestBody ReturnOrderRequestDTO returnOrderRequest) {
        try {
            log.info("ReturnApiController.refundAtAwaitedRto" + returnOrderRequest.toString());
            return awaitedRtoService.markAwaitedRto(returnOrderRequest);
        } catch (Exception e) {
            log.error("ReturnApiController.refundAtAwaitedRto", e);
            throw e;
        }
    }

    @ApiMethod
    @RequestMapping(value = "create-return/direct-received", method = RequestMethod.POST)
    public @ApiResponseObject
    Set<Integer> receiveItems(@ApiBodyObject @RequestBody ReturnOrderRequestDTO returnOrderRequest) throws Exception {
        try {
            log.info("[ReverseController][receiveItems]" + returnOrderRequest.toString());
            return receivingService.receiveItems(returnOrderRequest);
        } catch (Exception e) {
            log.error("[ReverseController][receiveItems]", e);
            throw e;
        }
    }

    @RequestMapping(value = "rto-items", method = RequestMethod.POST)
    public @ApiResponseObject Map<String , Object> rtoItems(@ApiBodyObject @RequestBody ReturnOrderRequestDTO returnRequest) throws Exception {
        log.info("[ReturnController][rtoItems]" + returnRequest);
        return returnUpdateService.markRtoItems(returnRequest);
    }
    @RequestMapping(value = "return-items", method = RequestMethod.POST)
    Map<String, Object> returnItems(@ApiBodyObject @RequestBody ReturnOrderRequestDTO returnOrderRequest) throws Exception {
        log.info("ReturnApiController.returnItems :" + returnOrderRequest);
        try {
            return receivingService.returnItems(returnOrderRequest);
        } catch (Exception e) {
            log.error("ReturnApiController.returnItems exception : ", e);
            throw e;
        }
    }

    @RequestMapping(value = "update/order-status-refund", method = RequestMethod.POST)
    public @ApiResponseObject
    ResponseEntity<Object> updateOrderStatusForRefund(@RequestBody DualRefundRequest refundRequest) {
        try{
            if(refundRequest==null)
                return new ResponseEntity<>(Boolean.FALSE, HttpStatus.BAD_REQUEST);

            log.info("Calling updateOrderStatusForRefund for request {}", refundRequest);
            returnOrderActionService.updateOrderStatusForRefund(refundRequest);
            return new ResponseEntity<>(Boolean.TRUE, HttpStatus.OK);
        } catch (Exception e){
            log.error("Exception caught in updateOrderStatusForRefund {} for {}", e.getMessage(), refundRequest, e);
            return new ResponseEntity<>(Boolean.FALSE, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @RequestMapping(value = "/update-return-reason", method = RequestMethod.POST)
    public @ApiResponseObject
    ResponseEntity<Object> updateReturnReason(@RequestBody UpdateReturnReasonRequest updateReturnReasonRequest) {
        try{
            log.info("Calling updateReturnReason for request {}", updateReturnReasonRequest);
            returnOrderActionService.updateReturnReason(updateReturnReasonRequest);
            return new ResponseEntity<>(Boolean.TRUE, HttpStatus.OK);
        } catch (Exception e){
            log.error("Exception caught in updateReturnReason {} for {}", e.getMessage(), updateReturnReasonRequest, e);
            return new ResponseEntity<>(Boolean.FALSE, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @RequestMapping(value = "/update-agent-emails", method = RequestMethod.POST)
    public @ApiResponseObject
    ResponseEntity<Map<String, Object>> updateAgentEmailsInReturn(@RequestBody UpdateAgentEmailReturnRequest updateAgentEmailReturnRequest) {
        log.info("Calling updateAgentEmailsInReturn for request {}", updateAgentEmailReturnRequest);
        return ResponseEntity.ok(returnOrderActionService.updateAgentEmailsInReturn(updateAgentEmailReturnRequest));
    }

    @RequestMapping(value ="/update/agent",method = RequestMethod.POST)
    @CrossOrigin(origins = {"https://vsm.ui.local.lenskart.com:4000", "https://vsm.scm.preprod.lenskart.com", "https://vsm.scm.lenskart.com", "https://vsm-ui-preprod.lenskart.com", "https://vsm-ui.ces.lenskart.com" })
    public ApiResponse updateReturnAgent(@RequestBody UpdateReturnAgentRequest request) {
        log.info("[updateReturnAgent] updateReturnAgent api request :{}", request);
        boolean success = returnOrderActionService.updateReturnAgent(request);
        ApiResponse apiResponse = new ApiResponse();
        if (success) {
            apiResponse.setSuccess(true);
            apiResponse.setMessage("Agent assigned successfully.");
        } else {
            apiResponse.setSuccess(false);
            apiResponse.setMessage("Agent not assigned.");
        }
        apiResponse.setStatus(200);
        return apiResponse;
    }

    @RequestMapping(value = "/update-return-item", method = RequestMethod.POST)
    public @ApiResponseObject
    ResponseEntity<Integer> updateReturnItem(@RequestBody UpdateReturnOrderItemRequest updateReturnOrderItemRequest) {
        log.info("Calling updateReturnItem for request {}", updateReturnOrderItemRequest);
        return ResponseEntity.ok(returnOrderActionService.updateReturnOrderItem(updateReturnOrderItemRequest));
    }

    @RequestMapping(value = "/rollback-return", method = RequestMethod.POST)
    public @ApiResponseObject
    ResponseEntity<Boolean> rollbackReturn() {
        log.info("rolling back the returns from returndb to inventory start");
        return ResponseEntity.ok(inventoryReturnUpdateService.updateReturns());
    }

//    @RequestMapping(value = "/update-return-order-item-meta/{lastId}", method = RequestMethod.GET)
//    public @ApiResponseObject
//    ResponseEntity<Boolean> processAndSaveReturnOrderItems(@PathVariable("lastId") Integer lastId) {
//        if (lastId == null || lastId < 0) {
//            return new ResponseEntity<>(Boolean.FALSE, HttpStatus.BAD_REQUEST);
//        }
//        log.info("processAndSaveReturnOrderItems start with lastId :{}", lastId);
//        return ResponseEntity.ok(returnsReplicaHandlerService.processAndSaveReturnOrderItems(lastId));
//    }

    @RequestMapping(value = "/update-insurance", method = RequestMethod.POST)
    public @ApiResponseObject
    ResponseEntity<Boolean> updateInsurance() {
        log.info("manually updating insurance claims");
        insuranceService.manualInsuranceUpdate();
        return ResponseEntity.ok(true);
    }

    @PostMapping("/frame-broken/approval/request")
    @HandleException
    public ResponseEntity<FrameBrokenApprovalResponse> frameBrokenApproval(@RequestBody FrameBrokenApprovalRequest frameBrokenApprovalRequest) {
        log.info("[frameBrokenApproval] Calling frameBrokenApproval for request {}", frameBrokenApprovalRequest);
        return ResponseEntity.ok(junoService.processFrameBrokenApprovalRequest(frameBrokenApprovalRequest));
    }


    @PostMapping(value = "/update-model-prompt", consumes = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> uploadText(@RequestParam("id") Integer id, @RequestBody String text) {

        log.info("Received ID: {}", id);
        log.info("Received text length: {}", text.length());

        return ResponseEntity.ok(returnOrderActionService.updateModelPrompt(id,text));
    }
}
