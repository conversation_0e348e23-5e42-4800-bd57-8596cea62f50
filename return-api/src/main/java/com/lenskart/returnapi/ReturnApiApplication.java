package com.lenskart.returnapi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableFeignClients(basePackages = "com.lenskart")
@ComponentScan(basePackages = "com.lenskart")
@EnableAsync
public class ReturnApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(ReturnApiApplication.class, args);
	}

}
