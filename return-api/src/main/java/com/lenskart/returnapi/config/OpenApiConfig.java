package com.lenskart.returnapi.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.servers.Server;
import jakarta.servlet.http.HttpServletRequest;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenApiCustomizer openApiCustomizer(HttpServletRequest request) {
        return openApi -> {
            String baseUrl = request.getRequestURL().toString()
                    .replace(request.getRequestURI(), request.getContextPath())
                    .replace("http://", "https://");  // just replace http with https

            Server server = new Server().url(baseUrl);
            openApi.setServers(List.of(server));
        };
    }
}

