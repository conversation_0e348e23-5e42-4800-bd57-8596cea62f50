<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>

	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
				<jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter"/>
				<timestampFormat>yyyy-MM-dd HH:mm:ss.SSS</timestampFormat>
				<appendLineSeparator>true</appendLineSeparator>
				<pattern>%d{ISO8601} %X{%traceId} [%thread] %-5level  %logger{36} - %msg%n</pattern>
			</layout>
		</encoder>
	</appender>

	<springProfile name="local">
		<root level="DEBUG">
			<appender-ref ref="CONSOLE"/>
		</root>
	</springProfile>

	<springProfile name="!local">
		<root level="INFO">
			<appender-ref ref="CONSOLE"/>
		</root>
	</springProfile>
</configuration>