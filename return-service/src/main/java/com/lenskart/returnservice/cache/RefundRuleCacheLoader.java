package com.lenskart.returnservice.cache;

import com.lenskart.returnrepository.entity.RefundRules;
import com.lenskart.returnrepository.repository.RefundRulesRepository;
import com.lenskart.returnservice.elasticsearch.service.RefundRulesCache;
import com.lenskart.returnservice.service.impl.RefundRuleWrapperImpl;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class RefundRuleCacheLoader {
    @Autowired
    RefundRulesRepository refundRulesRepository;
    @Autowired
    RefundRulesCache refundRulesCache;
    @Autowired
    RefundRuleWrapperImpl refundRuleWrapper;

    @PostConstruct
    public void loadCache(){
        List<RefundRules> refundRulesList = refundRulesRepository.findAll();
        if(null != refundRulesList){
            log.info("[loadCache] start time : " + new Date(System.currentTimeMillis()));
            refundRulesCache.registerCacheObserver(refundRuleWrapper);
            refundRulesCache.indexRefundRules(refundRulesList);
            log.info("[loadCache] end time : " + new Date(System.currentTimeMillis()));
        }

    }
}