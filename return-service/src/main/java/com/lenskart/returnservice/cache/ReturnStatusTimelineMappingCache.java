package com.lenskart.returnservice.cache;

import com.lenskart.returnrepository.entity.ReturnStatusTimelineMapping;
import com.lenskart.returnrepository.repository.ReturnStatusTimelineMappingRepository;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ReturnStatusTimelineMappingCache {
    @Autowired
    private ReturnStatusTimelineMappingRepository returnStatusTimelineMappingRepository;
    private Map<String, ReturnStatusTimelineMapping> returnStatusTimelineMappingMap = new ConcurrentHashMap<>();
    @PostConstruct
    public void init(){
        List<ReturnStatusTimelineMapping> returnStatusTimelineMappingList = returnStatusTimelineMappingRepository.findAll();
        if(!CollectionUtils.isEmpty(returnStatusTimelineMappingList)){
            for(ReturnStatusTimelineMapping returnStatusTimelineMapping: returnStatusTimelineMappingList){
                returnStatusTimelineMappingMap.put(returnStatusTimelineMapping.getCustomerFacingStatus(), returnStatusTimelineMapping);
            }
        }
    }

    public ReturnStatusTimelineMapping getReturnStatusTimlineByReturnStatus(String customerFacingStatus){
        return returnStatusTimelineMappingMap.get(customerFacingStatus);
    }
}
