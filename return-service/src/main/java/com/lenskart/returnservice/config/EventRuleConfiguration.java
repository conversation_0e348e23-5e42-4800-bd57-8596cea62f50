package com.lenskart.returnservice.config;

import com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.enums.RuleConditionType;
import com.lenskart.returnrepository.entity.CancelAndConvertRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class EventRuleConfiguration {

    private static EventRuleConfiguration eventRuleConfiguration;
    private static Map<PaymentMode, List<CancelAndConvertRuleDto>> eventTypeListMap;

    private EventRuleConfiguration() {}

    public synchronized static EventRuleConfiguration getInstance() {
        if (eventRuleConfiguration == null) {
            eventRuleConfiguration = new EventRuleConfiguration();
        }

        return eventRuleConfiguration;
    }

    public Map<PaymentMode, List<CancelAndConvertRuleDto>> getEventRules() {
        return eventTypeListMap;
    }

    public void setEventRules(Map<PaymentMode, List<CancelAndConvertRuleDto>> eventTypeListMap) {
        this.eventTypeListMap = eventTypeListMap;
    }

}
