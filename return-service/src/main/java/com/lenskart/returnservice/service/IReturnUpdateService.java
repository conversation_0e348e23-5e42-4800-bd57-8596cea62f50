package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;

import java.util.Map;

public interface IReturnUpdateService {
    ReturnDetailsUpdateResponse updateReturnStatus(ReturnDetailUpdateRequestDto request);
    boolean setAutoCancelFlagAsFalse(Integer returnId);

    Map<String, Object> markRtoItems(ReturnOrderRequestDTO rtoRequestDTO) throws Exception;

}
