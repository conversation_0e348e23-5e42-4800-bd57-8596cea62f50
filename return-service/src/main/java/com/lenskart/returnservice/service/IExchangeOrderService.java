package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.dto.ExchangeOrderCreationRequest;
import com.lenskart.returncommon.model.dto.ExchangeUnholdHelperDTO;

public interface IExchangeOrderService {
    Boolean createExchangeOrder(ExchangeOrderCreationRequest exchangeOrderCreationRequest);
    boolean unholdExchangeOrder(Integer incrementId);
    ExchangeOrdersDTO getExchangeOrder(Integer uwItemId, Integer returnId);
}
