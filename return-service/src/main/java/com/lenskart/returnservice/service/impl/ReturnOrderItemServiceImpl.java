package com.lenskart.returnservice.service.impl;

import com.lenskart.orderops.model.ReturnOrderItem;
import com.lenskart.returncommon.model.dto.ReturnDetailsDTO;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnOrderItemDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.Reasons;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnDetailReason;
import com.lenskart.returnrepository.entity.ReturnOrder;
import com.lenskart.returnrepository.repository.SystemPreferenceRepository;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReturnOrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.lenskart.returncommon.model.enums.ReturnStatus.RETURN_REJECTED_HANDOVER_DONE;
import static com.lenskart.returncommon.model.enums.ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING;

@Service
@Slf4j
public class ReturnOrderItemServiceImpl implements IReturnOrderItemService {

    @Autowired
    private IReturnOrderActionService returnOrderActionService;
    @Autowired
    SystemPreferenceRepository systemPreferenceRepository;
    @Autowired
    private IReturnEventService returnEventService;
    @Autowired
    private ReturnUtil returnUtil;

    @Override
    public ReturnDetailItem createReturnItem(ReturnItemDTO item, UwOrderDTO uwOrder, Integer returnId, String returnType, List<ReturnDetailReason> returnReasonList, String returnSource, Integer itemId) {
        ReturnDetailItem returnOrderItem = null;
        log.info("[ReturnOrderItemCrudAction:createReturnItem] saving details in return_order_item for returnId {}", returnId);
        if (item != null && item.getUwItemId() != 0) {
            if (returnId != null && returnId > 0) {
                returnOrderItem = returnOrderActionService.findTopByNewReturnId(returnId);
            } else {
                returnOrderItem = returnOrderActionService.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(item.getUwItemId());
            }
            List<String> returnCancelledStatusList = List.of(Constant.RETURN_ORDER_STATUS.CANCELLED.toLowerCase(), Constant.RETURN_ORDER_STATUS.CUSTOMER_CANCELLED.toLowerCase(), RETURN_REJECTED_HANDOVER_DONE.getStatus(), RETURN_REJECTED_HANDOVER_PENDING.getStatus());
            if (returnOrderItem == null || returnCancelledStatusList.contains(returnOrderActionService.getReturnOrderStatusById(returnOrderItem.getReturnId()))){
                returnOrderItem = new ReturnDetailItem();
                returnOrderItem.setReturnCreateDatetime(new Date());
                returnOrderItem.setReturnId(returnId);
            }
            returnOrderItem.setItemId(itemId);
            returnOrderItem.setUwItemId(uwOrder.getUwItemId());
            returnOrderItem.setProductId((long) uwOrder.getProductId());
            returnOrderItem.setQcStatus(item.getQcStatus());
            returnOrderItem.setQcFailReason("");
            returnOrderItem.setReasonForReturn(returnReasonList != null && !CollectionUtils.isEmpty(returnReasonList) ? returnReasonList.get(0).getSecondaryReason() : null);
            returnOrderItem.setProductDeliveryType(uwOrder.getProductDeliveryType());
            returnOrderItem.setClassification(uwOrder.getClassification() != null ? Integer.parseInt(uwOrder.getClassification()) : 0);
            returnOrderItem.setIsFranchise(uwOrder.getIsFranchise());
            returnOrderItem.setCsohUpdatedFlag(0);
            returnOrderItem.setItemSelectedFlag(0);
            returnOrderItem.setMethod(uwOrder.getMethod());
            returnOrderItem.setChannel(uwOrder.getChannel());
            String status = ReturnOrderItem.STATUS.RETURN_RECEIVED;
            if (ReturnSources.WEB.getSource().equalsIgnoreCase(returnSource) || ReturnSources.VSM.getSource().equalsIgnoreCase(returnSource))
                status = ReturnOrderItem.STATUS.INITIATED_STOCKIN;
            returnOrderItem.setStatus(status);
            if(item.getReasons() != null) {
                for (Reasons reason : item.getReasons()) {
                    if ("APPROVAL".equalsIgnoreCase(reason.getType())) {
                        if (StringUtils.isNotBlank(reason.getAdditionalComments())) {
                            returnOrderItem.setQcComment(returnUtil.trimString(reason.getAdditionalComments()));
                        }
                    }
                }
            }
            log.info("[createReturnItem]saving return order item details for returnId : {}, uwItemId : {}" , returnOrderItem.getReturnId(), item.getUwItemId() );
            returnOrderActionService.saveReturnOrderItem(returnOrderItem);
        }
        return returnOrderItem;
    }

    @Override
    public ReturnDetailItem findByUwItemId(Integer uwItemId) {
        return returnOrderActionService.findTopReturnOrderItemByUwItemId(uwItemId);
    }

    @Override
    public ReturnDetailItem createReturnItemForAwaitedRto(String qcStatus, UwOrderDTO uwOrder, Integer returnId, String reasonDetail) {
        ReturnDetailItem returnOrderItem=null;
        try {
            returnOrderItem = new ReturnDetailItem();
            returnOrderItem.setReturnId(returnId);
            returnOrderItem.setItemId(uwOrder.getItemId());
            returnOrderItem.setUwItemId(uwOrder.getUwItemId());
            returnOrderItem.setProductId((long) uwOrder.getProductId());
            returnOrderItem.setQcStatus(qcStatus);
            returnOrderItem.setQcComment("");
            returnOrderItem.setQcFailReason("");
            returnOrderItem.setReasonForReturn(reasonDetail);
            returnOrderItem.setReturnCreateDatetime(new Date());
            returnOrderItem.setProductDeliveryType(uwOrder.getProductDeliveryType());
            returnOrderItem.setClassification(Integer.valueOf(uwOrder.getClassification()));
            returnOrderItem.setIsFranchise(uwOrder.getIsFranchise());
            returnOrderItem.setMethod(uwOrder.getMethod());
            returnOrderItem.setChannel(uwOrder.getChannel());
            returnOrderItem = returnOrderActionService.saveReturnOrderItem(returnOrderItem);
        } catch (Exception e) {
            log.error("[createReturnItem] : "+ e);
        }
        return returnOrderItem;
    }
    @Override
    public Integer getReturnId(Integer uwItemId) {
        ReturnDetailItem topReturnOrderItemByUwItemId = returnOrderActionService.findTopReturnOrderItemByUwItemId(uwItemId);
        return topReturnOrderItemByUwItemId != null ? topReturnOrderItemByUwItemId.getReturnId() : null ;
    }

    @Override
    public void updateReturnOrderItem(Integer returnId, String status) {
        List<ReturnDetailItem> returnOrderItems = returnOrderActionService.findAllByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnOrderItems)){
            for(ReturnDetailItem returnOrderItem : returnOrderItems){
                returnOrderItem.setStatus(status);
                returnOrderActionService.saveReturnOrderItem(returnOrderItem);
            }
        }
    }

    public ReturnOrderItem getReturnOrderItem(ReturnDetailsDTO returnDetailsDTO){
        log.info("[getReturnOrderItem] enter returnDetailsDTO : {}", returnDetailsDTO);
        ReturnOrderItem returnOrderItem = null;
        if(returnDetailsDTO != null && !CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrders())){
            ReturnOrderItemDTO returnOrderItemDTO = returnDetailsDTO.getReturnOrderItems().get(0);
            log.info("[getReturnOrderItem] returnOrderItemDTO : {}",returnOrderItemDTO);
            if(Objects.nonNull(returnOrderItemDTO)){
                returnOrderItem = new ReturnOrderItem();
                returnOrderItem.setReturnId(returnOrderItemDTO.getReturnId());
                returnOrderItem.setProductId(Math.toIntExact(returnOrderItemDTO.getProductId()));
                returnOrderItem.setItemId(returnOrderItemDTO.getItemId());
                returnOrderItem.setReturnCreateDatetime(returnOrderItemDTO.getReturnCreateDatetime());
                returnOrderItem.setQcStatus(returnOrderItemDTO.getQcStatus());
                returnOrderItem.setUwItemId(returnOrderItemDTO.getUwItemId());
                returnOrderItem.setQcComment(returnOrderItemDTO.getQcComment());
            }
        }
        log.info("[getReturnOrderItem] returnOrderItem : {}", returnOrderItem);
        return returnOrderItem;
    }

    public com.lenskart.returnrepository.entity.ReturnOrder getReturnOrder(ReturnDetailsDTO returnDetailsDTO){
        log.info("[getReturnOrder] enter returnDetailsDTO : {}", returnDetailsDTO);
        com.lenskart.returnrepository.entity.ReturnOrder returnOrder = null;
        if(returnDetailsDTO != null && !CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrders())){
            ReturnOrderDTO returnOrderDTO = returnDetailsDTO.getReturnOrders().get(0);
            log.info("[getReturnOrder] returnOrderDTO : {}",returnOrderDTO);
            if(returnOrderDTO != null){
                returnOrder = new ReturnOrder();
                returnOrder.setOrderNo(returnOrderDTO.getIncrementId());
                returnOrder.setReturnId(returnOrderDTO.getId());
                returnOrder.setGroupId(Long.valueOf(returnOrderDTO.getGroupId()));
                returnOrder.setFacilityCode(returnOrderDTO.getFacilityCode());
                returnOrder.setReturnType(returnOrderDTO.getReturnType());
                returnOrder.setReturnMethod(returnOrderDTO.getReturnMethod());
                returnOrder.setIsInsurance(returnOrderDTO.getIsInsurance());
                returnOrder.setUnicomOrderCode(returnOrderDTO.getUnicomOrderCode());
                returnOrder.setReturnCreateDatetime(returnOrderDTO.getReturnCreateDatetime());
            }
        }
        log.info("[getReturnOrder] returnOrder : {}", returnOrder);
        return returnOrder;
    }
}
