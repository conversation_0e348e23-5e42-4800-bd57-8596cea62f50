package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto;
import com.lenskart.returncommon.model.dto.RuleContextDTO;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.response.CancelAndConvertEventRuleResponse;
import com.lenskart.returnservice.service.IEventDecisionAlgorithm;
import com.lenskart.returnservice.service.IEventDecisionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class EventDecisionServiceImpl implements IEventDecisionService {

    private final IEventDecisionAlgorithm decisionAlgorithm;

    @Override
    public CancelAndConvertEventRuleResponse getActionsResult(PaymentMode paymentMode, RuleContextDTO context, Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules) {
        return decisionAlgorithm.getActionsResult(paymentMode, context, eventRules);
    }
}
