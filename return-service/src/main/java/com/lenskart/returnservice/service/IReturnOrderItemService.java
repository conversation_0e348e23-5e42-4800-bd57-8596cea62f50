package com.lenskart.returnservice.service;

import com.lenskart.orderops.model.ReturnOrderItem;
import com.lenskart.returncommon.model.dto.ReturnDetailsDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnDetailReason;
import com.lenskart.returnrepository.entity.ReturnOrder;

import java.util.List;

public interface IReturnOrderItemService {
    ReturnDetailItem createReturnItem(ReturnItemDTO item, UwOrderDTO uwOrder, Integer returnId, String returnType, List<ReturnDetailReason> returnReasonList, String returnSource, Integer itemId);
    ReturnDetailItem findByUwItemId(Integer uwItemId);

    ReturnDetailItem createReturnItemForAwaitedRto(String qcStatus, UwOrderDTO uwOrder, Integer returnId, String returnReason);
    Integer getReturnId(Integer uwItemId);

    void updateReturnOrderItem(Integer returnId, String status);
      ReturnOrder getReturnOrder(ReturnDetailsDTO returnDetailsDTO);
      ReturnOrderItem getReturnOrderItem(ReturnDetailsDTO returnDetailsDTO);
}
