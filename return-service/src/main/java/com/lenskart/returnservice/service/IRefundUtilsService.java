package com.lenskart.returnservice.service;

import com.lenskart.core.model.UwOrder;
import com.lenskart.ordermetadata.dto.request.RefundDispatchRequest;
import com.lenskart.ordermetadata.dto.response.RefundDispatchResponse;
import com.lenskart.refund.client.model.enums.RefundReason;
import com.lenskart.refund.client.model.enums.RefundTriggerPoint;
import com.lenskart.refund.client.model.kafka.CheckRefundDispatchPointKafkaMessage;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.request.CreateRefundRequest;
import com.lenskart.refund.client.model.request.GetRefundAmountRequest;
import com.lenskart.refund.client.model.request.RefundRejectRequest;
import com.lenskart.refund.client.model.response.*;
import com.lenskart.returncommon.model.dto.RefundIntentDTO;
import com.lenskart.returncommon.model.dto.RefundProcessDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundInputDTO;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;

public interface IRefundUtilsService {
    GetRefundAmountResponse getOrderRefundAmount(Integer orderId);

    String getRefundRequestStatus(Integer uwItemId);

    RefundIntentDTO getRefundMethodIntentByCustomer(Integer returnId);

    Boolean isPurelyCODItem(ReturnRefundInputDTO inputDTO);

    CheckRefundInitiatedResponse getRefundAmount(CheckRefundInitiatedRequest checkRefundInitiatedRequest);

    Boolean initiateExchangeOrderRefund(Integer exchangeIncrementId, Integer masterOrderId);

    CheckRefundInitiatedResponse isRefundInitiated(CheckRefundInitiatedRequest checkRefundInitiatedRequest);

    KafkaProducerResponse pushToCheckDispatchPointQueue(CheckRefundDispatchPointKafkaMessage checkRefundDispatchPointKafkaMessage);

    GetMethodWiseRefundDetailsResponse getMethodWiseRefundDetails(GetRefundAmountRequest request);

    void getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse getMethodWiseRefundDetailsResponse, ReturnDetailsResponse response);

    boolean isAlreadyRefunded(int incrementId, Integer uwItemId);

    KafkaProducerResponse rejectRefundRequest(RefundRejectRequest request);

    boolean refundServiceSwitch(String identifierType, String identifierValue);

    void initiateRefundAsynchronously(Integer magentoItemId, Integer uwItemId, Integer returnId, String triggerPoint);

    boolean initiateRefundRequest(CreateRefundRequest payload);

    RefundMethodResponse getRefundMethod(String refundInitiatedAt, String paymentMode, String paymentMethod, String country, Integer incrementId);

    boolean getIsFranchiseRefundRequired(Integer incrementId, RefundTriggerPoint refundTriggerPoint, String source, RefundReason refundReason);

    void initiateRefund(RefundProcessDTO refundProcessDTO);

    boolean isFranchiseRefundEligible(String navChannel, RefundTriggerPoint refundTriggerPoint) throws Exception;

    RefundDispatchResponse isRefundDispatchableAtPOS(RefundDispatchRequest refundDispatchRequest);
    RefundDispatchResponse getRefundDispatchPoint(RefundDispatchRequest request);

    RefundDispatchResponse isRefundDispatchable(RefundDispatchRequest request);
    Boolean isDispatchable(Integer returnId, String ruleEngineStatusForDispatch);

}
