package com.lenskart.returnservice.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;

@Service
@Slf4j
public class CommunicationUtil {

    @Value("${short.url.service.base.url}")
    String shortUrlServiceBaseUrl;
    public String getShortUrlForJpg(String longUrl){

        String shortUrl = null;
        log.info("[getShortUrl] longUrl : "+longUrl);
        try{
            String encodedLongUrl = URLEncoder.encode(longUrl, "UTF-8");
            String newLongUrl = encodedLongUrl.replaceAll("%","~").replace(".jpg", "~2Ejpg");
            String requestUrl = shortUrlServiceBaseUrl+newLongUrl;
            log.info("[getShortUrl] requestUrl : "+requestUrl);
            ResponseEntity<String> responseEntity = new RestTemplate().getForEntity(requestUrl,String.class);
            if(null != responseEntity){
                if(responseEntity.getBody()!= null){
                    JSONObject response = new JSONObject(responseEntity.getBody());
                    shortUrl = response.get("short_url").toString();
                    log.info("[getShortUrl] shortUrl : "+shortUrl);
                    shortUrl = shortUrl.replace("http://", "");
                    if(shortUrl.contains("https://")){
                        shortUrl = shortUrl.replace("https://", "");
                    }
                }
            }
            log.info("[getShortUrl] short url : "+shortUrl);
        }catch(Exception e){
            log.error("[getShortUrl] Exception occurred : ",e);
        }
        return shortUrl;
    }
}
