package com.lenskart.returnservice.service;

import com.lenskart.returnrepository.entity.SystemPreference;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public interface ISystemPreferenceService {
    List<SystemPreference> findAll();

    List<SystemPreference> findAllByGroup(String group);

    SystemPreference findOneByGroupAndKey(String group, String key);

    List<SystemPreference> save(List<SystemPreference> systemPreferences);

    List<String> getValuesAsList(String group, String key) throws Exception;

    Object getValuesByType(SystemPreference systemPreference);

    List<SystemPreference> findAllByGroupIn(List<String> groups);

    SystemPreference findAllByKey(String key);

    String getSystemPreferenceValues(String key, String group);

    String getSystemPreferenceValues(String group, String key, int ttl, TimeUnit timeUnit);

    List<SystemPreference> findAllByGroup(String group, int ttl, TimeUnit timeUnit);

    Date getPaymentCaptureLSDate();

    boolean isCosmosCommunicationEnabled(String eventName);

}

