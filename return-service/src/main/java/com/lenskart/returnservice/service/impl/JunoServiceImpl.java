package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.orderops.model.ItemsStateStatusDto;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.request.FrameBrokenApprovalRequest;
import com.lenskart.returncommon.model.request.GiftVoucherRequest;
import com.lenskart.returncommon.model.response.FrameBrokenApprovalResponse;
import com.lenskart.returncommon.model.response.GiftVoucherResponse;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.platform.fl.utils.dto.giftCard.JunoGiftCardResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.FrameBrokenApproval;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.FrameBrokenApprovalRepo;
import com.lenskart.returnservice.feignclient.JunoFeignClient;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.ICommunicationService;
import com.lenskart.returnservice.service.IJunoService;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.*;
import static com.lenskart.returncommon.utils.Constant.JUNO_PRODUCT.*;

@Slf4j
@Service
public class JunoServiceImpl implements IJunoService {

    @Autowired
    private CommunicationUtil communicationUtil;
    @Autowired
    private JunoFeignClient junoFeignClient;
    @Autowired
    private FrameBrokenApprovalRepo frameBrokenApprovalRepo;
    @Autowired
    private ICommunicationService communicationService;
    @Autowired
    private IKafkaService kafkaService;
    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;
    @Autowired
    private ISystemPreferenceService systemPreferenceService;
    @Autowired
    private ReturnUtil returnUtil;

    @Value("${juno.api.gateway.base.url}")
    private String junoUrl;

    private static final String API_CLIENT_KEY = "X-Api-Client";
    public static final String FBR_APPROVED_STATUS = "APPROVED";
    public static final String FBR_REJECTED_STATUS = "REJECTED";
    private static final String FBR_GV_CODE = "FRBRK-[AN.4]-[AN.6]";
    private static final String FBR_GV_LENS_CODE = "FRBRK-LNSOLY-[AN.4]-[AN.6]";

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));


    @Override
    public Map<String, Object> getProductDetails(int productId) {
        Map<String, Object> responseMap = new HashMap<>();
        try {
            log.info("getProductDetailsFromJuno start : pid : {}", productId);
            RestTemplate template = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("user-agent", "Application");
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            headers.set(API_CLIENT_KEY, "desktop");
            HttpEntity<String> entity = new HttpEntity<>(headers);

            //https://api-gateway.juno.prod-eks.internal/v2/products/product/internal/136177
            String url = junoUrl + "/products/product/internal/" + productId;
            log.info("url={} headers={} ", url, headers);
            ResponseEntity<String> responseEntity = template.exchange(url, HttpMethod.GET, entity, String.class);
            log.info("getProductDetailsFromJuno responseEntity : {}", responseEntity);
            if (responseEntity != null && null != responseEntity.getBody()) {
                JSONObject response = new JSONObject(responseEntity.getBody());
                if (response.get(Constant.JUNO_PRODUCT.STATUS).equals(200)) {
                    JSONObject result = (JSONObject) response.get(Constant.JUNO_PRODUCT.RESULT);
                    JSONArray specifications= result.getJSONArray("specifications");
                    String frameMaterial = null;
                    String frameType = null;
                    String productBrand = null;
                    String itemCategory = null;

                    for (int i = 0; i < specifications.length(); i++) {
                        JSONObject specObj = specifications.getJSONObject(i);

                        // Find the object with name = "general"
                        if ("general".equalsIgnoreCase(specObj.getString("name"))) {
                            JSONArray items = specObj.getJSONArray("items");

                            for (int j = 0; j < items.length(); j++) {
                                JSONObject itemObj = items.getJSONObject(j);

                                // Find the object with name = "Frame Material"
                                if ("Frame Material".equalsIgnoreCase(itemObj.getString("name"))) {
                                    frameMaterial = itemObj.optString("value", null);
                                    break;
                                }
                            }
                        }
                        // Find the object with name = "technical"
                        if ("technical".equalsIgnoreCase(specObj.getString("name"))) {
                            JSONArray items = specObj.getJSONArray("items");

                            for (int j = 0; j < items.length(); j++) {
                                JSONObject itemObj = items.getJSONObject(j);

                                // Find the object with name = "Frame Material"
                                if ("Frame Type".equalsIgnoreCase(itemObj.getString("name"))) {
                                    frameType = itemObj.optString("value", null);
                                }

                                // Find the object with name = "Brand Name"
                                if ("Brand Name".equalsIgnoreCase(itemObj.getString("name"))) {
                                    productBrand = itemObj.optString("value", null);
                                }

                                // Find the object with name = "Product Type"
                                if ("Product Type".equalsIgnoreCase(itemObj.getString("name"))) {
                                    itemCategory = itemObj.optString("value", null);
                                }
                            }
                        }
                    }

                    responseMap.put(Constant.JUNO_PRODUCT.MODEL_NAME, result.get(Constant.JUNO_PRODUCT.MODEL_NAME));
                    responseMap.put(Constant.JUNO_PRODUCT.FRAME_MATERIAL,frameMaterial);
                    responseMap.put(Constant.JUNO_PRODUCT.FRAME_TYPE, frameType);
                    responseMap.put(Constant.JUNO_PRODUCT.PRODUCT_BRAND, productBrand);
                    responseMap.put(Constant.JUNO_PRODUCT.ITEM_CATEGORY, itemCategory);

                    if (result.has(Constant.JUNO_PRODUCT.IMAGE_URLS) && !result.isNull(Constant.JUNO_PRODUCT.IMAGE_URLS)) {
                        JSONArray imageUrlArrJson = result.getJSONArray(Constant.JUNO_PRODUCT.IMAGE_URLS);
                        if (imageUrlArrJson != null && imageUrlArrJson.get(0) != null) {
                            String httpsLongUrl = (String) imageUrlArrJson.get(0); //https
                            String httpLongUrl = httpsLongUrl.replace("https://", "http://");
                            String shortUrl = communicationUtil.getShortUrlForJpg(httpsLongUrl);
                            String httpShortUrl = "http://" + shortUrl;
                            String httpsShortUrl = "https://" + shortUrl;
                            responseMap.put(HTTPS_LONG_IMAGE_URL, httpsLongUrl);
                            responseMap.put(HTTP_LONG_IMAGE_URL, httpLongUrl);
                            responseMap.put(HTTPS_SHORT_IMAGE_URL, httpsShortUrl);
                            responseMap.put(HTTP_SHORT_IMAGE_URL, httpShortUrl);
                        }
                    }
                    log.info("getProductDetailsFromJuno responseMap : {}", responseMap);
                }
            }
        } catch (Exception e) {
            log.error("getProductDetailsFromJuno exception for productId {}", productId, e);
        }
        return responseMap;
    }

    @Override
    public boolean isFrameBrokenApprovedItem(Integer itemId) {
        return frameBrokenApprovalRepo.findTopByItemIdAndStatusOrderByIdDesc(itemId, FBR_APPROVED_STATUS) != null;
    }

    @Override
    public String getFrameBrokenOrderFlag(Integer incrementId) {
        if (incrementId == null) return null;

        boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
        var response = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(UW_ORDERS_DTO, ORDERS_HEADER_DTO))
                : orderOpsFeignClient.getOrderDetails(incrementId);
        var orderDetails = response != null ? response.getBody() : null;
        if (orderDetails == null) return null;

        var orders = orderDetails.getOrders();
        if (CollectionUtils.isEmpty(orders)) return null;

        var itemIds = orders.stream().map(OrdersDTO::getItemId).toList();
        var approvals = frameBrokenApprovalRepo.findByItemIdInAndStatusOrderByIdDesc(itemIds, FBR_APPROVED_STATUS);

        String magentoIds = null;
        if (CollectionUtils.isNotEmpty(approvals)) {

            var approvedItemIds = approvals.stream().map(FrameBrokenApproval::getItemId).collect(Collectors.toSet());

            magentoIds = orders.stream()
                    .filter(order -> approvedItemIds.contains(order.getItemId()))
                    .map(OrdersDTO::getMagentoItemId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }

        if (orderDetails.getOrdersHeader() != null && orderDetails.getOrdersHeader().isFrameBrokenGvFlow()) {
            return "true" + (StringUtils.isNotBlank(magentoIds) ? " -> " + magentoIds : "");
        }
        return magentoIds; // Return comma-separated string
    }


    @Override
    public FrameBrokenApprovalResponse processFrameBrokenApprovalRequest(FrameBrokenApprovalRequest req) {
        try {
            if (req == null || !(FBR_APPROVED_STATUS.equalsIgnoreCase(req.status()) || FBR_REJECTED_STATUS.equalsIgnoreCase(req.status()))) {
                return new FrameBrokenApprovalResponse(false, "FrameBrokenApprovalRequest is invalid.", null, null);
            }

            Integer itemId = null;
            Integer incrementId = null;
            Long customerId = null;
            OrderInfoResponseDTO orderDetails = null;
            String gvAmountRequested = req.gvAmountRequested();
            String mobile = req.mobile();
            boolean isLocalFittingFlow = false;

            if (StringUtils.isNotBlank(req.itemId()) && StringUtils.isNotBlank(req.orderId())) {
                Integer magentoItemId = Integer.parseInt(req.itemId());
                incrementId = Integer.parseInt(req.orderId());

                boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
                var response = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(ORDERS_DTO, UW_ORDERS_DTO, ORDERS_HEADER_DTO, ITEM_WISE_PRICES_DTO, ORDER_ADDRESS_UPDATE_DTO))
                        : orderOpsFeignClient.getOrderDetails(incrementId);
                orderDetails = response != null ? response.getBody() : null;

                if (orderDetails != null && !orderDetails.getOrders().isEmpty()) {
                    customerId = orderDetails.getOrders().get(0).getCustomerId();
                    OrdersDTO ordersDTO = orderDetails.getOrders().stream().filter(o -> magentoItemId.equals(o.getMagentoItemId()) && Objects.equals(o.getParentId(), 0)).findFirst().orElse(null);
                    if (ordersDTO == null) {
                        log.info("[FBR] No valid orders for incrementId {}", incrementId);
                        return new FrameBrokenApprovalResponse(false, "No valid orders for item id.", null, null);
                    }
                    itemId = ordersDTO.getItemId();
                    Integer itemIdValue = itemId;
                    ItemWisePriceDetailsDTO itemWisePriceDetailsDTO = orderDetails.getItemWisePrices().stream().filter(o -> itemIdValue.equals(o.getItemId())).findFirst().orElse(null);
                    if (StringUtils.isBlank(gvAmountRequested) && StringUtils.isNotBlank(req.reason())) {
                        gvAmountRequested = getGvAmountRequested(ordersDTO, req.reason(), itemWisePriceDetailsDTO);
                        isLocalFittingFlow = true;
                    }
                } else {
                    log.info("[FBR] No valid orders for incrementId {}", incrementId);
                    return new FrameBrokenApprovalResponse(false, "No valid orders for order id.", null, null);
                }

                if (StringUtils.isBlank(mobile)) {
                    Optional<OrderAddressUpdateDTO> orderAddressUpdate = response.getBody().getOrderAddressUpdates().stream().filter(orderAddressUpdateDTO -> "billing".equalsIgnoreCase(orderAddressUpdateDTO.getAddressType())).findAny();
                    if (orderAddressUpdate.isPresent()) {
                        mobile = orderAddressUpdate.get().getTelephone();
                    }
                }
            }

            if (StringUtils.isBlank(gvAmountRequested)) {
                return new FrameBrokenApprovalResponse(false, "Could not calculate gvAmountRequested", null, null);
            }

            if (StringUtils.isBlank(mobile))
                return new FrameBrokenApprovalResponse(false, "Invalid Mobile Number - Missing", null, null);

            if (mobile.startsWith("+") && !mobile.startsWith("+91"))
                return new FrameBrokenApprovalResponse(false, "Country code of mobile not supported", null, null);

            if (mobile.startsWith("+91-"))
                mobile = mobile.substring(4);


            if (!isEligibleForFrameBrokenApproval(req, customerId, mobile)) {
                return new FrameBrokenApprovalResponse(false, "Customer not eligible for frame broken approval, exceeding yearly limit.", null, null);
            }

            var approval = saveFrameBrokenApproval(req, customerId, itemId, gvAmountRequested, mobile);
            if (approval == null || !FBR_APPROVED_STATUS.equalsIgnoreCase(approval.getStatus())) {
                return new FrameBrokenApprovalResponse(true, "Rejected frame broken approval.", null, null);
            }

            var gvResponse = getGiftVoucher(req, gvAmountRequested, isLocalFittingFlow, mobile);

            if (gvResponse == null || gvResponse.getResult() == null || gvResponse.getResult().getId() == null) {
                return new FrameBrokenApprovalResponse(false, "GV creation failed.", null, null);
            }

            if (orderDetails != null) {
                markOrderClosed(incrementId, itemId, orderDetails);
            }

            Map<String, Object> payload = getFbrSmsMap(gvResponse, incrementId, itemId);
            if (payload == null) {
                return new FrameBrokenApprovalResponse(true, "GV created successfully, but got error while sending sms", gvResponse.getResult().getCode(), gvAmountRequested);
            }
            sendFrameBrokenCommunication(incrementId, payload, isLocalFittingFlow);
            return new FrameBrokenApprovalResponse(true, "GV sent successfully", gvResponse.getResult().getCode(), gvAmountRequested);

        } catch (Exception e) {
            log.error("[FBR] Exception while processing request: ", e);
            return new FrameBrokenApprovalResponse(false, "Unexpected error: " + e.getMessage(), null, null);
        }
    }

    private String getGvAmountRequested(OrdersDTO ordersDTO, String reason, ItemWisePriceDetailsDTO itemWisePriceDetailsDTO) {
        BigDecimal gvAmount = null;
        if (ordersDTO != null && StringUtils.isNotBlank(reason)) {
            if (reason.equalsIgnoreCase("FREELENS-FBR")) {
                SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey("frame-broken-gv", "frame-broken-gv-amount");
                if (systemPreference != null && StringUtils.isNotBlank(systemPreference.getValue()) && !systemPreference.getValue().equalsIgnoreCase("null")) {
                    log.info("[getGvAmountRequested] magentoId : {} , frame-broken-gv-amount: {}", ordersDTO.getMagentoItemId(), systemPreference.getValue());
                    gvAmount = new BigDecimal(systemPreference.getValue());
                }
            } else if (itemWisePriceDetailsDTO != null) {
                BigDecimal scDiscount = nonNull(itemWisePriceDetailsDTO.getStoreCreditDiscount())
                        .subtract(nonNull(itemWisePriceDetailsDTO.getExchangeDiscount()));

                gvAmount = BigDecimal.ZERO;
                gvAmount = gvAmount.add(scDiscount)
                        .add(nonNull(itemWisePriceDetailsDTO.getGiftVoucherDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getPrepaidDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getImplicitDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getLenskartDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getLenskartPlusDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getCouponDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getItemTotalAfterDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getTaxCollected()))
                        .add(nonNull(itemWisePriceDetailsDTO.getFcDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getExchangeDiscount()))
                        .add(nonNull(itemWisePriceDetailsDTO.getGiftCardDiscount()))
                        .subtract(nonNull(itemWisePriceDetailsDTO.getShippingCharges()));
            }
        }

        if (gvAmount != null) {
            SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey("frame-broken-gv", "delta-amount");
            if (systemPreference != null && StringUtils.isNotBlank(systemPreference.getValue()) && !systemPreference.getValue().equalsIgnoreCase("null")) {
                log.info("[getGvAmountRequested] magentoId : {} , delta amount: {}", ordersDTO.getMagentoItemId(), systemPreference.getValue());
                gvAmount = gvAmount.add(new BigDecimal(systemPreference.getValue()));
            }
        }
        return gvAmount != null ? gvAmount.setScale(0, RoundingMode.CEILING).toPlainString() : null;
    }

    private BigDecimal nonNull(Double value) {
        return value != null ? BigDecimal.valueOf(value) : BigDecimal.ZERO;
    }

    private static Map<String, Object> getFbrSmsMap(GiftVoucherResponse gvResponse, Integer incrementId, Integer itemId) {
        try {

            Map<String, Object> smsParams = new HashMap<>();
            smsParams.put("customerMobile", gvResponse.getResult().getPhoneCode() + "-" + gvResponse.getResult().getCustomerPhone());
            smsParams.put("customerEmail", gvResponse.getResult().getCustomerEmail());
            smsParams.put("customerName", StringUtils.isBlank(gvResponse.getResult().getCustomerName()) ? "Customer" : gvResponse.getResult().getCustomerName().trim());
            smsParams.put("gvCode", gvResponse.getResult().getCode());
            smsParams.put("gvAmount", gvResponse.getResult().getBalance());
            smsParams.put("gvExpiry", DateUtil.formatDate(gvResponse.getResult().getExpiredAt(), "yyyy-MM-dd", "MMMM d, yyyy", -1));
            smsParams.put("currency", "INR".equalsIgnoreCase(gvResponse.getResult().getCurrency()) ? "₹" : gvResponse.getResult().getCurrency() + " ");
            smsParams.put("orderId", incrementId);
            smsParams.put("itemId", itemId);
            return smsParams;
        } catch (Exception e) {
            log.error("[getFbrSmsMap] Exception occurred while generating sms payload ", e);
        }
        return null;
    }


    private boolean isEligibleForFrameBrokenApproval(FrameBrokenApprovalRequest req, Long customerId, String mobile) {
        if (req == null) return false;

        if (customerId == null && (mobile == null || mobile.isBlank())) return false;

        return getCountInLast365Days(customerId, mobile, req.repairId()) <= 1;
    }

    private int getCountInLast365Days(Long customerId, String mobile, String repairId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneYearAgo = now.minusDays(365);
        Timestamp start = Timestamp.valueOf(oneYearAgo);
        Timestamp end = Timestamp.valueOf(now);

        if (repairId == null) {
            return (customerId != null)
                    ? frameBrokenApprovalRepo.countByCustomerIdAndStatusAndCreatedAtBetweenAndRepairIdIsNull(
                    customerId, FBR_APPROVED_STATUS, start, end)
                    : frameBrokenApprovalRepo.countByMobileAndStatusAndCreatedAtBetweenAndRepairIdIsNull(
                    mobile, FBR_APPROVED_STATUS, start, end);
        } else {
            return (customerId != null)
                    ? frameBrokenApprovalRepo.countByCustomerIdAndStatusAndCreatedAtBetween(
                    customerId, FBR_APPROVED_STATUS, start, end)
                    : frameBrokenApprovalRepo.countByMobileAndStatusAndCreatedAtBetween(
                    mobile, FBR_APPROVED_STATUS, start, end);
        }
    }

    private GiftVoucherResponse getGiftVoucher(FrameBrokenApprovalRequest frameBrokenApprovalRequest, String gvAmountRequested, boolean isLocalFittingFlow, String mobile) {
        GiftVoucherRequest.Payload payload = new GiftVoucherRequest.Payload();
        payload.setCode(isLocalFittingFlow ? FBR_GV_LENS_CODE : FBR_GV_CODE);
        payload.setBalance(gvAmountRequested);
        payload.setCurrency("INR"); //<NAME_EMAIL>
        payload.setStatus("STATUS_ACTIVE");
        if (frameBrokenApprovalRequest.orderId() != null) {
            payload.setComments(String.format("Frame broken GV given against itemId %s and incrementId %s and repairId %s", frameBrokenApprovalRequest.itemId(), frameBrokenApprovalRequest.orderId(), frameBrokenApprovalRequest.repairId()));
        } else {
            payload.setComments(String.format("Frame broken GV given against repairId %s", frameBrokenApprovalRequest.repairId()));
        }
        payload.setPhoneCode("+91");
        payload.setCountryCode("IN");
        payload.setCustomerPhone(mobile);
        payload.setExpiredAt(DateUtil.getXMonthAfterDate(1));
        payload.setTicketId(Long.valueOf(frameBrokenApprovalRequest.ticketId()));
        ResponseEntity<GiftVoucherResponse> giftVoucherEntity = junoFeignClient.issueFrameBrokenVoucher(new GiftVoucherRequest(payload));
        log.info("[frameBrokenApproval] GiftVoucherResponse : {}", giftVoucherEntity);
        return giftVoucherEntity.getBody();
    }

    private void markOrderClosed(Integer incrementId, Integer itemId, OrderInfoResponseDTO orderInfo) {
        try {
            List<OrdersDTO> ordersDTOS = orderInfo.getOrders();
            List<UwOrderDTO> uwOrderDTOS = orderInfo.getUwOrders();
            UwOrderDTO mainItem = findItemById(itemId, uwOrderDTOS);
            if (mainItem == null) return;

            log.info("[markOrderClosed] Main UwOrderDTO: {}", mainItem);

            List<OrderStatusUpdateDTO> updateDTOs = new ArrayList<>();

            if ("B2B".equalsIgnoreCase(mainItem.getProductDeliveryType())) {
                UwOrderDTO b2bRefItem = findB2BReferenceItem(mainItem, uwOrderDTOS);
                if (b2bRefItem != null) {
                    updateDTOs.add(buildOrderStatusUpdateDTO(incrementId, b2bRefItem, ordersDTOS));
                }
            }

            updateDTOs.add(buildOrderStatusUpdateDTO(incrementId, mainItem, ordersDTOS));

            log.info("[markOrderClosed] Final orderStatusUpdateDetails: {}", updateDTOs);
            kafkaService.pushToKafka("return_order_status_update", String.valueOf(incrementId), updateDTOs);

        } catch (Exception ex) {
            log.error("[markOrderClosed] Exception while pushing to Kafka: ", ex);
        }
    }

    private UwOrderDTO findItemById(Integer itemId, List<UwOrderDTO> uwOrderDTOS) {
        return uwOrderDTOS.stream()
                .filter(u -> itemId.equals(u.getItemId()))
                .findFirst()
                .orElse(null);
    }

    private UwOrderDTO findB2BReferenceItem(UwOrderDTO mainItem, List<UwOrderDTO> allItems) {
        return allItems.stream()
                .filter(u -> Objects.equals(u.getUwItemId(), mainItem.getB2bRefrenceItemId()))
                .findFirst()
                .orElse(null);
    }

    private OrderStatusUpdateDTO buildOrderStatusUpdateDTO(Integer incrementId, UwOrderDTO dto, List<OrdersDTO>  ordersDTOS) {
        OptionalLong magentoItemId = ordersDTOS.stream().filter(o -> dto.getItemId() == o.getItemId()).mapToLong(OrdersDTO::getMagentoItemId).findFirst();
        ItemsStateStatusDto itemDto = null;
        if (magentoItemId.isPresent()) {
            itemDto = buildItemDto(dto, magentoItemId.getAsLong());
        }
        return new OrderStatusUpdateDTO(
                incrementId,
                dto.getUnicomOrderCode(),
                dto.getShipmentStatus(),
                null,
                itemDto != null ? List.of(itemDto) : null
        );
    }

    private ItemsStateStatusDto buildItemDto(UwOrderDTO dto, Long magentoItemId) {
        ItemsStateStatusDto item = new ItemsStateStatusDto();
        item.setItemId(magentoItemId);
        item.setUwItemId(dto.getUwItemId());
        item.setOldState(dto.getShipmentState());
        item.setOldStatus(dto.getShipmentStatus());
        return item;
    }

    private void sendFrameBrokenCommunication(Integer incrementId, Map<String, Object> params, boolean isLocalFittingFlow) {
        CommRequestDTO commRequestDTO = new CommRequestDTO();
        commRequestDTO.setEventType(isLocalFittingFlow ? Constant.EVENT.LOCAL_FIT_FBR_APPROVAL : Constant.EVENT.FRAME_BROKEN_APPROVAL);
        commRequestDTO.setOrderId(incrementId);
        commRequestDTO.setParams(params);
        commRequestDTO.setSkipValidation(true);
        communicationService.pushToCommunicationTopic(commRequestDTO);
    }


    private FrameBrokenApproval saveFrameBrokenApproval(FrameBrokenApprovalRequest req, Long customerId, Integer itemId, String gvAmountRequested, String mobile) {
        if (req != null && StringUtils.isNotEmpty(req.status())
            && (FBR_APPROVED_STATUS.equalsIgnoreCase(req.status())
                || FBR_REJECTED_STATUS.equalsIgnoreCase(req.status()))) {
            FrameBrokenApproval frameBrokenApproval = new FrameBrokenApproval();
            frameBrokenApproval.setStatus(req.status().toUpperCase());
            if (StringUtils.isNotBlank(req.orderId()) && itemId != null)
            {
                frameBrokenApproval.setIncrementId(Integer.valueOf(req.orderId()));
                frameBrokenApproval.setItemId(itemId);
            }
            frameBrokenApproval.setNotes(req.notes());
            frameBrokenApproval.setGvAmountRequested(new BigDecimal(gvAmountRequested));
            if (StringUtils.isNotBlank(req.reason())) {
                frameBrokenApproval.setReason(req.reason());
            }
            if (StringUtils.isNotBlank(req.repairId())) {
                frameBrokenApproval.setRepairId(Integer.valueOf(req.repairId()));
            }
            frameBrokenApproval.setTicketId(req.ticketId());
            frameBrokenApproval.setMobile(mobile);
            frameBrokenApproval.setCustomerId(customerId);
            return frameBrokenApprovalRepo.save(frameBrokenApproval);
        }
        log.info("[frameBrokenApproval] Frame broken approval not saved");
        return null;
    }

    @Override
    public JunoGiftCardResponse getGiftCardDetails(int orderId, String itemIds) throws Exception {

        log.info("getGiftCardDetails start : orderId : "+orderId+" itemIds : "+itemIds);

        try {
            RestTemplate template = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("user-agent", "Application");
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            headers.set(API_CLIENT_KEY, "desktop");
            HttpEntity<String> entity = new HttpEntity<>( headers);

            String url = String.format("%s/orders/gift-card/breakup?orderId=%s&itemIds=%s", junoUrl, orderId, itemIds);
            log.info("url={} headers={} ",url,headers);
            ResponseEntity<String> responseEntity = template.exchange(url, HttpMethod.GET,entity, String.class);
            if(!HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                throw new Exception("Didn't received Http Status code 200 from Juno gift card breakup API for entity" + entity);
            }
            log.info("getGiftCardDetails responseEntity : {}", responseEntity);
            return objectMapper.readValue(responseEntity.getBody(), JunoGiftCardResponse.class);
        } catch (Exception e){
            log.error("getGiftCardDetails exception for orderId {}", orderId, e);
            return createJunoGiftCardResponseDTOForFailedRequest(e.getMessage());
        }
    }

    private JunoGiftCardResponse createJunoGiftCardResponseDTOForFailedRequest(String message) {
        return JunoGiftCardResponse.builder()
                .message(message)
                .build();
    }
}
