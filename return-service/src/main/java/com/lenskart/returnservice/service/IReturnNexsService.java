package com.lenskart.returnservice.service;

import com.lenskart.returncommon.exception.UniCommerceException;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.return_refund_rules.model.ReturnItemRequest;
import com.lenskart.returncommon.exception.ReturnRequestFailException;
import com.lenskart.returncommon.model.response.NexsReturnResponse;

import java.util.List;

public interface IReturnNexsService {
    public NexsReturnResponse createReversePickupNexs(List<ReturnItemRequestDTO> returnItemRequestList, String saleOrderCode, Integer incrementId, List<UwOrderDTO> uwOrderDTOS) throws ReturnRequestFailException;
    NexsReturnResponse createReturnInNexsV2(List<ReturnItemRequestDTO> returnItemRequestList, String saleOrderCode, Integer incrementId, String facility, List<UwOrderDTO> uwOrderDTOS) throws UniCommerceException;

}
