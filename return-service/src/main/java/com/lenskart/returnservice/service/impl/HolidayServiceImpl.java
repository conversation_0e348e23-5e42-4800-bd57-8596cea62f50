package com.lenskart.returnservice.service.impl;

import com.lenskart.returnrepository.repository.HolidayListRepository;
import com.lenskart.returnservice.service.IHolidayService;
import com.lenskart.returnrepository.entity.HolidayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class HolidayServiceImpl implements IHolidayService {


    @Autowired
    HolidayListRepository holidayListRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Integer getHolidays(Integer deliveryDays,String countryCode) {
        Calendar date = Calendar.getInstance();
        date.add(Calendar.DATE, 0);
        Date initialDate = new Date(date.getTime().getTime());
        date.add(Calendar.DATE, deliveryDays);
        Date finalDate = new Date(date.getTime().getTime());
        log.info("[getHolidays] initialDate {} finalDate {} countryCode {}",initialDate,finalDate,countryCode);
        return getHolidaysBetweenDate(initialDate, finalDate,countryCode);
    }

    public Integer getHolidaysBetweenDate(Date initialDate, Date finalDate,String countryCode) {
        List<HolidayList> holidays = null;
        Integer holidayCount = 0;
        try {
            log.info("[getHolidaysBetweenDate] getting data from redis for key holiday_list_"+countryCode+" initialDate "+initialDate);
            holidays = (List<HolidayList>) redisTemplate.opsForValue().get("holiday_list_"+countryCode);
            if(CollectionUtils.isEmpty(holidays)){
                log.info("[getHolidaysBetweenDate] holidays from cache is null or empty");
            }
            else {
                log.info("[getHolidaysBetweenDate] holidays {}", holidays.get(0));
            }
        } catch(Exception e) {
            log.error("[getHolidaysBetweenDate] Redis Error while reading data : ", e);
        }
        if( CollectionUtils.isEmpty(holidays)) {
            try {
                log.info("[getHolidaysBetweenDate] getting data from db initialDate "+initialDate);
                holidays = holidayListRepository.findByCountryCode(countryCode);
                if(CollectionUtils.isEmpty(holidays)){
                    log.info("[getHolidaysBetweenDate] holidays from db is null or empty");
                }
                else {
                    log.info("[getHolidaysBetweenDate] holidays {}", holidays.get(0));
                    redisTemplate.opsForValue().set("holiday_list_"+countryCode, holidays, 12, TimeUnit.HOURS);
                }
            } catch(Exception e) {
                log.error("[getHolidaysBetweenDate] Redis Error while setting data :", e);
            }
        }
        if(null != holidays) {
            log.info("[getHolidaysBetweenDate] holidays size {}", holidays.size());
            for(HolidayList holiday : holidays) {
                if(holiday.getDate() == null){
                    log.info("[getHolidaysBetweenDate] date is null");
                }
                else {
                    if (holiday.getDate().compareTo(initialDate) >= 0 && holiday.getDate().compareTo(finalDate) <= 0) {
                        holidayCount++;
                    }
                }
            }
        }

        log.info("[getHolidaysBetweenDate] Holidays : "+holidayCount + ", initialDate:"+ initialDate +", finalDate:"+finalDate);
        return holidayCount;
    }
}
