package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailAddressUpdate;
import com.lenskart.returnrepository.repository.ReturnOrderAddressUpdateRepository;
import com.lenskart.returnservice.service.IExchangeDispatchTatService;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ReturnHeadingPlaceHolderValueFetchExchangeDispatchDateStrategy implements IReturnHeadingPlaceHolderValueFetchStrategy {
    @Autowired
    IExchangeDispatchTatService exchangeDispatchTatService;
    @Autowired
    ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;
    @Autowired
    IReturnOrderActionService returnOrderActionService;

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse) {
        ReturnStatusHeadingDetail returnStatusHeadingDetail = null;
        try {
            if (null != returnDetailsResponse) {
                returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            }
            ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
            if (returnDetail != null) {
                if (Constant.RETURN_METHOD.SHIP_TO_LENSKRT.equalsIgnoreCase(returnDetail.getReturnMethod()) ||
                        Constant.RETURN_METHOD.RETURN_TO_STORE.equalsIgnoreCase(returnDetail.getReturnMethod())) {
                    returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                } else {
                    ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                    if (null != returnOrderAddressUpdate && null != returnOrderAddressUpdate.getPostcode()) {
                        Date exchangeDispatchDate = exchangeDispatchTatService.getExchangeDispatchETA(returnDetailsResponse, returnDetail.getIncrementId(),
                                String.valueOf(returnOrderAddressUpdate.getPostcode()), returnDetail.getReturnCreateDatetime());
                        if (null != exchangeDispatchDate) {
                            if (null != returnStatusHeadingDetail) {
                                returnStatusHeadingDetail.setEstimatedDate(exchangeDispatchDate.getTime());
                            }
                        } else {
                            if (null != returnStatusHeadingDetail) {
                                returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                            }
                        }
                    } else {
                        returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                    }
                }
            }
        }catch (Throwable e){
            log.error("Exception while adding dates inreturn  heading", e);
            if(null != returnStatusHeadingDetail){
                returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
            }
        }
    }

    @Override
    public void getReturnTimelinePlaceHolderValue(ReturnTimeLine returnTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
        try {
            if (null != returnStatusHeadingDetail) {
                if(null != returnStatusHeadingDetail.getEstimatedDate()) {
                    returnTimeLine.setReturnEstimatedDate(returnStatusHeadingDetail.getEstimatedDate());
                }else{
                    returnTimeLine.setReturnSubStatus("");
                }
            }
        }catch (Throwable e){
            log.error("exception in getReturnTimelinePlaceHolderValue", e);
            if(null != returnTimeLine){
                returnTimeLine.setReturnSubStatus("");
            }
        }
    }

    @Override
    public void getRefundTimelinePlaceHolderValue(RefundTimeLine refundTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
        try {
            if (null != returnStatusHeadingDetail) {
                if(null != returnStatusHeadingDetail.getEstimatedDate()) {
                    refundTimeLine.setRefundEstimatedDate(returnStatusHeadingDetail.getEstimatedDate());
                }else{
                    refundTimeLine.setRefundSubStatus("");
                }
            }
        }catch (Throwable e){
            log.error("exception in getReturnTimelinePlaceHolderValue", e);
            if(null != refundTimeLine){
                refundTimeLine.setRefundSubStatus("");
            }
        }
    }

    @Override
    public void getExchangeTimelinePlaceHolderValue(ExchangeTimeLine exchangeTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
        try {
            if (null != returnStatusHeadingDetail) {
                try {
                    ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
                    if (returnDetail != null) {
                        if (Constant.RETURN_METHOD.SHIP_TO_LENSKRT.equalsIgnoreCase(returnDetail.getReturnMethod()) ||
                                Constant.RETURN_METHOD.RETURN_TO_STORE.equalsIgnoreCase(returnDetail.getReturnMethod())) {
                            exchangeTimeLine.setExchangeSubStatus("");
                        } else {
                            ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                            if (null != returnOrderAddressUpdate && null != returnOrderAddressUpdate.getPostcode()) {
                                Date exchangeDispatchDate = exchangeDispatchTatService.getExchangeDispatchETA(returnDetailsResponse, returnDetail.getIncrementId(),
                                        String.valueOf(returnOrderAddressUpdate.getPostcode()), returnDetail.getReturnCreateDatetime());
                                if (null != exchangeDispatchDate) {
                                    exchangeTimeLine.setExchangeDispatchDate(exchangeDispatchDate.getTime());
                                } else {
                                    exchangeTimeLine.setExchangeSubStatus("");
                                }
                            } else {
                                exchangeTimeLine.setExchangeSubStatus("");
                            }
                        }
                    }
                } catch (Throwable e) {
                    log.error("exception in getReturnTimelinePlaceHolderValue", e);
                    if (null != returnStatusHeadingDetail) {
                        exchangeTimeLine.setExchangeSubStatus("");
                    }
                }
            }
        } catch (Throwable e) {
            log.error("exception in getReturnTimelinePlaceHolderValue", e);
            if (null != returnStatusHeadingDetail) {
                exchangeTimeLine.setExchangeSubStatus("");
            }
        }
    }

    public void getReturnHistoryPlaceHolderValue(ReturnStatusHistory returnStatusHistory, ReturnDetailsResponse returnDetailsResponse){

    }

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse, List<RefundDetails> refundDetailsList, ReturnStatusHeadingDetail returnStatusHeadingDetail, Long incrementId, Date createdAt, String anyReceivingPoint) {

    }

}
