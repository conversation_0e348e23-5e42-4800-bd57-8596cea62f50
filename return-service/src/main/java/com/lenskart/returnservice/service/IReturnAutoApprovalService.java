package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;

public interface IReturnAutoApprovalService {
    void autoApproveReturn(ReturnCreationRequestDTO returnCreationRequest, ReturnCreationResponse refundRequestResponse);
}
