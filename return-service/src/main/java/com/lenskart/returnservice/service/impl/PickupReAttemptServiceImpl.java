package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.enums.UserActionEnum;
import com.lenskart.returncommon.model.request.NonPickupCustomerFeedback;
import com.lenskart.returncommon.model.response.ClickpostRescheduleResponse;
import com.lenskart.returncommon.model.response.ReturnCancellabilityResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.lenskart.returncommon.model.enums.LKCountry.IN;
import static com.lenskart.returncommon.utils.Constant.IVRCustomerFeedbackConstant.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.CANCELLED;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_STATUS_HISTORY_QUEUE;
import static com.lenskart.returncommon.utils.Constant.SUCCESS;

@Slf4j
@Service
public class PickupReAttemptServiceImpl implements IPickupReAttemptService {

    @Autowired
    INprCommunicationHistoryRepository nprCommunicationHistoryRepository;
    @Autowired
    IReturnDetailsService returnDetailsService;
    @Autowired
    IReturnUpdateService returnUpdateService;
    @Autowired
    ReverseTrackingEventRepository reverseTrackingEventRepository;
    @Autowired
    IReturnEventService returnEventService;
    @Autowired
    IHolidayService holidayService;
    @Autowired
    IKafkaService kafkaService;
    @Value("${clickpost.base.url}")
    String clickPostUrl;
    @Value("${clickpost.key}")
    String clickPostKey;
    @Value("${clickpost.userName}")
    String userName;

    private ObjectMapper objectMapper;
    private RestTemplate restTemplate;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @PostConstruct
    public void init() {
        objectMapper = new ObjectMapper();
        restTemplate = new RestTemplate();
    }

    @Override
    public void processCustomerFeedback(NonPickupCustomerFeedback nonPickupCustomerFeedback) {
        log.info("[processCustomerFeedback] with customerFeedbackWebhook:{}", nonPickupCustomerFeedback);

        if (nonPickupCustomerFeedback == null) {
            throw new RuntimeException("Invalid Request!");
        }

        NprCommunicationHistory nprCommunicationHistory = nprCommunicationHistoryRepository.findByUniqueId(nonPickupCustomerFeedback.getUniqueId());
        if (Objects.isNull(nprCommunicationHistory)) {
            throw new RuntimeException("Given uniqueId: " + nonPickupCustomerFeedback.getUniqueId() + " is not valid");
        }

        if (!isValidateRequest(nonPickupCustomerFeedback)) {
            updateNprUserStatusAndComment(FAILED, "ReturnOrderId is not valid", nprCommunicationHistory, nonPickupCustomerFeedback.getAction().toString());
            throw new RuntimeException("Given returnOrderID: " + nonPickupCustomerFeedback.getReturnId() + " is not valid");
        }

        try {
            UserActionEnum action = nonPickupCustomerFeedback.getAction();
            Integer returnId = nonPickupCustomerFeedback.getReturnId();
            String uniqueId = nonPickupCustomerFeedback.getUniqueId();
            updateNPRCommunicationHistory(nonPickupCustomerFeedback.toString(), action.name(), uniqueId, nonPickupCustomerFeedback.getSource());
            nprCommunicationHistory = nprCommunicationHistoryRepository.findByUniqueId(nonPickupCustomerFeedback.getUniqueId());
            log.info("Updated the npr communication response!");

            if (UserActionEnum.RESCHEDULE_PICKUP.equals(action)) {
                reschedulePickupAction(nonPickupCustomerFeedback);
            } else if (UserActionEnum.CANCEL_PICKUP.equals(action)) {

                if (checkEligibilityForCancelReturnOrder(returnId, nprCommunicationHistory, nonPickupCustomerFeedback)) {
                    if (cancelReturnOrder(returnId)) {
                        updateNprUserStatusAndComment(SUCCESS, null, nprCommunicationHistory, nonPickupCustomerFeedback.getAction().toString());
                        log.info("Order cancelled for returnOrderId:{}", returnId);
                    } else {
                        updateNprUserStatusAndComment(FAILED, "Fail to cancel the order", nprCommunicationHistory, nonPickupCustomerFeedback.getAction().toString());
                        log.error("Order not able to cancel for returnOrderId:{}", returnId);
                    }
                }
            } else if (UserActionEnum.CUSTOMER_SUPPORT.equals(action)) {
                updateNprUserStatusAndComment(SUCCESS, null, nprCommunicationHistory, nonPickupCustomerFeedback.getAction().toString());
            }
        } catch (Exception e) {
            log.error("Error :{}", (Object) e.getStackTrace());
            updateNprUserStatusAndComment(FAILED, e.getMessage(), nprCommunicationHistory, nonPickupCustomerFeedback.getAction().toString());
        }
    }

    public boolean checkEligibilityForCancelReturnOrder(Integer returnOrderId, NprCommunicationHistory nprCommunicationHistory, NonPickupCustomerFeedback customerFeedback) {
        log.info("[checkEligibilityForCancelReturnOrder] for returnOrderId:{}", returnOrderId);
        ReturnDetailItem returnOrderItem = returnOrderActionService.findTopByReturnId(returnOrderId);

        if (!Objects.isNull(returnOrderItem)) {
            try {
                ReturnCancellabilityResponse returnCancellabilityResponse = returnDetailsService.getReturnCancellabilityForItem(returnOrderItem.getUwItemId());
                log.info("[checkEligibilityForCancelReturnOrder] returnCancellabilityResponse:{}", returnCancellabilityResponse);
                if (returnCancellabilityResponse.getIsCancellable()) {
                    return true;
                } else {
                    log.info("ReturnOrderId :{} isn't cancellable", returnOrderId);
                    updateNprUserStatusAndComment(FAILED, returnCancellabilityResponse.getErrorMessage(), nprCommunicationHistory, customerFeedback.getAction().toString());
                    return false;
                }
            } catch (Exception ex) {
                log.error("Error:{}", (Object) ex.getStackTrace());
                updateNprUserStatusAndComment(FAILED, ex.getMessage(), nprCommunicationHistory, customerFeedback.getAction().toString());
                return false;
            }
        } else {
            updateNprUserStatusAndComment(FAILED, "Return Order Eligibility Failed", nprCommunicationHistory, customerFeedback.getAction().toString());
            return false;
        }

    }

    private boolean isValidateRequest(NonPickupCustomerFeedback customerFeedback) {

        Optional<ReturnDetail> returnOrder = returnOrderActionService.findReturnOrderById(customerFeedback.getReturnId());
        if (returnOrder.isEmpty()) {
            log.error("Given returnOrderId:{} is not valid", customerFeedback.getReturnId());
            return false;
        }
        return true;

    }

    private void updateNPRCommunicationHistory(String responseBody, String userAction, String uniqueId, String source) {
        log.info("[updateNPRCommunicationHistory] with responseBogy:{},userAction:{},uniqueId:{}", responseBody, userAction, uniqueId);
        nprCommunicationHistoryRepository.updateResponseForUniqueId(userAction, source, uniqueId);
    }

    private Map<String, String> calculatePickupDate(Date currentDate) {
        Map<String, String> pickupDateMap = new HashMap<>();

        if (currentDate.getHours() < 10) {
            pickupDateMap.put(PICK_UP_DATE, String.valueOf(currentDate.getDate()));
            pickupDateMap.put(COMMENT, "Please Pickup Today");
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            //need to call order-ops to get no-of holidays
            Integer noOfHoliday = holidayService.getHolidays(1, IN.value());
            int totalDayToAdd = 1;
            if (noOfHoliday != 0) {
                totalDayToAdd = totalDayToAdd + noOfHoliday;
            }
            calendar.add(Calendar.DAY_OF_MONTH, totalDayToAdd);
            Date nextDate = calendar.getTime();
            SimpleDateFormat sm = new SimpleDateFormat(PICKUP_DATE_FORMAT);
            pickupDateMap.put(PICK_UP_DATE, sm.format(nextDate));
            pickupDateMap.put(COMMENT, "Please Pickup Tomorrow");
        }

        return pickupDateMap;
    }

    private boolean cancelReturnOrder(Integer returnOrderId) {
        log.info("[cancelReturnOrder] with returnOrderId:{}", returnOrderId);
        try {
            ReturnDetailUpdateRequestDto request = new ReturnDetailUpdateRequestDto();
            request.setReturnId(returnOrderId);
            request.setStatus(CANCELLED);
            ReturnDetailsUpdateResponse returnDetailsUpdateResponse = returnUpdateService.updateReturnStatus(request);
            log.info("[cancelReturnOrder] returnDetailsUpdateResponse:{}", returnDetailsUpdateResponse.toString());
            return returnDetailsUpdateResponse.getStatus().equalsIgnoreCase(SUCCESS);
        } catch (Exception ex) {
            log.error("Error:{}", ex.getStackTrace());
            return false;
        }
    }

    private void reschedulePickupAction(NonPickupCustomerFeedback customerFeedback) {
        log.info("[reschedulePickupAction] with customerFeedback:{}", customerFeedback);
        Integer returnId = customerFeedback.getReturnId();
        ReturnDetail returnOrder = returnOrderActionService.findReturnOrderById(returnId).orElse(null);
        NprCommunicationHistory nprCommunicationHistory = nprCommunicationHistoryRepository.findByUniqueId(customerFeedback.getUniqueId());
        Map<String, String> pickupDateMap = calculatePickupDate(new Date());
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        Map<String, Object> params = new HashMap<>();
        params.put(PREFERRED_DATE, pickupDateMap.get(PICK_UP_DATE));
        params.put(COMMENT, pickupDateMap.get(COMMENT));
        params.put(PHONE_NUMBER, nprCommunicationHistory.getPhoneNo());
        params.put(ISSUE_ID, getIssueIdByNprCode(nprCommunicationHistory.getNprStatusCode()));
        log.info("[reschedulePickupAction] params:{}", params);
        ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTopByReturnIdOrderByIdDesc(customerFeedback.getReturnId());
        String awbAssigned = returnCourierDetail.getReverseAwbNumber() != null ? returnCourierDetail.getReverseAwbNumber() : returnCourierDetail.getReversePickupReferenceId();
        HttpEntity<?> entity = new HttpEntity<>(params, headers);
        String urlTemplate = UriComponentsBuilder.fromHttpUrl(clickPostUrl)
                .queryParam(KEY, clickPostKey)
                .queryParam(AWB, awbAssigned)
                .queryParam(USERNAME, userName)
                .build()
                .encode()
                .toUriString();

        log.info("[reschedulePickupAction] urlTemplate:{}", urlTemplate);

        try {
            HttpEntity<String> response = restTemplate.exchange(urlTemplate, HttpMethod.POST, entity, String.class);
            log.info("clickPost API response:{}", response.toString());
            ClickpostRescheduleResponse clickpostRescheduleResponse = new ObjectMapper().readValue(response.getBody(), ClickpostRescheduleResponse.class);
            if (clickpostRescheduleResponse != null && clickpostRescheduleResponse.getMeta().getSuccess()) {
                updateTablesForReschedulePickup(returnId, returnOrder, returnCourierDetail);
                updateNprUserStatusAndComment(SUCCESS, null, nprCommunicationHistory, customerFeedback.getAction().toString());
            } else {
                log.info("clickPost API response is not success!");
                updateNprUserStatusAndComment(FAILED, "Getting Failure status from clickPost API", nprCommunicationHistory, customerFeedback.getAction().toString());
            }
        } catch (Exception ex) {
            log.error("error:{}", ex.getStackTrace());
            updateNprUserStatusAndComment(FAILED, ex.getMessage(), nprCommunicationHistory, customerFeedback.getAction().toString());
        }
    }

    private void updateTablesForReschedulePickup(Integer returnId, ReturnDetail returnOrder, ReturnCourierDetail returnCourierDetail) {
        updateReverseTrackingEvents(returnId, returnOrder, returnCourierDetail);
        returnEventService.createReturnEvent(returnOrder.getRequestId(), returnOrder.getId(), Constant.RETURN_STATUS.AWB_ASSIGNED, "awb_rescheduled");
        insertIntoOrderStatusComments(returnOrder.getIncrementId());
    }

    private void updateReverseTrackingEvents(Integer returnOrderId, ReturnDetail returnOrder, ReturnCourierDetail returnCourierDetail) {
        ReverseDetailTrackingEvent reverseTrackingEvent = new ReverseDetailTrackingEvent();
        reverseTrackingEvent.setReturnId(returnOrderId);
        reverseTrackingEvent.setOrderId(returnOrder.getIncrementId());
        reverseTrackingEvent.setReverseAwb(returnCourierDetail.getReverseCourier() != null ? returnCourierDetail.getReverseAwbNumber() : returnCourierDetail.getReversePickupReferenceId());
        reverseTrackingEvent.setCreateDatetime(new Date());
        reverseTrackingEvent.setEventDateTime(new Date());
        reverseTrackingEvent.setTrackingRemark("awb_rescheduled");
        reverseTrackingEvent.setReverseMappedStatus("awb_rescheduled");
        reverseTrackingEvent.setTrackingStatusCode(28);
        reverseTrackingEvent.setNotificationEventId(9);
        reverseTrackingEventRepository.save(reverseTrackingEvent);
        log.info("reverseTrackingEventRepositoryId:{}", reverseTrackingEvent.getId());
        log.info("Reverse Tracking events entry created");

    }

    private String getIssueIdByNprCode(int nprStatusCode) {

        Integer issueId = null;

        switch (nprStatusCode) {
            case 1: //Customer unreachable
                issueId = 1;//CUSTOMER_UNREACHABLE
                break;
            case 2://Cancelled by customer
                issueId = 2;//CUSTOMER_CANCELLED
                break;
            case 3://QC failed
                issueId = 3;//PRODUCT_ISSUE
                break;
            case 9://Not attempted
                issueId = 5;//COURIER_DID_NOT_ATTEMPT
                break;
            default:// For all the other npr codes
                issueId = 4;//OTHER

        }
        return issueId.toString();

    }

    public void insertIntoOrderStatusComments(Integer incrementId) {
        try {
            log.info("[pickUpReAttemptServiceImpl] going to insert order-status-history comment for claim update, comment : " + incrementId);
            OrderCommentReq orderCommentReq = new OrderCommentReq();
            orderCommentReq.setIncrementId(incrementId);
            orderCommentReq.setComment("reverse pickup rescheduled based on customer feedback!");
            orderCommentReq.setCommentType("user");
            kafkaService.pushToKafka(ORDER_STATUS_HISTORY_QUEUE, String.valueOf(incrementId), objectMapper.writeValueAsString(orderCommentReq));
        } catch (Exception e) {
            log.error("[pickUpReAttemptServiceImpl] exception while pushing to queue :" + e);
        }
    }

    private void updateNprUserStatusAndComment(String userActionStatus, String comment, NprCommunicationHistory nprCommunicationHistory, String userAction) {
        nprCommunicationHistory.setUserAction(userAction);
        nprCommunicationHistory.setUserActionStatus(userActionStatus);
        nprCommunicationHistory.setComment(comment);
        nprCommunicationHistory.setUpdatedAt(new Date());
        nprCommunicationHistoryRepository.save(nprCommunicationHistory);
    }
}
