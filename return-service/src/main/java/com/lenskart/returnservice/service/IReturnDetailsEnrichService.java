package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnrepository.entity.ReturnDetail;

public interface IReturnDetailsEnrichService {
    void enrichReturnDetails(ReturnDetail returnDetail, ReturnDetailsResponse response, OrderInfoResponseDTO orderInfoResponseDTO);

    void updateAmountToRefund(ReturnDetailsResponse response, OrderInfoResponseDTO orderInfoResponseDTO);

    void updateRefundTimeLines(ReturnDetail returnDetail, ReturnDetailsResponse response);
}
