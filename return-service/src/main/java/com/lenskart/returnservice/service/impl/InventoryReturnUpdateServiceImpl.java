package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.orderops.model.ReturnOrder;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.ReverseLogisticsConsumerFeignClient;
import com.lenskart.returnservice.service.IInventoryReturnUpdateService;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.reversemodel.entity.ReverseTrackingEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
@Slf4j
public class InventoryReturnUpdateServiceImpl implements IInventoryReturnUpdateService {

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    @Autowired
    private ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Autowired
    private ReturnReasonRepository returnReasonRepository;

    @Autowired
    private ReturnRefundRuleRepository returnRefundRuleRepository;

    @Autowired
    private ReverseTrackingEventRepository reverseTrackingEventRepository;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    private DelightActionRepository delightActionRepository;

    @Autowired
    private IReturnOrderSaleDetailsRepository returnOrderSaleDetailsRepository;

    @Autowired
    private INprCommunicationHistoryRepository nprCommunicationHistoryRepository;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReverseLogisticsConsumerFeignClient reverseLogisticsConsumerFeignClient;

    ExecutorService executorService = Executors.newFixedThreadPool(5);

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Override
    public void updateReturnOrderService(Integer returnId) {
        log.info("[updateReturnOrderService] returnId : {}", returnId);
        try{
            ReturnDetail returnDetail = updateReturnOrder(returnId);
            if(returnDetail != null){
                updateReturnOrderItem(returnDetail, returnId);
                updateReturnOrderAddress(returnDetail, returnId);
                updateReturnReason(returnDetail, returnId);
                updateReturnRefundRule(returnDetail, returnId);
                updateReverseTrackingEvent(returnDetail, returnId);
                updateReturnHistory(returnDetail, returnId);
                updateDelightAction(returnDetail, returnId);
                updateReturnOrderSaleDetails(returnDetail, returnId);
                updateNprCommunicationHistory(returnDetail, returnId);
            }
        }catch (Exception exception){
            log.error("[returnSaleDetailsMap] exception : {}", exception.getMessage());
        }

    }

    @Override
    public Boolean updateReturns() {
        SystemPreference startDateSystemPref = systemPreferenceRepository.findByGroupAndKey("rollback-return", "startDate");
        SystemPreference endDateSystemPref = systemPreferenceRepository.findByGroupAndKey("rollback-return", "endDate");
        Date startDate = null, endDate = null;
        if(startDateSystemPref != null){
            String startDateValue = startDateSystemPref.getValue();
            try{
                startDate = DateUtil.getDate(startDateValue);
            }catch (Exception exception){
                log.error("[updateReturns] exception : {}",exception.getMessage());
            }

        }
        if(endDateSystemPref != null){
            String endDateValue = endDateSystemPref.getValue();
            try{
                endDate = DateUtil.getDate(endDateValue);
            }catch (Exception exception){
                log.error("[updateReturns] exception : {}",exception.getMessage());
            }
        }

        try{
            Pageable pageable = PageRequest.of(0, 10);
            Page<ReturnDetail> returnDetails = returnDetailRepository.findByReturnCreateDatetimeBetween(startDate, endDate, pageable);

            int totalPages = returnDetails.getTotalPages();

            for(int page = 0; page < totalPages; page++){
                pageable = PageRequest.of(page, 10);
                returnDetails = returnDetailRepository.findByReturnCreateDatetimeBetween(startDate, endDate, pageable);
                long totalElements = returnDetails.getNumberOfElements();
                log.info("[updateReturns] totalElements : {}", totalElements);
                List<ReturnDetail> returnDetailList = returnDetails.getContent();
                for(int taskId = 0; taskId < totalElements; taskId++){
                    executeTask(taskId, returnDetailList.get(taskId));
                }
            }
        }catch (Exception exception){
            log.error("[updateReturns] error occurred : {}",exception.getMessage());
            return false;
        }
        log.info("[updateReturns] successfully executed the rollback api");
        return true;
    }

    private void executeTask(int taskId, ReturnDetail returnDetail) {
        executorService.submit(() -> {
            log.info(Thread.currentThread().getName() + " is executing task: " + taskId);
            updateReturnOrderService(returnDetail.getId());
            log.info("Task " + taskId + " completed.");
        });
    }

    private void updateReturnHistory(ReturnDetail returnDetail, Integer returnId) {
        List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnEvents)){
            for(ReturnEvent returnEvent : returnEvents){
                ReturnHistory returnHistory = new ReturnHistory();
                returnHistory.setEntityId(returnDetail.getId());
                returnHistory.setEntityType("return_order");
                returnHistory.setCreatedAt(returnEvent.getCreatedAt());
                returnHistory.setCurrentStatus(returnEvent.getEvent().toLowerCase());
                returnHistory.setComment(returnEvent.getRemarks() != null ? returnEvent.getRemarks() : "");
                returnHistory.setAddedBy(0);
                orderOpsFeignClient.pushData(objectMapper.convertValue(returnHistory, Map.class), "reverse_history_inventory_queue");
                //kafkaService.pushToKafka("reverse_history_inventory_queue", String.valueOf(returnDetail.getId()), returnHistory);
            }
        }
    }

    private void updateReverseTrackingEvent(ReturnDetail returnDetail, Integer returnId) {
        List<ReverseDetailTrackingEvent> trackingEvents = reverseTrackingEventRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(trackingEvents)){
            for(ReverseDetailTrackingEvent reverseDetailTrackingEvent : trackingEvents){
                ReverseTrackingEvent reverseTrackingEvent = new ReverseTrackingEvent();
                reverseTrackingEvent.setReturnId(returnDetail.getId());
                reverseTrackingEvent.setReverseAwb(reverseDetailTrackingEvent.getReverseAwb());
                reverseTrackingEvent.setOrderId(reverseDetailTrackingEvent.getOrderId());
                reverseTrackingEvent.setNotificationEventId(reverseDetailTrackingEvent.getNotificationEventId());
                reverseTrackingEvent.setCreateDatetime(reverseDetailTrackingEvent.getCreateDatetime());
                reverseTrackingEvent.setEventDateTime(reverseDetailTrackingEvent.getEventDateTime());
                reverseTrackingEvent.setNonPickupReason(reverseDetailTrackingEvent.getNonPickupReason());
                reverseTrackingEvent.setPickupAttempted(reverseDetailTrackingEvent.getPickupAttempted());
                reverseTrackingEvent.setPickupDateTime(reverseDetailTrackingEvent.getPickupDateTime());
                reverseTrackingEvent.setReverseMappedStatus(reverseDetailTrackingEvent.getReverseMappedStatus());
                reverseTrackingEvent.setTrackingRemark(reverseDetailTrackingEvent.getTrackingRemark());
                reverseTrackingEvent.setTrackingStatusCode(reverseDetailTrackingEvent.getTrackingStatusCode());
                orderOpsFeignClient.pushData(objectMapper.convertValue(reverseTrackingEvent, Map.class), "reverse_tracking_event_inventory_queue");
                //kafkaService.pushToKafka("reverse_tracking_event_inventory_queue", String.valueOf(returnDetail.getId()), reverseTrackingEvent);
            }
        }
    }

    private void updateReturnRefundRule(ReturnDetail returnDetail, Integer returnId) {
        List<ReturnRefundRule> returnRefundRules = returnRefundRuleRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnRefundRules)){
            for(ReturnRefundRule returnRefundRule : returnRefundRules){
                ReturnRefundRule returnRefundRuleOld = new ReturnRefundRule();
                returnRefundRuleOld.setReturnId(returnDetail.getId());
                returnRefundRuleOld.setRuleId(returnRefundRule.getRuleId());
                returnRefundRuleOld.setUwItemId(returnRefundRule.getUwItemId());
                returnRefundRuleOld.setUpdatedAt(returnRefundRule.getUpdatedAt());
                returnRefundRuleOld.setCreatedAt(returnRefundRule.getCreatedAt());
                returnRefundRuleOld.setReturnRefundRuleResponse(returnRefundRule.getReturnRefundRuleResponse());
                returnRefundRuleOld.setDecisionValues(returnRefundRule.getDecisionValues());
                returnRefundRuleOld.setRuleCalledFrom(returnRefundRule.getRuleCalledFrom());
                returnRefundRuleOld.setTriggerPoint(returnRefundRule.getTriggerPoint());
                returnRefundRuleOld.setRequest(returnRefundRule.getRequest());
                orderOpsFeignClient.pushData(objectMapper.convertValue(returnRefundRuleOld, Map.class), "return_refund_rule_inventory_queue");
                //kafkaService.pushToKafka("return_refund_rule_inventory_queue", String.valueOf(returnDetail.getId()), returnRefundRuleOld);
            }
        }

    }

    private void updateReturnReason(ReturnDetail returnDetail, Integer returnId) {
        List<ReturnDetailReason> returnDetailReasons = returnReasonRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnDetailReasons)){
            for(ReturnDetailReason returnDetailReason : returnDetailReasons){
                ReturnReason returnReasonOld = new ReturnReason();
                returnReasonOld.setReturnId(returnDetail.getId());
                returnReasonOld.setReturnReasonId(returnDetailReason.getReturnReasonId());
                returnReasonOld.setSource(returnDetailReason.getSource());
                returnReasonOld.setSecondaryReason(returnDetailReason.getSecondaryReason());
                returnReasonOld.setOrderId(returnDetailReason.getOrderId());
                returnReasonOld.setType(returnDetailReason.getType());
                returnReasonOld.setUser(returnDetailReason.getUser());
                returnReasonOld.setPrimaryReason(returnDetailReason.getPrimaryReason());
                returnReasonOld.setUwItemId(returnDetailReason.getUwItemId());
                returnReasonOld.setCreatedAt(returnDetailReason.getCreatedAt() != null ? returnDetailReason.getCreatedAt() : new Date(System.currentTimeMillis()));
                orderOpsFeignClient.pushData(objectMapper.convertValue(returnReasonOld, Map.class), "return_reason_inventory_queue");
                //kafkaService.pushToKafka("return_reason_inventory_queue", String.valueOf(returnDetail.getId()), returnReasonOld);
            }
        }
    }

    private void updateReturnOrderAddress(ReturnDetail returnDetail, Integer returnId) {
        Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnId);
        Long groupId = returnDetailOptional.get().getGroupId();
        ReturnDetailAddressUpdate returnDetailAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(groupId);
        if(returnDetailAddressUpdate != null){
            ReturnOrderAddressUpdate returnOrderAddressUpdate = new ReturnOrderAddressUpdate();
            returnOrderAddressUpdate.setGroupId(Math.toIntExact(returnDetail.getGroupId()));
            returnOrderAddressUpdate.setCity(returnDetailAddressUpdate.getCity());
            returnOrderAddressUpdate.setEmail(returnDetailAddressUpdate.getEmail());
            returnOrderAddressUpdate.setCountry(returnDetailAddressUpdate.getCountry());
            returnOrderAddressUpdate.setCreatedAt(returnDetailAddressUpdate.getCreatedAt());
            returnOrderAddressUpdate.setFirstName(returnDetailAddressUpdate.getFirstName());
            returnOrderAddressUpdate.setLastName(returnDetailAddressUpdate.getLastName());
            returnOrderAddressUpdate.setIncrement_id(returnDetailAddressUpdate.getIncrement_id());
            returnOrderAddressUpdate.setPostcode(returnDetailAddressUpdate.getPostcode());
            returnOrderAddressUpdate.setState(returnDetailAddressUpdate.getState());
            returnOrderAddressUpdate.setStreet1(returnDetailAddressUpdate.getStreet1());
            returnOrderAddressUpdate.setStreet2(returnDetailAddressUpdate.getStreet2());
            returnOrderAddressUpdate.setTelephone(returnDetailAddressUpdate.getTelephone());
            orderOpsFeignClient.pushData(objectMapper.convertValue(returnOrderAddressUpdate, Map.class), "return_order_address_update_inventory_queue");
            //kafkaService.pushToKafka("return_order_address_update_inventory_queue", String.valueOf(returnDetail.getId()), returnOrderAddressUpdate);
        }
    }

    private void updateReturnOrderItem(ReturnDetail returnDetail, Integer returnId) {
        List<ReturnDetailItem> detailItemList = returnDetailItemRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(detailItemList)){
            for(ReturnDetailItem returnDetailItem : detailItemList){
                ReturnOrderItem returnOrderItem = new ReturnOrderItem();
                returnOrderItem.setUwItemId(returnDetailItem.getUwItemId());
                returnOrderItem.setReturnId(returnDetail.getId());
                returnOrderItem.setItemId(returnDetailItem.getItemId());
                returnOrderItem.setMethod(returnDetailItem.getMethod());

                returnOrderItem.setReturnType(returnDetail.getReturnType());

                returnOrderItem.setStatus(returnDetailItem.getStatus());
                returnOrderItem.setQcComment(returnDetailItem.getQcComment() != null ? returnDetailItem.getQcComment() : "");
                returnOrderItem.setRefundAmount(0);
                returnOrderItem.setIsFranchise(returnDetailItem.getIsFranchise());
                returnOrderItem.setProductDeliveryType(returnDetailItem.getProductDeliveryType());
                returnOrderItem.setIsFranchise(returnDetailItem.getIsFranchise());
                returnOrderItem.setClassification(returnDetailItem.getClassification());
                returnOrderItem.setChannel(returnDetailItem.getChannel());
                returnOrderItem.setCsohUpdatedFlag(returnDetailItem.getCsohUpdatedFlag());
                returnOrderItem.setItemSelectedFlag(returnDetailItem.getItemSelectedFlag());
                returnOrderItem.setTbybPrescription(returnDetailItem.getTbybPrescription());
                returnOrderItem.setReturnCreateDatetime(returnDetailItem.getReturnCreateDatetime());
                returnOrderItem.setReturnCreatedByUid(0);
                orderOpsFeignClient.pushData(objectMapper.convertValue(returnOrderItem, Map.class), "return_order_item_inventory_queue");
                //kafkaService.pushToKafka("return_order_item_inventory_queue", String.valueOf(returnDetail.getId()), returnOrderItem);
            }
        }
    }

    private ReturnDetail updateReturnOrder(Integer returnId) {
        Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnId);
        ReturnDetail returnDetail = null;
        if (returnDetailOptional.isPresent()) {
            returnDetail = returnDetailOptional.get();
            String facilityCode = returnDetail.getFacilityCode();
            Long groupId = returnDetail.getGroupId();
            Integer incrementId = returnDetail.getIncrementId();
            Boolean isAutoCancelEnable = returnDetail.getIsAutoCancelEnable() != null ? returnDetail.getIsAutoCancelEnable() : false;
            Boolean isInsurance = returnDetail.getIsInsurance() != null ? returnDetail.getIsInsurance() : false;
            Integer isQcAtDoorstep = returnDetail.getIsQcAtDoorstep();
            String receivingFlag = returnDetail.getReceivingFlag();
            Date returnCreateDatetime = null;
            Date lastFollowedupDate = null;
            try {
                returnCreateDatetime = returnDetail.getReturnCreateDatetime();
                lastFollowedupDate = returnDetail.getLastFollowupDatetime() != null ? returnDetail.getLastFollowupDatetime() : returnDetail.getReturnCreateDatetime();
            } catch (Exception exception) {
                log.error("[updateReturnOrderService] date parsing error : {}", exception.getMessage());
            }
            String returnMethod = returnDetail.getReturnMethod();
            String returnType = returnDetail.getReturnType();
            String source = returnDetail.getSource();
            String unicomOrderCode = returnDetail.getUnicomOrderCode();
            String bulkType = returnDetail.getBulkType() != null ? returnDetail.getBulkType() : "";
            Integer reversePuFollowupCnt = returnDetail.getReversePuFollowupCnt() != null ? returnDetail.getReversePuFollowupCnt() : 0;
            String agentEmail = returnDetail.getAgentEmail();
            String status = returnOrderActionService.getReturnOrderStatusById(returnId);

            ReturnOrder returnOrder = new ReturnOrder();
            returnOrder.setReturnId(returnId);
            returnOrder.setReturnMethod(returnMethod);
            returnOrder.setReturnType(returnType);
            returnOrder.setOrderNo(incrementId);
            returnOrder.setUnicomOrderCode(unicomOrderCode);
            returnOrder.setIsInsurance(isInsurance);
            returnOrder.setAutoCancelEnable(isAutoCancelEnable);
            returnOrder.setBulkType(bulkType);
            returnOrder.setGroupId(groupId);
            returnOrder.setReceivingFlag(receivingFlag);
            returnOrder.setAgentEmail(agentEmail);
            returnOrder.setStatus(status);
            returnOrder.setSource(source);
            returnOrder.setFacilityCode(facilityCode);
            returnOrder.setReturnCreateDatetime(returnCreateDatetime);
            returnOrder.setLastFollowupDatetime(lastFollowedupDate);
            returnOrder.setNextFollowupDatetime(lastFollowedupDate);
            returnOrder.setReversePuFollowupCnt(reversePuFollowupCnt);
            returnOrder.setIsQcAtDoorstep(isQcAtDoorstep);
            returnOrder.setCourierAmount(0d);
            returnOrder.setItemsRefundAmount(0d);
            returnOrder.setCourierRefundAmount(0d);
            returnOrder.setShippingRefundAmount(0d);
            returnOrder.setEmiRefundAmount(0d);
            returnOrder.setTotalRefundAmount(0d);
            returnOrder.setNewFlowFlag(1);
            returnOrder.setReturnLabelGenerated(false);
            returnOrder.setEasyRefundGiven("");
            returnOrder.setEasyRefundNotGivenReason("");
            returnOrder.setQcStatus("Pass");


            ReturnCourierDetail returnCourierDetail = returnCourierDetailRepository.findTopByReturnId(returnId);
            if (returnCourierDetail != null) {
                returnOrder.setReverseCourier(returnCourierDetail.getReverseCourier());
                returnOrder.setReverseAwb(returnCourierDetail.getReverseAwbNumber());
                returnOrder.setReversePickupReferenceId(returnCourierDetail.getReversePickupReferenceId());
            }

            //orderOpsFeignClient.pushData(objectMapper.convertValue(returnOrder, Map.class), "return_order_inventory_queue");
            //kafkaService.pushToKafka("return_order_inventory_queue", String.valueOf(returnId), returnOrder);
            ResponseEntity<ReturnOrder> responseEntity = reverseLogisticsConsumerFeignClient.copyReturn(returnOrder);
            log.info("[updateReturnOrderService][updateReturnOrder] responseEntity : {}", responseEntity);
            if(responseEntity.getStatusCode().is2xxSuccessful()){
                ReturnOrder responseEntityBody = responseEntity.getBody();
                if(responseEntityBody != null){
                    returnDetail.setId(responseEntityBody.getReturnId());
                    returnDetail.setGroupId(responseEntityBody.getGroupId());
                }
            }
        }
        return returnDetail;
    }

    private void updateDelightAction(ReturnDetail returnDetail, Integer returnId) {
        DelightAction delightAction = delightActionRepository.findTop1ByReturnIdOrderByIdDesc(returnId);
        if(delightAction != null){
            Map<String, Object> delightActionMap = objectMapper.convertValue(delightAction, Map.class);
            delightActionMap.put("returnId", returnDetail.getId());
            orderOpsFeignClient.pushData(delightActionMap, "delight_action_inventory_queue");
            //kafkaService.pushToKafka("delight_action_inventory_queue", String.valueOf(returnDetail.getId()), delightAction);
        }
    }

    private void updateReturnOrderSaleDetails(ReturnDetail returnDetail, Integer returnId) {
        ReturnDetailSaleDetails returnDetailSaleDetails = returnOrderSaleDetailsRepository.findByReturnId(returnId);
        if(returnDetailSaleDetails != null){
            Map<String, Object> returnSaleDetailsMap = objectMapper.convertValue(returnDetailSaleDetails, Map.class);
            returnSaleDetailsMap.put("returnId", returnDetail.getId());
            orderOpsFeignClient.pushData(returnSaleDetailsMap, "return_order_sale_details_inventory_queue");
            //kafkaService.pushToKafka("return_order_sale_details_inventory_queue", String.valueOf(returnDetail.getId()), returnDetailSaleDetails);
        }
    }

    private void updateNprCommunicationHistory(ReturnDetail returnDetail, Integer returnId) {
        List<NprCommunicationHistory> nprCommunicationHistory = nprCommunicationHistoryRepository.findByReturnId(returnId);
        if(CollectionUtils.isNotEmpty(nprCommunicationHistory)){
            for (NprCommunicationHistory communicationHistory : nprCommunicationHistory) {
                orderOpsFeignClient.pushData(objectMapper.convertValue(communicationHistory, Map.class), "npr_communication_history_inventory_queue");
                //kafkaService.pushToKafka("npr_communication_history_inventory_queue", String.valueOf(returnDetail.getId()), communicationHistory);
            }
        }
    }
}
