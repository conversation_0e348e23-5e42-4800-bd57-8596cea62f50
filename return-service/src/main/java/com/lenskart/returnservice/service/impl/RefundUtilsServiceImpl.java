package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.request.RefundDispatchRequest;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.response.CheckRefundSwitchActiveDTO;
import com.lenskart.ordermetadata.dto.response.RefundDispatchResponse;
import com.lenskart.orderops.model.RefundIntentRequest;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.enums.TriggerPoint;
import com.lenskart.returncommon.model.request.InitiateExchangeOrderRefundRequest;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.ordermetadata.dto.response.RefundIntentRequestDTO;
import com.lenskart.refund.client.model.dto.PGSpecificRefundDetailsDTO;
import com.lenskart.refund.client.model.dto.RefundAmountDetailsDTO;
import com.lenskart.refund.client.model.dto.RefundRequestDTO;
import com.lenskart.refund.client.model.dto.SpecificRefundDetailsDTO;
import com.lenskart.refund.client.model.enums.*;
import com.lenskart.refund.client.model.kafka.CheckRefundDispatchPointKafkaMessage;
import com.lenskart.refund.client.model.request.*;
import com.lenskart.refund.client.model.response.*;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnrepository.repository.ReturnRefundRuleRepository;
import com.lenskart.returnservice.cache.ReturnStatusTimelineMappingCache;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.RefundFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeComparator;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.ITEM_WISE_PRICES_DTO;
import static com.lenskart.returncommon.model.dto.ExchangedItem.CALLING_POINT.BACKWARD_FLOW;
import static com.lenskart.returncommon.utils.Constant.B2B_NAV_CHANNEL.B2B_NAV_CHANNELS;
import static com.lenskart.returncommon.utils.Constant.EVENT.REFUND_TRIGGERED;
import static com.lenskart.returncommon.utils.Constant.REFUND_QUEUE_CONSTANTS.REFUND_CREATE_STRATEGY;
import static com.lenskart.returncommon.utils.Constant.REFUND_QUEUE_CONSTANTS.REFUND_PROCESS_TOPIC;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.RETURN_ACCEPTED;
import static com.lenskart.returncommon.utils.Constant.RETURN_STATUS.RETURN_RECEIVED_ACTION_PENDING;
import static com.lenskart.returncommon.utils.Constant.STATUS.AWB_ASSIGNED;
import static com.lenskart.returncommon.utils.Constant.STATUS.RETURN_RECEIVED;

@Slf4j
@Service
public class RefundUtilsServiceImpl implements IRefundUtilsService {

    @Autowired
    RefundFeignClient refundFeignClient;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    IRefundAuthorizationService authorizationService;

    @Autowired
    IRefundMethodLabelService refundMethodLabelService;

    @Autowired
    ReturnStatusTimelineMappingCache returnStatusTimelineMappingCache;

    @Autowired
    OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    OrderAdaptorFeignClient orderAdaptorFeignClient;

    @Autowired
    private IRefundAuthorizationService refundAuthorizationService;

    @Value("${refund.service.baseurl}")
    private String baseUrlRefundService;

    @Autowired
    private ReturnUtil returnUtil;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IKafkaService kafkaService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    static Gson gson = new GsonBuilder().setPrettyPrinting().create();

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private ReturnRefundRuleRepository returnRefundRuleRepository;

    @Autowired
    private IExchangeItemDispatchService exchangeItemDispatchService;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Override
    public GetRefundAmountResponse getOrderRefundAmount(Integer incrementId) {
        GetRefundAmountResponse response = new GetRefundAmountResponse();
        RefundAmountDetailsDTO refundAmountDetailsNew = null;
        try {
            log.info("[RefundServiceImpl][getOrderRefundAmount] refund details for increment-id:{}", incrementId);
            GetRefundAmountRequest getRefundAmountRequest = new GetRefundAmountRequest();
            getRefundAmountRequest.setOrderId(incrementId.toString());
            getRefundAmountRequest.setIdentifierType(IdentifierType.ORDER_ID);
            getRefundAmountRequest.setIdentifierValues(Collections.singletonList(incrementId.toString()));

            ResponseEntity<GetRefundAmountResponse> responseEntity = refundFeignClient.getRefundAmountByOrderId(getRefundAmountRequest);
            if (responseEntity.getStatusCode().is2xxSuccessful() && Objects.nonNull(responseEntity.getBody())) {
                log.info("[RefundServiceImpl][getOrderRefundAmount] Got Response from refund service for incrementId: {} with body: {}", incrementId, responseEntity.getBody());
                refundAmountDetailsNew = responseEntity.getBody().getRefundDetailsDTO();
            } else {
                log.info("[RefundServiceImpl][getOrderRefundAmount] Got unexpected Response from refund service for incrementId: {} with responseEntity: {}", incrementId, responseEntity);
            }
        } catch (Exception e) {
            log.error("[RefundServiceImpl][getOrderRefundAmount] Exception:{}", e.getMessage(), e);
        }
        response.setRefundDetailsDTO(refundAmountDetailsNew);
        return response;
    }

    @Override
    public String getRefundRequestStatus(Integer uwItemId) {
        log.info("[RefundUtilsServiceImpl][getRefundRequestStatus] refund status for uw_item_id:{}", uwItemId);
        CheckRefundInitiatedRequest request = new CheckRefundInitiatedRequest();
        request.setIdentifierType(IdentifierType.UW_ITEM_ID);
        request.setIdentifierValue(String.valueOf(uwItemId));
        String refundRequestStatus = null;
        Map<String, Object> headers = authorizationService.addRefundAuthorizationHeaders(request, new HashMap<>());
        ResponseEntity<CheckRefundInitiatedResponse> refundRequestInitiatedResponse = refundFeignClient.checkRefundInitiated(request, headers);
        CheckRefundInitiatedResponse checkRefundInitiatedResponse = refundRequestInitiatedResponse.getBody();
        return checkRefundInitiatedResponse != null && checkRefundInitiatedResponse.getRefundRequestDTO() != null && checkRefundInitiatedResponse.getRefundRequestDTO().getStatus() != null ? checkRefundInitiatedResponse.getRefundRequestDTO().getStatus().getValue() : null;
    }

    @Override
    public RefundIntentDTO getRefundMethodIntentByCustomer(Integer returnId) {
        CheckRefundInitiatedRequest checkRefundInitiatedRequest=new CheckRefundInitiatedRequest();
        checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(returnId));
        checkRefundInitiatedRequest.setIdentifierType(IdentifierType.RETURN_ID);
        Map<String, Object> headers = authorizationService.addRefundAuthorizationHeaders(checkRefundInitiatedRequest, new HashMap<>());
        CheckRefundInitiatedResponse checkRefundInitiatedResponse=refundFeignClient.checkRefundInitiated(checkRefundInitiatedRequest, headers).getBody();
        if(Objects.nonNull(checkRefundInitiatedResponse) && Objects.nonNull(checkRefundInitiatedResponse.getRefundRequestDTO()) && Objects.nonNull(checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget()))
            return RefundIntentDTO.builder()
                    .refundIntent(checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget().getCode())
                    .createdAt(checkRefundInitiatedResponse.getRefundRequestDTO().getCreatedAt())
                    .updatedAt(checkRefundInitiatedResponse.getRefundRequestDTO().getUpdatedAt())
                    .build();
        return null;
    }

    @Override
    public Boolean isPurelyCODItem(ReturnRefundInputDTO inputDTO) {

        log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: looking up increment id for uw_item_id:: " + inputDTO.getUwItemId());
        UwOrderDTO uwOrderDTO = inputDTO.getRrrServiceRequest().getUwOrder();
        if (uwOrderDTO != null && uwOrderDTO.getIncrementId() != 0) {

            log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: looking up payment method for uw_item_id:: " + uwOrderDTO.getUwItemId() + " :: increment_id:: " + uwOrderDTO.getIncrementId());
            List<String> methods =
                    inputDTO.getRrrServiceRequest()
                            .getOrderInfoResponse()
                            .getOrders()
                            .stream()
                            .map(OrdersDTO::getMethod)
                            .toList();

            if (!CollectionUtils.isEmpty(methods) && hasCashOnDeliveryMethod(methods)) {

                boolean isB2BOrder = getIsB2BOrder(uwOrderDTO);
                if (isB2BOrder) {
                    log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: B2B order. Hence overriding uw_order by:: " + uwOrderDTO.getB2bRefrenceItemId());
                    uwOrderDTO = inputDTO.getRrrServiceRequest().getUwOrderWH();
                }

                log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: looking up item_wise_price_details row for uw_item_id:: " + uwOrderDTO.getUwItemId());

                final UwOrderDTO uwOrderDTOWH = uwOrderDTO;
                ItemWisePriceDetailsDTO itemWisePriceDetailsDTO = inputDTO.getRrrServiceRequest()
                        .getOrderInfoResponse()
                        .getItemWisePrices()
                        .stream()
                        .filter(item -> item.getItemId() == uwOrderDTOWH.getItemId())
                        .findFirst()
                        .orElse(null);

                log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: item_wise_price_details row retrieved for uw_item_id:: " + uwOrderDTOWH.getUwItemId() + " as " + gson.toJson(itemWisePriceDetailsDTO));
                if (null != itemWisePriceDetailsDTO) {
                    if (((itemWisePriceDetailsDTO.getStoreCreditDiscount() != null && itemWisePriceDetailsDTO.getStoreCreditDiscount().compareTo(0.00) > 0)
                            || (itemWisePriceDetailsDTO.getLenskartDiscount() != null && itemWisePriceDetailsDTO.getLenskartDiscount().compareTo(0.00) > 0)
                            || (itemWisePriceDetailsDTO.getLenskartPlusDiscount() != null) && itemWisePriceDetailsDTO.getLenskartPlusDiscount().compareTo(0.00) > 0)) {
                        log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: Not a pure COD Item uw_item_id:: " + uwOrderDTOWH.getUwItemId());
                        return false;
                    } else {
                        log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: Its a pure COD Item uw_item_id:: " + uwOrderDTOWH.getUwItemId());
                        return true;
                    }
                }

            }
        }
        log.info("[RefundUtilsServiceImpl][isPurelyCODItem]:: Not a pure COD Item uw_item_id:: " + inputDTO.getUwItemId());
        return false;
    }

    @Override
    public CheckRefundInitiatedResponse getRefundAmount(CheckRefundInitiatedRequest checkRefundInitiatedRequest) {
        return null;
    }

    private boolean getIsB2BOrder(UwOrderDTO uwOrderDTO) {
        return uwOrderDTO.getNavChannel() != null && B2B_NAV_CHANNELS.contains(uwOrderDTO.getNavChannel());
    }

    private boolean hasCashOnDeliveryMethod(List<String> paymentMethods) {
        List<String> CASH_ON_DELIVERY_METHODS = Arrays.asList(Constant.PAYMENT_METHOD.CASHONDELIVERY, Constant.PAYMENT_METHOD.COD);
        for (String method : paymentMethods) {
            if (CASH_ON_DELIVERY_METHODS.contains(method)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Boolean initiateExchangeOrderRefund(Integer exchangeIncrementId, Integer masterOrderId) {
        log.info("[initiateExchangeOrderRefund] exchangeIncrementId {}",exchangeIncrementId);
        try{
            InitiateExchangeOrderRefundRequest request = new InitiateExchangeOrderRefundRequest();
            request.setExchangeOrderId(Long.valueOf(exchangeIncrementId));
            request.setMasterOrderId(Long.valueOf(masterOrderId));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Application");
            headers = refundAuthorizationService.addRefundAuthorizationHeaders(request, headers);
            HttpEntity<InitiateExchangeOrderRefundRequest> httpEntity = new HttpEntity<>(request, headers);

            String url=baseUrlRefundService+"/v1.0/initiate-exchange-order-refund";
            ResponseEntity<Boolean> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Boolean.class);
            log.info("[initiateExchangeOrderRefund] response {}",responseEntity);
        }
        catch (Exception e){
            log.error("initiateExchangeOrderRefund Exception:{}",e.getMessage());
        }
        return true;
    }

    @Override
    public CheckRefundInitiatedResponse isRefundInitiated(CheckRefundInitiatedRequest checkRefundInitiatedRequest) {
        log.info("[isRefundInitiated] identifierType : {} , identifierValue : {}", checkRefundInitiatedRequest.getIdentifierType(), checkRefundInitiatedRequest.getIdentifierValue());
        Map<String, Object> headers = authorizationService.addRefundAuthorizationHeaders(checkRefundInitiatedRequest, new HashMap<>());
        ResponseEntity<CheckRefundInitiatedResponse> refundRequestInitiatedResponse = refundFeignClient.checkRefundInitiated(checkRefundInitiatedRequest, headers);
        log.info("[isRefundInitiated] identifierType : {} , identifierValue : {}, refundRequestInitiatedResponse : {}", checkRefundInitiatedRequest.getIdentifierType(), checkRefundInitiatedRequest.getIdentifierValue(), refundRequestInitiatedResponse);
        return refundRequestInitiatedResponse.getBody();
    }

    @Override
    public KafkaProducerResponse pushToCheckDispatchPointQueue(CheckRefundDispatchPointKafkaMessage checkRefundDispatchPointKafkaMessage) {
        ResponseEntity<KafkaProducerResponse> kafkaProducerResponseResponseEntity = apiCallToPushToRefundDispatchPoint(checkRefundDispatchPointKafkaMessage);
        log.info("[pushToCheckDispatchPointQueue] kafkaProducerResponseResponseEntity : {}", kafkaProducerResponseResponseEntity);
        return null;
    }

    private ResponseEntity<KafkaProducerResponse> apiCallToPushToRefundDispatchPoint(CheckRefundDispatchPointKafkaMessage kafkaMessage)
    {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "Application");
        headers = refundAuthorizationService.addRefundAuthorizationHeaders(kafkaMessage, headers);
        HttpEntity<CheckRefundDispatchPointKafkaMessage> httpEntity = new HttpEntity<>(kafkaMessage, headers);

        String url=baseUrlRefundService+"/v1.0/push-to-topic/"+EventType.CHECK_REFUND_DISPATCH_POINT.getTopic()+"/"+ kafkaMessage.getPartitionKey();
        log.info("checkRefundDispatchPoint url:{}",url);
        return restTemplate.exchange(url, HttpMethod.POST, httpEntity, KafkaProducerResponse.class);
    }

    @Override
    public GetMethodWiseRefundDetailsResponse getMethodWiseRefundDetails(GetRefundAmountRequest request) {
        return refundFeignClient.getMethodWiseRefundDetails(request).getBody();
    }

    @Override
    public void getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse getMethodWiseRefundDetailsResponse, ReturnDetailsResponse response) {

        List<RefundDetails> refundDetails = new ArrayList<>();
        List<OrderRefundDetails> refundTotalTypeWise = new ArrayList<>();
        BigDecimal totalItemLevelRefundedAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_DOWN);
        BigDecimal returnCancelRefundedAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_DOWN);
        Long totalRefundCompleteDate = null;
        if (Objects.nonNull(getMethodWiseRefundDetailsResponse) && Objects.nonNull(getMethodWiseRefundDetailsResponse.getMethodSpecificRefundDetailsDTOMap())) {
            totalRefundCompleteDate = 0L;
            for (Map.Entry<RefundMethod, SpecificRefundDetailsDTO> methodSpecificRefundDetailsDTOEntry : getMethodWiseRefundDetailsResponse.getMethodSpecificRefundDetailsDTOMap().entrySet()) {
                if (Objects.nonNull(methodSpecificRefundDetailsDTOEntry.getValue())) {
                    returnCancelRefundedAmount = returnCancelRefundedAmount.add(methodSpecificRefundDetailsDTOEntry.getValue().getTotalRefundedAmount().getAmount());
                    OrderRefundDetails orderRefundDetails = new OrderRefundDetails();
                    orderRefundDetails.setRefundMethod(methodSpecificRefundDetailsDTOEntry.getKey().getCode());
                    orderRefundDetails.setRefundAmount(methodSpecificRefundDetailsDTOEntry.getValue().getTotalRefundedAmount().getAmount().doubleValue());
                    refundTotalTypeWise.add(orderRefundDetails);
                    List<PGSpecificRefundDetailsDTO> refundDetailsList = methodSpecificRefundDetailsDTOEntry.getValue().getRefundDetailsList();
                    if (!CollectionUtils.isEmpty(refundDetailsList)) {
                        for (PGSpecificRefundDetailsDTO pgSpecificRefundDetailsDTO : refundDetailsList) {
                            RefundDetails refundDetail = new RefundDetails();
                            refundDetail.setRefundId(String.valueOf(pgSpecificRefundDetailsDTO.getRefundId()));
                            refundDetail.setRefundMethod(methodSpecificRefundDetailsDTOEntry.getKey().getCode());
                            refundDetail.setRefundAmount(pgSpecificRefundDetailsDTO.getRefundAmount().getAmount().doubleValue());
                            refundDetail.setRefundMethodLabel(refundMethodLabelService.getRefundMethodLabelByRefundMethod("source".equalsIgnoreCase(refundDetail.getRefundMethod()) ? "online" : refundDetail.getRefundMethod()));
                            refundDetail.setDate(pgSpecificRefundDetailsDTO.getCreatedAt());
                            refundDetail.setStatus(pgSpecificRefundDetailsDTO.getStatus());
                            refundDetail.setStoreCreditCode(pgSpecificRefundDetailsDTO.getCode());
                            refundDetail.setBankReferenceNumber(pgSpecificRefundDetailsDTO.getArn());
                            refundDetail.setCashFreeLink(pgSpecificRefundDetailsDTO.getPayoutLink());
                            if (methodSpecificRefundDetailsDTOEntry.getKey().getCode().equalsIgnoreCase(RefundMethod.LKCASH.getCode())) {
                                refundDetail.setLkCashRefunded(pgSpecificRefundDetailsDTO.getRefundAmount().getAmount().doubleValue());
                            } else if (methodSpecificRefundDetailsDTOEntry.getKey().getCode().equalsIgnoreCase(RefundMethod.LKCASHPLUS.getCode())) {
                                refundDetail.setLkCashPlusRefunded(pgSpecificRefundDetailsDTO.getRefundAmount().getAmount().doubleValue());
                            }
                            refundDetail.setRefundMethodSubLabel(null!=refundDetail.getRefundMethod() ? refundMethodLabelService.getRefundMethodSubLabelByRefundMethod(refundDetail.getRefundMethod(),refundDetail) : null);
                            updateRefundHistoryV2(pgSpecificRefundDetailsDTO, refundDetail);
                            totalRefundCompleteDate = Math.max(totalRefundCompleteDate , null!=pgSpecificRefundDetailsDTO.getUpdatedAt() ? pgSpecificRefundDetailsDTO.getUpdatedAt().getTime() : 0L);
                            refundDetails.add(refundDetail);
                            if(pgSpecificRefundDetailsDTO.getUwItemIdToRefundedAmountMap()!=null) {
                                BigDecimal itemLevelRefundedAmount = pgSpecificRefundDetailsDTO.getUwItemIdToRefundedAmountMap().keySet().stream()
                                        .filter(uw -> uw != null && response.getUwItemId() == uw.intValue())
                                        .map(uw -> pgSpecificRefundDetailsDTO.getUwItemIdToRefundedAmountMap().get(uw).getAmount())
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                                totalItemLevelRefundedAmount = totalItemLevelRefundedAmount.add(itemLevelRefundedAmount);
                            }
                        }
                    }
                }
            }
        }

        response.setTotalRefundAmount(totalItemLevelRefundedAmount.doubleValue());
        response.setReturnCancelRefundedAmount(returnCancelRefundedAmount.doubleValue());
        response.setTotalRefundCompleteDate(totalRefundCompleteDate);
        response.setRefundDetails(refundDetails);
        response.setRefundTotalTypeWise(refundTotalTypeWise);
    }

    @Override
    public boolean isAlreadyRefunded(int incrementId, Integer uwItemId) {
        boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
        ResponseEntity<OrderInfoResponseDTO> orderInfoResponseDTO = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(ITEM_WISE_PRICES_DTO))
                : orderOpsFeignClient.getOrderDetails(incrementId);
        ItemWiseFastRefunResponseDTO itemWiseFastRefundResponse = orderInfoResponseDTO.getBody().getItemWiseFastRefunResponseDTO();
        if (itemWiseFastRefundResponse != null && !CollectionUtils.isEmpty(itemWiseFastRefundResponse.getItemIds())) {
            for (ItemWiseGetAmountDTO itemWiseGetAmount : itemWiseFastRefundResponse.getItemIds()) {
                if (itemWiseGetAmount != null && itemWiseGetAmount.getUwItemId().equals(uwItemId) && itemWiseGetAmount.getRefundAmount() > 0.0d) {
                    log.info("[ReturnServiceImpl][getReturnCancellabilityForItem]: Partial or complete refund has already been given for this item's return");
                    return true;
                }
            }
        }
        return false;
    }

    private void updateRefundHistoryV2(PGSpecificRefundDetailsDTO refundDetails, RefundDetails refundDetail) {
        List<RefundStatusHistory> refundStatusHistoryList = new ArrayList<>();
        if (STATUS.REFUND_COMPLETE.equalsIgnoreCase(refundDetails.getStatus()) || STATUS.REFUND_CANCELLED.equalsIgnoreCase(refundDetails.getStatus()) || STATUS.REFUND_ARN_RECEIVED.equalsIgnoreCase(refundDetails.getStatus())) {
            RefundStatusHistory refundStatusHistorPendingStatus = new RefundStatusHistory();
            refundStatusHistorPendingStatus.setDate(refundDetails.getCreatedAt());
            refundStatusHistorPendingStatus.setRefundStatus(STATUS.REFUND_PENDING);
            RefundStatusHistory refundStatusHistory = new RefundStatusHistory();
            Date updatedDate = refundDetails.getUpdatedAt();
            Date createdDate = refundDetails.getCreatedAt();
            refundStatusHistory.setDate(DateTimeComparator.getInstance().compare(updatedDate, createdDate) > 0 ? updatedDate : createdDate);
            refundStatusHistory.setRefundStatus(refundDetails.getStatus());
            refundStatusHistoryList.add(refundStatusHistorPendingStatus);
            refundStatusHistoryList.add(refundStatusHistory);
        } else if (STATUS.REFUND_PENDING_PG.equalsIgnoreCase(refundDetails.getStatus())) {
            RefundStatusHistory refundStatusHistorPendingStatus = new RefundStatusHistory();
            refundStatusHistorPendingStatus.setDate(refundDetails.getCreatedAt());
            refundStatusHistorPendingStatus.setRefundStatus(STATUS.REFUND_PENDING);
            refundStatusHistorPendingStatus.setComment("PAYMENT_PENDING");
            refundStatusHistoryList.add(refundStatusHistorPendingStatus);
        } else if ("imps_unknown".equalsIgnoreCase(refundDetails.getStatus())) {
            RefundStatusHistory refundStatusHistorPendingStatus = new RefundStatusHistory();
            refundStatusHistorPendingStatus.setDate(refundDetails.getCreatedAt());
            refundStatusHistorPendingStatus.setRefundStatus("imps_unknown");
            refundStatusHistorPendingStatus.setComment("imps_unknown");
            ReturnStatusTimelineMapping returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus("imps_unknown");
            if (null != returnStatusTimelineMapping) {
                refundStatusHistorPendingStatus.setComment(returnStatusTimelineMapping.getTimelineSubheaderActual());
            }
            refundStatusHistoryList.add(refundStatusHistorPendingStatus);
        } else {
            RefundStatusHistory refundStatusHistorPendingStatus = new RefundStatusHistory();
            refundStatusHistorPendingStatus.setDate(refundDetails.getCreatedAt());
            refundStatusHistorPendingStatus.setRefundStatus(refundDetails.getStatus());
            refundStatusHistorPendingStatus.setComment(refundDetails.getStatus());
            refundStatusHistoryList.add(refundStatusHistorPendingStatus);
        }
        refundDetail.setRefundStatusHistoryList(refundStatusHistoryList);
    }

    public interface STATUS {
        String REFUND_PENDING = "refund_pending";
        String REFUND_CHEQUE_CREATED = "refund_cheque_created";
        String AWAITING_NEFT_INFO = "awaiting_neft_info";
        String REFUND_COMPLETE = "refund_complete";
        String REFUND_PENDING_PG = "refund_pending_pg";
        String REFUND_CANCELLED = "refund_cancelled";
        String REFUND_REJECT = "refund_reject";
        String REFUND_PENDING_PG_ITR = "refund_pending_pg_itr";
        String REFUND_ARN_RECEIVED = "refund_arn_received";
    }

    @Override
    public KafkaProducerResponse rejectRefundRequest(RefundRejectRequest request) {
        Map<String, Object> headers = authorizationService.addRefundAuthorizationHeaders(request, new HashMap<>());
        ResponseEntity<KafkaProducerResponse> response = refundFeignClient.rejectRefundRequest(request, headers);
        log.info("[rejectRefundRequest] response is " + response);
        return (null != response ? response.getBody() : null);
    }

    @Override
    public boolean refundServiceSwitch(String identifierType, String identifierValue) {
        log.info("[refundServiceSwitch] identifierType : {}, identifierValue : {}", identifierType, identifierValue);
        ResponseEntity<CheckRefundSwitchActiveDTO> checkRefundSwitchActiveDTOResponseEntity = orderOpsFeignClient.checkRefundSwitchActive(identifierType, identifierValue);
        log.info("[refundServiceSwitch] identifierValue : {}, checkRefundSwitchActiveDTOResponseEntity : {}", identifierValue, checkRefundSwitchActiveDTOResponseEntity);
        if (checkRefundSwitchActiveDTOResponseEntity.getStatusCode().is2xxSuccessful()) {
            return checkRefundSwitchActiveDTOResponseEntity.getBody() != null && checkRefundSwitchActiveDTOResponseEntity.getBody().isNewRefundFlow();
        }
        return false;
    }

    @Override
    public void initiateRefundAsynchronously(Integer magentoItemId, Integer uwItemId, Integer returnId, String triggerPoint) {

    }

    @Override
    public boolean initiateRefundRequest(CreateRefundRequest payload) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Application");

            headers = refundAuthorizationService.addRefundAuthorizationHeaders(payload, headers);

            HttpEntity<CreateRefundRequest> httpEntity = new HttpEntity<>(payload, headers);
            String url=baseUrlRefundService+"/v1.0/refund-request";
            log.info("[RefundUtilServiceImpl][initiateRefundRequest] Calling refund service for refund-request creation, url:{}, payload:{}",url,payload);
            ResponseEntity<Map> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Map.class);
            log.info("[RefundUtilServiceImpl][initiateRefundRequest] Response from create-refund api:"+responseEntity.getBody());
            if(responseEntity.getStatusCode().is2xxSuccessful()){
                Map<String,Object> response=responseEntity.getBody();
                if(Objects.nonNull(response) && (boolean)response.get("success")){
                    log.info("[RefundUtilServiceImpl][initiateRefundRequest] Refund request created for identifier_type:{}, identifier_value:{}",payload.getIdentifierType(),payload.getIdentifierValue());
                    return true;
                } else {
                    log.info("[RefundUtilServiceImpl][initiateRefundRequest] Refund request creation failed for identifier_type:{}, identifier_value:{}",payload.getIdentifierType(),payload.getIdentifierValue());
                }
            }
        } catch (Exception e){
            log.info("[RefundUtilServiceImpl][initiateRefundRequest] Exception:"+e);
        }
        return false;
    }

    @Override
    public RefundMethodResponse getRefundMethod(String refundInitiatedAt,String paymentMode,String paymentMethod,String country,Integer incrementId){
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Application");

            RefundMethodRequest refundMethodRequest=new RefundMethodRequest(refundInitiatedAt,paymentMode,paymentMethod,country);

            HttpEntity<RefundMethodRequest> httpEntity = new HttpEntity<>(refundMethodRequest, headers);
            String url=baseUrlRefundService+"/v1.0/get-refund-method";
            log.info("[RefundUtilServiceImpl][getRefundMethod] increment_id:{}, url:{}, payload:{}",incrementId,url,refundMethodRequest);
            ResponseEntity<RefundMethodResponse> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, RefundMethodResponse.class);
            if(responseEntity.getStatusCode().is2xxSuccessful() && Objects.nonNull(responseEntity.getBody())){
                if(responseEntity.getBody().isSuccess()){
                    log.info("[RefundUtilServiceImpl][getRefundMethod] increment_id:{}, refund_method:{}",incrementId,responseEntity.getBody());
                    return responseEntity.getBody();
                } else {
                    log.info("[RefundUtilServiceImpl][getRefundMethod] Failed to fetch refund method, increment_id:{}",incrementId);
                    return null;
                }
            }
        } catch (Exception e){
            log.error("[RefundUtilServiceImpl][getRefundMethod] increment_id:{}, Exception:{}",incrementId,e.getMessage());
        }
        return null;
    }

    @Override
    public boolean getIsFranchiseRefundRequired(Integer incrementId, RefundTriggerPoint refundTriggerPoint, String source, RefundReason refundReason) {
        boolean isFranchiseRefundRequired = false;
        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        refundRequestDTO.setOrderId(Long.valueOf(incrementId));
        refundRequestDTO.setRefundTriggerPoint(refundTriggerPoint.getName());
        refundRequestDTO.setSource(source);
        refundRequestDTO.setRefundReason(refundReason);
        ResponseEntity<Boolean> franchiseRefundRequiredResponse = orderOpsFeignClient.checkFranchiseRefundRequired(refundRequestDTO);
        log.info("[RefundUtilServiceImpl][getIsFranchiseRefundRequired] franchiseRefundRequiredResponse : {}", franchiseRefundRequiredResponse);
        if(franchiseRefundRequiredResponse.getStatusCode().is2xxSuccessful()){
            isFranchiseRefundRequired = franchiseRefundRequiredResponse.getBody() != null && franchiseRefundRequiredResponse.getBody();
        }
        return isFranchiseRefundRequired;
    }

    @Override
    public void initiateRefund(RefundProcessDTO refundProcessDTO) {
        boolean isNewFlow = refundServiceSwitch(Constant.IDENTIFIER_TYPE.ORDER_ID, String.valueOf(refundProcessDTO.getIncrementId()));
        for (Returns returns : refundProcessDTO.getReturnCreationResponse().getResult().getReturns()) {

            ExchangedItem exchangedItem = new ExchangedItem();
            exchangedItem.setCallingPoint(BACKWARD_FLOW);
            exchangedItem.setUwItemId(returns.getUwItemId());
            exchangedItem.setReturnId(returns.getReturnId());

            boolean isItemDispatchable = isExchangeItemDispatchable(exchangedItem);
            if (!isNewFlow && !isItemDispatchable) {
                boolean isRefundDispatchable = isRefundDispatchable(exchangedItem);
                ReturnItemDTO returnItem = refundProcessDTO.getMagentoItemToReturnItemMap().get(returns.getMagentoItemId());
                if (returnItem == null) continue;
                if (!"exchange".equalsIgnoreCase(returnItem.getRefundMethodRequest()) && returnItem.getDoRefund() && !returnItem.getNeedApproval()) {
                    if (isRefundDispatchable) {
                        ResponseEntity<RefundIntentRequestDTO> refundIntentRequestDTOResponseEntity = orderOpsFeignClient.getRefundIntentRequest(returns.getUwItemId(), returns.getReturnId());
                        if (refundIntentRequestDTOResponseEntity.getStatusCode().is2xxSuccessful()) {
                            // refundInitiateAsyncService.initiateRefundAsynchronously(returns.getMagentoItemId(), returns.getUwItemId(), returns.getReturnId(), TriggerPoint.ReturnInitiation.getName());
                            if (refundIntentRequestDTOResponseEntity.getBody() == null) {
                                return;
                            }
                            RefundIntentRequestDTO refundIntentRequestDTO = refundIntentRequestDTOResponseEntity.getBody();
                            UwOrderDTO uwOrderDTO = refundProcessDTO.getUwOrderDTOList()
                                    .stream()
                                    .filter(uw -> Objects.equals(uw.getUwItemId(), returns.getUwItemId())).findAny()
                                    .orElse(null);
                            if (uwOrderDTO == null) {
                                return;
                            }
                            OrdersDTO ordersDTO = refundProcessDTO.getOrdersDTOList()
                                    .stream()
                                    .filter(order -> order.getItemId() == uwOrderDTO.getItemId()).findFirst()
                                    .orElse(null);
                            if (ordersDTO == null) {
                                return;
                            }
                            RefundCreateRequest refundCreateRequest = new RefundCreateRequest();
                            refundCreateRequest.setRefundMethod(refundIntentRequestDTO.getRefundIntentMethod());
                            refundCreateRequest.setMagentoItemId(ordersDTO.getMagentoItemId());
                            refundCreateRequest.setLkCountry(returnUtil.findLkCountry(refundProcessDTO.getOrdersHeaderDTO()));

                            RefundKafkaRequest refundKafkaRequest = new RefundKafkaRequest();
                            refundKafkaRequest.setPartitionKey(uwOrderDTO.getUwItemId());
                            refundKafkaRequest.setRefundStrategy(REFUND_CREATE_STRATEGY);
                            refundKafkaRequest.setRetryCount(0);
                            refundKafkaRequest.setRefundCreateRequest(refundCreateRequest);
                            try {
                                returnEventService.createReturnEvent(refundProcessDTO.getRequestId(), refundProcessDTO.getReturnId(), REFUND_TRIGGERED, "");
                                kafkaService.pushToKafka(REFUND_PROCESS_TOPIC, String.valueOf(refundKafkaRequest.getPartitionKey()), objectMapper.writeValueAsString(refundKafkaRequest));
                            } catch (Exception exception) {
                                log.error("[initiateRefund] exception : " + exception);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public boolean isFranchiseRefundEligible(String navChannel, RefundTriggerPoint refundTriggerPoint) throws Exception {
        log.info("[isFranchiseRefundEligible] refundTriggerPoint={}",refundTriggerPoint);
        List<String> storeRefundNavChannelList = systemPreferenceService.getValuesAsList("store_refund","nav_channels");
        if(!RefundTriggerPoint.COURIER_PICKUP.equals(refundTriggerPoint)){
            return false;
        }
        //checking from DB/Cache for uw_orders.nav_channels i.e COCOB2B,COCOOTC,FOFOB2B,FOFOOTC
        if(navChannel == null || navChannel.length() == 0 || !storeRefundNavChannelList.contains(navChannel)){
            log.info("[isFranchiseRefundEligible] stop initiating storeRefund due to nav channel is : {} not in list {}", navChannel, storeRefundNavChannelList);
            return false;
        }
        return true;
    }

    @Override
    public RefundDispatchResponse isRefundDispatchableAtPOS(RefundDispatchRequest refundDispatchRequest) {
        log.info("[isRefundDispatchableAtPOS] refundDispatchRequest : {}", refundDispatchRequest);
        RefundDispatchResponse refundDispatchResponse = null;
        String remarks = null;
        String error = null;
        Boolean isDispatchable = false;
        Boolean isFranchiseDispatchable = false;
        String ruleEngineRefundDispatchStatus = null;

        if (refundDispatchRequest == null || refundDispatchRequest.getReturnId() == null || refundDispatchRequest.getUwItemId() == null || StringUtils.isEmpty(refundDispatchRequest.getRefundTarget())) {
            error = "refund item request is not valid";
            log.info("[isRefundDispatchableAtPOS] {} for {}", error, refundDispatchRequest);
            return new RefundDispatchResponse(null, false, false, remarks, error);
        }

        try{
            isDispatchable = false;
            isFranchiseDispatchable = isFranchiseRefundEligible(refundDispatchRequest.getNavChannel(), refundDispatchRequest.getRefundTriggerPoint()!=null? RefundTriggerPoint.getTriggerPointEnum(refundDispatchRequest.getRefundTriggerPoint()):null);

            String refundMethodSP = systemPreferenceService.getSystemPreferenceValues("refund_method_dispatchable", "pos_receiving");
            log.info("refundMethodSP {} uwItemId {}", refundMethodSP, refundDispatchRequest.getUwItemId());

            if (refundMethodSP != null) {
                List<String> refundMethods = Arrays.asList(refundMethodSP.split(","));
                if (refundMethods.contains(refundDispatchRequest.getRefundTarget())) {
                    return new RefundDispatchResponse(null, true, isFranchiseDispatchable, remarks, error);
                }
            }

            if ("exchange".equalsIgnoreCase(refundDispatchRequest.getRefundTarget())) {
                remarks = "refund target is exchange";
                log.info("{} for {}", remarks, refundDispatchRequest);
                return new RefundDispatchResponse(null, false, isFranchiseDispatchable, remarks, error);
            }


            ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(refundDispatchRequest.getUwItemId());
            if (returnRefundRule != null) {
                String refundMethodsForWhReceivingSP = systemPreferenceService.getSystemPreferenceValues("refund_dispatch_pos_return_whReceiving", "refund_rules");
                log.info("[isRefundDispatchableAtPOS] refundMethodsForWhReceivingSP: {}", refundMethodsForWhReceivingSP);

                List<String> refundMethodForWhReceivingList = (refundMethodsForWhReceivingSP != null) ? Arrays.asList(refundMethodsForWhReceivingSP.split(",")) : null;

                if (refundMethodForWhReceivingList != null && refundMethodForWhReceivingList.contains(refundDispatchRequest.getRefundTarget())) {
                    remarks = "customer refund dispatchable as false for refund method " + refundDispatchRequest.getRefundTarget();
                    log.info("{} for {}", remarks, refundDispatchRequest);
                    return new RefundDispatchResponse(null, false, isFranchiseDispatchable, remarks, error);
                }

                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                JSONObject jsonObject = new JSONObject(returnRefundRuleResponse);
                ruleEngineRefundDispatchStatus = jsonObject.optString("refundDispatch", null);

                log.info("[isRefundItemDispatchable] rule engine response for dispatch for uwitem id: " + refundDispatchRequest.getUwItemId() + " is " + returnRefundRule);
                isDispatchable = exchangeItemDispatchService.isDispatchableAtPOS(refundDispatchRequest.getReturnId(), ruleEngineRefundDispatchStatus, returnRefundRule.getTriggerPoint());
            }

        }catch (Exception exception){
            log.error("[isRefundItemDispatchable] refundDispatchRequest : {}, exception : {}", refundDispatchRequest, exception.getMessage());
            error = exception.getMessage();
        }
        return new RefundDispatchResponse(ruleEngineRefundDispatchStatus, isDispatchable, isFranchiseDispatchable, remarks, error);
    }

    @Override
    public RefundDispatchResponse isRefundDispatchable(RefundDispatchRequest request) {
        log.info("[isRefundDispatchable] request : {}", request);
        String remarks=null;
        String error=null;
        Boolean isDispatchable = false;
        Boolean isFranchiseDispatchable = false;
        String ruleEngineStatusForDispatch = null;

        if (request == null || request.getReturnId() == null || request.getUwItemId() == null) {
            error = "refund item request is not valid";
            log.info("{} for {}", error, request);
            return new RefundDispatchResponse(null, false, false, remarks, error);
        }

        try {
            isFranchiseDispatchable = isFranchiseRefundEligible(request.getNavChannel(), request.getRefundTriggerPoint()!=null? RefundTriggerPoint.getTriggerPointEnum(request.getRefundTriggerPoint()):null);
            ruleEngineStatusForDispatch = getRuleEngineResponseForDispatch(request.getUwItemId(), request.getReturnId());
            log.info("[isRefundItemDispatchable] rule engine response for dispatch for uwItemId: " + request.getUwItemId() + " is " + ruleEngineStatusForDispatch);

            if ("exchange".equalsIgnoreCase(request.getRefundTarget())) {
                remarks = "refund target is exchange";
                log.info("{} for {}", remarks, request);
                return new RefundDispatchResponse(ruleEngineStatusForDispatch, false, isFranchiseDispatchable, remarks, error);
            }

            isDispatchable = exchangeItemDispatchService.isDispatchable(request.getReturnId(), ruleEngineStatusForDispatch);

            log.info("[isRefundItemDispatchable] is dispatchable flag for uwItemId: " + request.getUwItemId() + " is " + isDispatchable);
        } catch (Exception e) {
            error = "exception caught "+ e.getMessage();
            log.info("{} for {}", error, request);
            return new RefundDispatchResponse(null, false, isFranchiseDispatchable, remarks, error);
        }

        return new RefundDispatchResponse(ruleEngineStatusForDispatch, isDispatchable, isFranchiseDispatchable, remarks, error);
    }

    private String getRuleEngineResponseForDispatch(Integer uwItemId, Integer returnId) {
        log.info("[getRuleEngineResponseForDispatch] getting rule engine response for dispatch for uwItemId: " + uwItemId + "returnId " + returnId);
        String ruleEngineDispatchStatus = null;
        try {
            ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
            if (returnRefundRule != null) {
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                org.json.JSONObject jsonObject = new org.json.JSONObject(returnRefundRuleResponse);
                ruleEngineDispatchStatus = (String) jsonObject.get("refundDispatch");
            }
        } catch (Exception e) {
            log.info("[getRuleEngineResponseForDispatch] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getRuleEngineResponseForDispatch] rule engine response for dispatch: " + ruleEngineDispatchStatus);
        return ruleEngineDispatchStatus;
    }

    @Override
    public Boolean isDispatchable(Integer returnId, String ruleEngineStatusForDispatch){
        if(TriggerPoint.ReturnInitiation.getName().equalsIgnoreCase(ruleEngineStatusForDispatch)){
            return true;
        }
        boolean isDispatchable=false;
        List<ReturnEvent> returnEvents=null;
        try {
            if (Constant.RULE_ENGINE_STATUS.ANY_RECEIVING_POINT.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                returnEvents = returnEventRepository.findByReturnId(returnId);
                if (returnEvents != null) {
                    for (ReturnEvent returnEvent : returnEvents) {
                        if (AWB_ASSIGNED.equals(returnEvent.getEvent()) || RETURN_RECEIVED.equals(returnEvent.getEvent()) || RETURN_RECEIVED_ACTION_PENDING.equals(returnEvent.getEvent())
                                || ReturnStatus.RETURN_ACCEPTED.getStatus().equalsIgnoreCase(returnEvent.getEvent())) {
                            isDispatchable = true;
                            break;
                        }
                    }
                }
            } else {
                if (Constant.RULE_ENGINE_STATUS.COURIER_PICKUP.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(AWB_ASSIGNED, RETURN_RECEIVED, RETURN_RECEIVED_ACTION_PENDING));
                } else if (Constant.RULE_ENGINE_STATUS.POS_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch) || Constant.RULE_ENGINE_STATUS.WH_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(RETURN_RECEIVED, RETURN_RECEIVED_ACTION_PENDING, RETURN_ACCEPTED));
                }
                isDispatchable = !CollectionUtils.isEmpty(returnEvents);
            }
        }
        catch(Exception exception){
            log.error("[isDispatchable] some exception occurred while calling isDispatchable service for return : "+returnId+" exception : "+exception);
        }
        log.info("[isDispatchable] returnId : {}, isDispatchable : {}", returnId, isDispatchable);
        return isDispatchable;
    }

    public Boolean isExchangeItemDispatchable(ExchangedItem exchangedItem) {
        Boolean isDispatchable = false;
        try{
            isDispatchable =  orderOpsFeignClient.isExchangeItemDispatchable(exchangedItem);
        }catch (Exception exception){
            log.error("[isExchangeItemDispatchable] exception : "+exception);
        }
        return isDispatchable;
    }

    public Boolean isRefundDispatchable(ExchangedItem exchangedItem) {
        if (exchangedItem == null || exchangedItem.getReturnId() == null || exchangedItem.getUwItemId() == null) {
            log.info("[isRefundDispatchable] refund item request is not valid");
            return false;
        }
        RefundIntentRequestDTO refundIntentRequest = null;
        Boolean isDispatchable=false;
        try {
            ResponseEntity<RefundIntentRequestDTO> refundIntentRequestDTOResponseEntity = orderOpsFeignClient.getRefundIntentRequest(exchangedItem.getUwItemId(), exchangedItem.getReturnId());
            if(refundIntentRequestDTOResponseEntity.getStatusCode().is2xxSuccessful()){
                refundIntentRequest = refundIntentRequestDTOResponseEntity.getBody();
            }
            if (refundIntentRequest == null ||  "exchange".equalsIgnoreCase(refundIntentRequest.getRefundIntentMethod())) {
                log.info("[isRefundItemDispatchable] refund method is not captured for order: " + exchangedItem);
                return false;
            }
            String ruleEngineStatusForDispatch =
                    getRuleEngineResponseForRefundDispatch(exchangedItem.getUwItemId(), exchangedItem.getReturnId()); //WH
            log.info("[isRefundItemDispatchable] rule engine response for dispatch for uwitem id: "+exchangedItem.getUwItemId()+" is "+ruleEngineStatusForDispatch);
            isDispatchable = exchangeItemDispatchService.isDispatchable(exchangedItem.getReturnId(),
                    ruleEngineStatusForDispatch);
            // If is isDispatchable is true and calling point is backwardflow then find its
            // new order and make entry in sync_unicom_status
            if (isDispatchable) {
                log.info(
                        "[isRefundItemDispatchable] refund item is dispatachable");
                return true;
            }
            log.info("[isRefundItemDispatchable] is dispatchable flag for uwitem: "+exchangedItem.getUwItemId()+" is "+isDispatchable);
        } catch (Exception e) {
            log.info(
                    "[isRefundItemDispatchable] some exception occurred while making entry for forward flow of: "
                            + exchangedItem);
            return false;
        }
        return isDispatchable;
    }

    private String getRuleEngineResponseForRefundDispatch(Integer uwItemId, Integer returnId) {
        log.info("[getRuleEngineResponseForRefundDispatch] getting rule engine response for refund dispatch for uwItemId: " + uwItemId + "returnId " + returnId);
        String ruleEngineRefundDispatchStatus = null;
        try {
            ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
            if (returnRefundRule != null) {
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                org.json.JSONObject jsonObject = new org.json.JSONObject(returnRefundRuleResponse);
                ruleEngineRefundDispatchStatus = (String) jsonObject.get("refundDispatch");
            }
        } catch (Exception e) {
            log.info("[getRuleEngineResponseForRefundDispatch] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getRuleEngineResponseForRefundDispatch] rule engine response for refund dispatch: " + ruleEngineRefundDispatchStatus);
        return ruleEngineRefundDispatchStatus;
    }

    @Override
    public RefundDispatchResponse getRefundDispatchPoint(RefundDispatchRequest request) {
        RefundDispatchResponse response = new RefundDispatchResponse();
        if (request == null) {
            return null;
        }

        if (request.getUwItemId() == null && request.getReturnId() == null) {
            return null;
        }

        if (request.getReturnId() != null && request.getUwItemId() == null) {
            ReturnDetailItem returnDetailItem = returnOrderActionService.findTopByReturnId(request.getReturnId());
            if(returnDetailItem != null){
                request.setUwItemId(returnDetailItem.getUwItemId());
            }
        }
        if (request.getUwItemId() != null && request.getReturnId() == null) {
            ReturnDetail returnDetail = returnOrderActionService.getReturnOrder(request.getUwItemId());
            if(returnDetail != null){
                request.setReturnId(returnDetail.getId());
            }
        }

        updateNavChannel(request);

        if (request.getIsPosReturn() != null && request.getIsPosReturn()) {
            response = isRefundDispatchableAtPOS(request);
        } else {
            response = isRefundDispatchable(request);
        }
        if (response == null) {
            return null;
        }

        if(response.getIsRefundDispatchable()!=null && response.getIsRefundDispatchable()){
            response.setIsFranchiseRefundDispatchable(true);
        }
        log.info("getRefundDispatchPoint request {}; response {} ", request, response);
        return response;
    }

    private void updateNavChannel(RefundDispatchRequest request) {
        ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(request.getUwItemId());
        if (returnRefundRule != null) {
            String decisionValues = returnRefundRule.getRequest();
            if(StringUtils.isNotEmpty(decisionValues)){
                org.json.JSONObject jsonObject = new org.json.JSONObject(decisionValues);
                String navChannel = jsonObject.optString("navChannel", "");
                request.setNavChannel(navChannel);
            }
        }
    }

}
