package com.lenskart.returnservice.service.helper;

import com.lenskart.ordermetadata.dto.ReturnHistoryDTO;
import com.lenskart.platform.fl.utils.dto.returnPayloads.packingSlip.PackingSlipContract;
import com.lenskart.platform.fl.utils.dto.returnPayloads.packingSlip.SalesLineList;
import com.lenskart.platform.fl.utils.dto.returnPayloads.salesOrder.SalesOrderHeader;
import com.lenskart.returncommon.model.dto.ClassificationDto;
import com.lenskart.returncommon.model.dto.InvoiceDetailsDto;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.ProductDto;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returnrepository.entity.ReturnHistory;
import com.lenskart.returnrepository.entity.ReturnOrder;
import com.lenskart.returnrepository.repository.ReturnHistoryRepository;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.lenskart.returncommon.utils.Constant.*;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.B2B;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.OTC;

@Component
@Slf4j
@RequiredArgsConstructor
public class ReturnEventPackingSlipHelper {

    @Value("#{'${finance.intCountries:SA,SG,AE,US,TH}'.split(',')}")
    private List<String> intCountries;

    private final ReturnHistoryRepository returnHistoryRepository;

    private final IReturnEventService returnEventService;

    private final InventoryDataHelper inventoryDataHelper;

    DateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);

    public String getShippingPackageId(UwOrderDTO uwOrder,UwOrderDTO uwOrderB2b) {
        Integer uwItemId = uwOrder.getUwItemId();
        String shippingPackageId = "";
        log.info("ReturnUtil : fetching shippingPackageId for uwOrder : {}",uwItemId);
        try {
            if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                if (uwOrderB2b != null) {
                    shippingPackageId = uwOrderB2b.getShippingPackageId();
                }
            } else {
                shippingPackageId = uwOrder.getShippingPackageId();
            }
        }catch (Exception e) {
            log.error("ReturnUtils : exception found to get shippingPackageId for uwItemId : {} : ",uwItemId, e);
        }
        log.info("ReturnUtil : shippingPackageId for uwOrder : {} : {}",uwItemId, shippingPackageId);
        return shippingPackageId;
    }

    public String getPSlipLegalEntity(UwOrderDTO uwOrder, OrdersHeaderDTO ordersHeader, SalesOrderHeader returnPayload, boolean isSbrt) {
        log.info("ReturnUtil : getPSlipLegalEntity for uwOrder : {}", uwOrder.getUwItemId());
        String pSlipLegalEntity = "";
        try {
            if (isSbrt) {
                pSlipLegalEntity = LK + ordersHeader.getLkCountry();
            }
            else {
                if ((B2B.equals(uwOrder.getProductDeliveryType()) && !FOFOB2B.equals(uwOrder.getNavChannel())) || OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        pSlipLegalEntity = LK + ordersHeader.getLkCountry();
                    } else if(uwOrder.getProductDeliveryType().equalsIgnoreCase(OTC)){
                        pSlipLegalEntity = LEGAL_ENTITY_DEALSKART;
                    } else {
                        pSlipLegalEntity = LEGAL_ENTITY_LENSKART;
                    }
                } else {
                    pSlipLegalEntity = returnPayload.getLegalEntity();
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to getPSlipLegalEntity for uwOrder : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : getPSlipLegalEntity for uwOrder : {} : {}", uwOrder.getUwItemId(), pSlipLegalEntity);
        return pSlipLegalEntity;
    }

    public void setPSlipDate(UwOrderDTO uwOrder, PackingSlipContract packingSlip, ReturnOrder returnOrder) {
        log.info("ReturnUtil : setPSlipDate for uwOrder : {}", uwOrder.getUwItemId());
        ReturnHistory returnHistory = null;
        Integer uwItemId = uwOrder.getUwItemId();
        try {
            if (returnOrder != null) {
                log.info("ReturnUtil : fetching return_history for uwItemId : {} ", uwItemId);
                returnHistory = returnHistoryRepository.findTopByEntityIdOrderByCreatedAtDesc(returnOrder.getReturnId());
                log.info("ReturnUtil : returnHistory for uwItemId : {} : {}",uwItemId, returnHistory);
                if (returnHistory != null && returnHistory.getCreatedAt() != null) {
                    packingSlip.setPackingSlipDate(dateFormat.format(returnHistory.getCreatedAt()));
                } else {
                    List<ReturnHistoryDTO> returnHistoryDTOList = getReturnHistory(returnOrder.getReturnId());
                    returnHistory = getLatestReturnHistory(returnHistoryDTOList, returnOrder.getReturnId());
                    if (returnHistory != null && returnHistory.getCreatedAt() != null) {
                        packingSlip.setPackingSlipDate(dateFormat.format(returnHistory.getCreatedAt()));
                    }else{
                        log.info("ReturnUtil : returnHistory found null fetching data from return_order for uwOrder : {}", uwItemId);
                        if (returnOrder != null && returnOrder.getReturnCreateDatetime() != null) {
                            packingSlip.setPackingSlipDate(dateFormat.format(returnOrder.getReturnCreateDatetime()));
                        } else {
                            packingSlip.setPackingSlipDate("NA");
                        }
                    }
                }
            } else {
                log.error("ReturnUtil : returnOrder found NULL for uwItemId : {} : {}",uwItemId, returnOrder);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setPSlipDate for uwOrder : {}", uwOrder.getUwItemId());
        }
    }

    private ReturnHistory getLatestReturnHistory(List<ReturnHistoryDTO> returnHistoryDTOList, Integer returnId) {
        log.info("[getLatestReturnHistory] returnId : {}, returnHistoryDTOList : {}", returnId, returnHistoryDTOList);
        ReturnHistory returnHistory = null;
        if(!CollectionUtils.isEmpty(returnHistoryDTOList)){
            ReturnHistoryDTO returnHistoryDTO = returnHistoryDTOList.get(returnHistoryDTOList.size()-1);
            returnHistory = getReturnHistory(returnHistoryDTO, returnId);
        }
        return returnHistory;
    }

    public List<ReturnHistoryDTO> getReturnHistory(Integer returnId){
        log.info("[getReturnHistory] returnId : {}, getting from headless returns", returnId);
        List<ReturnHistoryDTO> returnHistoryDTOList = returnEventService.getReturnHistory(returnId);
        log.info("[getReturnHistory] returnId : {}, returnDetailsDTO : {}", returnId, returnHistoryDTOList);
        return returnHistoryDTOList;
    }

    private ReturnHistory getReturnHistory(ReturnHistoryDTO returnHistoryDTO, Integer returnId) {
        log.info("[getReturnHistory] returnId : {}, returnHistoryDTO1 : {}", returnId, returnHistoryDTO);
        ReturnHistory returnHistory = null;
        if(Objects.nonNull(returnHistoryDTO)){
            returnHistory = new ReturnHistory();
            returnHistory.setAddedBy(returnHistoryDTO.getAddedBy());
            returnHistory.setComment(returnHistoryDTO.getComment());
            returnHistory.setCourier(returnHistoryDTO.getCourier());
            returnHistory.setCreatedAt(returnHistoryDTO.getCreatedAt());
            returnHistory.setCurrentStatus(returnHistoryDTO.getCurrentStatus());
            returnHistory.setEntityType(returnHistoryDTO.getEntityType());
            returnHistory.setReverseAwb(returnHistoryDTO.getReverseAwb());
            returnHistory.setReversePickupReferenceId(returnHistoryDTO.getReversePickupReferenceId());
            returnHistory.setEntityId(returnId);
        }
        log.info("[getReturnHistory] returnId : {}, returnHistoryDTO : {}", returnId, returnHistoryDTO);
        return returnHistory;
    }

    public void setPSlipTrackingNo(UwOrderDTO uwOrder, PackingSlipContract packingSlip, ReturnOrder returnOrder) {
        log.info("ReturnUtil : setPSlipTrackingNo for uwOrder : {}", uwOrder.getUwItemId());
        try {
            String reverseAwbNo = "";
            if (Objects.nonNull(returnOrder) && returnOrder.getReturnMethod() != null) {
                if ("RPU".equalsIgnoreCase(returnOrder.getReturnMethod())) {
                    reverseAwbNo = returnOrder.getReverseAwb();
                    log.info("ReverseAWB found in returnOrder {} ", reverseAwbNo);
                    packingSlip.setTrackingNo(reverseAwbNo);
                } else {
                    packingSlip.setTrackingNo(NA);
                }
            } else {
                log.info("ReturnOrder not found in ReturnRequestDto");
                packingSlip.setTrackingNo(NA);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setPSlipTrackingNo for uwOrder : {}", uwOrder.getUwItemId());
        }
    }

    public void setPSlipSalesInvoiceNo(UwOrderDTO uwOrder, PackingSlipContract packingSlip,UwOrderDTO b2bReferenceUwOrder) {
        log.info("ReturnUtil : setPSlipSalesInvoiceNo for uwOrder : {}", uwOrder.getUwItemId());
        InvoiceDetailsDto invoiceDetails= null;
        try {
            if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                invoiceDetails = inventoryDataHelper.getInvoiceDetails(b2bReferenceUwOrder.getShippingPackageId());
                packingSlip.setSalesInvoiceNo(StringUtils.defaultString(invoiceDetails.getInvoiceCode()));
            } else {
                invoiceDetails = inventoryDataHelper.getInvoiceDetails(uwOrder.getShippingPackageId());
                packingSlip.setSalesInvoiceNo(StringUtils.defaultString(invoiceDetails.getInvoiceCode()));
            }
        } catch (Exception e) {
            log.info("ReturnUtil : Exception found to setPSlipSalesInvoiceNo for uwOrder : {}", uwOrder.getUwItemId());
        }
    }


    public SalesLineList prepareSoLinesForPSlip(UwOrderDTO uwOrder, ReturnCreateRequest returnRequestDto, UwOrderDTO b2bReferenceItemOrder) {
        SalesLineList salesLineList = new SalesLineList();
        salesLineList.setSalesId(returnRequestDto.getReturnOrderItem().getReturnId() + "_" + returnRequestDto.getUwItemId());
        salesLineList.setQuantity(ITEM_QTY);
        salesLineList.setBatchId(BLANK);
        salesLineList.setItemNumber(StringUtils.defaultString(String.valueOf(uwOrder.getProductId())));
        salesLineList.setSerialId(fetchD365Barcode(uwOrder,b2bReferenceItemOrder));
        salesLineList.setLineNumber(Long.valueOf(uwOrder.getUwItemId()));
        setDimensions(salesLineList, uwOrder);
        return salesLineList;
    }

    private void setDimensions(SalesLineList salesLineList, UwOrderDTO uwOrder) {
        List<ProductDto> productList = inventoryDataHelper.getProductsByIds(Collections.singletonList(uwOrder.getProductId()));
        Map<Integer, ProductDto> productMap = productList.stream()
                .collect(Collectors.toMap(ProductDto::getProductId, p -> p));

        Set<Integer> allClassIds = productList.stream()
                .map(ProductDto::getClassification)
                .collect(Collectors.toSet());

        List<ClassificationDto> classificationList = inventoryDataHelper.getClassificationsByIds(new ArrayList<>(allClassIds));
        Map<Integer, ClassificationDto> classificationMap = classificationList.stream()
                .collect(Collectors.toMap(ClassificationDto::getId, c -> c));

        ProductDto productDto = productMap.getOrDefault(uwOrder.getProductId(),null);
        ClassificationDto classificationDto = classificationMap.getOrDefault(productDto.getClassification(),null);
        if(Objects.nonNull(productDto)){
            salesLineList.setBrand(productDto.getBrand().trim());
        }
        if(Objects.nonNull(classificationDto)) {
            salesLineList.setItemClassification(classificationDto.getDisplayName());
        }
    }

    public String fetchD365Barcode(UwOrderDTO uwOrder,UwOrderDTO b2bReferenceItemOrder){
        return getBarCode(uwOrder,b2bReferenceItemOrder);
    }

    public String getBarCode(UwOrderDTO uwOrder,UwOrderDTO uwOrderB2B) {
        Integer uwItemId = uwOrder.getUwItemId();
        String barCode = "";
        log.info("ReturnUtil : fetching BarCode for uwOrder : {}",uwItemId);
        try {
            if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()) && b2BVirtualOrder(uwOrder)) {
                if (uwOrderB2B != null && !StringUtils.isEmpty(uwOrderB2B.getBarcode())) {
                    barCode = StringUtils.defaultString(uwOrderB2B.getBarcode());
                }
            } else {
                if (!StringUtils.isEmpty(uwOrder.getBarcode())) {
                    barCode = StringUtils.defaultString(uwOrder.getBarcode());
                }
            }
        } catch (Exception e) {
            log.info("exception found to fetch BarCode for uwOrder : {} : ",uwItemId,e);
        }
        log.info("ReturnUtil : BarCode for uwOrder : {} : {}",uwItemId, barCode);
        return barCode;
    }

    public boolean b2BVirtualOrder(UwOrderDTO uwOrder) {
        List<String> hubMasterFacilityCodes = inventoryDataHelper.getHubMasterFacilityCodes();
        if (StringUtils.isNotBlank(uwOrder.getBarcode())) {
            if (!hubMasterFacilityCodes.contains(uwOrder.getFacilityCode())) {
                return B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()) && StringUtils.isNotBlank(uwOrder.getNavChannel()) && !uwOrder.getNavChannel().toLowerCase().contains(FOFO_IDENTIFIER);
            } else {
                return false;
            }
        }
        return true;
    }
}
