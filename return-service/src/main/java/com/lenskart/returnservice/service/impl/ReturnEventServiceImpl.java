package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ccautils.constants.CosmosConstants;
import com.lenskart.ordermetadata.dto.ReturnHistoryDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.GetBulkCancellationEventsRequest;
import com.lenskart.returncommon.model.response.BulkCancellationEvent;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.returncommon.utils.Constant.EVENT.RETURN_TRACKING_NO_RECEIVED_AT_WAREHOUSE;
import static com.lenskart.returncommon.utils.Constant.EVENT.RTO_TRACKING_NO_RECEIVED_AT_WAREHOUSE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.COSMOS_EVENT_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.RETURN_EVENT_TOPIC;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.AWAITED_RTO;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.REVERSE;
import static com.lenskart.returnservice.service.impl.RefundUtilsServiceImpl.gson;

@Service
@Slf4j
public class ReturnEventServiceImpl implements IReturnEventService {

    @Autowired
    private ReturnEventRepository returnEventRepository;
    @Autowired
    private ReverseTrackingEventRepository reverseTrackingEventRepository;

    @Autowired
    private IKafkaService kafkaService;
    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    @Autowired
    private ReturnHistoryRepository returnHistoryRepository;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    private final List<String> irrelevantEvents = List.of("PICKUP SCHEDULED");
    private final List<String> duplicateEvents = List.of("AWB_ASSIGNED");

    @Override
    public void persistEvent(Integer requestId, Integer returnId, String event, String remarks) {
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setReturnId(returnId);
        returnEvent.setReturnRequestId(requestId);
        returnEvent.setEvent(event);
        returnEvent.setRemarks(remarks);
        returnEvent.setCreatedAt(new Date());
        returnEventRepository.save(returnEvent);
        //push-cosmos-event
        pushEventToCosmos(returnId,event,requestId);
    }

    @Override
    public void createReturnEvent(Integer requestId, Integer returnId, String event, String remarks) {
        try{
            ReturnEvent returnEvent = new ReturnEvent();
            returnEvent.setReturnRequestId(requestId);
            returnEvent.setReturnId(returnId);
            returnEvent.setEvent(event);
            returnEvent.setRemarks(remarks);
            kafkaService.pushToKafka(RETURN_EVENT_TOPIC, String.valueOf(requestId), returnEvent);
            if(requestId == null){
                requestId = getRequestId(returnId);
            }
            List<Integer> returnIds = getReturnIds(requestId);
            ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTopByReturnId(returnId);
            if(!org.apache.commons.collections4.CollectionUtils.isEmpty(returnIds) && returnIds.size() > 1
                    && returnDetailItem != null && "B2B".equalsIgnoreCase(returnDetailItem.getProductDeliveryType())
                    && !event.contains("COSMOS")){
                returnIds = returnIds.stream().filter(returns -> !Objects.equals(returns, returnId)).collect(Collectors.toList());
                returnEvent.setReturnId(returnIds.get(0));
                returnEvent.setRemarks(" for B2B item of returnId :"+returnIds.get(0));
                if(!CollectionUtils.isEmpty(returnIds) && returnIds.get(0) != null){
                    kafkaService.pushToKafka(RETURN_EVENT_TOPIC, String.valueOf(requestId), returnEvent);
                }
            }
        }catch (Exception exception){
            log.error("[createReturnEvent] error occured : "+exception);
        }
    }

    private void pushEventToCosmos(Integer returnId, String event, Integer requestId) {
        try {
            ReturnEvent returnEvent = new ReturnEvent();
            returnEvent.setReturnRequestId(requestId);
            returnEvent.setReturnId(returnId);
            returnEvent.setEvent(event);
            kafkaService.pushToKafka(COSMOS_EVENT_QUEUE, String.valueOf(requestId), returnEvent);
            log.info("[pushEventToCosomos] Successfully pushed event to cosmos for event:{} and returnId: {}",event,returnId);
        } catch (Exception exception) {
            log.error("[pushEventToCosomos] error occured for returnId:{}",returnId,exception);
        }
    }

    private static final Map<String, String> COSMOS_EVENT_MAPPING = Map.ofEntries(
            Map.entry(ReturnStatus.CANCELLED.getStatus(), CosmosConstants.Events.RETURN_CANCELLED.eventName),
            Map.entry(ReturnStatus.CUSTOMER_CANCELLED.getStatus(), CosmosConstants.Events.RETURN_CANCELLED.eventName),
            Map.entry(ReturnStatus.RETURN_REJECTED.getStatus(), CosmosConstants.Events.RETURN_REJECTED.eventName),
            Map.entry(ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus(), CosmosConstants.Events.RETURN_REJECTED.eventName),
            Map.entry(ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus(), CosmosConstants.Events.RETURN_REJECTED_HANDOVER_DONE.eventName),
            Map.entry(ReturnStatus.RETURN_ACCEPTED.getStatus(), CosmosConstants.Events.RETURN_APPROVED.eventName),
            Map.entry(ReturnStatus.RETURN_EXCHANGE.getStatus(), CosmosConstants.Events.RETURN_EXCHANGED.eventName),
            Map.entry(ReturnStatus.REFERENCE_ID_ISSUED.getStatus(), CosmosConstants.Events.PICKUP_SCHEDULED.eventName),
            Map.entry(ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), CosmosConstants.Events.RETURN_NEED_APPROVAL.eventName),
            Map.entry(ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), CosmosConstants.Events.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.eventName),
            Map.entry(ReturnStatus.NEW_REVERSE_PICKUP.getStatus(), CosmosConstants.Events.NEW_RETURN_PICKUP.eventName),
            Map.entry(ReturnStatus.RETURN_EXPECTED_POS.getStatus(), CosmosConstants.Events.RETURN_EXPECTED_POS.eventName),
            Map.entry(ReturnStatus.RETURN_EXPECTED_WH.getStatus(), CosmosConstants.Events.RETURN_EXPECTED_WH.eventName),
            Map.entry(ReturnStatus.AWB_ASSIGNED.getStatus(), CosmosConstants.Events.RETURN_PICKED.eventName)
    );


    private String getCosmosEvent(String event,String source,String returnMethod) {
        String eventName =  COSMOS_EVENT_MAPPING.get(event);
        if (eventName != null) {
            return eventName;
        }
        if ("return_received".equalsIgnoreCase(event)) {
            if (ReturnSources.POS.getSource().equalsIgnoreCase(source) && "StoreReceiving".equalsIgnoreCase(returnMethod)) {
                return CosmosConstants.Events.RETURN_RECEIVED_AT_STORE.eventName;
            } else if (ReturnSources.WAREHOUSE.getSource().equalsIgnoreCase(source) && "DirectReceiving".equalsIgnoreCase(returnMethod)) {
                return CosmosConstants.Events.RETURN_RECEIVED_AT_WAREHOUSE.eventName;
            }
        }

        return CosmosConstants.Events.NEW_RETURN_PICKUP.eventName;
    }

    @Override
    public List<ReturnEvent> getReturnEvent(Integer returnId) {
        return returnEventRepository.findByReturnId(returnId);
    }

    @Override
    public void createTrackingEvent(ReturnEvent returnEvent) {
        Integer returnId = returnEvent.getReturnId();
        if (returnId == null) {
            log.error("[createTrackingEvent] returnId missing, cannot proceed");
            return;
        }
        String event = returnEvent.getEvent();
        if(irrelevantEvents.contains(event.toUpperCase())){
            log.info("[createTrackingEvent] not saving {} event in return events", event);
            return;
        }
        List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnId);
        if (returnEvents.stream().map(ReturnEvent::getEvent).noneMatch(rEvent -> rEvent != null && rEvent.equalsIgnoreCase(event))) {
            Integer returnRequestId = returnEvents.get(0).getReturnRequestId();
            returnEvent.setReturnRequestId(returnRequestId);
            persistEvent(returnRequestId, returnId, getEvent(returnEvent.getEvent()), returnEvent.getRemarks());
        }
    }

    @Override
    public void createAthenaTrackingEvent(ReturnTrackingEventDTO returnTrackingEventDTO) {
        log.info("[createAthenaTrackingEvent] returnTrackingEventDTO : "+ gson.toJson(returnTrackingEventDTO));

        String trackingNo = returnTrackingEventDTO.getTrackingNo();
        try {
            ReverseDetailTrackingEvent reverseDetailTrackingEvent = reverseTrackingEventRepository.findTop1ByReverseAwb(trackingNo);
            log.info("[createAthenaTrackingEvent] reverseDetailTrackingEvent : "+ gson.toJson(reverseDetailTrackingEvent));

            if (reverseDetailTrackingEvent != null && reverseDetailTrackingEvent.getReturnId() != null) {
                Integer returnId = reverseDetailTrackingEvent.getReturnId();
                List<ReturnDetail> returnDetails = returnDetailRepository.findByReturnIdIn(List.of(returnId));
                log.info("[createAthenaTrackingEvent] returnDetails : " + gson.toJson(returnDetails));

                if (!CollectionUtils.isEmpty(returnDetails)) {
                    String reverseType = returnDetails.get(0) != null ? returnDetails.get(0).getReturnType() : null;
                    log.info("[createAthenaTrackingEvent] reverseType : " + reverseType);
                    processReturnEvent(reverseDetailTrackingEvent, reverseType);
                }
            } else {
                log.info("Could not find trackingNo in new return db, hence its a part of old return flow");
            }

        } catch (Exception e) {
            log.error("[createAthenaTrackingEvent] Error occurred: ", e);
        }

    }

    private void processReturnEvent(ReverseDetailTrackingEvent reverseDetailTrackingEvent, String reverseType) {
        if (reverseType == null) {
            log.info("[createAthenaTrackingEvent] reverseType missing, cannot proceed");
            return;
        }
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setReturnId(reverseDetailTrackingEvent.getReturnId());
        if (REVERSE.equals(reverseType)) {
            returnEvent.setEvent(RETURN_TRACKING_NO_RECEIVED_AT_WAREHOUSE);
        } else if (AWAITED_RTO.equals(reverseType)) {
            returnEvent.setEvent(RTO_TRACKING_NO_RECEIVED_AT_WAREHOUSE);
        }
        log.info("[processReturnEvent] pushing returnEvent : " + gson.toJson(returnEvent));
        createTrackingEvent(returnEvent);
    }

    @Override
    public Integer getRequestId(Integer returnId) {
        List<ReturnEvent> returnEvents = getReturnEvent(returnId);
        return !CollectionUtils.isEmpty(returnEvents) ? returnEvents.get(0).getReturnRequestId() : null;
    }

    @Override
    public List<Integer> getReturnIds(Integer requestId) {
        List<Integer> returnIdList = null;
        if(requestId != null){
            List<ReturnEvent> returnEvents = returnEventRepository.findByReturnRequestId(requestId);
            if(!CollectionUtils.isEmpty(returnEvents)){
                returnIdList = returnEvents.stream().map(ReturnEvent::getReturnId).distinct().collect(Collectors.toList());
            }
        }
        return returnIdList;
    }

    private String getEvent(String event){
       return switch(event) {
           case "Pickup Done" -> ReturnStatus.AWB_ASSIGNED.getName().toUpperCase();
           default -> event.toUpperCase();
       };

    }

    @Override
    public List<ReturnHistoryDTO> getReturnHistory(Integer returnId){
        log.info("[getReturnHistory] returnId : {}", returnId);
        List<ReturnHistoryDTO> returnHistoryDTOList = new ArrayList<>();
        if(returnId != null){
            List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnId);
            if(!CollectionUtils.isEmpty(returnEvents)){
                for(ReturnEvent returnEvent : returnEvents){
                    ReturnHistoryDTO returnHistoryDTO = new ReturnHistoryDTO();
                    returnHistoryDTO.setCreatedAt(returnEvent.getCreatedAt());
                    returnHistoryDTO.setCurrentStatus(returnEvent.getEvent());
                    returnHistoryDTO.setComment(returnEvent.getRemarks());
                    returnHistoryDTOList.add(returnHistoryDTO);
                }
            }else{
                List<ReturnHistory> returnHistoryList = returnHistoryRepository.findByEntityId(returnId);
                if(!CollectionUtils.isEmpty(returnHistoryList)){
                    for(ReturnHistory returnHistory : returnHistoryList){
                        ReturnHistoryDTO returnHistoryDTO = new ReturnHistoryDTO();
                        returnHistoryDTO.setComment(returnHistory.getComment());
                        returnHistoryDTO.setCurrentStatus(returnHistory.getCurrentStatus());
                        returnHistoryDTO.setCreatedAt(returnHistory.getCreatedAt());
                        returnHistoryDTO.setCourier(returnHistory.getCourier());
                        returnHistoryDTO.setAddedBy(returnHistory.getAddedBy());
                        returnHistoryDTO.setEntityType(returnHistory.getEntityType());
                        returnHistoryDTO.setReverseAwb(returnHistory.getReverseAwb());
                        returnHistoryDTO.setReversePickupReferenceId(returnHistory.getReversePickupReferenceId());
                        returnHistoryDTO.setSource(returnHistory.getSource());
                        returnHistoryDTOList.add(returnHistoryDTO);
                    }
                }
            }
        }
        log.info("[getReturnHistory] returnId : {}, getReturnHistory : {}", returnId, returnHistoryDTOList);
        return returnHistoryDTOList;
    }

    @Override
    public List<BulkCancellationEvent> getBulkCancellationHistory(GetBulkCancellationEventsRequest request) {
        if (request == null || request.getStatus() == null) {
            return null;
        }

        if (request.getFrom() == null || request.getTo() == null) {
            Date today = Date.from(Instant.now().minus(1, ChronoUnit.DAYS));
            request.setFrom(DateUtil.getStartOfDay(today));
            request.setTo(DateUtil.getEndOfDay(today));
        } else {
            try {
                DateUtil.validateDateRange(request.getFrom(), request.getTo());
            } catch (Exception e) {
                log.error("[getBulkCancellationHistory] Invalid date range : ", e);
                return null;
            }
        }
        List<BulkCancellationEvent> events = new ArrayList<>();
        List<ReturnEvent> returnEvents = null;

        if (request.getStatus().equalsIgnoreCase("all")) {
            returnEvents = returnEventRepository.findByEventInAndCreatedAtBetween(List.of("BULK_CANCELLATION_SUCCESS", "BULK_CANCELLATION_FAILURE"), request.getFrom(), request.getTo());
        } else if (request.getStatus().equalsIgnoreCase("success")) {
            returnEvents = returnEventRepository.findByEventAndCreatedAtBetween("BULK_CANCELLATION_SUCCESS", request.getFrom(), request.getTo());
        } else if (request.getStatus().equalsIgnoreCase("failure")) {
            returnEvents = returnEventRepository.findByEventAndCreatedAtBetween("BULK_CANCELLATION_FAILURE", request.getFrom(), request.getTo());
        }

        if (!CollectionUtils.isEmpty(returnEvents)) {
            returnEvents.forEach(returnEvent -> {
                BulkCancellationEvent bulkCancellationEvent = new BulkCancellationEvent();
                bulkCancellationEvent.setCreatedAt(returnEvent.getCreatedAt());
                bulkCancellationEvent.setMessage(returnEvent.getRemarks());
                bulkCancellationEvent.setReturnId(returnEvent.getReturnId());
                bulkCancellationEvent.setSource(returnEvent.getSource());
                if (returnEvent.getEvent().equalsIgnoreCase("BULK_CANCELLATION_SUCCESS")) {
                    bulkCancellationEvent.setStatus("Success");
                    events.add(bulkCancellationEvent);
                } else if (returnEvent.getEvent().equalsIgnoreCase("BULK_CANCELLATION_FAILURE")) {
                    bulkCancellationEvent.setStatus("Failure");
                    events.add(bulkCancellationEvent);
                }
            });
        }

        return events;
    }
}
