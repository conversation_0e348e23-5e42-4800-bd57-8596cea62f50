package com.lenskart.returnservice.service;

import org.json.JSONObject;

public interface IReturnsReplicaHandlerService {
    void handleReturnsReplica(JSONObject payload);
    void handleReturnOrderReplica(JSONObject payload);
    void handleReturnOrderItemReplica(JSONObject payload);
    boolean processAndSaveReturnOrderItems(int lastProcessedId);
    void handleReturnReasonReplica(JSONObject payload);
    void handleReturnOrderAddressUpdateReplica(JSONObject payload);
    void handleReturnHistoryReplica(JSONObject payload);
    void handleRefundRulesReplica(JSONObject jsonObject);
    void handleReversePickupPincodeReplica(JSONObject jsonObject);
    void handleReverseCourierMappingReplica(JSONObject jsonObject);
}
