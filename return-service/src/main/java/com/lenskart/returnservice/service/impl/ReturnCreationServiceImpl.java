package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.refund.client.model.enums.RefundReason;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.exception.ReturnNotFound;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.response.ItemWiseFastRefunResponse;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.IReturnOrderSaleDetailsRepository;
import com.lenskart.returnrepository.repository.ReturnGroupRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lenskart.returncommon.utils.Constant.EVENT.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.BACK_SYNC_STATUS_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.MARK_DISPENSING_QUEUE;
import static com.lenskart.returncommon.utils.Constant.SYNC_STATUS.BLANK;
import static com.lenskart.returncommon.utils.Constant.SYNC_STATUS.CLOSED;

@Slf4j
@Service
public class ReturnCreationServiceImpl implements IReturnCreationService {

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IOrderUtilsService orderUtilsService;

    @Autowired
    private IReturnOrderSaleDetailsRepository returnOrderSaleDetailsRepository;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private INexsFacilityService nexsFacilityService;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReturnGroupRepository returnGroupRepository;

    @Value("#{'${bypassReturnReceivingCountries:SG}'.split(',')}")
    private List<String> bypassReturnReceivingCountries;

    @Autowired
    private IReturnReasonService returnReasonService;

    @Autowired
    private IReturnOrderItemService returnOrderItemService;

    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;

    @Autowired
    private IReverseCourierDetailService reverseCourierDetailService;

    @Autowired
    private IReturnOrderAddressUpdateService returnOrderAddressUpdateService;

    @Autowired
    private IOrderSyncService orderSyncService;

    @Autowired
    private IRefundRequestService refundRequestService;


    @Autowired
    private ID365FinanceService id365FinanceService;

    @Autowired
    private ReturnUtil returnUtil;

    @Autowired
    RedisTemplate<String,Object> redisTemplate;

    @Value("${redis.repeatedReturn.expirationTime:5}")
    Integer repeatedReturnExpTime;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate<String, Object> kafkaProducerTemplate;

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final List<ReturnStatus> DRAFTED_STATUS = Arrays.asList(ReturnStatus.RETURN_EXPECTED_WH, ReturnStatus.RETURN_EXPECTED_POS);

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    private List<String> physicalFacilities = new ArrayList<>();

    @PostConstruct
    public void init() {
        SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey("NEW_REFUND_FLOW", "B2B_WH_FACILITY");
        if (systemPreference != null) {
            String facilityCode = systemPreference.getValue();
            physicalFacilities = Arrays.asList(facilityCode.split("\\s*,\\s*"));
        }
    }
    @Override
    public ReturnCreationResponse createReturn(PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, ReturnCreationRequestDTO returnCreationRequest, ReverseCourierDetail reverseCourierDetail, String returnInitiatedFrom, ReturnRequest returnRequest) throws Exception {
        log.info("[ReturnOrderActionServiceImpl][create] order : {}", returnCreationRequest.getIncrementId());
        Integer orderId = purchaseOrderDetailsDTO.getOrders()
                .get(0).getOrderId();
        log.info("[ReturnOrderActionServiceImpl][create] reverseCourierDetail:{}",  reverseCourierDetail);
        List<Returns> itemWiseReturnIds = new ArrayList<Returns>();
        Long groupId = null;
        ReturnDetail returnOrder = null;
        Integer returnId = null;
        log.info("[ReturnOrderActionServiceImpl][create] order : {}, items : {}", returnCreationRequest.getIncrementId(), returnCreationRequest.getItems());
        for (ReturnItemDTO item : returnCreationRequest.getItems()) {
            Integer uwItemId = item.getUwItemId();
            log.info("[ReturnOrderActionServiceImpl:create] fetching uwOrder for uwItemId {}", uwItemId);

            if(isRepeatedReturnTransaction(uwItemId) && StringUtils.isEmpty(returnCreationRequest.getRequestApprover())){
                log.error("[ReturnActionImpl:create] repeated return txn call : for uwItemId : {}", uwItemId);
                throw new Exception("Repeated Transaction for uwItemId "+uwItemId);
            }else{
                storeReturnTransaction(uwItemId);
            }

            UwOrderDTO uwOrder = purchaseOrderDetailsDTO.getUwOrders()
                    .stream()
                    .filter(uw -> Objects.equals(uw.getUwItemId(), uwItemId))
                    .findFirst()
                    .get();
            String source = returnCreationRequest.getReturnSource().getSource();
            String method = returnCreationRequest.getReturnMethod();
            boolean isInsurance = Boolean.TRUE.equals(item.getClaimInsurance());
            ReturnStatus returnStatus = fetchReturnStatusBySourceAndReturnMethod(source, method, item.getNeedApproval(), isInsurance, orderId, uwOrder.getUnicomOrderCode());
            log.info("[ReturnOrderActionServiceImpl:create] Return status for item {} is {}", item.getUwItemId(), returnStatus);
            Map<Integer, Double> itemRefundMap = new HashMap<Integer, Double>();
            itemRefundMap.put(uwOrder.getUwItemId(), 0.0d);

            // return-cosmos-integration
            String eventName = returnUtil.getEventName(returnStatus.getStatus(),returnCreationRequest.getReturnSource().getSource(), returnCreationRequest.getReturnMethod());

            //double itemLevelToRefund = getRefundAmountAtItemLevel(incrementId, uwOrder, itemRefundMap);

            Optional<ReturnDetail> returnDetailsOpt = returnOrderActionService.getExistingReturnDetailsByItem(item.getUwItemId());
            boolean isNewReturnBeCreated = false;

            try {
                isNewReturnBeCreated = returnOrderActionService.shouldReturnBeCreated(item, returnDetailsOpt);
            } catch (ReturnNotFound returnNotFound) {
                returnEventService.createReturnEvent(returnRequest.getId(), returnDetailsOpt.get().getId(), returnNotFound.getCode(), returnNotFound.getMessage());
                throw returnNotFound;
            }
            QCType qcType = reverseCourierDetail != null ? reverseCourierDetail.getCourierAssignmentType() : null;
            returnOrder = returnOrderActionService.persistReturn(isNewReturnBeCreated, returnDetailsOpt, returnCreationRequest, uwOrder, purchaseOrderDetailsDTO.getOrdersHeaderDTO(), returnStatus, item, qcType, returnRequest.getId());
            performPostReturnCreationActivitiesAsync(returnOrder, returnRequest, returnCreationRequest, item, uwOrder, reverseCourierDetail, orderId, isNewReturnBeCreated, purchaseOrderDetailsDTO, returnStatus);
            itemWiseReturnIds.add(getReturns(uwOrder, returnOrder, item, eventName));
            groupId = returnOrder.getGroupId();
            returnId = returnOrder.getId();
        }
        return getReturnCreationResponse(itemWiseReturnIds, groupId, returnId);
    }

    private boolean isRepeatedReturnTransaction(Integer uwItemId){
        String key = uwItemId+"_Return";
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    private void storeReturnTransaction(Integer uwItemId){
        String key = uwItemId+"_Return";
        redisTemplate.opsForValue().set(key, uwItemId, repeatedReturnExpTime, TimeUnit.SECONDS);
    }

    private ReturnCreationResponse getReturnCreationResponse(List<Returns> itemWiseReturnIds, Long groupId, Integer returnId) {
        ReturnCreationResponse returnCreationResponse = new ReturnCreationResponse();
        Result result = new Result();
        result.setGroupId(groupId);
        result.setReturns(itemWiseReturnIds);
        result.setSuccess(true);
        returnCreationResponse.setResult(result);
        returnCreationResponse.setReturnId(returnId);
        log.info("[getReturnCreationResponse] response : {}", returnCreationResponse);
        return returnCreationResponse;
    }

    private void saveReturnCourierDetail(ReverseCourierDetail reverseCourierDetail, Integer returnId) {
        if(reverseCourierDetail != null){
            ReturnCourierDetail returnCourierDetail = new ReturnCourierDetail();
            returnCourierDetail.setReturnId(returnId);
            returnCourierDetail.setReverseCourier(reverseCourierDetail.getCourier());
            returnCourierDetail.setCreatedAt(new Date());
            returnOrderActionService.saveReturnCourier(returnCourierDetail);
        }
    }

    private Returns getReturns(UwOrderDTO uwOrder, ReturnDetail returnDetail, ReturnItemDTO item, String eventName) {
        Returns returns = new Returns();
        returns.setUwItemId(item.getUwItemId());
        returns.setReturnId(returnDetail.getId());
        returns.setMagentoItemId(item.getMagentoId());
        returns.setEventName(eventName);
        return returns;
    }


    private void saveReturnSaleDetails(Integer returnId, ReturnCreationRequestDTO returnCreationRequest, Boolean needApproval) {
        try {
            if (Boolean.TRUE.equals(needApproval)) {
                ReturnDetailSaleDetails returnOrderSaleDetails = returnOrderSaleDetailsRepository.findByReturnId(returnId);
                if (Objects.isNull(returnOrderSaleDetails)) {
                    returnOrderSaleDetails = new ReturnDetailSaleDetails();
                }
                returnOrderSaleDetails.setReturnId(returnId);
                returnOrderSaleDetails.setSalesmanName(returnCreationRequest.getSalesmanName());
                returnOrderSaleDetails.setSalesmanNumber(returnCreationRequest.getSalesmanNumber());
                returnOrderSaleDetails.setCallbackRequiredToSalesman(returnCreationRequest.isCallbackRequiredToSalesman());
                returnOrderSaleDetails.setStoreFacilityCode(returnCreationRequest.getStoreFacilityCode());
                returnOrderSaleDetailsRepository.save(returnOrderSaleDetails);
            }
        } catch (Exception e) {
            log.error("[ReturnActionImpl][createReturnSaleDetails] Failed to create createReturnSaleDetails for return-id: {}", returnId);
        }
    }

    private double getRefundAmountAtItemLevel(int incrementId, UwOrderDTO uwOrder, Map<Integer, Double> itemRefundMap) {
        double itemLevelToRefund = 0.0d;
        log.info("[ReturnOrderActionServiceImpl:create] fetching item refundable amount by fastRefundHistoryService for increment id {}", uwOrder.getIncrementId());
        ResponseEntity<ItemWiseFastRefunResponse> itemWiseFastRefunResponseResponseEntity = orderOpsFeignClient.getRefundHistoryV2(incrementId);
        if (itemWiseFastRefunResponseResponseEntity.getStatusCode().is2xxSuccessful()) {
            ItemWiseFastRefunResponse itemWiseFastRefunResponse = itemWiseFastRefunResponseResponseEntity.getBody();
            if (itemWiseFastRefunResponse != null) {
                List<ItemWiseGetAmount> itemIdList = itemWiseFastRefunResponse.getItemIds();
                if (!CollectionUtils.isEmpty(itemIdList)) {
                    for (ItemWiseGetAmount itemWiseGetAmount : itemIdList) {
                        if (null != itemRefundMap.get(itemWiseGetAmount.getUwItemId())) {
                            log.info("refunded amount is {}", itemWiseGetAmount.getRefundAmount());
                            itemLevelToRefund = itemWiseGetAmount.getActualAmount().doubleValue() - itemWiseGetAmount.getRefundAmount().doubleValue();
                            log.info("[processCustomerRefund] uwitemId : " + itemWiseGetAmount.getUwItemId() + " amount to refund : " + itemLevelToRefund);
                            if (itemLevelToRefund < 0) {
                                itemLevelToRefund = 0;
                            }
                            itemRefundMap.put(itemWiseGetAmount.getUwItemId(), itemLevelToRefund);
                        }
                    }
                }
            }
        }
        return itemLevelToRefund;
    }

    private String getLkCountry(OrdersHeaderDTO ordersHeaderDTO) {
        String lkCountry = ordersHeaderDTO.getLkCountry();
        log.info("[getLkCountry] lkCountry : {}", lkCountry);
        return lkCountry;
    }

    private static boolean isCourierToBeReassigned(Boolean isCourierReassigned) {
        return isCourierReassigned != null && isCourierReassigned;
    }
    private void saveCourierEvents(ReverseCourierDetail reverseCourierDetail, ReturnCreationRequestDTO returnCreationRequest, Integer returnId, Integer returnRequestId) {
        if (reverseCourierDetail != null  && !StringUtils.isEmpty(reverseCourierDetail.getCourier()) && !isCourierToBeReassigned(returnCreationRequest.getIsCourierReassigned())) {
            returnEventService.createReturnEvent(returnRequestId, returnId, COURIER_ALLOCATED, reverseCourierDetail.getCourier());
        }
        if (isCourierToBeReassigned(returnCreationRequest.getIsCourierReassigned())) {
            reverseCourierDetail = reverseCourierDetailService.reassignCourier(returnCreationRequest, reverseCourierDetail);
            if (reverseCourierDetail != null) {
                returnEventService.createReturnEvent(returnRequestId, returnId, COURIER_REALLOCATED, reverseCourierDetail.getCourier());
            }
        }
    }

    private void performPostReturnCreationActivitiesAsync(ReturnDetail returnOrder, ReturnRequest returnRequest, ReturnCreationRequestDTO returnCreationRequest, ReturnItemDTO item, UwOrderDTO uwOrder, ReverseCourierDetail reverseCourierDetail, Integer orderId, boolean isReturnCreatedNow, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, ReturnStatus returnStatus) throws Exception {
        log.info("[ReturnCreationServiceImpl][performPostReturnCreationActivities] enter order : {}, status : {}", returnCreationRequest.getIncrementId(), returnStatus.getStatus());

        PostReturnCreationActivityDTO postReturnCreationActivityDTO = new PostReturnCreationActivityDTO();
        postReturnCreationActivityDTO.setReturnId(returnOrder.getId());
        postReturnCreationActivityDTO.setReturnRequestId(returnRequest.getId());
        postReturnCreationActivityDTO.setIncrementId(returnOrder.getIncrementId());
        postReturnCreationActivityDTO.setGroupId(returnOrder.getGroupId());
        postReturnCreationActivityDTO.setSource(returnCreationRequest.getReturnSource().getSource());
        postReturnCreationActivityDTO.setReturnCreationDate(returnOrder.getReturnCreateDatetime());
        postReturnCreationActivityDTO.setItem(item);
        postReturnCreationActivityDTO.setUwOrder(uwOrder);
        postReturnCreationActivityDTO.setPurchaseOrderDetailsDTO(purchaseOrderDetailsDTO);
        postReturnCreationActivityDTO.setReturnCreationRequest(returnCreationRequest);
        postReturnCreationActivityDTO.setReverseCourierDetail(reverseCourierDetail);
        postReturnCreationActivityDTO.setOrderId(orderId);
        postReturnCreationActivityDTO.setReturnCreatedNow(isReturnCreatedNow);
        postReturnCreationActivityDTO.setReturnStatus(returnStatus);
        postReturnCreationActivityDTO.setReturnType(returnOrder.getReturnType());
        postReturnCreationActivityDTO.setReturnInitiatedFrom("pos".equalsIgnoreCase(returnRequest.getSource()) ? RefundReason.POS_RETURN.getValue() : RefundReason.ONLINE_RETURN.getValue());
        try {
            String postReturnCreationActivityDTORequest = objectMapper.writeValueAsString(postReturnCreationActivityDTO);
            log.info("ReturnCreationServiceImpl.performPostReturnCreationActivities topic: {} event: {}", "post_return_creation_activities_queue", postReturnCreationActivityDTORequest);
            kafkaProducerTemplate.send("post_return_creation_activities_queue", String.valueOf(returnOrder.getIncrementId()), postReturnCreationActivityDTORequest);
        } catch (JsonProcessingException e) {
            log.error("ReturnCreationServiceImpl.performPostReturnCreationActivities exception occurred while generating: ", e);
        }
        log.info("[ReturnCreationServiceImpl][performPostReturnCreationActivities] exit order : {}", returnCreationRequest.getIncrementId());
    }

    private void saveReturnStatusEvent(Integer returnRequestId, Integer returnId, String status, String source, String returnMethod) {
        if(ReturnSources.POS.getSource().equalsIgnoreCase(source) && "StoreReceiving".equalsIgnoreCase(returnMethod) && ReturnStatus.RETURN_RECEIVED.getStatus().equalsIgnoreCase(status)){
            returnEventService.createReturnEvent(returnRequestId, returnId, ReturnStatus.RETURN_RECEIVED_AT_STORE.getName(), "");
        }else if(ReturnSources.WAREHOUSE.getSource().equalsIgnoreCase(source)){
            returnEventService.createReturnEvent(returnRequestId, returnId, ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName(), "");
        } else{
            returnEventService.persistEvent(returnRequestId, returnId, status, "");
        }
        kafkaService.pushToKafka("d365_return_tracking_event", String.valueOf(returnId), new D365ReturnTrackingRequestDTO(returnId, status));
    }

    @Override
    public void performPostReturnCreationActivities(PostReturnCreationActivityDTO postReturnCreationActivityDTO) throws Exception{
        log.info("[ReturnCreationServiceImpl][performPostReturnCreationActivities] enter order : {}, status : {}", postReturnCreationActivityDTO.getIncrementId(), postReturnCreationActivityDTO.getReturnStatus().getStatus());

        ReturnCreationRequestDTO returnCreationRequest = postReturnCreationActivityDTO.getReturnCreationRequest();
        ReturnItemDTO item = postReturnCreationActivityDTO.getItem();
        ReverseCourierDetail reverseCourierDetail = postReturnCreationActivityDTO.getReverseCourierDetail();
        String returnType = postReturnCreationActivityDTO.getReturnType();
        ReturnStatus returnStatus = postReturnCreationActivityDTO.getReturnStatus();
        Long groupId = postReturnCreationActivityDTO.getGroupId();
        Integer incrementId = postReturnCreationActivityDTO.getIncrementId();
        Integer orderId = postReturnCreationActivityDTO.getOrderId();
        Integer returnId = postReturnCreationActivityDTO.getReturnId();
        Date returnCreationDate = postReturnCreationActivityDTO.getReturnCreationDate();
        String source = postReturnCreationActivityDTO.getSource();
        Integer returnRequestId = postReturnCreationActivityDTO.getReturnRequestId();
        boolean returnCreatedNow = postReturnCreationActivityDTO.isReturnCreatedNow();
        UwOrderDTO uwOrder = postReturnCreationActivityDTO.getUwOrder();
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = postReturnCreationActivityDTO.getPurchaseOrderDetailsDTO();
        String returnInitiatedFrom = postReturnCreationActivityDTO.getReturnInitiatedFrom();
        Integer itemId = null;
        Optional<Integer> itemIdOpt = purchaseOrderDetailsDTO.getUwOrders().stream().filter(uwOrderDTO -> Objects.equals(uwOrderDTO.getUwItemId(), item.getUwItemId())).map(UwOrderDTO::getItemId).findFirst();
        if(itemIdOpt.isPresent()){
            itemId = itemIdOpt.get();
        }

        saveReturnSaleDetails(postReturnCreationActivityDTO.getReturnId(), returnCreationRequest, item.getNeedApproval());
        saveCourierEvents(reverseCourierDetail, returnCreationRequest, returnId, returnRequestId);
        saveReturnCourierDetail(reverseCourierDetail, returnId);
        List<ReturnDetailReason> returnReasons = saveReturnReasonsForItem(source, incrementId, returnId, item, item.getUwItemId());
        ReturnDetailItem returnItem = returnOrderItemService.createReturnItem(item, uwOrder, returnId, returnType, returnReasons, source, itemId);
        returnOrderAddressUpdateService.saveReturnAddressDetails(returnCreationRequest, Math.toIntExact(groupId), incrementId);
        saveReturnStatusEvent(returnRequestId, returnId, returnStatus.getName(), source, returnCreationRequest.getReturnMethod());
        saveEnforceRefundAtStore(returnRequestId, returnId, returnCreationRequest);
        updateReturnRefundRule(uwOrder.getUwItemId(), returnId, source);
        updateDispatchPointsInReturnRequest(returnId, returnRequestId, source);
        saveOrderLevelComments(uwOrder, returnCreationRequest, source, orderId, groupId);
        Integer virtualB2BUwItemId = updateB2BOrderDetails(returnCreationRequest, item, uwOrder, purchaseOrderDetailsDTO.getOrdersHeaderDTO(), returnStatus, groupId, item.getNeedApproval(), reverseCourierDetail, purchaseOrderDetailsDTO.getUwOrders(), returnId, returnRequestId, source);
        reverseCourierDetailService.assignCourier(returnCreationRequest, returnOrderActionService.getReturnOrderStatusById(returnId), uwOrder, returnReasons, reverseCourierDetail, returnId, returnCreationDate, groupId, item, purchaseOrderDetailsDTO, orderId);
        orderSyncService.saveOrderSync(incrementId, CLOSED, BLANK);
        refundRequestService.initiateRefund(returnRequestId, returnCreationRequest, item, uwOrder, returnId, returnCreatedNow, returnInitiatedFrom, virtualB2BUwItemId);
        callBackSyncApi(returnCreationRequest, uwOrder, returnStatus, source, item);
        callMarkDispensingApi(returnCreationRequest, uwOrder, reverseCourierDetail, groupId);
        syncReturnToFinance(returnCreationRequest, returnItem, returnRequestId, postReturnCreationActivityDTO.getItem(), uwOrder);
        log.info("[ReturnCreationServiceImpl][performPostReturnCreationActivities] exit order : {}", returnCreationRequest.getIncrementId());
    }

    private void updateDispatchPointsInReturnRequest(Integer returnId, Integer returnRequestId, String source) {
        log.info("[updateDispatchPointsInRequest] returnId : {}, returnRequestId : {}, source : {}", returnId, returnRequestId, source);
        try{
            Optional<ReturnRequest> returnRequestOptional = returnRequestRepository.findById(returnRequestId);
            if(returnRequestOptional.isPresent()){
                ReturnRequest returnRequest = returnRequestOptional.get();
                ReturnRefundRule returnRefundRule = returnRefundRuleService.getReturnRefundRule(returnId, source);
                if(returnRefundRule != null){
                    String refundDispatch = returnRefundRuleService.getRefundDispatch(returnRefundRule);
                    String exchangeDispatch = returnRefundRuleService.getExchangeDispatch(returnRefundRule);
                    returnRequest.setRefundDispatch(refundDispatch);
                    returnRequest.setExchangeDispatch(exchangeDispatch);
                    log.info("[updateDispatchPointsInRequest] refundDispatchPoint : {}, exchangeDispatch : {}", refundDispatch, exchangeDispatch);
                    returnRequestRepository.save(returnRequest);
                }
            }
        }catch (Exception exception){
            log.error("[updateDispatchPointsInReturnRequest] exception occurred : {}", exception.getMessage());
        }
    }

    private void saveEnforceRefundAtStore(Integer returnRequestId, Integer returnId, ReturnCreationRequestDTO returnCreationRequest) {
        if (returnCreationRequest.isEnforceRefundAtStore() && Constant.RETURN_SOURCE.POS.equalsIgnoreCase(returnCreationRequest.getReturnSource().getSource())) {
            returnEventService.createReturnEvent(returnRequestId, returnId, ENFORCED_RETURN_AT_STORE, "POS return for : "+returnId);
        }
    }

    private void updateReturnRefundRule(Integer uwItemId, Integer returnId, String source){
        returnRefundRuleService.updateReturnRefundRule(uwItemId, returnId, source);
    }

    private List<ReturnDetailReason> saveReturnReasonsForItem(String source, Integer incrementId, Integer returnId, ReturnItemDTO item, int uwItemId) throws Exception{
        return returnReasonService.saveReturnReasonsForItem(source, incrementId, returnId, item, uwItemId);
    }
    private void callMarkDispensingApi(ReturnCreationRequestDTO returnCreationRequest, UwOrderDTO uwOrderDTO, ReverseCourierDetail reverseCourierDetail, Long groupId) {
        log.info("[callMarkDispensingApi] order : {}", uwOrderDTO.getIncrementId());
        if (!"POS".equalsIgnoreCase(returnCreationRequest.getReturnSource().getSource()) && reverseCourierDetail != null && Constant.COURIER.LKART.equalsIgnoreCase(reverseCourierDetail.getCourier())) {
            MarkDispensingDTO markDispensingDTO = MarkDispensingDTO.builder()
                    .incrementId(uwOrderDTO.getIncrementId())
                    .groupId(groupId)
                    .build();
            kafkaService.pushToKafka(MARK_DISPENSING_QUEUE, String.valueOf(uwOrderDTO.getIncrementId()), markDispensingDTO);
        }
    }

    private void syncReturnToFinance(ReturnCreationRequestDTO returnCreationRequest, ReturnDetailItem returnItem, Integer requestId, ReturnItemDTO returnItemDTO, UwOrderDTO uwOrderDTO) {
        log.info("[syncReturnToFinance] requestId : {}", requestId);
        if ("POS".equalsIgnoreCase(returnCreationRequest.getReturnSource().getSource()) && Boolean.FALSE.equals(returnItemDTO.getNeedApproval())) {
            ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
            returnCreateRequest.setUwItemId(returnItem.getUwItemId());
            id365FinanceService.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
            id365FinanceService.createReturnEInvoice(returnItem.getReturnId(), returnItem.getUwItemId(), uwOrderDTO.getFacilityCode());
        }
    }

    public Integer updateB2BOrderDetails(ReturnCreationRequestDTO returnCreationRequest, ReturnItemDTO item, UwOrderDTO uwOrder, OrdersHeaderDTO ordersHeaderDTO, ReturnStatus customerStatus, long group_id, boolean needApproval, ReverseCourierDetail reverseCourierDetail, List<UwOrderDTO> uwOrderDTOs, Integer returnId, Integer returnRequestId, String source) {
        log.info("[ReturnActionImpl:updateB2BOrderDetails] update details for b2b order for item {}", item.getUwItemId());
        Integer virtualB2BUwItemId = null;
        QCType qcType = reverseCourierDetail != null ? reverseCourierDetail.getCourierAssignmentType() : null;
        if (returnUtil.isB2BOrder(uwOrder)) {
            try {
                log.info("[updateB2BOrderDetails][items] : " + uwOrder.getUwItemId());
                log.info("[updateB2BOrderDetails] uw_item_id " + uwOrder.getUwItemId() + " of increment id " + uwOrder.getIncrementId());
                log.info("[updateB2BOrderDetails] Status to update is " + customerStatus + " and need approval flag is " + needApproval);
                log.info("[updateB2BOrderDetails] Need approval flag for uw_item_id " + uwOrder.getUwItemId() + " of increment id " + uwOrder.getIncrementId() + " is :: " + needApproval);
                Optional<ReturnDetail> optionalReturnDetail = returnOrderActionService.findReturnOrderById(returnId);
                ReturnDetail returnOrder = null;
                if (optionalReturnDetail.isPresent()) {
                    returnOrder = optionalReturnDetail.get();
                    log.info("currently receiving flag of return id " + returnOrder.getId() + " is " + returnOrder.getReceivingFlag());
                    if (("pos".equalsIgnoreCase(returnOrder.getSource()) || "wh".equalsIgnoreCase(returnOrder.getSource()) || Constant.DIRECT_RECEIVING.equalsIgnoreCase(returnOrder.getSource()) || "DirectReceiving".equalsIgnoreCase(returnOrder.getReturnMethod())) && !(customerStatus == ReturnStatus.RETURN_EXPECTED_POS || customerStatus == ReturnStatus.RETURN_EXPECTED_WH || customerStatus == ReturnStatus.NEW_REVERSE_PICKUP)) {
                        if (!needApproval) {
                            String lkCountry = getLkCountry(ordersHeaderDTO);
                            if ("DirectReceiving".equalsIgnoreCase(returnOrder.getReturnMethod())) {
                                log.info("updating receiving flag of return id " + returnOrder.getId() + " as yes becuse its not need approval flow");
                                callBackSyncApi(uwOrder, Constant.TRACKING_STATUS.RETURNED);
                            }
                            if (lkCountry == null || bypassReturnReceivingCountries.stream().noneMatch(lkCountry::equalsIgnoreCase)) {
                                log.info("updating receiving flag as yes");
                                returnOrder.setReceivingFlag("yes");
                            }
                        }
                    }
                    log.info("[updateB2BOrderDetails] updating return details for b2b order for uw_item_id {} returnDetails {}", uwOrder.getUwItemId(), returnOrder);
                    returnOrderActionService.saveReturnOrder(returnOrder);
                }

                String classification = uwOrder.getClassification();
                if (11356 == Integer.parseInt(classification)) {
                    log.info("[updateB2BOrderDetails][product][skip][classification] : 11356");
                    return -1;
                }

                UwOrderDTO uwOrderB2B = null;

                log.info("[updateB2BOrderDetails] uwItemId : {}, fetching b2buworder for ref id {} ", uwOrder.getUwItemId(), uwOrder.getB2bRefrenceItemId());
                uwOrderB2B = uwOrderDTOs.stream().filter(uwOrderDTO -> Objects.equals(uwOrderDTO.getB2bRefrenceItemId(), item.getUwItemId())).findFirst().orElse(null);

                if(uwOrderB2B != null){
                    log.info("[updateB2BOrderDetails] uwItemId : {}, facilityCode : {}, faciltyCodeList : {}", uwOrderB2B.getUwItemId(), uwOrderB2B.getFacilityCode(), physicalFacilities);
                    virtualB2BUwItemId = !physicalFacilities.contains(uwOrderB2B.getFacilityCode()) ? uwOrderB2B.getUwItemId() : uwOrder.getUwItemId();
                    ReturnItemDTO itemB2B = getB2BReturnItem(uwOrderB2B, item);
                    Optional<ReturnDetail> returnDetailB2BOpt = returnOrderActionService.getExistingReturnDetailsByItem(uwOrderB2B.getUwItemId());
                    boolean existingReturnDetailUsed = returnDetailB2BOpt.isPresent() && !returnOrderActionService.checkIsReturnOrderCancelled(returnDetailB2BOpt.get());
                    ReturnDetail returnOrderB2B = returnOrderActionService.persistB2BReturn(uwOrderB2B, group_id, returnOrder, qcType, existingReturnDetailUsed, returnRequestId, returnCreationRequest.getFacilityCode(), itemB2B);
                    List<ReturnDetailReason> returnReasonsB2B = returnReasonService.saveReturnReasonsForItem(source, returnOrderB2B.getIncrementId(), returnOrderB2B.getId(), itemB2B, uwOrderB2B.getUwItemId());
                    if(ReturnSources.POS.getSource().equalsIgnoreCase(source) && ReturnStatus.RETURN_RECEIVED.getStatus().equalsIgnoreCase(customerStatus.getName())){
                        returnEventService.persistEvent(returnRequestId, returnOrderB2B.getId(), ReturnStatus.RETURN_RECEIVED_AT_STORE.getName(), "saving b2b return detail object. " + returnOrderB2B.getId());
                    }else{
                        returnEventService.persistEvent(returnRequestId, returnOrderB2B.getId(), customerStatus.getName(), "saving b2b return detail object. " + returnOrderB2B.getId());
                    }
                    returnOrderItemService.createReturnItem(itemB2B, uwOrderB2B, returnOrderB2B.getId(), returnOrderB2B.getReturnType(), returnReasonsB2B, source, uwOrderB2B.getItemId());
                    orderSyncService.saveOrderSync(returnOrderB2B.getIncrementId(), CLOSED, BLANK);
                }

            } catch (Exception exception) {
                log.error("[updateB2BOrderDetails] error occurred : " + exception);
            }
        }
        log.info("[updateB2BOrderDetails] virtualB2BUwItemId : {}", virtualB2BUwItemId);
        return virtualB2BUwItemId;
    }

    private ReturnItemDTO getB2BReturnItem(UwOrderDTO uwOrderB2B, ReturnItemDTO returnItem) {
        ReturnItemDTO returnItemB2BDTO = new ReturnItemDTO();
        returnItemB2BDTO.setUwItemId(uwOrderB2B.getUwItemId());
        returnItemB2BDTO.setQcStatus(returnItem.getQcStatus());
        returnItemB2BDTO.setReasons(returnItem.getReasons());
        returnItemB2BDTO.setNeedApproval(returnItem.getNeedApproval());
        returnItemB2BDTO.setClaimInsurance(returnItem.getClaimInsurance());
        returnItemB2BDTO.setRefundMethodRequest(returnItem.getRefundMethodRequest());
        returnItemB2BDTO.setMagentoId(returnItem.getMagentoId());
        returnItemB2BDTO.setRefundMethod(returnItem.getRefundMethod());
        return returnItemB2BDTO;
    }

    private ReturnStatus fetchReturnStatusBySourceAndReturnMethod(String source, String returnMethod, boolean needApproval, boolean isInsurance, Integer orderId, String unicomOrderCode) {

        if (!(StringUtils.isEmpty(source) && StringUtils.isEmpty(returnMethod))) {
            if (ReturnSources.VSM.getSource().equalsIgnoreCase(source) && needApproval) {
                return ReturnStatus.RETURN_NEED_APPROVAL;
            } else if (ReturnSources.POS.getSource().equalsIgnoreCase(source) && needApproval) {
                return checkForWarehouseApproval(orderId, unicomOrderCode, isInsurance);
            } else if (ReturnSources.WEB.getSource().equalsIgnoreCase(source) && "returntoNearbyStore".equalsIgnoreCase(returnMethod)) {
                return ReturnStatus.RETURN_EXPECTED_POS;
            } else if (ReturnSources.WEB.getSource().equalsIgnoreCase(source) && "ShiptoLenskrt".equalsIgnoreCase(returnMethod)) {
                return ReturnStatus.RETURN_EXPECTED_WH;
            } else if (ReturnSources.POS.getSource().equalsIgnoreCase(source) && "StoreReceiving".equalsIgnoreCase(returnMethod)) {
                return ReturnStatus.RETURN_RECEIVED;
            } else if (ReturnSources.WAREHOUSE.getSource().equalsIgnoreCase(source) && "DirectReceiving".equalsIgnoreCase(returnMethod)) {
                return ReturnStatus.RETURN_RECEIVED;
            }
        }

        return ReturnStatus.NEW_REVERSE_PICKUP;
    }

    private ReturnStatus checkForWarehouseApproval(Integer orderId, String unicomOrderCode, boolean isInsurance) {
        log.info("[ReturnOrderActionServiceImpl][checkForWarehouseApproval] Checking delivery date for order: {}, unicomcode: {}", orderId, unicomOrderCode);
        ResponseEntity<List<ShippingStatusDetail>> shippingDetailsResponse = orderOpsFeignClient.getShippingDetails(orderId, unicomOrderCode);
        if (shippingDetailsResponse.getStatusCode().is2xxSuccessful()) {
            List<ShippingStatusDetail> shippingStatusDetails = shippingDetailsResponse.getBody();
            if (!CollectionUtils.isEmpty(shippingStatusDetails) && !isInsurance) {
                Date completeTime = shippingStatusDetails.get(0).getComplete_time();
                LocalDate currentDate = LocalDate.now();
                LocalDate thresholdDate = currentDate.minusDays(getDaysFromSystemPref());

                if (completeTime != null) {
                    LocalDate completeTimeLocalDate = completeTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                    if (completeTimeLocalDate.isBefore(thresholdDate)) {
                        log.info("[ReturnActionImpl][checkForWarehouseApproval] Order is older than {}, Sending approval to warehouse team", thresholdDate);
                        return ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE;
                    }
                }
            }
        }

        return ReturnStatus.RETURN_NEED_APPROVAL;
    }

    private Integer getDaysFromSystemPref() {
        int thresholdDays = 180;
        String systemPrefDate = nexsFacilityService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.WAREHOUSE_APPROVAL_THRESHOLD_DAYS, Constant.SYSTEM_PREFERENCE_GROUPS.RETURNS);
        if (systemPrefDate != null && !systemPrefDate.isEmpty()) {
            thresholdDays = Integer.parseInt(systemPrefDate);
        }

        log.info("[ReturnOrderActionServiceImpl][getDaysFromSystemPref] Sending to warehouse team if order is older than: {} days", thresholdDays);
        return thresholdDays;
    }

    private void saveOrderLevelComments(UwOrderDTO uwOrder, ReturnCreationRequestDTO returnCreationRequest, String source, Integer orderId, Long groupId) {
        log.info("[saveOrderLevelComments] saving comment for order : {}", uwOrder.getIncrementId());
        orderUtilsService.insertOrderComment("Return initiated from " +source+" for " + uwOrder.getProductValue() + " through return_Api", uwOrder.getIncrementId());
        ReturnDetailAddressUpdateDTO returnAddressUpdate = returnOrderAddressUpdateService.getReturnAddressUpdate(groupId);
        if (null != returnAddressUpdate && !StringUtils.isEmpty(returnAddressUpdate.getFirstName())) {
            orderUtilsService.insertOrderComment("Reverse Pickup Address updated --- " + returnAddressUpdate.getFirstName() + " " + returnAddressUpdate.getLastName() + " , " + returnAddressUpdate.getStreet1() + " " + returnAddressUpdate.getStreet2() + ", " + returnAddressUpdate.getCity() + ", " + returnAddressUpdate.getState() + ", " + returnAddressUpdate.getCountry() + ", " + returnAddressUpdate.getPostcode() + ", " + returnAddressUpdate.getTelephone() + ", " + returnAddressUpdate.getEmail(), uwOrder.getIncrementId());
        }
    }

    private void callBackSyncApi(ReturnCreationRequestDTO returnCreationRequest, UwOrderDTO uwOrder, ReturnStatus returnStatus, String source, ReturnItemDTO item) {
        log.info("[callBackSyncApi] order : {}",uwOrder.getIncrementId());
        if("POS".equalsIgnoreCase(source) && returnStatus != null && ReturnStatus.RETURN_NEED_APPROVAL.getStatus().equalsIgnoreCase(returnStatus.getStatus()) && Boolean.TRUE.equals(item.getNeedApproval())){
            callBackSyncApi(uwOrder, Constant.TRACKING_STATUS.RETURN_NEED_APPROVAL_PENDING);
        }
        else if (!"DirectReceiving".equalsIgnoreCase(returnCreationRequest.getReturnMethod())) {
            callBackSyncApi(uwOrder, Constant.TRACKING_STATUS.RETURN_REQUESTED);
        }
    }

    private void callBackSyncApi(UwOrderDTO uwOrder, String trackingStatus) {
        BackSyncStatusDTO backSyncStatusDTO = BackSyncStatusDTO.builder()
                .trackingStatus(trackingStatus)
                .uwItemId(uwOrder.getUwItemId())
                .incrementId(uwOrder.getIncrementId())
                .build();
        kafkaService.pushToKafka(BACK_SYNC_STATUS_QUEUE, String.valueOf(uwOrder.getIncrementId()), backSyncStatusDTO);
    }
}
