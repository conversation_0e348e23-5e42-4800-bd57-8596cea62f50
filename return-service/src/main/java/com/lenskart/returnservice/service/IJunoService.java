package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.request.FrameBrokenApprovalRequest;
import com.lenskart.returncommon.model.response.FrameBrokenApprovalResponse;

import java.util.Map;

import com.lenskart.platform.fl.utils.dto.giftCard.JunoGiftCardResponse;

public interface IJunoService {
    Map<String, Object> getProductDetails(int productId);

    FrameBrokenApprovalResponse processFrameBrokenApprovalRequest(FrameBrokenApprovalRequest frameBrokenApprovalRequest);

    boolean isFrameBrokenApprovedItem(Integer itemId);

    String getFrameBrokenOrderFlag(Integer incrementId);

    JunoGiftCardResponse getGiftCardDetails(int orderId, String itemIds) throws Exception;
}
