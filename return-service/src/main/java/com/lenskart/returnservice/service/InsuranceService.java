package com.lenskart.returnservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.returncommon.exception.ReturnNotFound;
import com.lenskart.returncommon.model.dto.insurance.InsuranceEligibilityRequest;
import com.lenskart.returncommon.model.dto.insurance.InsuranceEligibilityResponse;
import com.lenskart.returncommon.model.request.InsuranceClaimUpdateKafkaRequest;
import com.lenskart.returncommon.model.request.InsuranceReturnKafkaRequest;
import com.lenskart.returncommon.model.response.ClaimUpdateResponse;

public interface InsuranceService {

    public InsuranceEligibilityResponse getInsurancEligibilityDetailsForItems(Integer incrementId, InsuranceEligibilityRequest insuranceEligibilityRequest);
    void pushInsuranceReturnRequestToKafka(InsuranceReturnKafkaRequest insuranceReturnKafkaRequest);
    void pushUpdateClaimKafkaRequest(Integer returnId, String claimStatus, String reason);
    ClaimUpdateResponse updateInsuranceClaim(InsuranceClaimUpdateKafkaRequest insuranceClaimUpdateKafkaRequest) throws ReturnNotFound, JsonProcessingException,Exception;
    void manualInsuranceUpdate();
}