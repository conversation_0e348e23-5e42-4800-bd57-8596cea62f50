package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.exception.ReturnNotFound;
import com.lenskart.returncommon.model.dto.insurance.*;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ExternalOpticalClaimUpdateRequest;
import com.lenskart.returncommon.model.request.InsuranceClaimUpdateKafkaRequest;
import com.lenskart.returncommon.model.request.InsuranceReturnKafkaRequest;
import com.lenskart.returncommon.model.response.ClaimUpdateResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returncommon.utils.SymboUtil;
import com.lenskart.returnrepository.entity.DelightAction;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.DelightActionRepository;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.ORDERS_DTO;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_STATUS_HISTORY_QUEUE;

@Slf4j
@Service
public class InsuranceServiceImpl implements InsuranceService {

    private static final String Insurance_Raise_Claim_Request_Queue="Insurance-Raise-Claim-Request-Queue";
    private final static int MAX_RETRY = 3;
    @Value("${itemwise.cache.expiry:300}")
    private String itemWiseCacheExpiry;
    @Value("${insurance.hub.service.base.url:http://insurance-hub-service.scm.preprod-eks.internal/}")
    private String insuranceHubServiceBaseUrl;
    private RestTemplate restTemplate;

    @Autowired
    private ICacheDataService cacheDataService;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    SymboUtil symboUtil;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;
    @Autowired
    private IKafkaService kafkaService;
    private ObjectMapper objectMapper;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private DelightActionRepository delightActionRepository;

    @Autowired
    private ReturnUtil returnUtil;

    @PostConstruct
    public void init(){
        restTemplate = new RestTemplate();
        objectMapper = new ObjectMapper();
    }

    @Override
    public InsuranceEligibilityResponse getInsurancEligibilityDetailsForItems(Integer incrementId, InsuranceEligibilityRequest insuranceEligibilityRequest) {
        log.info("[InsuranceServiceImpl][getInsurancEligibilityDetailsForItems] Request for getInsuranceDetails: " + insuranceEligibilityRequest.toString());
        InsuranceEligibilityResponse insuranceEligibilityResponse = new InsuranceEligibilityResponse();
        if (isInvalidRequest(incrementId, insuranceEligibilityRequest)) {
            log.info("[InsuranceServiceImpl][getInsurancEligibilityDetailsForItems] Invalid Request: IncrementId or MagentoItemIds are not in the request");
            insuranceEligibilityResponse.setErrorMessage("Invalid Request: IncrementId or MagentoItemIds are not in the request");
            return insuranceEligibilityResponse;
        }
        List<OrdersDTO> orders = getOrders(incrementId, insuranceEligibilityRequest);

        if (orders == null || orders.isEmpty()) {
            log.info("[InsuranceServiceImpl][getInsurancEligibilityDetailsForItems] Not Found: IncrementId or MagentoItemIds are not found");
            insuranceEligibilityResponse.setErrorMessage("Not Found: IncrementId or MagentoItemIds are not found");
            return insuranceEligibilityResponse;
        }
        List<Integer> productIds = extractProductIds(orders);
        Boolean isInsuranceOrder = checkIfInsuranceOrder(productIds);
        log.info("[getInsurancEligibilityDetailsForItems] isInsuranceOrder : {}", isInsuranceOrder);
        if (!isInsuranceOrder) {
            insuranceEligibilityResponse.setErrorMessage("Insurance product is not available in order");
            return insuranceEligibilityResponse;
        }

        ExternalOpticalInsuranceClaimDetailResponse externalOpticalInsuranceClaimDetailResponse = null;
        externalOpticalInsuranceClaimDetailResponse = symboUtil.getInsuranceClaimDetails(incrementId);
        if (externalOpticalInsuranceClaimDetailResponse == null || externalOpticalInsuranceClaimDetailResponse.getStatus() != 200) {
            insuranceEligibilityResponse.setErrorMessage(externalOpticalInsuranceClaimDetailResponse == null ?
                    "There is some problem with insurance hub service. Please try again after some time" :
                    externalOpticalInsuranceClaimDetailResponse.getMessage());
            return insuranceEligibilityResponse;
        }

        LenskartClaimDetailStatusResponse claim = externalOpticalInsuranceClaimDetailResponse.getClaim();
        Boolean isClaimAllowed = (claim != null && claim.getIsClaimAllowed() != null) ? claim.getIsClaimAllowed() : false;

        List<ItemsInsuranceDetail> itemsInsuranceDetailList = getItemsInsuranceDetailList(insuranceEligibilityRequest.getMagentoItemIds(),
                externalOpticalInsuranceClaimDetailResponse,
                isClaimAllowed);

        insuranceEligibilityResponse.setItemsInsuranceDetailList(itemsInsuranceDetailList);
        insuranceEligibilityResponse.setIncrementId(incrementId);

        return insuranceEligibilityResponse;
    }

    @Override
    public void pushInsuranceReturnRequestToKafka(InsuranceReturnKafkaRequest insuranceReturnKafkaRequest) {
        if(insuranceReturnKafkaRequest!=null){
            log.info("[InsuranceServiceImpl][pushInsuranceReturnRequestToKafka] request for raise claim kafka push"+insuranceReturnKafkaRequest.toString());
            try {
                orderOpsFeignClient.pushData(objectMapper.convertValue(insuranceReturnKafkaRequest, Map.class),Insurance_Raise_Claim_Request_Queue);
            } catch (Exception e) {
                log.error("[InsuranceServiceImpl][pushInsuranceReturnRequestToKafka] exception occured while pushing raise claim request to kafka:"+e.getMessage());
            }
        }
    }

    @Override
    public void pushUpdateClaimKafkaRequest(Integer returnId, String claimStatus, String reason) {
        log.info("[ReturnUpdateServiceImpl][pushUpdateClaimKafkaRequest] Insurance order return update with return status " + claimStatus + " return id " + returnId);
        if (ReturnStatus.RETURN_ACCEPTED.getStatus().equalsIgnoreCase(claimStatus) || ReturnStatus.RETURN_REJECTED.getStatus().equalsIgnoreCase(claimStatus) || ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus().equalsIgnoreCase(claimStatus) || ReturnStatus.CANCELLED.getStatus().equalsIgnoreCase(claimStatus)) {
            if (ReturnStatus.RETURN_ACCEPTED.getStatus().equalsIgnoreCase(claimStatus))
                claimStatus = "APPROVED";
            else if (ReturnStatus.RETURN_REJECTED.getStatus().equalsIgnoreCase(claimStatus) || ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus().equalsIgnoreCase(claimStatus) || ReturnStatus.CANCELLED.getStatus().equalsIgnoreCase(claimStatus))
                claimStatus = "REJECTED";
            InsuranceClaimUpdateKafkaRequest insuranceClaimUpdateKafkaRequest = new InsuranceClaimUpdateKafkaRequest();
            insuranceClaimUpdateKafkaRequest.setReturnId(returnId);
            insuranceClaimUpdateKafkaRequest.setClaimStatus(claimStatus);
            insuranceClaimUpdateKafkaRequest.setRetryCount(1);
            insuranceClaimUpdateKafkaRequest.setReason(reason);
            log.info("[ReturnUpdateServiceImpl][pushUpdateClaimKafkaRequest] request for update claim kafka push" + insuranceClaimUpdateKafkaRequest);
            try {
                if("APPROVED".equalsIgnoreCase(claimStatus) || "REJECTED".equalsIgnoreCase(claimStatus)){
                    orderOpsFeignClient.pushData(objectMapper.convertValue(insuranceClaimUpdateKafkaRequest, Map.class),Constant.RETURN_TOPICS.Insurance_Update_Claim_Request_Queue);
                    returnEventService.createReturnEvent(null, returnId, Constant.EVENT.PUSHED_TO_INSURANCE_UPDATE_CLAIM_QUEUE, reason);
                }
            } catch (Exception e) {
                log.error("[InsuranceServiceImpl][pushInsuranceReturnRequestToKafka] exception occured while pushing update claim request to kafka:" + e.getMessage());
            }
        }
    }

    @Override
    public ClaimUpdateResponse updateInsuranceClaim(InsuranceClaimUpdateKafkaRequest insuranceClaimUpdateKafkaRequest) throws ReturnNotFound, JsonProcessingException, Exception {
        ClaimUpdateResponse claimUpdateResponse = null;
        log.info("[InsuranceClaimUpdateMessageProcessor] updateInsuranceClaimUpdateResponse is " + insuranceClaimUpdateKafkaRequest);
        try {
            ExternalOpticalClaimUpdateRequest externalOpticalClaimUpdateRequest = getExternalOpticalClaimUpdateRequest(insuranceClaimUpdateKafkaRequest);
            claimUpdateResponse = updateInsuranceClaim(externalOpticalClaimUpdateRequest);
            checkResponseAndRetry(insuranceClaimUpdateKafkaRequest, claimUpdateResponse);
            insertClaimUpdateCommentsToVSM(claimUpdateResponse, insuranceClaimUpdateKafkaRequest, externalOpticalClaimUpdateRequest);
        } catch (ReturnNotFound e) {
            log.error("[updateInsuranceService] return not found in new service : " + e);
        } catch (JsonProcessingException e) {
            log.error("[updateInsuranceService]  : exception while pushing to saving vsm comments" + e);
        } catch (Exception e) {
            log.error("[updateInsuranceService] exception while updating insurance : " + e);
            throw e;
        }
        return claimUpdateResponse;
    }

    @Override
    public void manualInsuranceUpdate() {
        Date startDate, endDate;
        try{
            String startTime = systemPreferenceService.getSystemPreferenceValues("insurance_update", "startTime", 15, TimeUnit.MINUTES);
            String endTime = systemPreferenceService.getSystemPreferenceValues("insurance_update", "endTime", 15, TimeUnit.MINUTES);
            String returnId = systemPreferenceService.getSystemPreferenceValues("insurance_update", "returnId", 5, TimeUnit.SECONDS);
            log.info("[manualInsuranceUpdate] startTime : {}, endTime : {}, returnId : {}", startTime, endTime, returnId);
            startDate = DateUtil.getDate(startTime);
            endDate = DateUtil.getDate(endTime);
            if(!"0".equalsIgnoreCase(returnId)){
                List<Integer> returnIdList = Arrays.stream(returnId.split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .toList();
                for(Integer insuranceReturnId : returnIdList){
                    List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(insuranceReturnId);
                    if(!CollectionUtils.isEmpty(returnEvents)){
                        for(ReturnEvent returnEvent : returnEvents){
                            if("PUSHED_TO_INSURANCE_CLAIM_QUEUE".equalsIgnoreCase(returnEvent.getEvent())){
                                DelightAction delightAction = delightActionRepository.findTop1ByReturnIdOrderByIdDesc(insuranceReturnId);
                                if(delightAction != null){
                                    String returnStatus = returnOrderActionService.getReturnOrderStatusById(delightAction.getReturnId());
                                    pushUpdateClaimKafkaRequest(delightAction.getReturnId(), returnStatus, delightAction.getComments());
                                }
                            }
                        }
                    }
                }
            }
            else if(startDate != null && endDate != null){
                List<ReturnEvent> returnEvents = returnEventRepository.findByEventInAndCreatedAtBetween(List.of("PUSHED_TO_INSURANCE_CLAIM_QUEUE"), startDate, endDate);
                if(!CollectionUtils.isEmpty(returnEvents)){
                    for(ReturnEvent returnEvent : returnEvents){
                        DelightAction delightAction = delightActionRepository.findTop1ByReturnIdOrderByIdDesc(returnEvent.getReturnId());
                        if(delightAction != null){
                            String returnStatus = returnOrderActionService.getReturnOrderStatusById(delightAction.getReturnId());
                            pushUpdateClaimKafkaRequest(delightAction.getReturnId(), returnStatus, delightAction.getComments());
                        }
                    }
                }
            }
        }catch (Exception exception){
            log.error("[manualInsuranceUpdate] error : "+ exception);
        }
    }

    private boolean isInvalidRequest(Integer incrementId, InsuranceEligibilityRequest insuranceEligibilityRequest) {
        return incrementId == null || insuranceEligibilityRequest.getMagentoItemIds() == null || insuranceEligibilityRequest.getMagentoItemIds().isEmpty();
    }

    public List<Integer> getInsuranceProductId() {

        List<Integer> productInsuranceIdList = null;
        String[] productids;

        Object cachedObject = cacheDataService.getKey(Constant.SYSTEM_PREFERENCE_KEYS.PRODUCT_ID_INSURANCE);
        if (cachedObject instanceof List<?>) {
            // Check if all elements in the list are instances of Integer
            boolean allElementsAreIntegers = ((List<?>) cachedObject).stream().allMatch(element -> element instanceof Integer);
            if (allElementsAreIntegers) {
                productInsuranceIdList = (List<Integer>) cachedObject;
            }
        }
        if (null == productInsuranceIdList && CollectionUtils.isEmpty(productInsuranceIdList)) {
            productInsuranceIdList = new ArrayList<>();
            SystemPreference sysPref1 = systemPreferenceService.findOneByGroupAndKey(Constant.SYSTEM_PREFERENCE_KEYS.PRODUCT_INSURANCE, Constant.SYSTEM_PREFERENCE_KEYS.PRODUCT_ID_INSURANCE);
            productids = sysPref1.getValue().split(",");
            for (String pid : productids
            ) {
                productInsuranceIdList.add(Integer.parseInt(pid));
                log.debug("[InsuranceServiceImpl][getInsuranceProductId] getInsuranceProductId() insurance pid added to list : " + pid);
            }
        }
        cacheDataService.saveMessage(Constant.SYSTEM_PREFERENCE_KEYS.PRODUCT_ID_INSURANCE, productInsuranceIdList, this.itemWiseCacheExpiry);
        log.info("[InsuranceServiceImpl][getInsuranceProductId] productInsuranceIdList " + productInsuranceIdList);
        return productInsuranceIdList;
    }

    private List<OrdersDTO> getOrders(Integer incrementId, InsuranceEligibilityRequest insuranceEligibilityRequest) {
        List<OrdersDTO> orders = insuranceEligibilityRequest.getOrdersDTOList();
        if (orders == null) {
            boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
            ResponseEntity<OrderInfoResponseDTO> orderInfo = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(ORDERS_DTO))
                    : orderOpsFeignClient.getOrderDetails(incrementId);
            orders = orderInfo.getBody() != null ? orderInfo.getBody().getOrders() : null;
        }
        return orders;
    }

    private List<Integer> extractProductIds(List<OrdersDTO> orders) {
        List<Integer> productIds = new ArrayList<>();
        for (OrdersDTO order : orders) {
            productIds.add(order.getProductId());
        }
        return productIds;
    }

    private boolean checkIfInsuranceOrder(List<Integer> productIds) {
        List<Integer> insurancePidList = getInsuranceProductId();
        for (Integer pid : productIds) {
            if (insurancePidList.contains(pid)) {
                log.info("[getInsurancEligibilityDetailsForItems] insurance order due to pid : {}", pid);
                return true;
            }
        }
        return false;
    }

    private List<ItemsInsuranceDetail> getItemsInsuranceDetailList(List<Integer> magentoItemIds,
                                                                   ExternalOpticalInsuranceClaimDetailResponse externalOpticalInsuranceClaimDetailResponse,
                                                                   Boolean isInsuranceEligibleForOrder) {

        List<ItemsInsuranceDetail> itemsInsuranceDetailList = new ArrayList<>();
        Map<Integer, Product> magentoItemIdToInsuranceItemMap = mapMagentoItemIdToInsuranceItem(externalOpticalInsuranceClaimDetailResponse.getProducts());

        for (Integer magentoItemId : magentoItemIds) {
            ItemsInsuranceDetail itemsInsuranceDetail = new ItemsInsuranceDetail();
            itemsInsuranceDetail.setMagentoItemId(magentoItemId);
            itemsInsuranceDetail.setIsInsuranceEligible(isInsuranceEligibleForOrder);

            if (magentoItemIdToInsuranceItemMap.get(magentoItemId) != null) {
                Product insuranceItem = magentoItemIdToInsuranceItemMap.get(magentoItemId);
                itemsInsuranceDetail.setSumInsured(insuranceItem.getSumInsured());
                itemsInsuranceDetail.setMaximumClaimableAmount(insuranceItem.getMaximumClaimableAmount());
                itemsInsuranceDetail.setCopayAmount(insuranceItem.getCopayAmount());
            }
            itemsInsuranceDetailList.add(itemsInsuranceDetail);
        }

        return itemsInsuranceDetailList;
    }

    private Map<Integer, Product> mapMagentoItemIdToInsuranceItem(List<Product> insuranceItems) {
        Map<Integer, Product> magentoItemIdToInsuranceItemMap = new HashMap<>();
        if (insuranceItems != null) {
            for (Product insuranceItem : insuranceItems) {
                magentoItemIdToInsuranceItemMap.put(Integer.parseInt(insuranceItem.getProductId()), insuranceItem);
            }
        }
        return magentoItemIdToInsuranceItemMap;
    }

    private ExternalOpticalClaimUpdateRequest getExternalOpticalClaimUpdateRequest(InsuranceClaimUpdateKafkaRequest insuranceClaimUpdateKafkaRequest) throws ReturnNotFound {
        ReturnDetail returnOrder = returnOrderActionService.findReturnOrderById(insuranceClaimUpdateKafkaRequest.getReturnId()).orElse(null);
        if (Objects.isNull(returnOrder)) {
            throw new ReturnNotFound();
        }
        ExternalOpticalClaimUpdateRequest externalOpticalClaimUpdateRequest = new ExternalOpticalClaimUpdateRequest();
        externalOpticalClaimUpdateRequest.setInvoiceNumber(String.valueOf(returnOrder.getIncrementId()));
        externalOpticalClaimUpdateRequest.setClaimStatus(insuranceClaimUpdateKafkaRequest.getClaimStatus());
        externalOpticalClaimUpdateRequest.setReason(insuranceClaimUpdateKafkaRequest.getReason());
        externalOpticalClaimUpdateRequest.setExchangeOrderInvoiceUrl(Collections.singletonList(insuranceClaimUpdateKafkaRequest.getExchangeInvoiceUrl()));
        return externalOpticalClaimUpdateRequest;
    }

    private ClaimUpdateResponse updateInsuranceClaim(ExternalOpticalClaimUpdateRequest externalOpticalClaimUpdateRequest) {
        ClaimUpdateResponse claimUpdateResponse = null;
        log.info("[updateInsuranceClaim]update claim details " + externalOpticalClaimUpdateRequest.toString());
        String claimUpdateUrl = insuranceHubServiceBaseUrl + "v1/insurance/claim";
        log.info("[updateInsuranceClaim] updateClaimUrl :: " + claimUpdateUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<Object>(externalOpticalClaimUpdateRequest, headers);
        try {
            HttpEntity<ClaimUpdateResponse> claimUpdateResponseHttpEntity = restTemplate.exchange(claimUpdateUrl, HttpMethod.PUT, entity, ClaimUpdateResponse.class);
            log.info("[updateInsuranceClaim] update claim api response: " + claimUpdateResponseHttpEntity.toString());
            if (claimUpdateResponseHttpEntity.getBody() != null) {
                claimUpdateResponse = claimUpdateResponseHttpEntity.getBody();
            }
        } catch (Exception e) {
            log.error("[updateInsuranceClaim] Exception while calling insurance hub service update claim", e);
        }
        return claimUpdateResponse;
    }

    private void checkResponseAndRetry(InsuranceClaimUpdateKafkaRequest insuranceClaimUpdateKafkaRequest, ClaimUpdateResponse response) throws Exception {
        if (insuranceClaimUpdateKafkaRequest.getRetryCount() <= MAX_RETRY && response == null) {
            log.info("call to insurance hub service failed for " + insuranceClaimUpdateKafkaRequest.toString());
            insuranceClaimUpdateKafkaRequest.setRetryCount((insuranceClaimUpdateKafkaRequest.getRetryCount() + 1));
            kafkaService.pushToKafka(Constant.RETURN_TOPICS.Insurance_Update_Claim_Request_Queue, String.valueOf(insuranceClaimUpdateKafkaRequest.getReturnId()), new ObjectMapper().writeValueAsString(insuranceClaimUpdateKafkaRequest));
        } else if (insuranceClaimUpdateKafkaRequest.getRetryCount() <= MAX_RETRY && response.getStatus() != HttpStatus.OK.value()) {
            log.info("call to insurance hub service failed for " + insuranceClaimUpdateKafkaRequest.toString());
            insuranceClaimUpdateKafkaRequest.setRetryCount((insuranceClaimUpdateKafkaRequest.getRetryCount() + 1));
            kafkaService.pushToKafka(Constant.RETURN_TOPICS.Insurance_Update_Claim_Request_Queue, String.valueOf(insuranceClaimUpdateKafkaRequest.getReturnId()), new ObjectMapper().writeValueAsString(insuranceClaimUpdateKafkaRequest));
        } else if (insuranceClaimUpdateKafkaRequest.getRetryCount() > MAX_RETRY) {
            log.info("Raise claim retry count for insurance hub service exceeded" + insuranceClaimUpdateKafkaRequest.toString());
        }
    }

    private void insertClaimUpdateCommentsToVSM(ClaimUpdateResponse claimUpdateResponse, InsuranceClaimUpdateKafkaRequest insuranceClaimUpdateKafkaRequest, ExternalOpticalClaimUpdateRequest externalOpticalClaimUpdateRequest) throws JsonProcessingException {
        if (claimUpdateResponse != null && claimUpdateResponse.getStatus() == HttpStatus.OK.value() && claimUpdateResponse.getSuccess()) {
            Map<String, String> commentsMap = new HashMap<>();
            String updateClaimComment = "";
            if ("APPROVED".equalsIgnoreCase(claimUpdateResponse.getClaimStatus())) {
                updateClaimComment = "Insurance claim is approved by delight team for Ref# " + insuranceClaimUpdateKafkaRequest.getReturnId();
            } else if ("REJECTED".equalsIgnoreCase(claimUpdateResponse.getClaimStatus())) {
                updateClaimComment = "Insurance claim is rejected by delight team for Ref# " + insuranceClaimUpdateKafkaRequest.getReturnId();
            } else if ("CLAIM_SETTLED".equalsIgnoreCase(claimUpdateResponse.getClaimStatus())) {
                updateClaimComment = "Insurance Claim has been setteled from Symbo for #Ref " + insuranceClaimUpdateKafkaRequest.getReturnId();
            }
            Integer orderId = null;
            log.info("[insertClaimUpdateCommentstoVSM] going to insert VSM comment for claim update, comment : " + updateClaimComment);
            OrderCommentReq orderCommentReq = new OrderCommentReq();
            orderCommentReq.setIncrementId(Integer.valueOf(externalOpticalClaimUpdateRequest.getInvoiceNumber()));
            orderCommentReq.setComment(updateClaimComment);
            orderCommentReq.setCommentType("cust");
            kafkaService.pushToKafka(ORDER_STATUS_HISTORY_QUEUE, externalOpticalClaimUpdateRequest.getInvoiceNumber(), objectMapper.writeValueAsString(orderCommentReq));
        }
    }
}


