package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.*;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.ordermetadata.dto.response.ProductMetaDataResponse;
import com.lenskart.returncommon.exception.ReturnNotFound;
import com.lenskart.returncommon.exception.ReturnRequestException;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.response.*;
import com.lenskart.returnrepository.entity.*;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface IReturnOrderActionService {
    boolean isExisitingReturnCancelled(Integer uwItemId, String source);

    boolean checkIsReturnOrderCancelled(ReturnDetail returnDetail);

    boolean isExisitingReturnNotRejectedCancelledReshiped(ReturnDetail returnOrder);

    String getReturnOrderStatus(ReturnDetail returnDetail);

    Map<Integer, String> getReturnOrderStatus(List<ReturnDetail> returnOrders);

    ReturnEvent getReturnOrderEvent(Integer returnId);

    Optional<ReturnDetail> getExistingReturnDetailsByItem(Integer uwItemId);

    Optional<ReturnDetail> getValidReturnDetailsByItem(Integer uwItemId);

    String getReturnOrderStatusById(Integer returnId);

    void setDraftStatusAndRefundMethodCaptured(UwOrderDTO uwOrder, ReturnRefundResponseDTO refundResponseDTO);

    void setReturnRefundabilityBasedOnExchangeDone(UwOrderDTO uwOrder, OrderExchangeCancellationDetails orderExchangeCancellationDetails, ReturnRefundResponseDTO refundResponseDTO);

    ReturnDetail getReturnOrder(Integer uwItemId);

    ReturnDetailItem findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId);

    ReturnDetailItem findTop1ByUwItemIdOrderByReturnCreateDatetimeDescNonInventory(Integer uwItemId);

    ReturnDetailItem findEligibilityClientsTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId);

    List<ReturnDetailItem> findByGroupIdOrderByReturnCreateDatetimeDesc(Long groupId);

    ReturnDetail findByReturnIdAndStatus(Integer returnId, String status);

    ReshipItemDetail findByReturnId(Integer returnId);

    boolean containsInReturnStatusList(String returnOrderStatus);

    List<ReturnDetail> fetchReturnOrdersOfLastTwoMonths(Long pickupId);

    ReturnDetailItem findTopByReturnId(Integer id);

    List<ReturnDetail> findAllByIncrementId(Integer incrementId);

    List<ReturnDetail> getReturnOrders(List<Integer> orderIds, boolean callInventory);

    ReturnCourierDetail findTop1ByReturnId(Integer Id);

    List<Integer> findReturnIdsByIncrementId(Integer orderNo);

    List<Integer> getReturnIdsFromInventory(String identifierType, String identifierValue);

    com.lenskart.ordermetadata.dto.ReturnOrderDTO getReturnFromInventory(String identifierType, String identifierValue);

    List<ReturnDetailItem> findAllByReturnId(Integer returnId);

    ReturnDetailItem findTopByNewReturnId(Integer returnId);

    Optional<ReturnDetail> findReturnOrderById(Integer id);

    Optional<ReturnDetail> findReturnOrderByIdNonInventory(Integer id);

    ReturnDetailItem findTopReturnOrderItemByUwItemId(Integer uwItemId);

    List<ReturnDetailItem> findReturnOrderItemByUwItemId(Integer uwItemId);
    CreateUpdateReturnOrderResponseDTO createAwaitedRtoReturnOrder(ReturnRequest returnRequest, ReturnOrderRequestDTO returnOrderRequest, String status, String returnType, UwOrderDTO uwOrderDTO);

    void updateReturnCourierDetailByIncrementIdAndGroupId(String status, String waybill, Long groupId, Integer incrementId, Integer value, ReverseCourierDetail reassignedCourier);

    List<ReturnDetail> findAllByGroupIdAndIncrementId(long groupId, Integer integer);

    CreateUpdateReturnOrderResponseDTO createUpdateReturnOrder(ReturnOrderRequestDTO returnRequest, Integer returnId, String status, String returnType, Integer requestId, UwOrderDTO uwOrderDTO, String receiving_flag) throws ReturnRequestException;

    CreateUpdateReturnOrderResponseDTO createUpdateReturnOrder(ReturnOrderRequestDTO returnRequest, Integer returnId, String status, String returnType, String returnOrderOriginalStatus, Integer requestId);

    void createReturnItem(ReturnItemRequestDTO returnItemRequest, UwOrderDTO uwOrder, Integer returnId, String returnType, Integer requestId)
            throws ReturnRequestException;


    Integer getDistinctDatesOfPickupFailed(Integer returnId);

    ReturnDetailsDTO getReturnDetailsByIdentifier(String identifierType, List<String> identifier, boolean callInventory);
    Integer getUwItemIdByReturnId(Integer returnId);
    Map<String, Integer> getItemDetailsByReturnId(Integer returnId);
    Map<String,Boolean> getIsReturnFromNewFlow(String identifierType,String identifierValue);

    List<com.lenskart.ordermetadata.dto.response.ReturnReasonTableDTO> getReturnReason(List<Integer> uwItemIds);

    String getReturnSource(Integer uwItemId);

    void updateReceivingFlag(String flag, Integer returnId, Integer incrementId);

    ReturnOrderItemDTO getReturnOrderItemByUwItemId(Integer uwItemId);

    Map<String,Boolean> getIdentifierValuesMappingWithNewFlow(String identifierType, List<String> identifier);


    com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO getReturnOrderDetails(String identifierType, String identifierValue);

    Long findGroupIdByReturnId(Integer b2bReturnId);

    Integer getReturnId(Integer uwItemId);
    List<ReturnDetail> findAllByGroupIdOrderByReturnCreateDatetimeDesc(long groupId);

    List<ReturnDetail> getReturnsForAutoCancellation(Date startDate, Date endDate, Integer batchSizeForReturnAutoCancelJob);

    List<ReturnDetail> findByReturnIdIn(List<Integer> returnIds, boolean callInventory);

    ReturnDetail findTop1ByOrderNoAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(Integer incrementId, String referenceOrderCode, String awaitedRto);

    ReturnDetail saveReturnOrder(ReturnDetail returnOrder);

    List<String> findReturnOrderReceivingFlagByByUwItemId(List<Integer> itemIds, List<String> returnItemStatus, String reverse);

    ReturnDetailItem saveReturnOrderItem(ReturnDetailItem returnOrderItem);

    Integer findReturnIdByReturnTypeUwItemID(String returnType, Integer itemId);
    ReturnDetail findReturnIdByReturnTypeUwItemID(List<String> returnType, Integer itemId);

    List<ReturnDetailItem> findByUwItemIdInAndStatusInAndReturnType(List<Integer> itemIds, List<String> returnItemStatus, String rto);

    ReturnDetail persistB2BReturn(UwOrderDTO uwOrderB2B, long group_id, ReturnDetail returnOrder, QCType qcType, boolean existingReturnDetailUsed, Integer returnRequestId, String facilityCode, ReturnItemDTO item);

    ReturnDetail persistReturn(boolean isNewReturnBeCreated, Optional<ReturnDetail> returnDetailsOpt, ReturnCreationRequestDTO returnCreationRequest, UwOrderDTO uwOrder, OrdersHeaderDTO ordersHeaderDTO, ReturnStatus returnStatus, ReturnItemDTO item, QCType qcType, Integer requestId);

    boolean shouldReturnBeCreated(ReturnItemDTO item, Optional<ReturnDetail> returnDetailsOpt) throws ReturnNotFound;

    List<ReturnDetail> findByGroupId(long groupId);

    ReturnCourierDetail findTopByReturnIdOrderByIdDesc(Integer id);

    void saveReturnCourier(ReturnCourierDetail returnCourierDetail);

    List<ReturnDetail> getReturnOrdersByStatusOrderAndCourier(Integer orderNo, String reverseCourier, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    List<ReturnDetail> getReturnsByOrderAndCourier(Integer orderNo, String reverseCourier, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    List<ReturnDetail> getReturnOrdersByStatusAndOrder(Integer orderNo, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    List<ReturnDetail> getReturnsByOrder(Integer orderNo, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    List<ReturnDetail> getReturnsByCourierAndStatus(String reverseCourier, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    List<ReturnDetail> getReturnsByCourier(String reverseCourier, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    List<ReturnDetail> getReturnsByStatus(String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    ReturnDetail getReturnOrderBasedOnUwItemIdAndStatus(Integer uwItemId);

    List<Map<String, Object>> getReturnOrderResponse(Integer incrementId, List<Integer> non2B2UwItemIds);

    Map<String, Object> getReturnTrackerOrderItemDtos(Integer uwItemId, ProductMetaDataResponse productInfoBody);

    Map<Integer, String> getReturnIdByStatusMapping(Integer incrementId);

    List<ReturnDetail> getLatestReturns(Integer pageSize, Integer pageNo);

    GetReturnsResponse getReturns(GetReturnsRequest returnsRequest);
    void updateOrderStatusForRefund(DualRefundRequest refundRequest);
    void saveReturnOrder(ReturnOrder returnOrder);
    void saveReturnOrderItem(ReturnOrderItem returnOrder);
    void saveReturnOrderItemAll(List<ReturnOrderItem> returnOrderItems);
    void saveReturnReason(ReturnReason returnReason);
    void saveReturnOrderAddressUpdate(ReturnOrderAddressUpdate returnOrderAddressUpdate);
    void saveReturnHistory(ReturnHistory returnHistory);
    ReturnDetail getAlternateB2BReturnDetails(Integer returnId);

    GetReturnReportResponse getReturnReportDetails(GetReturnsRequest returnsRequest);

    boolean updateReturnAgent(UpdateReturnAgentRequest request);
    List<ReturnDetail> getReturnOrdersByFacilityAndDateRange(String facility,Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    void updateReturnReason(UpdateReturnReasonRequest updateReturnReasonRequest);

    Map<String, Object> updateAgentEmailsInReturn(UpdateAgentEmailReturnRequest updateAgentEmailReturnRequest);

    int updateReturnOrderItem(UpdateReturnOrderItemRequest updateReturnOrderItemRequest);

//    Page<ReturnDetail> getReturnOrdersCombined(String status, Integer orderNo, String reverseCourier, String facility, Date startDate, Date endDate, Pageable pageable);

    PagedResponse<ReturnDetail> getReturnOrdersCombined(
            String status, Integer orderNo, String reverseCourier, String facility,
            Date startDate, Date endDate, Integer lastFetchedId, int pageSize);

    List<ReturnOrderItem> findReturnOrderItemBatch(int lastProcessedId, int BATCH_SIZE);

    ReversePickupPincode saveReversePickupPincode(ReversePickupPincode reversePickupPincode);

    ReverseCourierMapping saveReverseCourierMapping(ReverseCourierMapping reverseCourierMapping);

    RefundRules saveRefundRules(RefundRules refundRules);

    List<ReturnOrderItemDTO> findReturnOrderItems(List<Integer> returnIds);

    List<ReverseCourierDetailDTO> getReverseCourierDetailDTO(List<Integer> returnIds);

    List<ReturnReasonDTO> getReturnReasonDTO(List<Integer> returnIds);

    String updateModelPrompt(Integer id,String prompt);

    ReturnStatusResponse getReturnResponse(Integer magentoItemId);
}
