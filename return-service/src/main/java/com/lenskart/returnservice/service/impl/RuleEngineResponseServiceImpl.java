package com.lenskart.returnservice.service.impl;

import com.lenskart.returnrepository.entity.ReturnRefundRule;
import com.lenskart.returnrepository.repository.ReturnRefundRuleRepository;
import com.lenskart.returnservice.service.IRuleEngineResponseService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RuleEngineResponseServiceImpl implements IRuleEngineResponseService {

    @Autowired
    private ReturnRefundRuleRepository returnRefundRuleRepository;

    @Override
    public String getRuleEngineResponseForDispatch(Integer uwItemId, Integer returnId) {
        log.info("[getRuleEngineResponseForDispatch] getting rule engine response for dispatch for uwItemId: "+uwItemId+ "returnId "+returnId);
        String ruleEngineDispatchStatus=null;
        try {
            ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
            if (returnRefundRule != null) {
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                JSONObject jsonObject = new JSONObject(returnRefundRuleResponse);
                ruleEngineDispatchStatus = (String) jsonObject.get("exchangeOrderDispatch");
            }
        }
        catch (Exception e){
            log.info("[getRuleEngineResponseForDispatch] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getRuleEngineResponseForDispatch] rule engine response for dispatch: "+ruleEngineDispatchStatus);
        return ruleEngineDispatchStatus;
    }
}
