package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.refund.client.model.enums.RefundReason;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.RefundRequestDTO;
import com.lenskart.returncommon.model.enums.RefundMethod;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.response.RefundRequestResponseDTO;
import com.lenskart.returncommon.model.dto.RefundRequestInputDTO;
import com.lenskart.returncommon.predicate.RefundPredicate;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnrepository.repository.ReturnDetailItemRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IRefundRequestService;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.lenskart.returncommon.utils.Constant.EVENT.REFUND_REQUEST_CREATED;
import static com.lenskart.returncommon.utils.Constant.EVENT.REFUND_TRIGGERED;
import static com.lenskart.returncommon.utils.Constant.STATUS.RETURN_EXCHANGE;
import static com.lenskart.returncommon.utils.Constant.STATUS.RETURN_REFUNDED;

@Service
@Slf4j
public class RefundRequestServiceImpl implements IRefundRequestService {

    @Autowired
    private IKafkaService kafkaService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Value("${source.nav.channel:BRWEBDTC}")
    private String sourceNavChannels;

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    @Override
    public RefundRequestResponseDTO initiateRefund(Integer returnRequestId, ReturnCreationRequestDTO returnCreationRequest, ReturnItemDTO item, UwOrderDTO uwOrder, Integer returnId, boolean isReturnCreatedNow, String initiateRefundProcess, Integer virtualB2BUwItemId) {
        log.info("[initiateRefundProcess] enter order : {}", uwOrder.getIncrementId());
        RefundRequestResponseDTO requestResponseDTO = null;
        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        refundRequestDTO.setDoRefund(item.getDoRefund());
        String refundMethodRequest = item.getRefundMethodRequest();
        String source = returnCreationRequest.getReturnSource().getSource();
        if (ReturnSources.VSM.getSource().equalsIgnoreCase(source) && item.getNeedApproval()) {
            log.info("[initiateRefundProcess] blocking initiateRefund for order : {}", uwOrder.getIncrementId());
            return requestResponseDTO;
        }
        if(StringUtils.isEmpty(refundMethodRequest)){
            Optional<ReturnRequest> returnRequestOptional = returnRequestRepository.findById(returnRequestId);
            if(returnRequestOptional.isPresent()){
                ReturnRequest returnRequest = returnRequestOptional.get();
                refundMethodRequest = returnRequest.getReturnIntention();
            }
        }
        if(virtualB2BUwItemId != null && virtualB2BUwItemId > 0){
            refundRequestDTO.setUwItemId(virtualB2BUwItemId);
        }else{
            refundRequestDTO.setUwItemId(uwOrder.getUwItemId());
        }
        if(sourceNavChannels.equalsIgnoreCase(uwOrder.getNavChannel()) && (RefundMethod.STORECREDIT.getName().equalsIgnoreCase(refundMethodRequest) || RefundMethod.STORECREDIT.getName().equalsIgnoreCase(item.getRefundMethod()))){
            refundRequestDTO.setRefundMethodRequest(RefundMethod.SOURCE.getName());
            refundRequestDTO.setRefundMethod(RefundMethod.SOURCE.getName());
        }else{
            refundRequestDTO.setRefundMethodRequest(refundMethodRequest);
            refundRequestDTO.setRefundMethod(item.getRefundMethod());
        }
        refundRequestDTO.setIncrementId(uwOrder.getIncrementId());
        refundRequestDTO.setClaimInsurance(item.getClaimInsurance());
        refundRequestDTO.setInitiatedBy(String.valueOf(returnCreationRequest.getInitiatedBy()));
        refundRequestDTO.setMagentoId(item.getMagentoId());
        refundRequestDTO.setNeedApproval(item.getNeedApproval());
        refundRequestDTO.setQcStatus(item.getQcStatus());
        refundRequestDTO.setReturnCreatedNow(isReturnCreatedNow);
        refundRequestDTO.setReturnSource(returnCreationRequest.getReturnSource().getSource());
        refundRequestDTO.setReturnInitiatedFrom(initiateRefundProcess);
        try{
            RefundRequestInputDTO refundRequestInputDTO = new RefundRequestInputDTO(refundRequestDTO, returnRequestId);
            ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(refundRequestDTO.getUwItemId());
            refundRequestDTO.setReturnId(returnDetailItem.getReturnId());
            kafkaService.pushToKafka(Constant.RETURN_TOPICS.RETURN_REFUND, String.valueOf(refundRequestDTO.getIncrementId()), refundRequestInputDTO);
        }catch (Exception exception){
            log.error("[RefundRequestServiceImpl][initiateRefundProcess] exception : "+exception);
        }
        log.info("[initiateRefundProcess] exit order : {}", uwOrder.getIncrementId());
        return requestResponseDTO;
    }

    @Override
    public RefundRequestResponseDTO createRefundRequest(RefundRequestInputDTO refundRequestInputDTO) {
        log.info("[RefundRequestServiceImpl][createRefundRequest] request : "+refundRequestInputDTO);
        RefundRequestResponseDTO requestResponseDTO = null;
        String returnOrderStatus = returnOrderActionService.getReturnOrderStatusById(refundRequestInputDTO.getRefundRequestDTO().getReturnId());
        if(!ReturnStatus.RETURN_NEED_APPROVAL.getStatus().equalsIgnoreCase(returnOrderStatus)){
            ResponseEntity<RefundRequestResponseDTO> requestResponseDTOResponseEntity = orderOpsFeignClient.initiateRefundProcess(refundRequestInputDTO.getRefundRequestDTO());
            if(requestResponseDTOResponseEntity.getStatusCode().is2xxSuccessful()){
                requestResponseDTO = requestResponseDTOResponseEntity.getBody();
                if(requestResponseDTO != null){
                    returnEventService.createReturnEvent(refundRequestInputDTO.getReturnRequestId(), refundRequestInputDTO.getRefundRequestDTO().getReturnId(), REFUND_REQUEST_CREATED, "");
                    if (null != returnOrderStatus && !RefundPredicate.IS_RETURN_IN_DRAFTED_REJECTED_STATE.test(returnOrderStatus)
                            && RefundPredicate.IS_RETURN_NOT_IN_RPU_CYCLE.test(returnOrderStatus)) {
                        if (Constant.REFUND_STATUS.PROCESSED.equalsIgnoreCase(requestResponseDTO.getStatus())) {
                            String status = Constant.REFUND_METHOD.EXCHANGE.equalsIgnoreCase(requestResponseDTO.getRefundMethod()) ?
                                    RETURN_EXCHANGE : RETURN_REFUNDED;
                            returnEventService.createReturnEvent(refundRequestInputDTO.getReturnRequestId(), refundRequestInputDTO.getRefundRequestDTO().getReturnId(), status, "");
                        }
                    }
                }
            }
        }
        return requestResponseDTO;
    }
}
