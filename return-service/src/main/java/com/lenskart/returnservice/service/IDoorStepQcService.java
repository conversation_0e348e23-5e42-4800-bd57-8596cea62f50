package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.reversemodel.request.kafka.InitiateReverseRequest;

import java.util.List;

public interface IDoorStepQcService {
    boolean addQcAtDoorstepDetails(ReverseCourierDetail reverseCourierDetail, UwOrderDTO uwOrder, InitiateReverseRequest initiateReverseRequest, List<com.lenskart.ordermetadata.dto.request.Reasons> reasons, ItemWiseAmountDTO itemWiseAmountDTO, ShippingStatusDetail shippingStatusDetail);
}
