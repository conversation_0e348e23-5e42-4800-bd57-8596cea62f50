package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.ReversePickupPincodeDto;
import com.lenskart.returncommon.model.dto.ReverseTatDTO;
import com.lenskart.returncommon.model.request.AwbNumberValidationRequest;
import com.lenskart.returncommon.model.request.ReverseCourierUpdateRequestDTO;
import com.lenskart.returncommon.model.request.ReverseTatRequest;
import com.lenskart.returncommon.model.response.ReverseCourierUpdateResponse;
import com.lenskart.returnrepository.entity.ReturnDetail;

import java.util.List;

public interface IReversePickUpService {

    List<ReversePickupPincodeDto> getActiveLkartOrReversePickupCourier(Integer pincode, boolean isActiveLkartCourierRequired);
    ReverseCourierUpdateResponse updateReversePickUpInfo(ReverseCourierUpdateRequestDTO request);

    void handleExchangeOrder(ReturnDetail returnOrder);

    ReverseTatDTO getReverseTatInfo(ReverseTatRequest reverseTatRequest);

    void checkAndInitiateRefundRequest(ReturnDetail returnOrder, String remarks);
}
