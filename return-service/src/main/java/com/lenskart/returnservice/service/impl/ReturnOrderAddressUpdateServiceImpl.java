package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returnrepository.entity.ReturnDetailAddressUpdate;
import com.lenskart.returnrepository.entity.ReturnOrderAddressUpdate;
import com.lenskart.returnrepository.repository.ReturnOrderAddressUpdateOldRepository;
import com.lenskart.returnrepository.repository.ReturnOrderAddressUpdateRepository;
import com.lenskart.returnservice.service.IReturnOrderAddressUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;

@Service
@Slf4j
public class ReturnOrderAddressUpdateServiceImpl implements IReturnOrderAddressUpdateService {

    @Autowired
    private ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Autowired
    private ReturnOrderAddressUpdateOldRepository returnOrderAddressUpdateOldRepository;

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Override
    public Integer saveReturnAddressDetails(ReturnCreationRequestDTO returnCreationRequest, int groupId, Integer incrementId) {
        log.info("[ReturnActionImpl:saveReturnAddressDetails ] saving return address details for groupId "+groupId);
        ReturnDetailAddressUpdate returnOrderAddressUpdate = new ReturnDetailAddressUpdate();
        returnOrderAddressUpdate.setFirstName(returnCreationRequest.getReversePickupAddress().getFirstName());
        returnOrderAddressUpdate.setCreatedAt(new Timestamp(new Date().getTime()));
        returnOrderAddressUpdate.setGroupId(groupId);
        returnOrderAddressUpdate.setIncrement_id(incrementId);
        returnOrderAddressUpdate.setLastName(returnCreationRequest.getReversePickupAddress().getLastName());
        returnOrderAddressUpdate.setStreet1(returnCreationRequest.getReversePickupAddress().getStreet1());
        returnOrderAddressUpdate.setStreet2(returnCreationRequest.getReversePickupAddress().getStreet2());
        returnOrderAddressUpdate.setCity(returnCreationRequest.getReversePickupAddress().getCity());
        returnOrderAddressUpdate.setState(returnCreationRequest.getReversePickupAddress().getState());
        returnOrderAddressUpdate.setPostcode(returnCreationRequest.getReversePickupAddress().getPincode());
        returnOrderAddressUpdate.setCountry(returnCreationRequest.getReversePickupAddress().getCountry());
        returnOrderAddressUpdate.setEmail(returnCreationRequest.getReversePickupAddress().getEmail());
        returnOrderAddressUpdate.setTelephone(returnCreationRequest.getReversePickupAddress().getTelephone());
        return returnOrderAddressUpdateRepository.save(returnOrderAddressUpdate).getId();
    }

    @Override
    public ReturnDetailAddressUpdateDTO getReturnAddressUpdate(Integer incrementId) {
        log.info("[getReturnAddressUpdate] order : {}", incrementId);
        ReturnDetailAddressUpdateDTO returnDetailAddressUpdateDTO = null;
        ReturnDetailAddressUpdate returnDetailAddressUpdate = returnOrderAddressUpdateRepository.findByIncrement_id(incrementId);
        if(returnDetailAddressUpdate != null){
            returnDetailAddressUpdateDTO = objectMapper.convertValue(returnDetailAddressUpdate, ReturnDetailAddressUpdateDTO.class);
            returnDetailAddressUpdateDTO.setFirstName(returnDetailAddressUpdate.getFirstName());
            returnDetailAddressUpdateDTO.setLastName(returnDetailAddressUpdate.getLastName());
            returnDetailAddressUpdateDTO.setGroupId(returnDetailAddressUpdate.getGroupId());
            returnDetailAddressUpdateDTO.setIncrement_id(returnDetailAddressUpdate.getIncrement_id());
            returnDetailAddressUpdateDTO.setCountry(returnDetailAddressUpdate.getCountry());
            returnDetailAddressUpdateDTO.setCreatedAt(returnDetailAddressUpdate.getCreatedAt());
            returnDetailAddressUpdateDTO.setAddressType("reverse");
        }
        else{
            ReturnOrderAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateOldRepository.findByIncrement_id(incrementId);
            if(returnOrderAddressUpdate != null){
                returnDetailAddressUpdateDTO = objectMapper.convertValue(returnOrderAddressUpdate, ReturnDetailAddressUpdateDTO.class);
                returnDetailAddressUpdateDTO.setFirstName(returnOrderAddressUpdate.getFirstName());
                returnDetailAddressUpdateDTO.setLastName(returnOrderAddressUpdate.getLastName());
                returnDetailAddressUpdateDTO.setGroupId(returnOrderAddressUpdate.getGroupId());
                returnDetailAddressUpdateDTO.setIncrement_id(returnOrderAddressUpdate.getIncrement_id());
                returnDetailAddressUpdateDTO.setCountry(returnOrderAddressUpdate.getCountry());
                returnDetailAddressUpdateDTO.setCreatedAt(returnOrderAddressUpdate.getCreatedAt());
                returnDetailAddressUpdateDTO.setAddressType("reverse");
            }
        }
        log.info("[getReturnAddressUpdate] order : {}, response : {} ", incrementId, returnDetailAddressUpdateDTO);
        return returnDetailAddressUpdateDTO;
    }

    @Override
    public ReturnDetailAddressUpdateDTO getReturnAddressUpdate(Long groupId) {
        log.info("[getReturnAddressUpdate] groupId : {}", groupId);
        ReturnDetailAddressUpdateDTO returnDetailAddressUpdateDTO = null;
        ReturnDetailAddressUpdate returnDetailAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(groupId);
        if(returnDetailAddressUpdate != null){
            returnDetailAddressUpdateDTO = objectMapper.convertValue(returnDetailAddressUpdate, ReturnDetailAddressUpdateDTO.class);
            returnDetailAddressUpdateDTO.setAddressType("reverse");
            returnDetailAddressUpdateDTO.setCreatedAt(returnDetailAddressUpdate.getCreatedAt());
        }
        else{
            ReturnOrderAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateOldRepository.findByGroupId(groupId);
            if(returnOrderAddressUpdate != null){
                returnDetailAddressUpdateDTO = objectMapper.convertValue(returnOrderAddressUpdate, ReturnDetailAddressUpdateDTO.class);
                returnDetailAddressUpdateDTO.setAddressType("reverse");
                returnDetailAddressUpdateDTO.setCreatedAt(returnOrderAddressUpdate.getCreatedAt());
            }
        }
        log.info("[getReturnAddressUpdate] groupId : {}, response : {} ", groupId, returnDetailAddressUpdateDTO);
        return returnDetailAddressUpdateDTO;
    }

    @Override
    public ReturnDetailAddressUpdateDTO getReturnAddressUpdateNew(Integer incrementId) {
        log.info("[getReturnAddressUpdateNew] order : {}", incrementId);
        ReturnDetailAddressUpdateDTO returnDetailAddressUpdateDTO = null;
        ReturnDetailAddressUpdate returnDetailAddressUpdate = returnOrderAddressUpdateRepository.findByIncrement_idAndFirstNameExists(incrementId);
        if(returnDetailAddressUpdate != null){
            returnDetailAddressUpdateDTO = objectMapper.convertValue(returnDetailAddressUpdate, ReturnDetailAddressUpdateDTO.class);
            returnDetailAddressUpdateDTO.setFirstName(returnDetailAddressUpdate.getFirstName());
            returnDetailAddressUpdateDTO.setLastName(returnDetailAddressUpdate.getLastName());
            returnDetailAddressUpdateDTO.setGroupId(returnDetailAddressUpdate.getGroupId());
            returnDetailAddressUpdateDTO.setIncrement_id(returnDetailAddressUpdate.getIncrement_id());
            returnDetailAddressUpdateDTO.setCountry(returnDetailAddressUpdate.getCountry());
            returnDetailAddressUpdateDTO.setCreatedAt(returnDetailAddressUpdate.getCreatedAt());
            returnDetailAddressUpdateDTO.setAddressType("reverse");
        }
        else{
            ReturnOrderAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateOldRepository.findByIncrement_id(incrementId);
            if(returnOrderAddressUpdate != null){
                returnDetailAddressUpdateDTO = objectMapper.convertValue(returnOrderAddressUpdate, ReturnDetailAddressUpdateDTO.class);
                returnDetailAddressUpdateDTO.setFirstName(returnOrderAddressUpdate.getFirstName());
                returnDetailAddressUpdateDTO.setLastName(returnOrderAddressUpdate.getLastName());
                returnDetailAddressUpdateDTO.setGroupId(returnOrderAddressUpdate.getGroupId());
                returnDetailAddressUpdateDTO.setIncrement_id(returnOrderAddressUpdate.getIncrement_id());
                returnDetailAddressUpdateDTO.setCountry(returnOrderAddressUpdate.getCountry());
                returnDetailAddressUpdateDTO.setCreatedAt(returnOrderAddressUpdate.getCreatedAt());
                returnDetailAddressUpdateDTO.setAddressType("reverse");
            }
        }
        log.info("[getReturnAddressUpdate] order : {}, response : {} ", incrementId, returnDetailAddressUpdateDTO);
        return returnDetailAddressUpdateDTO;
    }

}
