package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.SystemPreferenceRepository;
import com.lenskart.returnservice.service.INexsFacilityService;
import io.micrometer.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class NexsFacilityService implements INexsFacilityService {
    private final Logger logger = LoggerFactory.getLogger(NexsFacilityService.class.getName());
    @Autowired
    RedisTemplate<String, Object> redisTemplate;
    @Autowired
    SystemPreferenceRepository systemPreferenceRepository;

    @Override
    public boolean isNexsFacility(String facilityCode) {
        logger.info("Checking if it is nexsFacility: " + facilityCode);
        List<String> nexsFacilities = getNexsFacilities();
        if (StringUtils.isNotEmpty(facilityCode) && !nexsFacilities.isEmpty())
            return nexsFacilities.contains(facilityCode);
        logger.info("Not a NexsFacility...");
        return false;
    }

    @Override
    public List<String> getNexsFacilities() {
        logger.info("Getting NexsFacilities from System Preferences..");
        String nexsFacilitiesStr = getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.NEXS_FACILITIES, Constant.SYSTEM_PREFERENCE_GROUPS.NEXS_FACILITIES);
        return Arrays.asList(nexsFacilitiesStr.split(","));
    }

    @Override
    public String getSystemPreferenceValues(String key, String group) {
        String searchKey = "";
        if (!StringUtils.isEmpty(key) && !StringUtils.isEmpty(group)) {
            searchKey = key + "_" + group;
        } else {
            return null;
        }

        String value = null;
        try {
            if (redisTemplate.hasKey(searchKey)) {
                logger.info("[getSystemPreferenceValues] Fetching from cache searchKey : " + searchKey);
                value = (String) redisTemplate.opsForValue().get(searchKey);
            } else {
                logger.info("[getSystemPreferenceValues] Fetching from DB searchKey : " + searchKey);
                value = getByDatabase(key, group);
                if (StringUtils.isNotBlank(value)) {
                    logger.info("[getSystemPreferenceValues] Setting searchKey : " + searchKey + " and value : " + value);
                    redisTemplate.opsForValue().set(searchKey, value, 1, TimeUnit.HOURS);
                }
            }
        } catch (Exception e) {
            logger.error("[getSystemPreferenceValues] Exception in config set " + e.getMessage());
            value = getByDatabase(key, group);
        }
        return value;
    }

    @Override
    public SystemPreference findAllByKey(String key) {
        try {
            return systemPreferenceRepository.findAllByKey(key);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public String getByDatabase(String key, String group) {
        String value = Constant.BLANK;
        SystemPreference systemPreference;
        if (!StringUtils.isEmpty(key) && !StringUtils.isEmpty(group)) {
            logger.info("[getByDatabase] findTopByGroupAndKey : " + group + " && " + key);
            systemPreference = systemPreferenceRepository.findTopByGroupAndKey(group, key);
            if (null != systemPreference) {
                value = systemPreference.getValue();
            }
        }
        logger.info("[getByDatabase] value : " + value);
        return value;
    }
}
