package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.OldRefundRequestCreateDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.orderops.model.Amount;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.refund.client.model.dto.RefundAmount;
import com.lenskart.refund.client.model.enums.*;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.request.CreateRefundRequest;
import com.lenskart.refund.client.model.response.CheckRefundInitiatedResponse;
import com.lenskart.refund.client.model.response.RefundMethodResponse;
import com.lenskart.returncommon.exception.ReturnRequestException;
import com.lenskart.returncommon.exception.ReturnRequestFailException;
import com.lenskart.returncommon.model.dto.CheckRefundSwitchActiveRequestDTO;
import com.lenskart.returncommon.model.dto.UnicomApiLogDto;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.response.NexsReturnResponse;
import com.lenskart.returncommon.predicate.RefundPredicate;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.ReturnGroupRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import feign.FeignException;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.UW_ORDERS_DTO;
import static com.lenskart.returncommon.utils.Constant.EVENT.*;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.B2B;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.REVERSE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.RTO;

@Slf4j
@Service
public class ReceivingServiceImpl implements IReceivingService {
    @Autowired
    private ReturnGroupRepository returnGroupRepository;
    public static final  String returnNexsFailedKey = "Failed";
    private static final List<String> RETURN_ITEM_NOT_RECEIVED_STATUS = new ArrayList<String>(Arrays.asList("new_reverse_pickup", "reference_id_issued", "awb_assigned", "sc_refunded_receiving_pending", "selfdispatched", "auto_sync_failed", "vendor_sync_pending","return_refunded_receiving_pending", "initiated_reverse"));
    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;
    @Autowired
    IReturnNexsService returnNexsService;
    @Autowired
    IReturnUnicomService returnUnicomService;
    @Autowired
    IOrderUtilsService orderUtilsService;
    @Autowired
    IReturnOrderItemService returnOrderItemService;
    @Autowired
    private IReturnEventService returnEventService;
    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;

    @Autowired
    private IReturnReasonService returnReasonService;
    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IKafkaService kafkaService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ID365FinanceService id365FinanceService;

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Autowired
    private ReturnUtil returnUtil;

    @Value("${enable.nexsAtStore:true}")
    private String enableNexsAtStore;

    @Override
    public Map<String, Object> returnItems(ReturnOrderRequestDTO returnRequest) throws Exception {
        log.info("[ReceivingServiceImpl][returnItems] : Enter in returnItems" + returnRequest.toString());
        Map<String, Object> returnItemsResponse = new HashMap<>();
        returnItemsResponse.put(Constant.SUCCESS, false);
        if (!validateReturnRequest(returnRequest)) {
            log.error("[ReceivingServiceImpl][returnItems] : Null or Empty parameters in ReturnRequest request");
            createUnicomApiLog(returnRequest.getIncrementId(), returnRequest.getReferenceOrderCode(), Constant.FAILURE, "Null or Empty parameters in ReturnRequest request");
            throw new ReturnRequestFailException(String.valueOf(HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST.getReasonPhrase());
        }
        String facility = returnRequest.getFacility();
        String reversePickupCode = null;
        Map<Integer, UwOrderDTO> uwItemIdToUwOrderDtoMap = new HashMap<>();
        List<UwOrderDTO> uwOrderDTOS = returnRequest.getUwOrderDTOs();
        if(CollectionUtils.isEmpty(uwOrderDTOS)){
            boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(returnRequest.getIncrementId(), "order-info.rollout.percentage");
            ResponseEntity<OrderInfoResponseDTO> orderInfoResponse = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(returnRequest.getIncrementId(), List.of(UW_ORDERS_DTO))
                    : orderOpsFeignClient.getOrderDetails(returnRequest.getIncrementId());
            uwOrderDTOS = Objects.requireNonNull(orderInfoResponse.getBody()).getUwOrders();
        }
        returnRequest.setDoRefund(Boolean.TRUE.equals(returnRequest.getDoRefund()));
        returnRequest.setIsDualCo(Boolean.TRUE.equals(returnRequest.getIsDualCo()));
        uwOrderDTOS.forEach(uw -> uwItemIdToUwOrderDtoMap.put(uw.getUwItemId(), uw));
        List<Integer> uwItems = new ArrayList<>();
        if (!validateAllUwItemIds(returnRequest, uwItemIdToUwOrderDtoMap)) {
            throw new ReturnRequestFailException(String.valueOf(HttpStatus.BAD_REQUEST.value()), "Item not found in Inventory");
        }
        returnRequest.getItems().forEach(uw -> uwItems.add(uw.getItemId()));
        Set<Integer> returnIds = new HashSet<>();
        Map<Integer, String> existingReturnStatusMap = null;
        String returnType = null != returnRequest.getRtoItem() && returnRequest.getRtoItem() ? RTO : REVERSE;
        if (!checkForDuplicateReturn(uwItems)) {
            log.info("[ReverseServiceImpl][returnItems][checkForDuplicateReturn]");
            existingReturnStatusMap = getExistingStatus(uwItems);
            returnIds = receiveItems(returnRequest);
            if (!CollectionUtils.isEmpty(returnIds)) {
                log.info("[ReverseServiceImpl]createReversePickup[returnItems][createReversePickupUnicom] RaiseRPUatUnicom : {}", returnRequest.getRaiseRPUatUnicom());
                log.info("[ReverseServiceImpl]createReversePickup[returnItems][createReversePickupNexs] RaiseRPUatNexs : {}", returnRequest.getRaiseRPUatNexs());
                if (returnRequest.getRaiseRPUatNexs()) {
                    reversePickupCode = createReversePickupNexs(returnRequest, facility, returnRequest.getRtoItem(), uwOrderDTOS);
                } else if (returnRequest.getRaiseRPUatUnicom()) {
                    reversePickupCode = createReversePickupUnicom(returnRequest, facility, returnRequest.getRtoItem());
                }
                if (StringUtils.isNotBlank(reversePickupCode)) {
                    boolean alreadyExists = Constant.REVERSE_PICKUP_ALREADY_EXISTS.equalsIgnoreCase(reversePickupCode);
                    returnItemsResponse.put(Constant.SUCCESS, !alreadyExists);
                    returnItemsResponse.put(Constant.ALREADY_EXIST, alreadyExists);
                    if (returnRequest.getRaiseRPUatUnicom()) {
                        returnItemsResponse.put(Constant.REVERSE_PICKUP_CODE, reversePickupCode);
                    }
                    returnItemsResponse.put(Constant.RETURN_ID, returnIds.toArray()[0]);
                }
            } else {
                log.error("[ReverseServiceImpl][checkForDuplicateReturn] : Duplicate uwItems");
                throw new ReturnRequestFailException("Duplicate Items for return");
            }
        }
        log.info("[ReverseServiceImpl][returnItems] : Enter in returnItems" + returnItemsResponse);
        if(Boolean.TRUE.equals(returnItemsResponse.get(Constant.SUCCESS))) saveReturnEvent(returnIds, existingReturnStatusMap, returnType);
        return returnItemsResponse;
    }

    private Map<Integer, String> getExistingStatus(List<Integer> uwItems) {
        Map<Integer, String> existingReturnStatusMap = new HashMap<>();
        for(Integer uwItemId : uwItems){
            ReturnDetailItem returnDetailItem = returnOrderActionService.findTopReturnOrderItemByUwItemId(uwItemId);
            if(returnDetailItem != null){
                String status = returnOrderActionService.getReturnOrderStatusById(returnDetailItem.getReturnId());
                existingReturnStatusMap.put(returnDetailItem.getReturnId(), status);
            }
        }
        return existingReturnStatusMap;
    }

    private void saveReturnEvent(Set<Integer> returnIds, Map<Integer, String> existingReturnStatusMap, String returnType) {
        if(!CollectionUtils.isEmpty(returnIds)) {
            log.info("[ReceivingServiceImpl][saveReturnEvent] : Saving return event " + RETURN_RECEIVED_AT_WAREHOUSE + " for returnIds: " + returnIds);
            returnIds.forEach(returnId -> {
                List<ReturnEvent> returnEvents = returnEventService.getReturnEvent(returnId);
                Integer requestId = returnEvents.stream().map(ReturnEvent::getReturnRequestId).filter(Objects::nonNull).findAny().orElse(null);
                String existingStatus = existingReturnStatusMap.get(returnId);
                String status = existingStatus;
                if ((ReturnStatus.RETURN_REFUNDED_RECEIVING_PENDING.getName().equalsIgnoreCase(existingStatus) || ReturnStatus.RETURN_REFUNDED.getName().equalsIgnoreCase(existingStatus) || ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getName().equalsIgnoreCase(existingStatus)) && REVERSE.equalsIgnoreCase(returnType)) {
                    status = Constant.RETURN_STATUS.RETURN_REFUNDED;
                    log.info("[ReturnOrderActionServiceImpl][createUpdateReturnOrder]:: status updated to: {}", status);
                }
                if(Constant.RETURN_ORDER_STATUS.RETURN_REFUNDED.equalsIgnoreCase(status) || Constant.RETURN_ORDER_STATUS.RETURN_EXCHANGE.equalsIgnoreCase(status)){
                    returnEventService.createReturnEvent(requestId, returnId, status, "");
                }
            });
        }
    }


    @Override
    public Set<Integer> receiveItems(ReturnOrderRequestDTO returnRequest) throws Exception {
        log.info("[ReverseServiceImpl]receiveItems ::::: {}", returnRequest);
        if (null != returnRequest) {
            String returnType = null != returnRequest.getRtoItem() && returnRequest.getRtoItem() ? RTO : REVERSE;
            log.info("[ReverseServiceImpl][receiveItems] : " + returnRequest.toString());
            if(CollectionUtils.isEmpty(returnRequest.getUwOrderDTOs())){
                PurchaseOrderDetailsDTO purchaseOrderDetails = orderUtilsService.getPurchaseOrderDetails(IdentifierType.INCREMENT_ID.name(), String.valueOf(returnRequest.getIncrementId()));
                if (purchaseOrderDetails == null) {
                    log.error("[ReverseServiceImpl][receiveItems] order details not found for order : {}", returnRequest.getIncrementId());
                    return null;
                }
                returnRequest.setUwOrderDTOs(purchaseOrderDetails.getUwOrders());
                returnRequest.setOrderDTOs(purchaseOrderDetails.getOrders());
            }
            Set<Integer> returnIds = new HashSet<Integer>();
            Map<Integer, Integer> uwReturnMap = new HashMap<Integer, Integer>();
            for (ReturnItemRequestDTO returnItemRequest : returnRequest.getItems()) {
                UwOrderDTO uwOrder = returnItemRequest.getUwOrderDTO();
                if (uwOrder == null) {
                    log.error("[ReverseServiceImpl][receiveItems] requested item {} not found in order : {}", returnItemRequest.getItemId(), returnRequest.getIncrementId());
                    continue;
                }

                CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse = null;
                log.info("[ReverseServiceImpl][receiveItems] uwItemId : {}, parentUw : {}" , uwOrder.getUwItemId(), uwOrder.getParentUw());
                if (uwOrder.getParentUw() == 0) {
                    Integer returnId = null;
                    Integer parentUwCheck = uwOrder.getUwItemId();
                    if (uwReturnMap.containsKey(parentUwCheck))
                        returnId = uwReturnMap.get(parentUwCheck);
                    else {
                        returnId = getExistingReturnOrderId(returnRequest.getIncrementId());
                    }
                    Integer returnIdForItem = getExistingReturnDetailItem(uwOrder.getUwItemId());

                    String classification = uwOrder.getClassification()!=null ? uwOrder.getClassification() : "0";
                    if (returnIdForItem != null) {
                        log.info("[ReverseServiceImpl][receiveItems][createReturnItem] Overriding return id by " + returnIdForItem);
                        returnId = returnIdForItem;
                    }
                    if("11356".equalsIgnoreCase(classification)){
                        log.info("[ReverseServiceImpl][receiveItems] presecription lens");
                        continue;
                    }
                    log.info("[ReverseServiceImpl][receiveItems][uwOrder][returnId][first] returnId :::: " + returnId);
                    boolean createNewReturn = returnId == null;
                    //
                    ReturnRequest request = null;
                    Integer requestId = null;
                    String source = null;
                    if(createNewReturn) {
                        request = createReturnRequest(returnRequest, uwOrder.getUwItemId());
                        requestId = request.getId();
                        source = request.getSource();
                    }else{
                        List<ReturnDetail> returnDetails = returnOrderActionService.findByReturnIdIn(Collections.singletonList(returnId), true);
                        if(!CollectionUtils.isEmpty(returnDetails)){
                            ReturnDetail returnDetail = returnDetails.get(0);
                            requestId = returnDetail.getRequestId();
                            request = returnRequestRepository.findById(requestId).get();
                            source = request.getSource();
                        }
                    }


                    //return order creation
                    createUpdateReturnOrderResponse = createReturn(returnId, returnRequest, returnItemRequest, uwOrder, returnType, uwReturnMap, returnIds, Integer.parseInt(classification), requestId, source);

                    //refund part
                    returnId = returnId==null ? createUpdateReturnOrderResponse.getReturnId() : returnId;
                    CheckRefundSwitchActiveRequestDTO checkRefundSwitchActiveRequestDTO = new CheckRefundSwitchActiveRequestDTO();
                    checkRefundSwitchActiveRequestDTO.setRequestId(requestId);
                    checkRefundSwitchActiveRequestDTO.setUwOrder(uwOrder);
                    checkRefundSwitchActiveRequestDTO.setReturnRequest(returnRequest);
                    checkRefundSwitchActiveRequestDTO.setReturnId(returnId);
                    checkRefundSwitchActiveRequestDTO.setCreateNewReturn(createNewReturn);
                    checkRefundSwitchActiveRequestDTO.setIncrementId(returnRequest.getIncrementId());
                    checkRefundSwitchActiveRequestDTO.setReturnType(returnType);
                    checkRefundSwitchActiveRequestDTO.setCreateUpdateReturnOrderResponse(createUpdateReturnOrderResponse);
                    kafkaService.pushToKafka("check_refund_active_queue",String.valueOf(returnRequest.getIncrementId()),checkRefundSwitchActiveRequestDTO);
                }
            }
            statusUpdate(returnRequest);
            return returnIds;
        }
        return null;
    }

    private ReturnRequest createReturnRequest(ReturnOrderRequestDTO returnOrderRequest, Integer uwItemId) throws JsonProcessingException {
        String identifierType = IdentifierType.UW_ITEM_ID.name();
        String identifierValue = String.valueOf(uwItemId);
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setReturnReason("DIRECT-RECEIVING");
        returnRequest.setIdentifierType(identifierType);
        returnRequest.setIdentifierValue(identifierValue);
        returnRequest.setCreatedAt(new Date());
        returnRequest.setIncrementId(returnOrderRequest.getIncrementId());
        returnRequest.setSource(ReturnSources.WAREHOUSE.getSource());
        try{
            ReturnOrderRequestDTO returnOrderRequestDTO = objectMapper.convertValue(returnOrderRequest, ReturnOrderRequestDTO.class);
            returnOrderRequestDTO.setUwOrderDTOs(null);
            returnOrderRequestDTO.setOrderDTOs(null);
            returnOrderRequestDTO.setOrdersHeaderDTO(null);
            returnRequest.setReturnCreationRequest(objectMapper.writeValueAsString(returnOrderRequestDTO));
        }catch (Exception exception){
            log.error("[createReturnRequest] exception occurred : {}", exception.getMessage());
        }
        returnRequest = returnRequestRepository.save(returnRequest);
        returnEventService.createReturnEvent(returnRequest.getId(), null, RETURN_REQUEST_CREATED, "");
        return returnRequest;
    }

    private boolean validateAllUwItemIds(ReturnOrderRequestDTO returnOrderRequest, Map<Integer, UwOrderDTO> uwItemIdToUwOrderDtoMap) {
        for (ReturnItemRequestDTO returnItemRequest : returnOrderRequest.getItems()) {
            if (null == returnItemRequest.getItemId() || StringUtils.isBlank(returnItemRequest.getQcStatus())) {
                createUnicomApiLog(returnOrderRequest.getIncrementId(), returnOrderRequest.getReferenceOrderCode(), "Failure", "Null or Empty parameters (unitItemId/QcStatus)in items request");
                log.error("[ReverseServiceImpl][returnItems] : Null or Empty parameters (unitItemId/QcStatus)in items request");
                return false;
            }
            if (Objects.isNull(uwItemIdToUwOrderDtoMap.get(returnItemRequest.getItemId()))
                    || !uwItemIdToUwOrderDtoMap.get(returnItemRequest.getItemId()).getUnicomOrderCode().equalsIgnoreCase(returnOrderRequest.getReferenceOrderCode())) {
                createUnicomApiLog(returnOrderRequest.getIncrementId(), returnOrderRequest.getReferenceOrderCode(), "Failure", "Item not found in Inventory");
                return false;
            }
        }
        return true;
    }

    private String createReversePickupNexs(ReturnOrderRequestDTO returnRequest, String facility, Boolean rtoItem, List<UwOrderDTO> uwOrderDTOS) throws ReturnRequestFailException {
        log.info("[createReversePickupUnicom] returnRequest : {}, facility : {}", returnRequest.toString(), facility);
        NexsReturnResponse reversePickupResponse = null;
        try {
            if(enableNexsAtStore.equalsIgnoreCase("true"))
            {
                log.info("Nexs at store is enabled, calling createReturnInNexsV2");
                reversePickupResponse = returnNexsService.createReturnInNexsV2(returnRequest.getItems(), returnRequest.getReferenceOrderCode(), returnRequest.getIncrementId(), facility, uwOrderDTOS);
            }else{
                log.info("Nexs at store is disabled, calling createReversePickupNexs");
                reversePickupResponse = returnNexsService.createReversePickupNexs(returnRequest.getItems(), returnRequest.getReferenceOrderCode(), returnRequest.getIncrementId(), uwOrderDTOS);
            }
        } catch (Exception e) {
            log.error("[ReverseServiceImpl][returnItems]" + e.getMessage());
            String reversePickupExists = ".*SaleOrderItem.*already requested to be reverse picked.*";
            if (e.getMessage() != null && e.getMessage().matches(reversePickupExists)) {
                log.error("[ReverseServiceImpl][returnItems] :: nexs exception matched for SaleOrderItem already reverse picked");
                return Constant.REVERSE_PICKUP_ALREADY_EXISTS;
            }
            throw new ReturnRequestFailException("UNICOM_EXCEPTION", "[ReverseServiceImpl][returnItems]" + e.getMessage());
        }
        if (reversePickupResponse != null && returnNexsFailedKey.equalsIgnoreCase(reversePickupResponse.getMeta().getMessage())) {
            throw new ReturnRequestFailException("NEXES_CREATE_RETURN_EXCEPTION", "Nexs exception");
        }
        return (null != reversePickupResponse ? reversePickupResponse.getMeta().getCode() : null);
    }

    private String createReversePickupUnicom(ReturnOrderRequestDTO returnRequest, String facility, Boolean rtoItem) throws ReturnRequestFailException {
        log.info("[createReversePickupUnicom] returnRequest : {}, facility : {}", returnRequest.toString(), facility);
        ResponseEntity<String> responseEntity = null;
        JsonNode rootNode = null;
        try {
            responseEntity = returnUnicomService.createReversePickup(returnRequest.getItems(), null, returnRequest.getReferenceOrderCode(), facility, rtoItem);
            log.info("[createReversePickupUnicom] response from unicom return api is " + responseEntity);
            if (Objects.isNull(responseEntity)) {
                log.error("[[createReversePickupUnicom]] unable to create reverse pickup for unicom order code ");
                throw new ReturnRequestFailException("UNICOM_EXCEPTION", "[ReverseServiceImpl][returnItems]");
            }
            ObjectMapper objectMapperReversePickup = new ObjectMapper();
            JsonNode bodyNode = objectMapperReversePickup.readTree(responseEntity.getBody());
            String response = bodyNode.path(Constant.JSON_CONSTANT.RESPONSE).asText();
            rootNode = objectMapper.readTree(response);
            String successful = rootNode.path(Constant.JSON_CONSTANT.SUCCESSFUL).asText();
            StringBuilder failureMessage = new StringBuilder();
            if (!successful.equalsIgnoreCase(Constant.JSON_CONSTANT.TRUE)) {
                JsonNode errors = rootNode.path("errors");
                String reversePickupExists = ".*SaleOrderItem.*already requested to be reverse picked.*";
                for (JsonNode jsonNode : errors) {
                    JsonNode error = jsonNode.path("description");
                    failureMessage.append(error.asText());
                }
                if (StringUtils.isBlank(failureMessage.toString())) {
                    failureMessage = new StringBuilder("Unicommerce returned false:something wrong with input data");
                }
                if (StringUtils.isNotBlank(failureMessage.toString()) && failureMessage.toString().matches(reversePickupExists)) {
                    log.error("[createReversePickupUnicom][returnItems] :: unicom exception matched for SaleOrderItem already reverse picked");
                    return Constant.REVERSE_PICKUP_ALREADY_EXISTS;
                }
                throw new ReturnRequestFailException("UNICOM_EXCEPTION", "[ReverseServiceImpl][returnItems]" + failureMessage);
            }
        } catch (Exception e) {
            log.error("[ReverseServiceImpl][returnItems]" + e.getMessage());
            String reversePickupExists = ".*SaleOrderItem.*already requested to be reverse picked.*";
            if (e.getMessage() != null && e.getMessage().matches(reversePickupExists)) {
                log.error("[ReverseServiceImpl][returnItems] :: unicom exception matched for SaleOrderItem already reverse picked");
                return Constant.REVERSE_PICKUP_ALREADY_EXISTS;
            }
            throw new ReturnRequestFailException("UNICOM_EXCEPTION", "[ReverseServiceImpl][returnItems]" + e.getMessage());
        }
        return rootNode.path("reversePickupCode").asText();
    }

    private void createUnicomApiLog(Integer incrementId, String unicomOrderCode, String status, String message) {
        UnicomApiLogDto unicomApiLogDto;
        if (null != incrementId && null != unicomOrderCode && null != message && null != status) {
            unicomApiLogDto = new UnicomApiLogDto();
            unicomApiLogDto.setIncrementId(incrementId.toString());
            unicomApiLogDto.setType("I");
            unicomApiLogDto.setApi(Constant.ORDER_STATE_OR_STATUS.MARK_ORDER_RETURN);
            unicomApiLogDto.setStatus(status);
            unicomApiLogDto.setMessage(message);
            unicomApiLogDto.setException(false);
            unicomApiLogDto.setEMsg("");
            unicomApiLogDto.setCreateDatetime(new Date());
            unicomApiLogDto.setUnicomOrderCode(unicomOrderCode);
            CompletableFuture<Void> saveUnicomApiLogResponse = CompletableFuture.runAsync(() -> {
                orderOpsFeignClient.saveUnicomApiLog(unicomApiLogDto);
            });
            saveUnicomApiLogResponse.thenAccept(body -> {
                log.info("[ReceivingServiceImpl] [saveUnicomApiLog] response is :" + body);
            });
        }
    }

    private Boolean checkForDuplicateReturn(List<Integer> itemIds) {
        try{
            if (null != itemIds && !itemIds.isEmpty()) {
                List<String> receivingFlagByByUwItemId = returnOrderActionService.findReturnOrderReceivingFlagByByUwItemId(itemIds, Constant.RETURN_ITEM_STATUS, Constant.RETURN_TYPE.REVERSE);
                if (!CollectionUtils.isEmpty(receivingFlagByByUwItemId)) {
                    boolean receivingFlag = receivingFlagByByUwItemId.stream().anyMatch("yes"::equalsIgnoreCase);
                    log.info("[checkForDuplicateReturn] is any item already received : {}", receivingFlag);
                }
            }
        }catch (Exception exception){
            log.error("[checkForDuplicateReturn]");
        }
        log.info("[checkForDuplicateReturn] returning false");
        return false;
    }

    private boolean validateReturnRequest(ReturnOrderRequestDTO returnRequest) {
        return (Objects.nonNull(returnRequest)
                && null != returnRequest.getIncrementId()
                && returnRequest.getIncrementId() != 0
                && !StringUtils.isEmpty(returnRequest.getReferenceOrderCode())
                && !CollectionUtils.isEmpty(returnRequest.getItems()));
    }

    private void statusUpdate(ReturnOrderRequestDTO returnOrderRequest) {
        OrderStatusUpdateDetails request = new OrderStatusUpdateDetails();
        request.setState("closed");
        request.setStatus("closed");
        request.setIncrementId(returnOrderRequest.getIncrementId());
        request.setUnicomOrderCode(returnOrderRequest.getReferenceOrderCode());
        try{
            kafkaService.pushToKafka(STATUS_UPDATE,String.valueOf(returnOrderRequest.getIncrementId()),objectMapper.writeValueAsString(request));
        }catch (Exception exception){
            log.error("[statusUpdate] exception while pushing to kafka : "+exception);
        }
    }

    private void backSync(Integer incrementId, List<Integer> uwItemIds, String status){
        log.info("[backSync] order : {}, status : {}, uwItemIds : {}", incrementId, status, uwItemIds);
        try{
            CompletableFuture<Void> backSyncCompletableFuture = CompletableFuture.runAsync(() -> orderOpsFeignClient.callBackSyncApi(uwItemIds, status));
            backSyncCompletableFuture
                    .thenAccept(body -> {
                        log.info("[backSync] Response received: " + body);
                    })
                    .exceptionally(ex -> {
                        ex.printStackTrace();
                        return null;
                    });
        }catch (Exception exception){
            log.error("[backSync] exception : "+exception);
        }
    }

    private void returnRefundOperationsV1(Integer returnId, ReturnOrderRequestDTO returnRequest, CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse) {
        log.info("[returnRefundOperationsV1] returnId : {}, incrementId : {}", returnId, returnRequest.getIncrementId());
        OldRefundRequestCreateDTO oldRefundRequestCreateDTO = new OldRefundRequestCreateDTO();
        oldRefundRequestCreateDTO.setReturnId(returnId);
        oldRefundRequestCreateDTO.setDoRefund(returnRequest.getDoRefund());
        oldRefundRequestCreateDTO.setSource(returnRequest.getSource());
        oldRefundRequestCreateDTO.setRefundMethod(returnRequest.getRefundMethod());
        oldRefundRequestCreateDTO.setIncrementId(returnRequest.getIncrementId());
        oldRefundRequestCreateDTO.setAwaitedRtoItem(false);
        oldRefundRequestCreateDTO.setReturnStatus(createUpdateReturnOrderResponse.getReturnStatus());
        try{
            log.info("[returnRefundOperationsV1] request dto : {}", oldRefundRequestCreateDTO);
            kafkaService.pushToKafka(CREATE_REFUND_REQUEST_QUEUE,String.valueOf(returnId),oldRefundRequestCreateDTO);
        }catch (Exception exception){
            log.error("[returnRefundOperationsV1] exception occurred : "+exception);
        }
    }

    private void returnRefundOperationsV2(Integer returnId) {
        log.info("[returnRefundOperationsV2] returnId {} ", returnId);
        try {
            returnOrderItemService.updateReturnOrderItem(returnId, "initiated_stockin");

            CheckRefundInitiatedRequest checkRefundInitiatedRequest=new CheckRefundInitiatedRequest();
            checkRefundInitiatedRequest.setIdentifierType(com.lenskart.refund.client.model.enums.IdentifierType.RETURN_ID);
            checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(returnId));
            CheckRefundInitiatedResponse checkRefundInitiatedResponse= refundUtilsService.isRefundInitiated(checkRefundInitiatedRequest);
            if(checkRefundInitiatedResponse==null || checkRefundInitiatedResponse.getRefundRequestDTO()==null || checkRefundInitiatedResponse.getRefundRequestDTO().getId()==null){
                log.error("returnRefundOperationsV2 checkRefundInitiatedResponse={}",checkRefundInitiatedResponse);
                return;
            }

            RefundRequestStatus refundStatus = checkRefundInitiatedResponse.getRefundRequestDTO().getStatus();
            RefundTarget refundTarget = checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget();
            if (RefundRequestStatus.PROCESSED.equals(refundStatus) || RefundTarget.EXCHANGE.equals(refundTarget)) {
                Optional<ReturnDetail> returnOrder = returnOrderActionService.findReturnOrderById(returnId);
                String returnOrderStatus = null;
                if(returnOrder.isPresent()){
                    returnOrderStatus = returnOrderActionService.getReturnOrderStatus(returnOrder.get());
                }
                if (returnOrder.isPresent() && !RefundPredicate.IS_RETURN_IN_DRAFTED_REJECTED_STATE.test(returnOrderStatus)
                        && RefundPredicate.IS_RETURN_NOT_IN_RPU_CYCLE.test(returnOrderStatus)) {
                    String status = RefundTarget.EXCHANGE.equals(refundTarget) ? Constant.RETURN_ORDER_STATUS.RETURN_EXCHANGE : Constant.RETURN_ORDER_STATUS.RETURN_REFUNDED;
                    log.info("[returnRefundOperationsV2] status to update is " + refundStatus);
                    Integer requestId = returnEventService.getRequestId(returnId);
                    returnEventService.createReturnEvent(requestId, returnId, status, "");
                    log.info("[returnRefundOperationsV2] return order status is ===---========-----===== " + status);
                }
            }
        }
        catch (Exception e) {
            log.error("[returnRefundOperationsV2] Exception {}", e.getMessage());
        }
    }

    private void createAndInitiateRefundRequestOnReturn(Integer returnId, ReturnOrderRequestDTO returnRequest, UwOrderDTO uwOrderDTO) throws ExecutionException, InterruptedException {
        OrdersHeaderDTO ordersHeaderDTO = returnRequest.getOrdersHeaderDTO();

        CompletableFuture<Amount> amountCompletableFuture = CompletableFuture.supplyAsync(() -> getRefundAmount(uwOrderDTO.getIncrementId(), returnId));
        CompletableFuture<String> refundMethodCompletableFuture = CompletableFuture.supplyAsync(() -> getRefundMethod(uwOrderDTO, returnRequest, ordersHeaderDTO));
        CompletableFuture<Boolean> isFranchiseRefundRequiredCompletableFuture = CompletableFuture.supplyAsync(() -> getIsFranchiseRefundRequired(ordersHeaderDTO.getIncrementId(), returnRequest.getSource()));

        CompletableFuture.allOf(amountCompletableFuture, refundMethodCompletableFuture, isFranchiseRefundRequiredCompletableFuture).join();

        Amount amount = amountCompletableFuture.get();
        String refundMethod = refundMethodCompletableFuture.get();
        Boolean isFranchiseRefundRequired = isFranchiseRefundRequiredCompletableFuture.get();

        CreateRefundRequest createRefundRequest = new CreateRefundRequest();
        createRefundRequest.setIdentifierType(com.lenskart.refund.client.model.enums.IdentifierType.RETURN_ID);
        createRefundRequest.setIdentifierValue(String.valueOf(returnId));
        createRefundRequest.setRefundReason(RefundReason.DIRECT_RECEIVING);
        createRefundRequest.setOrderId((long) ordersHeaderDTO.getIncrementId());
        createRefundRequest.setRefundTarget(RefundTarget.getByValue(refundMethod));
        createRefundRequest.setRefundAmount(new RefundAmount(BigDecimal.valueOf(amount.getPrice()), amount.getCurrency()));
        createRefundRequest.setType(RefundType.CREDIT);
        createRefundRequest.setRefundTriggerPoint(RefundTriggerPoint.WH_RECEIVING.getName());
        createRefundRequest.setFranchiseRefundRequired(isFranchiseRefundRequired);
        createRefundRequest.setClient(returnRequest.getSource());
        kafkaService.pushToKafka(REFUND_INITIATION_ON_RECEIVING_QUEUE,String.valueOf(returnId),createRefundRequest);
    }

    private boolean getIsFranchiseRefundRequired(Integer incrementId, String source){
        return refundUtilsService.getIsFranchiseRefundRequired(incrementId, RefundTriggerPoint.WH_RECEIVING, source, RefundReason.DIRECT_RECEIVING);
    }

    private Amount getRefundAmount(Integer incrementId, Integer returnId){
        try {
            return orderOpsFeignClient.getRefundAmount(incrementId, returnId);
        } catch (FeignException e) {
            log.error("Error fetching refund amount: {}", e.getMessage());
            return new Amount();
        }
    }

    private String getRefundMethod(UwOrderDTO uwOrderDTO, ReturnOrderRequestDTO returnOrderRequest, OrdersHeaderDTO ordersHeaderDTO) {
        OrdersDTO ordersDTO = returnOrderRequest.getOrderDTOs()
                .stream()
                .filter(ord -> Objects.equals(ord.getItemId(), uwOrderDTO.getItemId()))
                .findFirst()
                .orElse(null);
        String paymentMethod = getPaymentMethod(uwOrderDTO, returnOrderRequest, ordersDTO != null ? ordersDTO.getMethod() : null);

        String refundInitiatedAt = RefundInitiatedAt.DIRECT_RECEIVING.getValue();
        if(ordersHeaderDTO.getIsExchangeOrder()!=null && ordersHeaderDTO.getIsExchangeOrder()){
            refundInitiatedAt = RefundInitiatedAt.EXCHANGE_DIRECT_RECEIVING.getValue();
        }

        //call get refund methods api
         RefundMethodResponse refundMethodResponse = refundUtilsService.getRefundMethod(refundInitiatedAt, ordersHeaderDTO.getPaymentMode(),
                        paymentMethod, ordersHeaderDTO.getLkCountry(), ordersHeaderDTO.getIncrementId());

        return refundMethodResponse != null ? refundMethodResponse.getAutoRefundMethod() : null;
    }

    private String getPaymentMethod(UwOrderDTO uwOrderDTO, ReturnOrderRequestDTO returnOrderRequest, String orderMethod) {
        return orderUtilsService.getPaymentMethod(uwOrderDTO, returnOrderRequest.getUwOrderDTOs(), returnOrderRequest.getOrderDTOs(), orderMethod);
    }

    private Integer getExistingReturnOrderId(Integer incrementId) {
        log.info("Method getExistingReturnOrderId :::: "+incrementId);
        if(null != incrementId) {
            List<ReturnDetail> returnOrders = returnOrderActionService.findAllByIncrementId(incrementId);
            if(null != returnOrders && !returnOrders.isEmpty()) {
                for(ReturnDetail returnOrder : returnOrders) {
                    String status = returnOrderActionService.getReturnOrderStatus(returnOrder);
                    log.info("Method getExistingReturnOrderId :::: "+incrementId+" and return status ::: "+status);
                    if(null != status && RETURN_ITEM_NOT_RECEIVED_STATUS.contains(status)) {
                        return returnOrder.getId();
                    }
                }
            }
        }
        return null;
    }

    private Integer getExistingReturnDetailItem(Integer uwItemId) {
        Integer returnId = null;
        if(null != uwItemId) {
            ReturnDetailItem returnOrderItem = returnOrderItemService.findByUwItemId(uwItemId);
            log.info("[getExistingReturnDetailItem] Fetching return order items for uwItemId "+ uwItemId);
            returnId = returnOrderItem != null ? returnOrderItem.getReturnId() : null;
        }
        return returnId;
    }

    private CreateUpdateReturnOrderResponseDTO getReturnOrderResponse(ReturnOrderRequestDTO returnRequest, Integer returnId, String status, String returnType, Integer requestId, UwOrderDTO uwOrderDTO, String receiving_flag) throws ReturnRequestException {
        return returnOrderActionService.createUpdateReturnOrder(returnRequest, returnId, status, returnType, requestId, uwOrderDTO, receiving_flag);
    }

    private CreateUpdateReturnOrderResponseDTO createReturn(Integer returnId, ReturnOrderRequestDTO returnRequest, ReturnItemRequestDTO returnItemRequest, UwOrderDTO uwOrder, String returnType, Map<Integer, Integer> uwReturnMap, Set<Integer> returnIds, int classification, Integer existingRequestId, String source) throws Exception {
        CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse = null;
        if (returnId == null) {

            Integer parentUw = uwOrder.getUwItemId();
            if (uwReturnMap.containsKey(parentUw)) {
                returnId = uwReturnMap.get(parentUw);
            } else {
                Integer requestId = existingRequestId !=null ? existingRequestId : getRequestId(returnId);
                returnRequest.setGroupId(returnGroupRepository.save(new ReturnGroup()).getId());
                createUpdateReturnOrderResponse = getReturnOrderResponse(returnRequest, returnId, ReturnStatus.RETURN_RECEIVED.getName(), returnType, requestId, uwOrder, Constant.RECEIVING_FLAG.YES);
                returnId = (null != createUpdateReturnOrderResponse ? createUpdateReturnOrderResponse.getReturnId() : returnId);
                Optional<ReturnItemRequestDTO> itemRequestOptional = getReturnItemRequest(returnRequest, uwOrder.getUwItemId());
                ReturnItemDTO item = new ReturnItemDTO();
                item.setUwItemId(uwOrder.getUwItemId());
                if (itemRequestOptional.isPresent()) {
                    ReturnItemRequestDTO request = itemRequestOptional.get();
                    item.setQcStatus(request.getQcStatus());
                }
                returnOrderItemService.createReturnItem(item, uwOrder, returnId, returnType, null, source, uwOrder.getItemId());
                updateReturnRefundRule(returnRequest.getSource(), returnId, uwOrder.getUwItemId());
                uwReturnMap.put(parentUw, returnId);
                updateB2BItems(returnRequest, returnItemRequest, uwOrder, returnType, uwReturnMap, returnIds, classification, requestId, item, createUpdateReturnOrderResponse);
            }
        } else {
            Optional<ReturnDetail> returnOrderOpt = returnOrderActionService.findReturnOrderById(returnId);
            ReturnDetail returnOrder = returnOrderOpt.get();
            returnRequest.setGroupId(returnOrder.getGroupId());
            Integer parentUw = uwOrder.getParentUw() == 0 ? uwOrder.getUwItemId() : uwOrder.getParentUw();
            Integer requestId = existingRequestId !=null ? existingRequestId : getRequestId(returnId);
            createUpdateReturnOrderResponse = getReturnOrderResponse(returnRequest, returnId, ReturnStatus.RETURN_RECEIVED.getName(), returnType, requestId, uwOrder, Constant.RECEIVING_FLAG.YES);
            returnId = (null != createUpdateReturnOrderResponse ? createUpdateReturnOrderResponse.getReturnId(): null);
            uwReturnMap.put(parentUw, returnId);
            Optional<ReturnItemRequestDTO> itemRequestOptional = getReturnItemRequest(returnRequest, uwOrder.getUwItemId());
            ReturnItemDTO item = new ReturnItemDTO();
            item.setUwItemId(uwOrder.getUwItemId());
            if (itemRequestOptional.isPresent()) {
                ReturnItemRequestDTO request = itemRequestOptional.get();
                item.setQcStatus(request.getQcStatus());
            }
            updateB2BItems(returnRequest, returnItemRequest, uwOrder, returnType, uwReturnMap, returnIds, classification, requestId, item, createUpdateReturnOrderResponse);
        }
        returnIds.add(returnId);
        updateReturnReason(uwOrder.getUwItemId(), returnId);
        return createUpdateReturnOrderResponse;
    }

    private static Optional<ReturnItemRequestDTO> getReturnItemRequest(ReturnOrderRequestDTO returnRequest, Integer uwItemId) {
        return returnRequest.getItems().stream()
                .filter(returnItemRequest1 -> Objects.equals(returnItemRequest1.getItemId(), uwItemId))
                .findAny();
    }

    private void updateReturnReason(Integer uwItemId, Integer returnId) {
        returnReasonService.saveReturnReason(uwItemId, returnId);
    }

    private Integer getRequestId(Integer returnId) {
        List<ReturnEvent> returnEvents = returnEventService.getReturnEvent(returnId);
        Integer requestId = 0;
        if (!CollectionUtils.isEmpty(returnEvents)) {
            requestId = returnEvents.get(0).getReturnRequestId();
        }
        return requestId;
    }

    private void updateReturnRefundRule(String source, Integer returnId, Integer uwItemId) {
        returnRefundRuleService.updateReturnRefundRule(uwItemId, returnId, source);
    }

    private void updateB2BItems(ReturnOrderRequestDTO returnRequest, ReturnItemRequestDTO returnItemRequest, UwOrderDTO uwOrder , String returnType, Map<Integer, Integer> uwReturnMap, Set<Integer> returnIds, int classification, Integer requestId, ReturnItemDTO item, CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse) throws ReturnRequestException {
        Optional<UwOrderDTO> uwOrderDTOOpt = returnRequest.getUwOrderDTOs().stream().filter(uwOrd -> B2B.equalsIgnoreCase(uwOrd.getProductDeliveryType()) && Objects.equals(uwOrd.getB2bRefrenceItemId(), uwOrder.getUwItemId())).findAny();
        if(uwOrderDTOOpt.isPresent()){
            UwOrderDTO uwOrderB2BDTO = uwOrderDTOOpt.get();
            CreateUpdateReturnOrderResponseDTO response = getReturnOrderResponse(returnRequest, null, ReturnStatus.RETURN_RECEIVED.getName(), returnType, requestId, uwOrderB2BDTO, Constant.RECEIVING_FLAG.YES);
            returnOrderItemService.createReturnItem(item, uwOrderB2BDTO, response.getReturnId(), returnType, null, returnRequest.getSource(), uwOrderB2BDTO.getItemId());
            updateReturnRefundRule(returnRequest.getSource(), response.getReturnId(), uwOrderB2BDTO.getUwItemId());
            uwReturnMap.put(uwOrderB2BDTO.getUwItemId(), response.getReturnId());
            returnIds.add(response.getReturnId());
            log.info("[updateB2BItems] setting virtual return details, returnId : {}, incrementId : {}, uwItemId :{}, facility :{}", response.getReturnId(), returnRequest.getIncrementId(),
                    uwOrderB2BDTO.getUwItemId(), uwOrderB2BDTO.getFacilityCode());
            createUpdateReturnOrderResponse.setVirtualReturnId(response.getReturnId());
            createUpdateReturnOrderResponse.setVirtualUWItemId(uwOrderB2BDTO.getUwItemId());
            createUpdateReturnOrderResponse.setFacilityCode(uwOrderB2BDTO.getFacilityCode());

        }
    }
}
