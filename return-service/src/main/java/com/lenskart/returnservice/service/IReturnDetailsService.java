package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.OrderTelephoneMapperRequestDTO;
import com.lenskart.returncommon.model.AwbReverseDto;
import com.lenskart.returncommon.model.dto.CommRequestDTO;
import com.lenskart.returncommon.model.dto.ReturnDetailsForSmsDTO;
import com.lenskart.returncommon.model.request.AwbNumberValidationRequest;
import com.lenskart.returncommon.model.request.ReturnDetailInfoUtilRequestDto;
import com.lenskart.returncommon.model.dto.ReturnDTO;
import com.lenskart.returncommon.model.response.GetFiltersResponse;
import com.lenskart.returncommon.model.response.ReturnCancellabilityResponse;
import com.lenskart.returncommon.model.response.ReturnDetailedResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface IReturnDetailsService {
    ReturnDetailsResponse getReturnDetailsByMagentoId(Long magentoItemId,ReturnDetailInfoUtilRequestDto returnDetailInfoUtilRequestDto);

    ReturnCancellabilityResponse getReturnCancellabilityForItem(Integer uwItemId);

    ReturnDetailsForSmsDTO getRefundDetailsForSMS(CommRequestDTO commRequestDTO);
    List<ReturnDTO> getReturnDetails(Map<String, Object> filterParams) throws ParseException;

    GetFiltersResponse getFilters();

    AwbReverseDto validateReturnTypeReverse(AwbNumberValidationRequest validationRequest);
    List<ReturnDetailedResponse> getReturnsDetailedResponse(OrderTelephoneMapperRequestDTO returnDetailsRequest);
}
