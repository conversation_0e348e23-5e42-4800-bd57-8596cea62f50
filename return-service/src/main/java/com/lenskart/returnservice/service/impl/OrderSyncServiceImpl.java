package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.OrderStatusSyncDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IOrderSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_SYNC_QUEUE;

@Service
@Slf4j
public class OrderSyncServiceImpl implements IOrderSyncService {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private IKafkaService kafkaService;
    @Override
    public void saveOrderSync(Integer incrementId, String status, String trackingNo) {
        OrderStatusSyncDTO statusSyncDTO = OrderStatusSyncDTO.builder()
                .status(status)
                .trackingNo(trackingNo)
                .incrementId(incrementId)
                .build();
        kafkaService.pushToKafka(ORDER_SYNC_QUEUE, String.valueOf(incrementId), statusSyncDTO);
    }
}
