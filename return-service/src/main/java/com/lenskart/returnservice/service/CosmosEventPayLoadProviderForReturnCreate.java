package com.lenskart.returnservice.service;
/* Created by rajiv on 07/02/25 */

import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.Returns;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import org.springframework.stereotype.Component;

import java.util.Map;
@Component
public interface CosmosEventPayLoadProviderForReturnCreate {
    Map<String, Object> getPayloadForReturnCreate(ReturnCreationRequestDTO returnCreationRequest, ReturnCreationResponse returnCreationResponse, Map<Long, ReturnItemDTO> magentoItemToReturnItemMap, Returns returns);

}
