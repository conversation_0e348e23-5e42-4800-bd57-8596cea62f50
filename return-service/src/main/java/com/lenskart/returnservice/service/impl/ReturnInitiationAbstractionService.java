package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.PrimaryReturnReasons;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnrepository.entity.SecondaryReturnReason;
import com.lenskart.returnrepository.repository.PrimaryReturnReasonsRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnrepository.repository.SecondaryReturnReasonRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.lenskart.returncommon.utils.Constant.EVENT.RETURN_REQUEST_CREATED;
import static com.lenskart.returncommon.utils.Constant.QC_STATUS.QC_PASS;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.UW_ORDERS_STATUS_UPDATE;

@Slf4j
@Service
public abstract class ReturnInitiationAbstractionService {
    @Autowired
    private INexsFacilityService nexsFacilityService;
    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Value("${dealskart.manesar.facility}")
    private String facilityCode;
    @Autowired
    private ReturnRequestRepository returnRequestRepository;
    @Autowired
    private IReturnEventService returnEventService;
    @Autowired
    private PrimaryReturnReasonsRepository primaryReturnReasonsRepository;
    @Autowired
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;
    @Autowired
    private IReturnCreationService returnCreationService;
    @Autowired
    private IKafkaService kafkaService;
    @Autowired
    private ICommunicationService communicationService;
    private final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    protected PurchaseOrderDetailsDTO getOrderDetails(ReturnCreationRequest returnCreationRequest, Map<Long, ReturnItem> magentoItemToReturnItemMap, Map<Long, String> returnIntentMap, List<ReturnItem> exchangeItemList, List<ReturnItem> returnItemsUpdated, List<UwOrderDTO> uwOrders, Map<ReturnItem, Integer> productIdsMap) {
        PurchaseOrderDetailsDTO purchaseOrderDetails = new PurchaseOrderDetailsDTO();
        for (ReturnItem returnItem : returnCreationRequest.getItems()) {

            returnIntentMap.put(returnItem.getMagentoId(), returnItem.getRefundMethodRequest());
            if (null != returnItem.getRefundMethodRequest() && returnItem.getRefundMethodRequest().toLowerCase().contains("exchange")) {
                if (returnItem.getRefundMethodRequest().contains("Same Product")) {
                    exchangeItemList.add(returnItem);
                }
                returnItem.setRefundMethodRequest("exchange");
            }

            String identifierType = getIdentifierType(returnItem.getUwItemId());
            String identifierValue = getIdentifierValue(identifierType, returnItem.getUwItemId(), returnItem.getMagentoId());
            purchaseOrderDetails = getPurchaseOrderDetails(identifierType, identifierValue);

            if (purchaseOrderDetails != null) {
                for (OrdersDTO order : purchaseOrderDetails.getOrders()) {
                    UwOrderDTO uwOrderDTO = purchaseOrderDetails.getUwOrders()
                            .stream()
                            .filter(uw -> uw.getItemId() == order.getItemId())
                            .findAny().orElse(null);

                    if (uwOrderDTO != null) {
                        if (filterReturnOrdersForB2B(order, uwOrderDTO, facilityCode, 11356)) {
                            uwOrders.add(uwOrderDTO);
                            returnItem.setUwItemId(uwOrderDTO.getUwItemId());
                            returnItemsUpdated.add(returnItem);
                            productIdsMap.put(returnItem, uwOrderDTO.getProductId());
                        }
                    }
                }

            } else {
                throw new NoSuchElementException("No order found for the given magento item id");
            }
            magentoItemToReturnItemMap.put(returnItem.getMagentoId(), returnItem);
        }
        return purchaseOrderDetails;
    }

    protected boolean filterReturnOrdersForB2B(OrdersDTO ordersDTO, UwOrderDTO uwOrderDTO, final String dealskartFacilityCode, final int filterClassification) {
        log.info("[filterReturnOrdersForB2B] fetching nexs facilities from system_preference key => " + Constant.SYSTEM_PREFERENCE_KEYS.NEXS_FACILITIES);
        String nexsFacilites = nexsFacilityService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.NEXS_FACILITIES, Constant.SYSTEM_PREFERENCE_KEYS.NEXS_FACILITIES);
        log.info("[filterReturnOrdersForB2B] fetching nexs facilities from system_preference value => " + nexsFacilites);
        List<String> nexsfacilityList = new ArrayList<String>(Arrays.asList(nexsFacilites.split(",")));
        log.info("[filterReturnOrdersForB2B] for nexsfacilityList =>" + nexsfacilityList);
        boolean filteredReturnOrders = !uwOrderDTO.getClassification()
                .equalsIgnoreCase(String.valueOf(filterClassification))
                && !(Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(ordersDTO.getProductDeliveryType()) && (ordersDTO.getFacilityCode().equalsIgnoreCase(dealskartFacilityCode) || nexsfacilityList.contains(ordersDTO.getFacilityCode())));
        log.info("[filterReturnOrdersForB2B] uwItemId : {}, filteredReturnOrders : {}", uwOrderDTO.getUwItemId(), filteredReturnOrders);
        return filteredReturnOrders;
    }

    protected void enrichReturnCreationRequest(ReturnCreationRequest returnCreationRequest,  Map<ReturnItem,Integer> productIdsMap, List<ReturnItem> returnItemsUpdated, Integer incrementId) {
        returnCreationRequest.setItems(returnItemsUpdated);
        returnCreationRequest.setProductIdsMap(productIdsMap);
        returnCreationRequest.setIncrementId(incrementId);
    }

    protected ReturnCreationResponse createReturn(PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, ReturnCreationRequestDTO returnCreationRequest, ReverseCourierDetail reverseCourierDetail, ReturnRequest returnRequest) throws Exception {
        log.info("[WebReturnInitiationServiceImpl][createReturn] Creating return and making relevant entries for increment id {}", purchaseOrderDetailsDTO.getOrders().get(0).getIncrementId());
        returnCreationRequest.getItems().forEach(item -> item.setQcStatus(QC_PASS));        //setting qc status as Pass as a default for all items
        return returnCreationService.createReturn(purchaseOrderDetailsDTO, returnCreationRequest, reverseCourierDetail, returnCreationRequest.getReturnSource().getSource(), returnRequest);
    }

    protected void statusUpdate(List<UwOrderDTO> uwOrderDTOS) {
        try{
            List<Integer> uwItemIds = uwOrderDTOS.stream().map(UwOrderDTO::getUwItemId).toList();
            kafkaService.pushToKafka(UW_ORDERS_STATUS_UPDATE, String.valueOf(uwOrderDTOS.get(0).getIncrementId()), objectMapper.writeValueAsString(uwItemIds));
        }catch (Exception exception){
            log.error("[statusUpdate] exception : "+exception);
        }
    }

    protected void statusUpdate(List<UwOrderDTO> uwOrderDTOS, List<ReturnItemDTO> returnItemDTOS, Integer returnId) {
        try{
            List<OrderStatusUpdateDTO> orderStatusUpdateDTOS = new ArrayList<>();
            String nexsFacilites = nexsFacilityService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.NEXS_FACILITIES, Constant.SYSTEM_PREFERENCE_KEYS.NEXS_FACILITIES);
            log.info("[statusUpdate] fetching nexs facilities from system_preference value => "+nexsFacilites);
            List<String> nexsfacilityList = new ArrayList<>(Arrays.asList(nexsFacilites.split(",")));
            log.info("[statusUpdate] nexsfacilityList =>" + nexsfacilityList);
            for(UwOrderDTO uwOrderDTO : uwOrderDTOS){
                for(ReturnItemDTO returnItemDTO : returnItemDTOS){
                    log.info("[statusUpdate] updating for uwItemId : {}", returnItemDTO.getUwItemId());
                    if(Objects.equals(returnItemDTO.getUwItemId(), uwOrderDTO.getUwItemId())){
                        log.info("[statusUpdate] uwItemId : {}", returnItemDTO.getUwItemId());
                        Integer incrementId = uwOrderDTO.getIncrementId();
                        String unicomOrderCode = uwOrderDTO.getUnicomOrderCode();
                        String shipmentStatus = uwOrderDTO.getShipmentStatus();
                        if("B2B".equalsIgnoreCase(uwOrderDTO.getProductDeliveryType())){
                            Integer b2bRefrenceItemId = uwOrderDTO.getB2bRefrenceItemId();
                            Optional<UwOrderDTO> b2bUwOrderOpt = uwOrderDTOS.stream().filter(uwOrderDTO1 -> Objects.equals(uwOrderDTO1.getUwItemId(), b2bRefrenceItemId)).findFirst();
                            if(b2bUwOrderOpt.isPresent()){
                                ReturnDetail alternateB2BReturnDetails = returnOrderActionService.getAlternateB2BReturnDetails(returnId);
                                Integer b2BReturnId = returnId;
                                if(alternateB2BReturnDetails != null){
                                    b2BReturnId = alternateB2BReturnDetails.getId();
                                }
                                orderStatusUpdateDTOS.add(new OrderStatusUpdateDTO(incrementId, b2bUwOrderOpt.get().getUnicomOrderCode(), b2bUwOrderOpt.get().getShipmentStatus(), b2BReturnId));
                            }
                        }
                        log.info("[statusUpdate] uwItemId : {}, incrementId : {}, unicom : {}, shipmentStatus : {}, returnId : {}", returnItemDTO.getUwItemId(), incrementId, unicomOrderCode, shipmentStatus, returnId);
                        OrderStatusUpdateDTO orderStatusUpdateDTO = new OrderStatusUpdateDTO(incrementId, unicomOrderCode, shipmentStatus, returnId);
                        orderStatusUpdateDTOS.add(orderStatusUpdateDTO);
                    }
                }
            }
            log.info("[statusUpdate] status update dto : {}", orderStatusUpdateDTOS);
            kafkaService.pushToKafka("return_order_status_update", String.valueOf(uwOrderDTOS.get(0).getIncrementId()), orderStatusUpdateDTOS);
        }catch (Exception exception){
            log.error("[statusUpdate] exception : "+exception);
        }
    }

    protected ReturnCreationResponse getReturnCreationResponse(ReverseCourierDetail reverseCourierDetail, ReturnCreationResponse returnCreationResponse, Integer incrementId, List<UwOrderDTO> uwOrders) {
        Result result=returnCreationResponse.getResult();
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yyyy");

        result.setDispensingFlag(reverseCourierDetail != null && reverseCourierDetail.isAllowDispensingCourier());

        ReversePickupDetails reversePickupDetails=new ReversePickupDetails();
        reversePickupDetails.setIncrementId(incrementId);
        reversePickupDetails.setAwbNumber("-");
        reversePickupDetails.setCourier(reverseCourierDetail != null ? reverseCourierDetail.getCourier() : null);
        reversePickupDetails.setReverseCreatedAt(sdf.format(new Date()));

        returnCreationResponse.setResult(result);
        returnCreationResponse.setReversePickupDetails(reversePickupDetails);
        returnCreationResponse.setUwOrders(uwOrders);
        returnCreationResponse.setIncrementId(incrementId);
        returnCreationResponse.setStatus(200);
        log.info("[getReturnCreationResponse] order : {}, returnCreationResponse : {}", incrementId, returnCreationResponse);
        return returnCreationResponse;
    }
    private PurchaseOrderDetailsDTO getPurchaseOrderDetails(String identifierType, String identifierValue) {
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = null;
        ResponseEntity<PurchaseOrderDetailsDTO> purchaseOrderDetailsDTOResponseEntity = null;
        purchaseOrderDetailsDTOResponseEntity = orderOpsFeignClient.getPurchaseOrderDetails(identifierType, identifierValue);

        if (purchaseOrderDetailsDTOResponseEntity.getStatusCode().is2xxSuccessful() && purchaseOrderDetailsDTOResponseEntity.getBody() != null) {
            purchaseOrderDetailsDTO = purchaseOrderDetailsDTOResponseEntity.getBody();
            purchaseOrderDetailsDTO.setIdentifierType(identifierType);
            purchaseOrderDetailsDTO.setIdentifierValue(identifierValue);
        } else {
            log.error("[getPurchaseOrderDetails] error : " + purchaseOrderDetailsDTOResponseEntity);
        }
        return purchaseOrderDetailsDTO;
    }

    private String getIdentifierType(Integer uwItemId) {
        return (uwItemId != null)
                ? IdentifierType.UW_ITEM_ID.name()
                : IdentifierType.MAGENTO_ID.name();
    }

    private String getIdentifierValue(String identifierType, Integer uwItemId, Long magentoId) {
        return (identifierType.equalsIgnoreCase(IdentifierType.UW_ITEM_ID.name()))
                ? String.valueOf(uwItemId)
                : String.valueOf(magentoId);
    }

    protected PurchaseOrderDetailsDTO getOrderDetails(ReturnCreationRequestDTO returnCreationRequest, Map<Long, ReturnItemDTO> magentoItemToReturnItemMap, Map<Long, String> returnIntentMap, List<ReturnItemDTO> exchangeItemList, List<ReturnItemDTO> returnItemsUpdated, List<UwOrderDTO> uwOrders, Map<ReturnItemDTO, Integer> productIdsMap) {
        log.info("[getOrderDetails] enter order : {}, returnItemsUpdated : {}", returnCreationRequest.getIncrementId(), returnItemsUpdated);
        PurchaseOrderDetailsDTO purchaseOrderDetails = new PurchaseOrderDetailsDTO();
        for (ReturnItemDTO returnItem : returnCreationRequest.getItems()) {

            returnIntentMap.put(returnItem.getMagentoId(), returnItem.getRefundMethodRequest() != null ? returnItem.getRefundMethodRequest() : returnItem.getRefundMethod());
            if (null != returnItem.getRefundMethodRequest() && returnItem.getRefundMethodRequest().toLowerCase().contains("exchange")) {
                if (returnItem.getRefundMethodRequest().contains("Same Product")) {
                    exchangeItemList.add(returnItem);
                }
                returnItem.setRefundMethodRequest("exchange");
            }

            purchaseOrderDetails = returnCreationRequest.getPurchaseOrderDetailsDTO();
            if(purchaseOrderDetails == null){
                String identifierType = getIdentifierType(returnItem.getUwItemId());
                String identifierValue = getIdentifierValue(identifierType, returnItem.getUwItemId(), returnItem.getMagentoId());
                log.info("[getOrderDetails] identifierType : {}, identifierValue : {}", identifierType, identifierValue);
                purchaseOrderDetails = getPurchaseOrderDetails(identifierType, identifierValue);
            }

            if (purchaseOrderDetails != null) {
                String identifierType = purchaseOrderDetails.getIdentifierType();
                if(StringUtils.isEmpty(identifierType)){
                    identifierType = IdentifierType.MAGENTO_ID.name();
                    String identifierValue = String.valueOf(returnItem.getMagentoId());
                    purchaseOrderDetails.setIdentifierType(identifierType);
                    purchaseOrderDetails.setIdentifierValue(identifierValue);
                }

                List<OrdersDTO> ordersDTOS = purchaseOrderDetails.getOrders()
                        .stream()
                        .filter(ordersDTO -> ordersDTO.getMagentoItemId() == returnItem.getMagentoId()).toList();

                for (UwOrderDTO uwOrderDTO : purchaseOrderDetails.getUwOrders()) {
                    for(OrdersDTO order : ordersDTOS){
                        if (order.getItemId() == uwOrderDTO.getItemId() && uwOrderDTO.getParentUw() == 0) {
                            if (filterReturnOrdersForB2B(order, uwOrderDTO, facilityCode, 11356) && !returnItemsUpdated.contains(returnItem)) {
                                uwOrders.add(uwOrderDTO);
                                returnItemsUpdated.add(returnItem);
                                productIdsMap.put(returnItem, uwOrderDTO.getProductId());
                            }
                        }
                    }
                }

                for (OrdersDTO order : purchaseOrderDetails.getOrders()) {
                    if(order.getMagentoItemId() == returnItem.getMagentoId()){
                        log.info("[getOrderDetails] order : {}", order.getIncrementId());
                        Optional<UwOrderDTO> uwOrderDTOOptional = purchaseOrderDetails.getUwOrders().stream().filter(uwOrderDTO -> uwOrderDTO.getItemId() == order.getItemId()).findFirst();
                        if(uwOrderDTOOptional.isPresent()){
                            UwOrderDTO uwOrderDTO = uwOrderDTOOptional.get();
                            if(isCustomerFacingItem(uwOrderDTO, order)){
                                log.info("[getOrderDetails] order : {}, setting uwItemId  : {}", order.getIncrementId(), uwOrderDTO.getUwItemId());
                                returnItem.setUwItemId(uwOrderDTO.getUwItemId());
                            }
                        }
                    }
                }

            } else {
                throw new NoSuchElementException("No order found for the given magento item id");
            }
            magentoItemToReturnItemMap.put(returnItem.getMagentoId(), returnItem);
        }
        log.info("[getOrderDetails] exit order : {}, magentoMap : {}, returnOrderItems : {}, returnItemsUpdated : {}", returnCreationRequest.getIncrementId(), magentoItemToReturnItemMap, returnCreationRequest.getItems(), returnItemsUpdated);
        return purchaseOrderDetails;
    }

    private boolean isCustomerFacingItem(UwOrderDTO uwOrderDTO, OrdersDTO order){
        log.info("[isCustomerFacingItem] item : {}", uwOrderDTO.getUwItemId());
        boolean isThisItemCustomerFacing = (!"B2B".equalsIgnoreCase(uwOrderDTO.getProductDeliveryType()) && uwOrderDTO.getParentUw() == 0) || filterReturnOrdersForB2B(order, uwOrderDTO, facilityCode, 11356);
        log.info("[isCustomerFacingItem] item : {}, isThisItemCustomerFacing : {}", uwOrderDTO.getUwItemId(), isThisItemCustomerFacing);
        return isThisItemCustomerFacing;
    }

    protected ReturnRequest createReturnRequest(ReturnCreationRequestDTO returnCreationRequest, Map<Long,ReturnItemDTO> magentoItemToReturnItemMap, Map<Long, String> returnIntentMap, String identifierType , String identifierValue, Integer incrementId) {
        ReturnRequest returnRequest = null;
        final List<String> returnCancelledStatusList = Arrays.asList(ReturnStatus.CUSTOMER_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED.getStatus(),
                ReturnStatus.CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED.getStatus(), ReturnStatus.RETURN_RESHIP.getStatus(),
                ReturnStatus.RETURN_REFUND_REJECTED.getStatus(), ReturnStatus.EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED_TBYB.getStatus(),
                ReturnStatus.PARTIAL_EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus());
        for(Long magento: magentoItemToReturnItemMap.keySet()){
            ReturnItemDTO returnItem = magentoItemToReturnItemMap.get(magento);
            log.info("[createReturnRequest] returnItem : {}", returnItem);
            returnRequest = returnRequestRepository.findTop1ByIdentifierValueOrderByCreatedAtDesc(String.valueOf(returnCreationRequest.getItems().get(0).getMagentoId()));
            boolean isNewReturnRequestCreationNeeded = false;
            if(returnRequest != null){
                try{
                    List<Integer> returnIds = returnEventService.getReturnIds(returnRequest.getId());
                    Integer returnId = !CollectionUtils.isEmpty(returnIds) ? returnIds.stream().filter(Objects::nonNull).toList().get(0) : null;
                    log.info("[createReturnRequest] returnCancelledStatusList : {}, returnId : {}", returnCancelledStatusList, returnId);
                    isNewReturnRequestCreationNeeded = returnCancelledStatusList.contains(returnOrderActionService.getReturnOrderStatusById(returnId));
                    if(returnCreationRequest.getIsCourierReassigned() != null && returnCreationRequest.getIsCourierReassigned()){
                        returnIntentMap.put(magento, returnRequest.getReturnIntention());
                    }else{
                        if(StringUtils.isNotEmpty(returnItem.getRefundMethod())){
                            returnIntentMap.put(magento, returnItem.getRefundMethod());
                        }
                    }
                }catch (Exception exception){
                    log.error("[createReturnRequest] error : {}", exception.getMessage());
                }
            }
            if(returnRequest != null && !isNewReturnRequestCreationNeeded){
                log.info("[createReturnRequest] updating intention : {}", returnIntentMap.get(magento));
                returnRequest.setReturnIntention(returnIntentMap.get(magento));
                returnRequestRepository.save(returnRequest);
            }
            if(returnRequest == null || isNewReturnRequestCreationNeeded){
                returnRequest = createReturnRequest(returnItem, returnCreationRequest, returnIntentMap, identifierType, identifierValue, incrementId);
                returnRequest = returnRequestRepository.save(returnRequest);
                returnEventService.createReturnEvent(returnRequest.getId(), null, RETURN_REQUEST_CREATED, "");
            }
        }
        return returnRequest;
    }

    protected ReturnRequest createReturnRequest(ReturnItemDTO returnItem, ReturnCreationRequestDTO returnCreationRequest, Map<Long, String> returnIntentMap, String identifierType , String identifierValue, Integer incrementId ) {
        log.info("[createReturnRequest] enter order : {}", returnCreationRequest.getIncrementId());
        StringBuilder primaryReturnReason = new StringBuilder();
        StringBuilder secondaryReturnReason = new StringBuilder();
        for(com.lenskart.ordermetadata.dto.request.Reasons reason : returnItem.getReasons()){
            PrimaryReturnReasons primaryReason = primaryReturnReasonsRepository.findByPrimaryReasonId(reason.getPrimaryReasonId());
            SecondaryReturnReason secondaryReason = secondaryReturnReasonRepository.findBySecondaryReasonId(reason.getSecondaryReasonId());
            if(primaryReason != null){
                primaryReturnReason.append(primaryReason.getReason());
            }
            if(secondaryReason != null){
                secondaryReturnReason.append(secondaryReason.getReason());
            }
        }
        String reason = new String(primaryReturnReason.append(",").append(secondaryReturnReason));

        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setCreatedAt(new Date());
        returnRequest.setSource(returnCreationRequest.getReturnSource().getSource());
        returnRequest.setReturnReason(reason);
        returnRequest.setReturnIntention(StringUtils.isNotEmpty(returnItem.getRefundMethod()) ? returnItem.getRefundMethod() : returnItem.getRefundMethodRequest());
        returnRequest.setIdentifierType(identifierType);
        returnRequest.setIdentifierValue(identifierValue);
        returnRequest.setIncrementId(incrementId);
        returnRequest.setAgentId(returnCreationRequest.getInitiatedBy() == null ? "0" : String.valueOf(returnCreationRequest.getInitiatedBy()));
        try {
            ReturnCreationRequest request = objectMapper.convertValue(returnCreationRequest, ReturnCreationRequest.class);
            returnRequest.setReturnCreationRequest(objectMapper.writeValueAsString(request));
        } catch (JsonProcessingException e) {
            log.error("[createReturnRequest] mapping issue : {}", e.getMessage());
        }
        log.info("[createReturnRequest] exit order : {}", returnCreationRequest.getIncrementId());
        return returnRequest;
    }

    protected void enrichReturnCreationRequest(ReturnCreationRequestDTO returnCreationRequest,  Map<ReturnItemDTO,Integer> productIdsMap, List<ReturnItemDTO> returnItems, Integer incrementId) {
        if(!CollectionUtils.isEmpty(returnItems)){
            for(ReturnItemDTO returnItemDTO : returnItems){
                returnItemDTO.setNeedApproval(Optional.ofNullable(returnItemDTO.getNeedApproval()).orElse(false));
            }
            returnCreationRequest.setItems(returnItems);
        }
        returnCreationRequest.setProductIdsMap(productIdsMap);
        returnCreationRequest.setIncrementId(incrementId);
    }
}
