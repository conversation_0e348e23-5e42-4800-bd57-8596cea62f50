package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ExchangeItemDispatchableDTO;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.request.ReverseCourierUpdateRequestDTO;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.enums.RefundTriggerPoint;
import com.lenskart.refund.client.model.kafka.CheckRefundDispatchPointKafkaMessage;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.response.CheckRefundInitiatedResponse;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.RefundMethod;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.request.ReverseTatRequest;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.ReverseCourierUpdateResponse;
import com.lenskart.returncommon.model.response.ReversePickUpInfoUpdateResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.ReversePickupPincodeRepository;
import com.lenskart.returnrepository.repository.SystemPreferenceRepository;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.ORDERS_DTO;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.BACK_SYNC_STATUS_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_COMMENT_QUEUE;

@Slf4j
@Service
public class ReversePickUpServiceImpl implements IReversePickUpService {

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;

    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired
    private IExchangeRefundMethodService exchangeRefundMethodService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ID365FinanceService d365FinanceService;
    @Autowired
    private IReturnEventService returnEventService;

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;
    @Autowired
    private ReversePickupPincodeRepository reversePickupPincodeRepository;

    @Autowired
    private ICommunicationService communicationService;

    @Autowired
    private IReversePickupTatService reversePickupTatService;

    @Autowired
    private ReturnUtil returnUtil;

    @Override
    @Transactional
    public ReverseCourierUpdateResponse updateReversePickUpInfo(ReverseCourierUpdateRequestDTO request) {
        ReverseCourierUpdateResponse response = new ReverseCourierUpdateResponse();
        ReversePickUpInfoUpdateResponse serviceResponse = new ReversePickUpInfoUpdateResponse();
        log.info("[updateReversePickUpInfo] ReverseCourierUpdateRequest : {}", request);
        try {
            List<ReturnDetail> returnOrderList = returnOrderActionService.fetchReturnOrdersOfLastTwoMonths(Long.valueOf(request.getPickupId()));
            if (CollectionUtils.isEmpty(returnOrderList)) {
                return createErrorResponse("Invalid PickupId or reverse_pickup_flag 0");
            }

            processReturnOrders(returnOrderList, request, serviceResponse);

            if (!CollectionUtils.isEmpty(serviceResponse.getReturnCreateRequestList())) {
                //need to add cosmos event
                sendUpdateReversePickUpInfoSMSAndEmail(Long.valueOf(request.getPickupId()),serviceResponse.getReturnStatus(),serviceResponse.getNoOfItems(), serviceResponse.getReturnOrderList(),serviceResponse.getOrder());
                triggerD365ReturnOrder(serviceResponse.getReturnCreateRequestList());
                triggerD365Invoice(serviceResponse.getReturnCreateRequestList(), request.getOrdersDTO().getFacilityCode());
            }

            response.setMessage(serviceResponse.getMessage());
            response.setSuccessful(serviceResponse.getSuccessful());
        } catch (Exception e) {
            log.error("[updateReversePickUpInfo] Exception Occurred : ", e);
            return createErrorResponse("Exception: " + e.getMessage());
        }
        return response;
    }

    @Override
    public List<ReversePickupPincodeDto> getActiveLkartOrReversePickupCourier(Integer pincode, boolean isActiveLkartCourierRequired) {
        List<ReversePickupPincodeDto> reversePickupPincodeDtos = new ArrayList<>();
        if (null != pincode) {
            List<ReversePickupPincode> reversePickupPincodes = new ArrayList<>();
            if (isActiveLkartCourierRequired) {
                reversePickupPincodes = reversePickupPincodeRepository.findActiveLKartCouriers(pincode);
            }
            if (CollectionUtils.isEmpty(reversePickupPincodes)) {
                reversePickupPincodes = reversePickupPincodeRepository.findActiveReversePickupCouriers(pincode);
            }
            reversePickupPincodes.forEach(rp -> {
                ReversePickupPincodeDto reversePickupPincodeDto = new ReversePickupPincodeDto();
                BeanUtils.copyProperties(rp, reversePickupPincodeDto);
                reversePickupPincodeDtos.add(reversePickupPincodeDto);
            });
        }
        return reversePickupPincodeDtos;
    }

    private void triggerD365Invoice(List<ReturnCreateRequest> returnCreateRequestList, String sourceFacility) {
        log.info("[ReversePickupServiceImpl][triggerD365Invoice] Sending data to D365FinanceConsumer, requestSize is : {}", returnCreateRequestList.size());
        try {
            for (ReturnCreateRequest returnCreateRequest1 : returnCreateRequestList
            ) {
                log.info("[ReversePickupServiceImpl][triggerD365Invoice] Processing data to D365FinanceConsumer for uwItemId : {}", returnCreateRequest1.getUwItemId());
                Boolean dataProcessed = null;
                dataProcessed = d365FinanceService.createReturnEInvoice(returnCreateRequest1.getReturnOrderItem().getReturnId(), returnCreateRequest1.getUwItemId(), sourceFacility);
                log.info("[ReversePickupServiceImpl][triggerD365Invoice] dataProcessed value : {}", dataProcessed);
            }
        } catch (Exception e) {
            log.error("[ReversePickupServiceImpl][triggerD365Invoice] exception {} {}", e.getMessage(), e);
        }
    }

    private void sendUpdateReversePickUpInfoSMSAndEmail(Long pickupId, String returnStatus, Integer noOfItems, List<ReturnOrderDTO> returnOrderList, OrdersDTO order) {
        log.info("[sendUpdateReversePickUpInfoSMSAndEmail] pickupId {}",pickupId);
        try {

            CommRequestDTO commRequestDTO = new CommRequestDTO();
            commRequestDTO.setReturnRequestId(returnOrderList.get(0).getRequestId());
            commRequestDTO.setReturnId(returnOrderList.get(0).getId());
            commRequestDTO.setOrderId(returnOrderList.get(0).getIncrementId());

            if (Constant.RETURN_STATUS.REFERENCE_ID_ISSUED.equalsIgnoreCase(returnStatus)) {
                commRequestDTO.setEventType(ReturnStatus.REFERENCE_ID_ISSUED.getStatus());
            } else if (Constant.RETURN_STATUS.AWB_ASSIGNED.equalsIgnoreCase(returnStatus)) {
                commRequestDTO.setEventType(ReturnStatus.AWB_ASSIGNED.getStatus());
                commRequestDTO.setConditionsMap(Map.of("courier", Constant.COURIER.SELF_COURIER));
                communicationService.pushToCommunicationTopic(commRequestDTO);

                commRequestDTO.setEventType(ReturnStatus.AWB_ASSIGNED.getStatus());
                RefundMethodsResponse refundMethods = exchangeRefundMethodService.findExchangeRefundMethod(order.getMagentoItemId(),"vsm", null);
                if (refundMethods.getRefundMethods().contains(RefundMethod.EXCHANGE.getName()) && (refundMethods.getRefundMethods().contains(RefundMethod.CUSTOMER_WALLET.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.STORECREDIT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.NEFT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.SOURCE.getName()))) {
                    commRequestDTO.setConditionsMap(Map.of("refundMethod", "EXG_WLT_STR_NFT_SRC"));
                } else if (refundMethods.getRefundMethods().contains(RefundMethod.EXCHANGE.getName())) {
                    commRequestDTO.setConditionsMap(Map.of("refundMethod", "exchange"));
                } else if (refundMethods.getRefundMethods().contains(RefundMethod.CUSTOMER_WALLET.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.STORECREDIT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.NEFT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.SOURCE.getName())) {
                    commRequestDTO.setConditionsMap(Map.of("refundMethod", "WLT_STR_NFT_SRC"));
                }
                commRequestDTO.setConditionsMap(Map.of("ruleEngine", "true"));
            }
            communicationService.pushToCommunicationTopic(commRequestDTO);

        }
        catch (Exception e){
            log.error("[sendUpdateReversePickUpInfoSMSAndEmail] exception occurred {} {}",e.getMessage(),e);
        }
    }

    private void triggerD365ReturnOrder(List<ReturnCreateRequest> returnCreateRequestList) {

        log.info("[ReturnServiceImpl][triggerD365ReturnOrder] Sending data to D365FinanceConsumer, requestSize is : {}", returnCreateRequestList.size());
        try {
            for (ReturnCreateRequest returnCreateRequest1 : returnCreateRequestList
            ) {
                log.info("[ReturnServiceImpl][triggerD365ReturnOrder] Processing data to D365FinanceConsumer for uwItemId : {}", returnCreateRequest1.getUwItemId());
                Boolean dataProcessed = null;
                dataProcessed = d365FinanceService.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest1);
                log.info("[ReturnServiceImpl][triggerD365ReturnOrder] dataProcessed value : {}", dataProcessed);
            }
        } catch (Exception e) {
            log.error("[triggerD365ReturnOrder] exception {} {}", e.getMessage(), e);
        }
    }

    private ReverseCourierUpdateResponse createErrorResponse(String message) {
        ReverseCourierUpdateResponse response = new ReverseCourierUpdateResponse();
        response.setSuccessful(false);
        response.setMessage(message);
        return response;
    }

    private void processReturnOrders(List<ReturnDetail> returnOrderList, ReverseCourierUpdateRequestDTO request, ReversePickUpInfoUpdateResponse response) {
        List<ReturnCreateRequest> returnCreateRequestList = new ArrayList<>();
        Integer noOfItems = 0;
        String orderComment = null;
        Integer incrementId = 0;
        String returnStatus = null;

        for (ReturnDetail returnOrder : returnOrderList) {
            incrementId = returnOrder.getIncrementId();
            String status = processSingleReturnOrderAndGetStatus(returnOrder, request, returnCreateRequestList);
            if (status != null) {
                returnStatus = status;
                orderComment = generateOrderComment(request, status);
                noOfItems++;
            }
        }

        OrdersDTO ordersDTO = request.getOrdersDTO();
        if(ordersDTO == null){
            boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
            ResponseEntity<OrderInfoResponseDTO> orderInfoResponse = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(ORDERS_DTO))
                    : orderOpsFeignClient.getOrderDetails(incrementId);
            ordersDTO = Objects.requireNonNull(orderInfoResponse.getBody()).getOrders().get(0);
            request.setOrdersDTO(ordersDTO);
        }
        updateOrderComment(ordersDTO, orderComment);
        populateResponse(response, returnOrderList, returnCreateRequestList, noOfItems, returnStatus, ordersDTO);
        response.setSuccessful(true);
    }

    private String processSingleReturnOrderAndGetStatus(ReturnDetail returnOrder, ReverseCourierUpdateRequestDTO request, List<ReturnCreateRequest> returnCreateRequestList) {
        log.info("[ReturnServiceImpl] Processing returnOrder : {}", returnOrder);

        String existingStatus = returnOrderActionService.getReturnOrderStatus(returnOrder);
        String referenceId = request.getReferenceId();
        String awb = request.getAwb();

        if (isEligibleForReferenceIdUpdate(existingStatus, referenceId, awb)) {
            pushReturnEventToKafka(returnOrder, "REFERENCE_ID_ISSUED");
            return "reference_id_issued";
        } else if (isEligibleForAwbUpdate(existingStatus, awb)) {
            pushAwbAssignedReturnEvent(returnOrder, "AWB_ASSIGNED");
            handleAwbAssigned(returnOrder, returnCreateRequestList);
            return "awb_assigned";
        } else {
            log.info("[updateReversePickUpInfo] Skipping returnOrder with returnId : {}", returnOrder.getId());
            return null;
        }
    }

    private void pushReturnEventToKafka(ReturnDetail returnOrder, String event) {
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setReturnId(returnOrder.getId());
        returnEvent.setReturnRequestId(returnOrder.getRequestId());
        returnEvent.setEvent(event);
        returnEventService.createReturnEvent(returnOrder.getRequestId(),returnOrder.getId(), event, "");
    }

    private void pushAwbAssignedReturnEvent(ReturnDetail returnOrder, String event) {
        List<ReturnEvent> returnEvents = returnEventService.getReturnEvent(returnOrder.getId());
        boolean isAlreadyAwbAssigned = returnEvents.stream()
                .map(ReturnEvent::getEvent)
                .anyMatch("AWB_ASSIGNED"::equalsIgnoreCase);
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setReturnId(returnOrder.getId());
        returnEvent.setReturnRequestId(returnOrder.getRequestId());
        returnEvent.setEvent(event);
        if(!isAlreadyAwbAssigned){
            returnEventService.createReturnEvent(returnOrder.getRequestId(),returnOrder.getId(), event, "return order picked ");
        }
    }

    private boolean isEligibleForReferenceIdUpdate(String status, String referenceId, String awb) {
        return getReturnStatusListForReferenceIdEnabled().contains(status) && StringUtils.isNotBlank(referenceId) && StringUtils.isBlank(awb);
    }

    private boolean isEligibleForAwbUpdate(String status, String awb) {
        return getReturnStatusListForAwb().contains(status) && StringUtils.isNotBlank(awb);
    }

    private List<String> getReturnStatusListForAwb() {
        SystemPreference sysPref1 = systemPreferenceRepository.findTopByGroupAndKey(Constant.SYSTEM_PREFERENCE_KEYS.RETURN_STATUS_AWB_ASSIGNED_GROUP, Constant.SYSTEM_PREFERENCE_KEYS.RETURN_ID_AWB_KEY);
        return Arrays.asList(sysPref1.getValue().split(","));
    }

    public List<String> getReturnStatusListForReferenceIdEnabled() {
        SystemPreference sysPref1 = systemPreferenceRepository.findTopByGroupAndKey(Constant.SYSTEM_PREFERENCE_KEYS.RETURN_STATUS_REF_ENABLED_GROUP, Constant.SYSTEM_PREFERENCE_KEYS.RETURN_ID_REF_ID_ENABLED_KEY);
        return Arrays.asList(sysPref1.getValue().split(","));
    }

    private void handleAwbAssigned(ReturnDetail returnOrder, List<ReturnCreateRequest> returnCreateRequestList) {
        log.info("[updateReversePickUpInfo] AWB assigned for returnId : {}", returnOrder.getId());

        kafkaService.pushToKafka("d365_return_tracking_event", String.valueOf(returnOrder.getIncrementId()), new D365ReturnTrackingRequestDTO(returnOrder.getId(), "awb_assigned"));
        ReturnDetailItem returnOrderItem = returnOrderActionService.findTopByReturnId(returnOrder.getId());
        if (returnOrderItem != null) {
            ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
            returnCreateRequest.setUwItemId(returnOrderItem.getUwItemId());
            returnCreateRequest.setReturnOrderItem(objectMapper.convertValue(returnOrderItem, ReturnOrderItemDTO.class));
            returnCreateRequestList.add(returnCreateRequest);

            kafkaService.pushToKafka("exchange_order_handler_queue",String.valueOf(returnOrder.getIncrementId()), returnOrder);
            BackSyncStatusDTO backSyncStatusDTO = new BackSyncStatusDTO();
            backSyncStatusDTO.setUwItemId(returnOrderItem.getUwItemId());
            backSyncStatusDTO.setTrackingStatus(Constant.TRACKING_STATUS.PICKUP_DONE);
            kafkaService.pushToKafka(BACK_SYNC_STATUS_QUEUE, String.valueOf(returnOrder.getIncrementId()), backSyncStatusDTO);
        }
    }

    @Override
    public void handleExchangeOrder(ReturnDetail returnOrder) {
        log.info("[handleExchangeOrder] order : {}, returnId : {}", returnOrder.getIncrementId(), returnOrder.getId());
        ReturnDetailItem returnOrderItem = returnOrderActionService.findTopByReturnId(returnOrder.getId());
        log.info("[handleExchangeOrder] order : returnOrderItem : {}",returnOrderItem);
        ResponseEntity<OrderExchangeCancellationDetails> responseEntity = orderOpsFeignClient.getExchangeAndCancellationDetails(returnOrderItem.getUwItemId(), returnOrder.getId());
        List<ExchangeOrdersDTO> exchangeOrders = responseEntity.getBody() != null ? responseEntity.getBody().getExchangeOrdersDTOList() : null;
        ExchangeOrdersDTO exchangeOrdersDTO = null;
        if (!CollectionUtils.isEmpty(exchangeOrders)) {
            exchangeOrdersDTO = exchangeOrders.get(0);
        }
        if (exchangeOrdersDTO != null) {
            log.info("[handleExchangeOrder] exchangeOrdersDTO : {}, checking for refund : {}", exchangeOrdersDTO, returnOrder.getIncrementId());
            ExchangedItem exchangedItem = createExchangedItem(returnOrder, exchangeOrdersDTO);
            ResponseEntity<ExchangeItemDispatchableDTO> exchangeItemDispatchableDTOResponseEntity = orderOpsFeignClient.getExchangeItemDispatchableDTO(exchangedItem);
            if(exchangeItemDispatchableDTOResponseEntity.getStatusCode().is2xxSuccessful()){
                log.info("[handleExchangeOrder] dipatchable refund check : {} ", returnOrder.getIncrementId());
                ExchangeItemDispatchableDTO exchangeItemDispatchableDTO = exchangeItemDispatchableDTOResponseEntity.getBody();
                returnRefundRuleService.isExchangeItemDispatchable(exchangeItemDispatchableDTO);
                boolean newFlowRefundForExchange = refundUtilsService.refundServiceSwitch(Constant.IDENTIFIER_TYPE.INCREMENT_ID, String.valueOf(exchangeOrdersDTO.getExchangeIncrementId()));
                if(newFlowRefundForExchange){
                    refundUtilsService.initiateExchangeOrderRefund(exchangeOrdersDTO.getExchangeIncrementId(), exchangeOrdersDTO.getIncrementId());
                }
            }
        } else {
            checkAndInitiateRefundRequest(returnOrder, "AWB_ASSIGNED");
        }
    }

    @Override
    public ReverseTatDTO getReverseTatInfo(ReverseTatRequest reverseTatRequest) {
        Date pickUpTat = null;
        if (reverseTatRequest.getReturnId() != null) {
            pickUpTat = reversePickupTatService.getPickupTatNew(reverseTatRequest.getPincode(), reverseTatRequest.getCreatedAt(), reverseTatRequest.getReturnId());
        } else {
            pickUpTat = reversePickupTatService.getPickupTat(reverseTatRequest.getPincode(), reverseTatRequest.getCreatedAt());
        }
        ReverseTatDTO reverseTatDTO = new ReverseTatDTO();
        reverseTatDTO.setPickupTat(pickUpTat);
        reverseTatDTO.setReverseDeliveryDays(reversePickupTatService.getReverseDeliveryDays(reverseTatRequest.getPincode()));
        return reverseTatDTO;
    }

    private ExchangedItem createExchangedItem(ReturnDetail returnOrder, ExchangeOrdersDTO exchangeOrder) {
        ExchangedItem exchangedItem = new ExchangedItem();
        exchangedItem.setReturnId(returnOrder.getId());
        exchangedItem.setUwItemId(exchangeOrder.getUwItemId());
        exchangedItem.setCallingPoint(ExchangedItem.CALLING_POINT.BACKWARD_FLOW);
        log.info("[updateReversePickUpInfo] exchangedItem : {}", exchangedItem);
        return exchangedItem;
    }

    @Override
    public void checkAndInitiateRefundRequest(ReturnDetail returnOrder, String remarks) {
        try {
            CheckRefundInitiatedRequest checkRefundInitiatedRequest = new CheckRefundInitiatedRequest();
            checkRefundInitiatedRequest.setIdentifierType(IdentifierType.RETURN_ID);
            checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(returnOrder.getId()));
            CheckRefundInitiatedResponse checkRefundInitiatedResponse = refundUtilsService.isRefundInitiated(checkRefundInitiatedRequest);
            if (Objects.isNull(checkRefundInitiatedResponse)) {
                log.info("[checkAndInitiateRefundRequest] Null response from the refund-service-api, rolling back transaction for return-id:{}", returnOrder.getId());
                return;
            }
            if (checkRefundInitiatedResponse.isRefundInitiated()) {
                log.info("[checkAndInitiateRefundRequest] Refund already initiated for return-id:{}", returnOrder.getId());
            } else {
                log.info("[checkAndInitiateRefundRequest] Push to check-dispatch-point consumer for return-id:{}", returnOrder.getId());
                CheckRefundDispatchPointKafkaMessage checkRefundDispatchPointKafkaMessage = new CheckRefundDispatchPointKafkaMessage();
                checkRefundDispatchPointKafkaMessage.setRefundTriggerPoint(RefundTriggerPoint.COURIER_PICKUP.getName());
                checkRefundDispatchPointKafkaMessage.setRefundRequestId(checkRefundInitiatedResponse.getRefundRequestDTO().getId());
                if (checkRefundInitiatedResponse.getRefundRequestDTO().getOrderId() != null)
                    checkRefundDispatchPointKafkaMessage.setPartitionKey(checkRefundInitiatedResponse.getRefundRequestDTO().getOrderId().toString());
                else
                    checkRefundDispatchPointKafkaMessage.setPartitionKey("0");
                checkRefundDispatchPointKafkaMessage.setRuleId(-1);
                checkRefundDispatchPointKafkaMessage.setRemarks(remarks);
               // returnEventService.createReturnEvent(returnOrder.getRequestId(),returnOrder.getId(),Constant.EVENT.REFUND_TRIGGERED,"call made to refund service");
                refundUtilsService.pushToCheckDispatchPointQueue(checkRefundDispatchPointKafkaMessage);
            }
        } catch (Exception e) {
            log.error("[checkAndInitiateRefundRequest] Exception:{}", e.getMessage());
        }

    }

    private void updateOrderComment(OrdersDTO ordersDTO, String orderComment) {
        CommentDTO commentDTO = CommentDTO.builder()
                .orderId(ordersDTO.getOrderId())
                .comment(Map.of("comment", orderComment, "comment_type", "cust"))
                .build();
        kafkaService.pushToKafka(ORDER_COMMENT_QUEUE, String.valueOf(ordersDTO.getOrderId()), commentDTO);
    }

    private String generateOrderComment(ReverseCourierUpdateRequestDTO request, String status) {
        StringBuilder commentBuilder = new StringBuilder();
        if ("reference_id_issued".equals(status)) {
            commentBuilder.append("Reference id ")
                    .append(request.getReferenceId())
                    .append(" issued to reverse pickup by ")
                    .append(request.getUserName());
        } else if ("awb_assigned".equals(status)) {
            commentBuilder.append("AWB no. ")
                    .append(request.getAwb())
                    .append(" assigned to reverse pickup by ")
                    .append(request.getUserName());
        }
        return commentBuilder.toString();
    }

    private void populateResponse(ReversePickUpInfoUpdateResponse response, List<ReturnDetail> returnOrderList, List<ReturnCreateRequest> returnCreateRequestList, Integer noOfItems, String returnStatus, OrdersDTO ordersDTO) {
        response.setNoOfItems(noOfItems);
        response.setReturnCreateRequestList(returnCreateRequestList);
        response.setReturnOrderList(objectMapper.convertValue(returnOrderList, new TypeReference<List<ReturnOrderDTO>>() {
        }));
        response.setReturnStatus(returnStatus);
        response.setOrder(ordersDTO);
    }
}
