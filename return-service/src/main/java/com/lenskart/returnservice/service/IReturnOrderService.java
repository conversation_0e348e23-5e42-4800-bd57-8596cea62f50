package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnReverseInfoResponse;
import com.lenskart.returncommon.model.response.ReturnTrackerResponse;
import com.lenskart.returnrepository.entity.ReturnDetail;
import org.apache.kafka.common.metrics.Stat;

import java.util.List;
import java.util.Map;

public interface IReturnOrderService {
    List<ReturnOrderDTO> getReturnDetails(Integer orderId);

    public GetDelayedPickupOrdersResponse getDelayedPickupOrders(GetDelayedPickupOrdersRequest delayedRequest) throws Exception;

    public GetReturnRefundMappingResponse getReturnRefundMapping(Integer incrementId);
    ReturnReverseInfoResponse getReturnRefundInfo(Integer integer);
    Integer getOrderDetailsForGroupId(Integer groupId);
    Integer getOrderIdByReturnId(Integer returnId);
    ReturnTrackerResponse getReturnTrackerInfoByIncrementId(Integer incrementId);
    String getReturnOrderStatusById(Integer returnId);
    ReturnDetail getReturnDetailByReturnIdAndStatus(Integer returnId, String status);
}
