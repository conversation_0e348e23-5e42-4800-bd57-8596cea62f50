package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.response.ProductMetaDataResponse;
import com.lenskart.refund.client.model.dto.PGSpecificRefundDetailsDTO;
import com.lenskart.refund.client.model.dto.Refund;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.enums.RefundMethod;
import com.lenskart.refund.client.model.request.GetRefundAmountRequest;
import com.lenskart.refund.client.model.response.GetMethodWiseRefundDetailsResponse;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnReverseInfoResponse;
import com.lenskart.returncommon.model.response.ReturnTrackerResponse;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IRefundUtilsService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReturnOrderService;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReturnOrderServiceImpl implements IReturnOrderService {

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IRefundUtilsService refundUtilsService;
    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private Map<String, String> returnViewMap;

    @PostConstruct
    public void init(){
        returnViewMap = new HashMap<>();
        returnViewMap.put("RETURN_INITIATED","Return Initiated");
        returnViewMap.put("REFERENCE_ID_ISSUED","Pickup Scheduled");
        returnViewMap.put("AWB_ASSIGNED","Pickup Done");
        returnViewMap.put("RETURN_RECEIVED_AT_STORE","Return Received at Store");
        returnViewMap.put("RETURN_RECEIVED_AT_WAREHOUSE","Return Received at Lenskart Warehouse");
        returnViewMap.put("CANCELLED","Pickup Cancelled");
        returnViewMap.put("CUSTOMER_CANCELLED","Pickup Cancelled");
        returnViewMap.put("REFUND_COMPLETED","Refund Completed");
        returnViewMap.put("RTO_RECEIVED_AT_WAREHOUSE","RTO Received at Lenskart Warehouse");
    }

    @Override
    public List<ReturnOrderDTO> getReturnDetails(Integer orderId) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            log.info("Fetching return orders for orderId: {}", orderId);

            if (orderId == null || orderId < 0) {
                throw new IllegalArgumentException(String.valueOf(orderId));
            }

            List<ReturnDetail> returnOrders = returnOrderActionService.findAllByIncrementId(orderId);

            if (returnOrders.isEmpty()) {
                throw new EntityNotFoundException(String.valueOf(orderId));
            }

            return returnOrders.stream()
                    .map(order -> objectMapper.convertValue(order, ReturnOrderDTO.class))
                    .toList();
        } catch (IllegalArgumentException ex) {
            log.error("Invalid input: {}", ex.getMessage());
            throw new IllegalArgumentException("Invalid input: " + ex.getMessage());
        } catch (EntityNotFoundException ex) {
            log.warn("No return orders found: {}", ex.getMessage());
            throw new EntityNotFoundException("No return orders found: " + ex.getMessage());
        } catch (Exception ex) {
            log.error("Error fetching return orders: {}", ex.getMessage());
            throw new RuntimeException("Error fetching return orders: " + ex.getMessage());
        }
    }

    @Override
    public GetDelayedPickupOrdersResponse getDelayedPickupOrders(GetDelayedPickupOrdersRequest delayedRequest) throws Exception {
        log.info("[getDelayedPickupOrders] delayedRequest : " + delayedRequest);
        GetDelayedPickupOrdersResponse getDelayedPickupOrdersResponse = new GetDelayedPickupOrdersResponse();
        List<ReturnDetail> returnOrdersList = returnOrderActionService.getReturnOrders(delayedRequest.getIncrementIds(), true);

        List<ReturnDetail> awbAssignedReturnOrders = filterOrdersByStatus(returnOrdersList, "awb_assigned");
        log.info("[getDelayedPickupOrders] delayedRequest : " + delayedRequest + " awbAssignedReturnOrders: " + awbAssignedReturnOrders);

        List<ReturnDetail> newReversePickupReturnOrders = filterOrdersByStatus(returnOrdersList, "new_reverse_pickup");
        log.info("[getDelayedPickupOrders] delayedRequest : " + delayedRequest + " newReversePickupReturnOrders: " + newReversePickupReturnOrders);

        Map<String, Map<String, Object>> pickupOrders = new HashMap<>();
        processOrders(awbAssignedReturnOrders, "awb_assigned", pickupOrders);
        processOrders(newReversePickupReturnOrders, "new_reverse_pickup", pickupOrders);

        getDelayedPickupOrdersResponse.setResult(pickupOrders);
        return getDelayedPickupOrdersResponse;
    }

    private List<ReturnDetail> filterOrdersByStatus(List<ReturnDetail> returnOrders, String status) {
        return returnOrders.stream()
                .filter(order -> status.equals(returnOrderActionService.getReturnOrderStatus(order)))
                .collect(Collectors.toList());
    }

    private void processOrders(List<ReturnDetail> orders, String status, Map<String, Map<String, Object>> pickupOrders) {
        for (ReturnDetail order : orders) {
            String incrementId = Objects.toString(order.getIncrementId(), null);
            if (!pickupOrders.containsKey(incrementId)) {
                Map<String, Object> pickupOrderDetails = new HashMap<>();
                ReturnDetailItem returnOrderItem = returnOrderActionService.findTopByReturnId(order.getId());
                ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTop1ByReturnId(order.getId());

                pickupOrderDetails.put("order_no", incrementId);
                pickupOrderDetails.put("return_id", Objects.toString(returnOrderItem != null ? returnOrderItem.getReturnId() : null, null));
                pickupOrderDetails.put("reverse_courier", returnCourierDetail != null ? returnCourierDetail.getReverseCourier() : null);
                pickupOrderDetails.put("reverse_pickup_reference_id", returnCourierDetail != null ? returnCourierDetail.getReversePickupReferenceId() : null);
                pickupOrderDetails.put("pickup_estimate", returnCourierDetail != null ? returnCourierDetail.getPickupEstimate() : null);
                pickupOrderDetails.put("status", status);

                pickupOrders.put(incrementId, pickupOrderDetails);
            }
        }
    }

    @Override
    public GetReturnRefundMappingResponse getReturnRefundMapping(Integer incrementId) {

        return getReturnRefundMappingV2(incrementId);
    }

    @Override
    public ReturnReverseInfoResponse getReturnRefundInfo(Integer returnId) {
        ReturnReverseInfoResponse response = new ReturnReverseInfoResponse();
        List<ReturnReverseCombinedInfoDto> returnReverseCombinedInfoDtoList = new ArrayList<>();
        List<ReturnEvent> eventList = returnEventRepository.findByReturnId(returnId);
        Optional<ReturnDetail> returnDetailOptional = returnOrderActionService.findReturnOrderById(returnId);
        log.info("[getReturnRefundInfo] eventList size : {}", eventList.size());
        if(!CollectionUtils.isEmpty(eventList)){
                eventList.forEach(re->{
                    if(returnViewMap.containsKey(re.getEvent())){
                        returnReverseCombinedInfoDtoList.add(ReturnReverseCombinedInfoDto.builder()
                                .returnId(re.getReturnId())
                                .eventName(returnViewMap.get(re.getEvent()))
                                .nonPickUpReason(re.getRemarks())
                                .createdAt(re.getCreatedAt())
                                .returnSource(re.getSource())
                                .eventSource(returnDetailOptional.get().getSource())
                                .build());
                    }
                });
        }else{
            ResponseEntity<List<Map<String, Object>>> returnInfoForInventory = orderOpsFeignClient.getReturnInfoForInventory(returnId);
            log.info("[getReturnRefundInfo] returnId : {}, response : {}", returnId, returnInfoForInventory);
            if(returnInfoForInventory.getStatusCode().is2xxSuccessful()){
                List<Map<String, Object>> returnInfoMapList = returnInfoForInventory.getBody();
                if(!CollectionUtils.isEmpty(returnInfoMapList)){
                    for(Map<String, Object> returnInfoMap : returnInfoMapList) {
                        Date date = null;
                        try{
                            String dateString = returnInfoMap.getOrDefault("created_at","").toString();
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
                            ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateString, formatter);
                            Instant instant = zonedDateTime.toInstant();
                            date = Date.from(instant);
                        }catch (Exception exception){
                            log.error("[getReturnRefundInfo] error : {}", exception.getMessage());
                            try{
                                String dateString = returnInfoMap.getOrDefault("created_at","").toString();
                                date = new Date(Long.parseLong(dateString));
                            }catch (Exception excep){
                                log.error("[getReturnRefundInfo] returnId : {}, error : {}",returnId, excep.getMessage());
                            }
                        }
                        returnReverseCombinedInfoDtoList.add(ReturnReverseCombinedInfoDto.builder()
                                .returnId(returnId)
                                .eventName(getDefaultNonNullString("mapped_status", returnInfoMap))
                                .nonPickUpReason(getDefaultNonNullString("non_pickup_reason", returnInfoMap))
                                .createdAt(date)
                                .returnSource("")
                                .eventSource(getDefaultNonNullString("source", returnInfoMap))
                                .build());
                    }
                }
            }
        }
        response.setReturnReverseCombinedInfoDtos(returnReverseCombinedInfoDtoList);
        return response;
    }

    private String getDefaultNonNullString(String key, Map<String, Object> returnInfoMap){
        return returnInfoMap.containsKey(key) && returnInfoMap.get(key) != null && !"null".equalsIgnoreCase(returnInfoMap.get(key).toString()) ? returnInfoMap.getOrDefault(key,"").toString() : null;
    }

    @Override
    public Integer getOrderDetailsForGroupId(Integer groupId) {
        Integer incrementId = null;
        List<ReturnDetail> returnOrders = returnOrderActionService.findAllByGroupIdOrderByReturnCreateDatetimeDesc(groupId);
        if(!CollectionUtils.isEmpty(returnOrders)){
            incrementId = returnOrders.get(0).getIncrementId();
        }
        return incrementId;
    }

    @Override
    public Integer getOrderIdByReturnId(Integer returnId) {
        Optional<ReturnDetail> returnOrder = returnOrderActionService.findReturnOrderById(returnId);
        return returnOrder.map(ReturnDetail::getIncrementId).orElse(null);
    }

    /**
     * @param incrementId
     * @return GetReturnRefundMappingResponse
     * returnOrderDTOs contains all returnOrders for given incrementId.
     * refunds stores the methodWiseRefundDetails.
     * getReturnRefundMappings stores list of refunds for each returnOrder.
     */
    public GetReturnRefundMappingResponse getReturnRefundMappingV2(Integer incrementId) {
        List<ReturnOrderDTO> returnOrderDTOs = getReturnOrderDTOs(incrementId);
        log.info("[getReturnRefundMappingV2], returnOrderDTOs:{}", returnOrderDTOs);
        List<Refund> refunds = getRefunds(incrementId);

        List<ReturnRefundMapping> returnRefundMappings = getReturnRefundMappings(returnOrderDTOs, refunds);
        List<Refund> additionalRefunds = getAdditionalRefunds(refunds);
        List<Refund> orderRefunds = getOrderRefunds(returnRefundMappings);
        double totalRefund = calculateTotalRefund(orderRefunds) + calculateTotalRefund(additionalRefunds);

        GetReturnRefundMappingResponse response = new GetReturnRefundMappingResponse();
        response.setReturnRefundMapping(returnRefundMappings);
        response.setAdditionalRefunds(additionalRefunds);
        response.setRefunds(orderRefunds);
        response.setTotal_refund(totalRefund);

        log.info("[getReturnRefundMappingV2]. Response" + response);
        return response;
    }

    protected List<ReturnOrderDTO> getReturnOrderDTOs(Integer incrementId) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<ReturnDetail> returnOrders = returnOrderActionService.findAllByIncrementId(incrementId);
        log.info("[getReturnOrderDTOs], returnOrders:{}", returnOrders);
        return returnOrders.stream()
                .map(order -> objectMapper.convertValue(order, ReturnOrderDTO.class))
                .toList();
    }

    protected List<Refund> getRefunds(Integer incrementId) {
        GetRefundAmountRequest request = createRefundAmountRequest(incrementId);
        GetMethodWiseRefundDetailsResponse response = refundUtilsService.getMethodWiseRefundDetails(request);
        log.info("[getRefunds], GetMethodWiseRefundDetailsResponse:{}", response);

        List<Refund> refunds = new ArrayList<>();
        if (response.getMethodSpecificRefundDetailsDTOMap() != null) {
            response.getMethodSpecificRefundDetailsDTOMap().forEach((refundMethod, detailsDTO) -> {
                if (detailsDTO != null) {
                    detailsDTO.getRefundDetailsList().forEach(pgDetails -> {
                        if (pgDetails != null) {
                            refunds.add(createRefund(pgDetails, refundMethod.getCode(), incrementId));
                        }
                    });
                }
            });
        }

        log.info("[getRefunds]. Refunds: " + refunds);
        return refunds;
    }

    private List<Refund> getAdditionalRefunds(List<Refund> refunds) {
        return refunds.stream()
                .filter(refund -> refund.getReturnId() == 0)
                .collect(Collectors.toList());
    }

    private List<Refund> getOrderRefunds(List<ReturnRefundMapping> returnRefundMappings) {
        List<Refund> orderRefunds = new ArrayList<>();
        for (ReturnRefundMapping mapping : returnRefundMappings) {
            orderRefunds.addAll(mapping.getRefunds());
        }
        return orderRefunds;
    }

    private double calculateTotalRefund(List<Refund> refunds) {
        return refunds.stream()
                .mapToDouble(refund -> refund.getRefundAmount().doubleValue())
                .sum();
    }


    private List<ReturnRefundMapping> getReturnRefundMappings(List<ReturnOrderDTO> returnOrderDTOs, List<Refund> refunds) {
        List<ReturnRefundMapping> returnRefundMappings = new ArrayList<>();
        for (ReturnOrderDTO returnOrder : returnOrderDTOs) {
            List<Refund> orderRefunds = refunds.stream()
                    .filter(refund -> returnOrder.getId().equals(refund.getReturnId()))
                    .collect(Collectors.toList());

            ReturnRefundMapping mapping = new ReturnRefundMapping();
            mapping.setReturnOrder(returnOrder);
            mapping.setRefunds(orderRefunds);
            returnRefundMappings.add(mapping);
        }
        log.info("[getReturnRefundMappings], returnRefundMappings:{}", returnRefundMappings);
        return returnRefundMappings;
    }

    private Refund createRefund(PGSpecificRefundDetailsDTO pgDetails, String refundMethodCode, Integer incrementId) {
        Refund refund = new Refund();
        refund.setId(pgDetails.getRefundId());
        refund.setRefundAmount(pgDetails.getRefundAmount().getAmount());
        refund.setRefundMethod(refundMethodCode);
        refund.setGatewayName(refundMethodCode);
        refund.setOrderId(Long.valueOf(incrementId));
        refund.setOrderRefundReason(pgDetails.getRefundReason() != null ? pgDetails.getRefundReason().getValue() : null);
        refund.setCreatedAt(pgDetails.getCreatedAt());
        refund.setStatus(pgDetails.getStatus());
        refund.setUpdatedAt(pgDetails.getUpdatedAt());
        refund.setOnlineTransactionId(pgDetails.getTxnId());
        refund.setNeftUtr(pgDetails.getArn());

        if (refundMethodCode.equalsIgnoreCase(RefundMethod.STORECREDIT.getCode()) && StringUtils.isNotEmpty(pgDetails.getCode())) {
            refund.setWebStatus(1);
            refund.setRemarks(pgDetails.getCode());
        }

        refund.setReturnId(pgDetails.getReturnId() != null ? Math.toIntExact(pgDetails.getReturnId()) : 0);
        log.info("[createRefund], refund:{}", refund);
        return refund;
    }

    private GetRefundAmountRequest createRefundAmountRequest(Integer incrementId) {
        GetRefundAmountRequest request = new GetRefundAmountRequest();
        request.setOrderId(String.valueOf(incrementId));
        request.setIdentifierType(IdentifierType.ORDER_ID);
        request.setIdentifierValues(Collections.singletonList(String.valueOf(incrementId)));
        return request;
    }

    @Override
    public ReturnTrackerResponse getReturnTrackerInfoByIncrementId(Integer incrementId) {
        ReturnTrackerResponse returnTrackerResponse = new ReturnTrackerResponse();
        List<ReturnOrderInfoDto> returnOrderInfoDtos = new ArrayList<>();
        Map<Integer, List<ReturnTrackerOrderItemDto>> returnTrackerOrderItemDtoMap = new HashMap<>();
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<Integer> non2B2UwItemIds = null;
        ResponseEntity<List<Integer>> responseEntity = orderOpsFeignClient.getNonB2BUwItemIds(incrementId);
        log.info("[getReturnTrackerInfoByIncrementId] responeEntity : {}", responseEntity);
        if(responseEntity.getStatusCode().is2xxSuccessful()){
            non2B2UwItemIds = responseEntity.getBody();
        }
        if (null != non2B2UwItemIds && !CollectionUtils.isEmpty(non2B2UwItemIds)) {
            mapList = returnOrderActionService.getReturnOrderResponse(incrementId, non2B2UwItemIds);
        }
        log.info("[getReturnTrackerInfoByIncrementId] mapList: {}", mapList);
        if (!CollectionUtils.isEmpty(mapList)) {
            for (Map<String, Object> map : mapList) {
                Integer returnId = (Integer) map.get("return_id");
                ReturnOrderInfoDto returnOrderInfoDto = new ReturnOrderInfoDto();
                returnOrderInfoDto.setReturnId(returnId);
                returnOrderInfoDto.setOrderNo(parseLong(map.get("order_no")));
                returnOrderInfoDto.setReturnType(safeString(map.get("return_type")));
                returnOrderInfoDto.setReverseAwb(safeString(map.get("reverse_awb")));
                returnOrderInfoDto.setReverseCourier(safeString(map.get("reverse_courier")));
                returnOrderInfoDto.setStatus(safeString(map.get("status")));
                returnOrderInfoDto.setSource(safeString(map.get("source")));
                returnOrderInfoDto.setReturnCreateDatetime((Date) map.get("return_create_datetime"));
                returnOrderInfoDto.setReturnMethod(safeString(map.get("return_method")));
                returnOrderInfoDto.setIsQcAtDoorstep(parseInteger(map.get("is_qc_at_doorstep")));
                returnOrderInfoDto.setUwItemId(parseLong(map.get("uw_item_id")));
                returnOrderInfoDto.setBulkType(safeString(map.get("bulk_type")));

                returnOrderInfoDtos.add(returnOrderInfoDto);

                Integer uwItemId = Math.toIntExact(returnOrderInfoDto.getUwItemId());
                ResponseEntity<ProductMetaDataResponse> productInfo = orderOpsFeignClient.getProductInfo(uwItemId);
                if(productInfo.getStatusCode().is2xxSuccessful() && productInfo.getBody() != null){
                    ProductMetaDataResponse productInfoBody = productInfo.getBody();
                    Map<String, Object> productReturnMap = returnOrderActionService.getReturnTrackerOrderItemDtos(uwItemId, productInfoBody);
                    ReturnTrackerOrderItemDto returnTrackerOrderItemDto = new ReturnTrackerOrderItemDto();
                    returnTrackerOrderItemDto.setReturnForReason(safeString(productReturnMap.get("reason_for_return")));
                    returnTrackerOrderItemDto.setQcStatus(safeString(productReturnMap.get("qc_status")));
                    returnTrackerOrderItemDto.setQcFailReason(safeString(productReturnMap.get("qc_fail_reason")));
                    returnTrackerOrderItemDto.setQcComment(safeString(productReturnMap.get("qc_comment")));
                    returnTrackerOrderItemDto.setProductId(parseLong(productReturnMap.get("product_id")));
                    returnTrackerOrderItemDto.setClassification(parseLong(productReturnMap.get("classification")));
                    returnTrackerOrderItemDto.setSku(safeString(productReturnMap.get("sku")));
                    returnTrackerOrderItemDto.setProductUrl(safeString(productReturnMap.get("product_url")));
                    returnTrackerOrderItemDto.setProductImage(safeString(productReturnMap.get("product_image")));
                    returnTrackerOrderItemDto.setValue(safeString(productReturnMap.get("value")));
                    returnTrackerOrderItemDto.setWebsite(safeString(productReturnMap.get("website")));

                    returnTrackerOrderItemDtoMap.computeIfAbsent(returnId, k -> new ArrayList<>()).add(returnTrackerOrderItemDto);
                }
            }
        }
        returnTrackerResponse.setReturnOrderResponse(returnOrderInfoDtos);
        returnTrackerResponse.setReturnTrackerOrderItemDtoMap(returnTrackerOrderItemDtoMap);
        return returnTrackerResponse;
    }

    private Long parseLong(Object obj) {
        try {
            return obj != null ? Long.valueOf(obj.toString()) : null;
        } catch (NumberFormatException e) {
            log.error("Failed to parse Long from value: {}", obj, e);
            return null;
        }
    }

    private Integer parseInteger(Object obj) {
        try {
            return obj != null ? Integer.valueOf(obj.toString()) : null;
        } catch (NumberFormatException e) {
            log.error("Failed to parse Integer from value: {}", obj, e);
            return null;
        }
    }

    private String safeString(Object obj) {
        return obj != null ? obj.toString() : null;
    }

    private Integer safeInt(Long value) {
        try {
            return (value != null) ? Math.toIntExact(value) : null;
        } catch (ArithmeticException e) {
            log.error("Failed to convert Long to Integer: {}", value, e);
            return null;
        }
    }


    @Override
    public String getReturnOrderStatusById(Integer returnId) {
        return returnOrderActionService.getReturnOrderStatusById(returnId);
    }

    @Override
    public ReturnDetail getReturnDetailByReturnIdAndStatus(Integer returnId, String status) {
        Optional<ReturnDetail> returnDetailOpt = returnOrderActionService.findReturnOrderById(returnId);
        return returnDetailOpt.orElse(null);
    }
}
