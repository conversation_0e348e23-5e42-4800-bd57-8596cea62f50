package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ReturnHeadingPlaceHolderValueFetchRefundModeStrategy implements IReturnHeadingPlaceHolderValueFetchStrategy {

    @Autowired
    IReturnOrderActionService returnOrderActionService;

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            String refundMode = "";
            List<RefundDetails> refundDetailsList = returnDetailsResponse.getRefundDetails();
            try {
                if (CollectionUtils.isNotEmpty(refundDetailsList)) {
                    for (RefundDetails refundDetails : refundDetailsList) {
                        if (Constant.REFUND_METHOD.CASHFREE.equalsIgnoreCase(refundDetails.getRefundMethod())) {
                            if (null != refundDetails) {
                                refundMode = refundDetails.getRefundMethodLabel();
                            }
                        } else if (Constant.REFUND_METHOD.getOnlineRefundMethods().contains(refundDetails.getRefundMethod()) ||
                                Constant.REFUND_METHOD.SOURCE.equalsIgnoreCase(refundDetails.getRefundMethod())) {
                            ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
                            OrdersHeaderDTO ordersHeader = returnDetailsResponse.getOrdersHeaderDTO();
                            refundMode = ordersHeader.getPaymentMode();
                        }
                    }
                    try {
                        if (null != returnStatusHeadingDetail) {
                            List<String> returnSubHeadingList = returnStatusHeadingDetail.getSubHeading();
                            List<String> returnSubHeadingListNew = null;
                            if (CollectionUtils.isNotEmpty(returnSubHeadingList)) {
                                returnSubHeadingListNew = new ArrayList<>();
                                for (String subHeading : returnSubHeadingList) {
                                    int keyStartIndex = subHeading.indexOf("{");
                                    int keyEndIndex = subHeading.indexOf("}");
                                    if (-1 != keyEndIndex && -1 != keyEndIndex) {
                                        subHeading = subHeading.replace(subHeading.substring(keyStartIndex, keyEndIndex + 1), refundMode);
                                    }
                                    returnSubHeadingListNew.add(subHeading);
                                }
                            }
                            returnStatusHeadingDetail.setSubHeading(returnSubHeadingListNew);
                            log.info("ReturnStatusHeadingDetail is :: " + returnStatusHeadingDetail.toString());
                        }
                    } catch (Throwable e) {
                        log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                        if (null != returnStatusHeadingDetail) {
                            returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                        }
                    }
                }
            } catch (Throwable e) {
                log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                if (null != returnStatusHeadingDetail) {
                    returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                }
            }
        }
    }

    @Override
    public void getReturnTimelinePlaceHolderValue(ReturnTimeLine returnTimeLine, ReturnDetailsResponse returnDetailsResponse) {

    }

    @Override
    public void getRefundTimelinePlaceHolderValue(RefundTimeLine refundTimeLine, ReturnDetailsResponse returnDetailsResponse) {

    }

    @Override
    public void getExchangeTimelinePlaceHolderValue(ExchangeTimeLine exchangeTimeLine, ReturnDetailsResponse returnDetailsResponse) {

    }

    public void getReturnHistoryPlaceHolderValue(ReturnStatusHistory returnStatusHistory, ReturnDetailsResponse returnDetailsResponse){

    }

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse, List<RefundDetails> refundDetailsList, ReturnStatusHeadingDetail returnStatusHeadingDetail, Long incrementId, Date createdAt, String anyReceivingPoint) {
            String refundMode = "";
            try {
                if (CollectionUtils.isNotEmpty(refundDetailsList)) {
                    for (RefundDetails refundDetails : refundDetailsList) {
                        if (Constant.REFUND_METHOD.CASHFREE.contains(refundDetails.getRefundMethod())) {
                            if (null != refundDetails) {
                                refundMode = refundDetails.getRefundMethodLabel();
                            }
                        } else if (Constant.REFUND_METHOD.getOnlineRefundMethods().contains(refundDetails.getRefundMethod()) ||
                                Constant.REFUND_METHOD.SOURCE.equalsIgnoreCase(refundDetails.getRefundMethod())) {
                            OrdersHeaderDTO ordersHeader = returnDetailsResponse.getOrdersHeaderDTO();
                            refundMode = ordersHeader.getPaymentMode();
                        }
                    }
                    try {
                        if (null != returnStatusHeadingDetail) {
                            List<String> returnSubHeadingList = returnStatusHeadingDetail.getSubHeading();
                            List<String> returnSubHeadingListNew = null;
                            if (CollectionUtils.isNotEmpty(returnSubHeadingList)) {
                                returnSubHeadingListNew = new ArrayList<>();
                                for (String subHeading : returnSubHeadingList) {
                                    int keyStartIndex = subHeading.indexOf("{");
                                    int keyEndIndex = subHeading.indexOf("}");
                                    if (-1 != keyEndIndex && -1 != keyEndIndex) {
                                        subHeading = subHeading.replace(subHeading.substring(keyStartIndex, keyEndIndex + 1), refundMode);
                                    }
                                    returnSubHeadingListNew.add(subHeading);
                                }
                            }
                            returnStatusHeadingDetail.setSubHeading(returnSubHeadingListNew);
                            log.info("ReturnStatusHeadingDetail is :: " + returnStatusHeadingDetail.toString());
                        }
                    } catch (Throwable e) {
                        log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                        if (null != returnStatusHeadingDetail) {
                            returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                        }
                    }
                }
            } catch (Throwable e) {
                log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                if (null != returnStatusHeadingDetail) {
                    returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                }
            }
    }
}
