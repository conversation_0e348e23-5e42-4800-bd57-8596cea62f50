package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.BackSyncRequest;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IOrderUtilsService;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lenskart.returncommon.utils.Constant.RETURN_METHOD.ENFORCED_RETURN_AT_STORE;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.RETURN_REFUNDED;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.BACK_SYNC_STATUS_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_STATUS_HISTORY_QUEUE;

@Service
@Slf4j
public class OrderUtilsServiceImpl implements IOrderUtilsService {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    RedisTemplate redisTemplate;
    @Value("${dealskart.manesar.facility}")
    private String lenskartFacility;
    public static final String OO_HUBMASTER_FACILITIES = "OO-HUBMASTER_FACILITIES";

    @Autowired
    private ReturnUtil returnUtil;

    @Autowired
    IKafkaService kafkaService;
    @Autowired
    private ReturnEventRepository returnEventRepository;
    @Autowired
    private IReturnEventService returnEventService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Value("${blocked.return.refund.exchange.order.priority.types:SHARKTANK25}")
    private List<String> orderPriorityTypes;

    @Override
    public boolean getIsFullLKCashOrder(List<ItemWisePriceDetailsDTO> itemWisePriceDetailsDTOList) {
        double totalLkCashApplied = 0.0d;
        double totalSCApplied = 0.0d;
        double totalAmountAfterDiscount = 0.0d;
        for (ItemWisePriceDetailsDTO itemWisePriceDetails : itemWisePriceDetailsDTOList) {
            totalLkCashApplied += (itemWisePriceDetails.getLenskartDiscount() + itemWisePriceDetails.getLenskartPlusDiscount());
            totalSCApplied += itemWisePriceDetails.getStoreCreditDiscount();
            totalAmountAfterDiscount += itemWisePriceDetails.getItemTotalAfterDiscount();
        }
        return (totalLkCashApplied != 0 && totalSCApplied == 0 && totalAmountAfterDiscount == 0);
    }

    @Override
    public void insertOrderComment(String comment, Integer orderId) {
        try {
            OrderCommentReq orderCommentReq = new OrderCommentReq();
            orderCommentReq.setIncrementId(orderId);
            orderCommentReq.setComment(comment);
            orderCommentReq.setCommentType("user");
            kafkaService.pushToKafka(ORDER_STATUS_HISTORY_QUEUE, String.valueOf(orderId), objectMapper.writeValueAsString(orderCommentReq));

        } catch (Exception exception) {
            log.error("[insertOrderComment] error occurred : " + exception);
        }

    }

    @Override
    public void callingBackSyncApi(Integer incrementId, List<UwOrderDTO> uwOrderDTOS, String checkPoint) {
        log.info("[callingBackSyncApi] Calling order back sync API for incrementId: " + incrementId + " checkPoint : " + checkPoint);
        BackSyncRequest backSyncRequest = new BackSyncRequest();
        List<String> hubFacilities = getPersistentHubFacilities();
        List<Integer> uwItemIdList = new ArrayList<>();
        for (UwOrderDTO uwOrderDTO : uwOrderDTOS) {
            boolean hubCondition = hubFacilities.contains(uwOrderDTO.getFacilityCode());
            if (isB2BReturnItem(uwOrderDTO, checkPoint)) {
                hubCondition = !hubCondition;
            }
            log.info("[callingBackSyncApi] hubCondition : " + hubCondition + " for order back sync API for uwItemId : " + uwOrderDTO.getUwItemId() + ", incrementId: " + incrementId);
            if (uwOrderDTO.getParentUw() == 0 && (hubCondition || Constant.PRODUCT_DELIVERY_TYPE.OTC.equalsIgnoreCase(uwOrderDTO.getProductDeliveryType()) || uwOrderDTO.getIsLocalFittingRequired())) {
                uwItemIdList.add(uwOrderDTO.getUwItemId());
            } else if (Constant.TRACKING_STATUS.REFUND_COMPLETED.equalsIgnoreCase(checkPoint)) {
                uwItemIdList.add(uwOrderDTO.getUwItemId());
            }
        }
        if (!CollectionUtils.isEmpty(uwItemIdList)) {
            backSyncRequest.setUwItemIdList(uwItemIdList);
            backSyncRequest.setCheckPoint(checkPoint);
            //todo need to log events in this case checkpoint event name and remarks backsync update for uwItemId list
            ResponseEntity<String> response = orderOpsFeignClient.backSyncUpdate(incrementId, backSyncRequest);
            log.info("[updateReturn] backsync response is :"+response);
        } else {
            log.info("[callingBackSyncApi] Empty uwItemList for incrementId: " + incrementId);
        }
    }

    @Override
    public void updateBackSyncStatus(List<ReturnDetailItem> returnItemList, String status, List<UwOrderDTO> uwOrderDTOS, Integer returnRequestId, Integer returnId) {
        List<Integer> uwItemIdList = returnItemList.stream().map(ReturnDetailItem::getUwItemId).toList();
        List<UwOrderDTO> uwOrderDTOS1 = Optional.ofNullable(uwOrderDTOS)
                .orElse(Collections.emptyList())
                .stream()
                .filter(uwOrderDTO -> uwItemIdList.contains(uwOrderDTO.getUwItemId()))
                .collect(Collectors.toList());

        int incrementId = uwOrderDTOS1.get(0).getIncrementId();
        log.debug("[ReturnUpdateServiceImpl : updateBackSyncStatus] return status for incrementId {} is {} to call backsync api.", incrementId, status);
        BackSyncStatusDTO backSyncStatusDTO = null;
        if (status.equalsIgnoreCase(ReturnStatus.CANCELLED.getStatus()) || status.equalsIgnoreCase(ReturnStatus.CUSTOMER_CANCELLED.getStatus()) || status.equalsIgnoreCase(ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus()) || status.equalsIgnoreCase(ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus())) {
            backSyncStatusDTO = BackSyncStatusDTO.builder()
                    .trackingStatus(Constant.TRACKING_STATUS.RETURN_CANCELLED)
                    .incrementId(incrementId)
                    .uwOrderDTOS(uwOrderDTOS1)
                    .build();
            //returnEventService.createReturnEvent(returnRequestId, returnId, Constant.EVENT.RETURN_CANCELLED, status);
        } else if (status.equalsIgnoreCase(ReturnStatus.RETURN_RECEIVED.getStatus())) {
            backSyncStatusDTO = BackSyncStatusDTO.builder()
                    .trackingStatus(Constant.TRACKING_STATUS.RETURN_RECEIVED)
                    .incrementId(incrementId)
                    .uwOrderDTOS(uwOrderDTOS1)
                    .build();
            //returnEventService.createReturnEvent(returnRequestId, returnId, Constant.EVENT.RETURN_RECEIVED, status);
        } else if (status.equalsIgnoreCase(ReturnStatus.RETURN_REFUNDED.getStatus())) {
            backSyncStatusDTO = BackSyncStatusDTO.builder()
                    .trackingStatus(Constant.TRACKING_STATUS.RETURN_REFUNDED)
                    .incrementId(incrementId)
                    .uwOrderDTOS(uwOrderDTOS1)
                    .build();
            //returnEventService.createReturnEvent(returnRequestId, returnId, Constant.EVENT.RETURN_REFUNDED, status);
        } else if (status.equalsIgnoreCase(ReturnStatus.RETURN_ACCEPTED.getStatus())) {
            backSyncStatusDTO = BackSyncStatusDTO.builder()
                    .trackingStatus(Constant.TRACKING_STATUS.RETURN_NEED_APPROVAL_ACCEPTED)
                    .incrementId(incrementId)
                    .uwOrderDTOS(uwOrderDTOS1)
                    .build();
            //returnEventService.createReturnEvent(returnRequestId, returnId, Constant.EVENT.RETURN_ACCEPTED, status);
        }

        if (backSyncStatusDTO != null) {
            kafkaService.pushToKafka(BACK_SYNC_STATUS_QUEUE, String.valueOf(incrementId), backSyncStatusDTO);
        }

    }

    @Override
    public Boolean isB2BReturnItem(Object uwOrderOne, String checkPoint) {
        log.info("[isB2BReturnItem] checkPoint : " + checkPoint);
        UwOrderDTO uwOrder = (UwOrderDTO) uwOrderOne;
        log.info("[isB2BReturnItem] checkPoint : " + checkPoint + ", shipmentState : " + uwOrder.getShipmentState() + ", shipmentStatus : " + uwOrder.getShipmentStatus());
        return (uwOrder.getShipmentState().equalsIgnoreCase(Constant.ORDER_STATE_OR_STATUS.CLOSED) || Constant.TRACKING_STATUS.RETURN_REQUESTED.equalsIgnoreCase(checkPoint) || Constant.TRACKING_STATUS.RETURN_TO_ORIGIN.equalsIgnoreCase(checkPoint)) && !uwOrder.getShipmentStatus().equalsIgnoreCase(Constant.ORDER_STATE_OR_STATUS.PREDELIVERY_REFUND) && !uwOrder.getShipmentState().equalsIgnoreCase(Constant.ORDER_STATE_OR_STATUS.ORDER_NOT_CONFIRMED) && uwOrder.getB2bRefrenceItemId() != null && !lenskartFacility.equalsIgnoreCase(uwOrder.getFacilityCode());
    }

    @Override
    public List<String> getPersistentHubFacilities() {
        List<String> hubMasterFacilities = null;
        if (redisTemplate.hasKey(OO_HUBMASTER_FACILITIES)) {
            try {
                hubMasterFacilities = (List<String>) redisTemplate.opsForValue().get(OO_HUBMASTER_FACILITIES);
            } catch (Exception e) {
                log.error("Error in [getPersistentHubFacilities]: {}", e.getMessage());
            }
        }
        if (hubMasterFacilities == null) {
            hubMasterFacilities = orderOpsFeignClient.getHubFacilities().getBody();
            if (null != hubMasterFacilities && !CollectionUtils.isEmpty(hubMasterFacilities)) {
                redisTemplate.opsForValue().set(OO_HUBMASTER_FACILITIES, hubMasterFacilities, 4, TimeUnit.HOURS);
            }
        }
        return Objects.nonNull(hubMasterFacilities) ? hubMasterFacilities : new ArrayList<>();
    }

    @Override
    public PurchaseOrderDetailsDTO getPurchaseOrderDetails(String identifierType, String identifierValue) {
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = null;
        ResponseEntity<PurchaseOrderDetailsDTO> ordersDetails = orderOpsFeignClient.getPurchaseOrderDetails(identifierType, identifierValue);
        log.debug("[getPurchaseOrderDetails] ordersDetails : "+ordersDetails);
        if(ordersDetails.getStatusCode().is2xxSuccessful()){
            purchaseOrderDetailsDTO =  ordersDetails.getBody();
        }else{
            log.error("[getPurchaseOrderDetails] getPurchaseOrderDetails didn't give success response : "+ordersDetails.getStatusCode());
        }
        return purchaseOrderDetailsDTO;
    }

    @Override
    public String getPaymentMethod(UwOrderDTO uwOrderDTO, List<UwOrderDTO> uwOrderDTOS, List<OrdersDTO> ordersDTOS, String orderMethod) {
        List<String> physicalFacilities = returnUtil.getPhysicalFacilityCodes();
        String paymentMethod = null;

        if(StringUtils.isBlank(orderMethod)){
            List<UwOrderDTO> uwOrders = uwOrderDTOS
                    .stream()
                    .filter(uwOrder -> "B2B".equalsIgnoreCase(uwOrder.getProductDeliveryType()) != physicalFacilities.contains(uwOrder.getFacilityCode()))
                    .toList();
            List<Integer> virtualOrderItemIds = uwOrders.stream().map(UwOrderDTO::getItemId).toList();

            OrdersDTO ordersDTO = ordersDTOS
                    .stream()
                    .filter(dto -> virtualOrderItemIds.contains(dto.getItemId()))
                    .findFirst()
                    .orElse(null);
            paymentMethod = ordersDTO != null ? ordersDTO.getMethod() : null;
        }else if (!Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(uwOrderDTO.getProductDeliveryType())) {
            paymentMethod = orderMethod;
        }
        else if(Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(uwOrderDTO.getProductDeliveryType()) && !physicalFacilities.contains(uwOrderDTO.getFacilityCode())){
            paymentMethod = orderMethod;
        }else{
            UwOrderDTO uwOrderB2BDTO = uwOrderDTOS.stream().filter(uwOrderDTO1 -> Objects.equals(uwOrderDTO1.getB2bRefrenceItemId(), uwOrderDTO.getUwItemId())).findFirst().orElse(null);
            if(uwOrderB2BDTO != null){
                OrdersDTO ordersB2BDTO = ordersDTOS.stream().filter(ordersDTO -> Objects.equals(ordersDTO.getItemId(), uwOrderB2BDTO.getItemId())).findFirst().orElse(null);
                if(ordersB2BDTO != null){
                    paymentMethod = ordersB2BDTO.getMethod();
                }
            }
        }
        log.info("[getPaymentMethod] paymentMethod : {}", paymentMethod);
        return paymentMethod;
    }

    @Override
    public void updateOrderStatus(DualRefundRequest request) {
        log.info("[updateOrderStatus] start request : {}", request);
        List<Integer> itemIds = request.getItemIds();
        for (Integer itemId : itemIds) {
            ResponseEntity<PurchaseOrderDetailsDTO> purchaseOrderDetails = orderOpsFeignClient.getPurchaseOrderDetails(IdentifierType.UW_ITEM_ID.name(), String.valueOf(itemId));
            if(purchaseOrderDetails.getStatusCode().is2xxSuccessful() && purchaseOrderDetails.getBody() != null) {
                PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = purchaseOrderDetails.getBody();
                List<UwOrderDTO> uwOrders = purchaseOrderDetailsDTO.getUwOrders();
                if(!CollectionUtils.isEmpty(uwOrders)){
                    UwOrderDTO uwOrderDTO = uwOrders.get(0);
                    List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(Math.toIntExact(request.getReturnId()));
                    if(!CollectionUtils.isEmpty(returnEvents)){
                        Optional<ReturnEvent> returnEventOptional = returnEvents.stream().filter(r -> r.getEvent().equals(ENFORCED_RETURN_AT_STORE)).findAny();
                        if(returnEventOptional.isEmpty()){
                            OrderStatusUpdateDetails orderStatusUpdateDetails = new OrderStatusUpdateDetails();
                            orderStatusUpdateDetails.setState("closed");
                            orderStatusUpdateDetails.setStatus(request.getStatus());
                            orderStatusUpdateDetails.setIncrementId(uwOrderDTO.getIncrementId());
                            orderStatusUpdateDetails.setUnicomOrderCode(uwOrderDTO.getUnicomOrderCode());
                            orderStatusUpdateDetails.setShipmentPackageId(uwOrderDTO.getShippingPackageId());
                            orderStatusUpdateDetails.setSource(request.getSource());
                            log.info("[updateOrderStatus] request : {}", orderStatusUpdateDetails);
                            orderOpsFeignClient.orderStatusUpdate(orderStatusUpdateDetails);
                            createOrderStatusUpdateEvent(orderStatusUpdateDetails, request.getReturnId());
                        }
                    }
                    BackSyncStatusDTO backSyncStatusDTO = new BackSyncStatusDTO();
                    backSyncStatusDTO.setUwItemId(uwOrderDTO.getUwItemId());
                    backSyncStatusDTO.setTrackingStatus(RETURN_REFUNDED.toUpperCase());
                    kafkaService.pushToKafka("back_sync_status_queue", String.valueOf(uwOrderDTO.getIncrementId()), backSyncStatusDTO);
                }
            }
        }
        log.info("[updateOrderStatus] end request : {}", request);
    }

    private void createOrderStatusUpdateEvent(OrderStatusUpdateDetails orderStatusUpdateDetails, Long returnId) {
        ReturnEventDTO returnEventDTO = new ReturnEventDTO();
        returnEventDTO.setReturnId(Math.toIntExact(returnId));
        returnEventDTO.setEvent("ORDER_STATUS_UPDATED");
        returnEventDTO.setRemarks("order status updated to : "+orderStatusUpdateDetails.getStatus()+ " and state to : "+orderStatusUpdateDetails.getState());
        kafkaService.pushToKafka("return_event_queue",String.valueOf(orderStatusUpdateDetails.getIncrementId()),returnEventDTO);
    }

    @Override
    public String getOrderPriorityType(Integer itemId, Integer incrementId) {
        log.info("[OrderUtilsServiceImpl][getOrderPriorityType] itemId : {}, incrementId : {}", itemId, incrementId);
        String orderPriorityType = null;
        try{
            ResponseEntity<String> orderPriorityTypeResponse = orderOpsFeignClient.getOrderPriorityType(itemId, incrementId);
            if(orderPriorityTypeResponse.getStatusCode().is2xxSuccessful()){
                orderPriorityType = orderPriorityTypeResponse.getBody();
            }
        }catch (Exception exception){
            log.error("[OrderUtilsServiceImpl][getOrderPriorityType] exception : "+exception);
        }
        log.info("[OrderUtilsServiceImpl][getOrderPriorityType] itemId : {}, incrementId : {}, orderPriorityType : {}", itemId, incrementId, orderPriorityType);
        return orderPriorityType;
    }

    @Override
    public boolean isPrioritizedOrder(String orderPriorityType) {
        return StringUtils.isNotEmpty(orderPriorityType)  && orderPriorityTypes.contains(orderPriorityType);
    }
}
