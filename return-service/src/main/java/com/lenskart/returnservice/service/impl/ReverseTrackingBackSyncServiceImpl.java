package com.lenskart.returnservice.service.impl;

import com.google.common.base.Strings;
import com.lenskart.returncommon.model.dto.ReverseTrackingEventDTO;
import com.lenskart.returncommon.model.request.BackSyncRequest;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReverseTrackingBackSyncService;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class ReverseTrackingBackSyncServiceImpl implements IReverseTrackingBackSyncService {

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Override
    public void backSync(ReverseTrackingEventDTO reverseTrackingEventDTO) {
        log.info("[ReverseTrackingBackSyncServiceImpl][backSync] start order : {}", reverseTrackingEventDTO.getOrderId());
        try {
            BackSyncRequest backSyncRequest = new BackSyncRequest();
            ReturnDetailItem returnDetailItem = returnOrderActionService.findTopByReturnId(reverseTrackingEventDTO.getReturnId());
            backSyncRequest.setUwItemIdList(Collections.singletonList(returnDetailItem.getUwItemId()));
            backSyncRequest.setCheckPoint(Constant.revrseMappedStatusMapping.get(reverseTrackingEventDTO.getNotificationEventId()));
            log.info("[OrderOpsCallerServiceImpl][processEventForBackSync] backSyncRequest: {}", backSyncRequest);
            if(shouldSendBackSync(reverseTrackingEventDTO)){
                log.info("[OrderOpsCallerServiceImpl][processEventForBackSync] order: {} , sending backsync", reverseTrackingEventDTO.getOrderId());
                ResponseEntity<String> response = orderOpsFeignClient.backSyncUpdate(reverseTrackingEventDTO.getOrderId(), backSyncRequest);
                log.info("[OrderOpsCallerServiceImpl][processEventForBackSync] order: {} , response: {}",  reverseTrackingEventDTO.getOrderId(), response);
            }
        } catch (Exception e) {
            log.error("[OrderOpsCallerServiceImpl][processEventForBackSync] Exception while calling order-ops backSyncUpdate API, e: {}", e.getMessage());
        }
        log.info("[ReverseTrackingBackSyncServiceImpl][backSync] end order : {}", reverseTrackingEventDTO.getOrderId());
    }

    boolean shouldSendBackSync(ReverseTrackingEventDTO reverseTrackingEventDTO) {
        SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey("clickpost_update", "backsync_event_notification_id");
        List<String> backsyncEventNotificationIdList = new ArrayList<>();
        if (systemPreference != null && !Strings.isNullOrEmpty(systemPreference.getValue())) {
            backsyncEventNotificationIdList = Arrays.asList(systemPreference.getValue().split(","));
        }
        String notificationEventId = String.valueOf(reverseTrackingEventDTO.getNotificationEventId());
        return backsyncEventNotificationIdList.contains(notificationEventId) && !notificationEventId.equalsIgnoreCase("2");
    }

}
