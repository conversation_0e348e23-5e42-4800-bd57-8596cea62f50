package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.ReverseAwbCourierMapperDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailReason;
import com.lenskart.returnrepository.entity.ReturnRequest;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IReverseCourierDetailService {
    ReverseCourierDetail getReverseCourierDetail(ReturnCreationRequestDTO returnCreationRequest, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, Integer incrementId, ReturnRequest returnRequest);

    ReverseCourierDetail reassignCourier(ReturnCreationRequestDTO returnCreationRequest, ReverseCourierDetail reverseCourierDetail);

    void assignCourier(ReturnCreationRequestDTO returnCreationRequest, String status, UwOrderDTO uwOrder, List<ReturnDetailReason> returnReasons,
                       ReverseCourierDetail reverseCourierDetail, Integer returnId, Date returnCreationDate, Long groupId,
                       ReturnItemDTO item, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, Integer orderId);

    ReturnCourierDetail updateReverseCourierDetail(Integer returnId, String reverseAwb);
    List<Map<String, Object>> getCourierDetails(String identifierType, String identifierValue) throws ParseException;

    List<ReverseAwbCourierMapperDTO> getReverseAwbCourierMap(List<String> reverseAwbs);
}
