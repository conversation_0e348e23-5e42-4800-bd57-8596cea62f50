package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returnservice.service.IRefundAuthorizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class IRefundAuthorizationServiceImpl implements IRefundAuthorizationService {
    private static final String SIGNATURE_KEY = "signature";

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Value("${refund.api.auth.key}")
    private String secretKey;

    @Override
    public <T> Map<String, Object> addRefundAuthorizationHeaders(T request, Map<String,Object> headers) {
        String generatedRequestHashKey = null;
        try {
            generatedRequestHashKey = createChecksum(secretKey, objectMapper.writeValueAsString(request));
            if (headers == null) {
                headers = new HashMap<>();
            }
            headers.put(SIGNATURE_KEY, generatedRequestHashKey);
        } catch (JsonProcessingException e) {
            log.error("Could not parse request body : {}", request);
        }
        return headers;
    }

    public static String createChecksum(String secret, String body) {
        log.info("body : {}", body);
        String result = null;
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), HMAC_SHA256_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(body.getBytes());
            result = Base64.encodeBase64String(rawHmac).toLowerCase();

        } catch (Exception e) {
            log.info("exception while generating hash key: " + e);
        }
        return result;
    }

    @Override
    public <T> HttpHeaders addRefundAuthorizationHeaders(T request, HttpHeaders headers) {
        String generatedRequestHashKey = null;
        try {
            generatedRequestHashKey = createChecksum(secretKey, objectMapper.writeValueAsString(request));
            if (headers == null) {
                headers = new HttpHeaders();
            }
            headers.set(SIGNATURE_KEY, generatedRequestHashKey);
        } catch (JsonProcessingException e) {
            log.error("Could not parse request body : {}", request);
        }
        return headers;
    }
}
