package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returncommon.utils.DateUtil;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.entity.ReturnHistory;
import com.lenskart.returnrepository.entity.ReverseTat;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnrepository.repository.ReturnHistoryRepository;
import com.lenskart.returnrepository.repository.ReversePickupTatRepository;
import com.lenskart.returnservice.service.IHolidayService;
import com.lenskart.returnservice.service.IReversePickupTatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Slf4j
@Service
public class ReversePickupTatServiceImpl implements IReversePickupTatService {

    @Autowired
    ReversePickupTatRepository reversePickupTatRepo;
    @Autowired
    IHolidayService holidayService;
    @Autowired
    ReturnEventRepository returnEventRepository;
    @Autowired
    ReturnHistoryRepository returnHistoryRepository;

    @Override
    public Date getPickupTat(String pincode, Date createdAt) {
        log.info("[getPickupTat] pincode {} createdAt {}",pincode,createdAt);
        if (StringUtils.isEmpty(pincode) || createdAt == null) {
            return null;
        }
        ReverseTat reverseTat =  reversePickupTatRepo.findByPincode(pincode);
        log.info("[getPickupTat] reverseTat {}",reverseTat);
        if(reverseTat ==null) return null;
        Calendar cal = getPickupCalanderTat(createdAt, reverseTat);
        Date pickupDate = cal.getTime();
        log.info("[getPickupTat] pickupDate {}",pickupDate);
        Date currentDate = new Date();
        if(pickupDate.before(currentDate)){
            cal.add(Calendar.DATE, 2);
            pickupDate = cal.getTime();
            log.info("[getPickupTat] pickupDate 1 {}",pickupDate);
            if(pickupDate.before(currentDate)) {
                cal.setTime(currentDate);
                cal.add(Calendar.DATE, 3);
                Calendar currentDateCal = Calendar.getInstance();
                currentDateCal.setTime(currentDate);
                log.info("[getPickupTat] calling holiday service");
                Integer holidays = holidayService.getHolidays(reverseTat.getReversePickupDays(), "IN");
                cal.add(Calendar.DATE, holidays);
                int sunday = 0;
                cal.set(Calendar.MILLISECOND, 0);
                cal.set(Calendar.SECOND, 0);
                currentDateCal.set(Calendar.MILLISECOND, 0);
                currentDateCal.set(Calendar.SECOND, 0);
                while (cal.compareTo(currentDateCal)>=0) {
                    if (currentDateCal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                        sunday++;
                    }
                    currentDateCal.add(Calendar.DATE, 1);
                }
                log.info("[getPickupTat] sunday "+sunday);
                cal.add(Calendar.DATE, sunday );
                if(cal.get(Calendar.DAY_OF_WEEK)==Calendar.SUNDAY){
                    cal.add(Calendar.DATE, 1);
                }
            }
        }
        log.info("[getPickupTat] pickup tat {}",cal.getTime());
        return cal.getTime();
    }

    public Calendar getPickupCalanderTat(Date returnCreationTime, ReverseTat reverseTat) {
        Integer holidays= holidayService.getHolidays(reverseTat.getReversePickupDays(),"IN");

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        String strDate = dateFormat.format(returnCreationTime);
        log.info("[getEstimatedReversePickUpDate] strDate : {}", strDate);
        String dayName = DateUtil.dayName(strDate, "yyyy-M-d");
            /*if (dayName.equalsIgnoreCase("Sunday")) {
                holidays = holidays + 1;
            }*/
        try {
            Calendar returnCalandar = Calendar.getInstance();
            returnCalandar.setTime(returnCreationTime);
            int hour = returnCalandar.get(Calendar.HOUR_OF_DAY);
            if (hour < 9) {
                reverseTat.setReversePickupDays(reverseTat.getReversePickupDays() - 1);
            }
            cal.setTime(dateFormat.parse(strDate));
            cal.add(Calendar.DATE, reverseTat.getReversePickupDays() + holidays);
            Calendar pickupCalendar = (Calendar) cal.clone();
            int sundays = 0;
            pickupCalendar.set(Calendar.MILLISECOND, 0);
            pickupCalendar.set(Calendar.SECOND, 0);
            returnCalandar.set(Calendar.MILLISECOND, 0);
            returnCalandar.set(Calendar.SECOND, 0);
            while (pickupCalendar.compareTo(returnCalandar) >= 0) {
                if (returnCalandar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY)
                    sundays++;
                returnCalandar.add(Calendar.DATE, 1);
            }
            cal.add(Calendar.DATE, sundays);
            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                cal.add(Calendar.DATE, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return cal;
    }

    @Override
    public Date getReverseDeliveryTat(String pincode) {
        ReverseTat reverseTat =  reversePickupTatRepo.findByPincode(pincode);
        Calendar reverseDeliveryTat = Calendar.getInstance();
        reverseDeliveryTat.add(Calendar.DATE,reverseTat.getReverseDeliveryDays());
        return reverseDeliveryTat.getTime();
    }

    @Override
    public int getReverseDeliveryDays(String pincode) {
        ReverseTat reverseTat =  reversePickupTatRepo.findByPincode(pincode);
        if(reverseTat!=null) return reverseTat.getReverseDeliveryDays();

        return 0;
    }

    @Override
    public Date getPickupTatNew(String reversePincode, Date createdAt, Integer returnId) {
        log.info("[getPickupTatNew] pincode {} createdAt {}",reversePincode,createdAt);
        if (StringUtils.isEmpty(reversePincode) || createdAt == null) {
            return null;
        }
        ReverseTat reverseTat =  reversePickupTatRepo.findByPincode(reversePincode);
        log.info("[getPickupTatNew] reverseTat {}",reverseTat);
        if(reverseTat ==null) return null;
        Date actualPickUpDate = getPickUpDoneDate(returnId);
        if(null!=actualPickUpDate){
            log.info("[getPickupTatNew] actualPickUpDate {}",actualPickUpDate);
            return actualPickUpDate;
        }
        Calendar pickupCalanderTat = getPickupCalanderTat(createdAt, reverseTat);
        Calendar initialPickupEta = (Calendar) pickupCalanderTat.clone();
        Date pickupDate = pickupCalanderTat.getTime();
        Date currentDate = new Date();
        Calendar currentCal = Calendar.getInstance();
        currentCal.setTime(currentDate);
        currentCal.set(Calendar.HOUR_OF_DAY,0);
        currentCal.set(Calendar.MINUTE,0);
        currentCal.set(Calendar.SECOND,0);
        currentCal.set(Calendar.MILLISECOND,0);
        currentDate = currentCal.getTime();
        log.info("[getPickupTatNew] pickupDate 1 "+ pickupCalanderTat.getTime());
        if(pickupDate.before(currentDate)){
            pickupCalanderTat.add(Calendar.DATE, 2);
            pickupDate = pickupCalanderTat.getTime();
            log.info("[getPickupTatNew] pickupDate 2 "+ pickupCalanderTat.getTime());
            if(pickupDate.before(currentDate)) {
                pickupCalanderTat.add(Calendar.DATE, 3);
                pickupDate = pickupCalanderTat.getTime();
                log.info("[getPickupTatNew] pickupDate 3 "+ pickupCalanderTat.getTime());
            }
            Calendar currentDateCal = Calendar.getInstance();
            currentDateCal.setTime(currentDate);
            log.info("[getPickupTat] calling holiday service");
            Integer holidays = holidayService.getHolidays(reverseTat.getReversePickupDays(), "IN");
            log.info("[getPickupTatNew] holidays "+holidays);
            pickupCalanderTat.add(Calendar.DATE, holidays);
            Integer sunday = getSundaysBetween(initialPickupEta, pickupCalanderTat);
            log.info("[getPickupTatNew] sunday "+sunday);
            pickupCalanderTat.add(Calendar.DATE, sunday );
            if(pickupCalanderTat.get(Calendar.DAY_OF_WEEK)==Calendar.SUNDAY){
                pickupCalanderTat.add(Calendar.DATE, 1);
            }
        }
        log.info("[getPickupTatNew] pickup tat {}", pickupCalanderTat.getTime());
        return pickupCalanderTat.getTime();
    }

    private Integer getSundaysBetween(Calendar startDate, Calendar endDate) {
        log.info("[getSundaysBetween] startDate {} endDate {}",startDate.getTime(), endDate.getTime());
        int sunday = 0;
        try {
            if (null != startDate && null != endDate) {
                startDate.set(Calendar.MILLISECOND, 0);
                startDate.set(Calendar.SECOND, 0);
                endDate.set(Calendar.MILLISECOND, 0);
                endDate.set(Calendar.SECOND, 0);
                while (endDate.compareTo(startDate) >= 0) {
                    if (startDate.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                        sunday++;
                    }
                    startDate.add(Calendar.DATE, 1);
                }
            }
        }
        catch (Exception e){
            log.error("[getSundaysBetween] exception {} {}",e.getMessage(),e);
        }
        log.info("[getSundaysBetween] sundays count: {}",sunday);
        return sunday;
    }

    private Date getPickUpDoneDate(Integer returnId) {
        log.info("[getPickUpDoneDate] returnId : {}", returnId);
        Date pickUpDate = null;
        if(null!=returnId) {
            ReturnEvent returnEvent = returnEventRepository.findTopByReturnIdAndEventOrderByIdDesc(returnId, Constant.RETURN_STATUS.AWB_ASSIGNED);
            if(returnEvent!=null && returnEvent.getCreatedAt()!=null){
                pickUpDate = returnEvent.getCreatedAt();
            }else {
                log.info("[getPickUpDoneDate] returnId : {}, going to check in  return_history table", returnId);
                ReturnHistory returnHistory = returnHistoryRepository.findTopByEntityIdAndCurrentStatus(returnId, Constant.RETURN_STATUS.AWB_ASSIGNED);
                if(returnHistory != null && returnHistory.getCreatedAt() != null){
                    pickUpDate = returnHistory.getCreatedAt();
                }
            }
        }
        log.info("[getPickUpDoneDate] returnId : {} , pickUpDate {}",returnId, pickUpDate);
        return pickUpDate;
    }
}
