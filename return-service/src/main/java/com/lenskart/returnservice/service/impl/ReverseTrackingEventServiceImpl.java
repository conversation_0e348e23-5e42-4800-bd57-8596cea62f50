package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ReverseTrackingEventDTO;
import com.lenskart.returnrepository.entity.ReverseDetailTrackingEvent;
import com.lenskart.returnrepository.repository.ReverseTrackingEventRepository;
import com.lenskart.returnservice.service.IReverseTrackingEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReverseTrackingEventServiceImpl implements IReverseTrackingEventService {

    @Autowired
    private ReverseTrackingEventRepository reverseTrackingEventRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void persistTrackingEvent(ReverseTrackingEventDTO reverseTrackingEventDTO) {
        log.info("[persistTrackingEvent] persisting event for order : {}", reverseTrackingEventDTO.getOrderId());
        ReverseDetailTrackingEvent reverseTrackingEvent = objectMapper.convertValue(reverseTrackingEventDTO, ReverseDetailTrackingEvent.class);
        reverseTrackingEventRepository.save(reverseTrackingEvent);
        log.info("[persistTrackingEvent] persisted event for order : {}", reverseTrackingEventDTO.getOrderId());
    }
}
