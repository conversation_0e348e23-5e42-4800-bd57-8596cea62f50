package com.lenskart.returnservice.service.impl;

import com.lenskart.returnservice.service.ICacheDataService;
import com.lenskart.returnservice.service.IEventDeduplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EventDeduplicationServiceImpl implements IEventDeduplicationService {

    @Autowired
    private ICacheDataService cacheDataService;

    @Override
    public boolean pushEvent(String eventId) {
        Object message = cacheDataService.getKey(eventId);

        Boolean added = message != null;

        if (Boolean.TRUE.equals(added)) {
            log.info("[EventDeduplicationServiceImpl][pushEvent] Duplicate event ignored: {}", eventId);
            return false;
        }

        cacheDataService.saveMessage(eventId, eventId, "15");
        log.info("Event pushed : {}" ,eventId);
        return true;
    }
}
