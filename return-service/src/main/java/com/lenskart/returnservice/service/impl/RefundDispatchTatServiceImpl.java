package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnservice.service.IRefundDispatchTatService;
import com.lenskart.returnservice.service.IReversePickupTatService;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RefundDispatchTatServiceImpl implements IRefundDispatchTatService {
    @Autowired
    IReversePickupTatService reversePickupTatService;

    @Value("${refund_processing_days}")
    private int refundProcessingDays;

    @Value("${cc_refund_complete_days}")
    private int ccRefundCompleteDays;

    @Value("${dc_refund_complete_days}")
    private int dcRefundCompleteDays;

    @Value("${nb_refund_complete_days}")
    private int nbRefundCompleteDays;

    private final String ANY_RECEIVING_POINT ="AnyReceivingPoint";

    @Autowired
    ISystemPreferenceService systemPreferenceService;


    @Override
    public Date getRefundDispatchETA(Map<String, String> refundMethodToActualPaymentMethodMap, String refundDispatchPoint, String reversePincode, String status, Date createdAt, Integer returnId) {
        Date pickupTat = null;
        if (returnId != null) {
            pickupTat = reversePickupTatService.getPickupTatNew(reversePincode, createdAt, returnId);
        }
        else {
            pickupTat = reversePickupTatService.getPickupTat(reversePincode, createdAt);
        }
        if (pickupTat == null) return null;
        Date refundDispatchETA = null;
        Date maxRefundDispatchETA = new Date();
        for (Map.Entry<String, String> entry : refundMethodToActualPaymentMethodMap.entrySet()) {
            String refundMethod = entry.getKey();
            String actualPaymentMethod = entry.getValue();
            if(Constant.REFUND_METHOD.SOURCE.equalsIgnoreCase(refundMethod)){
                refundMethod = Constant.REFUND_METHOD.ONLINE;
            }
            if (Constant.REFUND_METHOD.STORECREDIT.equalsIgnoreCase(refundMethod)) {
                if (refundDispatchPoint.equalsIgnoreCase(ReturnStatus.AWB_ASSIGNED.getStatus()) || refundDispatchPoint.equalsIgnoreCase(ANY_RECEIVING_POINT)) {
                    refundDispatchETA = pickupTat;
                } else if (refundDispatchPoint.equalsIgnoreCase("return_received")) {
                    int reverseDeliveryDays = reversePickupTatService.getReverseDeliveryDays(reversePincode);
                    refundDispatchETA = addDaysToDate(pickupTat, reverseDeliveryDays);
                }
            } else if (Constant.REFUND_METHOD.getOnlineRefundMethods().contains(refundMethod)) {
                int refundCompleteDays = getRefundCompleteDays(actualPaymentMethod);
                if (refundDispatchPoint.equalsIgnoreCase(Constant.RETURN_STATUS.AWB_ASSIGNED)|| refundDispatchPoint.equalsIgnoreCase(ANY_RECEIVING_POINT)) {
                    if(Constant.REFUND_STATUS.REFUND_COMPLETE.equalsIgnoreCase(status)){
                        //question: why adding refundCompleteDays when its already in refund_complete status (is it the arn days?)
                        refundDispatchETA = addDaysToDate(pickupTat,refundProcessingDays + refundCompleteDays);
                    }else {
                        refundDispatchETA = addDaysToDate(pickupTat, refundProcessingDays);
                    }
                } else if (refundDispatchPoint.equalsIgnoreCase("return_received")) {
                    int reverseDeliveryDays = reversePickupTatService.getReverseDeliveryDays(reversePincode);
                    if(Constant.REFUND_STATUS.REFUND_COMPLETE.equalsIgnoreCase(status)) {
                        refundDispatchETA = addDaysToDate(pickupTat, reverseDeliveryDays + refundProcessingDays + refundCompleteDays);
                    }else{
                        refundDispatchETA = addDaysToDate(pickupTat, reverseDeliveryDays + refundProcessingDays);
                    }
                }
            } else if (Constant.REFUND_METHOD.CASHFREE.equalsIgnoreCase(refundMethod)) {
                if (refundDispatchPoint.equalsIgnoreCase(Constant.RETURN_STATUS.AWB_ASSIGNED)|| refundDispatchPoint.equalsIgnoreCase(ANY_RECEIVING_POINT)) {
                    refundDispatchETA = addDaysToDate(pickupTat, refundProcessingDays);
                } else if (refundDispatchPoint.equalsIgnoreCase("return_received")) {
                    int reverseDeliveryDays = reversePickupTatService.getReverseDeliveryDays(reversePincode);
                    refundDispatchETA = addDaysToDate(pickupTat, reverseDeliveryDays + refundProcessingDays);
                }
            }
            if(refundDispatchETA!=null && refundDispatchETA.compareTo(maxRefundDispatchETA)>=0){
                maxRefundDispatchETA = refundDispatchETA;
            }
        }
        log.info("[getRefundDispatchETA] maxRefundDispatchETA: {}",maxRefundDispatchETA);
        return maxRefundDispatchETA;
    }

    private Date addDaysToDate(Date date, int days) {
        Calendar currDate = Calendar.getInstance();
        currDate.setTime(date);
        currDate.add(Calendar.DATE, days);
        return currDate.getTime();
    }

    private int getRefundCompleteDays(String actualPaymentMode) {
        int refundCompleteDays = 0;
        List<SystemPreference> systemPreferenceList = systemPreferenceService.findAllByGroup(Constant.SYSTEM_PREFERENCE_GROUPS.PAYMENT_MODE_REFUND_COMPLETE_DAYS,1, TimeUnit.DAYS);
        if(!CollectionUtils.isEmpty(systemPreferenceList)){
            for(SystemPreference systemPreference: systemPreferenceList){
                if(actualPaymentMode.equalsIgnoreCase(systemPreference.getKey())){
                    log.info("[getRefundCompleteDays] actualPaymentMode {} refundCompleteDays {}",actualPaymentMode,systemPreference.getValue());
                    return Integer.parseInt(systemPreference.getValue());
                }
            }
        }
        return refundCompleteDays;
    }
}
