package com.lenskart.returnservice.service.impl;

import com.lenskart.returnservice.service.ICacheDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CacheDataServiceImpl implements ICacheDataService {

    @Autowired
    private RedisTemplate<String,Object> redisTemplateHubEngine;

    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    @Override
    public Object getMessageForHubEngine(String key) {
        Object cacheValue = null;
        try {
            if(Boolean.TRUE.equals(redisTemplateHubEngine.hasKey(key))){
                cacheValue = redisTemplateHubEngine.opsForSet().members(key);
                log.info("Found value {} for key {} in getMessageForHubEngine() cache", cacheValue, key);
                return cacheValue;
            }
        } catch (Exception e) {
            log.error("Error while retrieving from getMessageForHubEngine() cache for key {}. Exception:", key, e);
        }
        return cacheValue;
    }

    @Override
    public <T> void saveMessage(String key, T value, String cacheExpiry) {
        try {
            log.info("[saveMessage] Saving message {} in cache with key {}", value, key);
            redisTemplate.opsForValue().set(key, value, Integer.parseInt(cacheExpiry.trim()), TimeUnit.MINUTES);
            //redisTemplate.expire(key,Integer.parseInt(cacheExpiry.trim()), TimeUnit.MINUTES);
            log.info("[saveMessage] key {} , Cache expiry time : {} ", key, cacheExpiry);
        } catch (Exception e) {
            log.error("Error while caching message : ", e);
        }
    }

    @Override
    public Object getKey(String key) {
        Object cacheValue = null;
        log.info("[getKey] key : {}", key);
        try {
            if(Boolean.TRUE.equals(redisTemplate.hasKey(key))){
                cacheValue = redisTemplate.opsForValue().get(key);
                log.info("[getKey] Found value {} for key {} in getMessageForHubEngine() cache", cacheValue, key);
                return cacheValue;
            }
        } catch (Exception e) {
            log.error("[getKey] Error while retrieving from getKey() cache for key {}. Exception:", key, e);
        }
        return cacheValue;
    }
}
