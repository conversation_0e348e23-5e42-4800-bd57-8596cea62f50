package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.return_refund_rules.model.ReturnOrderRequest;

import java.util.Map;
import java.util.Set;

public interface IReceivingService {
    Map<String, Object> returnItems(ReturnOrderRequestDTO returnRequest) throws Exception;
    Set<Integer> receiveItems(ReturnOrderRequestDTO returnOrderRequest) throws Exception;
}
