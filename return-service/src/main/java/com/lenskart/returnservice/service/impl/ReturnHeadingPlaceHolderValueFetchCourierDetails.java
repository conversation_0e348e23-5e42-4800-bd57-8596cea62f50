package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.ReturnSubHeaderTimelineDatesEnum;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ReturnHeadingPlaceHolderValueFetchCourierDetails implements IReturnHeadingPlaceHolderValueFetchStrategy {
    @Autowired
    IReturnOrderActionService returnOrderActionService;

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse) {

    }

    @Override
    public void getReturnTimelinePlaceHolderValue(ReturnTimeLine returnTimeLine, ReturnDetailsResponse returnDetailsResponse) {

    }

    @Override
    public void getRefundTimelinePlaceHolderValue(RefundTimeLine refundTimeLine, ReturnDetailsResponse returnDetailsResponse) {

    }

    @Override
    public void getExchangeTimelinePlaceHolderValue(ExchangeTimeLine exchangeTimeLine, ReturnDetailsResponse returnDetailsResponse) {

    }

    @Override
    public void getReturnHistoryPlaceHolderValue(ReturnStatusHistory returnStatusHistory, ReturnDetailsResponse returnDetailsResponse) {
        // if(ReturnStatus.NEW_REVERSE_PICKUP.getStatus().equalsIgnoreCase(returnStatusHistory.getReturnStatus())){
        try {
            ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTopByReturnIdOrderByIdDesc(returnDetailsResponse.getReturnId());
            String trackingDetail = "";
            if (StringUtils.isNotBlank(returnCourierDetail.getReversePickupReferenceId())) {
                trackingDetail = returnCourierDetail.getReversePickupReferenceId();
            } else if (StringUtils.isNotBlank(returnCourierDetail.getReverseAwbNumber())) {
                trackingDetail = returnCourierDetail.getReverseAwbNumber();
            }
            String returnSubStatus = returnStatusHistory.getComment();
            while (true) {
                int keyStartIndex = returnSubStatus.indexOf("{");
                int keyEndIndex = returnSubStatus.indexOf("}");
                if (-1 != keyEndIndex && -1 != keyEndIndex) {
                    String key = returnSubStatus.substring(keyStartIndex + 1, keyEndIndex);
                    if (key.equalsIgnoreCase(ReturnSubHeaderTimelineDatesEnum.COURIER_NAME.getName())) {
                        returnSubStatus = returnSubStatus.replace(returnSubStatus.substring(keyStartIndex, keyEndIndex + 1), returnCourierDetail.getReverseCourier());
                    } else if (key.equalsIgnoreCase(ReturnSubHeaderTimelineDatesEnum.TRACKING_ID.getName())) {
                        returnSubStatus = returnSubStatus.replace(returnSubStatus.substring(keyStartIndex, keyEndIndex + 1), trackingDetail);
                    } else if (key.equalsIgnoreCase(ReturnSubHeaderTimelineDatesEnum.TRACKING_URL.getName())) {
                        returnSubStatus = returnSubStatus.replace(returnSubStatus.substring(keyStartIndex, keyEndIndex + 1), trackingDetail);
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            }
            returnStatusHistory.setComment(returnSubStatus);
            // }
        } catch (Throwable e) {
            log.error("Caught exception while updating courier details", e);
            if (null != returnStatusHistory) {
                returnStatusHistory.setComment("");
            }
        }
    }

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse, List<RefundDetails> refundDetailsList, ReturnStatusHeadingDetail returnStatusHeadingDetail, Long incrementId, Date createdAt, String anyReceivingPoint) {

    }
}
