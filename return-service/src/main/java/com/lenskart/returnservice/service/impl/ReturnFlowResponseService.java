package com.lenskart.returnservice.service.impl;

import brave.propagation.CurrentTraceContext;
import brave.propagation.TraceContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.dto.ItemDto;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.ReturnFlowResultDto;
import com.lenskart.returncommon.model.enums.TriggerPoint;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.ReturnFlowResponse;
import com.lenskart.returncommon.model.response.ThresholdCheckResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnRefundRule;
import com.lenskart.returnrepository.repository.ReturnRefundRuleRepository;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IReturnFlowResponseService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.*;

@Slf4j
@Service
public class ReturnFlowResponseService implements IReturnFlowResponseService {
    @Value("${statusSync.auth.token}")
    private String authToken;
    @Value("${juno.base.status.sync.url}")
    private String junoBaseUrl;
    @Value("${fraud.threshold.limit:2}")
    private int fraudThresholdLimit;
    @Value("${fraud.threshold.limit.exceed:3}")
    private int fraudThresholdLimitExceed;

    @Autowired
    private ReturnUtil returnUtil;
    @Autowired
    OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    OrderAdaptorFeignClient orderAdaptorFeignClient;
    @Autowired
    ReturnRefundRuleRepository returnRefundRuleRepository;
    private RestTemplate restTemplate;
    private ObjectMapper objectMapper;

    @Autowired
    CurrentTraceContext currentTraceContext;

    @PostConstruct
    public void init() {
        restTemplate = new RestTemplate();
        objectMapper = new ObjectMapper();
    }

    @Override
    public ReturnFlowResponse getReturnFlowResponse(Integer incrementId, boolean isEligible) {
        log.info("[getReturnFlowResponse] get return flow response  from juno API for increment id : " + incrementId + " isEligible :" + isEligible);
        RestTemplate restTemplate = new RestTemplate();
        try {
            boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
            ResponseEntity<OrderInfoResponseDTO> orderInfoResponse = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(ORDERS_DTO))
                    : orderOpsFeignClient.getOrderDetails(incrementId);
            List<OrdersDTO> ordersDTOS = Objects.requireNonNull(orderInfoResponse.getBody()).getOrders();
            if (null != incrementId
                    && isJJOrder(incrementId, !CollectionUtils.isEmpty(ordersDTOS) ? ordersDTOS.get(0) : null)) {
                return getJJReturnFlowResponse(incrementId, ordersDTOS);
            }

            String url = "";
            if (isEligible)
                url = junoBaseUrl + "juno-order/v2/orders/" + incrementId + "/return/eligibility";
            else
                url = junoBaseUrl + "juno-order/v2/orders/" + incrementId + "/return/eligibility?checkScmEligibility=false";
            log.info("[getReturnFlowResponse] url is " + url);
            HttpHeaders headers = new HttpHeaders();
            headers.add("X-Auth-token", authToken);
            headers.add("X-Api-Client", "VSM");
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            HttpEntity<String> headersEntity = new HttpEntity<String>("parameters", headers);
            ResponseEntity<ReturnFlowResponse> responseEntity = restTemplate.exchange(url, HttpMethod.GET, headersEntity, ReturnFlowResponse.class);
            log.info("[getReturnFlowResponse] response from juno details API " + responseEntity + " " + "for the incrementId " + incrementId);
            return responseEntity.getBody();
        } catch (Exception e) {
            log.error("[getReturnFlowResponse] Exception occurred : ", e);
        }
        return null;
    }


    private ReturnFlowResponse getJJReturnFlowResponse(Integer incrementId, List<OrdersDTO> ordersDTOS) {
        try {
            ReturnFlowResponse returnFlowResponse = new ReturnFlowResponse();
            ReturnFlowResultDto returnFlowResultDto = new ReturnFlowResultDto();

            returnFlowResultDto.setReturnable(true);
            returnFlowResultDto.setIsFraud(false);
            returnFlowResultDto.setId(incrementId);

            List<ItemDto> itemsList = new ArrayList<>();
            List<Integer> magentoItemsIds = Optional.of(ordersDTOS.stream().map(OrdersDTO::getMagentoItemId).toList()).orElse(new ArrayList<>());
            for (Integer magentoItemId : magentoItemsIds) {
                ItemDto itemDto = new ItemDto();
                itemDto.setReturnable(false);
                itemDto.setId(magentoItemId);
                itemsList.add(itemDto);
            }
            returnFlowResultDto.setItemDtos(itemsList);

            returnFlowResponse.setResult(returnFlowResultDto);
            returnFlowResponse.setStatus(String.valueOf(HttpStatus.OK.value()));
            TraceContext traceContext = currentTraceContext.get();
            if (traceContext != null) {
                returnFlowResponse.setTraceId(traceContext.traceIdString());
            }
            log.info("[getJJReturnFlowResponse] incrementId: " + incrementId + " returnFlowResponse: " + returnFlowResponse + "items: " + returnFlowResponse.getResult().getItemDtos());
            return returnFlowResponse;
        } catch (Exception e) {
            log.error("[getJJReturnFlowResponse] exception " + e);
        }
        return null;
    }

    @Override
    public Boolean getReturnFlowResponseResult(Integer incrementId, boolean isEligibleRequired) {
        ReturnFlowResponse returnFlowResponse = getReturnFlowResponse(incrementId, isEligibleRequired);
        if (null != returnFlowResponse) {
            ReturnFlowResultDto returnFlowResultDto = returnFlowResponse.getResult();
            if (null != returnFlowResultDto) {
                if (null != returnFlowResultDto.getReturnable()) {
                    log.info("returning result as " + returnFlowResultDto.getReturnable());
                    return returnFlowResultDto.getReturnable();
                }
            }
        }
        return null;
    }

    @Override
    public ThresholdCheckResponse isFraudThresholdReached(Integer incrementId, List<Integer> uwItemIds, String triggerPoint) {
        ReturnFlowResponseService.log.info("[ReturnFlowResponseServiceImpl][isFraudThresholdReached] order : {}, uwItemIds : {}", incrementId, uwItemIds);
        ThresholdCheckResponse result = new ThresholdCheckResponse();
        ReturnRefundRule returnRefundRule = null;
        int exchangeCount = 0;
        try {
            boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
            ResponseEntity<OrderInfoResponseDTO> orderInfoResponse = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(EXCHANGE_ORDERS_DTO))
                    : orderOpsFeignClient.getOrderDetails(incrementId);
            ExchangeOrdersDTO exchangeOrdersDTO = Objects.requireNonNull(orderInfoResponse.getBody()).getExchangeOrdersDTO();
            if (exchangeOrdersDTO != null) {
                for (Integer uwItemId : uwItemIds) {
                    returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(uwItemId, TriggerPoint.ReturnInitiation.getName());
                    if (returnRefundRule != null || (StringUtils.isNotEmpty(triggerPoint) && TriggerPoint.ReturnInitiation.getName().equalsIgnoreCase(triggerPoint))) {
                        exchangeCount = exchangeOrdersDTO.getCountExchangeItems();
                        if (exchangeCount >= fraudThresholdLimitExceed) {
                            log.info("[ReturnFlowResponseServiceImpl][isFraudThresholdReached] order : {}, uwItemId : {}, the customer has already placed 3 exchanges", incrementId, uwItemId);
                            result.setStatus("200");
                            result.setHasViolatedThreshold(true);
                            return result;
                        } else if (exchangeCount == fraudThresholdLimit) {
                            log.info("[ReturnFlowResponseServiceImpl][isFraudThresholdReached] order : {}, uwItemId : {}, threshold reached : {}", incrementId, uwItemId, true);
                            result.setStatus("200");
                            result.setResult(true);
                            return result;
                        }
                        break;
                    }
                }
            }
            if (returnRefundRule == null && exchangeCount >= fraudThresholdLimitExceed) {
                log.info("[ReturnFlowResponseServiceImpl][isFraudThresholdReached] order : {}, No rule matched", incrementId);
                result.setStatus("200");
                result.setHasViolatedThreshold(true);
                return result;
            }
            result.setStatus("200");
        } catch (Exception e) {
            log.error("[ReturnFlowResponseServiceImpl][isFraudThresholdReached] order : {}, error occurred : {}", incrementId, e.getMessage());
            result.setStatus("500");
        }
        log.info("[ReturnFlowResponseServiceImpl][isFraudThresholdReached] order : {}, threshold reached : {}", incrementId, false);
        return result;
    }

    private boolean isJJOrder(Integer incrementId, OrdersDTO ordersDTO) {
        boolean jjOrder = false;
        try {
            if (Objects.nonNull(ordersDTO)
                    && StringUtils.isNotBlank(ordersDTO.getChannel())
                    && ordersDTO.getChannel().equalsIgnoreCase(Constant.CHANNEL.JJONLINE)) {
                jjOrder = true;
            }
        } catch (Exception e) {
            log.error("[isJJOrder] exception: " + e);
        }
        log.info("[isJJOrder] incrementId " + incrementId + " isJJOrder: " + jjOrder);
        return jjOrder;
    }
}
