package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.dto.ReturnPolicyInputDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundResponseDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundInputDTO;
import com.lenskart.returncommon.model.dto.ShippingDeliveredCompleted;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequest;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequestDTO;
import com.lenskart.returncommon.model.response.ReturnPolicyResponse;
import com.lenskart.returncommon.model.response.ReturnRefundEligibilityResponse;
import com.lenskart.returnrepository.entity.RefundRules;

import java.util.List;
import java.util.Map;

public interface IReturnRefundEligibilityService {
    ReturnRefundEligibilityResponse getReturnRefundEligibility(ReturnRefundEligibilityRequestDTO request) throws Exception;

    boolean bookStoreAppointment(ReturnRefundEligibilityRequest request) throws Exception;

    Map<Integer, ReturnRefundResponseDTO> processEligibilityRequestForStoreAppointment(ReturnRefundEligibilityRequestDTO request);

    Map<Integer, ReturnPolicyResponse> fetchReturnPolicyInBulk(List<ReturnPolicyInputDTO> returnPolicyInputDTOList);

    ShippingDeliveredCompleted fetchReturnEligibleTilDate(UwOrderDTO uwOrder, UwOrderDTO uwOrderWH, int returnEligibilityPeriod, List<ShippingStatusDetail> allShippingStatuses, ReturnRefundEligibilityRequestDTO returnRefundEligibilityRequestDTO, ReturnRefundInputDTO inputDTO);
}
