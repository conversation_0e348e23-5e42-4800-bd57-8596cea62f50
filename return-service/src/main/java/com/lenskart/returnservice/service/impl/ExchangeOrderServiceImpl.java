package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.lenskart.ordermetadata.dto.ExchangeOrderUnholdRequest;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IExchangeOrderService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static com.lenskart.returncommon.model.dto.ExchangedItem.CALLING_POINT.BACKWARD_FLOW;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.EXCHANGE_ORDER_UNHOLD_QUEUE;

@Service
@Slf4j
public class ExchangeOrderServiceImpl implements IExchangeOrderService {

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${juno.exchange.base.url}")
    private String junoExchangeBaseUrl;

    @Value("${statusSync.auth.token}")
    private String authToken;

    @Value("${callback.lkWalletReversal.sessionUrl}")
    private String junoSessionUrl;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Override
    public Boolean createExchangeOrder(ExchangeOrderCreationRequest exchangeOrderCreationRequest) {

        Boolean exchangeCreated = false;
        log.info("[createExchangeOrder] ExchangeOrderCreationRequest : "+exchangeOrderCreationRequest);
        try {
            String url = junoExchangeBaseUrl + "juno-order-payment/v2/orderpayment/wrapper";
            String session = callSession(junoSessionUrl);
            log.info("[createExchangeOrder] url is " + url);
            HttpHeaders headers = new HttpHeaders();
            headers.add("X-Auth-token", authToken);
            headers.add("X-Api-Client", "VSM");
            headers.add("X-Service-Type", "exchange");
            headers.add("X-Session-Token", session);
            headers.add("Content-Type", "application/json");
            HttpEntity<Object> headersEntity = new HttpEntity<>(exchangeOrderCreationRequest,headers);

            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST,headersEntity, String.class);
            log.info("[createExchangeOrder] Response from juno API " + responseEntity.toString());
            if (responseEntity != null && responseEntity.getBody() != null) {
                JSONObject jsonObject = new JSONObject(responseEntity.getBody());
                if(jsonObject.get("status").equals(200) && jsonObject.get("result") != null) {
                    exchangeCreated = true;
                }
            }else {
                log.info("[createExchangeOrder] Empty response from juno API " + responseEntity);
            }
        } catch (Exception e) {
            log.error("[createExchangeOrder] Exception occurred : "+ e);
        }
        return exchangeCreated;
    }

    @Override
    public boolean unholdExchangeOrder(Integer incrementId) {
        try {
            OrderStatusUpdateDetails orderStatusUpdateDetails = new OrderStatusUpdateDetails();
            orderStatusUpdateDetails.setIncrementId(incrementId);
            orderStatusUpdateDetails.setState(Constant.ORDER_STATE_OR_STATUS.PROCESSING);
            orderStatusUpdateDetails.setStatus(Constant.ORDER_STATE_OR_STATUS.PROCESSING);
            orderStatusUpdateDetails.setTrackingNo("ExchangeOnHolded_Unholded");
            ResponseEntity<Boolean> statusUpdateResponseEntity = orderOpsFeignClient.statusUpdate(orderStatusUpdateDetails);
            log.info("[unholdExchangeOrder] response : "+statusUpdateResponseEntity);
            if(statusUpdateResponseEntity.getStatusCode().is2xxSuccessful() && statusUpdateResponseEntity.getBody() != null){
                return statusUpdateResponseEntity.getBody();
            }
        }catch (Exception exception){
            log.error("[unholdExchangeOrder] exception occurred : "+exception);
        }
        return false;
    }

    @Override
    public ExchangeOrdersDTO getExchangeOrder(Integer uwItemId, Integer returnId) {
        ResponseEntity<OrderExchangeCancellationDetails> responseEntity = orderOpsFeignClient.getExchangeAndCancellationDetails(uwItemId, returnId);
        log.info("[unholdExchangeOrder] order : {} , responseEntity : {}", uwItemId, responseEntity);
        ExchangeOrdersDTO exchangeOrdersDTO = null;
        if (null != responseEntity && responseEntity.getBody() != null && !CollectionUtils.isEmpty((responseEntity.getBody().getExchangeOrdersDTOList()))) {
            exchangeOrdersDTO = responseEntity.getBody().getExchangeOrdersDTOList().get(0);
        }
        return exchangeOrdersDTO;
    }

    private String callSession(String sessionUrl) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String request ="";
        HttpEntity<String> entity = new HttpEntity<String>(request, headers);
        ResponseEntity<String> response = restTemplate.exchange(sessionUrl, HttpMethod.POST, entity,String.class);
        log.info("Response from session: " + response.toString());
        Gson gson = new Gson();
        JsonElement element = gson.fromJson (response.getBody(), JsonElement.class);
        JsonObject jsonObj = element.getAsJsonObject();
        JsonObject result = jsonObj.get("result").getAsJsonObject();
        String session = result.get("id").getAsString();
        log.info("Value of Session is: "+session);
        return session;
    }
}
