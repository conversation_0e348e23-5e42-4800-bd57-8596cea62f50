package com.lenskart.returnservice.service.impl;
/* Created by rajiv on 07/02/25 */

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ReturnOrderItemExtraInfoDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.Result;
import com.lenskart.returncommon.model.dto.ReturnItem;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnrepository.repository.ReturnCourierDetailRepository;
import com.lenskart.returnrepository.repository.ReturnDetailItemRepository;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.CosmosEventPayloadProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lenskart.returnservice.service.impl.RefundUtilsServiceImpl.gson;

@Component
@Slf4j
public class ReturnCreateCosmosEventPayLoadProvider implements CosmosEventPayloadProvider {

    @Autowired
    private ReturnDetailRepository returnDetailRepository;
    @Autowired
    private ReturnCourierDetailRepository returnCourierDetailRepository;
    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;
    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    private final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    private ReturnDetail getReturnDetail(Integer returnId) {
        Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnId);
        return returnDetailOptional.orElse(null);
    }

    private ReturnCourierDetail getReturnCourierDetail(Integer returnId) {
        return returnCourierDetailRepository.findTopByReturnId(returnId);
    }

    private Integer getMagentoItemId(Integer returnId) {
        List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByReturnId(returnId);
        log.info("[ReturnCreateCosmosEventPayLoadProvider][getMagentoItemId]Return Detail Items: {}", returnDetailItems);
        if (returnDetailItems.isEmpty()) {
            log.info("[ReturnCreateCosmosEventPayLoadProvider][getMagentoItemId] Return Detail Item is empty for returnId: {}", returnId);
            return null;
        }
        Integer uwItemId = returnDetailItems.get(0).getUwItemId();
        if (uwItemId == null) {
            log.info("[ReturnCreateCosmosEventPayLoadProvider][getMagentoItemId] uwItemId is null for returnId: {}", returnId);
            return null;
        }
        ResponseEntity<ReturnOrderItemExtraInfoDTO> response = orderOpsFeignClient.getExtraInfoDetails(uwItemId);
        log.info("[ReturnCreateCosmosEventPayLoadProvider][getMagentoItemId] response of getExtraInfoDetails: {}", gson.toJson(response));
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            return response.getBody().getMagentoItemId();
        }
        return null;
    }

    private Integer getItemCount(Integer returnId) {
        ReturnDetail returnDetail = getReturnDetail(returnId);
        if (returnDetail == null || returnDetail.getRequestId() == null) {
            return null;
        }
        return returnRequestRepository.findById(returnDetail.getRequestId())
                .map(returnRequest -> getItemCountFromCreationRequest(returnRequest.getReturnCreationRequest()))
                .orElse(null);
    }

    private Integer getItemCountFromCreationRequest(String request) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(request);
            log.info("[ReturnCreateCosmosEventPayLoadProvider][getItemCountFromCreationRequest] rootNode : {}", gson.toJson(rootNode));
            return rootNode.path("items").size();
        } catch (Exception e) {
            log.error("[getItemCountFromCreationRequest] error while parsing json", e);
        }
        return null;
    }

    private Map<String, Object> getPayloadForReturnCreate(String eventName, Integer returnId) {
        Map<String, Object> eventData = new HashMap<>();
        ReturnDetail returnDetail = getReturnDetail(returnId);
        ReturnCourierDetail returnCourierDetail = getReturnCourierDetail(returnId);
        if (returnDetail == null) {
            log.info("[getPayloadForReturnCreate] returnDetail is null for returnId: {}", returnId);
            return eventData;
        }
        Map<Long, ReturnItemDTO> magentoItemToReturnItemMap = new HashMap<>();
        ReturnRequest returnRequest = returnRequestRepository.findTop1ByIncrementIdOrderByCreatedAtDesc(returnDetail.getIncrementId());
        try {
            ReturnCreationRequestDTO requestDTO = objectMapper.readValue(returnRequest.getReturnCreationRequest(), ReturnCreationRequestDTO.class);
            magentoItemToReturnItemMap = requestDTO.getItems().stream().collect(Collectors.toMap(ReturnItemDTO::getMagentoId, Function.identity()));
        } catch (JsonProcessingException e) {
            log.error("[getPayloadForReturnCreate] error while parsing json", e);
            throw new RuntimeException(e);
        }
        eventData.put("checkpoint", "headless_return_pos_return_action");
        eventData.put("reverseCourier", returnCourierDetail != null ? returnCourierDetail.getReverseCourier() : null);
        eventData.put("groupId", returnDetail.getGroupId());
        eventData.put("returnMethod", returnDetail.getReturnMethod());
        eventData.put("returnSource", returnDetail.getSource());
        eventData.put("returnFacilityCode", returnDetail.getFacilityCode());
        Integer magentoId = getMagentoItemId(returnId);
        log.info("[getPayloadForReturnCreate] for returnId: {} magentoId: {}", returnId, magentoId);
        if (magentoId != null) {
            eventData.put("returnItem", magentoItemToReturnItemMap.getOrDefault((long) magentoId, null));
        }
        eventData.put("returnItemTotalCount", getItemCount(returnId));
        eventData.put("eventName", eventName);
        log.info("[pushOrderReturnToCosmos-POS]:: eventData: {}", eventData);
        return eventData;
    }

    @Override
    public Map<String, Object> getPayload(Object... params) {
        if (params == null || params.length < 3) {
            log.warn("[ReturnCreateCosmosEventPayLoadProvider] Invalid parameters provided to getPayload. Params received: {}", Arrays.toString(params));
            return Collections.emptyMap();
        }
        String eventName = (String) params[0];
        Integer returnId = (Integer) params[2];
        return getPayloadForReturnCreate(eventName, returnId);
    }
}
