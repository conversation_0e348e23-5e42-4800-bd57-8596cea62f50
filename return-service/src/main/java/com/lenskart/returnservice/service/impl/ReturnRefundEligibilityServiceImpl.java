package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.order.interceptor.dto.OrderEventDto;
import com.lenskart.order.interceptor.request.CreateOrderRequest;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.request.ItemRequestDTO;
import com.lenskart.ordermetadata.dto.request.ItemResoltionFlatRequestDTO;
import com.lenskart.orderops.model.Canceledorders;
import com.lenskart.refund.client.model.response.*;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.*;
import com.lenskart.returncommon.model.request.ItemRequest;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequest;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequestDTO;
import com.lenskart.returncommon.model.response.*;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.refund.client.model.dto.RefundAmount;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.request.RRRServiceRequest;
import com.lenskart.returncommon.model.response.KafkaProducerResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returncommon.utils.Constant.PAYMENT_METHOD;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderInterceptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.RefundFeignClient;
import com.lenskart.returnservice.feignclient.ReverseReceivingFeignClient;
import com.lenskart.returnservice.feignclient.WMSFeignClient;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.*;
import com.lenskart.returnservice.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.lenskart.returncommon.utils.Constant.ORDER_NOT_FOUND_EXCEPTION_MESSAGE;
import static com.lenskart.returncommon.utils.Constant.PAYMENT_METHOD.LK_CASH;
import static com.lenskart.returncommon.utils.Constant.PAYMENT_METHOD.LK_CASH_PLUS;
import static com.lenskart.returncommon.utils.Constant.RefundEligibilityError.*;
import static com.lenskart.returnservice.service.impl.JunoServiceImpl.FBR_APPROVED_STATUS;

@Slf4j
@Service
public class ReturnRefundEligibilityServiceImpl implements IReturnRefundEligibilityService {

    private static final String COMPLETE = "complete";
    private static final String CLOSED_STATE = "closed";
    private static final String DELIVERED = "delivered";
    private static final String SUCCESS = "success";
    private static final String PROG = "PROG";
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    WMSFeignClient wmsFeignClient;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired
    private INexsFacilityService nexsFacilityService;

    @Autowired
    private IReturnOrderActionService returnDetailsAction;

    @Autowired
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;

    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;

    @Autowired
    private IOrderUtilsService orderUtilsService;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private DelightActionRepository delightActionRepository;

    @Autowired
    private IReturnFlowResponseService returnFlowResponseService;

    @Value("${exchange.eligible.period:0}")
    private Integer exchangeEligibilityPeriodValue;

    @Value("${dealskart.manesar.facility:LKH03}")
    private String dealskartfacility;
    @Value("${cashfree.flag:cashfree}")
    private String cashfreeFlag;
    @Value("${return.eligible.period.progressive.package:30}")
    private Integer returnEligibilityPeriodForProgressivePackage;

    @Autowired
    private RefundFeignClient refundFeignClient;

    @Autowired
    private OrderInterceptorFeignClient orderInterceptorFeignClient;

    @Autowired
    private IExchangeOrderService exchangeOrderService;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private ReverseReceivingFeignClient reverseReceivingFeignClient;

    @Autowired
    private IRefundAuthorizationService authorizationService;

    @Autowired
    private IMvcService mvcService;

    @Autowired
    private StoreAppointmentOrdersRepository storeAppointmentOrdersRepository;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private FrameBrokenApprovalRepo frameBrokenApprovalRepo;

    @Autowired
    private ICancelAndConvertRuleService cancelAndConvertRuleService;

    @Autowired
    private ICancelAndConvertService cancelAndConvertService;

    @Autowired
    private ExchangeEventTrackerRepository exchangeEventTrackerRepository;

    @Autowired
    private PriorityReturnEligibleOrderRepo priorityReturnEligibleOrderRepo;

    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    private ReturnUtil returnUtil;

    @Value("${blocked.return.refund.exchange.order.priority.types:SHARKTANK25}")
    private List<String> orderPriorityTypes;

    private List<String> hooperPids;

    private List<String> allowedShippmentStatusForPOSReturn;

    private final Gson gson = new Gson();

    private final Map<String, Boolean> btsMap = new HashMap<>();

    public static final List<String> newReturnStatus = Arrays.asList(ReturnStatus.CUSTOMER_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED.getStatus(),
            ReturnStatus.CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED.getStatus(), ReturnStatus.RETURN_RESHIP.getStatus(),
            ReturnStatus.RETURN_REFUND_REJECTED.getStatus(), ReturnStatus.EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED_TBYB.getStatus(),
            ReturnStatus.PARTIAL_EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus());

    private static final List<String> cashPaymentMethods = Arrays.asList("COD","cashondelivery","cod","storecash","offlinecash");

    Map<String, PaymentMode> paymentModeMap = new HashMap<>();

    @Value("${should.enable.bts:true}")
    private boolean shouldEnableBTS;

    private List<String> needApprovalSources = Arrays.asList("POS","VSM");

    @PostConstruct
    private void init() {
        try {
            hooperPids = systemPreferenceService.getValuesAsList("return_refund_eligibility", "hooper_pids");
            log.info("Successfully loaded hooperPids {}", hooperPids);
            allowedShippmentStatusForPOSReturn = systemPreferenceService.getValuesAsList("allowed_shipment_status_pos_return","shipment_status");
            log.info("Successfully loaded allowedShippmentStatusForPOSReturn {}", allowedShippmentStatusForPOSReturn);
            btsMap.put("COD",false);
            paymentModeMap.put("cod", PaymentMode.POSTPAID);
            paymentModeMap.put("cashondelivery", PaymentMode.POSTPAID);
            paymentModeMap.put("exchangep", PaymentMode.EXCHANGE);
            paymentModeMap.put("payzero", PaymentMode.EXCHANGE);
        }
        catch (Exception e){
            log.info("exception occured while loading hooperPids {}", e.getMessage());
        }
    }

    @Override
    public ReturnRefundEligibilityResponse getReturnRefundEligibility(ReturnRefundEligibilityRequestDTO request) throws Exception {

        long startTime = System.nanoTime();
        ReturnRefundEligibilityResponse response = new ReturnRefundEligibilityResponse();
        try {
            List<ItemResponse> itemsResponses = processReturnRefundEligibilityRequest(request, response);

            response.setItemsResponseList(itemsResponses);

            long endTime = System.nanoTime();

            log.info("getReturnRefundEligibility total time taken in milliseconds :{}", (endTime - startTime) / 1000000);
        } catch (CompletionException e) {
            log.error("CompletionException in getReturnRefundEligibility: {} ", e.getMessage(), e);
            if (e.getMessage() != null && e.getMessage().contains(ORDER_NOT_FOUND_EXCEPTION_MESSAGE)) {
                response.setItemsResponseList(request.getItemList().stream().map(item -> new ItemResponse()).collect(Collectors.toList()));
                return response;
            }
            throw e;
        } catch (Exception e) {
            log.error("Error in getReturnRefundEligibility: {} ", e.getMessage(), e);
            throw e;
        }
        return response;
    }

    @Override
    public boolean bookStoreAppointment(ReturnRefundEligibilityRequest request) throws Exception {

        if(request==null || request.getOrderId()==null){
            log.info("bookStoreAppointment validation request failed :{}",request);
            return false;
        }

        ReturnRefundEligibilityRequestDTO returnRefundEligibilityRequest = orderOpsFeignClient.eligibilityRequestBodyForHeadless(request);

        List<ItemRequest> filterItemList=new ArrayList<>();
        for(ItemRequest itemRequest:request.getItemList()) {
            RRRServiceRequest serviceRequest = new RRRServiceRequest();
            ItemRequestDTO itemRequestDTO = new ItemRequestDTO();
            itemRequestDTO.setMagentoItemId(itemRequest.getMagentoItemId());

            serviceRequest.setOrderInfoResponse(returnRefundEligibilityRequest.getOrderInfoDTO());
            serviceRequest.setItemRequest(itemRequestDTO);
            populateUwOrderInRRRServiceRequest(serviceRequest);

            StoreAppointmentOrders storeAppointmentOrders=storeAppointmentOrdersRepository.findAppointmentByIdentifierValue(serviceRequest.getUwOrder().getUwItemId());
            if(storeAppointmentOrders==null || storeAppointmentOrders.getId()==null){
                filterItemList.add(itemRequest);
            }
        }


        request.setItemList(filterItemList);

        if(request.getItemList()==null || request.getItemList().isEmpty()){
            log.info("bookStoreAppointment validation request failed ,since itemlist is empty:{}",request);
            return false;
        }

        ResponseEntity<KafkaProducerResponse> kafkaResponse=kafkaService.pushToKafka("store_appointment_queue",String.valueOf(request.getOrderId()), request);

        return kafkaResponse.getBody() != null && kafkaResponse.getBody().getSuccess();
    }

    public List<ItemResponse> processReturnRefundEligibilityRequest(ReturnRefundEligibilityRequestDTO request, ReturnRefundEligibilityResponse response) throws Exception {
        List<ItemResponse> itemsResponses = new ArrayList<>();

        AtomicReference<GetRefundAmountResponse> refundAmountResponse = new AtomicReference<>();

        long startTime = System.nanoTime();
        checkAndPopulatePrerequisiteAPIData(request, refundAmountResponse);
        long endTime = System.nanoTime();
        log.info("checkAndPopulatePrerequisiteAPIData total time taken in milliseconds :{}", (endTime - startTime) / 1000000);

        populateMVCDetails(request, response);

        request.getItemList().parallelStream().forEach(itemRequest -> {
            RRRServiceRequest serviceRequest = new RRRServiceRequest();
            serviceRequest.setOrderInfoResponse(request.getOrderInfoDTO());
            serviceRequest.setShippingStatuses(request.getShippingStatusDetails());
            serviceRequest.setRefundAmountResponse(refundAmountResponse.get());
            serviceRequest.setRreRequest(request);
            serviceRequest.setItemRequest(itemRequest);

            //prepare data for ReturnRefundRuleService Request
            populateUwOrderInRRRServiceRequest(serviceRequest);

            if (serviceRequest.getUwOrder() == null) {
                log.info("[processReturnRefundEligibilityRequest] uwOrder not found");
                itemsResponses.add(new ItemResponse());
                return;
            }

            ReturnRefundInputDTO inputDTO = createReturnRefundInputDTO(serviceRequest);

            //fetch and apply refund rule
            ReturnRefundResponseDTO refundResponseDTO = fetchReturnRefundRule(inputDTO);
            long startTime1 = System.nanoTime();
            ReturnRefundResponseDTO overriddenRefundResponseDTO;
            if (refundResponseDTO != null && refundResponseDTO.isFetchExistingReturnResponse()) {
                //pick existing return refund rule and populate response
                log.info("[processReturnRefundEligibilityRequest] isFetchExistingReturnResponse ");
                getExistingReturnResponse(refundResponseDTO, inputDTO);
                overriddenRefundResponseDTO = createCloneOfReturnRefundResponseDTO(refundResponseDTO);
            } else {
                log.info("[processReturnRefundEligibilityRequest] new Rule matched ");
                overriddenRefundResponseDTO = createCloneOfReturnRefundResponseDTO(refundResponseDTO);
                performOverridingConditions(overriddenRefundResponseDTO, serviceRequest, inputDTO);
            }

            //below code is common for both types rule fire and existing rule match
            performCommonOverridingConditions(overriddenRefundResponseDTO, serviceRequest, inputDTO);
            long endTime1 = System.nanoTime();
            log.info("overridingResponse total time taken in milliseconds :{}", (endTime1 - startTime1) / 1000000);

            ItemResponse itemResponse = createItemResponse(serviceRequest, overriddenRefundResponseDTO);

            saveReturnRefundRule(refundResponseDTO, overriddenRefundResponseDTO, inputDTO);

            itemsResponses.add(itemResponse);
        });

        GetRefundAmountResponse getRefundAmountResponse = refundAmountResponse.get();
        if(Objects.nonNull(getRefundAmountResponse) && Objects.nonNull(getRefundAmountResponse.getRefundDetailsDTO())){
            double refundedAmount = getRefundAmountResponse.getRefundDetailsDTO().getTotalRefundedAmount().getAmount().doubleValue() + getRefundAmountResponse.getRefundDetailsDTO().getAdditionalFastRefundedAmount().getAmount().doubleValue();
            response.setOrderRefundedAmount(refundedAmount);
        }

        if (request.getOrderInfoDTO() != null && request.getOrderInfoDTO().getFraudCustomerDTO() != null) {
            response.setFraud(request.getOrderInfoDTO().getFraudCustomerDTO().isFraud());
        }

        if (request.isCheckCancellable()) {
            response.setOrderCancellable(request.getIsOrderCancellable());
        }

        log.info("[processReturnRefundEligibilityRequest] incrementId : {} response : {}", request.getOrderId(), itemsResponses);
        return itemsResponses;
    }

    @Override
    public Map<Integer,ReturnRefundResponseDTO> processEligibilityRequestForStoreAppointment(ReturnRefundEligibilityRequestDTO request) {
        Map<Integer,ReturnRefundResponseDTO> uwItemIdWithRules=new HashMap<>();
        AtomicReference<GetRefundAmountResponse> refundAmountResponse = new AtomicReference<>();

        long startTime = System.nanoTime();
        checkAndPopulatePrerequisiteAPIData(request, refundAmountResponse);
        long endTime = System.nanoTime();
        log.info("processEligibilityRequestForStoreAppointment checkAndPopulatePrerequisiteAPIData total time taken in milliseconds :{}", (endTime - startTime) / 1000000);
        ReturnRefundEligibilityResponse response=new ReturnRefundEligibilityResponse();
        populateMVCDetails(request, response);

        request.getItemList().parallelStream().forEach(itemRequest -> {
            RRRServiceRequest serviceRequest = new RRRServiceRequest();
            serviceRequest.setOrderInfoResponse(request.getOrderInfoDTO());
            serviceRequest.setShippingStatuses(request.getShippingStatusDetails());
            serviceRequest.setRefundAmountResponse(refundAmountResponse.get());
            serviceRequest.setRreRequest(request);
            serviceRequest.setItemRequest(itemRequest);

            //prepare data for ReturnRefundRuleService Request
            populateUwOrderInRRRServiceRequest(serviceRequest);

            ReturnRefundInputDTO inputDTO = createReturnRefundInputDTO(serviceRequest);

            //fetch and apply refund rule
            ReturnRefundResponseDTO refundResponseDTO = fetchReturnRefundRule(inputDTO);
            ShippingDeliveredCompleted shippingStatus = fetchReturnEligibleTilDate(serviceRequest.getUwOrder(), serviceRequest.getUwOrderWH(), refundResponseDTO.getReturnEligibilityPeriod(), serviceRequest.getShippingStatuses(), request, inputDTO);
            refundResponseDTO.setReturnEligibleTillDate(shippingStatus.getReturnEligiblePeriodDate());

            uwItemIdWithRules.put(serviceRequest.getUwOrder().getUwItemId(),refundResponseDTO);
        });

        log.info("[processEligibilityRequestForStoreAppointment] response : {}", uwItemIdWithRules);
        return uwItemIdWithRules;
    }

    public Map<Integer, ReturnPolicyResponse> getReturnPolicy(List<ReturnPolicyInputDTO> returnPolicyInputDTOList) {
        return returnRefundRuleService.fetchReturnPolicy(returnPolicyInputDTOList);
    }

    private void populateMVCDetails(ReturnRefundEligibilityRequestDTO request, ReturnRefundEligibilityResponse response) {
        String disableMVCCheck = systemPreferenceService.getSystemPreferenceValues("mvc_profiling","mvc_check_disabled", 1, TimeUnit.HOURS);
        OrdersDTO order= request.getOrderInfoDTO().getOrders().get(0);

        MVCDetailsDTO mvcDetailsDTO = null;
        if (!StringUtils.isEmpty(disableMVCCheck) && !Boolean.parseBoolean(disableMVCCheck)) {
            Long customerId = order.getCustomerId();

            MVCResponseDTO mvcResponseDTO = null;

            mvcResponseDTO = request.getMvcResponseDTO();
            if(mvcResponseDTO == null){
                log.info("[populateMVCDetails] customerID: {}, storeId: {} ", customerId,request.getStoreId());
                CustomerStoreProfileResponse mvcMvsStats = mvcService.getMvcMvsStats(customerId, request.getStoreId());

                mvcResponseDTO = new MVCResponseDTO(mvcMvsStats.getCustomerScore(), mvcMvsStats.getStoreScore());

                MvcOrderDTO mvcOrderResponseOld = mvcService.getMvcOrdersResponse("ORDER_ID", String.valueOf(order.getIncrementId()));
                if(mvcOrderResponseOld != null){
                    mvcResponseDTO.setCustomerScore(mvcOrderResponseOld.getCustomerScore());
                }
            }


            mvcDetailsDTO = new MVCDetailsDTO();
            request.setCustomerScore(mvcResponseDTO.getCustomerScore() == null ? 1 : mvcResponseDTO.getCustomerScore());
            request.setStoreScore(mvcResponseDTO.getStoreScore() == null ? 1 : mvcResponseDTO.getStoreScore());
            if (mvcResponseDTO.getCustomerScore() != null && mvcResponseDTO.getCustomerScore() > 9) {
                mvcDetailsDTO.setMvcOrder(true);
            }
            mvcDetailsDTO.setCustomerMvcScore(mvcResponseDTO.getCustomerScore() == null ? 1 : mvcResponseDTO.getCustomerScore());

        } else {
            request.setCustomerScore(1);
            request.setStoreScore(1);
        }
        response.setMvcDetails(mvcDetailsDTO);
    }

    public void checkAndPopulatePrerequisiteAPIData(ReturnRefundEligibilityRequestDTO request, AtomicReference<GetRefundAmountResponse> refundAmountResponseFinal) {
        AtomicReference<OrderInfoResponseDTO> orderInfoDTO = new AtomicReference<>();
        AtomicReference<List<ShippingStatusDetail>> shippingDetails = new AtomicReference<>();
        AtomicReference<GetRefundAmountResponse> refundAmountResponse = new AtomicReference<>();
        AtomicReference<Boolean> cancellabilityResponse = new AtomicReference<>();


        List<CompletableFuture<?>> futures = new ArrayList<>();
        List<AtomicReference> atomicReferences = new ArrayList<>();

        if (request.getOrderInfoDTO() == null) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(request.getOrderId(), "order-info.rollout.percentage");
                ResponseEntity<OrderInfoResponseDTO> orderInfoResponse = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(request.getOrderId(), null)
                        : orderOpsFeignClient.getOrderDetails(request.getOrderId());
                return orderInfoResponse.getBody();
            }));
            atomicReferences.add(orderInfoDTO);
        }else{
            orderInfoDTO.set(request.getOrderInfoDTO());
        }

        if (request.getShippingStatusDetails() == null) {
            futures.add(CompletableFuture.supplyAsync(() -> orderOpsFeignClient.getShippingDetails(request.getOrderId(), "").getBody()));
            atomicReferences.add(shippingDetails);
        }else{
            shippingDetails.set(request.getShippingStatusDetails());
        }

        if (request.getIsOrderCancellable() == null) {
            futures.add(CompletableFuture.supplyAsync(() -> orderOpsFeignClient.getIsOrderCancellable(request.getOrderId()).getBody()));
            atomicReferences.add(cancellabilityResponse);
        }else{
            cancellabilityResponse.set(request.getIsOrderCancellable());
        }

        futures.add(CompletableFuture.supplyAsync(() -> refundUtilsService.getOrderRefundAmount(request.getOrderId())));
        atomicReferences.add(refundAmountResponse);

        performAsyncApiCalls(futures, atomicReferences);

        request.setOrderInfoDTO(orderInfoDTO.get());
        request.setShippingStatusDetails(shippingDetails.get());
        request.setIsOrderCancellable(cancellabilityResponse.get());
        refundAmountResponseFinal.set(refundAmountResponse.get());
    }

    public ReturnRefundResponseDTO createCloneOfReturnRefundResponseDTO(ReturnRefundResponseDTO refundResponseDTO) {
        if (refundResponseDTO == null) {
            return null;
        }

        ReturnRefundResponseDTO clonedDTO = new ReturnRefundResponseDTO();

        clonedDTO.setId(refundResponseDTO.getId());
        clonedDTO.setIsReturnable(refundResponseDTO.getIsReturnable());
        clonedDTO.setDoRefund(refundResponseDTO.getDoRefund());
        clonedDTO.setAction(refundResponseDTO.getAction());
        clonedDTO.setRefundMethod(refundResponseDTO.getRefundMethod());
        clonedDTO.setExchangeOrderDispatch(refundResponseDTO.getExchangeOrderDispatch());
        clonedDTO.setFraud(refundResponseDTO.isFraud());
        clonedDTO.setReturnCount(refundResponseDTO.getReturnCount());
        clonedDTO.setExchangeCount(refundResponseDTO.getExchangeCount());
        clonedDTO.setRefundCount(refundResponseDTO.getRefundCount());
        clonedDTO.setReturnEligibilityPeriod(refundResponseDTO.getReturnEligibilityPeriod());
        clonedDTO.setRefundDispatch(refundResponseDTO.getRefundDispatch());
        clonedDTO.setWarrantyActive(refundResponseDTO.isWarrantyActive());
        List<String> originalRulesList = refundResponseDTO.getRulesList();
        if (originalRulesList != null) {
            clonedDTO.setRulesList(new ArrayList<>(originalRulesList));
        } else {
            clonedDTO.setRulesList(null);
        }
        clonedDTO.setFetchExistingReturnResponse(refundResponseDTO.isFetchExistingReturnResponse());

        return clonedDTO;
    }

    public ItemResponse createItemResponse(RRRServiceRequest serviceRequest, ReturnRefundResponseDTO refundResponseDTO) {
        ItemResponse itemResponse = new ItemResponse();
        itemResponse.setMagentoItemId(serviceRequest.getItemRequest().getMagentoItemId());
        itemResponse.setExchange(refundResponseDTO.isExchange());
        itemResponse.setExchangeOnlyCTA(refundResponseDTO.isExchangeOnlyCTA());
        itemResponse.setDoRefund(refundResponseDTO.getDoRefund());
        itemResponse.setReturnable(refundResponseDTO.getIsReturnable());
        itemResponse.setRefundMethods(refundResponseDTO.getRefundMethods());
        itemResponse.setRefundMethodRequest(refundResponseDTO.getRefundMethodRequest());
        itemResponse.setAmountToRefund(refundResponseDTO.getAmountToRefund());
        itemResponse.setDraftStatus(refundResponseDTO.isDraftStatus());
        itemResponse.setReturnEligibleTillDate(refundResponseDTO.getReturnEligibleTillDate());
        itemResponse.setApprovalNeeded(refundResponseDTO.isApprovalNeeded());
        itemResponse.setItemPrice(refundResponseDTO.getItemPrice());
        itemResponse.setItemRefundedAmount(refundResponseDTO.getItemRefundedAmount());
        itemResponse.setLkcashToRefund(refundResponseDTO.getLkcashToRefund());
        itemResponse.setLkcashPlusToRefund(refundResponseDTO.getLkcashPlusToRefund());
        itemResponse.setErrorForNotReturnable(refundResponseDTO.getErrorForNotReturnable());
        itemResponse.setErrorForNotRefundable(refundResponseDTO.getErrorForNotRefundable());
        itemResponse.setErrorForNotExchangeable(refundResponseDTO.getErrorForNotExchangeable());
        itemResponse.setRefundIntentCreatedAt(refundResponseDTO.getRefundIntentCreatedAt());
        itemResponse.setExchangeExpired(refundResponseDTO.isExchangeExpired());
        itemResponse.setReturnEligibilityPeriod(refundResponseDTO.getReturnEligibilityPeriod());
        itemResponse.setNeedApprovalMessage(refundResponseDTO.getNeedApprovalMessage());
        itemResponse.setReturnId(refundResponseDTO.getReturnId());
        itemResponse.setReturnSource(refundResponseDTO.getReturnSource());
        ErrorLables errorLables = new ErrorLables();
        errorLables.setErrorForNotReturnable(refundResponseDTO.getErrorForNotReturnable());
        errorLables.setErrorForNotRefundable(refundResponseDTO.getErrorForNotRefundable());
        errorLables.setErrorForNotExchangeable(refundResponseDTO.getErrorForNotExchangeable());
        itemResponse.setErrorLables(errorLables);
        itemResponse.setStoreCreditCancelConvertEligibility(refundResponseDTO.getStoreCreditCancelConvertEligibility());
        itemResponse.setRtoExchangeEligibilityResponse(refundResponseDTO.getRtoExchangeEligibilityResponse());
        return itemResponse;
    }

//    private void mockupValues(ItemResponse itemResponse) {
//        RtoExchangeEligibilityResponse rtoExchangeEligibilityResponse = new RtoExchangeEligibilityResponse();
//        rtoExchangeEligibilityResponse.setRtoExchangeable(true);
//        rtoExchangeEligibilityResponse.setEligibleScExchangeAmount(1200);
//        itemResponse.setRtoExchangeEligibilityResponse(rtoExchangeEligibilityResponse);
//        StoreCreditCancelConvertEligibility storeCreditCancelConvertEligibility = new StoreCreditCancelConvertEligibility();
//        storeCreditCancelConvertEligibility.setScCancelConvertible(true);
//        List<RefundBreakupResponse> refundBreakupResponseList = new ArrayList<>();
//        RefundBreakupResponse refundBreakupResponse = new RefundBreakupResponse();
//        refundBreakupResponse.setAmount(800);
//        refundBreakupResponse.setCurrency("INR");
//        refundBreakupResponse.setLabel("PhonePe");
//        refundBreakupResponse.setSource(true);
//        refundBreakupResponseList.add(refundBreakupResponse);
//        refundBreakupResponse = new RefundBreakupResponse();
//        refundBreakupResponse.setAmount(400);
//        refundBreakupResponse.setCurrency("INR");
//        refundBreakupResponse.setLabel("Store Credit");
//        refundBreakupResponseList.add(refundBreakupResponse);
//        storeCreditCancelConvertEligibility.setScRefundBreakup(refundBreakupResponseList);
//        itemResponse.setStoreCreditCancelConvertEligibility(storeCreditCancelConvertEligibility);
//    }

    public void saveReturnRefundRule(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundResponseDTO overriddenRefundResponseDTO, ReturnRefundInputDTO inputDTO) {
 log.info("[saveReturnRefundRule] saving return refund rule for uw_item_id : {}", inputDTO.getUwItemId());
        //       CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            ReturnRefundRule returnRefundRule = getReturnRefundRule(refundResponseDTO, overriddenRefundResponseDTO, inputDTO);
            returnRefundRuleService.persist(returnRefundRule);
//        });
//        completableFuture
//                .thenAccept(body -> {
//                    log.info("[saveReturnRefundRule] Response received: " + body);
//                })
//                .exceptionally(ex -> {
//                    ex.printStackTrace();
//                    return null;
//                });

    }

    public ReturnRefundRule getReturnRefundRule(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundResponseDTO overriddenRefundResponseDTO, ReturnRefundInputDTO inputDTO) {
        log.info("[getReturnRefundRule] uw_item_id : {}, trigger_point : {}, inputDTO : {}, refundResponseDTO : {}", inputDTO.getUwItemId(), inputDTO.getTriggerPoint(), inputDTO, refundResponseDTO);
        ReturnRefundRule returnRefundRule = returnRefundRuleService.getRefundRuleUwItemIdAndTriggerPoint(inputDTO.getUwItemId(), inputDTO.getTriggerPoint());
        try {
            if (returnRefundRule == null) {
                returnRefundRule = new ReturnRefundRule();
            }
            returnRefundRule.setReturnId(inputDTO.getReturnId());
            returnRefundRule.setUwItemId(inputDTO.getUwItemId());
            returnRefundRule.setRuleId(String.valueOf(refundResponseDTO.getId()));
            returnRefundRule.setRuleCalledFrom(inputDTO.getRuleCalledFrom());
            returnRefundRule.setTriggerPoint(inputDTO.getTriggerPoint());
            returnRefundRule.setRequest(objectMapper.writeValueAsString(inputDTO));
            if(StringUtils.isEmpty(returnRefundRule.getDecisionValues())){
                returnRefundRule.setDecisionValues(objectMapper.writeValueAsString(inputDTO.getDecisionTableRefundDTO()));
            }
            returnRefundRule.setReturnRefundRuleResponse(objectMapper.writeValueAsString(refundResponseDTO));
            returnRefundRule.setReturnRefundRuleOverriddenResponse(objectMapper.writeValueAsString(overriddenRefundResponseDTO));
        } catch (JsonProcessingException e) {
            log.error("[getReturnRefundRule] error : ", e);
        }
        return returnRefundRule;
    }

    public void getExistingReturnResponse(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO) {
        ReturnRefundRule returnRefundRule = returnRefundRuleService.getRefundRuleUwItemIdAndTriggerPoint(inputDTO.getUwItemId(), inputDTO.getTriggerPoint());
        Optional<ReturnDetail> returnOrder = Optional.empty();
        if (returnRefundRule != null && returnRefundRule.getReturnId() != null) {
            returnOrder = returnOrderActionService.getValidReturnDetailsByItem(returnRefundRule.getUwItemId());
        }
        if (returnRefundRule != null && StringUtils.isNotEmpty(returnRefundRule.getReturnRefundRuleResponse()) && returnOrder.isPresent()) {
            String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
            try {
                JsonNode jsonObject = objectMapper.readTree(returnRefundRuleResponse);
                String refundMethodStr = (jsonObject.has("refundMethod") && !jsonObject.get("refundMethod").isNull()) ? jsonObject.get("refundMethod").asText() : null;
                List<OrdersDTO> orders = inputDTO.getRrrServiceRequest().getOrderInfoResponse().getOrders();
                Optional<OrdersDTO> juspayOrder = orders.stream().filter(order -> order.getMethod().equalsIgnoreCase("juspay")).findFirst();
                if (juspayOrder.isPresent())
                    refundResponseDTO.setRefundMethod(getRefundMethods(inputDTO.getRrrServiceRequest().getOrderInfoResponse().getOrdersHeader(), refundMethodStr));
                else
                    refundResponseDTO.setRefundMethod(refundMethodStr);
                boolean dorefund = jsonObject.get("doRefund").asBoolean();
                refundResponseDTO.setDoRefund(dorefund);
                boolean isReturnable = jsonObject.get("isReturnable").asBoolean();
                refundResponseDTO.setIsReturnable(isReturnable);
                String action = (jsonObject.has("action") && !jsonObject.get("action").isNull()) ? jsonObject.get("action").asText() : null;
                refundResponseDTO.setAction(action);
                String exchangeOrderDispatch = (jsonObject.has("exchangeOrderDispatch") && !jsonObject.get("exchangeOrderDispatch").isNull()) ? jsonObject.get("exchangeOrderDispatch").asText() : null;
                boolean isWarrantyActive = jsonObject.has("warrantyActive") && jsonObject.get("warrantyActive").asBoolean();
                refundResponseDTO.setWarrantyActive(isWarrantyActive);
                refundResponseDTO.setExchangeOrderDispatch(exchangeOrderDispatch);
                refundResponseDTO.setId(Integer.parseInt(returnRefundRule.getRuleId()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            boolean isFraud = inputDTO.getRrrServiceRequest().getOrderInfoResponse().getFraudCustomerDTO() != null && inputDTO.getRrrServiceRequest().getOrderInfoResponse().getFraudCustomerDTO().isFraud();
            refundResponseDTO.setFraud(isFraud);
            refundResponseDTO.setExchangeCount(inputDTO.getExchangeCount());
            refundResponseDTO.setReturnCount(inputDTO.getReturnCount());
            refundResponseDTO.setRefundCount(inputDTO.getRefundCount());
            Integer returnEligibilityPeriod = getReturnEligibilityPeriodForProgressiveReturnPackageByUwItemId(inputDTO);
            if (returnEligibilityPeriod != 0) {
                refundResponseDTO.setReturnEligibilityPeriod(returnEligibilityPeriod+refundResponseDTO.getExtensionInReturnEligibilityPeriod());
            }
            ReturnDetail returnOrder1 = returnOrder.get();
            String returnOrderStatus = returnOrderActionService.getReturnOrderStatus(returnOrder1);
            if (null != returnOrder1) {
                if (ReturnStatus.RETURN_NEED_APPROVAL.getStatus().equalsIgnoreCase(returnOrderStatus) || ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus().equalsIgnoreCase(returnOrderStatus)) {
                    log.debug("setting refund method as NA because return id " + returnOrder1.getId() + " is in RETURN_NEED_APPROVAL/RETURN_NEED_APPROVAL_FROM_WAREHOUSE status");
                    refundResponseDTO.setDoRefund(false);
                    refundResponseDTO.setRefundMethod("NA");
                }

            }

            if (returnRefundRule.getReturnId() != null) {
                log.debug("[ExchangeRefundMethodServiceImpl][fetchReturnRefundRule] ReturnId is " + returnRefundRule.getReturnId());
                setDelightActions(refundResponseDTO, inputDTO, returnOrder1);
            } else {
                if (returnOrder1 != null) {
                    log.debug("[ExchangeRefundMethodServiceImpl][fetchReturnRefundRule] inside returnOrder with returnId " + returnOrder1.getId());

                    if (!returnOrderActionService.containsInReturnStatusList(returnOrderStatus)) {
                        log.debug("[ExchangeRefundMethodServiceImpl][fetchReturnRefundRule] returnId set in returnRefundRule table" + returnOrder.get().getId());
                        inputDTO.setReturnId(returnOrder1.getId());
                        try {
                            ReturnRefundResponseDTO responseDTO = new ObjectMapper().readValue(returnRefundRuleResponse, ReturnRefundResponseDTO.class);
                            setRefundDispatchForQcAtDoorstep(inputDTO, refundResponseDTO, returnOrder1);
                        } catch (Exception e) {
                            log.error("Error while setting returnRefundResponse for return id {}", returnOrder1.getId(), e);
                        }
                    }
                }
            }
        }
    }


    public String getRefundMethods(OrdersHeaderDTO ordersHeaderDTO, String refundMethod) {
        log.info("ExchangeRefundMethodServiceImpl] [setRefundMethods] setting refund methods for increment id " + ordersHeaderDTO.getIncrementId());
        if (Objects.nonNull(ordersHeaderDTO.getPaymentMode()) && ordersHeaderDTO.getPaymentMode().equalsIgnoreCase(PAYMENT_METHOD.SIMPL)) {
            if (StringUtils.isEmpty(refundMethod))
                return "";
            else if (refundMethod.contains(",")) {
                return getMultipleRefundMethods(refundMethod);
            } else {
                if (refundMethod.equalsIgnoreCase(PAYMENT_METHOD.NEFT) || refundMethod.equalsIgnoreCase(PAYMENT_METHOD.CASHFREE) || refundMethod.equalsIgnoreCase(PAYMENT_METHOD.STORECREDIT))
                    return "";
                else
                    return refundMethod;
            }
        } else if (Objects.isNull(ordersHeaderDTO.getPaymentMode())) {
            log.info("ExchangeRefundMethodServiceImpl] [setRefundMethods] payment mode received in orders header table" + ordersHeaderDTO);
            return "";
        } else {
            log.info("ExchangeRefundMethodServiceImpl] [setRefundMethods] payment mode received in orders header table" + ordersHeaderDTO.getPaymentMode());
            return refundMethod;
        }
    }

    public String getMultipleRefundMethods(String refundMethod) {
        String[] refundMethods = refundMethod.split(",");
        StringBuilder juspayAndSimplRefundMethods = new StringBuilder();
        Arrays.stream(refundMethods)
                .filter(method -> method.equalsIgnoreCase(PAYMENT_METHOD.EXCHANGE) || method.equalsIgnoreCase(PAYMENT_METHOD.SOURCE))
                .forEach(method -> juspayAndSimplRefundMethods.append(method + ","));
        if (!StringUtils.isEmpty(juspayAndSimplRefundMethods))
            juspayAndSimplRefundMethods.deleteCharAt(juspayAndSimplRefundMethods.length() - 1);
        return juspayAndSimplRefundMethods.toString();
    }


    public void performOverridingConditions(ReturnRefundResponseDTO refundResponseDTO, RRRServiceRequest serviceRequest, ReturnRefundInputDTO inputDTO) {

        unsetRefundabilityReturnabilityFlags(refundResponseDTO, inputDTO);

        setAction(refundResponseDTO, serviceRequest.getUwOrder());

        setExchangeOrderDispatchPoint(refundResponseDTO, inputDTO);

        setDelightActions(refundResponseDTO, inputDTO, null);

        //setReshipActions(refundResponseDTO, inputDTO);

        setOtherParams(refundResponseDTO,serviceRequest, inputDTO);

    }

    public void performCommonOverridingConditions(ReturnRefundResponseDTO refundResponseDTO, RRRServiceRequest serviceRequest, ReturnRefundInputDTO inputDTO) {
        checkPriorityReturnEligibleOrder(refundResponseDTO, serviceRequest, inputDTO);

        boolean isReturnable = getIsReturnable(serviceRequest.getUwOrder(), serviceRequest.getUwOrderWH(), refundResponseDTO, serviceRequest.getRreRequest(), serviceRequest.getOrderInfoResponse(), serviceRequest.getShippingStatuses(), inputDTO);

        refundResponseDTO.setIsReturnable(isReturnable);

        setReturnRefundabilityBasedOnRto(refundResponseDTO, serviceRequest, inputDTO);

        setRefundMethods(serviceRequest.getUwOrder(), refundResponseDTO, inputDTO);

        setReturnRefundabilityBasedOnExchangeDone(serviceRequest.getUwOrder(), serviceRequest.getOrderInfoResponse().getOrderExchangeCancellationDetails(), refundResponseDTO);

        setExchangeExpired(refundResponseDTO);

        setApprovalNeededFlag(refundResponseDTO, inputDTO, getDeliveredDate(serviceRequest.getUwOrder(), serviceRequest.getUwOrderWH(), serviceRequest.getShippingStatuses()));

        setDraftStatusAndRefundMethodCaptured(serviceRequest.getUwOrder(), refundResponseDTO);

        setFraudStatus(refundResponseDTO, serviceRequest);

        setEligibilityBasedOnRefundAmount(refundResponseDTO, inputDTO, serviceRequest);

        setExchangeOnlyFlag(refundResponseDTO, serviceRequest.getUwOrder());

        checkBOGOGOEligibility(refundResponseDTO, serviceRequest);

        blockFlagsBasedOnSharkTankOffers(refundResponseDTO, serviceRequest.getUwOrder());

        checkFrameBrokenApprovedItem(refundResponseDTO, serviceRequest);

        populateReturnDetails(refundResponseDTO, inputDTO);

        setStoreCreditDetailsForCncAndExchange(refundResponseDTO, inputDTO);

    }

    private void setStoreCreditDetailsForCncAndExchange(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO) {
        try{
            log.info("[setStoreCreditDetailsForCncAndExchange] uwItemId : {}", inputDTO.getUwItemId());
            Integer uwItemId = inputDTO.getUwItemId();
            double refundedScAmount = 0.0;
            boolean isCashComponentInPayment = false;
            boolean isPaymentModeExchange = false;

            Map<Integer, ExchangePricingDTO> exchangePricingDTOMap = inputDTO.getRrrServiceRequest().getOrderInfoResponse().getExchangePricingDTOMap();
            if(!CollectionUtils.isEmpty(exchangePricingDTOMap)){
                log.info("[setStoreCreditDetailsForCncAndExchange] exchangePricingDTOMap : {}",exchangePricingDTOMap);
                isPaymentModeExchange = true;
                for (Map.Entry<Integer, ExchangePricingDTO> entry : exchangePricingDTOMap.entrySet()) {
                    Integer orderId = entry.getKey();
                    ExchangePricingDTO exchangePricingDTO = entry.getValue();
                    if (exchangePricingDTO != null && cashPaymentMethods.contains(exchangePricingDTO.getPaymentMethod())) {
                        isCashComponentInPayment = true;
                        break;
                    }
                }
            }
            StoreCreditRefundDetails scRefundDetails = cancelAndConvertService.getScRefundDetails(uwItemId, inputDTO.getRrrServiceRequest().getUwOrder().getUnicomOrderCode());

            if(scRefundDetails != null && scRefundDetails.getStoreCreditResponse() != null){
                refundedScAmount = scRefundDetails.getRefundedScAmount();
                com.lenskart.refund.client.model.response.StoreCreditResponse storeCreditResponse = scRefundDetails.getStoreCreditResponse();
                log.info("[setStoreCreditDetailsForCncAndExchange] uwItemId : {}, refundedScAmount : {}", inputDTO.getUwItemId(), refundedScAmount);
                if(refundedScAmount > 0.0d){
                    Integer itemId = inputDTO.getRrrServiceRequest().getUwOrder().getItemId();
                    Optional<OrdersDTO> ordersDTOOptional = getOrdersDTO(inputDTO, itemId);
                    Optional<ItemWisePriceDetailsDTO> itemWisePriceDetailsDTOOptional = getItemWisePriceDetailsDTO(inputDTO, itemId);
                    PaymentDTO paymentDetails = getPaymentDetails(inputDTO);
                    if(itemWisePriceDetailsDTOOptional.isPresent() && ordersDTOOptional.isPresent()){
                        ItemWisePriceDetailsDTO itemWisePriceDetailsDTO = itemWisePriceDetailsDTOOptional.get();
                        Double onlinePayment = itemWisePriceDetailsDTO.getItemTotalAfterDiscount();
                        Double storeCreditDiscount = itemWisePriceDetailsDTO.getStoreCreditDiscount();
                        Double lenskartDiscount = itemWisePriceDetailsDTO.getLenskartDiscount();
                        Double lenskartPlusDiscount = itemWisePriceDetailsDTO.getLenskartPlusDiscount();
                        Double exchangeDiscount = itemWisePriceDetailsDTO.getExchangeDiscount();

                        OrdersDTO ordersDTO = ordersDTOOptional.get();
                        String paymentMethod = ordersDTO.getMethod();
                        log.info("[setStoreCreditDetailsForCncAndExchange] uwItemId : {}, onlinePayment : {}, storeCreditDiscount : {}, lenskartDiscount : {}, lenskartPlusDiscount : {}, exchangeDiscount: {}, paymentMethod : {}",
                                inputDTO.getUwItemId(), onlinePayment, storeCreditDiscount, lenskartDiscount, lenskartPlusDiscount, exchangeDiscount, paymentMethod);
                        //call rule engine to get the eligibility
                        PaymentMode paymentMode = isPaymentModeExchange ? PaymentMode.EXCHANGE : paymentModeMap.getOrDefault(paymentMethod.toLowerCase(), PaymentMode.PREPAID);
                        Map<RuleConditionType, Boolean> ruleConditionTypeMap = new HashMap<>();
                        if(paymentMethod.equalsIgnoreCase("storecash") || paymentMethod.equalsIgnoreCase("offlinecash")){
                            ruleConditionTypeMap.put(RuleConditionType.CASHPAYMENT, true);
                        }
                        if(scRefundDetails.isRtoFlag()){
                            ruleConditionTypeMap.put(RuleConditionType.RTO, true);
                        }
                        if(scRefundDetails.isReturnFlag()){
                            ruleConditionTypeMap.put(RuleConditionType.RETURN, true);
                        }
                        if(scRefundDetails.isPartialCancellationFlag()){
                            ruleConditionTypeMap.put(RuleConditionType.CANCELLATION, true);
                        }
                        if(scRefundDetails.isFastRefundFlag()){
                            ruleConditionTypeMap.put(RuleConditionType.FAST_REFUND, true);
                        }
                        if(paymentDetails != null && !StringUtils.isEmpty(paymentDetails.getTransactionId())){
                            ruleConditionTypeMap.put(RuleConditionType.IS_PAYMENT_DONE, true);
                        }
                        if(paymentDetails != null && DateUtil.isWithinThreeMonths(paymentDetails.getAuthorisedAt())){
                            ruleConditionTypeMap.put(RuleConditionType.IS_DATE_WITHIN_PG_ALLOWANCE, true);
                        }
                        if(isCashComponentInPayment){
                            ruleConditionTypeMap.put(RuleConditionType.IS_CASH_PAYMENT_INVOLVED, true);
                        }
                        if(refundedScAmount > storeCreditDiscount){
                            ruleConditionTypeMap.put(RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true);
                        }
                        if(refundedScAmount==storeCreditDiscount){
                            ruleConditionTypeMap.put(RuleConditionType.SC_REFUNDED_IS_EQUAL_SC_PAID, true);
                        }

                        CancelAndConvertEventRuleResponse cancelAndConvertEventRuleResponse = cancelAndConvertRuleService.evaluateAndGetActions(paymentMode, new RuleContextDTO(ruleConditionTypeMap));
                        log.info("[setStoreCreditDetailsForCncAndExchange] cancelAndConvertEventRuleResponse {}", cancelAndConvertEventRuleResponse);
                        if(cancelAndConvertEventRuleResponse != null){
                            Map<ActionType, Boolean> conditionsMap = cancelAndConvertEventRuleResponse.getActionRuleContextDTO().getConditions();
                            log.info("[setStoreCreditDetailsForCncAndExchange] conditionsMap {}", conditionsMap);
                            if(!CollectionUtils.isEmpty(conditionsMap)){
                                boolean exchangeAllowed = conditionsMap.get(ActionType.EXCHANGE);
                                boolean cncAllowed = conditionsMap.get(ActionType.CNC);
                                log.info("[setStoreCreditDetailsForCncAndExchange] exchangeAllowed {}, cncAllowed : {}", exchangeAllowed, cncAllowed);
                                setRtoExchangeEligibility(exchangeAllowed, refundResponseDTO, refundedScAmount);
                                setBifurcationForCnc(cncAllowed, refundResponseDTO, onlinePayment, storeCreditDiscount, lenskartDiscount, lenskartPlusDiscount, paymentMethod, storeCreditResponse);
                            }
                        }else {
                            log.info("[setStoreCreditDetailsForCncAndExchange] setting default response");
                            refundResponseDTO.setRtoExchangeEligibilityResponse(new RtoExchangeEligibilityResponse(false, refundedScAmount));
                            refundResponseDTO.setStoreCreditCancelConvertEligibility(new StoreCreditCancelConvertEligibility(false, new ArrayList<>()));
                        }
                    }
                }
            }

            overrideScDetailsForAlreadyCreatedExchangeOrder(refundResponseDTO, Long.valueOf(inputDTO.getRrrServiceRequest().getItemRequest().getMagentoItemId()));
            overrideScDetailsBasedOnBtsFlag(refundResponseDTO);

        }catch (Exception exception){
            log.error("[setStoreCreditDetailsForCncAndExchange] error : "+exception);
        }
    }

    private void overrideScDetailsBasedOnBtsFlag(ReturnRefundResponseDTO refundResponseDTO) {
        if(refundResponseDTO.getStoreCreditCancelConvertEligibility() != null && !shouldEnableBTS){
            log.info("[overrideScDetailsBasedOnBtsFlag] since flag is off, disabling sc bts data ");
            refundResponseDTO.setStoreCreditCancelConvertEligibility(null);
        }
    }

    private void overrideScDetailsForAlreadyCreatedExchangeOrder(ReturnRefundResponseDTO refundResponseDTO, Long magentoItemId) throws Exception {
        log.info("[overrideScDetailsForAlreadyCreatedExchangeOrder] magentoItemId : {} --- enter", magentoItemId);

        if(magentoItemId != null){
            ExchangeEventsTracker exchangeEventsTracker = exchangeEventTrackerRepository.findTopByMasterMagentoItemId(magentoItemId);
            if(exchangeEventsTracker != null){
                EventStatus eventsTrackerStatus = exchangeEventsTracker.getStatus();
                if(eventsTrackerStatus == EventStatus.CREATED){
                    //checking if the exchange order is cancelled or not
                    List<Canceledorders> canceledOrders = orderOpsFeignClient.getCanceledOrders(Math.toIntExact(exchangeEventsTracker.getExchangeOrderId()));
                    if(CollectionUtils.isEmpty(canceledOrders)){
                        log.info("[overrideScDetailsForAlreadyCreatedExchangeOrder] magentoItemId : {} --- exchange-order : {} is not cancelled, hence overiding response to null", magentoItemId, exchangeEventsTracker.getExchangeOrderId());
                        refundResponseDTO.setRtoExchangeEligibilityResponse(null);
                        refundResponseDTO.setStoreCreditCancelConvertEligibility(null);
                    }
                }
            }
        }

        log.info("[overrideScDetailsForAlreadyCreatedExchangeOrder] magentoItemId : {} ---- exit", magentoItemId);
    }

    private static PaymentDTO getPaymentDetails(ReturnRefundInputDTO inputDTO) {
        return inputDTO.getRrrServiceRequest()
                .getOrderInfoResponse()
                .getPaymentDTO();
    }

    private static Optional<ItemWisePriceDetailsDTO> getItemWisePriceDetailsDTO(ReturnRefundInputDTO inputDTO, Integer itemId) {
        return inputDTO.getRrrServiceRequest()
                .getRreRequest()
                .getOrderInfoDTO()
                .getItemWisePrices()
                .stream()
                .filter(itemWisePriceDetailsDTO -> Objects.equals(itemWisePriceDetailsDTO.getItemId(), itemId))
                .findAny();
    }

    private static Optional<OrdersDTO> getOrdersDTO(ReturnRefundInputDTO inputDTO, Integer itemId) {
        return inputDTO.getRrrServiceRequest()
                .getOrderInfoResponse()
                .getOrders()
                .stream()
                .filter(order -> Objects.equals(order.getItemId(), itemId))
                .findAny();
    }

    private void setRtoExchangeEligibility(boolean exchangeable, ReturnRefundResponseDTO refundResponseDTO, double refundedScAmount) {
        refundResponseDTO.setRtoExchangeEligibilityResponse(new RtoExchangeEligibilityResponse(exchangeable, refundedScAmount));
    }

    private void setBifurcationForCnc(boolean cncAllowed, ReturnRefundResponseDTO refundResponseDTO, Double onlinePayment, Double storeCreditDiscount, Double lenskartDiscount, Double lenskartPlusDiscount, String paymentMethod, com.lenskart.refund.client.model.response.StoreCreditResponse storeCreditResponse) {
        List<RefundBreakupResponse> scRefundBreakupList = new ArrayList<>();
        if(onlinePayment > 0.0d){
            RefundBreakupResponse refundBreakupResponse = new RefundBreakupResponse();
            refundBreakupResponse.setLabel(paymentMethod);
            refundBreakupResponse.setAmount(onlinePayment);
            refundBreakupResponse.setCurrency(storeCreditResponse.getCurrency());
            refundBreakupResponse.setSource(true);
            scRefundBreakupList.add(refundBreakupResponse);
        }
        if(storeCreditDiscount > 0.0d){
            RefundBreakupResponse refundBreakupResponse = new RefundBreakupResponse();
            refundBreakupResponse.setLabel("Store Credit");
            refundBreakupResponse.setAmount(storeCreditDiscount);
            refundBreakupResponse.setCurrency(storeCreditResponse.getCurrency());
            scRefundBreakupList.add(refundBreakupResponse);
        }
        if(lenskartDiscount > 0.0d){
            RefundBreakupResponse refundBreakupResponse = new RefundBreakupResponse();
            refundBreakupResponse.setLabel("LKCash");
            refundBreakupResponse.setAmount(lenskartDiscount);
            refundBreakupResponse.setCurrency(storeCreditResponse.getCurrency());
            scRefundBreakupList.add(refundBreakupResponse);
        }
        if(lenskartPlusDiscount > 0.0d){
            RefundBreakupResponse refundBreakupResponse = new RefundBreakupResponse();
            refundBreakupResponse.setLabel("LKCashPlus");
            refundBreakupResponse.setAmount(lenskartPlusDiscount);
            refundBreakupResponse.setCurrency(storeCreditResponse.getCurrency());
            scRefundBreakupList.add(refundBreakupResponse);
        }
        refundResponseDTO.setStoreCreditCancelConvertEligibility(new StoreCreditCancelConvertEligibility(cncAllowed, scRefundBreakupList));
    }

    private void checkPriorityReturnEligibleOrder(ReturnRefundResponseDTO refundResponseDTO, RRRServiceRequest serviceRequest, ReturnRefundInputDTO inputDTO) {
        try {
            Integer magentoItemId = serviceRequest.getItemRequest().getMagentoItemId();
            Integer incrementId = serviceRequest.getRreRequest().getOrderId();
            long returnPeriod = returnRefundRuleService.deliveredDaysBefore(inputDTO.getRrrServiceRequest().getUwOrder(), inputDTO.getRrrServiceRequest().getShippingStatuses());
            if (isPriorityReturnEligibleOrder(incrementId, magentoItemId) && returnPeriod <= 100) {
                log.info("[checkPriorityReturnEligibleOrder] making everything eligible for priority order, magentoId : {}", serviceRequest.getItemRequest().getMagentoItemId());
                refundResponseDTO.setIsReturnable(true);
                refundResponseDTO.setExchange(true);
                refundResponseDTO.setDoRefund(true);
                refundResponseDTO.setReturnEligibilityPeriod(100);
                OrdersDTO ordersDTO = serviceRequest.getOrderInfoResponse().getOrders().stream().filter(o -> o.getMagentoItemId() == magentoItemId).findAny().orElse(null);
                refundResponseDTO.setRefundMethod(String.join(",", getPriorityReturnEligibleOrderRefundMethods(ordersDTO != null ? ordersDTO.getMethod() : null)));
            }
        } catch (Exception e) {
            log.error("[checkPriorityReturnEligibleOrder] error while checking priority eligible order", e);
        }
    }

    public List<String> getPriorityReturnEligibleOrderRefundMethods(String paymentMethod) {
        try {
            SystemPreference systemPreference = systemPreferenceRepository.findTopByGroupAndKey("priority_return_eligible", "source_refund_method_mapping");

            if (systemPreference != null) {
                Map<String, List<String>> config = objectMapper.readValue(systemPreference.getValue(), new TypeReference<>() {
                });
                log.info("[getPriorityReturnEligibleOrderRefundMethods] config :{}", config);
                String key = (paymentMethod == null ? "default" : paymentMethod.toLowerCase());
                List<String> refundMethods = config.getOrDefault(key, config.getOrDefault("default", List.of("NA")));
                log.info("[getPriorityReturnEligibleOrderRefundMethods] refundMethods :{}", refundMethods);
                return refundMethods;
            }
        } catch (Exception e) {
            return List.of("NA");
        }
        return List.of("NA");
    }

    public boolean isPriorityReturnEligibleOrder(Integer incrementId, Integer magentoItemId) {
        return priorityReturnEligibleOrderRepo.findTopByIncrementIdAndMagentoItemIdAndActiveIsTrue(incrementId, magentoItemId) != null;
    }

    private void checkFrameBrokenApprovedItem(ReturnRefundResponseDTO refundResponseDTO, RRRServiceRequest serviceRequest) {
        if (isFrameBrokenApprovedItem(serviceRequest.getUwOrder().getItemId()) || (serviceRequest.getUwOrderWH() != null && isFrameBrokenApprovedItem(serviceRequest.getUwOrderWH().getItemId()))) {
            log.info("[isItemReturnable] Frame broken item is not allowed to return itemId :{}", serviceRequest.getUwOrder().getItemId());
            refundResponseDTO.setIsReturnable(false);
            refundResponseDTO.setExchange(false);
            refundResponseDTO.setDoRefund(false);
            refundResponseDTO.setRefundMethods(Collections.emptyList());
            refundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.FRAME_BROKEN_ORDER_NOT_ALLOWED);
            refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.FRAME_BROKEN_ORDER_NOT_ALLOWED);
            refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.FRAME_BROKEN_ORDER_NOT_ALLOWED);
        }

//        if (serviceRequest.getOrderInfoResponse().getOrdersHeader().isFrameBrokenGvFlow()) {
//            refundResponseDTO.setDoRefund(false);
//            refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.FRAME_BROKEN_GV_ORDER_NOT_ALLOWED);
//        }
    }

    public boolean isFrameBrokenApprovedItem(Integer itemId) {
        return frameBrokenApprovalRepo.findTopByItemIdAndStatusOrderByIdDesc(itemId, FBR_APPROVED_STATUS) != null;
    }

    public void checkBOGOGOEligibility(ReturnRefundResponseDTO refundResponseDTO, RRRServiceRequest serviceRequest) {
        String incrementId = String.valueOf(serviceRequest.getUwOrder().getIncrementId());
        log.info("[checkBOGOGOEligibility] doRefund: {}, refundMethods: {}, incrementId: {}",
                refundResponseDTO.getDoRefund(), refundResponseDTO.getRefundMethods(), incrementId);

        boolean doRefund = Boolean.TRUE.equals(refundResponseDTO.getDoRefund());
        List<String> refundMethods = refundResponseDTO.getRefundMethods();

        if (doRefund && refundMethods != null && !refundMethods.isEmpty() && !refundMethods.contains("NA")) {
            boolean bogogoApplicable = isBOGOGOApplicable(serviceRequest.getUwOrder().getIncrementId(),
                    serviceRequest.getUwOrder(),
                    refundResponseDTO.getReturnId());
            log.info("[checkBOGOGOEligibility] bogogoApplicable:{}", bogogoApplicable);
            if (bogogoApplicable) {
                refundResponseDTO.setDoRefund(false);
                refundResponseDTO.setExchange(true);
                refundResponseDTO.setRefundMethods(Collections.singletonList("exchange"));
                refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_AS_ORDER_IS_BOGOGO);

                log.info("[checkBOGOGOEligibility] BOGOGO applied - refund disabled, exchange enabled for incrementId: {}", incrementId);
            }
        }
    }

    public boolean isBOGOGOApplicable(Integer incrementId, UwOrderDTO uwOrder, Integer returnId) {
        String preferenceValue = systemPreferenceService.getSystemPreferenceValues(
                Constant.SYSTEM_PREFERENCE_GROUPS.RETURN_REFUND_ELIGIBILITY,
                Constant.SYSTEM_PREFERENCE_KEYS.CHECK_BOGOGO,
                1, TimeUnit.HOURS);

        boolean isBOGOGOCheckEnabled = StringUtils.isNotBlank(preferenceValue) && Boolean.parseBoolean(preferenceValue);

        if (!isBOGOGOCheckEnabled) {
            log.info("[isBOGOGOApplicable] BOGOGO check disabled via system preference for incrementId: {}", incrementId);
            return false;
        }

        try {
            Integer ultimateParentIncrementId = getUltimateParentIncrementId(uwOrder, returnId, incrementId);
            CreateOrderRequest createOrderRequest = getCreateOrderRequest(ultimateParentIncrementId);
            log.info("[isBOGOGOApplicable] createOrderRequest for incrementId {}: {}", incrementId, createOrderRequest);
            return createOrderRequest != null && createOrderRequest.isSalesOrder();
        } catch (Exception e) {
            log.error("[isBOGOGOApplicable] Error checking BOGOGO eligibility for incrementId: {}", incrementId, e);
            return false;
        }
    }


    public Integer getUltimateParentIncrementId(UwOrderDTO uwOrder, Integer returnId, Integer incrementId) {
        log.info("[getUltimateParentIncrementId] with uwOrder:{}, returnId:{}, incrementId:{}", uwOrder.getUwItemId(), returnId, incrementId);
        if (uwOrder == null || returnId == null) return incrementId;

        ExchangeOrdersDTO exchangeOrdersDTO = exchangeOrderService.getExchangeOrder(uwOrder.getUwItemId(), returnId);
        log.info("[getUltimateParentIncrementId] exchangeOrdersDTO:{}", exchangeOrdersDTO);
        if (exchangeOrdersDTO != null) {
            return exchangeOrdersDTO.getIncrementId();
        }
        return incrementId;
    }


    public CreateOrderRequest getCreateOrderRequest(Integer ultimateParentIncrementId) {
        if (ultimateParentIncrementId == null) {
            log.warn("[getCreateOrderRequest] Received null ultimateParentIncrementId");
            return null;
        }
        try {
            ResponseEntity<OrderEventDto> response = orderInterceptorFeignClient.getIsOrderCancellable(
                    ultimateParentIncrementId.toString(), "CREATE_ORDER");

            if (HttpStatus.OK.equals(response.getStatusCode()) || HttpStatus.ACCEPTED.equals(response.getStatusCode())) {
                log.info("[getCreateOrderRequest] Success: status={}, body={}", response.getStatusCode(), response.getBody());
                ObjectMapper mapper = new ObjectMapper()
                        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                return mapper.readValue(response.getBody().getRequestData(), CreateOrderRequest.class);
            } else {
                log.info("[getCreateOrderRequest] Failed: status={}, body={}", response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            log.error("[getCreateOrderRequest] Exception while fetching CREATE_ORDER for incrementId: {}", ultimateParentIncrementId, e);
        }

        return null;
    }



    private void populateReturnDetails(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO) {
        ReturnDetail returnOrder = returnOrderActionService.getReturnOrderBasedOnUwItemIdAndStatus(inputDTO.getUwItemId());
        if(returnOrder != null){
            String returnSource = returnOrder.getSource();
            String returnStatus = returnOrderActionService.getReturnOrderStatus(returnOrder);
            if(!newReturnStatus.contains(returnStatus)){
                refundResponseDTO.setReturnId(returnOrder.getId());
                refundResponseDTO.setReturnSource(returnSource);
            }
        }
    }

    private void setReturnEligibilityPeriodForHooper(ReturnRefundResponseDTO refundResponseDTO,RRRServiceRequest serviceRequest, ReturnRefundInputDTO inputDTO) {
        if("SG".equals(serviceRequest.getOrderInfoResponse().getOrdersHeader().getLkCountry())
                && serviceRequest.getOrderInfoResponse().getUwOrders().stream().anyMatch(uwOrderDTO -> hooperPids.contains(String.valueOf(uwOrderDTO.getProductId())))
                && inputDTO.getExchangeCount()==0){
            log.info("setting return EligibilityPeriod to 365");
            refundResponseDTO.setReturnEligibilityPeriod(365);
        }
    }

    private void setExchangeExpired(ReturnRefundResponseDTO refundResponseDTO) {
        if (exchangeEligibilityPeriodValue.equals(0) || !refundResponseDTO.isExchange() || refundResponseDTO.getRefundMethodRequest() == null || !refundResponseDTO.getRefundMethodRequest().equalsIgnoreCase("exchange") || refundResponseDTO.getRefundIntentCreatedAt() == null) {
            return;
        }
        log.info("inside setExchangeExpired refundResponseDTO:{}", refundResponseDTO);
        Date time = refundResponseDTO.getRefundIntentCreatedAt();
        Calendar c = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        c.setTime(time);
        c.add(Calendar.DATE, exchangeEligibilityPeriodValue);
        Date currentDate = new Date();

        String currentDateString = sdf.format(currentDate);
        String eligibilityEndDateString = sdf.format(c.getTime());

        boolean isExchangeExpired = currentDateString.compareTo(eligibilityEndDateString) > 0;
        refundResponseDTO.setExchange(!isExchangeExpired);
        refundResponseDTO.setExchangeExpired(isExchangeExpired);
        refundResponseDTO.setReturnEligibleTillDate(eligibilityEndDateString);
    }

    private void setExchangeOnlyFlag(ReturnRefundResponseDTO refundResponseDTO, UwOrderDTO uwOrder) {
        if (refundResponseDTO.isExchangeCreatedAndCancelled() && refundResponseDTO.isExchange()) {
            refundResponseDTO.setExchangeOnlyCTA(enableExchangeOnlyCTA(uwOrder.getUwItemId()));
            log.info("[setExchangeOnlyFlag] Exchange created and canceled for uwItemId: {}. Setting ExchangeOnlyCTA to: {}", uwOrder.getUwItemId(), refundResponseDTO.isExchangeOnlyCTA());
        }
    }

    public boolean enableExchangeOnlyCTA(Integer uwItemId) {
        boolean enableExchangeOnlyCTA = false;
        try {
            boolean isRPUCancelNotAllowedStatus = false;
            boolean isExchangeIntent = false;
            ReturnDetailItem returnOrderItem = returnOrderActionService.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
            if (returnOrderItem != null) {
                String status = returnOrderActionService.getReturnOrderStatusById(returnOrderItem.getReturnId());
                List<String> rpuCancelNotAllowedStatusList = systemPreferenceService.getValuesAsList("clickpost_rpu_cancel", "clickpost_rpu_cancel_not_allowed_status");
                isRPUCancelNotAllowedStatus = rpuCancelNotAllowedStatusList.contains(status);

                RefundIntentDTO refundIntentDTO = refundUtilsService.getRefundMethodIntentByCustomer(returnOrderItem.getReturnId());
                isExchangeIntent = "exchange".equalsIgnoreCase(refundIntentDTO.getRefundIntent());
                log.info("[enableExchangeOnlyCTA] returnOrderStatus:{} and refundTarget:{} for uwItemId:{}", status, refundIntentDTO.getRefundIntent(), uwItemId);
            }
            enableExchangeOnlyCTA = isRPUCancelNotAllowedStatus && isExchangeIntent;
        } catch (Exception e) {
            log.error("[enableExchangeOnlyCTA] setting enableExchangeOnlyCTA as false because of exception for uwItemId:{}, exception: ", uwItemId, e);
        }
        log.info("[enableExchangeOnlyCTA] setting enableExchangeOnlyCTA as {} for uwItemId:{}", enableExchangeOnlyCTA, uwItemId);
        return enableExchangeOnlyCTA;
    }

    public void setReshipActions(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO) {
        ReturnDetailItem returnOrderItem = returnOrderActionService.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(inputDTO.getUwItemId());
        if (returnOrderItem != null && returnOrderItem.getReturnId() != null) {
            ReturnDetail returnOrder = returnOrderActionService.findByReturnIdAndStatus(returnOrderItem.getReturnId(), ReturnStatus.RETURN_RESHIP.getStatus());
            //get following info from reverse receiving
            ItemResoltionFlatRequestDTO itemResoltionFlatRequestDTO = new ItemResoltionFlatRequestDTO();
            itemResoltionFlatRequestDTO.setReturnId(returnOrderItem.getReturnId());
            itemResoltionFlatRequestDTO.setUwItemId(inputDTO.getUwItemId());
            itemResoltionFlatRequestDTO.setResolution("PUTAWAY_YES_GOOD_INVENTORY");
            ItemResolutionFlatDTO itemResolutionFlat = null;
            try{
                itemResolutionFlat = reverseReceivingFeignClient.getItemResolutionFlat(itemResoltionFlatRequestDTO);
            }catch (Exception exception){
                log.error("[setReshipActions] exception occurred : {}", exception.getMessage());
            }
            ReshipItemDetail reshipItemDetail = returnDetailsAction.findByReturnId(returnOrderItem.getReturnId());
            if (null != returnOrder || null != reshipItemDetail || null != itemResolutionFlat) {
                refundResponseDTO.setRefundMethod("NA");
                refundResponseDTO.setDoRefund(false);
                log.debug("[ExchangeRefundMethodServiceImpl][getDroolsRefundResponse]  Setting IsExchange to false  and no exchange order will be created as reship is marked for this UwItemId " + " " + inputDTO.getUwItemId());
            }
        }
    }

    public void setDelightActions(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO, ReturnDetail returnOrder) {
        log.info("[ReturnRefundEligibilityServiceImpl][setDelightActions] ReturnId is " + inputDTO.getReturnId());
        if (returnOrder == null) {
            returnOrder = returnOrderActionService.getValidReturnDetailsByItem(inputDTO.getUwItemId()).orElse(null);
        }

        if (returnOrder != null) {
            setRefundDispatchForQcAtDoorstep(inputDTO, refundResponseDTO, returnOrder);
        }

        DelightAction delightAction = delightActionRepository.findTop1ByReturnIdOrderByIdDesc(inputDTO.getReturnId());
        if (delightAction != null && StringUtils.isNotEmpty(delightAction.getDelightMethod())) {
            refundResponseDTO.setRefundMethod(delightAction.getDelightMethod());
            refundResponseDTO.setDoRefund(true);
            log.info("[ReturnRefundEligibilityServiceImpl][setDelightActions] delightAction methods are " + delightAction.getDelightMethod());
        }
    }

    public void setRefundDispatchForQcAtDoorstep(ReturnRefundInputDTO inputDTO, ReturnRefundResponseDTO responseDTO, ReturnDetail returnOrder) {
        Integer orderId = inputDTO.getRrrServiceRequest().getRreRequest().getOrderId();
        log.info("START checkAndSetRefundDispatchForQcAtDoorstep  returnId={} , orderId ={}", inputDTO.getReturnId(), orderId);
        String refundDispatchForQcAtDoorstep = null;
        if (Objects.nonNull(returnOrder.getIsQcAtDoorstep()) && returnOrder.getIsQcAtDoorstep() == 1) {
            refundDispatchForQcAtDoorstep = nexsFacilityService.getSystemPreferenceValues("qc_at_doorstep", "refund_dispatch");
        } else if (Objects.nonNull(returnOrder.getIsQcAtDoorstep()) && returnOrder.getIsQcAtDoorstep() == 2) {
            refundDispatchForQcAtDoorstep = nexsFacilityService.getSystemPreferenceValues("refund_dispatch_qc_overridden", "refund_dispatch");
        }

        if (StringUtils.isNotBlank(refundDispatchForQcAtDoorstep) && TriggerPoint.CourierPickup.getName().equalsIgnoreCase(inputDTO.getTriggerPoint())
                && ReturnStatus.AWB_ASSIGNED.getStatus().equalsIgnoreCase(inputDTO.getRuleCalledFrom())) {
            try {
                String prevDispatchPoint = responseDTO.getRefundDispatch();
                responseDTO.setRefundDispatch(refundDispatchForQcAtDoorstep);
                String comment = "For return id " + inputDTO.getReturnId() + " RefundDispatch point overridden to " + refundDispatchForQcAtDoorstep + " from " + prevDispatchPoint;
                insertOrderComment(comment, inputDTO.getRrrServiceRequest().getRreRequest().getOrderInfoDTO().getOrdersHeader().getOrderId());
            } catch (Exception e) {
                log.error("ERROR in checkAndSetRefundDispatchForQcAtDoorstep orderId={} returnId={} isQcAtDoorstep={}", orderId, inputDTO.getReturnId(), returnOrder.getIsQcAtDoorstep(), e);
            }
            log.info("END checkAndSetRefundDispatchForQcAtDoorstep orderId={} returnId={} isQcAtDoorstep={}", inputDTO.getReturnId(), returnOrder.getId(), returnOrder.getIsQcAtDoorstep());
        }
    }

    public void insertOrderComment(String comment, Integer orderId) {
        try {

            // Define the JSON payload
            Map<String, String> comments = new HashMap<>();
            comments.put("comment", comment);

            CompletableFuture<Void> commentFuture = CompletableFuture.runAsync(() ->
            {
                orderOpsFeignClient.insertOrderComment(comments, orderId);
            });

            // Handle the response when it's available
            commentFuture
                    .thenAccept(body -> {
                        log.info("[insertOrderComment] Response received: " + body);
                    })
                    .exceptionally(ex -> {
                        ex.printStackTrace();
                        return null;
                    });
        } catch (Exception exception) {
            log.error("[insertOrderComment] error occurred : " + exception);
        }

    }

    public void setOtherParams(ReturnRefundResponseDTO refundResponseDTO,RRRServiceRequest serviceRequest, ReturnRefundInputDTO inputDTO) {
        refundResponseDTO.setReturnCount(inputDTO.getReturnCount());
        refundResponseDTO.setExchangeCount(inputDTO.getExchangeCount());
        refundResponseDTO.setRefundCount(inputDTO.getRefundCount());

        String posWhRecevingCheckFlagSP = nexsFacilityService.getSystemPreferenceValues("pos_whReceiving_flag", "refund_rules");
        Boolean posWhRecevingCheckFlag = posWhRecevingCheckFlagSP != null ? Boolean.valueOf(posWhRecevingCheckFlagSP) : Boolean.FALSE;

        if (posWhRecevingCheckFlag && DecisionTableRefundDTO.RETURN_SOURCE.POS.equalsIgnoreCase(inputDTO.getReturnInitiatedSource()) && "0".equalsIgnoreCase(String.valueOf(refundResponseDTO.getId()))) {
            refundResponseDTO.setRefundDispatch(TriggerPoint.WHReceiving.getName());
        }
        Integer returnEligibilityPeriod = getReturnEligibilityPeriodForProgressiveReturnPackageByUwItemId(inputDTO);
        if (returnEligibilityPeriod != 0) {
            refundResponseDTO.setReturnEligibilityPeriod(returnEligibilityPeriod+refundResponseDTO.getExtensionInReturnEligibilityPeriod());
        }
        log.info("setting return eligibility for hooper {} {} {}", refundResponseDTO, serviceRequest, gson.toJson(inputDTO));
        setReturnEligibilityPeriodForHooper(refundResponseDTO, serviceRequest, inputDTO);
    }

    public Integer getReturnEligibilityPeriodForProgressiveReturnPackageByUwItemId(ReturnRefundInputDTO inputDTO) {
        try {
            String lensPackage = inputDTO.getRrrServiceRequest().getUwOrder().getLensPackage();
            if (StringUtils.isNotBlank(lensPackage) && lensPackage.contains(PROG)) {
                log.info("overriding return eligibility period for progressive return package :" + inputDTO.getUwItemId());
                return returnEligibilityPeriodForProgressivePackage;
            }
        } catch (Exception e) {
            log.error("exception while updating for return eligibility period for progressive return package: " + e);
        }
        return 0;
    }

    public void setExchangeOrderDispatchPoint(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO) {
        if (refundResponseDTO.getExchangeOrderDispatch() == null || "NA".equalsIgnoreCase(refundResponseDTO.getExchangeOrderDispatch())) {
            if (DecisionTableRefundDTO.RETURN_SOURCE.POS.equalsIgnoreCase(inputDTO.getReturnInitiatedSource())) {
                refundResponseDTO.setExchangeOrderDispatch("POSReceiving");
                log.debug("[ReturnRefundEligibilityServiceImpl][setExchangeOrderDispatchPoint] Enter in Default rule 5.1 . Setting ExchangeOrderDispatch to POSReceiving ");
            } else {
                refundResponseDTO.setExchangeOrderDispatch("WHReceiving");
                log.debug("[ReturnRefundEligibilityServiceImpl][setExchangeOrderDispatchPoint] Enter in Default rule 5.2 . Setting ExchangeOrderDispatch to WHReceiving ");
            }
        }
    }

    public void unsetRefundabilityReturnabilityFlags(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO) {
        if (refundResponseDTO.getDoRefund() == null) {
            log.debug("[ReturnRefundEligibilityServiceImpl][unsetRefundabilityReturnabilityFlags] Enter in Default rule 2 . Setting the doRefund value to false for uwItemId : {}", inputDTO.getUwItemId());
            refundResponseDTO.setDoRefund(false);
        }
        if (refundResponseDTO.getIsReturnable() == null) {
            refundResponseDTO.setIsReturnable(false);
            log.debug("[ReturnRefundEligibilityServiceImpl][unsetRefundabilityReturnabilityFlags] Enter in Default rule 4 . Setting isReturnable to false for uwItemId : {} ", inputDTO.getUwItemId());
        }
    }

    public void setReturnRefundabilityBasedOnRto(ReturnRefundResponseDTO refundResponseDTO, RRRServiceRequest serviceRequest, ReturnRefundInputDTO inputDTO) {
        String refundRequestStatus = getRefundRequestStatus(inputDTO.getUwItemId());
        Boolean isCodItem = refundUtilsService.isPurelyCODItem(inputDTO);
        if ((inputDTO.getReverseType().equalsIgnoreCase("rto") && isCodItem) || Constant.REFUND_STATUS.PROCESSED.equalsIgnoreCase(refundRequestStatus)) {
            log.info("[ReturnRefundEligibilityServiceImpl][setReturnRefundabilityBasedOnRto] Setting doRefund false & isReturnable true as either RTO or status as PROCESSED in refundRequest for uwItemId: {}", inputDTO.getUwItemId());
            refundResponseDTO.setDoRefund(false);
            refundResponseDTO.setIsReturnable(true);
            refundResponseDTO.setRefundMethod("NA");
            refundResponseDTO.setAction("PUTAWAY_YES_GOOD_INVENTORY");
        }

        ReturnDetail returnOrder = returnOrderActionService.getReturnOrder(inputDTO.getUwItemId());

        boolean isRtoItem = false;
        if (null != returnOrder) {
            isRtoItem = Constant.RETURN_TYPE.RTO.equalsIgnoreCase(returnOrder.getReturnType()) || Constant.RETURN_TYPE.AWAITED_RTO.equalsIgnoreCase(returnOrder.getReturnType());
        }

        String returnOrderStatus = returnOrderActionService.getReturnOrderStatus(returnOrder);

        boolean isApprovalStatus = ReturnStatus.RETURN_NEED_APPROVAL.getStatus().equalsIgnoreCase(returnOrderStatus) || ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus().equalsIgnoreCase(returnOrderStatus);

        log.info("[ReturnRefundEligibilityServiceImpl][setReturnRefundabilityBasedOnRto] isRtoItem: {}, isCodItem: {}, isApprovalStatus: {} for uwItemId: {} ", isRtoItem, isCodItem, isApprovalStatus, inputDTO.getUwItemId());
        if ((isCodItem && isRtoItem) || isApprovalStatus) {
            refundResponseDTO.setRefundMethod("NA");
            refundResponseDTO.setDoRefund(false);
        } else if (isRtoItem) {
            refundResponseDTO.setExchange(true);
            refundResponseDTO.setRefundMethod("exchange");
        }

    }

    public String getRefundRequestStatus(Integer uwItemId) {
        try {
            return refundUtilsService.getRefundRequestStatus(uwItemId);
        } catch (Exception e) {
            log.error("[getRefundRequestStatus] caught exception for uwItemId: {}, e:", uwItemId, e);
            return null;
        }
    }

    public void setAction(ReturnRefundResponseDTO refundResponseDTO, UwOrderDTO uwOrderDTO) {
        if (refundResponseDTO.getAction() == null) {
            log.debug("[ReturnRefundEligibilityServiceImpl][setAction] Enter in Default rule 1 . Setting resolution action as Delight for uwItemId {}", uwOrderDTO.getUwItemId());
            refundResponseDTO.setAction("DELIGHT");
        }
    }

    public void setFraudStatus(ReturnRefundResponseDTO refundResponseDTO, RRRServiceRequest serviceRequest) {
        if (refundResponseDTO.isFraud()) {
            log.info("[ReturnRefundEligibilityServiceImpl][getDroolsRefundResponse] IsFraud flag is {} ", refundResponseDTO.isFraud());
            refundResponseDTO.setIsReturnable(false);
            refundResponseDTO.setErrorForNotReturnable(ITEM_IS_NOT_RETURNABLE_FOR_FRAUD_CUSTOMER);
            refundResponseDTO.setRefundMethod("NA");
            refundResponseDTO.setDoRefund(false);
            refundResponseDTO.setErrorForNotRefundable(ITEM_IS_NOT_REFUNDABLE_FOR_FRAUD_CUSTOMER);
            log.debug("[ReturnRefundEligibilityServiceImpl][getDroolsRefundResponse] IsFraud is true and isReturnable is " + refundResponseDTO.getIsReturnable() + " and doRefund is " + refundResponseDTO.getDoRefund() + " and  refundMethod is " + refundResponseDTO.getRefundMethod());
        }
    }

    public void setEligibilityBasedOnRefundAmount(ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO, RRRServiceRequest serviceRequest) {

        Integer uwItemId = inputDTO.getUwItemId();
        Integer incrementId = serviceRequest.getOrderInfoResponse().getOrdersHeader().getIncrementId();

        ReturnDetail returnOrder = returnOrderActionService.getReturnOrder(uwItemId);
        int returnId = null != returnOrder ? returnOrder.getId() : 0;

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = serviceRequest.getOrderInfoResponse().getItemWiseFastRefunResponseDTO();
        log.info("[setEligibilityBasedOnRefundAmount] itemWiseFastRefunResponseDTO : {}", itemWiseFastRefunResponseDTO);
        if(Objects.nonNull(itemWiseFastRefunResponseDTO) && !CollectionUtils.isEmpty(itemWiseFastRefunResponseDTO.getItemIds())){
            ItemWiseGetAmountDTO itemWiseGetAmount = itemWiseFastRefunResponseDTO.getItemIds().stream().filter(i -> i.getUwItemId().equals(uwItemId)).findFirst().orElse(null);
            ItemWiseGstAmountDTO itemWiseGstAmountDTO = null;
            if(!CollectionUtils.isEmpty(itemWiseFastRefunResponseDTO.getGstIds())){
                itemWiseGstAmountDTO = itemWiseFastRefunResponseDTO.getGstIds().stream().filter(i -> i.getUwItemId().equals(uwItemId)).findFirst().orElse(null);
            }
            if (itemWiseGetAmount != null) {
                double amountToRefund = itemWiseGetAmount.getActualAmount() - itemWiseGetAmount.getRefundAmount();
                double itemRefundedAmount = itemWiseGetAmount.getRefundAmount();
                log.info("[setEligibilityBasedOnRefundAmount] refunded amount for uwItemId {} itemRefundedAmount is {} itemPrice is {} and amount to refund is {}",uwItemId,itemWiseGetAmount.getRefundAmount(),itemWiseGetAmount.getActualAmount(),amountToRefund);
                refundResponseDTO.setAmountToRefund(amountToRefund);
                refundResponseDTO.setItemPrice(itemWiseGetAmount.getActualAmount());
                refundResponseDTO.setItemRefundedAmount(itemRefundedAmount);

                if (itemRefundedAmount > 0.0d) {


                    boolean isExchange = itemWiseGstAmountDTO != null
                            && itemWiseGstAmountDTO.getGstRefundedAmount() == itemRefundedAmount;

                    log.info("[setEligibilityBasedOnRefundAmount] isExchange :  {}", isExchange);

                    refundResponseDTO.setExchange(isExchange);

                    if (!isExchange) {
                        log.info("Setting Exchange flag as false as refund amount is greater than 0");
                        refundResponseDTO.setErrorForNotExchangeable(
                                Constant.RefundEligibilityError.ITEM_NOT_EXCHANGEABLE_REFUND_AMOUNT_GREATER_THAN_ZERO
                        );
                    }

                }

                double proRatedLkCashAmountToRefund = getProRatedLKCashAmountToRefund(inputDTO, returnId,
                        inputDTO.getRrrServiceRequest().getUwOrder().getItemId());
                double proRatedLkCashPlusAmountToRefund = getProRatedLKCashPlusAmountToRefund(inputDTO, returnId,
                        inputDTO.getRrrServiceRequest().getUwOrder().getItemId());
                if (amountToRefund < 0.0d && (!CollectionUtils.isEmpty(refundResponseDTO.getRefundMethods())
                        && refundResponseDTO.getRefundMethods().contains(Constant.REFUND_METHOD.CUSTOMER_WALLET)
                        && proRatedLkCashAmountToRefund < 0.0d
                        && proRatedLkCashPlusAmountToRefund < 0.0d)) {
                    log.info("amount/lkcash/lkcash+ to refund is less than 1.0d setting do refund and exhange as false");
                    refundResponseDTO.setExchange(false);
                    refundResponseDTO.setDoRefund(false);
                    refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.ITEM_NOT_EXCHANGEABLE_REFUND_LESS_THAN_ZERO);
                    refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_REFUND_LESS_THAN_ZERO);
                } else if (amountToRefund < 0.0d && (CollectionUtils.isEmpty(refundResponseDTO.getRefundMethods())
                        || !refundResponseDTO.getRefundMethods().contains(Constant.REFUND_METHOD.CUSTOMER_WALLET))) {
                    refundResponseDTO.setExchange(false);
                    refundResponseDTO.setDoRefund(false);
                    refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.ITEM_NOT_EXCHANGEABLE_REFUND_LESS_THAN_ZERO);
                    refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_REFUND_LESS_THAN_ZERO);
                } else {
                    refundResponseDTO.setLkcashToRefund(proRatedLkCashAmountToRefund);
                    refundResponseDTO.setLkcashPlusToRefund(proRatedLkCashPlusAmountToRefund);
                }


            }
        }


        String status = null;
        if (null != returnOrder) {
            status = returnOrderActionService.getReturnOrderStatus(returnOrder);
            if (ReturnStatus.RETURN_CLOSED.getStatus().equalsIgnoreCase(status) || ReturnStatus.RETURN_REJECTED.getStatus().equalsIgnoreCase(status)
                    || ReturnStatus.RETURN_RESHIP.getStatus().equalsIgnoreCase(status)
            ) {
                log.info("Setting refund and exchange as false as return cycle is closed with return status " + status);
                refundResponseDTO.setExchange(false);
                refundResponseDTO.setDoRefund(false);
                refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.ITEM_NOT_EXCHANGEABLE_RETURN_CYCLE_CLOSED+status);
                refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_RETURN_CYCLE_CLOSED+status);
            }
        }

        if (isGVOnlyOrder(inputDTO)) {
            log.info("[populateRefundDetailsV2] Its GV Only order. setting exchange as true.  uwItemId: {}",uwItemId);
            refundResponseDTO.setExchange(true);
            refundResponseDTO.setDoRefund(false);
            refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_GIFT_VOUCHER);

            if (checkRtoShipment(returnOrder)) {
                log.info("[populateRefundDetailsV2] Its GV Only order and RTO shipment. uwItemId: {}",uwItemId);
                refundResponseDTO.setExchange(false);
                refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.ITEM_NOT_EXCHANGEABLE_ON_RTO_SHIPMENTS);
            }

        }

        if (getIsParentOrderAlreadyRefunded(inputDTO)) {
            log.info("Setting refund and exchange as false as parent orders are already refunded , uwItemId : " + uwItemId + ", returnId : " + returnId);
            refundResponseDTO.setExchange(false);
            refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.NOT_EXCHANGEABLE_PARENT_ALREADY_REFUNDED);

        } else if(nthExchange(incrementId, uwItemId, inputDTO, status)){
            log.info("[populateRefundDetailsV2] Setting exchange as true for nth exchange , uwItemId : {} , returnId : {}, incrementId: {}, source : {}",uwItemId, returnId, incrementId, inputDTO.getReturnInitiatedSource());
            refundResponseDTO.setIsReturnable(true);
            refundResponseDTO.setExchange(true);
            refundResponseDTO.setDoRefund(true);
        }

    }

    private boolean nthExchange(int incrementId, Integer uwItemId, ReturnRefundInputDTO returnRefundInputDTO, String status) {
        log.info("[nthExchange] incrementId : {} , uwItemId : {}", incrementId, uwItemId);
        try {
            String returnSource = returnRefundInputDTO.getReturnInitiatedSource();
            ThresholdCheckResponse fraudThresholdReached = returnFlowResponseService.isFraudThresholdReached(incrementId, Collections.singletonList(uwItemId), returnRefundInputDTO.getTriggerPoint());
            boolean thresholdReachedFlag = fraudThresholdReached.isResult();
            UwOrderDTO uwOrder = returnRefundInputDTO.getRrrServiceRequest().getUwOrder();
            boolean isShipmentStateClosed = false;
            if ("closed".equalsIgnoreCase(uwOrder.getShipmentState())) {
                if (StringUtils.isNotEmpty(status)) {
                    List<String> cancelledStatuses = Arrays.asList("cancelled", "customer_cancelled");
                    if (!cancelledStatuses.contains(status)) {
                        isShipmentStateClosed = true;
                    }
                } else {
                    isShipmentStateClosed = true;
                }
            }
            log.info("[nthExchange] incrementId : {} , returnSource : {}, thresholdReachedFlag : {}, isShipmentStateClosed : {}", incrementId, returnSource, thresholdReachedFlag, isShipmentStateClosed);
            boolean isNthExchange = !isShipmentStateClosed && "vsm".equalsIgnoreCase(returnSource) && thresholdReachedFlag;
            log.info("[nthExchange] incrementId : {} : isNthExchange : {}", incrementId, isNthExchange);
            return isNthExchange;
        } catch (Exception e) {
            log.error("[nthExchange] incrementId : {} : error : {}", incrementId, e.getMessage(), e);
        }
        return false;
    }

    private boolean checkRtoShipment(ReturnDetail returnOrder) {
        if (returnOrder == null || StringUtils.isBlank(returnOrder.getReturnType())) {
            return false;
        }

        String returnType = returnOrder.getReturnType();
        String returnOrderStatus = returnOrderActionService.getReturnOrderStatusById(returnOrder.getId());

        return (returnType.equalsIgnoreCase("rto") || returnType.equalsIgnoreCase("awaited_rto"))
                && (returnOrderStatus.equalsIgnoreCase(ReturnStatus.RETURN_UNDER_FOLLOWUP.getStatus())
                || returnOrderStatus.equalsIgnoreCase(ReturnStatus.RETURN_PENDING_APPROVAL.getStatus()));
    }


    public boolean getIsParentOrderAlreadyRefunded(ReturnRefundInputDTO inputDTO) {
        log.info("[isParentOrderAlreadyRefunded] uwItemId : " + inputDTO.getUwItemId() + " ,returnId " + inputDTO.getReturnId());
        boolean isParentOrderAlreadyRefunded = false;
        MasterOrderDetailsDTO exchangeOrders = getExchangeOrders(inputDTO);
        if (exchangeOrders != null && exchangeOrders.getItemWiseAmount() != null) {
            Long exchangeOrdersUwItemId = exchangeOrders.getUwItemId();
            Double totalPriceOfOrder = exchangeOrders.getItemWiseAmount().getTotalPriceOfOrder();
            Double exchangePgAmount = exchangeOrders.getItemWiseAmount().getExchangePgAmount();

            CheckRefundInitiatedRequest checkRefundInitiatedRequest = new CheckRefundInitiatedRequest();
            checkRefundInitiatedRequest.setIdentifierType(IdentifierType.UW_ITEM_ID);
            checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(exchangeOrdersUwItemId));
            Map<String, Object> headers = authorizationService.addRefundAuthorizationHeaders(checkRefundInitiatedRequest, new HashMap<>());
            ResponseEntity<CheckRefundInitiatedResponse> checkRefundInitiatedResponseEntity = refundFeignClient.checkRefundInitiated(checkRefundInitiatedRequest, headers);
            CheckRefundInitiatedResponse checkRefundInitiatedResponse = checkRefundInitiatedResponseEntity.getBody();
            if (checkRefundInitiatedResponse != null) {
                double refundAmount = !CollectionUtils.isEmpty(checkRefundInitiatedResponse.getRefundDetailsDTOList()) ? checkRefundInitiatedResponse.getRefundDetailsDTOList().get(0).getRefundAmount() : 0d;
                double totalPrice = totalPriceOfOrder + exchangePgAmount;
                log.info("[isParentOrderAlreadyRefunded] uwItemId : {} , returnId : {} , totalPrice : {} , refundAmount : {}", inputDTO.getUwItemId(), inputDTO.getReturnId(), totalPrice, refundAmount);
                if (totalPrice != 0 && (totalPrice - refundAmount) <= 0) {
                    isParentOrderAlreadyRefunded = true;
                }
            }
            MasterOrderDetailsDTO masterOrderDetails = null;
            while (exchangeOrders.getMasterOrderDetails() != null) {
                masterOrderDetails = exchangeOrders.getMasterOrderDetails();
                exchangeOrders = masterOrderDetails;
            }
            if (!isParentOrderAlreadyRefunded && masterOrderDetails != null) {
                Long masterOrdersUwItemId = exchangeOrders.getUwItemId();
                Double masterOrderPrice = exchangeOrders.getItemWiseAmount().getTotalPriceOfOrder();
                Double masterOrderPgAmount = exchangeOrders.getItemWiseAmount().getExchangePgAmount();

                checkRefundInitiatedRequest = new CheckRefundInitiatedRequest();
                checkRefundInitiatedRequest.setIdentifierType(IdentifierType.UW_ITEM_ID);
                checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(masterOrdersUwItemId));

                headers = authorizationService.addRefundAuthorizationHeaders(checkRefundInitiatedRequest, new HashMap<>());
                checkRefundInitiatedResponseEntity = refundFeignClient.checkRefundInitiated(checkRefundInitiatedRequest, headers);
                checkRefundInitiatedResponse = checkRefundInitiatedResponseEntity.getBody();
                double refundAmount = checkRefundInitiatedResponse != null && !CollectionUtils.isEmpty(checkRefundInitiatedResponse.getRefundDetailsDTOList()) ? checkRefundInitiatedResponse.getRefundDetailsDTOList().get(0).getRefundAmount() : 0d;
                double totalPrice = masterOrderPrice + masterOrderPgAmount;
                log.info("[isParentOrderAlreadyRefunded] master uwItemId : {} , returnId : {} , totalPrice : {} , refundAmount : {}", inputDTO.getUwItemId(), inputDTO.getReturnId(), totalPrice, refundAmount);
                if (totalPrice != 0 && (totalPrice - refundAmount) <= 0) {
                    isParentOrderAlreadyRefunded = true;
                }
            }
        }
        log.info("[isParentOrderAlreadyRefunded] uwItemId : {} , isParentOrderAlreadyRefunded : {}", inputDTO.getUwItemId(), isParentOrderAlreadyRefunded);
        return isParentOrderAlreadyRefunded;
    }

    public MasterOrderDetailsDTO getExchangeOrders(ReturnRefundInputDTO inputDTO) {
        ResponseEntity<MasterOrderDetailsDTO> masterOrderDetails = orderOpsFeignClient.getMasterOrderDetails((long) inputDTO.getUwItemId());
        return masterOrderDetails.getBody();
    }

    public double getProRatedLKCashPlusAmountToRefund(ReturnRefundInputDTO inputDTO, Integer returnId, Integer itemId) {
        double lkCashPlusAmountToRefund = 0.0d;
        List<ItemWisePriceDetailsDTO> itemWisePriceDetailsDTOs = inputDTO.getRrrServiceRequest()
                .getOrderInfoResponse()
                .getItemWisePrices();
        ItemWisePriceDetailsDTO itemWisePriceDetails = itemWisePriceDetailsDTOs
                .stream()
                .filter(itemWisePrices -> Objects.equals(itemWisePrices.getItemId(), itemId))
                .findFirst()
                .orElse(null);


        if (null != itemWisePriceDetails && 0.0d != itemWisePriceDetails.getLenskartPlusDiscount()) {
            if (0 == returnId) {
                double lkCashPlusTotalAmount = getProratedLKCash(inputDTO, LK_CASH_PLUS);
                long itemsCountByLKCashPlus = itemWisePriceDetailsDTOs
                        .stream()
                        .filter(itemWisePrices -> itemWisePrices.getLenskartPlusDiscount() > 0.0d)
                        .count();
                double lkCashProratedItemWise = 0.0d;
                log.info("[getProRatedLKCashAmountToRefund] returnId : {}, itemsCountByLKCashPlus : {}", returnId, itemsCountByLKCashPlus);
                if(itemsCountByLKCashPlus > 0L){
                    lkCashProratedItemWise = itemWisePriceDetails.getLenskartPlusDiscount()
                            - lkCashPlusTotalAmount / itemsCountByLKCashPlus;
                }
                lkCashPlusAmountToRefund = Math.max(lkCashProratedItemWise, 0.0d);
            } else {
                lkCashPlusAmountToRefund = getLKCashForNonFastRefund(inputDTO, returnId, LK_CASH_PLUS);
            }
        } else {
            log.info("Either item wise prices or refund wallet details are not found for uw_item_id: {}",
                    inputDTO.getUwItemId());
        }
        log.info("[getProRatedLKCashAmountToRefund] returnId : {}, lkCashPlusAmountToRefund : {}", returnId, lkCashPlusAmountToRefund);
        return lkCashPlusAmountToRefund;
    }

    public double getProRatedLKCashAmountToRefund(ReturnRefundInputDTO inputDTO, Integer returnId, Integer itemId) {
        double lkCashAmountToRefund = 0.0d;
        List<ItemWisePriceDetailsDTO> itemWisePriceDetailsDTOs = inputDTO.getRrrServiceRequest().getOrderInfoResponse().getItemWisePrices();
        ItemWisePriceDetailsDTO itemWisePriceDetails = itemWisePriceDetailsDTOs
                .stream()
                .filter(itemWisePrices -> Objects.equals(itemWisePrices.getItemId(), itemId))
                .findFirst()
                .orElse(null);
        if (null != itemWisePriceDetails && 0.0d != itemWisePriceDetails.getLenskartDiscount()) {
            if (0 == returnId) {
                double lkCashTotalAmount = getProratedLKCash(inputDTO, LK_CASH);
                long itemsCountByLKCash = itemWisePriceDetailsDTOs
                        .stream()
                        .filter(itemWisePrices -> itemWisePrices.getLenskartDiscount() > 0.0d)
                        .count();
                log.info("[getProRatedLKCashAmountToRefund] returnId : {}, itemsCountByLKCash : {}", returnId, itemsCountByLKCash);
                double lkCashProratedItemWise = 0.0d;
                if(itemsCountByLKCash > 0L){
                    lkCashProratedItemWise = itemWisePriceDetails.getLenskartDiscount()
                            - lkCashTotalAmount / itemsCountByLKCash;
                }

                lkCashAmountToRefund = Math.max(lkCashProratedItemWise, 0.0d);
            } else {
                lkCashAmountToRefund = getLKCashForNonFastRefund(inputDTO, returnId, LK_CASH);
            }
        } else {
            log.info("Either item wise prices or refund wallet details are not found for uw_item_id: {}",
                    inputDTO.getUwItemId());
        }
        log.info("[getProRatedLKCashAmountToRefund] returnId : {}, lkCashAmountToRefund : {}", returnId, lkCashAmountToRefund);
        return lkCashAmountToRefund;
    }


    public double getProratedLKCash(ReturnRefundInputDTO inputDTO, String type) {
        GetRefundAmountResponse refundAmountResponse = inputDTO.getRrrServiceRequest().getRefundAmountResponse();
        double lkCashTotalAmount = refundAmountResponse.getRefundDetailsDTO().getLenskartRefundedAmount() != null ? refundAmountResponse.getRefundDetailsDTO().getLenskartRefundedAmount().getAmount().doubleValue() : 0.0d;
        double lkCashPlusTotalAmount = refundAmountResponse.getRefundDetailsDTO().getLenskartPlusRefundedAmount() != null ? refundAmountResponse.getRefundDetailsDTO().getLenskartPlusRefundedAmount().getAmount().doubleValue() : 0.0d;
        return LK_CASH.equalsIgnoreCase(type) ? lkCashTotalAmount : lkCashPlusTotalAmount;
    }

    public double getLKCashForNonFastRefund(ReturnRefundInputDTO inputDTO, Integer returnId,
                                            String type) {
        double lkCashAmountToRefund = 0.0d;
        Map<Long, RefundAmount> lkCashItemWiseRefundedAmountMap = inputDTO.getRrrServiceRequest().getRefundAmountResponse().getRefundDetailsDTO().getLkCashItemWiseRefundedAmountMap();
        Map<Long, RefundAmount> lkCashPlusItemWiseRefundedAmountMap = inputDTO.getRrrServiceRequest().getRefundAmountResponse().getRefundDetailsDTO().getLkCashPlusItemWiseRefundedAmountMap();


        RefundAmount lkCashRefAmount = lkCashItemWiseRefundedAmountMap.get(inputDTO.getUwItemId());
        RefundAmount lkCashPlusRefAmount = lkCashPlusItemWiseRefundedAmountMap.get(inputDTO.getUwItemId());

        ItemWisePriceDetailsDTO itemWisePriceDetailsDTO = inputDTO.getRrrServiceRequest().getOrderInfoResponse()
                .getItemWisePrices().stream()
                .filter(item -> Objects.equals(inputDTO.getRrrServiceRequest().getUwOrder().getItemId(), item.getItemId()))
                .findFirst().orElse(null);

        if (itemWisePriceDetailsDTO == null) {
            return lkCashAmountToRefund;
        }

        double lkCashTotal = LK_CASH.equalsIgnoreCase(type) && lkCashRefAmount != null && lkCashRefAmount.getAmount() != null ? lkCashRefAmount.getAmount().doubleValue()
                :  (lkCashPlusRefAmount != null && lkCashPlusRefAmount.getAmount() != null ? lkCashPlusRefAmount.getAmount().doubleValue() : 0.0d);
        Double lkCashDiscount = LK_CASH.equalsIgnoreCase(type) ? itemWisePriceDetailsDTO.getLenskartDiscount()
                : itemWisePriceDetailsDTO.getLenskartPlusDiscount();
        lkCashAmountToRefund = lkCashDiscount != null ? (lkCashDiscount - lkCashTotal) : 0.0d;
        return lkCashAmountToRefund;
    }

    public boolean isGVOnlyOrder(ReturnRefundInputDTO inputDTO) {
        return inputDTO.getRrrServiceRequest()
                .getOrderInfoResponse()
                .getOrders()
                .stream()
                .anyMatch(order -> PAYMENT_METHOD.giftvoucher.equalsIgnoreCase(order.getMethod()));
    }

    public void setDraftStatusAndRefundMethodCaptured(UwOrderDTO uwOrder, ReturnRefundResponseDTO refundResponseDTO) {
        returnOrderActionService.setDraftStatusAndRefundMethodCaptured(uwOrder, refundResponseDTO);
    }

    public void setReturnRefundabilityBasedOnExchangeDone(UwOrderDTO uwOrder, OrderExchangeCancellationDetails orderExchangeCancellationDetails, ReturnRefundResponseDTO refundResponseDTO) {
        returnOrderActionService.setReturnRefundabilityBasedOnExchangeDone(uwOrder, orderExchangeCancellationDetails, refundResponseDTO);
    }

    public void populateUwOrderInRRRServiceRequest(RRRServiceRequest serviceRequest) {
        populateUwOrder(serviceRequest); //UwOrder of Franchise to Customer in case of B2B

        checkB2BAndPopulateUwOrderWH(serviceRequest);
    }


    public ReturnRefundResponseDTO fetchReturnRefundRule(ReturnRefundInputDTO inputDTO) {
        return returnRefundRuleService.fetchReturnRefundRule(inputDTO);
    }

    public Map<Integer, ReturnPolicyResponse> fetchReturnPolicyInBulk(List<ReturnPolicyInputDTO> inputDTO) {
        return returnRefundRuleService.fetchReturnPolicy(inputDTO);
    }


    public void setApprovalNeededFlag(ReturnRefundResponseDTO returnRefundResponseDTO, ReturnRefundInputDTO inputDTO, Date deliveredDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String currentDateStr = sdf.format(new Date());
        String returnEligibleDateStr = returnRefundResponseDTO.getReturnEligibleTillDate();
        boolean isReturnPeriodActive = false;
        boolean isCurrentStatusNeedApproval = false;
        boolean isPriorityOrder = false;
        try {
            log.info("[ReturnRefundEligibilityServiceImpl][setIsApprovalNeeded] returnEligibleDateStr:{}, currentDateStr:{}", returnEligibleDateStr, currentDateStr);
            if (returnEligibleDateStr != null && !sdf.parse(currentDateStr).after(sdf.parse(returnEligibleDateStr))) {
                isReturnPeriodActive = true;
            }
            ReturnDetail returnOrder = returnOrderActionService.getReturnOrderBasedOnUwItemIdAndStatus(inputDTO.getUwItemId());
            String status = returnOrderActionService.getReturnOrderStatus(returnOrder);
            if(returnOrder != null){
                isCurrentStatusNeedApproval = Arrays.asList("return_need_approval_from_whse","return_need_approval").contains(status);
                List<String> invalidStatuses = Arrays.asList("customer_cancelled","cancelled");
                DelightAction delightAction = delightActionRepository.findTop1ByReturnIdOrderByIdDesc(returnOrder.getId());
                log.info("[ReturnRefundEligibilityServiceImpl][setIsApprovalNeeded] returnId : {}, status : {}, invalidStatuses : {}", returnOrder.getId(), status, invalidStatuses);
                if(delightAction != null && "APPROVE".equalsIgnoreCase(delightAction.getDelightAction()) && !invalidStatuses.contains(status)) {
                    returnRefundResponseDTO.setNeedApprovalMessage("Request already Approved by the CS team");
                    returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.ACTIVE_RETURN_ALREADY_APPROVED_BY_CS);
                    log.info("[ReturnRefundEligibilityServiceImpl][setIsApprovalNeeded] returnId : {} - Request already Approved by the CS team", returnOrder.getId());
                    return;//returning for those returns which has already been approved.
                }

                if (delightAction == null && isCurrentStatusNeedApproval) {
                    returnRefundResponseDTO.setNeedApprovalMessage("Request already submitted and under review by the CS team");
                    returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.ACTIVE_RETURN_WAITING_FOR_APPROVAL);
                    log.info("[ReturnRefundEligibilityServiceImpl][setIsApprovalNeeded] returnId : {} - Request already submitted and under review by the CS tea", returnOrder.getId());
                    return;
                }
            }
            isPriorityOrder = isPrioritizedOrder(inputDTO);
        } catch (ParseException e) {
            log.error("[ReturnRefundEligibilityServiceImpl][setIsApprovalNeeded] Error in parsing Dates, e: {}", e.getMessage());
        }
        log.info("[ReturnRefundEligibilityServiceImpl][setIsApprovalNeeded] isReturnPeriodActive : {}, isWarrantyActive : {}, isCurrentStatusNeedApproval : {}, isPriorityOrder : {}", isReturnPeriodActive, returnRefundResponseDTO.isWarrantyActive(), isCurrentStatusNeedApproval, isPriorityOrder);
        boolean isApprovalNeeded = !isReturnPeriodActive && returnRefundResponseDTO.isWarrantyActive() && !isCurrentStatusNeedApproval && !isPriorityOrder;
        returnRefundResponseDTO.setApprovalNeeded(isApprovalNeeded);
        log.info("[ReturnRefundEligibilityServiceImpl][setIsApprovalNeeded] Returning with isApprovalNeeded:{}", isApprovalNeeded);

        boolean disableRefund = false;
        try {
            if (inputDTO.getReturnId() != null) {
                String returnOrderStatus = returnDetailsAction.getReturnOrderStatusById(inputDTO.getReturnId());
                SystemPreference systemPreference = nexsFacilityService.findAllByKey(Constant.SYSTEM_PREFERENCE_KEYS.REFUND_ON_APPROVAL);
                if (systemPreference != null && systemPreference.getValue() != null) {
                    disableRefund = systemPreference.getValue().equalsIgnoreCase("1");
                }
                if (disableRefund && StringUtils.isNotBlank(returnOrderStatus) && inputDTO.getReturnInitiatedSource().equalsIgnoreCase(Constant.RETURN_SOURCE.ONLINE_WEB) && returnOrderStatus.equalsIgnoreCase(ReturnStatus.RETURN_ACCEPTED.getStatus())) {
                    log.info("[populateNeedApprovalRefundDetails] setting doRefund to false");
                    returnRefundResponseDTO.setDoRefund(false);
                    returnRefundResponseDTO.setErrorForNotRefundable(DISABLED_WEB_REFUND_FOR_NEED_APPROVAL);
                    returnRefundResponseDTO.setExchange(false);
                    returnRefundResponseDTO.setErrorForNotExchangeable(DISABLED_WEB_RETURN_FOR_NEED_APPROVAL);
                }
            }
        } catch (Exception e) {
            log.error("[populateNeedApprovalRefundDetails] exception {} {}", e.getMessage(), e);
        }

        String systemPreference = nexsFacilityService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.IS_NEED_APPROVAL_AUTO_WARRANTY_ENABLE, Constant.SYSTEM_PREFERENCE_GROUPS.NEED_APPROVAL);
        log.info("[getReturnRefundEligibility] deliveredDate is :" + deliveredDate);
        if (Objects.nonNull(systemPreference) && Boolean.parseBoolean(systemPreference) && needApprovalSources.stream().anyMatch(source -> source.equalsIgnoreCase(inputDTO.getReturnInitiatedSource())) && null != deliveredDate) {
            boolean isNeedApproval = getNeedApprovalFlag(returnRefundResponseDTO, deliveredDate, inputDTO);
            log.info("[getReturnRefundEligibility] isNeedApproval : {} , uwItemId :{}", isNeedApproval, inputDTO.getUwItemId());
            returnRefundResponseDTO.setApprovalNeeded(isNeedApproval);
        }

    }

    private boolean isPrioritizedOrder(ReturnRefundInputDTO inputDTO){
        String orderPriorityType = orderUtilsService.getOrderPriorityType(inputDTO.getItemId(), inputDTO.getRrrServiceRequest().getUwOrder().getIncrementId());
        log.info("[isPrioritizedOrder] itemId : {}, orderPriorityType : {}" ,  inputDTO.getItemId(), orderPriorityType );
        return orderUtilsService.isPrioritizedOrder(orderPriorityType);
    }

    private void blockFlagsBasedOnSharkTankOffers(ReturnRefundResponseDTO refundResponseDTO, UwOrderDTO uwOrder) {
        log.info("[blockFlagsBasedOnSharkTankOffers] order : {}, uwItem : {}, itemId : {}", uwOrder.getIncrementId(), uwOrder.getUwItemId(), uwOrder.getItemId() );
        String orderPriorityType = orderUtilsService.getOrderPriorityType(uwOrder.getItemId(), uwOrder.getIncrementId());
        log.info("[blockFlagsBasedOnSharkTankOffers] itemId : {}, orderPriorityType : {}" ,  uwOrder.getItemId(), orderPriorityType );
        if(orderUtilsService.isPrioritizedOrder(orderPriorityType)){
            refundResponseDTO.setIsReturnable(false);
            refundResponseDTO.setExchange(false);
            refundResponseDTO.setDoRefund(false);
            refundResponseDTO.setRefundMethods(Collections.singletonList("NA"));
            refundResponseDTO.setErrorForNotReturnable("order not returnable because it is of type : "+orderPriorityType);
            refundResponseDTO.setErrorForNotExchangeable("order not returnable because it is of type : "+orderPriorityType);
            refundResponseDTO.setErrorForNotRefundable("order not refundable because it is of type : "+ orderPriorityTypes);
        }
    }

    static String cacheKey = Constant.SYSTEM_PREFERENCE_KEYS.PRIVATE_BRANDS;

    public boolean getNeedApprovalFlag(ReturnRefundResponseDTO returnRefundResponseDTO, Date deliveredDate, ReturnRefundInputDTO inputDTO) {
        boolean isNeedApproval = false;
        String privateBrandList = null;
        boolean isPriorityOrder = false;
        try {

            log.debug("[ExchangeRefundMethodServiceImpl][setRefundFields] Data from DB :  " + cacheKey);
            SystemPreference privateBrand = nexsFacilityService.findAllByKey(cacheKey);
            if (null != privateBrand) {
                log.info("[ExchangeRefundMethodServiceImpl][setRefundFields] Data from DB and Saving cacheData {} ", privateBrand);
                privateBrandList = privateBrand.getValue();
            }
            Integer timeDiffInDays = 0;
            Boolean isPrivateBrand = isPrivateBrandsCheck(privateBrandList, inputDTO.getRrrServiceRequest().getUwOrder().getBrand());
            String systemPreference = isPrivateBrand ? nexsFacilityService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.NON_BRANDED, Constant.SYSTEM_PREFERENCE_GROUPS.NEED_APPROVAL) : nexsFacilityService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.BRANDED_PRODUCT, Constant.SYSTEM_PREFERENCE_GROUPS.NEED_APPROVAL);
            timeDiffInDays = getTimeDiffInDays(deliveredDate);
            //todo remove after testing
            log.info("timeDiffInDays: {} and deliveredDate is: {}", timeDiffInDays, deliveredDate);
            isNeedApproval = timeDiffInDays.compareTo(Integer.valueOf(systemPreference)) < 0 && !(returnRefundResponseDTO.getDoRefund() && returnRefundResponseDTO.isExchange());
            isPriorityOrder = isPrioritizedOrder(inputDTO);
        } catch (Exception e) {
            log.error("issue while getting needApproval flag :" + inputDTO.getUwItemId() + " " + e);
        }
        return isNeedApproval && !isPriorityOrder;
    }

    public Integer getTimeDiffInDays(Date deliveredDate) {
        LocalDate currDate = LocalDate.now();
        return Math.toIntExact(ChronoUnit.DAYS.between(deliveredDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), currDate));
    }

    public boolean isPrivateBrandsCheck(String privateBrandArrayList, String productBrand) {
        List<String> privateBrandList = new ArrayList<String>();
        log.debug("[ExchangeRefundMethodServiceImpl][getPrivateBrands] inside getPrivateBrands for {} <<<<<<<<with brand>>>>>> {}", privateBrandArrayList, productBrand);
        if (StringUtils.isNotEmpty(privateBrandArrayList)) {
            log.debug("[ExchangeRefundMethodServiceImpl][getPrivateBrands] privateBrandCache is true.");
            String[] str = privateBrandArrayList.split(",");
            for (String brand : str) {
                if (StringUtils.isNotBlank(brand) && StringUtils.isNotBlank(productBrand) && brand.trim().equalsIgnoreCase(productBrand.trim())) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean getIsReturnable(UwOrderDTO uwOrder, UwOrderDTO uwOrderWH, ReturnRefundResponseDTO refundResponseDTO, ReturnRefundEligibilityRequestDTO request, OrderInfoResponseDTO orderInfoDTO, List<ShippingStatusDetail> shippingStatuses, ReturnRefundInputDTO inputDTO) {
        boolean isReturnable = isItemReturnable(uwOrder, uwOrderWH, refundResponseDTO, request, false, shippingStatuses,orderInfoDTO);
        boolean isReturnableWH = uwOrderWH != null && isItemReturnable(uwOrder, uwOrderWH, refundResponseDTO, request, true, shippingStatuses, orderInfoDTO);


//        if (uwOrder != null && !isReturnable && StringUtils.isNotBlank(request.getSource()))
//            isReturnable = isReturnAllowedForPOS(request, refundResponseDTO.getIsReturnable(), uwOrder, orderInfoDTO);
//        if (uwOrderWH != null && !isReturnableWH && StringUtils.isNotBlank(request.getSource()))
//            isReturnableWH = isReturnAllowedForPOS(request, refundResponseDTO.getIsReturnable(), uwOrderWH, orderInfoDTO);

        boolean isCompleteOrClosedOrDelivered = uwOrderWH != null ? isItemCompleteOrCloseOrDelivered(uwOrder, uwOrderWH, true, shippingStatuses) : isItemCompleteOrCloseOrDelivered(uwOrder, uwOrderWH, false, shippingStatuses);


        if (isCompleteOrClosedOrDelivered) {
            ShippingDeliveredCompleted shippingStatus = fetchReturnEligibleTilDate(uwOrder, uwOrderWH, refundResponseDTO.getReturnEligibilityPeriod(), shippingStatuses, request, inputDTO);
            refundResponseDTO.setReturnEligibleTillDate(shippingStatus.getReturnEligiblePeriodDate());

            boolean isCODOrder = orderInfoDTO.getOrders().stream().anyMatch(o -> o.getMethod() != null && o.getMethod().equalsIgnoreCase("cod") || o.getMethod().equalsIgnoreCase("cashondelivery"));
            if (isCODOrder && ("web".equalsIgnoreCase(request.getSource()) || "vsm".equalsIgnoreCase(request.getSource())) && !shippingStatus.isOrderDelivered()) {
                refundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.THE_ORDER_IS_COD);
                if (!shippingStatus.isOrderDelivered()) {
                    refundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.THE_ORDER_IS_NOT_YET_MARKED_DELIVERED);
                }
                isReturnable = false;
                isReturnableWH = false;
            }
        }


        if (uwOrderWH != null && isReturnableWH && !isReturnable) {
            //TODO:: run asynch call to create UnprocessedReturnOrders
//            createEntryInUnprocessedReturnOrders(uwOrder);
        }


        boolean returnable = uwOrderWH != null ? isReturnableWH : isReturnable;
        log.info("[getIsReturnable] returnable : {}", returnable);
        return returnable;
    }

    public boolean isReturnAllowedForPOSOrVSM(ReturnRefundEligibilityRequestDTO request, boolean isReturnableFromRules, UwOrderDTO uwOrder,
                                              OrderInfoResponseDTO orderInfoDTO) {

        String nonReturnPaymentMethods = systemPreferenceService.getSystemPreferenceValues("PaymentMethods", "non_allowed_return_in_processing_for_payment");
        log.info("[isReturnAllowedForPOSOrVSM] System preference value for nonReturnPaymentMethods is {}", nonReturnPaymentMethods);
        if (StringUtils.isEmpty(nonReturnPaymentMethods)) {
            nonReturnPaymentMethods = "cashondelivery,cod";
        }

        List<String> paymentMethodFromSystem = Arrays.asList(nonReturnPaymentMethods.split(","));

        log.info("[isReturnAllowedForPOSOrVSM] Inside return eligibility check for POS, uwItemId: {}", uwOrder.getUwItemId());
        List<String> allowedSources = List.of("POS", "VSM");
        if (allowedSources.contains(request.getSource().toUpperCase()) && isReturnableFromRules) {

            Optional<OrdersDTO> ordersDTO = orderInfoDTO.getOrders().stream()
                    .filter(order -> uwOrder.getItemId() == order.getItemId())
                    .findFirst();

            String paymentMethod = ordersDTO.map(OrdersDTO::getMethod).orElse(null);

            if (paymentMethod != null && !paymentMethod.isEmpty() && paymentMethodFromSystem.contains(paymentMethod)) {
                log.info("[isReturnAllowedForPOSOrVSM] Non-allowed payment method detected for uwItemId: {}", uwOrder.getUwItemId());
                return false;
            }

            boolean isShipmentInProcessing = Status.PROCESSING.getValue().equalsIgnoreCase(uwOrder.getShipmentState())
                    && Status.PROCESSING.getValue().equals(uwOrder.getShipmentStatus());

            if (orderInfoDTO.getOrdersHeader().getPaymentCaptureFlag() == 1 && isShipmentInProcessing) {
                log.info("[isReturnAllowedForPOSOrVSM] Checking shipment status in WMS API for shippingPackageId: {}", uwOrder.getShippingPackageId());
                String shipmentStatus = getWMSStatusByShipmentId(uwOrder.getIncrementId(), uwOrder.getShippingPackageId());

                if (Objects.nonNull(shipmentStatus)) {
                    log.info("[isReturnAllowedForPOSOrVSM] Retrieved shipment status: {}", shipmentStatus);
                    if (allowedShippmentStatusForPOSReturn.contains(shipmentStatus)) {
                        log.info("[isReturnAllowedForPOSOrVSM] Shipment status is allowed for POS return.");
                        return true;
                    } else {
                        log.info("[isReturnAllowedForPOSOrVSM] Shipment status is not allowed for POS return.");
                    }
                } else {
                    log.info("[isReturnAllowedForPOSOrVSM] Shipment status is null for shipmentPackageId: {}", uwOrder.getShippingPackageId());
                }
            } else {
                log.info("[isReturnAllowedForPOSOrVSM] Either payment is not captured or shipment is not in processing.");
            }
        } else {
            log.info("[isReturnAllowedForPOSOrVSM] Not a POS order or rule does not allow return.");
        }

        return false;
    }


    @Override
    public ShippingDeliveredCompleted fetchReturnEligibleTilDate(UwOrderDTO uwOrder, UwOrderDTO uwOrderWH, int returnEligibilityPeriod, List<ShippingStatusDetail> allShippingStatuses, ReturnRefundEligibilityRequestDTO returnRefundEligibilityRequestDTO, ReturnRefundInputDTO inputDTO) {
        Date time = null;
        String unicomOrderCode;
        UwOrderDTO uwOrderNew = null;
        Calendar c = Calendar.getInstance();
        Date zeroTime = new Date(0000, 00, 00, 0, 0, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<ShippingStatusDetail> shippingStatuses = null;
        boolean deliveredDateAvailable = true;

        if (uwOrderWH != null) {
            unicomOrderCode = uwOrderWH.getUnicomOrderCode();
        } else {
            unicomOrderCode = uwOrder.getUnicomOrderCode();
        }

        ShippingDeliveredCompleted shippingDeliveredCompleted = new ShippingDeliveredCompleted();
        log.info(("fetchReturnEligibleTilDate: Order is B2B. Unicom order code " + unicomOrderCode + " uw item id is :: " + uwOrder.getUwItemId()));
        if (null != allShippingStatuses && !allShippingStatuses.isEmpty()) {
            shippingStatuses = allShippingStatuses.stream().filter(s -> unicomOrderCode.equals(s.getUnicomOrderCode()) && s.getOrderNo().equals(uwOrder.getIncrementId())).toList();
            if (CollectionUtils.isEmpty(shippingStatuses) && uwOrderWH != null) {
                shippingStatuses = allShippingStatuses.stream().filter(s -> uwOrder.getUnicomOrderCode().equals(s.getUnicomOrderCode()) && s.getOrderNo().equals(uwOrder.getIncrementId())).toList();
            }
            for (ShippingStatusDetail shippingStatus : shippingStatuses) {
                if (shippingStatus.getComplete_time() != null && !shippingStatus.getComplete_time().equals(zeroTime)) {
                    time = shippingStatus.getComplete_time();
                }
                if (shippingStatus.getDeliveredDate() != null && !shippingStatus.getDeliveredDate().equals(zeroTime)) {
                    shippingDeliveredCompleted.setOrderDelivered(true);
                    time = shippingStatus.getDeliveredDate();
                }
            }
            if (time == null) {
                time = uwOrder.getCreatedAt();
                deliveredDateAvailable = false;
            }
        } else {
            time = uwOrder.getCreatedAt();
        }
        c.setTime(time);
        if(returnRefundEligibilityRequestDTO!=null
                && "SG".equals(returnRefundEligibilityRequestDTO.getOrderInfoDTO().getOrdersHeader().getLkCountry())
                && hooperPids.contains(String.valueOf(uwOrder.getProductId()))
                && inputDTO!=null
                && inputDTO.getExchangeCount()==0
                && !deliveredDateAvailable){
            log.info("for hooper brand 1 time exchange is allowed PID {}", uwOrder.getProductId());
            returnEligibilityPeriod = 380;
        }
        c.add(Calendar.DATE, returnEligibilityPeriod);
        String returnEligiblePeriodDate = sdf.format(c.getTime());
        shippingDeliveredCompleted.setReturnEligiblePeriodDate(returnEligiblePeriodDate);
        log.info(("returnEligiblePeriodDate for the order " + uwOrder.getIncrementId() + " is :: " + returnEligiblePeriodDate + " ShippingDeliveredCompleted:: " + shippingDeliveredCompleted));
        return shippingDeliveredCompleted;
    }

    public boolean isItemReturnable(UwOrderDTO uwOrder, UwOrderDTO uwOrderWh, ReturnRefundResponseDTO returnRefundResponseDTO, ReturnRefundEligibilityRequestDTO returnRefundEligibilityRequest, boolean checkForWhUwOrder, List<ShippingStatusDetail> shippingStatuses,OrderInfoResponseDTO orderInfoDTO) {
        boolean isReturnable = false;

        UwOrderDTO uwOrderDTO = checkForWhUwOrder ? uwOrderWh : uwOrder;
        if (null != uwOrderDTO) {
            if (Status.CANCELED.getValue().equals(uwOrderDTO.getShipmentState())
                    || Status.ORDER_NOT_CONFIRMED.getValue().equals(uwOrderDTO.getShipmentState())
                    || Status.PREDELIVERY_CANCELLATION.getValue().equals(uwOrderDTO.getShipmentStatus())
                    || Status.PREDELIVERY_REFUND.getValue().equals(uwOrderDTO.getShipmentStatus())) {
                if(Status.CANCELED.getValue().equals(uwOrder.getShipmentState())){
                    returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.THIS_ORDER_HAS_ALREADY_BEEN_CANCELLED);
                }
                if(Status.ORDER_NOT_CONFIRMED.getValue().equals(uwOrder.getShipmentState())){
                    returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.THIS_ORDER_IS_NOT_YET_CONFIRMED);
                }
                if(Status.PREDELIVERY_CANCELLATION.getValue().equals(uwOrder.getShipmentStatus())){
                    returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.THIS_ITEM_HAS_ALREADY_BEEN_CANCELLED);
                }
                if(Status.PREDELIVERY_REFUND.getValue().equals(uwOrder.getShipmentStatus())){
                    returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.THIS_ITEM_HAS_ALREADY_BEEN_CANCELLED_REFUNDED);
                }
                return false;
            }
            boolean isCompleteOrClosedOrDelivered = isItemCompleteOrCloseOrDelivered(uwOrder, uwOrderWh, checkForWhUwOrder, shippingStatuses);
            boolean isExistingReturnCancelled = returnDetailsAction.isExisitingReturnCancelled(uwOrderDTO.getUwItemId(), returnRefundEligibilityRequest.getSource());
            isReturnable = returnRefundResponseDTO.getIsReturnable() && isCompleteOrClosedOrDelivered && isExistingReturnCancelled;

            if(!isReturnable &&  !returnRefundResponseDTO.getIsReturnable()){
                returnRefundResponseDTO.setErrorForNotReturnable(ITEM_IS_NOT_RETURNABLE_AS_PER_SYSTEM_POLICY);
            }

            if (!isExistingReturnCancelled) {
                returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.ACTIVE_RETURN_AGAINST_ITEM);
            }
            if(!isCompleteOrClosedOrDelivered){
                returnRefundResponseDTO.setErrorForNotReturnable(Constant.RefundEligibilityError.ORDER_NOT_IN_DELIVERED_CLOSED_COMPLETE_STATE + uwOrder.getShipmentStatus());
            }

            //adding exceptional condition to allow return based on the nexs order status when orders status not updated in uw_orders in case of POS & VSM
            if (!isReturnable && isExistingReturnCancelled && StringUtils.isNotBlank(returnRefundEligibilityRequest.getSource())) {
                isReturnable = isReturnAllowedForPOSOrVSM(returnRefundEligibilityRequest, returnRefundResponseDTO.getIsReturnable(), uwOrder, orderInfoDTO);
                if (isReturnable) {
                    returnRefundResponseDTO.setErrorForNotReturnable(null);
                }
            }
        }
        return isReturnable;
    }

    public boolean isItemCompleteOrCloseOrDelivered(UwOrderDTO uwOrder, UwOrderDTO uwOrderWh, boolean checkForWhUwOrder, List<ShippingStatusDetail> shippingStatuses) {
        UwOrderDTO uwOrderDTO = checkForWhUwOrder ? uwOrderWh : uwOrder;
        boolean isCompleteOrClosed = COMPLETE.equalsIgnoreCase(uwOrderDTO.getShipmentState())
                || CLOSED_STATE.equalsIgnoreCase(uwOrderDTO.getShipmentState());
        boolean isDelivered = isDelivered(uwOrder, uwOrderWh, shippingStatuses);
        log.info("isDelivered flag is :" + isDelivered);
        boolean isCompleteOrClosedOrDelivered = (isCompleteOrClosed || isDelivered);
        log.info("[returnRefundEligibilityService] isCompleteOrClosedOrDelivered :" + isCompleteOrClosedOrDelivered);
        return isCompleteOrClosedOrDelivered;
    }

    public boolean isDelivered(UwOrderDTO uwOrder, UwOrderDTO uwOrderWh, List<ShippingStatusDetail> shippingStatuses) {
        List<String> unicomOrderCode = new ArrayList<>();

        boolean isDelivered = false;
        if (StringUtils.isNotBlank(uwOrder.getUnicomOrderCode())) {
            unicomOrderCode.add(uwOrder.getUnicomOrderCode());
        }

        if (uwOrderWh != null && StringUtils.isNotBlank(uwOrderWh.getUnicomOrderCode())) {
            unicomOrderCode.add(uwOrderWh.getUnicomOrderCode());
        }
        try {
            log.info("[isDeliveredCheckFor] checking in shipping status for increment_id : {},unicomOrderCode :{}", uwOrder.getIncrementId(), unicomOrderCode);
            shippingStatuses = shippingStatuses.stream().filter(s -> unicomOrderCode.contains(s.getUnicomOrderCode()) && s.getOrderNo().equals(uwOrder.getIncrementId())).toList();
            if (!shippingStatuses.isEmpty()) {
                for (ShippingStatusDetail shippingStatus : shippingStatuses) {
                    if (shippingStatus.getIsDelivered()) {
                        isDelivered = true;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("exception while getting isDelivered flag :" + e);
        }
        return isDelivered;
    }

    public String getWMSStatusByShipmentId(Integer incrementId, String shippingPackageId) {
        log.info("[getWMSStatusByShipmentId] Fetching shipment status for incrementId: {}, shippingPackageId: {}", incrementId, shippingPackageId);

        try {
            ResponseEntity<Map<String, Object>> responseObject = wmsFeignClient.getOrderLevelDetails(incrementId);
            if (responseObject.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> wmsOrderResponse = responseObject.getBody();
                log.info("[getWMSStatusByShipmentId] response from wms API -- response:{}",responseObject);
                if (wmsOrderResponse != null && wmsOrderResponse.containsKey("data")) {
                    Map<String,Object> wmsOrderResponseData = (Map<String, Object>) wmsOrderResponse.get("data");
                    if (wmsOrderResponseData.containsKey("orderItemHeaderResponseList")) {
                        List<Map<String, Object>> itemDetails = (List<Map<String, Object>>) wmsOrderResponseData.get("orderItemHeaderResponseList");

                    for (Map<String, Object> shipment : itemDetails) {
                        Object packageId = shipment.get("shippingPackageId");
                        if (shippingPackageId.equals(packageId)) {
                            log.info("[getWMSStatusByShipmentId] Found matching shippingPackageId: {}", packageId);

                            Map<String, Object> shipmentResponse = (Map<String, Object>) shipment.get("shipmentResponse");
                            if (shipmentResponse != null && shipmentResponse.containsKey("status")) {
                                String shipmentStatus = shipmentResponse.get("status").toString();
                                log.info("[getWMSStatusByShipmentId] Shipment status for packageId {}: {}", packageId, shipmentStatus);
                                return shipmentStatus;
                            } else {
                                log.warn("[getWMSStatusByShipmentId] shipmentResponse is null or missing 'status' for packageId: {}", packageId);
                            }
                        }
                    }
                } else {
                    log.info("[getWMSStatusByShipmentId] orderItemHeaderResponseList is missing or null for incrementId: {}", incrementId);
                }
                }
            } else {
                log.error("[getWMSStatusByShipmentId] WMS API call failed with status: {} for incrementId: {}", responseObject.getStatusCode(), incrementId);
            }
        } catch (Exception ex) {
            log.error("[getWMSStatusByShipmentId] Exception occurred while fetching shipment status for incrementId: {}: {}", incrementId, ex.getMessage(), ex);
        }

        log.info("[getWMSStatusByShipmentId] Returning null shipment status for incrementId: {}, shippingPackageId: {}", incrementId, shippingPackageId);
        return null;
    }


    public Date getDeliveredDate(UwOrderDTO uwOrder, UwOrderDTO uwOrderWh, List<ShippingStatusDetail> shippingStatuses) {
        List<String> unicomOrderCode = new ArrayList<>();
        if (StringUtils.isNotBlank(uwOrder.getUnicomOrderCode())) {
            unicomOrderCode.add(uwOrder.getUnicomOrderCode());
        }

        if (uwOrderWh != null && StringUtils.isNotBlank(uwOrderWh.getUnicomOrderCode())) {
            unicomOrderCode.add(uwOrderWh.getUnicomOrderCode());
        }
        shippingStatuses = shippingStatuses.stream().filter(s -> unicomOrderCode.contains(s.getUnicomOrderCode()) && s.getOrderNo().equals(uwOrder.getIncrementId())).toList();
        if (!shippingStatuses.isEmpty()) {
            for (ShippingStatusDetail shippingStatus : shippingStatuses) {
                if (shippingStatus.getIsDelivered()) {
                    return shippingStatus.getDeliveredDate();
                } else {
                    return shippingStatus.getComplete_time();
                }
            }
        } else {
            return uwOrder.getCreatedAt();
        }
        return uwOrder.getCreatedAt();
    }


    public ReturnRefundInputDTO createReturnRefundInputDTO(RRRServiceRequest serviceRequest) {
        ReturnRefundInputDTO inputDTO = new ReturnRefundInputDTO();
        inputDTO.setUwItemId(serviceRequest.getUwOrder().getUwItemId());
        inputDTO.setItemId(serviceRequest.getUwOrder().getItemId());
        inputDTO.setLensOnly(serviceRequest.getUwOrder().isLensOnly());
        inputDTO.setRrrServiceRequest(serviceRequest);
        inputDTO.setReverseType("reverse");
        inputDTO.setCustomerScore(serviceRequest.getRreRequest().getCustomerScore());
        inputDTO.setStoreScore(serviceRequest.getRreRequest().getStoreScore());
        inputDTO.setQcPass("Pass".equalsIgnoreCase(serviceRequest.getItemRequest().getQcStatus()));
        inputDTO.setCountryCode(serviceRequest.getOrderInfoResponse().getOrdersHeader().getLkCountry());
        if (serviceRequest.getRreRequest().getSource() != null && serviceRequest.getRreRequest().getSource().equalsIgnoreCase(Constant.DIRECT_RECEIVING)) {
            inputDTO.setReturnInitiatedSource("direct");
        } else {
            inputDTO.setReturnInitiatedSource(serviceRequest.getRreRequest().getSource());
        }
        inputDTO.setTriggerPoint(determineTriggerPoint(serviceRequest.getRreRequest().getSource()));
        if (!CollectionUtils.isEmpty(serviceRequest.getItemRequest().getReasonsList())) {
            Integer reasonId = serviceRequest.getItemRequest().getReasonsList().get(0).getSecondaryReasonId();
            if (reasonId != null) {
                SecondaryReturnReason secondaryReason = secondaryReturnReasonRepository.findBySecondaryReasonId(reasonId);
                if (secondaryReason != null) {
                    inputDTO.setReturnReason(secondaryReason.getReason());
                }
            }
        }
        inputDTO.setRuleCalledFrom("ReturnRefundEligibility");
        inputDTO.setNavChannel(serviceRequest.getUwOrder().getNavChannel());
        log.info("[ReturnRulesController] [Request Parameters are]: " + inputDTO);
        return inputDTO;
    }


    public List<String> setRefundMethods(UwOrderDTO uwOrder, ReturnRefundResponseDTO refundResponseDTO, ReturnRefundInputDTO inputDTO) {
        if (refundResponseDTO.getRefundMethod() != null) {
            if (refundResponseDTO.getRefundMethod().contains(" ")) {
                String refundMethodCSV = refundResponseDTO.getRefundMethod().replaceAll(" ", ",");
                refundResponseDTO.setRefundMethod(refundMethodCSV);
                log.debug("[returnRefundEligibilityService][setRefundMethods] refundMethod is  {}", refundResponseDTO.getRefundMethod());

            } else {
                refundResponseDTO.setRefundMethod(refundResponseDTO.getRefundMethod());
            }
        } else {
            log.debug("[returnRefundEligibilityService][setRefundMethods] refundMethodCSV is set as NA. ");
            refundResponseDTO.setRefundMethod("NA");
        }

        if ("Vincent Chase Essentials".equalsIgnoreCase(uwOrder.getBrand())) {
            if (StringUtils.isNotBlank(refundResponseDTO.getRefundMethod())) {
                if (refundResponseDTO.getRefundMethod().contains("exchange")) {
                    String[] tempRefundMethods = refundResponseDTO.getRefundMethod().split(",");
                    String finalRefundMethod = "";
                    int refundMethodSize = tempRefundMethods.length;
                    int count = 0;
                    for (String refundMethod : tempRefundMethods) {
                        if (!"exchange".equalsIgnoreCase(refundMethod)) {
                            finalRefundMethod = finalRefundMethod.concat(refundMethod);
                            if (count < (refundMethodSize - 1)) {
                                finalRefundMethod = finalRefundMethod.concat(",");
                            }
                        }
                        count = count + 1;
                    }
                    if (finalRefundMethod.endsWith(",")) {
                        finalRefundMethod = finalRefundMethod.substring(0, finalRefundMethod.length() - 1);
                    }
                    refundResponseDTO.setRefundMethod(finalRefundMethod);
                }
            }
        }

        if (refundResponseDTO.getRefundMethod().contains(ReturnRefundResponseDTO.RefundMethod.NEFT)) {
            String cashfreeFlag = refundResponseDTO.getRefundMethod().replace(ReturnRefundResponseDTO.RefundMethod.NEFT, this.cashfreeFlag);
            log.debug("[returnRefundEligibilityService][setRefundMethods] RefundMethod cashfreeFlag is " + cashfreeFlag);
            log.debug("[returnRefundEligibilityService][setRefundMethods] RefundMethod this.cashfreeFlag is value : " + this.cashfreeFlag);
            refundResponseDTO.setRefundMethod(cashfreeFlag);
        }

        /*---Adding refund method as customer_wallet in case of full LKCASH orders*/
        boolean isFullLKCashOrder = orderUtilsService.getIsFullLKCashOrder(inputDTO.getRrrServiceRequest().getOrderInfoResponse().getItemWisePrices());
        if (isFullLKCashOrder) {
            boolean exchangeRefundMethodExist = false;
            log.debug("order is of type full lkcash, setting refund method as customer_wallet");
            if (StringUtils.isNotBlank(refundResponseDTO.getRefundMethod())) {
                if (refundResponseDTO.getRefundMethod().contains("exchange")) {
                    exchangeRefundMethodExist = true;
                }
            }
            String refundMethodStr = RefundMethod.CUSTOMER_WALLET.getName();
            if (exchangeRefundMethodExist) {
                refundMethodStr = refundMethodStr.concat(",exchange");
            }
            log.info("order is of type full lkcash, setting refund method as customer_wallet");
            refundResponseDTO.setRefundMethod(refundMethodStr);
        }

        /*---Adding refund method as exchange in case of SG and hooper PID if within eligibility data---*/
        if("SG".equals(inputDTO.getCountryCode()) && hooperPids.contains(String.valueOf(uwOrder.getProductId())) && inputDTO.getExchangeCount()==0){
            log.info("if item is hooper and SG order eligibility for exchange is 365 days");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date currentDate = new Date();
            String currentDateString = sdf.format(currentDate);
            if(refundResponseDTO.getReturnEligibleTillDate()!=null
                    && refundResponseDTO.getReturnEligibleTillDate().compareTo(currentDateString) > 0
                    && !refundResponseDTO.getRefundMethod().contains("exchange")){
                String refundMethodStr = refundResponseDTO.getRefundMethod();
                refundMethodStr = refundMethodStr.concat(",exchange");
                refundResponseDTO.setRefundMethod(refundMethodStr);
            }
        }


        List<String> refundMethods = new ArrayList<>(ReturnRefundResponseDTO.getRefundMethodList(refundResponseDTO.getRefundMethod()));

        refundResponseDTO.setRefundMethods(refundMethods);

        return refundMethods;
    }


    public String determineTriggerPoint(String source) {
        if(source == null) return "";
        return switch (source.toUpperCase()) {
            case "WEB", "VSM" -> TriggerPoint.ReturnInitiation.toString();
            case "POS" -> TriggerPoint.POSReceiving.toString();
            case Constant.DIRECT_RECEIVING -> TriggerPoint.WHReceiving.toString();
            default -> "";
        };
    }


    public void populateUwOrder(RRRServiceRequest serviceRequest) {
        OrderInfoResponseDTO orderInfoDTO = serviceRequest.getOrderInfoResponse();

        // Return null immediately if orderInfoDTO is null or UwOrders is not empty
        if (orderInfoDTO == null || CollectionUtils.isEmpty(orderInfoDTO.getUwOrders())) {
            serviceRequest.setUwOrder(null);
            return;
        }

        log.info("[populateUwOrder] fetched orders from orderInfoDTO: {}", gson.toJson(orderInfoDTO.getOrders()));
        // Get the list of item IDs that match the Magento item ID in itemRequest
        List<Integer> itemIds = orderInfoDTO.getOrders().stream()
                .filter(o -> serviceRequest.getItemRequest().getMagentoItemId().equals(o.getMagentoItemId()))
                .map(OrdersDTO::getItemId)
                .toList();

        // Find the latest UwOrder that has a parentUw of 0 and an itemId in the itemIds list (latest means uwOrder of Franchise to Customer)
        serviceRequest.setUwOrder(orderInfoDTO.getUwOrders().stream()
                .filter(uw -> uw.getParentUw() == 0 && itemIds.contains(uw.getItemId()))
                .max(Comparator.comparing(UwOrderDTO::getUwItemId))
                .orElse(null));
    }

    public void checkB2BAndPopulateUwOrderWH(RRRServiceRequest serviceRequest) {
        UwOrderDTO uwOrder = serviceRequest.getUwOrder();
        OrderInfoResponseDTO orderResponse = serviceRequest.getOrderInfoResponse();
        if (uwOrder != null && "B2B".equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            boolean isNEXSFacility = nexsFacilityService.getNexsFacilities().contains(uwOrder.getFacilityCode());
            boolean isDealsKartFacility = dealskartfacility.equalsIgnoreCase(uwOrder.getFacilityCode());

            if (isDealsKartFacility || isNEXSFacility) {
                serviceRequest.setUwOrderWH(uwOrder); //uwOrder is Warehouse version

                //set other version then warehouse version
                serviceRequest.setUwOrder(orderResponse.getUwOrders().stream().filter(uw -> uwOrder.getUwItemId().equals(uw.getB2bRefrenceItemId())).findFirst().orElse(null));
            } else {
                //set warehouse version
                serviceRequest.setUwOrderWH(orderResponse.getUwOrders().stream().filter(uw -> uwOrder.getUwItemId().equals(uw.getB2bRefrenceItemId())).findFirst().orElse(null));
            }
        }
    }

    public boolean getIsExchangeCreatedAndCanceled(List<ExchangeOrdersDTO> exchangeOrdersDTOS, CanceledOrdersDTO canceledOrdersDTO) {
        return !CollectionUtils.isEmpty(exchangeOrdersDTOS) && canceledOrdersDTO != null;
    }

    public boolean getIsExchangeCreated(List<ExchangeOrdersDTO> exchangeOrdersDTOS) {
        return !CollectionUtils.isEmpty(exchangeOrdersDTOS);
    }


    public void performAsyncApiCalls(List<CompletableFuture<?>> futures, List<AtomicReference> references) {
        if (futures.size() != references.size()) {
            throw new IllegalArgumentException("The number of futures and AtomicReference arguments must be the same.");
        }

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        combinedFuture.thenAcceptAsync(Void -> {
            Object[] results = new Object[futures.size()];

            for (int i = 0; i < futures.size(); i++) {
                results[i] = futures.get(i).join();
            }

            for (int i = 0; i < references.size(); i++) {
                references.get(i).set(results[i]);
            }
        }).join();
    }

}
