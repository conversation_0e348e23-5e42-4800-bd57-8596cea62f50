package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.PackageSlipCreationRequest;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;

public interface ID365FinanceService {
    Boolean sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest returnCreateRequest);
    Boolean createReturnEInvoice(Integer returnId, Integer uwItemId, String sourceFacility);
    void syncReturn(UwOrderDTO uwOrder);
    void generatePayloadForReturn(ReturnCreateRequest returnRequestDto) throws Exception;
    void generatePayloadForPackingSlip(PackageSlipCreationRequest packageSlipCreationRequest) throws Exception;
}
