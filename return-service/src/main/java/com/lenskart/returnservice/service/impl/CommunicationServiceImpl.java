package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.PayloadName;
import com.lenskart.returncommon.model.enums.RefundMethod;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.CommunicationTemplate;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnservice.factory.CommunicationPayloadFactoryProducer;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.lenskart.returncommon.utils.Constant.CHANNEL.JJONLINE;
import static com.lenskart.returncommon.utils.Constant.COURIER.LKART;
import static com.lenskart.returncommon.utils.Constant.COURIER.SELF_COURIER;
import static com.lenskart.returncommon.utils.Constant.EVENT.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_SOURCE.ONLINE_WEB;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.RETURN_COMMUNICATION;

@Service
@Slf4j
public class CommunicationServiceImpl implements ICommunicationService {

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private IExchangeOrderService exchangeOrderService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    ICommunicationTemplateService communicationTemplateService;

    @Autowired
    IRefundUtilsService refundUtilsService;

    @Autowired
    IExchangeRefundMethodService exchangeRefundMethodService;

    @Autowired
    CommunicationPayloadFactoryProducer payloadFactory;
    private final ObjectMapper objectMapper = new ObjectMapper();


    public void triggerCommunication(ReverseCourierDetail reverseCourierDetail, ReturnCreationRequestDTO returnCreationRequest, Long groupId, List<OrdersDTO> returnedOrders,
                                     List<UwOrderDTO> uwOrders, List<ReturnItemDTO> exchangeItemList, Integer requestId, Integer returnId, Boolean isExchangeCreated) {
        if (groupId != null && returnedOrders != null && returnedOrders.size() != 0) {
            log.info("[createReturn] returnMethod : {}, incrementId : {}", returnCreationRequest.getReturnMethod(), returnedOrders.get(0).getIncrementId());
            OrdersDTO ordersDTO = returnedOrders.get(0);
            CommRequestDTO commRequestDTO = new CommRequestDTO();
            commRequestDTO.setEventType(RETURN_INITIATED);
            commRequestDTO.setReturnRequestId(requestId);
            commRequestDTO.setReturnId(returnId);
            commRequestDTO.setOrderId(ordersDTO.getIncrementId());
            Map<String, String> conditionsMap = new HashMap<>();
            if (null != reverseCourierDetail && null != reverseCourierDetail.getCourier() && (SELF_COURIER.equalsIgnoreCase(reverseCourierDetail.getCourier()) || LKART.equalsIgnoreCase(reverseCourierDetail.getCourier()))) {
                conditionsMap.put("courier", reverseCourierDetail.getCourier());

                if (LKART.equalsIgnoreCase(reverseCourierDetail.getCourier())) {
                    Optional<ReturnDetail> returnOrder = returnOrderActionService.findReturnOrderById(returnId);
                    if (returnOrder.isPresent()) {
                        String returnStatus = returnOrderActionService.getReturnOrderStatus(returnOrder.get());
                        if (Constant.RETURN_STATUS.RETURN_CANCELLED.equalsIgnoreCase(returnStatus)) {
                            conditionsMap.put("status", Constant.RETURN_STATUS.RETURN_CANCELLED);
                        } else if (returnOrder.get().getReturnMethod().equalsIgnoreCase("StoreReceiving")){
                            conditionsMap.put("returnMethod", "StoreReceiving");
                        } else {
                            conditionsMap.put("flow", "dispensing_assigned_master");
                        }
                    }
                }
                commRequestDTO.setConditionsMap(conditionsMap);
                pushToCommunicationTopic(commRequestDTO);
            } else {
                if (returnCreationRequest.getReturnMethod().equalsIgnoreCase(Constant.RETURN_METHOD.RETURN_TO_STORE)) {
                    conditionsMap.put("returnMethod", Constant.RETURN_METHOD.RETURN_TO_STORE);
                } else {
                    log.info("[createReturn] refund request : {} for order : {}", returnCreationRequest.getItems().get(0).getRefundMethodRequest(), returnedOrders.get(0).getIncrementId());
                    if (CollectionUtils.isEmpty(exchangeItemList) || StringUtils.isBlank(ordersDTO.getChannel()) || ordersDTO.getChannel().equalsIgnoreCase(JJONLINE)) {
                        conditionsMap.put("isExchangeCreated", "false");
                    }
                }

                commRequestDTO.setConditionsMap(conditionsMap);
                pushToCommunicationTopic(commRequestDTO);

                if (returnCreationRequest.getReturnMethod().equalsIgnoreCase(Constant.RETURN_METHOD.RPU)) {
                    commRequestDTO.setConditionsMap(Map.of("returnMethod", Constant.RETURN_METHOD.RPU));
                    pushToCommunicationTopic(commRequestDTO);
                }
                if (null != returnCreationRequest.getReturnSource() && !(Constant.RETURN_SOURCE.POS.equalsIgnoreCase(returnCreationRequest.getReturnSource().getSource()) || ONLINE_WEB.equalsIgnoreCase(returnCreationRequest.getReturnSource().getSource()))) {
                    try {
                        RefundMethodsResponse refundMethods = exchangeRefundMethodService.findExchangeRefundMethod(returnedOrders.get(0).getMagentoItemId(),"vsm", null);
                        if(refundMethods != null && !CollectionUtils.isEmpty(refundMethods.getRefundMethods())){
                            if (refundMethods.getRefundMethods().contains(RefundMethod.EXCHANGE.getName()) && (refundMethods.getRefundMethods().contains(RefundMethod.CUSTOMER_WALLET.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.STORECREDIT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.NEFT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.SOURCE.getName()))) {
                                commRequestDTO.setConditionsMap(Map.of("refundMethod", "EXG_WLT_STR_NFT_SRC"));
                            } else if (refundMethods.getRefundMethods().contains(RefundMethod.EXCHANGE.getName())) {
                                commRequestDTO.setConditionsMap(Map.of("refundMethod", "exchange"));
                            } else if (refundMethods.getRefundMethods().contains(RefundMethod.CUSTOMER_WALLET.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.STORECREDIT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.NEFT.getName()) || refundMethods.getRefundMethods().contains(RefundMethod.SOURCE.getName())) {
                                commRequestDTO.setConditionsMap(Map.of("refundMethod", "WLT_STR_NFT_SRC"));
                            }
                        }
                        pushToCommunicationTopic(commRequestDTO);
                    } catch (Exception e) {
                        log.error("Exception occurred while getting refundMethods", e);
                    }
                }

            }
        }
    }

    @Override
    public void sendReturnUpdateCommunication(ReturnOrderDTO returnOrderDTO, OrdersDTO ordersDTO, ReturnCourierDetail returnCourierDetail, String status, String currStatus) {
        log.info("[updateReturnStatus] going to send communication if status: {} valid for comms and returnId: {}", status, returnOrderDTO.getId());

        CommRequestDTO commRequestDTO = new CommRequestDTO();
        commRequestDTO.setEventType("RETURN_UPDATE");
        commRequestDTO.setReturnRequestId(returnOrderDTO.getRequestId());
        commRequestDTO.setReturnId(returnOrderDTO.getId());
        commRequestDTO.setOrderId(returnOrderDTO.getIncrementId());
        Map<String, String> conditionsMap = new HashMap<>();
        try {
            if (status.equalsIgnoreCase(ReturnStatus.CANCELLED.getStatus()) || status.equalsIgnoreCase(ReturnStatus.CUSTOMER_CANCELLED.getStatus())) {
                log.info("Calling bridge sms method : sendReturnCancellationBridgeSms()");
                if (returnCourierDetail != null && !"LKart".equalsIgnoreCase(returnCourierDetail.getReverseCourier())) {
                    conditionsMap.put("courier", "!LKart");
                }
                if (returnCourierDetail != null && "LKart".equalsIgnoreCase(returnCourierDetail.getReverseCourier()) && "cancelled".equalsIgnoreCase(currStatus)) {
                    log.info("calling dispensing method for sending dispensing issue solved sms");
                    conditionsMap.put("courier", "LKart");
                    if (currStatus.equalsIgnoreCase(Constant.RETURN_STATUS.RETURN_CANCELLED) && returnOrderDTO.getSource().equalsIgnoreCase("vsm")) {
                        conditionsMap.put("status", "cancelled");
                    } else if (returnOrderDTO.getReturnMethod().equalsIgnoreCase("StoreReceiving")) {
                        conditionsMap.put("returnMethod", "StoreReceiving");
                    } else {
                        conditionsMap.put("flow", "dispensing_assigned_master");
                    }
                }
            } else if (status.equalsIgnoreCase(ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus())) {
                conditionsMap.put("status", ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus());
                if (ordersDTO.getFacilityCode().startsWith("SG")) {
                    conditionsMap.put("country", "SG");
                }
            }
            commRequestDTO.setConditionsMap(conditionsMap);
            pushToCommunicationTopic(commRequestDTO);
        } catch (Exception e) {
            log.error("[updateReturnStatus] getting exception while return update communication " + e);
        }
    }

    @Override
    public void pushToCommunicationTopic(CommRequestDTO commRequestDTO) {
        kafkaService.pushToKafka(Constant.RETURN_COMMUNICATION_TOPIC, commRequestDTO.getOrderId() != null ? commRequestDTO.getOrderId().toString() : null, commRequestDTO);
    }

    @Override
    public void sendCommunication(CommRequestDTO commRequestDTO) {
        if (isInvalid(commRequestDTO)) return;

        boolean isCosmosCommunicationEnabled = systemPreferenceService.isCosmosCommunicationEnabled(commRequestDTO.getEventType());

        if (!commRequestDTO.isSkipValidation() && isCosmosCommunicationEnabled) {
            log.info("Communication enabled through cosmos, hence returning");
            return;
        }

        CommunicationTemplate template = communicationTemplateService.findByEventTypeAndConditions(commRequestDTO.getEventType(), commRequestDTO.getConditionsMap());

        if (template == null) {
            log.info("Template not found for eventType:{}, conditionsMap:{}", commRequestDTO.getEventType(), commRequestDTO.getConditionsMap());
            return;
        }

        commRequestDTO.setTemplateName(template.getTemplateName());
        BridgePayloadDTO bridgePayloadDTO = payloadFactory.createBridgePayloadDTO(PayloadName.getPayloadName(template.getPayloadName()), commRequestDTO);

        SmsPayload smsPayload = constructSmsPayload(bridgePayloadDTO);

        smsPayload.getParams().put(Constant.TEMPLATE_ID, template.getTemplateName());

        boolean isSmsSent = sendSms(smsPayload);

        log.info("isSmsSent={} smsPayload={}", isSmsSent, smsPayload);

        try {
            //returnEventService.persistEvent(commRequestDTO.getReturnRequestId(), commRequestDTO.getReturnId(), "SMS", objectMapper.writeValueAsString(smsPayload));
        }catch (Exception e){log.error("Exception in saving sms refund_event {} ", e.getMessage(), e);}
    }

    private boolean isInvalid(CommRequestDTO commRequestDTO) {
        if (commRequestDTO != null && commRequestDTO.isSkipValidation()) {
            return false;
        }

        if (ObjectUtils.anyNull(commRequestDTO, commRequestDTO.getEventType(), commRequestDTO.getReturnRequestId())) {
            log.info("Invalid commRequestDTO " + commRequestDTO);
            return true;
        }

        if(commRequestDTO.getReturnId()!=null && commRequestDTO.getEventType()!=null){
            List<ReturnEvent> refundEventList = returnEventService.getReturnEvent(commRequestDTO.getReturnId());
            List<ReturnEvent> filteredEventList = refundEventList.stream().filter(e-> commRequestDTO.getEventType().equals(e.getEvent())).toList();
            if(filteredEventList.size()>1){
                log.info("Same event triggered more than once hence not sending sms {}", commRequestDTO);
                return true;
            }
        }
        return false;
    }

    private SmsPayload constructSmsPayload(BridgePayloadDTO bridgePayloadDTO) {
        BasePayloadDTO basePayloadDTO = (BasePayloadDTO) bridgePayloadDTO;
        return SmsPayload.builder()
                .mobile(basePayloadDTO.getCustomerMobile())
                .communicationType(basePayloadDTO.getCommunicationType())
                .mask(basePayloadDTO.getMask())
                .desc(basePayloadDTO.getDesc())
                .orderId(basePayloadDTO.getOrderId())
                .params(objectMapper.convertValue(bridgePayloadDTO, new TypeReference<Map<String, Object>>() {
                }))
                .build();
    }

    private boolean sendSms(SmsPayload smsPayload) {
        try {
            ResponseEntity<Boolean> responseEntity = orderOpsFeignClient.sendSms(smsPayload);
            return responseEntity.getBody();
        } catch (Exception e){
            log.error("Exception sendSms {}", e.getMessage());
            return false;
        }
    }

}
