package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.ItemAmountGetRequestDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmountUtil {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    public ItemWiseAmountDTO getItemAmount(boolean isCancellation, Integer incrementId, List<Integer> uwItemIds){

        ItemWiseAmountDTO itemWiseAmountDTO = null;

        log.info("[getItemAmount] the list of uw_items {}",uwItemIds);

        ItemAmountGetRequestDTO itemAmountGetRequestDTO = new ItemAmountGetRequestDTO();
        itemAmountGetRequestDTO.setIncrementId(incrementId);
        itemAmountGetRequestDTO.setUwItemIds(uwItemIds);
        itemAmountGetRequestDTO.setCancellation(false);
        itemAmountGetRequestDTO.setUwItemIdsRtoList(uwItemIds);
        try{
            ResponseEntity<ItemWiseAmountDTO> itemWiseAmountDTOResponseEntity = orderOpsFeignClient.getItemAmount(itemAmountGetRequestDTO);
            log.info("[getItemAmount] response : {}", itemWiseAmountDTOResponseEntity);
            if(itemWiseAmountDTOResponseEntity.getStatusCode().is2xxSuccessful()){
                itemWiseAmountDTO = itemWiseAmountDTOResponseEntity.getBody();
            }
        }catch (Exception exception){
            log.error("[getItemAmount] uwItemIds : {}, exception occurred : {}", uwItemIds, exception);
        }
        return itemWiseAmountDTO;
    }
}
