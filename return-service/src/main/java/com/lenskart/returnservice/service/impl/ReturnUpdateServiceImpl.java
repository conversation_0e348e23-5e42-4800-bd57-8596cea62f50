package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.ordermetadata.dto.response.DispensingDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.request.RefundRejectRequest;
import com.lenskart.returncommon.exception.InvalidRequestException;
import com.lenskart.returncommon.exception.OrderNotFound;
import com.lenskart.returncommon.exception.ReturnRequestException;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.CancelRequest;
import com.lenskart.returncommon.model.request.CancelReverseRequest;
import com.lenskart.returncommon.model.response.Response;
import com.lenskart.returncommon.model.response.ReturnCancellabilityResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.UW_ORDERS_DTO;
import static com.lenskart.orderops.model.ReturnDetail.STATUS.RETURN_PENDING_APPROVAL;
import static com.lenskart.returncommon.model.enums.ReturnEvent.RTO_RECEIVED_AT_WAREHOUSE;
import static com.lenskart.returncommon.utils.Constant.EVENT.CLICKPOST_CANCELLED_STATUS;
import static com.lenskart.returncommon.utils.Constant.EVENT.RETURN_REQUEST_CREATED;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.RETURN_RECEIVED;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_COMMENT_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.STATUS_UPDATE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.AWAITED_RTO;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.RTO;
import static com.lenskart.returncommon.utils.Constant.STATUS.UNICOM_STOCKIN;
import static com.lenskart.returncommon.utils.Constant.SUCCESS;

@Slf4j
@Service
public class ReturnUpdateServiceImpl implements IReturnUpdateService {

    @Autowired
    private IReturnOrderItemService returnOrderItemService;
    @Autowired
    private IReturnOrderActionService returnOrderActionService;
    @Autowired
    private ISystemPreferenceService systemPreferenceService;
    @Autowired
    private IReturnEventService returnEventService;
    @Autowired
    private IReverseTrackingEventRepository reverseTrackingEventRepository;
    @Autowired
    private ReversePickupPincodeRepository reversePickupPincodeRepository;
    @Autowired
    private ReverseCourierMappingRepository reverseCourierMappingRepository;
    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;
    @Autowired
    private IOrderUtilsService orderUtilsService;
    @Autowired
    private IRefundUtilsService refundUtilsService;
    @Value("${reverse.logistics.consumer.url}")
    private String reverseLogisticsConsumerUrl;
    @Autowired
    private IKafkaService kafkaService;
    @Autowired
    private ReturnEventRepository returnEventRepository;
    @Autowired
    private ReturnUtil returnUtil;
    @Autowired
    ICommunicationService communicationService;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private IReturnDetailsService returnDetailsService;
    private final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));
    private static final List<String> RETURN_ITEM_STATUS = new ArrayList<String>(
            Arrays.asList(UNICOM_STOCKIN, RETURN_RECEIVED));

    @Autowired
    private ID365FinanceService financeService;

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Override
    public ReturnDetailsUpdateResponse updateReturnStatus(ReturnDetailUpdateRequestDto request) {
        log.info("[ReturnUpdateServiceImpl][updateReturnStatus] updateReturnStatus request : {}", request);

        ReturnDetailsUpdateResponse response = new ReturnDetailsUpdateResponse();
        if (Objects.isNull(request) || request.getReturnId() == 0) {
            setReturnOrderUpdateResponse(response, Constant.FAILURE, null != request ? Constant.INVALID_RETURN_ID : Constant.REQUEST_IS_NULL);
            return response;
        }
        ReturnDetail returnOrder = returnOrderActionService.findReturnOrderById(request.getReturnId()).orElse(null);
        String currentStatus = returnOrderActionService.getReturnOrderStatus(returnOrder);
        String status = request.getStatus();
        if (Objects.isNull(returnOrder)) {
            log.info("ReturnUpdateServiceImpl:updateReturnStatus return not found against return id :" + request.getReturnId());
            setReturnOrderUpdateResponse(response, Constant.FAILURE, "return not found against return id :" + request.getReturnId());
            return response;
        }
        List<ReturnDetailItem> returnItemList = returnOrderActionService.findAllByReturnId(request.getReturnId());
        ReturnEvent returnEvent = returnEventRepository.findTopByReturnIdOrderByIdDesc(returnOrder.getId());
        Integer returnRequestId = returnEvent != null ? returnEvent.getReturnRequestId() : null;
        String returnEventName = ReturnStatus.getEnumNameByStatus(request.getStatus());
        Integer uwItemId = getUwItemId(request.getReturnId());
        if (Constant.STATUS.CANCELLED.equalsIgnoreCase(request.getStatus())) {
            for (ReturnDetailItem returnOrderItem : returnItemList) {
                uwItemId = returnOrderItem.getUwItemId();
                ReturnCancellabilityResponse returnCancellabilityResponse = returnDetailsService.getReturnCancellabilityForItem(returnOrderItem.getUwItemId());
                if (!returnCancellabilityResponse.getIsCancellable()) {
                    returnEventService.createReturnEvent(returnRequestId, returnOrder.getId(), Constant.EVENT.RETURN_ORDER_ITEM_NOT_CANCELLABLE, returnCancellabilityResponse.getErrorMessage());
                    setReturnOrderUpdateResponse(response, Constant.FAILURE, returnCancellabilityResponse.getErrorMessage());
                    return response;
                }
            }
        }
        try {
            ReturnOrderDTO returnOrderDTO = getReturnOrderDto(returnOrder);
            OrderInfoResponseDTO orderInfoResponseDTO = null;
            List<UwOrderDTO> uwOrderDTOS = null;
            OrdersDTO ordersDTO = null;
            UwOrderDTO uwOrderDTO = null;
            if(null != request.getOrderInfoResponseDTO()){
                orderInfoResponseDTO = request.getOrderInfoResponseDTO();
            }
            if(Objects.isNull(orderInfoResponseDTO) || CollectionUtils.isEmpty(orderInfoResponseDTO.getOrders())) {
                boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(returnOrder.getIncrementId(), "order-info.rollout.percentage");
                ResponseEntity<OrderInfoResponseDTO> orderInfo = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(returnOrder.getIncrementId(), List.of(UW_ORDERS_DTO))
                        : orderOpsFeignClient.getOrderDetails(returnOrder.getIncrementId());
                if (orderInfo.getStatusCode().is2xxSuccessful() && Objects.nonNull(orderInfo.getBody())) {
                    orderInfoResponseDTO = orderInfo.getBody();
                }
            }
            if(null!=orderInfoResponseDTO) {
                ordersDTO = orderInfoResponseDTO.getOrders().stream().findFirst().orElse(null);
                uwOrderDTOS = orderInfoResponseDTO.getUwOrders();
                Integer finalUwItemId = uwItemId;
                log.info("[ReturnUpdateServiceImpl][:]updateReturnStatus] finalUwItemId : {}", finalUwItemId);
                uwOrderDTO = uwOrderDTOS.stream().filter(uw -> Objects.equals(uw.getUwItemId(), finalUwItemId)).findFirst().orElse(null);
            }
            if(uwOrderDTO == null && request.getUwOrderDTO() != null){
                uwOrderDTO = request.getUwOrderDTO();
                uwOrderDTOS = List.of(uwOrderDTO);
            }
            ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTopByReturnIdOrderByIdDesc(returnOrder.getId());
            if (status.equalsIgnoreCase(ReturnStatus.CANCELLED.getStatus()) || status.equalsIgnoreCase(ReturnStatus.CUSTOMER_CANCELLED.getStatus())) {
                List<String> clickpostStatuses = Arrays.asList("new_reverse_pickup","reference_id_issued");
                log.info("[ReturnUpdateServiceImpl][updateReturnStatus] returnId : {}, currentStatus : {}, clickpostStatuses : {}", request.getReturnId(), currentStatus, clickpostStatuses);
                if (!"TrackingEvent".equalsIgnoreCase(request.getUsername()) && clickpostStatuses.contains(currentStatus)) {
                    List<ReverseDetailTrackingEvent> reverseDetailTrackingEvents = reverseTrackingEventRepository.findByReturnId(request.getReturnId());
                    if(!CollectionUtils.isEmpty(reverseDetailTrackingEvents)){
                        Optional<Integer> isOrderCancelledAtClickpost = reverseDetailTrackingEvents
                                .stream()
                                .map(ReverseDetailTrackingEvent::getTrackingStatusCode)
                                .filter(trackingStatusCode -> trackingStatusCode == 10)
                                .findAny();
                        log.info("[ReturnUpdateServiceImpl][updateReturnStatus] returnId : {}, isOrderCancelledAtClickpost : {}", request.getReturnId(), isOrderCancelledAtClickpost.isPresent());
                        if(isOrderCancelledAtClickpost.isEmpty()){
                            Map<String, Object> clickPostResponse = cancelReverseAtClickPost(returnOrder, returnCourierDetail, currentStatus);
                            if (!(boolean) clickPostResponse.get(SUCCESS) && !clickPostResponse.getOrDefault("status",200).equals(Integer.parseInt(CLICKPOST_CANCELLED_STATUS))) {
                                log.error("ReturnUpdateServiceImpl:updateReturnStatus failed to cancel from clickpost");
                                setReturnOrderUpdateResponse(response, Constant.FAILURE, "ClickPost Message: " + clickPostResponse.get("message").toString());
                                Map<String, String> comments = new HashMap<>();
                                String comment = "Return could not be canceled by ClickPost. Error:" + clickPostResponse.get(Constant.MESSAGE).toString();
                                returnEventService.createReturnEvent(returnRequestId, returnOrder.getId(), Constant.EVENT.CANCELLATION_FAILED_AT_CLICKPOST, clickPostResponse.get("message").toString());
                                if(null!=ordersDTO) {
                                    updateOrderComment(ordersDTO.getOrderId(), comment);
                                }
                                return response;
                            }
                        }
                    }
                }
                cancelDispensing(returnOrder, ordersDTO, returnCourierDetail);
            }
            if (uwOrderDTO == null){
                log.info("could not find uwOrderDTO, returning..............");
                return response;
            }
            returnEventService.createReturnEvent(returnRequestId, returnOrder.getId(), returnEventName, request.getComments());
            kafkaService.pushToKafka("d365_return_tracking_event", String.valueOf(returnOrder.getIncrementId()), new D365ReturnTrackingRequestDTO(returnOrder.getId(), returnEventName));
            boolean isB2bOrder = returnUtil.isB2BOrder(uwOrderDTO);
            if (isB2bOrder) {
                updateB2BUwITemIdReturnDetail(uwOrderDTO, status, returnEventName, currentStatus);
            }
            setReceivingFlagToNoOnReturnAccept(status, currentStatus, returnOrder, uwOrderDTO);

            // return-cancel-cosmos-integration
            //String eventName = returnUtil.getReturnUpdateEventName(status);
            //CompletableFuture.runAsync(() -> returnEventService.pushEventToCosmos(returnOrder.getId(),eventName));

            communicationService.sendReturnUpdateCommunication(returnOrderDTO, ordersDTO, returnCourierDetail, status, currentStatus);
            setReturnOrderUpdateResponse(response, SUCCESS, Constant.RETURN_DETAILS_UPDATED_SUCCESSFULLY);
            log.info("ReturnUpdateServiceImpl:updateReturnStatus : returnDetail:" + returnOrder.getId() + " returnDetailsUpdateResponse:" + response);
            log.info("Return detail receving flag for return id " + returnOrder.getId() + " is " + returnOrder.getReceivingFlag());
            orderUtilsService.updateBackSyncStatus(returnItemList, status, uwOrderDTOS, returnRequestId, returnOrder.getId());
            Integer orderId = null != ordersDTO ? ordersDTO.getOrderId() : null;
            cancelExchangeIfExistOnReturnCancel(returnEvent, returnOrder, orderId, request);
            if (returnOrder.getIsInsurance() != null && returnOrder.getIsInsurance() && !request.getStatus().equalsIgnoreCase(ReturnStatus.RETURN_NEED_APPROVAL.getStatus())) {
                insuranceService.pushUpdateClaimKafkaRequest(returnOrder.getId(), request.getStatus(), request.getComments());
            }
        } catch (Exception ex) {
            log.error("ReturnUpdateServiceImpl:updateReturnStatus return not found" + ex);
            returnEventService.createReturnEvent(returnRequestId, returnOrder.getId(), "EXCEPTION_IN_updateReturnStatus", ex.getMessage());
            setReturnOrderUpdateResponse(response, Constant.FAILURE, ex.getMessage());
        }
        return response;
    }

    private Integer getUwItemId(int returnId) {
        return returnOrderActionService.getUwItemIdByReturnId(returnId);
    }

    @Override
    public boolean setAutoCancelFlagAsFalse(Integer returnId) {
        log.info("updating auto_cancel_flag for return id :"+returnId);
        if(null!=returnId && returnId!=0) {
            Optional<ReturnDetail> optionalReturnOrder = (returnOrderActionService.findReturnOrderById(returnId));
            if (optionalReturnOrder.isPresent()) {
                ReturnDetail returnOrder = optionalReturnOrder.get();
                returnOrder.setIsAutoCancelEnable(false);
                returnOrderActionService.saveReturnOrder(returnOrder);
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Object> markRtoItems(ReturnOrderRequestDTO rtoRequestDTO) throws Exception {

        validateReturnRequest(rtoRequestDTO);
        List<Integer> items = new ArrayList<>();
        log.info("[markRtoItems] " + rtoRequestDTO.toString());

        for (ReturnItemRequestDTO returnItemRequest : rtoRequestDTO.getItems()) {
            validateReturnItemRequest(rtoRequestDTO, returnItemRequest);
            UwOrderDTO uwOrder = returnItemRequest.getUwOrderDTO();
            if(uwOrder == null){
                uwOrder = fetchUwOrder(returnItemRequest.getItemId(), rtoRequestDTO.getIncrementId(), rtoRequestDTO.getReferenceOrderCode());
            }
            validateUwOrder(rtoRequestDTO, uwOrder);
            if(uwOrder.getParentUw() == 0){
                items.add(returnItemRequest.getItemId());
                returnItemRequest.setUwOrderDTO(uwOrder);
            }
        }

        isThisDuplicateReturn(rtoRequestDTO, items);

        return processRtoItems(rtoRequestDTO);
    }

    private void validateReturnRequest(ReturnOrderRequestDTO returnRequest) throws InvalidRequestException {
        if (returnRequest == null || returnRequest.getIncrementId() == null || returnRequest.getReferenceOrderCode() == null ||
                CollectionUtils.isEmpty(returnRequest.getItems())) {
            assert returnRequest != null;
            logAndThrowInvalidRequest(returnRequest, "Null or Empty parameters in ReturnRequest request");
        }
    }

    private void validateReturnItemRequest(ReturnOrderRequestDTO returnRequest, ReturnItemRequestDTO returnItemRequest) throws InvalidRequestException {
        if (returnItemRequest.getItemId() == null || StringUtils.isBlank(returnItemRequest.getQcStatus())) {
            logAndThrowInvalidRequest(returnRequest, "Null or Empty parameters (unitItemId/QcStatus) in items request");
        }
    }

    private void validateUwOrder(ReturnOrderRequestDTO returnRequest, UwOrderDTO uwOrder) throws OrderNotFound {
        if (!returnRequest.getReferenceOrderCode().equals(uwOrder.getUnicomOrderCode())) {
            logAndThrowOrderNotFound(returnRequest, "Item not found in Inventory");
        }
    }

    private void logAndThrowInvalidRequest(ReturnOrderRequestDTO returnRequest, String message) throws InvalidRequestException {
        createUnicomApiLog(returnRequest.getIncrementId(), returnRequest.getReferenceOrderCode(), "Failure", message, true);
        log.error("[ReturnServiceImpl][returnItems] : {}", message);
        throw new InvalidRequestException(HttpStatus.BAD_REQUEST.toString(), message);
    }

    private void logAndThrowOrderNotFound(ReturnOrderRequestDTO returnRequest, String message) throws OrderNotFound {
        createUnicomApiLog(returnRequest.getIncrementId(), returnRequest.getReferenceOrderCode(), "Failure", message, true);
        log.error("[ReturnServiceImpl][returnItems] : {}", message);
        throw new OrderNotFound(message);
    }


    private UwOrderDTO fetchUwOrder(Integer itemId, Integer incrementId, String referenceOrderCode) throws OrderNotFound {
        log.info("[fetchUwOrder] fetching data from order-ops - order : {}, unicomOrderCode : {}", incrementId, referenceOrderCode);
        ResponseEntity<UwOrderDTO> response = orderOpsFeignClient.getUwOrderInfo(String.valueOf(IdentifierType.UW_ITEM_ID),String.valueOf(itemId));
        UwOrderDTO uwOrder = response != null ? response.getBody() : null;
        if (uwOrder == null) {
            createUnicomApiLog(incrementId, referenceOrderCode, "Failure",
                    "Item not found in Inventory", true);
            throw new OrderNotFound("Item not found in Inventory");
        }
        return uwOrder;
    }

    private boolean isThisDuplicateReturn(ReturnOrderRequestDTO returnRequest, List<Integer> items) throws OrderNotFound, ReturnRequestException {
        if (!isThisDuplicateReturn(items)) {
            UwOrderDTO uwOrder = returnRequest.getItems().get(0).getUwOrderDTO();
            if (!AWAITED_RTO.equalsIgnoreCase(uwOrder.getShipmentStatus())) {
                createUnicomApiLog(returnRequest.getIncrementId(), returnRequest.getReferenceOrderCode(), "Failure", "Duplicate Return Request", true);
            }
        }
        return false;
    }

    private Map<String, Object> processRtoItems(ReturnOrderRequestDTO returnRequest) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try {

            RtoItemDTO rtoItemDTO = rtoItems(returnRequest);
            Integer returnId = rtoItemDTO != null ? rtoItemDTO.getReturnId() : null;
            Integer requestId = rtoItemDTO != null ? rtoItemDTO.getRequestId() : null;

            log.info("[markRtoItems] returnID: {}", returnId);
            if (returnId != null) {
                result.put("success", true);
                result.put("returnId", returnId);
                log.info("[markRtoItems] " + result);
                saveReturnEvent(returnId,RTO_RECEIVED_AT_WAREHOUSE.name(),requestId);
                createReturnEInvoice(returnId, returnRequest.getUwOrderDTOs());
                //syncReturnToFinance(returnId);
            } else {
                result.put("success", false);
            }
        } catch (Exception e) {
            log.error("Error in [markRtoItems][rtoItems]: {}", e.getMessage());
            throw e;
        }
        return result;
    }

    private void syncReturnToFinance(Integer returnId) {

        log.info("[syncReturnToFinance] Sending data to D365FinanceConsumer, returnId is : {}", returnId);
        try {
            ReturnDetailItem returnDetailItem = returnOrderActionService.findTopByReturnId(returnId);
            log.info("[syncReturnToFinance] Processing data to D365FinanceConsumer for uwItemId : {}", returnDetailItem.getUwItemId());
            Boolean dataProcessed = null;
            ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
            returnCreateRequest.setUwItemId(returnDetailItem.getUwItemId());
            dataProcessed = financeService.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
            log.info("[syncReturnToFinance] dataProcessed value : {}", dataProcessed);
        } catch (Exception e) {
            log.error("[triggerD365ReturnOrder] exception {} {}", e.getMessage(), e);
        }
    }

    private void createReturnEInvoice(Integer returnId, List<UwOrderDTO> uwOrderDTOS){
        try{
            Integer uwItemId = returnOrderActionService.getUwItemIdByReturnId(returnId);
            String sourceFacility = null;
            if(!CollectionUtils.isEmpty(uwOrderDTOS)){
                Optional<UwOrderDTO> uwOrderDTO = uwOrderDTOS.stream().filter(uwOrder -> Objects.equals(uwOrder.getUwItemId(), uwItemId)).findFirst();
                if(uwOrderDTO.isPresent()){
                    sourceFacility = uwOrderDTO.get().getFacilityCode();
                }
            }
            financeService.createReturnEInvoice(returnId, uwItemId, sourceFacility);
        }catch (Exception exception){
            log.error("[createReturnEInvoice] exception occurred : {}", exception.getMessage());
        }
    }

    private void saveReturnEvent(Integer returnId, String event, Integer requestId) {
        returnEventService.createReturnEvent(requestId, returnId, event, "");
    }

    @Transactional(rollbackFor = Exception.class)
    public RtoItemDTO rtoItems(ReturnOrderRequestDTO returnRequest) throws Exception {
        if (returnRequest == null) {
            return null;
        }
        RtoItemDTO rtoItemDTO = new RtoItemDTO();
        log.info("[markRtoItems][rtoItems] : {}", returnRequest);

        Integer returnId = getReturnIdFromReturnOrder(returnRequest);
        if (returnId == null && !returnRequest.getItems().isEmpty()) {
            returnId = getReturnIdFromReturnOrderItem(returnRequest);
        }
        ReturnRequest request = null;
        Integer requestId = null;

        if(returnId == null) {
            request = createReturnRequest(returnRequest);
            requestId = request.getId();
        }else{
            List<ReturnDetail> returnDetails = returnOrderActionService.findByReturnIdIn(Collections.singletonList(returnId), true);
            if(!CollectionUtils.isEmpty(returnDetails)){
                ReturnDetail returnDetail = returnDetails.get(0);
                requestId = returnDetail.getRequestId();
            }
        }

        returnId = createOrUpdateReturnOrder(returnRequest, returnId, requestId);

        if (returnId != null) {
            processReturnItems(returnRequest, returnId, requestId);
            updateOrderStatus(returnRequest);
        }
        rtoItemDTO.setReturnId(returnId);
        rtoItemDTO.setRequestId(requestId);

        return rtoItemDTO;
    }

    private ReturnRequest createReturnRequest(ReturnOrderRequestDTO returnOrderRequest) throws JsonProcessingException {
        String identifierType = com.lenskart.returncommon.model.enums.IdentifierType.UW_ITEM_ID.name();
        String identifierValue = String.valueOf(returnOrderRequest.getItems().get(0).getItemId());
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setReturnReason("RTO");
        returnRequest.setIdentifierType(identifierType);
        returnRequest.setIdentifierValue(identifierValue);
        returnRequest.setCreatedAt(new Date());
        returnRequest.setIncrementId(returnOrderRequest.getIncrementId());
        returnRequest.setSource(returnOrderRequest.getSource());
        try{
            ReturnOrderRequestDTO returnOrderRequestDTO = objectMapper.convertValue(returnOrderRequest, ReturnOrderRequestDTO.class);
            returnOrderRequestDTO.setUwOrderDTOs(null);
            returnOrderRequestDTO.setOrderDTOs(null);
            returnOrderRequestDTO.setOrdersHeaderDTO(null);
            returnRequest.setReturnCreationRequest(objectMapper.writeValueAsString(returnOrderRequestDTO));
        }catch (Exception exception){
            log.error("[createReturnRequest] exception occurred : {}", exception.getMessage());
        }
        returnRequest = returnRequestRepository.save(returnRequest);
        returnEventService.createReturnEvent(returnRequest.getId(), null, RETURN_REQUEST_CREATED, "");
        return returnRequest;
    }

    private Integer getReturnIdFromReturnOrder(ReturnOrderRequestDTO returnRequest) {
        ReturnDetail returnOrder = returnOrderActionService.findTop1ByOrderNoAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(
                returnRequest.getIncrementId(),
                returnRequest.getReferenceOrderCode(),
                AWAITED_RTO
        );
        Integer returnId = returnOrder != null ? returnOrder.getId() : null;
        log.info("[rtoItems] return id from RO: {}", returnId);
        return returnId;
    }

    private Integer getReturnIdFromReturnOrderItem(ReturnOrderRequestDTO returnRequest) {
        Integer returnId = returnOrderActionService.findReturnIdByReturnTypeUwItemID(
                AWAITED_RTO,
                returnRequest.getItems().get(0).getItemId()
        );
        log.info("[markRtoItems][rtoItems] returnId: {}", returnId);
        return returnId;
    }

    private Integer createOrUpdateReturnOrder(ReturnOrderRequestDTO returnRequest, Integer returnId, Integer requestId) throws Exception {
        CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse = returnOrderActionService.createUpdateReturnOrder(
                returnRequest,
                returnId,
                RETURN_RECEIVED,
                RTO,
                null,
                requestId
        );
        returnId = createUpdateReturnOrderResponse != null ? createUpdateReturnOrderResponse.getReturnId() : null;
        log.info("[markRtoItems][rtoItems][returnId]: {}", returnId);
        if(returnId != null){
            kafkaService.pushToKafka("d365_return_tracking_event", String.valueOf(createUpdateReturnOrderResponse.getReturnId()), new D365ReturnTrackingRequestDTO(returnId, RETURN_RECEIVED));
        }
        return returnId;
    }

    private void processReturnItems(ReturnOrderRequestDTO returnRequest, Integer returnId, Integer requestId) throws Exception {
        for (ReturnItemRequestDTO returnItemRequest : returnRequest.getItems()) {
            UwOrderDTO uwOrder = returnItemRequest.getUwOrderDTO();
            log.info("[markRtoItems][rtoItems][createReturnItem] " + returnItemRequest.toString());
            returnOrderActionService.createReturnItem(returnItemRequest, uwOrder, returnId, RTO, requestId);
        }
    }

    public void createUnicomApiLog(Integer incrementId, String unicomOrderCode, String status, String message,
                                   Boolean exception) {
        try {
            if (null != incrementId && null != unicomOrderCode && null != message && null != status) {
                UnicomApiLogDto unicomApiLog = new UnicomApiLogDto();
                unicomApiLog.setIncrementId(incrementId.toString());
                unicomApiLog.setType("I");
                unicomApiLog.setApi(UnicomApiLogDto.API.MARK_ORDER_RTO);
                unicomApiLog.setStatus(status);
                unicomApiLog.setMessage(message);
                unicomApiLog.setException(exception);
                unicomApiLog.setEMsg("");
                unicomApiLog.setCreateDatetime(new Date());
                unicomApiLog.setUnicomOrderCode(unicomOrderCode);
                kafkaService.pushToKafka("unicom_api_log_topic",String.valueOf(incrementId),unicomApiLog);
            }
        } catch (Exception e) {
            log.error("[statusUpdate] exception while pushing to kafka : "+exception);
        }
    }


    private Boolean isThisDuplicateReturn(List<Integer> itemIds) throws ReturnRequestException {
        if (!CollectionUtils.isEmpty(itemIds)) {
            List<ReturnDetailItem> items = returnOrderActionService.findByUwItemIdInAndStatusInAndReturnType(itemIds,
                    RETURN_ITEM_STATUS, RTO);
            if (!CollectionUtils.isEmpty(items)) {
                log.error("[ReturnServiceImpl][checkForDuplicateReturn] : Duplicate uwItems");
                throw new ReturnRequestException("Duplicate Items for return");
            }
        }
        return false;
    }

    private void updateOrderStatus(ReturnOrderRequestDTO returnOrderRequest) {
        log.info("[updateOrderStatus]" + returnOrderRequest.toString());
        OrderStatusUpdateDetails request = new OrderStatusUpdateDetails();
        request.setState("closed");
        request.setStatus(RTO);
        request.setIncrementId(returnOrderRequest.getIncrementId());
        request.setUnicomOrderCode(returnOrderRequest.getReferenceOrderCode());
        log.info("[updateOrderStatus] orderStatusUpdateDetails: {}", request);
        try {
            kafkaService.pushToKafka(STATUS_UPDATE, String.valueOf(returnOrderRequest.getIncrementId()), objectMapper.writeValueAsString(request));
        } catch (Exception exception) {
            log.error("[statusUpdate] exception while pushing to kafka : " + exception);
        }
    }


    public Map<String, Object> cancelReverseAtClickPost(ReturnDetail returnOrder, ReturnCourierDetail returnCourierDetail, String currentStatus) {
        Map<String, Object> clickPostRes = new HashMap<>();
        clickPostRes.put(SUCCESS, false);
        clickPostRes.put(Constant.MESSAGE, Constant.DEFAULT_CLICKPOST_RESPONSE);
        if (null != returnOrder && returnOrder.getId() != null) {
            if (ReturnStatus.RETURN_EXPECTED_POS.getStatus().equalsIgnoreCase(currentStatus)) {
                log.info("[cancelReverseAtClickPost] returnId : {}, status : {}", returnOrder.getId(), currentStatus);
                clickPostRes.put("success", true);
                clickPostRes.put("message", "Return done through manual flow");
                log.info("[cancelReverseAtClickPost] Not sending request to ClickPost for returnId : {} as status is : {}", returnOrder.getId(), currentStatus);
                return clickPostRes;
            }
            if (Objects.nonNull(returnCourierDetail)) {
                log.info("[cancelReverseAtClickPost] returnId : " + returnOrder.getId() + ", AWB : " + returnCourierDetail.getReverseCourier() + "/" + returnCourierDetail.getReversePickupReferenceId());
                List<ReverseDetailTrackingEvent> reverseTrackingEvents = reverseTrackingEventRepository.findByReturnId(returnOrder.getId());
                if (reverseTrackingEvents.isEmpty()) {
                    clickPostRes.put("success", true);
                    clickPostRes.put("message", "Return done through manual flow");
                    log.info("[cancelReverseAtClickPost] Not sending request to ClickPost for returnId : " + returnOrder.getId() + ", as AWB : " + returnCourierDetail.getReverseAwbNumber() + "/" + returnCourierDetail.getReversePickupReferenceId());
                    return clickPostRes;
                }

                ReversePickupPincode reversePickupPincode = reversePickupPincodeRepository.findTopByCourier(returnCourierDetail.getReverseCourier());
                if (null != reversePickupPincode) {

                    try {
                        String requestUrl = reverseLogisticsConsumerUrl + "/reverse/cancel";
                        CancelReverseRequest cancelReverseRequest = new CancelReverseRequest();
                        if (StringUtils.isNotBlank(returnCourierDetail.getReverseAwbNumber()))
                            cancelReverseRequest.setWaybill(returnCourierDetail.getReverseAwbNumber());
                        else
                            cancelReverseRequest.setWaybill(returnCourierDetail.getReversePickupReferenceId());

                        ReverseCourierMapping reverseCourierMapping = reverseCourierMappingRepository.findByCourier(reversePickupPincode.getCourier());
                        log.info("[ReturnInitiationServiceImpl][assignReverseCourier] reverseCourierMapping : " + reverseCourierMapping);
                        if (null != reverseCourierMapping) {
                            cancelReverseRequest.setCpId(reverseCourierMapping.getCpId());
                            cancelReverseRequest.setAccountCode(reverseCourierMapping.getCpName());
                        }

                        log.info("[cancelReverseAtClickPost] cancelReverseRequest : " + cancelReverseRequest);
                        ResponseEntity<Response> responseEntity = new RestTemplate().postForEntity(requestUrl, cancelReverseRequest, Response.class);
                        if (null != responseEntity) {
                            Response response = responseEntity.getBody();
                            JSONObject jsonObject = new JSONObject(response.getResponseContent());
                            JSONObject getMeta = jsonObject.getJSONObject("meta");
                            if (getMeta.has("success")) {
                                clickPostRes.put("success", getMeta.get("success"));
                            }
                            if (getMeta.has("message")) {
                                clickPostRes.put("message", getMeta.get("message"));
                            }
                            if (getMeta.has("status")) {
                                clickPostRes.put("status", getMeta.get("status"));
                            }
                            log.info("[cancelReverseAtClickPost] response : " + response.toString());
                            if (response.getResponseCode().equals(HttpStatus.OK)) {
                                if (getMeta.get("success").equals(true)) {
                                    log.info("[cancelReverseAtClickPost] Reverse Pickup Successfully canceled : " + responseEntity);
                                } else {
                                    log.info("[cancelReverseAtClickPost] Reverse Pickup Not canceled : " + getMeta.toString());
                                }
                            }else if(clickPostRes.getOrDefault("status",200).equals(CLICKPOST_CANCELLED_STATUS)){
                                log.info("[cancelReverseAtClickPost] returnId : {}, return already cancelled at clickpost", returnOrder.getId());
                            }
                        }
                    } catch (Exception e) {
                        log.error("[cancelReverseAtClickPost] Exception occurred : ", e);
                    }
                }
            }
        }
        log.info("[cancelReverseAtClickPost] clickPostRes : {}", clickPostRes);
        return clickPostRes;
    }

    public void setReceivingFlagToNoOnReturnAccept(String status, String currentStatus, ReturnDetail returnOrder, UwOrderDTO uwOrderDTO) {
        boolean setReceivingFlagToNoOnReturnAccept = false;
        if (ReturnStatus.RETURN_NEED_APPROVAL.getStatus().equalsIgnoreCase(currentStatus)
                || ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus().equalsIgnoreCase(currentStatus)) {
            if (ReturnStatus.RETURN_ACCEPTED.getStatus().equalsIgnoreCase(status)) {
                setReceivingFlagToNoOnReturnAccept = true;
            }
        }
        if (setReceivingFlagToNoOnReturnAccept) {
            boolean isB2bOrder = returnUtil.isB2BOrder(uwOrderDTO);
            returnOrder = returnOrderActionService.findReturnOrderById(returnOrder.getId()).orElse(null);
            if (null != returnOrder && "yes".equalsIgnoreCase(returnOrder.getReceivingFlag())) {
                returnOrder.setReceivingFlag("no");
                returnOrderActionService.saveReturnOrder(returnOrder);
            }
            if (isB2bOrder) {
                updateB2BUwITemIdReturnDetail(uwOrderDTO);
            }
            log.info("updating receiving flag to no as its wrongly set to yes in return need approval cycle for customer order with return id " + returnOrder.getId());
        }
    }

    public void rejectRefundRequest(int returnId, String remarks) {
        RefundRejectRequest refundRejectRequest = new RefundRejectRequest();
        refundRejectRequest.setIdentifierType(IdentifierType.RETURN_ID);
        refundRejectRequest.setIdentifierValue(String.valueOf(returnId));
        refundRejectRequest.setRemarks(remarks);
        refundUtilsService.rejectRefundRequest(refundRejectRequest);
    }

    public ReturnOrderDTO getReturnOrderDto(ReturnDetail returnOrder) {
        ReturnOrderDTO returnOrderDTO = null;
        if (Objects.nonNull(returnOrder)) {
            returnOrderDTO = objectMapper.convertValue(returnOrder, ReturnOrderDTO.class);
        }
        log.info("[getReturnOrderDto] returnOrderDTO : {}", returnOrderDTO);
        return returnOrderDTO;
    }


    public void updateB2BUwITemIdReturnDetail(UwOrderDTO uwOrderDTO) {
        Integer b2bReferenceUwItemId = uwOrderDTO.getB2bRefrenceItemId();
        if (null != b2bReferenceUwItemId) {
            ReturnDetailItem b2bReturnOrderItem = returnOrderActionService.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(b2bReferenceUwItemId);
            ReturnDetail b2bReturnOrder = returnOrderActionService.findReturnOrderById(b2bReturnOrderItem.getReturnId()).orElse(null);
            if (null != b2bReturnOrder) {
                b2bReturnOrder.setReceivingFlag("no");
                returnOrderActionService.saveReturnOrder(b2bReturnOrder);
            }
        }
    }

    public void updateB2BUwITemIdReturnDetail(UwOrderDTO uwOrderDTO, String status, String returnEventName, String currentStatus) {
        Integer b2bReferenceUwItemId = uwOrderDTO.getB2bRefrenceItemId();
        if (null != b2bReferenceUwItemId) {
            ReturnDetailItem b2bReturnOrderItem = returnOrderActionService.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(b2bReferenceUwItemId);
            if(b2bReturnOrderItem != null && !RETURN_PENDING_APPROVAL.equalsIgnoreCase(currentStatus)) {
                returnOrderActionService.findReturnOrderById(b2bReturnOrderItem.getReturnId()).ifPresent(b2bReturnOrder -> returnEventService.createReturnEvent(b2bReturnOrder.getRequestId(), b2bReturnOrder.getId(), returnEventName, status));
            }
        }
    }


    private void cancelExchangeIfExistOnReturnCancel(ReturnEvent returnEvent, ReturnDetail returnOrder, Integer orderId, ReturnDetailUpdateRequestDto request) {
        ExchangeOrdersDTO exchangeOrdersDTO = request.getExchangeOrdersDTO();
        if(Objects.isNull(exchangeOrdersDTO)) {
            List<ExchangeOrdersDTO> exchangeOrdersDTOS = Objects.requireNonNull(orderOpsFeignClient.getExchangeAndCancellationDetails(null, returnOrder.getId()).getBody()).getExchangeOrdersDTOList();
            exchangeOrdersDTO = (null != exchangeOrdersDTOS && !CollectionUtils.isEmpty(exchangeOrdersDTOS)) ? exchangeOrdersDTOS.get(0) : null;
        }
        if (Objects.nonNull(exchangeOrdersDTO) && request.getStatus().equalsIgnoreCase(ReturnStatus.CANCELLED.getStatus()) && !Constant.ORDER_STATE_OR_STATUS.EXCHANGE_CANCELLED_SO_CANCELLING_RETURN.equalsIgnoreCase(request.getComments()) && !(Constant.ORDER_STATE_OR_STATUS.CANCELING_RPU_FOR_REASSIGNING_COURIER.equalsIgnoreCase(request.getComments()))) {
            log.info("Return order is cancelled hence need to cancel exchange order as well" + exchangeOrdersDTO.getExchangeIncrementId());
            CancelRequest cancelRequest = generateCancelRequest(request.getUsername());
            log.info("Proceeding to cancel the exchange order " + exchangeOrdersDTO.getExchangeIncrementId());
            ExchangeOrdersDTO finalExchangeOrdersDTO = exchangeOrdersDTO;
            String eventRemarks = null;
            CompletableFuture<Map<String, String>> exchangeCancelResponse = CompletableFuture.supplyAsync(() -> orderOpsFeignClient.orderCancel(cancelRequest, finalExchangeOrdersDTO.getExchangeIncrementId(), true));
            exchangeCancelResponse.thenAccept(cancelResponse -> {
                System.out.println("[exchangeCancelResponse] is " + cancelResponse);
                returnEventService.createReturnEvent(returnEvent.getReturnRequestId(), returnOrder.getId(), Constant.EVENT.CANCEL_EXCHANGE_ON_RETURN_CANCEL, cancelResponse.get("message"));
            });
            log.info("Updating refund_request for returnId " + returnOrder.getId());
            returnEventService.createReturnEvent(returnEvent.getReturnRequestId(), returnOrder.getId(), Constant.EVENT.REFUND_REJECT_ON_EXCHANGE_CANCELLATION, cancelRequest.getReasonDetail());
            rejectRefundRequest(request.getReturnId(), "order-ops: " + cancelRequest.getReasonDetail());
            String comment = "return cancelled so exchange is also cancelled : return order id : " + exchangeOrdersDTO.getIncrementId() + " exchange order id " + exchangeOrdersDTO.getIncrementId();
            updateOrderComment(orderId,comment);
            log.info("Cancellation completed for exchange order " + exchangeOrdersDTO.getExchangeIncrementId());
        }
    }


    private void cancelDispensing(ReturnDetail returnOrder, OrdersDTO ordersDTO, ReturnCourierDetail returnCourierDetail) {
        log.info("ReturnUpdateServiceImpl:cancelDispensing cancel for dispensing return id :" + returnOrder.getId() + " , incrementId :" + returnOrder.getIncrementId());
        DispensingDTO dispensingDTO = orderOpsFeignClient.getDispensingDetails(returnOrder.getIncrementId()).getBody();
        Boolean cancelDispensingOutput = false;

        if (returnCourierDetail != null && Constant.COURIER.LKART.equalsIgnoreCase(returnCourierDetail.getReverseCourier())) {

            if (null != dispensingDTO) {
                String comment = "";
                if (Constant.Dispensing.LEAD_EXT_STATUS.CLOSED_BOTH.equalsIgnoreCase(dispensingDTO.getLeadExtStatus()) || Constant.Dispensing.LEAD_EXT_STATUS.CLOSED_FIELDEZ.equalsIgnoreCase(dispensingDTO.getLeadExtStatus()) || Constant.Dispensing.MARK_ATTENDED.YES.equalsIgnoreCase(dispensingDTO.getMarkAttended())) {
                    comment = "RPU cancelled, Lead Already Closed in FieldEZ";
                } else if (null != dispensingDTO.getFieldEzstatus() && dispensingDTO.getSyncedToFieldez() == 1) {
                    comment = "RPU cancelled, Lead synced to FieldEZ";
                } else {
                    comment = "RPU cancelled, Lead NOT synced to FieldEZ";
                }
                dispensingDTO.setSyncedToFieldez(2);
                dispensingDTO.setReason("Cancel RPU");
                dispensingDTO.setLeadExtStatus(Constant.Dispensing.LEAD_EXT_STATUS.CLOSED_BOTH);
                dispensingDTO.setShipmentClosedBy(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
                //todo pussy to consumer
                boolean isCancelDispensing = Boolean.TRUE.equals(orderOpsFeignClient.updateDispensingOrder(dispensingDTO).getBody());
                if (isCancelDispensing && null != ordersDTO) {
                    cancelDispensingOutput = true;
                    updateOrderComment(ordersDTO.getOrderId(),comment);
                }
            }
        }
        log.info("ReturnUpdateServiceImpl:cancelDispensing RESPONSE cancel for dispensing return id :" + returnOrder.getId() + " , cancelDispensingOutput :" + cancelDispensingOutput);
    }

    private void setReturnOrderUpdateResponse(ReturnDetailsUpdateResponse response, String status, String message) {
        response.setStatus(status);
        response.setMsg(message);
    }

    private CancelRequest generateCancelRequest(String username) {
        CancelRequest cancelRequest = new CancelRequest();
        cancelRequest.setCancellationType("full_cancellation");
        cancelRequest.setPaymentMethod("exchange");
        cancelRequest.setReasonId(262);
        cancelRequest.setReasonDetail("return cancelled before pickup so cancelling exchange");
        cancelRequest.setSource("vsm");
        cancelRequest.setInitiatedBy(username);
        cancelRequest.setCancelRefund(true);
        return cancelRequest;
    }

    private void updateOrderComment(Integer orderId, String orderComment) {
        CommentDTO commentDTO = CommentDTO.builder()
                .orderId(orderId)
                .comment(Map.of("comment", orderComment, "comment_type", "cust"))
                .build();
        kafkaService.pushToKafka(ORDER_COMMENT_QUEUE, String.valueOf(orderId), commentDTO);
    }
}
