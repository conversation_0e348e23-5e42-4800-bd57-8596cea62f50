package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailAddressUpdate;
import com.lenskart.returnrepository.repository.ReturnOrderAddressUpdateRepository;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReversePickupTatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ReturnHeadingPlaceHolderValueFetchPickupDateStrategy implements IReturnHeadingPlaceHolderValueFetchStrategy {
    @Autowired
    IReversePickupTatService reversePickupTatService;
    @Autowired
    ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;
    @Autowired
    IReturnOrderActionService returnOrderActionService;

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse) {
        ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
        ReturnStatusHeadingDetail returnStatusHeadingDetail = null;
        returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
        try {
            if (null != returnDetail) {
                ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                if (null != returnOrderAddressUpdate && null != returnOrderAddressUpdate.getPostcode()) {
                    Date pickupEstimateDate = reversePickupTatService.getPickupTatNew(String.valueOf(returnOrderAddressUpdate.getPostcode()), returnDetail.getReturnCreateDatetime(), returnDetail.getId());
                    if (null != pickupEstimateDate) {

                        if (null != returnStatusHeadingDetail) {
                            returnStatusHeadingDetail.setEstimatedDate(pickupEstimateDate.getTime());
                        }
                    } else {
                        if (null != returnStatusHeadingDetail) {
                            returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                        }
                    }
                }
            }
        }catch (Throwable e){
            log.error("exception in getReturnHeadingsPlaceHolderValue", e);
            if(null != returnStatusHeadingDetail){
                returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
            }
        }
    }

    @Override
    public void getReturnTimelinePlaceHolderValue(ReturnTimeLine returnTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if(null != returnDetailsResponse){
            try {
                ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
                if (null != returnDetail) {
                    ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                    if (null != returnOrderAddressUpdate && null != returnOrderAddressUpdate.getPostcode()) {
                        Date pickupEstimateDate = reversePickupTatService.getPickupTatNew(String.valueOf(returnOrderAddressUpdate.getPostcode()), returnDetail.getReturnCreateDatetime(), returnDetail.getId());
                        if (null != pickupEstimateDate) {
                            returnTimeLine.setReturnEstimatedDate(pickupEstimateDate.getTime());
                        }else{
                            returnTimeLine.setReturnSubStatus("");
                        }
                    }
                }else{
                    returnTimeLine.setReturnSubStatus("");
                }
            }catch (Throwable e){
                log.error("exception in getReturnHeadingsPlaceHolderValue", e);
                if(null != returnTimeLine){
                    returnTimeLine.setReturnSubStatus("");
                }
            }
        }
    }

    @Override
    public void getRefundTimelinePlaceHolderValue(RefundTimeLine refundTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if(null != returnDetailsResponse){
            ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            try {
                if (null != returnStatusHeadingDetail) {
                    if(null != returnStatusHeadingDetail.getEstimatedDate()) {
                        refundTimeLine.setRefundEstimatedDate(returnStatusHeadingDetail.getEstimatedDate());
                    }else{
                        refundTimeLine.setRefundSubStatus("");
                    }
                }
            }catch (Throwable e){
                log.error("exception in getReturnHeadingsPlaceHolderValue", e);
                if(null != refundTimeLine){
                    refundTimeLine.setRefundSubStatus("");
                }
            }
        }
    }

    @Override
    public void getExchangeTimelinePlaceHolderValue(ExchangeTimeLine exchangeTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if(null != returnDetailsResponse){
            ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            try {
                if (null != returnStatusHeadingDetail) {
                    if(null != returnStatusHeadingDetail.getEstimatedDate()) {
                        exchangeTimeLine.setExchangeDispatchDate(returnStatusHeadingDetail.getEstimatedDate());
                    }else{
                        exchangeTimeLine.setExchangeSubStatus("");
                    }
                }
            }catch (Throwable e){
                log.error("exception in getReturnHeadingsPlaceHolderValue", e);
                if(null != exchangeTimeLine){
                    exchangeTimeLine.setExchangeSubStatus("");
                }
            }
        }
    }

    public void getReturnHistoryPlaceHolderValue(ReturnStatusHistory returnStatusHistory, ReturnDetailsResponse returnDetailsResponse){

    }

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse, List<RefundDetails> refundDetailsList, ReturnStatusHeadingDetail returnStatusHeadingDetail, Long incrementId, Date createdAt, String anyReceivingPoint) {

    }
}
