package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.cache.CacheLoadObserver;
import com.lenskart.returncommon.model.dto.DecisionTableRefundDTO;
import com.lenskart.returncommon.model.dto.MvcOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundResponseDTO;
import com.lenskart.returncommon.model.response.ItemResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returncommon.model.response.ReturnPolicyResponse;
import com.lenskart.returnrepository.entity.RefundRules;
import com.lenskart.returnrepository.entity.StoreAppointmentOrders;
import com.lenskart.returnrepository.repository.RefundRulesRepository;
import com.lenskart.returnrepository.repository.StoreAppointmentOrdersRepository;
import com.lenskart.returnservice.elasticsearch.service.RefundRulesCache;
import com.lenskart.returnservice.service.IMvcService;
import com.lenskart.returnservice.service.IRefundRuleWrapper;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RefundRuleWrapperImpl implements IRefundRuleWrapper, CacheLoadObserver {

    @Autowired
    private RefundRulesCache refundRulesCache;

    @Autowired
    private RefundRulesRepository refundRulesRepository;

    @Autowired
    private StoreAppointmentOrdersRepository storeAppointmentOrdersRepository;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Value("${refund.rule.elastic.search.read:false}")
    boolean loadFromCache;

    boolean rulesLoaded = false;

    @Autowired
    private IMvcService mvcService;

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Value("${mvc.starting.score: 9}")
    private Integer mvcStartingScore;

    @Override
    public ReturnRefundResponseDTO fetchRule(DecisionTableRefundDTO decisionTableRefundDTO) {
        RefundRules refundRules = null;
        Integer defaultRuleId = null;
        log.info("[fetchRule] Entered into func. uwitemId {} decisionTableRefundDTO {}", decisionTableRefundDTO.getUwItemId(), decisionTableRefundDTO);
        try {
            MvcOrderDTO mvcOrderResponse = mvcService.getMvcOrdersResponse("UW_ITEM_ID", String.valueOf(decisionTableRefundDTO.getUwItemId()));
            if("web".equalsIgnoreCase(decisionTableRefundDTO.getReturnInitiatedSource()) && mvcOrderResponse != null && mvcOrderResponse.getCustomerScore() > mvcStartingScore){
                ItemResponse itemsResponse = objectMapper.readValue(mvcOrderResponse.getReturnRefundRuleResponse(), ItemResponse.class);
                return getReturnRefundResponseDTO(itemsResponse);
            }else if(mvcOrderResponse != null && mvcOrderResponse.getCustomerScore() != null){
                decisionTableRefundDTO.setCustomerScore(mvcOrderResponse.getCustomerScore());
            }

            String sysPref = systemPreferenceService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_KEYS.STORE_APPOINTMENT,Constant.SYSTEM_PREFERENCE_KEYS.STORE_APPOINTMENT_DAYS, 1, TimeUnit.HOURS);
            Integer daysBefore = sysPref!=null?Integer.valueOf(sysPref):7; //7days
            Date toDate = new Date();
            Date fromDate = getLastNDays(toDate, daysBefore);

            log.info("daysBefore value:{} and toDate:{} and fromDate:{} ",daysBefore,toDate,fromDate);
            StoreAppointmentOrders storeAppointmentOrders=storeAppointmentOrdersRepository.findAppointmentByUwItemIdAndSource(decisionTableRefundDTO.getUwItemId(),decisionTableRefundDTO.getReturnInitiatedSource(),fromDate,toDate);
            log.info("storeAppointmentOrders:{}",storeAppointmentOrders);
            if(storeAppointmentOrders!=null){
                log.info("storeAppointmentOrders id:{}",storeAppointmentOrders.getId());
                if(storeAppointmentOrders.getReturnRefundRuleResponse()!=null) {
                    ReturnRefundResponseDTO savedReturnRefundResponseDTO = objectMapper.readValue(storeAppointmentOrders.getReturnRefundRuleResponse(), ReturnRefundResponseDTO.class);
                    log.info("savedReturnRefundResponseDTO from storeAppointment Orders:{}", savedReturnRefundResponseDTO);
                    getDtoForStoreAppointment(savedReturnRefundResponseDTO, storeAppointmentOrders, daysBefore);
                    if(savedReturnRefundResponseDTO.getId()!=0 && savedReturnRefundResponseDTO.getId()!=-1){
                        return savedReturnRefundResponseDTO;
                    }
                }
            }


            log.info("decisionTableRefundDTO check isLensOnly:{}",decisionTableRefundDTO.getIsLensOnly());

            if (loadFromCache && rulesLoaded) {
                refundRules = refundRulesCache.getRefundRulesById(decisionTableRefundDTO);
                log.info("[fetchRule] Loading rules from cache for uwItemId {}", decisionTableRefundDTO.getUwItemId());
            } else {
                refundRules = fetchRuleFromDatabase(decisionTableRefundDTO);
                log.info("[fetchRule] Loading rules from db for uwItemId {}", decisionTableRefundDTO.getUwItemId());
            }
        } catch (Throwable e) {
            log.error("Caught exception  while fetching  refund rules: {}", e.getMessage(), e);
            refundRules = fetchRuleFromDatabase(decisionTableRefundDTO);
        }
        log.info("[fetchRule] first refundRules matched: {}", refundRules);
        if (null != refundRules && null != refundRules.getOverrideWarrantyPeriod() && refundRules.getOverrideWarrantyPeriod()) {
            Integer new_warranty = decisionTableRefundDTO.getItemWarrantyPeriod();
            RefundRules refundRules1 = null;
            if (null != new_warranty) {
                if (new_warranty < decisionTableRefundDTO.getReturnPeriod()) {
                    try {
                        if (loadFromCache && rulesLoaded) {
                            refundRules1 = refundRulesCache.getRefundRulesByIdAndNotOverrideWarranty(decisionTableRefundDTO);
                        } else {
                            refundRules1 = fetchRuleFromDatabaseNotOverrideWarranty(decisionTableRefundDTO);
                        }
                    } catch (Throwable e) {
                        log.error("Caught exception  while fetching  refund rules: {}", e.getMessage(), e);
                        if (loadFromCache) {
                            refundRules1 = fetchRuleFromDatabaseNotOverrideWarranty(decisionTableRefundDTO);
                        }
                    }
                    if (null == refundRules1) {
                        defaultRuleId = -1;
                    }
                    return getReturnRefundResponseDTO(refundRules1, defaultRuleId);
                }
            }
        }
        return getReturnRefundResponseDTO(refundRules, defaultRuleId);
    }

    private Date getLastNDays(Date date, Integer daysBefore) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -daysBefore);
        return cal.getTime();
    }
    public Map<Integer, ReturnPolicyResponse> fetchRuleInBulk(Map<Integer,DecisionTableRefundDTO> decisionTableRefundDTOMap) {
        Map<Integer, ReturnPolicyResponse> refundRulesMap = new HashMap<>();
        refundRulesMap = refundRulesCache.getRefundRulesByIdBulk(decisionTableRefundDTOMap);
        return refundRulesMap;
    }


    private RefundRules fetchRuleFromDatabase(DecisionTableRefundDTO decisionTableRefundDTO) {
        return refundRulesRepository.findRuleTriggered(decisionTableRefundDTO.getReverseType(), decisionTableRefundDTO.getReturnInitiatedSource(),
                decisionTableRefundDTO.getTriggerPoint(), decisionTableRefundDTO.getIsPseudoGatepass(), decisionTableRefundDTO.getIsBranded(),
                decisionTableRefundDTO.getIsQcPass(), decisionTableRefundDTO.getReturnPeriod(), decisionTableRefundDTO.getIsAccessoryMissing(),
                decisionTableRefundDTO.getIsLastPiece(), decisionTableRefundDTO.getIsLensOnly(), decisionTableRefundDTO.getAmountValidity().intValue(),
                decisionTableRefundDTO.getBlacklistedPhoneNumbers(), decisionTableRefundDTO.getExchangeAllowed(), decisionTableRefundDTO.getDraftReturnMethod(),
                decisionTableRefundDTO.getNavChannel(), decisionTableRefundDTO.getReturnReasons(), decisionTableRefundDTO.getCategory(),
                decisionTableRefundDTO.getBlacklistedPincodes(), decisionTableRefundDTO.getCountryCode(), decisionTableRefundDTO.getInsurancePolicy(),
                decisionTableRefundDTO.getPaymentMethod(), decisionTableRefundDTO.getCustomerScore(), decisionTableRefundDTO.getStoreScore());
    }

    private ReturnRefundResponseDTO getReturnRefundResponseDTO(RefundRules refundRules, Integer defaultRuleId) {
        log.info("[getReturnRefundResponseDTO] refundRules: {}", refundRules);
        ReturnRefundResponseDTO returnRefundResponseDTO = new ReturnRefundResponseDTO();
        if (null != defaultRuleId) {
            returnRefundResponseDTO.setId(defaultRuleId);
        }
        if (null != refundRules) {
            returnRefundResponseDTO.setRefundMethod(refundRules.getRefundMethod());
            returnRefundResponseDTO.setDoRefund(refundRules.getDoRefund());
            returnRefundResponseDTO.setIsReturnable(refundRules.getIsReturnable());
            returnRefundResponseDTO.setAction(refundRules.getAction());
            returnRefundResponseDTO.setExchangeOrderDispatch(refundRules.getExchangeOrderDispatch());
            returnRefundResponseDTO.setRefundDispatch(refundRules.getRefundDispatch());
            returnRefundResponseDTO.setReturnEligibilityPeriod(refundRules.getReturnEligibilityPeriod());
            returnRefundResponseDTO.setId(refundRules.getId());
        }
        returnRefundResponseDTO.setWarrantyActive(refundRules != null);
        return returnRefundResponseDTO;
    }

    private ReturnRefundResponseDTO getReturnRefundResponseDTO(ItemResponse itemsResponse){
        ReturnRefundResponseDTO returnRefundResponseDTO = new ReturnRefundResponseDTO();
        returnRefundResponseDTO.setIsReturnable(itemsResponse.isReturnable());
        returnRefundResponseDTO.setDoRefund(itemsResponse.isDoRefund());
        returnRefundResponseDTO.setRefundMethod(CollectionUtils.isEmpty(itemsResponse.getRefundMethods()) ? "NA" : String.join(",", itemsResponse.getRefundMethods()));
        returnRefundResponseDTO.setReturnEligibilityPeriod(itemsResponse.getReturnEligibilityPeriod());
        return returnRefundResponseDTO;
    }

    private void getDtoForStoreAppointment(ReturnRefundResponseDTO savedReturnRefundResponseDTO,StoreAppointmentOrders storeAppointmentOrders,Integer daysBefore) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date returnEligibilityDate = sdf.parse(savedReturnRefundResponseDTO.getReturnEligibleTillDate());
        Date appointmentCreatedAt=storeAppointmentOrders.getCreatedAt();

        LocalDate returnEligibilityDateLocal = returnEligibilityDate.toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();

        LocalDate appointmentCreatedAtLocal = appointmentCreatedAt.toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();

        int daysBetween = Math.toIntExact(ChronoUnit.DAYS.between(appointmentCreatedAtLocal, returnEligibilityDateLocal));
        log.info("Days difference: {}", daysBetween);


        if(daysBetween<daysBefore && daysBetween>0){
            savedReturnRefundResponseDTO.setExtensionInReturnEligibilityPeriod(daysBefore-daysBetween);
            savedReturnRefundResponseDTO.setReturnEligibilityPeriod(savedReturnRefundResponseDTO.getReturnEligibilityPeriod()+savedReturnRefundResponseDTO.getExtensionInReturnEligibilityPeriod());
        }else if (daysBetween<0) {
            log.info("Store Appointment Booking was done after the returnEligiblilityPeriodDate: {}", daysBetween);
            savedReturnRefundResponseDTO.setId(-1);
        }
    }

    private RefundRules fetchRuleFromDatabaseNotOverrideWarranty(DecisionTableRefundDTO decisionTableRefundDTO) {
        return refundRulesRepository.findRuleTriggeredNotOverrideWarranty(decisionTableRefundDTO.getReverseType(), decisionTableRefundDTO.getReturnInitiatedSource(),
                decisionTableRefundDTO.getTriggerPoint(), decisionTableRefundDTO.getIsPseudoGatepass(), decisionTableRefundDTO.getIsBranded(),
                decisionTableRefundDTO.getIsQcPass(), decisionTableRefundDTO.getReturnPeriod(), decisionTableRefundDTO.getIsAccessoryMissing(),
                decisionTableRefundDTO.getIsLastPiece(), decisionTableRefundDTO.getIsLensOnly(), decisionTableRefundDTO.getAmountValidity().intValue(),
                decisionTableRefundDTO.getBlacklistedPhoneNumbers(), decisionTableRefundDTO.getExchangeAllowed(), decisionTableRefundDTO.getDraftReturnMethod(),
                decisionTableRefundDTO.getNavChannel(), decisionTableRefundDTO.getReturnReasons(), decisionTableRefundDTO.getCategory(),
                decisionTableRefundDTO.getBlacklistedPincodes(), decisionTableRefundDTO.getCountryCode(), decisionTableRefundDTO.getInsurancePolicy(),
                decisionTableRefundDTO.getPaymentMethod(), decisionTableRefundDTO.getCustomerScore(), decisionTableRefundDTO.getStoreScore());
    }

    @Override
    public void onCacheLoadComplete() {
        rulesLoaded = true;
        log.info("Observer worked");
        log.info("[loadCache] end time : " + new Date(System.currentTimeMillis()));
    }
}
