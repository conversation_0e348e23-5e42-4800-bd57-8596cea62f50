package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto;
import com.lenskart.returncommon.model.dto.RuleContextDTO;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.response.CancelAndConvertEventRuleResponse;

import java.util.List;
import java.util.Map;

public interface ICancelAndConvertRuleService {
    CancelAndConvertEventRuleResponse evaluateAndGetActions(PaymentMode paymentMode, RuleContextDTO request);
    Map<PaymentMode, List<CancelAndConvertRuleDto>> fetchAndPopulateRules();
}
