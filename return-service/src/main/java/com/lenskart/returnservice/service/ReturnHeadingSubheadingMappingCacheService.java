package com.lenskart.returnservice.service;

import com.lenskart.returnrepository.entity.ReturnHeadingSubheadingMapping;

public interface ReturnHeadingSubheadingMappingCacheService {

    ReturnHeadingSubheadingMapping getMapping(String returnSource, String returnStatus, String dispatchPoint, String refundMethod, String refundMode, Boolean exchangeExpired, Boolean exchangeCreated, Boolean exchangeOrderDispatched, String refundStatus);

    ReturnHeadingSubheadingMapping getReturnHeadingSubheadingMapping(String returnSource, String returnStatus, String refundStatus, String refundMethod, String refundMode);
}
