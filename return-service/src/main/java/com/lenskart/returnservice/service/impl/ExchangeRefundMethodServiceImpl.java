package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.ItemRequestDTO;
import com.lenskart.returncommon.model.dto.RefundMethodRequestByMagentoDto;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequestDTO;
import com.lenskart.returncommon.model.dto.RefundMethodsResponse;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returncommon.model.response.ItemResponse;
import com.lenskart.returncommon.model.response.ReturnRefundEligibilityResponse;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IExchangeRefundMethodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class ExchangeRefundMethodServiceImpl implements IExchangeRefundMethodService {

    @Autowired
    private ReturnRefundEligibilityServiceImpl returnRefundEligibilityService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Override
    public RefundMethodsResponse findExchangeRefundMethod(Integer magentoItemId, String source, RefundMethodRequestByMagentoDto refundMethodRequestByMagentoDto) throws Exception {
        RefundMethodsResponse refundMethodsResponse = new RefundMethodsResponse();
        UwOrderDTO uwOrderDTO = null != refundMethodRequestByMagentoDto
                && null != refundMethodRequestByMagentoDto.getUwOrderDTO()
                ? refundMethodRequestByMagentoDto.getUwOrderDTO()
                : getUwOrder(magentoItemId);
        if (uwOrderDTO == null) {
            log.info("[ExchangeRefundMethodServiceImpl][findExchangeRefundMethod] uwOrder entity not found for magentoItemId: {}", magentoItemId);
            return refundMethodsResponse;
        }

        log.info("UwOrder: {}", uwOrderDTO);

        ReturnRefundEligibilityRequestDTO request = createReturnRefundEligibilityRequest(magentoItemId, source, uwOrderDTO);
        log.info("ReturnRefundEligibilityRequest: {}", request);

        ReturnRefundEligibilityResponse response = returnRefundEligibilityService.getReturnRefundEligibility(request);
        List<ItemResponse> itemResponses = Optional.ofNullable(response)
                .map(ReturnRefundEligibilityResponse::getItemsResponseList)
                .orElse(Collections.emptyList());

        log.info("itemResponses: {}", itemResponses);

        return buildRefundMethodsResponse(magentoItemId, itemResponses);
    }

    private UwOrderDTO getUwOrder(int magentoItemId) {
        return Optional.ofNullable(orderOpsFeignClient.getUwOrderInfo(IdentifierType.MAGENTO_ITEM_ID.name(), String.valueOf(magentoItemId)))
                .map(ResponseEntity::getBody)
                .orElse(null);
    }

    private ReturnRefundEligibilityRequestDTO createReturnRefundEligibilityRequest(Integer magentoItemId, String source, UwOrderDTO uwOrderDTO) {
        ItemRequestDTO itemRequest = new ItemRequestDTO();
        itemRequest.setMagentoItemId(magentoItemId);
        itemRequest.setQcStatus("Pass");

        ReturnRefundEligibilityRequestDTO request = new ReturnRefundEligibilityRequestDTO();
        request.setSource(source);
        request.setOrderId(uwOrderDTO.getIncrementId());
        request.setItemList(Collections.singletonList(itemRequest));
        return request;
    }

    private RefundMethodsResponse buildRefundMethodsResponse(Integer magentoItemId, List<ItemResponse> itemResponses) {
        RefundMethodsResponse refundMethodsResponse = new RefundMethodsResponse();
        refundMethodsResponse.setMagentoItemId(magentoItemId);

        if (!itemResponses.isEmpty()) {
            ItemResponse itemResponse = itemResponses.get(0);
            refundMethodsResponse.setRefundMethods(itemResponse.getRefundMethods());
            refundMethodsResponse.setIsExchange(itemResponse.isExchange());
        } else {
            refundMethodsResponse.setRefundMethods(Collections.emptyList());
            refundMethodsResponse.setIsExchange(false);
        }

        log.info("RefundMethodsResponse: {}", refundMethodsResponse);
        return refundMethodsResponse;
    }
}
