package com.lenskart.returnservice.service.impl;

import com.lenskart.ccautils.constants.CosmosConstants;
import com.lenskart.ccautils.util.CosmosEventPushUtil;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.EventType;
import com.lenskart.returncommon.model.enums.LKCountry;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.model.response.StoreCreditRefundDetails;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.SystemPreferenceRepository;
import com.lenskart.returnservice.factory.cosmospayloadfactory.CosmosEventPayloadFactory;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ReturnUtil {

    @Autowired
    private CosmosEventPushUtil cosmosEventPushUtil;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    protected List<String> LK_FACILITY_CODE_LIST = new ArrayList<>();
    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;
    @PostConstruct
    public void init() {
        SystemPreference facility = systemPreferenceRepository.findTopByGroupAndKey("revised_edd", "facility");
        if (facility != null && facility.getValue() != null) {
            LK_FACILITY_CODE_LIST = Arrays.asList(facility.getValue().split("\\s*,\\s*"));
        }
    }
    public Boolean isB2BOrder(UwOrderDTO uwOrder){
        return !StringUtils.isEmpty(uwOrder.getProductDeliveryType()) && Constant.PRODUCT_DELIVERY_TYPE.B2B.contains(uwOrder.getProductDeliveryType());
    }

    public LKCountry findLkCountry(OrdersHeaderDTO ordersHeaderDTO){
        LKCountry lkCountry =null;
        try {
            if (ordersHeaderDTO != null) {
                lkCountry = ordersHeaderDTO.getLkCountry().equalsIgnoreCase(LKCountry.SG.value()) ? LKCountry.SG : LKCountry.IN;
                return lkCountry;
            }
        }catch (Exception e){
            log.error("[findLkCountry] exception", e);
        }
        lkCountry = LKCountry.IN;
        return lkCountry;
    }

    public String getPaymentMethod(OrdersDTO ordersDTO){
        return ordersDTO != null ? ordersDTO.getMethod() : null;
    }

    public List<String> getPhysicalFacilityCodes(){
        return LK_FACILITY_CODE_LIST;
    }

    public String getEventName(String returnStatus, String source, String returnMethod) {
        switch(returnStatus){
            case "return_need_approval":
                return CosmosConstants.Events.RETURN_NEED_APPROVAL.toString();
            case "return_need_approval_from_whse":
                return CosmosConstants.Events.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.toString();
            case "return_received":
                if(ReturnSources.POS.getSource().equalsIgnoreCase(source) && "StoreReceiving".equalsIgnoreCase(returnMethod)) {
                    return CosmosConstants.Events.RETURN_RECEIVED_AT_STORE.toString();
                } else if(ReturnSources.WAREHOUSE.getSource().equalsIgnoreCase(source) && "DirectReceiving".equalsIgnoreCase(returnMethod)) {
                    return CosmosConstants.Events.RETURN_RECEIVED_AT_WAREHOUSE.toString();
                }
            case "new_reverse_pickup":
                return CosmosConstants.Events.NEW_RETURN_PICKUP.toString();
            case "return_expected_pos":
                return CosmosConstants.Events.RETURN_EXPECTED_POS.toString();
            case "return_expected_wh":
                return CosmosConstants.Events.RETURN_EXPECTED_WH.toString();
            default:
                return CosmosConstants.Events.NEW_RETURN_PICKUP.toString();
        }
    }

    public void pushCosmosEvents(ReturnCreationResponse returnCreationResponse, Map<Long, ReturnItemDTO> magentoItemToReturnItemMap, ReturnCreationRequestDTO returnCreationRequest) {

        if (returnCreationResponse == null || returnCreationResponse.getResult() == null ||
                returnCreationResponse.getResult().getReturns() == null || magentoItemToReturnItemMap == null ||
                returnCreationRequest == null) {
            log.warn("[pushCosmosEvents] Skipping execution due to null input parameters.");
            return;
        }
        returnCreationResponse.getResult().getReturns().forEach(returns -> {
            Map<String, Object> payload = CosmosEventPayloadFactory.getEventPayload(
                    returns.getEventName(), returnCreationResponse, magentoItemToReturnItemMap
            );
            if (payload!=null) {
                String eventName = returns.getEventName();
                Integer returnId = returns.getReturnId();
                Integer incrementId = returnCreationResponse.getIncrementId();

                log.info("[pushCosmosEvents]:: Pushing event '{}' for returnId: {}, payload: {}", eventName, returnId,payload);
                cosmosEventPushUtil.pushEventToCosmosUsingReturnIds(payload, eventName, incrementId, Collections.singletonList(returnId), null);
            } else {
                log.warn("[pushCosmosEvents]:: Skipping event '{}' for returnId '{}' due to empty payload", returns.getEventName(), returns.getReturnId());
            }
        });
    }

    public String trimString(String input) {
        if (input == null) {
            return null;
        }
        return input.length() <= 250 ? input : input.substring(0, 250);
    }

    public EventType getEventType(StoreCreditRefundDetails scRefundDetails){
        boolean fastRefundFlag = scRefundDetails.isFastRefundFlag();
        boolean rtoFlag = scRefundDetails.isRtoFlag();
        boolean returnFlag = scRefundDetails.isReturnFlag();
        boolean partialCancellationFlag = scRefundDetails.isPartialCancellationFlag();
        EventType eventType = EventType.RTO;
        if(returnFlag) eventType = EventType.RETURN;
        if(rtoFlag) eventType = EventType.RTO;
        if(fastRefundFlag) eventType = EventType.FAST_REFUND;
        if(partialCancellationFlag) eventType = EventType.PARTIAL_CANCELLATION;
        return eventType;
    }

    public boolean isFeatureEligibleByRolloutPlan(Integer incrementId, String key) {
        boolean isFeatureEligible = false;
        try {
            int rolloutPercent = 0;
            String value = systemPreferenceService.getSystemPreferenceValues("headless_returns_group", key, 10, TimeUnit.MINUTES);
            if (!StringUtils.isEmpty(value)) {
                rolloutPercent = Integer.parseInt(value.trim());
            }
            int lastTwoDigits = incrementId % 100;
            isFeatureEligible = lastTwoDigits < rolloutPercent;
        } catch (Exception e) {
            log.error("[isFeatureEligibleByRolloutPlan] error: ", e);
        }
        log.info("[isFeatureEligibleByRolloutPlan] isFeatureEligible: {} for incrementId: {}", isFeatureEligible, incrementId);
        return isFeatureEligible;
    }

}
