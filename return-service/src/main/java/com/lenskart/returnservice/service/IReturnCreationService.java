package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.PostReturnCreationActivityDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.exception.ReturnNotFound;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returnrepository.entity.ReturnRequest;

public interface IReturnCreationService {
    ReturnCreationResponse createReturn(PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, ReturnCreationRequestDTO returnCreationRequest, ReverseCourierDetail reverseCourierDetail, String source, ReturnRequest returnRequest) throws Exception;
    void performPostReturnCreationActivities(PostReturnCreationActivityDTO postReturnCreationActivityDTO)  throws Exception;
}
