package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returnservice.service.ReturnOrderEnrichmentService;
import com.lenskart.returnservice.service.impl.enrichers.CourierDetailEnricher;
import com.lenskart.returnservice.service.impl.enrichers.FallbackEnricher;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class ReturnOrderEnrichmentServiceImpl implements ReturnOrderEnrichmentService {

    @Autowired
    private CourierDetailEnricher courierEnricher;

    @Autowired
    private FallbackEnricher fallbackEnricher;

    @PostConstruct
    public void setupChain() {
        courierEnricher.setNextEnricher(fallbackEnricher);
    }

    @Override
    public void populateTrackingDetails(List<ReturnOrderDTO> returnOrders) {
        if (returnOrders == null || returnOrders.isEmpty()) {
            return;
        }

        List<Integer> ids = returnOrders.stream()
                .map(ReturnOrderDTO::getId)
                .toList();

        courierEnricher.preload(ids);
        fallbackEnricher.preload(ids);

        for (ReturnOrderDTO dto : returnOrders) {
            courierEnricher.enrich(dto);
        }
    }
}
