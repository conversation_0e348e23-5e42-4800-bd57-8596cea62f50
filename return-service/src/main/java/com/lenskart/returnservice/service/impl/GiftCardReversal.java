package com.lenskart.returnservice.service.impl;

import static com.lenskart.returncommon.utils.Constant.CES;
import static com.lenskart.returncommon.utils.Constant.RETURN;
import static com.lenskart.returncommon.utils.Constant.RETURN_GIFT_CARD;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.platform.fl.utils.dto.FinanceEventDto;
import com.lenskart.platform.fl.utils.dto.giftCard.Data;
import com.lenskart.platform.fl.utils.dto.giftCard.GeneralJournalHeader;
import com.lenskart.platform.fl.utils.dto.giftCard.GiftCardD365Request;
import com.lenskart.platform.fl.utils.dto.giftCard.GiftCardMetaData;
import com.lenskart.platform.fl.utils.dto.giftCard.GiftCardResponseDto;
import com.lenskart.platform.fl.utils.dto.giftCard.JournalLineDto;
import com.lenskart.platform.fl.utils.dto.giftCard.JunoGiftCardResponse;
import com.lenskart.platform.fl.utils.dto.giftCard.ProgramBreakup;
import com.lenskart.platform.fl.utils.enums.EventChannel;
import com.lenskart.platform.fl.utils.finance.service.FinanceService;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.ProductDto;
import com.lenskart.returncommon.model.dto.ReturnDetailsDTO;
import com.lenskart.returncommon.model.dto.ReturnOrderItemDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.FinanceReturnEvent;
import com.lenskart.returncommon.model.enums.SyncStatus;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.response.CreditNoteResponse;
import com.lenskart.returncommon.model.response.CreditNoteResponse.CreditNoteResponseData;
import com.lenskart.returncommon.model.response.CreditNoteResponse.DocumentContent;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.FinanceSyncReturnTrackingEvent;
import com.lenskart.returnrepository.repository.FinanceSyncReturnTrackingRepository;
import com.lenskart.returnservice.feignclient.FDSFeignClient;
import com.lenskart.returnservice.service.IJunoService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.helper.InventoryDataHelper;
import com.lenskart.returnservice.service.helper.ReturnEventServiceHelper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@RequiredArgsConstructor
public class GiftCardReversal {

    private final ObjectMapper mapper;

    private final FinanceService financeService;

    private final FDSFeignClient fdsFeignClient;

    private final InventoryDataHelper inventoryDataHelper;

    private final IJunoService junoService;

    private final IReturnOrderActionService returnOrderActionService;

    private final ReturnEventServiceHelper returnEventServiceHelper;

    @Value("${gift-card.enabled:true}")
    boolean isGiftCardEnabled;

    @Value("${gift.card.legal.entity:LKIN}")
    String giftCardLegalEntity;

    @Value("${gift.card.journal.name:RZ_GIFTCAR}")
    String giftCardJournalName;

    @Value("${gift.card.account.number:411148}")
    String giftCardAccountNumber;

    @Value("${gift.card.account.offset.number:119042}")
    String giftCardAccountOffsetNumber;

    @Autowired
    private FinanceSyncReturnTrackingRepository financeSyncReturnTrackingRepository;

    public GiftCardResponseDto checkIfGiftCardApplicableForUwItems(ReturnCreateRequest giftCardEligibilityRequestDto) {
        GiftCardResponseDto responseDto = processGiftCardIfApplicableForUwItems(giftCardEligibilityRequestDto, null, RETURN);
        if(!responseDto.getHttpStatusCode().equals(HttpStatus.NOT_ACCEPTABLE.value())) {
            SyncStatus syncStatus = responseDto.getHttpStatusCode().equals(HttpStatus.OK.value()) ? SyncStatus.SUCCESS : SyncStatus.FAILED;
            Map<String,Integer> data = responseDto.getData() == null ? new HashMap<>() : (Map<String,Integer>) responseDto.getData();
            if(SyncStatus.FAILED.equals(syncStatus)) {
                saveReturnTrackingEvent(data.getOrDefault("return_id", null),giftCardEligibilityRequestDto.getUwItemId(), FinanceReturnEvent.RETURN_GIFT_CARD, syncStatus, responseDto.getMessage(), giftCardEligibilityRequestDto.getRetryCount());
            }
        }
        return responseDto;
    }

    public GiftCardResponseDto checkIfGiftCardApplicableForUwItemsList(List<Integer> uwItemIdsList) {
        ReturnCreateRequest giftCardEligibilityRequestDto = new ReturnCreateRequest();
        for (Integer uwItemsId : uwItemIdsList) {
            giftCardEligibilityRequestDto.setUwItemId(uwItemsId);
            checkIfGiftCardApplicableForUwItems(giftCardEligibilityRequestDto);
        }
        return buildGiftCardResponseDto(HttpStatus.OK, "Successfully validated shipments based on uwItemIds.", null);

    }

    public GiftCardResponseDto processGiftCardIfApplicableForUwItems(ReturnCreateRequest giftCardEligibilityRequestDto, String legalEntity, String source) {
        List<Integer> uwItemIds = List.of(giftCardEligibilityRequestDto.getUwItemId());
        try {
            if (!isGiftCardEnabled) {
                log.info(" gift card flow is not enabled currently");
                return buildGiftCardResponseDto(HttpStatus.NOT_ACCEPTABLE, "Gift card flow is blocked currently!", null);
            }
            PurchaseOrderDetailsDTO orderDetails = inventoryDataHelper.getOrdersDetailsInfo("UW_ITEM_ID", String.valueOf(uwItemIds.get(0)));
            legalEntity = getLegalEntity(orderDetails);
            List<UwOrderDTO> uwOrderList = inventoryDataHelper.getUwOrders(null, orderDetails);
            if (CollectionUtils.isEmpty(uwOrderList)) {
                throw new RuntimeException("UwItem Ids not present in UwOrders table");
            }
            if (isGiftCardEligibleForOrder(orderDetails.getItemWisePrices())) {
                Long clientEventId = returnEventServiceHelper.createFinanceEvent(giftCardEligibilityRequestDto, RETURN_GIFT_CARD);
                UwOrderDTO uwOrder = uwOrderList.get(0);
                if ("B2B".equalsIgnoreCase(uwOrderList.get(0).getProductDeliveryType()) && !"FOFOB2B".equalsIgnoreCase(uwOrderList.get(0).getNavChannel())) {
                    PurchaseOrderDetailsDTO b2bOrderDetails = inventoryDataHelper.getOrdersDetailsInfo("UW_ITEM_ID", String.valueOf(uwItemIds.get(0)));
                    List<UwOrderDTO> b2bUwOrderList = inventoryDataHelper.getUwOrders(null, b2bOrderDetails);
                    uwOrder = b2bUwOrderList.get(0);   //warehouse order
                }

                ReturnDetailsDTO returnDetailsDTO = returnOrderActionService.getReturnDetailsByIdentifier("UW_ITEM_ID", Collections.singletonList(String.valueOf(uwItemIds.get(0))), true);
    
                if(Objects.isNull(returnDetailsDTO) || CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrderItems()) || CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrders())) {
                    log.info("[GiftCardService][processGiftCardIfApplicableForUwItems]returnDetails does not exists for UwItemId : {}", uwItemIds.get(0));
                    return buildGiftCardResponseDto(HttpStatus.INTERNAL_SERVER_ERROR, "returnDetails does not exists for UwItemId : " + uwItemIds.get(0), null);
                }

                ReturnOrderItemDTO returnOrderItemDTO = returnDetailsDTO.getReturnOrderItems().get(0);

                if(Objects.isNull(returnOrderItemDTO)) {
                    log.info("[GiftCardService][processGiftCardIfApplicableForUwItems]returnOrderItem does not exists for UwItemId : {}", uwItemIds.get(0));
                    throw new RuntimeException("returnOrderItem does not exists");
                }

                GiftCardD365Request giftCardPayload = generateGiftCardPayload(uwItemIds,
                    String.valueOf(uwOrder.getIncrementId()),
                    returnOrderItemDTO.getReturnId(),
                    uwOrder.getUwItemId(),
                    source, orderDetails);
                giftCardPayload.setEventCorrelationIdentifier(giftCardPayload.getGeneralJournalHeader().getOrderNo());
                giftCardPayload.setEventUniqueIdentifier(giftCardPayload.getGeneralJournalHeader().getDocumentNo());
                String rawEvent = mapper.writeValueAsString(giftCardPayload);
                FinanceEventDto financeEventDto = FinanceEventDto.builder()
                        .legalEntity(legalEntity)
                        .country(getCountry(orderDetails))
                        .source(CES)
                        .eventType(RETURN_GIFT_CARD)
                        .eventData(giftCardPayload)
                        .rawEventData(rawEvent)
                        .clientEventId(String.valueOf(clientEventId))
                        .channel(EventChannel.KAFKA)
                        .createdBy(CES)
                        .build();
                financeService.saveEvent(financeEventDto);
                return buildGiftCardResponseDto(HttpStatus.OK, "Gift card payload sent successfully!", Map.of("return_id", returnOrderItemDTO.getReturnId()));
            }
            return buildGiftCardResponseDto(HttpStatus.NOT_ACCEPTABLE, "Not eligible for gift card trigger given uwItems", null);
        } catch (Exception e) {
            log.error("Failed to send kafka message for gift_card for uwItems : {} ", uwItemIds, e);
            if ("RETURN".equalsIgnoreCase(source)) {
                return buildGiftCardResponseDto(HttpStatus.INTERNAL_SERVER_ERROR, "Some server error happened at finance-consumer's end : " + e.getMessage(), null);
            }
        }
        return null;
    }

    private String getLegalEntity(PurchaseOrderDetailsDTO orderDetails) {
        if(orderDetails.getOrdersHeaderDTO() != null) {
            return Constant.LK + orderDetails.getOrdersHeaderDTO().getLkCountry();
        }
        return giftCardLegalEntity;
    }

    private String getCountry(PurchaseOrderDetailsDTO orderDetails) {
        if(orderDetails.getOrdersHeaderDTO() != null) {
            return orderDetails.getOrdersHeaderDTO().getLkCountry();
        }
        return "IN";
    }

    public boolean isGiftCardEligibleForOrder(List<ItemWisePriceDetailsDTO> itemWisePricesList) {
        if (!CollectionUtils.isEmpty(itemWisePricesList)) {
            itemWisePricesList = itemWisePricesList.stream()
                    .filter(item -> item.getGiftCardDiscount() != null && item.getGiftCardDiscount() > 0)
                    .collect(Collectors.toList());
        }
        return !CollectionUtils.isEmpty(itemWisePricesList) ? true : false;
    }

    public GiftCardD365Request getPayloadViaApi(List<Integer> uwItemIds) {
        try {
            PurchaseOrderDetailsDTO orderDetails = inventoryDataHelper.getOrdersDetailsInfo("UW_ITEM_ID", String.valueOf(uwItemIds.get(0)));
            List<UwOrderDTO> uwOrderList = inventoryDataHelper.getUwOrders(null, orderDetails);
            if (CollectionUtils.isEmpty(uwOrderList)) {
                throw new RuntimeException("UwItem Ids not present in UwOrders table");
            }
            UwOrderDTO uwOrder = uwOrderList.get(0);
            ReturnDetailsDTO returnDetailsDTO = returnOrderActionService.getReturnDetailsByIdentifier("UW_ITEM_ID", Collections.singletonList(String.valueOf(uwItemIds.get(0))), true);

            if(Objects.isNull(returnDetailsDTO) || CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrderItems()) || CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrders())) {
                log.info("[GiftCardService][getPayloadViaApi]ReturnDetailsDTO does not exists for UwItemId : {}", uwItemIds.get(0));
                throw new RuntimeException("Return Order Item does not exists");
            }
            ReturnOrderItemDTO returnOrderItemDTO = returnDetailsDTO.getReturnOrderItems().get(0);
            if(Objects.nonNull(returnOrderItemDTO)) {
                log.info("[GiftCardService][getPayloadViaApi]ReturnDetailsDTO does not exists for UwItemId : {}", uwItemIds.get(0));
                throw new RuntimeException("returnOrderItem does not exists");
            }
            return generateGiftCardPayload(uwItemIds,
                                           String.valueOf(uwOrder.getIncrementId()),
                                           returnOrderItemDTO.getReturnId(),
                                           uwOrder.getUwItemId(),
                                           "RETURN", orderDetails);
        } catch (Exception e) {
            log.error("Error while generating gift card payload for uwitemIds : {}", uwItemIds, e);
            return null;
        }
    }

    private GiftCardResponseDto buildGiftCardResponseDto(HttpStatus httpStatus, String message, Map<String, Integer> giftCardEligibilityCheckDto) {
        return GiftCardResponseDto.builder().httpStatusCode(httpStatus.value()).message(message).data(giftCardEligibilityCheckDto).build();
    }

    private GiftCardD365Request generateGiftCardPayload(List<Integer> uwItemIds, String orderId, Integer returnId, Integer uwItemId, String source, PurchaseOrderDetailsDTO orderDetails) throws JsonProcessingException {
        List<GiftCardMetaData> giftCardMetaData = generateGiftCardMetaData(orderId, returnId, source, uwItemIds, orderDetails);
        if(Objects.isNull(giftCardMetaData) || giftCardMetaData.isEmpty()) {
            throw new RuntimeException("Gift card meta data not found for uwItemIds " + uwItemIds);
        }
        return GiftCardD365Request.builder().generalJournalHeader(generateGiftCardHeader(returnId, uwItemId, orderId, source, uwItemIds, giftCardMetaData, orderDetails)).build();
    }

    private List<GiftCardMetaData> generateGiftCardMetaData(String orderId, Integer returnId, String source, List<Integer> uwItemIds, PurchaseOrderDetailsDTO orderDetails) throws JsonProcessingException {
        List<UwOrderDTO> uwOrderList = inventoryDataHelper.getUwOrders(null, orderDetails);
        if (CollectionUtils.isEmpty(uwOrderList)) {
            throw new RuntimeException("UwItem Ids not present in UwOrders table");
        }
        if (CollectionUtils.isEmpty(uwOrderList)) {
            log.error("GiftCardService : error UwItemIds not present in UwOrder for : {}", (uwItemIds));
            throw new RuntimeException("GiftCardService : error UwItemIds not present in UwOrder for : " + (uwItemIds));
        }
        Set<Integer> itemIds = uwOrderList.stream().map(UwOrderDTO::getItemId).collect(Collectors.toSet());
        OrdersDTO orderDto = inventoryDataHelper.getOrders(null, orderDetails);
        if (orderDto == null) {
            throw new RuntimeException("GiftCardService : error Item ids not present in Orders table for : " + itemIds);
        }

        HashMap<Integer, Integer> magentoIdAndUwItemId = new HashMap<>();
        for (UwOrderDTO uwOrder : uwOrderList) {
            magentoIdAndUwItemId.put(orderDto.getMagentoItemId(), uwOrder.getUwItemId());
        }
        Set<String> magentoIds = new HashSet<>();
        magentoIds.add(String.valueOf(orderDto.getMagentoItemId()));

        return buildGiftCardMeta(orderId, returnId, source, uwItemIds, magentoIds, magentoIdAndUwItemId, orderDetails);
    }

    private List<GiftCardMetaData> buildGiftCardMeta(String orderId, Integer returnId, String source, List<Integer> uwIemIds, Set<String> magentoIds, HashMap<Integer, Integer> magentoIdAndUwItemId, PurchaseOrderDetailsDTO orderDetails) throws JsonProcessingException {
        List<GiftCardMetaData> giftCardMetaDataList = new ArrayList<>();
        List<String> magentoIdsStringList = new ArrayList<>(magentoIds);
        String itemIdsParam = String.join(",", magentoIdsStringList);
        Double expenseAmount;

        log.info("GiftCardService : Magento Id and GiftCard Id mapping : {}", mapper.writeValueAsString(magentoIdAndUwItemId));

        JunoGiftCardResponse junoGiftCardResponse = null;
        try {
            junoGiftCardResponse = junoService.getGiftCardDetails(Integer.valueOf(orderId), itemIdsParam);
        } catch (Exception e) {
            log.error("GiftCardService : Exception occurred while fetching juno gift card response  for returnId : {}", returnId, e);
        }
        if (Objects.nonNull(junoGiftCardResponse) && Objects.nonNull(junoGiftCardResponse.getData())) {

            List<Data> data = junoGiftCardResponse.getData();
            for (Data result : data) {
                List<ProgramBreakup> programBreakupList = result.getProgramBreakup();
                for (ProgramBreakup programBreakup : programBreakupList) {
                    expenseAmount = programBreakup.getAmount();
                    GiftCardMetaData giftCardMetaData = createGiftCardMetaData(result.getItemId().intValue(), programBreakup.getProgramId(), magentoIdAndUwItemId.get(result.getItemId().intValue()));

                    if ("SCM".equalsIgnoreCase(source)) {
                        giftCardMetaData.setExpenseAmount(expenseAmount);
                    } else if ("RETURN".equalsIgnoreCase(source)) {
                        giftCardMetaData.setExpenseReversal(expenseAmount);
                    } else {
                        log.error("GiftCardService : error unknown source {} found for returnId {} ", source, returnId);
                    }
                    log.info("GiftCardService : Preparing to persist GiftCard Meta : {}", giftCardMetaData);
                    giftCardMetaDataList.add(giftCardMetaData);

                }
            }
        } else {
            log.error("GiftCardService : error in processing Juno api for fetching expense amount for magentoId : {} , error : {}", magentoIds, junoGiftCardResponse.getMessage());
            return giftCardMetaDataList;   // terminate flow as gift_card_meta_data cant be populated as juno api's breakup is not present
        }
        return giftCardMetaDataList;
    }

    private GiftCardMetaData createGiftCardMetaData(Integer magentoId, String programId, Integer referenceId) {
        return GiftCardMetaData.builder().magentoId(magentoId).programId(programId).referenceId(referenceId).build();
    }

    private GeneralJournalHeader generateGiftCardHeader(Integer returnId, Integer uwItemId, String orderId, String source, List<Integer> uwItemIds, List<GiftCardMetaData> giftCardMetaData, PurchaseOrderDetailsDTO orderDetails) {
        GeneralJournalHeader generalJournalHeader = new GeneralJournalHeader();
        if(giftCardMetaData.isEmpty()) {
            throw new RuntimeException("Can not generate payload since gift card meta data is null for uwItemIds " + uwItemIds);
        }
        try {
            generalJournalHeader.setShipmentId(String.valueOf(returnId));
            generalJournalHeader.setOrderType("Returned order");
            generalJournalHeader.setDocumentNo(orderId + "_" + returnId + "_" + uwItemId);
            generalJournalHeader.setJournalName(giftCardJournalName);
            generalJournalHeader.setLegalEntity(giftCardLegalEntity);
            generalJournalHeader.setOrderNo(String.valueOf(orderId));
            OrdersHeaderDTO orderHeader = orderDetails.getOrdersHeaderDTO();
            if(orderHeader == null) {
                throw new RuntimeException("Unable to find orders header for order id " + orderId);
            }
            generalJournalHeader.setCountry(orderHeader.getLkCountry());
            List<UwOrderDTO> uwOrderList = inventoryDataHelper.getUwOrders(null, orderDetails);
            if (CollectionUtils.isEmpty(uwOrderList)) {
                throw new RuntimeException("UwItem Ids not present in UwOrders table");
            }
            generalJournalHeader.setNavChannel(uwOrderList.get(0).getNavChannel());
            generalJournalHeader.setJournalLines(getJournalLines(returnId, uwItemId, uwOrderList.get(0), giftCardMetaData, source,orderHeader));
            log.info("[GiftCardSyncServiceImplV1][getGiftCardPayload] Gift Card payload : {}", generalJournalHeader);
            return generalJournalHeader;
        } catch (Exception e) {
            log.info("[GiftCardSyncServiceImplV1][getGiftCardPayload] Error while forming payload for gift for returnId {}, uw_item_id {} and source {}", returnId, uwItemId, source);
            throw e;
        }
    }

    public List<JournalLineDto> getJournalLines(Integer returnId, Integer uwItemId, UwOrderDTO uwOrder, List<GiftCardMetaData> giftCardMetaDataList, String source, OrdersHeaderDTO orderHeader) {
        Set<Integer> journalLineDtosSet = new HashSet<>();
        List<JournalLineDto> journalLineDtos = new ArrayList<>();
        try {
            for (GiftCardMetaData giftCardMetaData : giftCardMetaDataList) {
                if (journalLineDtosSet.contains(giftCardMetaData.getMagentoId())) {
                    continue;
                }
                //Added to skip same magento ID with different program ID later
                journalLineDtosSet.add(giftCardMetaData.getMagentoId());

                JournalLineDto journalLineDto = new JournalLineDto();

                journalLineDto.setTransDate(LocalDateTime.now().toLocalDate().toString());
                journalLineDto.setAccount(String.valueOf(giftCardAccountNumber));
                journalLineDto.setOffsetAccount(String.valueOf(giftCardAccountOffsetNumber));
                journalLineDto.setDescription("GC Expense Booking");
                journalLineDto.setCurrency("INR");
                journalLineDto.setItemId(String.valueOf(giftCardMetaData.getMagentoId()));

                double expense;
                if ("RETURN".equalsIgnoreCase(source)) {
                    expense = giftCardMetaDataList.stream().filter(order -> order.getMagentoId().equals(giftCardMetaData.getMagentoId())).mapToDouble(GiftCardMetaData::getExpenseReversal).sum();
                    journalLineDto.setAmount(Double.toString(-expense));
                    journalLineDto.setDocumentDate(getReturnCreationDate(returnId));
                }
                if (isOnlineOrder(orderHeader)) {
                    journalLineDto.setCostCenter("WEB-079");
                } else if (isHTOOrder(uwOrder)) {
                    journalLineDto.setCostCenter("HTO-075");
                } else {
                    journalLineDto.setCostCenter("OFF-085");
                }
                //Document number and qty to be updated for future reference
                journalLineDtos.add(journalLineDto);
            }
        } catch (Exception e) {
            log.error("[GiftCardSyncServiceImplV1][getJournalLines] Error while creating Journal lines for gift card processing for returnId {}, uw_item_id {} and source {}", returnId, uwItemId, source);
            throw e;
        }
        return journalLineDtos;
    }

    private String getReturnCreationDate(Integer returnId) {
        try {
            String searchParam = String.format("documentSourceRefId.eq:%s___documentSource.eq:RETURN___documentType:CREDIT_NOTE", returnId);
            ResponseEntity<String> response = fdsFeignClient.searchDocuments(searchParam);
            if (response != null && response.getBody() != null && response.getStatusCode().is2xxSuccessful()) {
                CreditNoteResponse creditNoteResponse = mapper.readValue(response.getBody(), CreditNoteResponse.class);
                if (creditNoteResponse != null) {
                    CreditNoteResponseData creditNoteResponseData = creditNoteResponse.getData();
                    List<DocumentContent> documentContentList = creditNoteResponseData != null ? creditNoteResponseData.getContent() : Collections.emptyList();
                    if (!CollectionUtils.isEmpty(documentContentList)) {
                        DocumentContent documentContent = documentContentList.get(0);
                        if (documentContent != null && documentContent.getDocumentDate() != null) {
                            return documentContent.getDocumentDate().split("T")[0];
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[GiftCardService][getReturnCreationDate] Exception occurred while fetching return creation date for returnId {}", returnId, e);
        }
        return LocalDateTime.now().toLocalDate().toString();
    }

    private void saveReturnTrackingEvent(Integer returnId, Integer uwItemId, FinanceReturnEvent eventType, SyncStatus status, String statusRemarks, Integer retryCount) {
        FinanceSyncReturnTrackingEvent returnTrackingEvent = FinanceSyncReturnTrackingEvent.builder()
                .returnId(returnId)
                .uwItemId(uwItemId)
                .identifierType(eventType.name())
                .status(status.name())
                .statusRemarks(statusRemarks)
                .retryCount(retryCount)
                .build();
        financeSyncReturnTrackingRepository.save(returnTrackingEvent);
    }
    /**
     * Check if the given order is an HTO order
     * @param uwOrder uwOrder object
     * @return true if it is an HTO order, false otherwise
     */
    private boolean isHTOOrder(UwOrderDTO uwOrder) {
        List<ProductDto> productList = inventoryDataHelper.getProductsByIds(Collections.singletonList(uwOrder.getProductId()));
        Map<Integer, ProductDto> productMap = productList.stream()
                .collect(Collectors.toMap(ProductDto::getProductId, p -> p));
        ProductDto productDto = productMap.getOrDefault(uwOrder.getProductId(),null);
        if(Objects.nonNull(productDto)){
            return productDto.getProductId() == 47552;
        }
        return false;
    }

    /**
     * Checks if the given orders header is an online order or not.
     * @param ordersHeader orders header object
     * @return true if it is an online order, false otherwise
     */
     public boolean isOnlineOrder(OrdersHeaderDTO ordersHeader) {
        if(ordersHeader.getFacilityCode() != null && !ordersHeader.getFacilityCode().equals("0")){
            return false;
        }
        return true;
    }


}

