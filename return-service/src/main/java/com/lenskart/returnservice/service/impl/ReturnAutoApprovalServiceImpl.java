package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.request.AutoApprovalRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IReturnAutoApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReturnAutoApprovalServiceImpl implements IReturnAutoApprovalService {

    @Autowired
    private IKafkaService kafkaService;

    @Override
    public void autoApproveReturn(ReturnCreationRequestDTO returnCreationRequest, ReturnCreationResponse refundRequestResponse) {
        log.info("[ReturnAutoApprovalServiceImpl][autoApproveReturn] enter");
        if(validateRequest(returnCreationRequest,refundRequestResponse)) {
            AutoApprovalRequest autoApprovalRequest=new AutoApprovalRequest();

            autoApprovalRequest.setReturnCreationRequest(returnCreationRequest);
            autoApprovalRequest.setRefundRequestResponse(refundRequestResponse);

            kafkaService.pushToKafka("return_auto_approval_queue", String.valueOf(refundRequestResponse.getReturnId()), autoApprovalRequest);
        }
        log.info("[ReturnAutoApprovalServiceImpl][autoApproveReturn] exit");
    }

    private boolean validateRequest(ReturnCreationRequestDTO returnCreationRequest, ReturnCreationResponse refundRequestResponse) {
        try {
            if (returnCreationRequest == null || refundRequestResponse == null) {
                log.info("[ReturnAutoApprovalServiceImpl][autoApproveReturn] Invalid arguments: returnCreationRequest or refundRequestResponse is null");
                return false;
            }

        }catch (Exception e){
            log.error("[ReturnAutoApprovalServiceImpl][autoApproveReturn] error in validateRequest of ReturnAutoApprovalImpl: {}",e.getMessage());
            return false;
        }

        return true;
    }
}
