package com.lenskart.returnservice.service.impl;
/* Created by rajiv on 06/02/25 */

import com.lenskart.returnservice.service.CosmosEventPayloadProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
@Component
@Slf4j
public class ReturnUpdateCosmosEventPayLoadProvider implements CosmosEventPayloadProvider {

    @Override
    public Map<String, Object> getPayload(Object... params) {
        if (params == null || params.length < 1) {
            log.warn("[ReturnUpdateCosmosEventPayLoadProvider] Invalid parameters provided to getPayload. Params received: {}", Arrays.toString(params));
            return Collections.emptyMap();
        }
        String returnStatus = (String) params[1];
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("checkpoint", "headless_return_return_update");
        eventData.put("returnStatus", returnStatus.toLowerCase());
        return eventData;
    }
}
