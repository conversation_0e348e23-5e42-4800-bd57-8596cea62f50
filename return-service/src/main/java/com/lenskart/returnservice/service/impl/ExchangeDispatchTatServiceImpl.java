package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.TriggerPoint;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnservice.service.IExchangeDispatchTatService;
import com.lenskart.returnservice.service.IReversePickupTatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ExchangeDispatchTatServiceImpl implements IExchangeDispatchTatService {

    @Autowired
    IReversePickupTatService reversePickupTatService;
    @Override
    public Integer getExchangeIncrementId(ReturnDetailsResponse returnDetailsResponse) {
        ExchangeOrdersDTO exchangeOrder = returnDetailsResponse.getExchangeOrdersDTO();
        if(exchangeOrder!=null) return exchangeOrder.getExchangeIncrementId();

        return null;
    }

    @Override
    public Integer getWareHouseProcessingTat(ReturnDetailsResponse returnDetailsResponse) {
        UwOrderDTO uwOrder = returnDetailsResponse.getExchangeUwOrderDTO();
        if(uwOrder!=null && uwOrder.getExpectedDispatchDate()!=null){
            Calendar c1 = Calendar.getInstance();
            c1.setTime(uwOrder.getExpectedDispatchDate());
            Calendar c2 = Calendar.getInstance();
            c2.setTime(uwOrder.getCreatedAt());
            c1.set(Calendar.HOUR_OF_DAY,0);
            c1.set(Calendar.MINUTE,0);
            c1.set(Calendar.SECOND,0);
            c2.set(Calendar.HOUR_OF_DAY,0);
            c2.set(Calendar.MINUTE,0);
            c2.set(Calendar.SECOND,0);
            log.info("[getWareHouseProcessingTat] cal instance expectedDispatchTime: {} createdAt {}",c1.getTime(),c2.getTime());
            long diffInMs = c1.getTime().getTime() - c2.getTime().getTime();
            int diffInDays = (int) TimeUnit.DAYS.convert(diffInMs,TimeUnit.MILLISECONDS);
            log.info("[getWareHouseProcessingTat] incrementId: {} WareHouseProcessingTat: {}",returnDetailsResponse.getExchangeUwOrderDTO().getIncrementId(),diffInDays);
            return diffInDays;
        }
        return null;
    }

    @Override
    public Date getExchangeDispatchETA(ReturnDetailsResponse returnDetailsResponse, Integer reverseOrderIncrementId, String returnOrderPincode, Date createdAt) {
        Integer exchangeIncrementId = getExchangeIncrementId(returnDetailsResponse);
        Date reversePickupTat = reversePickupTatService.getPickupTatNew(returnOrderPincode, createdAt, returnDetailsResponse.getReturnId());
        if(exchangeIncrementId!=null){
            Integer exchangeOrderWareHouseProcessingTat = getWareHouseProcessingTat(returnDetailsResponse);
            if(TriggerPoint.CourierPickup.getName().equalsIgnoreCase(returnDetailsResponse.getExchangeDispatchPoint()) || "AnyReceivingPoint".equalsIgnoreCase(returnDetailsResponse.getExchangeDispatchPoint())) {
                Calendar c = Calendar.getInstance();
                c.setTime(reversePickupTat);
                c.add(Calendar.DATE,exchangeOrderWareHouseProcessingTat);
                log.info("[getExchangeDispatchETA] CourierPickup {}",c.getTime());
                return c.getTime();
            }
            else if(TriggerPoint.WHReceiving.getName().equalsIgnoreCase(returnDetailsResponse.getExchangeDispatchPoint())){
                Integer reverseDeliveryTat = reversePickupTatService.getReverseDeliveryDays(returnOrderPincode);
                Calendar c = Calendar.getInstance();
                c.setTime(reversePickupTat);
                c.add(Calendar.DATE, exchangeOrderWareHouseProcessingTat+reverseDeliveryTat);
                log.info("[getExchangeDispatchETA] WHReceiving {}",c.getTime());
                return c.getTime();
            } else{
                log.info("[getExchangeDispatchETA] WHReceiving {}",reversePickupTat);
                return reversePickupTat;
            }
        }
        return null;
    }
}
