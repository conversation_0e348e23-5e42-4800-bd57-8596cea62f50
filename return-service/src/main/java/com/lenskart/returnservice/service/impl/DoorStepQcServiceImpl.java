package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.ordermetadata.dto.ItemWiseGetAmountDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.dto.DoorstepQcEligibility;
import com.lenskart.returncommon.model.dto.ItemWiseGetAmount;
import com.lenskart.returncommon.model.dto.Reasons;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.DoorstepQcQuestionMapping;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.DoorstepQcQuestionMappingRepository;
import com.lenskart.returnservice.service.IDoorStepQcService;
import com.lenskart.returnservice.service.IJunoService;
import com.lenskart.returnservice.service.INexsFacilityService;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import com.lenskart.reversemodel.request.kafka.InitiateReverseRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class DoorStepQcServiceImpl implements IDoorStepQcService {

    @Autowired
    private INexsFacilityService nexsFacilityService;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    RedisTemplate<String, Object> redisTemplate;
    @Autowired
    DoorstepQcQuestionMappingRepository doorstepQcQuestionMappingRepository;

    @Autowired
    private IJunoService junoService;

    @Override
    public boolean addQcAtDoorstepDetails(ReverseCourierDetail reverseCourierDetail, UwOrderDTO uwOrder, InitiateReverseRequest initiateReverseRequest, List<com.lenskart.ordermetadata.dto.request.Reasons> reasons, ItemWiseAmountDTO itemWiseAmountDTO, ShippingStatusDetail shippingStatusDetail) {
        log.info("starting qc at door step for order "+uwOrder.getIncrementId());
        if(reverseCourierDetail.isQcAtDoorStepEligibleByCourierDetails()) {
            int reasonId = !CollectionUtils.isEmpty(reasons) ? reasons.get(0).getSecondaryReasonId() : 0;
            DoorstepQcEligibility isQcAtDoorStepEligible = checkQcAtDoorStepEligibility( uwOrder, reasonId, itemWiseAmountDTO, shippingStatusDetail);

            if(isQcAtDoorStepEligible==null || !isQcAtDoorStepEligible.isEligible()){
                log.info("order is not eligible for qc at doorstep {} {}",uwOrder.getUwItemId(), isQcAtDoorStepEligible);
                return false;
            }

            double n = getTestingCoverage();
            double random = Math.random();
            log.info("n for {} is {}, random={}", uwOrder.getUwItemId(), n, random);
            if (isQcAtDoorStepEligible.isEligible() && isEligibleNumber(initiateReverseRequest.getPickUpInfo().getPickupPhone()) && random < n) {
                log.info("order is eligible for qc so setting the remaining fields for order {} {} {} ",uwOrder.getIncrementId(), uwOrder.getUwItemId(), isQcAtDoorStepEligible);
                initiateReverseRequest.getAdditional().setRvpReason(isQcAtDoorStepEligible.getRvpReason());
                initiateReverseRequest.getAdditional().setSpecialInstructions(isQcAtDoorStepEligible.getSpecialInstruction());
                initiateReverseRequest.setQcAtDoorStepEligible(true);
                initiateReverseRequest.getAdditional().setQcType("doorstep");

                //brand images article number
                initiateReverseRequest.getShipmentDetails().getItems().forEach(item ->{
                    StringBuilder images = new StringBuilder();
                    String sn_sample_image = null;
                    String modelName = null;
                    Map<String, Object> productDetailsMap = junoService.getProductDetails(uwOrder.getProductId());
                    if(productDetailsMap!=null) {
                        images.append(getImageUrl(reverseCourierDetail, productDetailsMap));
                        modelName = (productDetailsMap.get(Constant.JUNO_PRODUCT.MODEL_NAME) != null ? (String) productDetailsMap.get(Constant.JUNO_PRODUCT.MODEL_NAME) : null);
                    }
                    Map<String,String> additionalReturnItem = item.getAdditional();
                    SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey("classification_group", uwOrder.getClassificationName());
                    SystemPreference snSampleImage = systemPreferenceService.findOneByGroupAndKey("Quality-Based_Pickup", "sn_sample_image_url");
                    String brand = uwOrder.getBrand();
                    if(Objects.nonNull(systemPreference) && !systemPreference.getValue().isEmpty()){
                        brand = systemPreference.getValue();
                    }
                    if(Objects.nonNull(snSampleImage) && !snSampleImage.getValue().isEmpty()){
                        sn_sample_image = snSampleImage.getValue();
                    }
                    addAccountCodeAndSnSampleImage(additionalReturnItem,reverseCourierDetail,images,sn_sample_image);

                    item.setSerial_no(modelName);
                    additionalReturnItem.put("images",images.toString());
                    additionalReturnItem.put("brand",brand);
                    item.setAdditional(additionalReturnItem);
                });
                log.info("successfully set payload for order "+uwOrder.getIncrementId());
                return true;
            }
            else if(isQcAtDoorStepEligible.isEligible()){
                log.info("order {} is eligible for qc but coverage is not under {} OR phone number {} is not eligible", uwOrder.getIncrementId(), n, initiateReverseRequest.getPickUpInfo().getPickupPhone());
                initiateReverseRequest.setQcAtDoorStepEligible(true);
                return false;
            }
        }
        log.info("order is not eligible for qc "+uwOrder.getIncrementId());
        return false;
    }

    private void addAccountCodeAndSnSampleImage(Map<String, String> additionalReturnItem, ReverseCourierDetail reverseCourierDetail, StringBuilder images, String sn_sample_image) {
        if (reverseCourierDetail != null && "Xpressbees_SN_QC".equalsIgnoreCase(reverseCourierDetail.getCourier())) {
            log.info("[addAccountCode] adding account_code XB_B2C_QC_SN");
            additionalReturnItem.put("account_code", "XB_B2C_QC_SN");
            images.append(",").append(sn_sample_image);
            log.info("[addQcAtDoorstepDetails] updated image url: {}",images.toString());
        }
    }

    private double getTestingCoverage() {
        String value = nexsFacilityService.getSystemPreferenceValues("testing_coverage","qc_at_doorstep" );
        if(StringUtils.isNotBlank(value)){
            return Double.parseDouble(value);
        }
        return 0.0;
    }

    private boolean isEligibleNumber(String pickupPhone) {
        List<String> phoneNumberList;
        try {
            phoneNumberList = systemPreferenceService.getValuesAsList("qc_at_doorstep", "phone_numbers");
        } catch (Exception e) {
            log.error("Error in isEligibleNumber for {} ", pickupPhone, e);
            return true;
        }
        return phoneNumberList==null || phoneNumberList.contains("ALL") || phoneNumberList.contains(pickupPhone);
    }

    private DoorstepQcEligibility checkQcAtDoorStepEligibility( UwOrderDTO uwOrder, int reasonId, ItemWiseAmountDTO itemWiseAmountDTO, ShippingStatusDetail shippingStatusDetail) {
        DoorstepQcEligibility doorstepQcEligibility = new DoorstepQcEligibility();
        doorstepQcEligibility.setIncrementId(uwOrder.getIncrementId());
        doorstepQcEligibility.setReasonId(reasonId);
        doorstepQcEligibility.setUwOrder(uwOrder);
        doorstepQcEligibility.setEligible(false);
        try {
            log.info("START checkQcAtDoorStepEligibility uw-item-id {}, reason-id:{}", uwOrder.getUwItemId(),reasonId);
            Double price=itemWiseAmountDTO.getTotalItemAmount();
            String navChannel = uwOrder.getNavChannel();
            doorstepQcEligibility.setNavChannel(navChannel);
            doorstepQcEligibility.setPrice(price);

            List<String> navChannelList = systemPreferenceService.getValuesAsList("qc_at_doorstep", "valid_nav_channel");
            if(!CollectionUtils.isEmpty(navChannelList) && navChannelList.stream().noneMatch(c -> c.equalsIgnoreCase(navChannel) || c.equalsIgnoreCase("ALL"))){
                log.info("checkQcAtDoorStepEligibility invalidNavChannel {} {} {} {}", navChannel, navChannelList, uwOrder.getUwItemId(), doorstepQcEligibility);
                return doorstepQcEligibility;
            }

            String minPriceSP = systemPreferenceService.getSystemPreferenceValues("qc_at_doorstep", "min_price", 6, TimeUnit.HOURS);
            Integer minPrice = StringUtils.isNotBlank(minPriceSP) ? Integer.parseInt(minPriceSP) : 0;
            if (price.compareTo(minPrice.doubleValue()) < 0) {
                log.info("checkQcAtDoorStepEligibility minPrice {} {} {} {}", price, minPrice, uwOrder.getUwItemId(), doorstepQcEligibility);
                return doorstepQcEligibility;
            }

            List<String> classificationList = systemPreferenceService.getValuesAsList("qc_at_doorstep", "valid_classification");
            doorstepQcEligibility.setClassification(Integer.parseInt(uwOrder.getClassification()));
            if(!CollectionUtils.isEmpty(classificationList) && classificationList.stream().noneMatch(c -> c.equalsIgnoreCase(uwOrder.getClassificationName()) || c.equalsIgnoreCase("ALL"))){
                log.info("checkQcAtDoorStepEligibility classificationList {} {} {} {}", uwOrder.getClassification(), classificationList, uwOrder.getUwItemId(), doorstepQcEligibility);
                return doorstepQcEligibility;
            }

            String period = getPeriod(shippingStatusDetail);
            doorstepQcEligibility.setPeriod(period);

            String redisKeyForDoorstepQuestionMapping = getRedisKeyForDoorstepQuestionMapping(Integer.parseInt(uwOrder.getClassification()), reasonId, period);
            DoorstepQcQuestionMapping doorstepQcQuestionMapping = null;
            try{doorstepQcQuestionMapping = (DoorstepQcQuestionMapping) redisTemplate.opsForValue().get(redisKeyForDoorstepQuestionMapping);} catch (Exception e){}
            if(doorstepQcQuestionMapping==null) {
                doorstepQcQuestionMapping = doorstepQcQuestionMappingRepository.findTopByClassificationAndReasonAndPeriodOrderByUpdatedAtDesc(Integer.parseInt(uwOrder.getClassification()), reasonId, period);
                try{redisTemplate.opsForValue().set(redisKeyForDoorstepQuestionMapping, doorstepQcQuestionMapping, 6, TimeUnit.HOURS);} catch (Exception e){}
            }

            if(doorstepQcQuestionMapping==null || !doorstepQcQuestionMapping.isQcRequired()){
                log.info("checkQcAtDoorStepEligibility doorstepQcQuestionMapping {} {} {}", doorstepQcQuestionMapping, uwOrder.getUwItemId(), doorstepQcEligibility);
                return doorstepQcEligibility;
            }

            doorstepQcEligibility.setEligible(true);
            doorstepQcEligibility.setSpecialInstruction(doorstepQcQuestionMapping.getSpecialInstruction());
            doorstepQcEligibility.setRvpReason(doorstepQcQuestionMapping.getRvpReason());
            log.info("END checkQcAtDoorStepEligibility passed for uw_item_id {} {}", uwOrder.getUwItemId(), doorstepQcEligibility);
            return doorstepQcEligibility;
        }catch (Exception e){
            log.error("checkQcAtDoorStepEligibility caught exception for {} {} {}", uwOrder.getIncrementId(), uwOrder.getUwItemId(), e.getMessage());
            return doorstepQcEligibility;
        }
    }

    private String getPeriod(ShippingStatusDetail shippingStatus) {
        String period ="return"; //Customer availing warranty period  (GT 21 days from complete time or GT 14 days from delivery time)
        try {
            if (shippingStatus != null) {
                Date completeTime = shippingStatus.getComplete_time();
                Date deliveryTime = shippingStatus.getDeliveredDate();

                Date today = new Date();
                long millisecondsPerDay = 24 * 60 * 60 * 1000;

                long deliveryDaysAfter = deliveryTime != null ? deliveryTime.getTime() + 21 * millisecondsPerDay : 0L; //21 days after delivery date
                long completeDaysAfter = completeTime != null ? completeTime.getTime() + 14 * millisecondsPerDay : 0L; //14 days after complete date

                if ((deliveryDaysAfter > 0L && (today).after(new Date(deliveryDaysAfter))) || (completeDaysAfter > 0L && (today).after(new Date(completeDaysAfter)))) {
                    period = "warranty";
                }
                log.info("Period for {} {} is {}", shippingStatus.getOrderNo(), shippingStatus.getUnicomOrderCode(), period);
            }
        }catch(Exception e){
            log.error("getPeriod caught error for {} {} ",  e.getMessage(), e);
        }
        return period;
    }

    private String getRedisKeyForDoorstepQuestionMapping(int classification, int reasonId, String period) {
        return "Question_Mapping_"+classification+"_"+period+"_"+reasonId;
    }

    private String getImageUrl(ReverseCourierDetail reverseCourierDetail, Map<String, Object> productDetailsMap) {
        String images;
//        String httpsLongCouriers = systemPreferenceService.getSystemPreferenceValues("qc_at_doorstep", "httpsLongCouriers", 6, TimeUnit.HOURS);
        String httpLongCouriers = systemPreferenceService.getSystemPreferenceValues("qc_at_doorstep", "httpLongCouriers", 6, TimeUnit.HOURS);
        String httpsShortCouriers = systemPreferenceService.getSystemPreferenceValues("qc_at_doorstep", "httpsShortCouriers", 6, TimeUnit.HOURS);
        String httpShortCouriers = systemPreferenceService.getSystemPreferenceValues("qc_at_doorstep", "httpShortCouriers", 6, TimeUnit.HOURS);

        if(StringUtils.isNotBlank(httpLongCouriers) && Arrays.stream(httpLongCouriers.split(",")).anyMatch(c->c.equalsIgnoreCase(reverseCourierDetail.getCourier()) || c.equalsIgnoreCase("ALL"))){
            images = productDetailsMap.get(Constant.JUNO_PRODUCT.HTTP_LONG_IMAGE_URL) != null ? (String) productDetailsMap.get(Constant.JUNO_PRODUCT.HTTP_LONG_IMAGE_URL) : null;
        }
        else if(StringUtils.isNotBlank(httpsShortCouriers) && Arrays.stream(httpsShortCouriers.split(",")).anyMatch(c->c.equalsIgnoreCase(reverseCourierDetail.getCourier()) || c.equalsIgnoreCase("ALL"))){
            images = productDetailsMap.get(Constant.JUNO_PRODUCT.HTTPS_SHORT_IMAGE_URL) != null ? (String) productDetailsMap.get(Constant.JUNO_PRODUCT.HTTPS_SHORT_IMAGE_URL) : null;
        }
        else if(StringUtils.isNotBlank(httpShortCouriers) && Arrays.stream(httpShortCouriers.split(",")).anyMatch(c->c.equalsIgnoreCase(reverseCourierDetail.getCourier()) || c.equalsIgnoreCase("ALL"))){
            images = productDetailsMap.get(Constant.JUNO_PRODUCT.HTTP_SHORT_IMAGE_URL) != null ? (String) productDetailsMap.get(Constant.JUNO_PRODUCT.HTTP_SHORT_IMAGE_URL) : null;
        }
        else{
            images = productDetailsMap.get(Constant.JUNO_PRODUCT.HTTPS_LONG_IMAGE_URL) != null ? (String) productDetailsMap.get(Constant.JUNO_PRODUCT.HTTPS_LONG_IMAGE_URL) : null;
        }
        return images;
    }
}
