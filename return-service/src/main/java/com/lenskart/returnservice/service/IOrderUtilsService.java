package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.DualRefundRequest;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returnrepository.entity.ReturnDetailItem;

import java.util.List;

public interface IOrderUtilsService {
    boolean getIsFullLKCashOrder(List<ItemWisePriceDetailsDTO> itemWisePriceDetailsDTOList);
    void insertOrderComment(String comment, Integer orderId);
    void callingBackSyncApi(Integer incrementId, List<UwOrderDTO> uwOrderDTOS, String checkPoint );
    void updateBackSyncStatus(List<ReturnDetailItem> returnItemList, String status, List<UwOrderDTO> uwOrderDTOS, Integer returnRequestId, Integer returnId);
    Boolean isB2BReturnItem(Object uwOrderOne, String checkPoint);
    List<String> getPersistentHubFacilities();
    PurchaseOrderDetailsDTO getPurchaseOrderDetails(String identifierType, String identifierValue);
    String getPaymentMethod(UwOrderDTO uwOrderDTO, List<UwOrderDTO> uwOrderDTOS, List<OrdersDTO> ordersDTOS, String orderMethod);
    void updateOrderStatus(DualRefundRequest request);

    String getOrderPriorityType(Integer itemId, Integer incrementId);
    boolean isPrioritizedOrder(String orderPriorityType);
}
