package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.enums.TriggerPoint;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.service.IExchangeItemDispatchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import com.lenskart.returncommon.model.enums.ReturnStatus;

@Service
@Slf4j
public class ExchangeItemDispatchServiceImpl implements IExchangeItemDispatchService {

    @Autowired
    private ReturnEventRepository returnEventRepository;

    private final String ANY_RECEIVING_POINT ="AnyReceivingPoint";
    private final String COURIER_PICKUP="CourierPickup";
    private final String POS_RECEIVING="POSReceiving";
    private final String WH_RECEIVING="WHReceiving";

    @Override
    public Boolean isDispatchable(Integer returnId, String ruleEngineStatusForDispatch) {
        log.info("[isDispatchable] returnId : {}, ruleEngineStatusForDispatch : {}", returnId, ruleEngineStatusForDispatch);
        if(TriggerPoint.ReturnInitiation.getName().equalsIgnoreCase(ruleEngineStatusForDispatch)){
            return true;
        }
        Boolean isDispatchable=false;
        List<ReturnEvent> returnEvents = null;
        try {
            if (ANY_RECEIVING_POINT.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                returnEvents = returnEventRepository.findByReturnId(returnId);
                if (!CollectionUtils.isEmpty(returnEvents)) {
                    for (ReturnEvent returnEvent : returnEvents) {
                        if (ReturnStatus.AWB_ASSIGNED.getStatus().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED.getStatus().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getStatus().equalsIgnoreCase(returnEvent.getEvent())
                                || ReturnStatus.RETURN_ACCEPTED.getStatus().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_AT_STORE.getName().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName().equalsIgnoreCase(returnEvent.getEvent())) {
                            return true;
                        }
                    }
                }
            } else {
                if (COURIER_PICKUP.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(ReturnStatus.AWB_ASSIGNED.getStatus(), ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getStatus(), ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName(), ReturnStatus.RETURN_RECEIVED_AT_STORE.getName()));
                } else if (POS_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch) || WH_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getStatus(),ReturnStatus.RETURN_ACCEPTED.getStatus(), ReturnStatus.RETURN_RECEIVED_AT_STORE.getName(), ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName()));
                }
                if (!CollectionUtils.isEmpty(returnEvents)) {
                    isDispatchable = true;
                }
            }
        }
        catch(Exception e){
            log.info("[isDispatchable] some exception occurred while calling isDispatchable service for exchange order: "+e);
        }
        return isDispatchable;
    }

    @Override
    public Boolean isDispatchableAtPOS(Integer returnId, String ruleEngineStatusForDispatch, String triggerPoint) {
        log.info("[isDispatchableAtPOS] returnId : {}, ruleEngineStatusForDispatch : {}, triggerPoint : {}",returnId, ruleEngineStatusForDispatch, triggerPoint);
        if(TriggerPoint.ReturnInitiation.getName().equalsIgnoreCase(ruleEngineStatusForDispatch)){
            return true;
        }
        Boolean isDispatchable=false;
        List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnId);

        if (CollectionUtils.isNotEmpty(returnEvents)) {
            Optional<ReturnEvent> returnEvent = returnEvents.stream().filter(r -> r.getEvent().equals(Constant.RETURN_METHOD.ENFORCED_RETURN_AT_STORE)).findAny();
            if (returnEvent.isPresent()) {
                return true;
            }
        }

        try {
            if (ANY_RECEIVING_POINT.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                returnEvents = returnEventRepository.findByReturnId(returnId);
                if (!CollectionUtils.isEmpty(returnEvents)) {
                    for (ReturnEvent returnEvent : returnEvents) {
                        if (ReturnStatus.AWB_ASSIGNED.getStatus().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED.getStatus().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getStatus().equalsIgnoreCase(returnEvent.getEvent())
                                || ReturnStatus.RETURN_ACCEPTED.getStatus().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_AT_STORE.getName().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName().equalsIgnoreCase(returnEvent.getEvent())) {
                            return true;
                        }
                    }
                }
            } else {
                if (COURIER_PICKUP.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(ReturnStatus.AWB_ASSIGNED.getStatus(), ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getStatus(), ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName(), ReturnStatus.RETURN_RECEIVED_AT_STORE.getName()));
                } else if (POS_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch) || (WH_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch) && !TriggerPoint.POSReceiving.getName().equalsIgnoreCase(triggerPoint))) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getStatus(),ReturnStatus.RETURN_ACCEPTED.getStatus(), ReturnStatus.RETURN_RECEIVED_AT_STORE.getName(), ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName()));
                }
                if (!CollectionUtils.isEmpty(returnEvents)) {
                    isDispatchable = true;
                }
            }
        }
        catch(Exception e){
            log.info("[isDispatchableAtPOS] some exception occurred while calling Dis patchable service for exchange order: "+e);
        }
        return isDispatchable;
    }
}
