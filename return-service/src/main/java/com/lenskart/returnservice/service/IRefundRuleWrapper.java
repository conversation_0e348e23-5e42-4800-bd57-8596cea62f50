package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.DecisionTableRefundDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundResponseDTO;
import com.lenskart.returncommon.model.response.ReturnPolicyResponse;
import com.lenskart.returnrepository.entity.RefundRules;

import java.util.List;
import java.util.Map;

public interface IRefundRuleWrapper {
    ReturnRefundResponseDTO fetchRule(DecisionTableRefundDTO decisionTableRefundDTO);

    Map<Integer, ReturnPolicyResponse> fetchRuleInBulk(Map<Integer,DecisionTableRefundDTO> decisionTableRefundDTOMap);
}
