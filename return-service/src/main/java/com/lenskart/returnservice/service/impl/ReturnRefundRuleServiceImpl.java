package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.dto.ExchangeItemDispatchableDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.SyncUnicomStatusDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.enums.*;
import com.lenskart.returncommon.model.request.RRRServiceRequest;
import com.lenskart.returncommon.model.response.ReturnPolicyResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.lenskart.returncommon.utils.Constant.BACKWARD_FLOW;
import static com.lenskart.returncommon.utils.Constant.EXCHANGE_ONHOLD_TO_PROCESSING;
import static com.lenskart.returncommon.utils.Constant.REFUND_RULES_CONSTANT.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.RETURN_ACCEPTED;
import static com.lenskart.returncommon.utils.Constant.RETURN_STATUS.RETURN_RECEIVED_ACTION_PENDING;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.REVERSE;
import static com.lenskart.returncommon.utils.Constant.STATUS.AWB_ASSIGNED;
import static com.lenskart.returncommon.utils.Constant.STATUS.RETURN_RECEIVED;
import static com.lenskart.returncommon.utils.ReturnPredicate.IS_RETURN_NOT_REJECTED_AND_CANCELLED_AND_RESHIP;

@Slf4j
@Service
public class ReturnRefundRuleServiceImpl implements IReturnRefundRuleService {

    @Autowired
    private ReturnRefundRuleRepository returnRefundRuleRepository;

    @Autowired
    private ReturnOrderActionServiceImpl returnOrderActionService;

    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    private IRefundRuleWrapper refundRuleWrapper;
    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    public static final String paymentMethod = "AnyPaymentMethod";

    public static final String MEDIBUDDY = "medibuddy";

    @Value("${blacklist.reverse.pincode:221005,721302,324005,560090,576104,244001,110093,603203,400028,400709,110006,560072,600088}")
    private String blacklistPincode;

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private IExchangeOrderService exchangeOrderService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ISyncUnicomStatusService syncUnicomStatusService;


    private List<String> brandNames = new ArrayList<>();

    @PostConstruct
    public void init(){
        //query to get the brandName from SP

       SystemPreference systemPreference = systemPreferenceRepository.findByKey("branded_list");
        if (systemPreference != null && StringUtils.isNotBlank(systemPreference.getValue())) {
            brandNames = Arrays.stream(systemPreference.getValue().split(","))
                    .map(String::trim)
                    .map(String::toLowerCase)
                    .toList();
            log.info("Loaded brandNames: {}", brandNames);
        }

    }

    @Override
    public ReturnRefundResponseDTO fetchReturnRefundRule(ReturnRefundInputDTO returnRefundInputDTO) {
        long startTime = System.nanoTime();
        ReturnRefundResponseDTO returnRefundResponseDTO = new ReturnRefundResponseDTO();
        UwOrderDTO uwOrder = returnRefundInputDTO.getRrrServiceRequest().getUwOrder();

        if (uwOrder == null) {
            return returnRefundResponseDTO;
        }
        AtomicBoolean updateTriggerPoint = new AtomicBoolean(true);

        ReturnDetail returnOrder = returnOrderActionService.getValidReturnDetailsByItem(uwOrder.getUwItemId()).orElse(null);

        updateReturnRefundInputDTO(returnRefundInputDTO, updateTriggerPoint, returnOrder);

        ReturnRefundRule existingReturnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(returnRefundInputDTO.getUwItemId(), returnRefundInputDTO.getTriggerPoint());

        boolean isMvsStore = returnRefundInputDTO.getStoreScore() == 10;

        log.info("[fetchReturnRefundRule] existingReturnRefundRule : {}, isMvsStore : {}", existingReturnRefundRule, isMvsStore);

        if (existingReturnRefundRule == null || existingReturnRefundRule.getReturnId() == null || returnOrder == null || isMvsStore) {
            returnRefundResponseDTO = fetchAndApplyRefundRule(returnRefundInputDTO);
        } else {
            returnRefundResponseDTO.setFetchExistingReturnResponse(true);
        }
        long endTime = System.nanoTime();
        log.info("fetchReturnRefundRule total time taken in milliseconds :{}", (endTime - startTime)/1000000);
        log.info("[fetchReturnRefundRule] returnRefundResponseDTO : {}",returnRefundResponseDTO);

        return returnRefundResponseDTO;
    }

    //
    public Map<Integer, ReturnPolicyResponse> fetchReturnPolicy(List<ReturnPolicyInputDTO> returnPolicyInputDTOList) {
        long startTime = System.currentTimeMillis();
        log.info("fetchReturnPolicy started");

        long constructStartTime = System.currentTimeMillis();
        Map<Integer, DecisionTableRefundDTO> decisionTableRefundDTOList = constructDecisionTableList(returnPolicyInputDTOList);
        long constructEndTime = System.currentTimeMillis();
        log.info("constructDecisionTableList executed in {} ms", (constructEndTime - constructStartTime));

        if (!decisionTableRefundDTOList.isEmpty()) {
            long fetchRuleStartTime = System.currentTimeMillis();
            Map<Integer, ReturnPolicyResponse> rr = refundRuleWrapper.fetchRuleInBulk(decisionTableRefundDTOList);
            long fetchRuleEndTime = System.currentTimeMillis();
            log.info("fetchRuleInBulk executed in {} ms", (fetchRuleEndTime - fetchRuleStartTime));

            long endTime = System.currentTimeMillis();
            log.info("fetchReturnPolicy completed in {} ms", (endTime - startTime));
            return rr;
        }

        long endTime = System.currentTimeMillis();
        log.info("fetchReturnPolicy completed in {} ms (no rules found)", (endTime - startTime));
        return null;
    }

    private Map<Integer, DecisionTableRefundDTO> constructDecisionTableList(List<ReturnPolicyInputDTO> returnPolicyInputDTOList) {
        // Use ConcurrentHashMap for thread-safe writes during parallel processing

        Map<Integer, DecisionTableRefundDTO> decisionTableRefundDTOMap = new ConcurrentHashMap<>();

        returnPolicyInputDTOList.parallelStream().forEach(request -> {

            boolean isBrand = false;
            if (request.getBrandName() != null) {
                isBrand = brandNames.contains(request.getBrandName().toLowerCase());
            }
            DecisionTableRefundDTO dto = new DecisionTableRefundDTO();
            dto.setReverseType(REVERSE);
            dto.setNavChannel(request.getChannel() != null ? request.getChannel() : WEBDTC);
            dto.setReturnInitiatedSource(request.getReturnInitiatedSource() != null ? request.getReturnInitiatedSource() : WEB);
            dto.setTriggerPoint(RETURN_INITIATION);
            dto.setIsPseudoGatepass(Boolean.FALSE);
            dto.setIsBranded(isBrand); // TODO: fetch from SP
            dto.setIsQcPass(Boolean.TRUE);
            dto.setReturnPeriod(1);
            dto.setCategory(request.getClassification() != null ? request.getClassification() : EYE_FRAME);
            dto.setIsAccessoryMissing(Boolean.FALSE);
            dto.setIsLastPiece(Boolean.FALSE);
            dto.setIsLensOnly(Boolean.FALSE);
            dto.setCountryCode(request.getCountry() != null ? request.getCountry() : "IN");
            dto.setInsurancePolicy(Boolean.FALSE);
            dto.setBlacklistedPincodes(Boolean.FALSE);
            dto.setBlacklistedPhoneNumbers(Boolean.FALSE);
            dto.setDraftReturnMethod(NONE);
            dto.setReturnReasons(ANY_REASON);
            dto.setPaymentMethod(JUS_PAY);
            dto.setExchangeAllowed(ONE);
            dto.setItemWarrantyPeriod(ONE);
            dto.setAmountValidity(request.getPrice() != null ? request.getPrice() : DEFAULT_AMOUNT);

            decisionTableRefundDTOMap.put(request.getSequenceId(), dto);
        });
        return decisionTableRefundDTOMap;
    }

    @Override
    public ReturnRefundRule getRefundRuleUwItemIdAndTriggerPoint(Integer uwItemId, String triggerPoint) {
        return returnRefundRuleRepository.findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(uwItemId, triggerPoint);
    }

    @Override
    public void persist(ReturnRefundRule returnRefundRule) {
        returnRefundRuleRepository.save(returnRefundRule);
    }

    @Override
    public ReturnRefundRule getRule(Integer uwItemId, String returnInitiatedSource) {
        return returnRefundRuleRepository.findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(uwItemId, returnInitiatedSource);
    }

    @Override
    public String getRuleEngineResponseForDispatch(Integer uwItemId, Integer returnId) {
        log.info("[getRuleEngineResponseForDispatch] getting rule engine response for dispatch for uwItemId: " + uwItemId + "returnId " + returnId);
        String ruleEngineDispatchStatus = null;
        try {
            ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
            if (returnRefundRule != null) {
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                org.json.JSONObject jsonObject = new org.json.JSONObject(returnRefundRuleResponse);
                if (jsonObject.has("exchangeOrderDispatch") && jsonObject.get("exchangeOrderDispatch") != null) {
                    ruleEngineDispatchStatus = (String) jsonObject.get("exchangeOrderDispatch");
                }
            }else{
                ruleEngineDispatchStatus = getDispatchPoint(uwItemId, returnId, "exchange_dispatch_point");
            }
        } catch (Exception e) {
            log.info("[getRuleEngineResponseForDispatch] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getRuleEngineResponseForDispatch] rule engine response for dispatch: " + ruleEngineDispatchStatus);
        return ruleEngineDispatchStatus;
    }

    @Override
    public String getRuleEngineResponseForRefundDispatch(Integer uwItemId, Integer returnId) {
        log.info("[getRuleEngineResponseForRefundDispatch] getting rule engine response for refund dispatch for uwItemId: " + uwItemId + "returnId " + returnId);
        String ruleEngineRefundDispatchStatus = null;
        try {
            ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
            if (returnRefundRule != null) {
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                org.json.JSONObject jsonObject = new org.json.JSONObject(returnRefundRuleResponse);
                ruleEngineRefundDispatchStatus = (String) jsonObject.get("refundDispatch");
            }else{
                ruleEngineRefundDispatchStatus = getDispatchPoint(uwItemId, returnId, "refund_dispatch_point");
            }
        } catch (Exception e) {
            log.info("[getRuleEngineResponseForRefundDispatch] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getRuleEngineResponseForRefundDispatch] rule engine response for refund dispatch: " + ruleEngineRefundDispatchStatus);
        return ruleEngineRefundDispatchStatus;
    }


    private String getDispatchPoint(Integer uwItemId, Integer returnId, String attribute){
        log.info("[getRuleEngineResponseForDispatch] going to call order-ops to check dispatch point there for uwItemId : {}, returnId : {}", uwItemId, returnId);
        String ruleEngineDispatchPoint = null;
        ResponseEntity<?> exchangeRefundDispatchPointResponseEntity = orderOpsFeignClient.getExchangeRefundDispatchPoint(uwItemId, returnId);
        if(exchangeRefundDispatchPointResponseEntity.getStatusCode().is2xxSuccessful() && exchangeRefundDispatchPointResponseEntity.getBody() != null){
            Map<String, Object> exchgRefResponseMap = (Map<String, Object>) exchangeRefundDispatchPointResponseEntity.getBody();
            ruleEngineDispatchPoint = exchgRefResponseMap.getOrDefault(attribute, "").toString();
        }
        return ruleEngineDispatchPoint;
    }
    @Override
    public ReturnRefundRule getRuleEngineResponse(Integer uwItemId, Integer returnId) {
        log.info("[getRuleEngineResponseForRefundDispatch] getting rule engine response for refund dispatch for uwItemId: " + uwItemId + "returnId " + returnId);
        String ruleEngineRefundDispatchStatus = null;
        ReturnRefundRule returnRefundRule = null;
        try {
            returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);

        } catch (Exception e) {
            log.info("[getRuleEngineResponseForRefundDispatch] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getRuleEngineResponseForRefundDispatch] rule engine response for refund dispatch: " + returnRefundRule);
        return returnRefundRule;
    }


    @Override
    public void updateReturnRefundRule(Integer uwItemId, Integer returnId, String source) {
        String triggerPoint = getTriggerPoint(source);
        ReturnRefundRule returnRefundRule = getRule(uwItemId, triggerPoint);
        if (returnRefundRule != null) {
            returnRefundRuleRepository.updateReturnIdForUwItemIdAndId(returnRefundRule.getId(), returnId, uwItemId, triggerPoint);
        }
    }

    private String getTriggerPoint(String source){
        String triggerPoint = TriggerPoint.ReturnInitiation.getName();
        if (ReturnSources.POS.getSource().equalsIgnoreCase(source)) {
            triggerPoint = TriggerPoint.POSReceiving.getName();
        } else if (ReturnSources.WAREHOUSE.getSource().equalsIgnoreCase(source)) {
            triggerPoint = TriggerPoint.WHReceiving.getName();
        }
        log.info("[getTriggerPoint] trigger point : {}", triggerPoint);
        return triggerPoint;
    }

    @Override
    public Boolean isExchangeItemDispatchable(ExchangedItem exchangedItem) {
        Boolean isDispatchable = false;
        try{
            isDispatchable =  orderOpsFeignClient.isExchangeItemDispatchable(exchangedItem);
        }catch (Exception exception){
            log.error("[isExchangeItemDispatchable] exception : "+exception);
        }
        return isDispatchable;
    }

    @Override
    public Boolean isExchangeItemDispatchable(ExchangeItemDispatchableDTO exchangeItemDispatchableDTO) {
        log.info("[isExchangeItemDispatchable] exchangeItemDispatchableDTO : {}",exchangeItemDispatchableDTO);
        ExchangeItemDispatchableDTO exchangeItemDispatchable = exchangeItemDispatchableDTO;
        if(exchangeItemDispatchable == null){
            return false;
        }
        if(exchangeItemDispatchable.getOrdersHeaderDTO() == null){
            //get details from order-ops and proceed
            ExchangedItem exchangedItem = new ExchangedItem();
            exchangedItem.setUwItemId(exchangeItemDispatchable.getExchangeItemDTO().getUwItemId());
            exchangedItem.setReturnId(exchangeItemDispatchable.getExchangeItemDTO().getReturnId());
            exchangedItem.setCallingPoint(exchangeItemDispatchable.getExchangeItemDTO().getCallingPoint());
            ResponseEntity<ExchangeItemDispatchableDTO> exchangeItemDispatchableDTOResponseEntity = orderOpsFeignClient.getExchangeItemDispatchableDTO(exchangedItem);
            if(exchangeItemDispatchableDTOResponseEntity.getStatusCode().is2xxSuccessful()){
                exchangeItemDispatchable = exchangeItemDispatchableDTOResponseEntity.getBody();
                if(exchangeItemDispatchable == null){
                    log.error("[isExchangeItemDispatchable] exchangeItemDispatchable is null");
                    return false;
                }
            }
        }
        ExchangeItemDTO exchangedItem = exchangeItemDispatchable.getExchangeItemDTO();
        ExchangeOrdersDTO exchangeOrdersDTO = exchangeItemDispatchable.getExchangeOrdersDTO();
        OrdersHeaderDTO ordersHeaderDTO = exchangeItemDispatchable.getOrdersHeaderDTO();
        List<UwOrderDTO> uwOrderDTOs = exchangeItemDispatchable.getUwOrderDTOs();
        String ruleEngineStatusForDispatch = getRuleEngineResponseForExchangeOrderDispatch(exchangedItem.getUwItemId(), exchangedItem.getReturnId());

        boolean isExchangedItemValid = getIsExchangedItemValid(exchangedItem);
        boolean isExchangeOrderValid = validateExchangeOrder(exchangedItem, exchangeOrdersDTO);
        boolean isExchangeOrderAlreadyUnholded = getIsExchangeOrderAlreadyUnholded(ordersHeaderDTO);

        log.info("[isExchangeItemDispatchable] order : {}, isExchangedItemValid : {}, isExchangeOrderValid : {}, isExchangeOrderAlreadyUnholded: {}",ordersHeaderDTO.getOrderId(), isExchangedItemValid, isExchangeOrderValid, isExchangeOrderAlreadyUnholded);

        boolean isDispatchable = isExchangedItemValid
                && isExchangeOrderValid
                && !isExchangeOrderAlreadyUnholded
                && isDispatchable(exchangedItem.getReturnId(), ruleEngineStatusForDispatch);

        log.info("[isExchangeItemDispatchable] order : {}, dipatchable flag : {}, calling point : {}", ordersHeaderDTO.getIncrementId(), isDispatchable, exchangedItem.getCallingPoint());

        if (isDispatchable && exchangedItem.getCallingPoint().equalsIgnoreCase(BACKWARD_FLOW)) {
            performDispatchableOperations(uwOrderDTOs, ordersHeaderDTO, exchangedItem);
        }
        if(isExchangeOrderAlreadyUnholded){
            log.info("[isExchangeItemDispatchable] temporary fix for unholding order : {}", ordersHeaderDTO.getIncrementId());
            isDispatchable = isExchangeOrderAlreadyUnholded;
        }
        return isDispatchable;
    }

    @Override
    public String getRuleEngineResponseForExchangeOrderDispatch(Integer uwItemId, Integer returnId) {
        log.info("[getRuleEngineResponseForDispatch] getting rule engine response for dispatch for uwItemId: " + uwItemId + "returnId " + returnId);
        String ruleEngineDispatchStatus = null;
        try {
            ReturnRefundRule returnRefundRule = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
            if (returnRefundRule != null) {
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                org.json.JSONObject jsonObject = new org.json.JSONObject(returnRefundRuleResponse);
                ruleEngineDispatchStatus = jsonObject.isNull("exchangeOrderDispatch") ? Constant.RULE_ENGINE_STATUS.ANY_RECEIVING_POINT : jsonObject.getString("exchangeOrderDispatch");
                if(Constant.RULE_ENGINE_STATUS.POS_RECEIVING.equalsIgnoreCase(returnRefundRule.getTriggerPoint()) && StringUtils.isEmpty(ruleEngineDispatchStatus)){
                    ruleEngineDispatchStatus = Constant.RULE_ENGINE_STATUS.POS_RECEIVING;
                }
            }
        } catch (Exception e) {
            log.info("[getRuleEngineResponseForDispatch] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getRuleEngineResponseForDispatch] rule engine response for dispatch: {} , returnId : {}" , ruleEngineDispatchStatus, returnId);
        return ruleEngineDispatchStatus;
    }

    private void performDispatchableOperations(List<UwOrderDTO> uwOrderDTOs, OrdersHeaderDTO ordersHeaderDTO, ExchangeItemDTO exchangedItem) {
        log.info("[performDispatchableOperations] new exchange item is dispatachable and entry will be made in sync_unicom_status to set custom field at unicom , order : {}", ordersHeaderDTO.getIncrementId());
        SyncUnicomStatusDTO syncUnicomStatusDTO = new SyncUnicomStatusDTO();
        syncUnicomStatusDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        syncUnicomStatusDTO.setUwOrderDTOs(uwOrderDTOs);
        syncUnicomStatusDTO.setExchangeItemDTO(exchangedItem);
        try{
            boolean syncUnicomStatusFlag = syncUnicomStatusService.syncUnicomStatus(syncUnicomStatusDTO);
            log.info("[performDispatchableOperations] syncUnicomStatusFlag : {}, paymentFlag : {}", syncUnicomStatusFlag, ordersHeaderDTO.getPaymentCaptureFlag());
            if (syncUnicomStatusFlag && ordersHeaderDTO.getPaymentCaptureFlag() == 1) {
                unholdExchangeOrder(ordersHeaderDTO.getIncrementId(), exchangedItem.getReturnId());
                saveOrderComments(ordersHeaderDTO);
            }
        }catch (Exception exception){
            log.error("[performDispatchableOperations] exception occurred : "+exception);
        }
    }

    private void saveOrderComments(OrdersHeaderDTO ordersHeaderDTO) {
        try{
            OrderCommentReq orderCommentReq = new OrderCommentReq();
            orderCommentReq.setComment(EXCHANGE_ONHOLD_TO_PROCESSING);
            orderCommentReq.setIncrementId(ordersHeaderDTO.getIncrementId());
            orderCommentReq.setCommentType("cust");
            kafkaService.pushToKafka(ORDER_STATUS_HISTORY_QUEUE,String.valueOf(ordersHeaderDTO.getIncrementId()), objectMapper.writeValueAsString(orderCommentReq));
        }catch (Exception exception){
            log.error("[performDispatchableOperations] comment insertion failed to kafka : "+exception);
        }
    }

    private void unholdExchangeOrder(int incrementId, Integer returnId) {
        ExchangeOrderUnholdRequest exchangeOrderUnholdRequest = new ExchangeOrderUnholdRequest(incrementId, returnId);
        try{
            kafkaService.pushToKafka(EXCHANGE_ORDER_UNHOLD_QUEUE,String.valueOf(incrementId), objectMapper.writeValueAsString(exchangeOrderUnholdRequest));
        }catch (Exception exception){
            log.error("[unholdExchangeOrder] order : {} error : {}", incrementId, exception);
        }
    }

    private boolean getIsExchangedItemValid(ExchangeItemDTO exchangedItem) {
        if (exchangedItem == null
                || exchangedItem.getReturnId() == null
                || exchangedItem.getUwItemId() == null
                || exchangedItem.getCallingPoint() == null) {
            log.info("[getIsExchangedItemValid] exchanged item request is not valid");
            return false;
        }
        return true;
    }

    private boolean validateExchangeOrder(ExchangeItemDTO exchangedItem, ExchangeOrdersDTO exchangeOrdersDTO){
        if (exchangedItem.getCallingPoint().equalsIgnoreCase(BACKWARD_FLOW) && exchangeOrdersDTO == null) {
            log.info("[validateExchangeOrder] exchange order has not been created for order: " + exchangedItem);
            return false;
        }
        return true;
    }


    private boolean getIsExchangeOrderAlreadyUnholded(OrdersHeaderDTO ordersHeaderDTO) {
        boolean isExchangeOrderAlreadyUnholded = ordersHeaderDTO != null && Boolean.TRUE.equals(ordersHeaderDTO.getAssignedMgr());
        log.info("[getIsExchangeOrderAlreadyUnholded] exchange order is already unholded ? : {}", isExchangeOrderAlreadyUnholded);
        return isExchangeOrderAlreadyUnholded;
    }

    @Override
    public Boolean isDispatchable(Integer returnId, String ruleEngineStatusForDispatch){
        log.info("[isDispatchable] returnId : {}, ruleEngineStatusForDispatch : {}", returnId, ruleEngineStatusForDispatch);
        if(TriggerPoint.ReturnInitiation.getName().equalsIgnoreCase(ruleEngineStatusForDispatch)){
            return true;
        }
        boolean isDispatchable=false;
        List<ReturnEvent> returnEvents=null;
        try {
            if (Constant.RULE_ENGINE_STATUS.ANY_RECEIVING_POINT.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                returnEvents = returnEventRepository.findByReturnId(returnId);
                if (returnEvents != null) {
                    for (ReturnEvent returnEvent : returnEvents) {
                        if (AWB_ASSIGNED.equalsIgnoreCase(returnEvent.getEvent()) || RETURN_RECEIVED.equalsIgnoreCase(returnEvent.getEvent()) || RETURN_RECEIVED_ACTION_PENDING.equalsIgnoreCase(returnEvent.getEvent())
                                || ReturnStatus.RETURN_ACCEPTED.getStatus().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_AT_STORE.getName().equalsIgnoreCase(returnEvent.getEvent()) || ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName().equalsIgnoreCase(returnEvent.getEvent())) {
                            isDispatchable = true;
                            break;
                        }
                    }
                }
            } else {
                if (Constant.RULE_ENGINE_STATUS.COURIER_PICKUP.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(AWB_ASSIGNED, RETURN_RECEIVED, RETURN_RECEIVED_ACTION_PENDING, ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName().toLowerCase()));
                } else if (Constant.RULE_ENGINE_STATUS.POS_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch) || Constant.RULE_ENGINE_STATUS.WH_RECEIVING.equalsIgnoreCase(ruleEngineStatusForDispatch)) {
                    returnEvents = returnEventRepository.findByReturnIdAndEventIn(returnId, Arrays.asList(RETURN_RECEIVED, RETURN_RECEIVED_ACTION_PENDING, RETURN_ACCEPTED, ReturnStatus.RETURN_RECEIVED_AT_STORE.getName().toLowerCase(), ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName().toLowerCase()));
                }
                isDispatchable = !CollectionUtils.isEmpty(returnEvents);
            }
        }
        catch(Exception exception){
            log.error("[isDispatchable] some exception occurred while calling isDispatchable service for return : "+returnId+" exception : "+exception);
        }
        log.info("[isDispatchable] returnId : {}, isDispatchable : {}", returnId, isDispatchable);
        return isDispatchable;
    }

    @Override
    public Boolean isRefundDispatchable(ExchangedItem exchangedItem) {
        //TODO - to implement this function

        return null;
    }

    @Override
    public ReturnRefundRule getReturnRefundRule(Integer returnId, String source) {
        log.info("[getReturnRefundRule] getting return refund rule for source: " + source + "returnId " + returnId);
        ReturnRefundRule returnRefundRule = null;
        try {
            String triggerPoint = getTriggerPoint(source);
            returnRefundRule = returnRefundRuleRepository.findTop1ByReturnIdAndTriggerPointOrderByCreatedAtDesc(returnId, triggerPoint);
        } catch (Exception e) {
            log.info("[getReturnRefundRule] : exception occurred while getting rule engine response :" + e);
        }
        log.info("[getReturnRefundRule] returnRefundRule is not null ? : {}" , returnRefundRule != null);
        return returnRefundRule;
    }

    @Override
    public String getRefundDispatch(ReturnRefundRule returnRefundRule){
        String refundDispatchPoint = null;
        if (returnRefundRule != null) {
            try{
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                org.json.JSONObject jsonObject = new org.json.JSONObject(returnRefundRuleResponse);
                refundDispatchPoint = jsonObject.isNull("refundDispatch") ? "" : jsonObject.getString("refundDispatch");
                if(Constant.RULE_ENGINE_STATUS.POS_RECEIVING.equalsIgnoreCase(returnRefundRule.getTriggerPoint()) && StringUtils.isEmpty(refundDispatchPoint)){
                    refundDispatchPoint = Constant.RULE_ENGINE_STATUS.POS_RECEIVING;
                }
            }catch (Exception exception){
                log.error("[getRefundDispatch] exception : {}", exception.getMessage());
            }
        }
        return refundDispatchPoint;
    }

    @Override
    public String getExchangeDispatch(ReturnRefundRule returnRefundRule){
        String exchangeDispatchPoint = null;
        if (returnRefundRule != null) {
            try{
                String returnRefundRuleResponse = returnRefundRule.getReturnRefundRuleResponse();
                org.json.JSONObject jsonObject = new org.json.JSONObject(returnRefundRuleResponse);
                exchangeDispatchPoint = jsonObject.isNull("exchangeOrderDispatch") ? "" : jsonObject.getString("exchangeOrderDispatch");
                if(Constant.RULE_ENGINE_STATUS.POS_RECEIVING.equalsIgnoreCase(returnRefundRule.getTriggerPoint()) && StringUtils.isEmpty(exchangeDispatchPoint)){
                    exchangeDispatchPoint = Constant.RULE_ENGINE_STATUS.POS_RECEIVING;
                }
            }catch (Exception exception){
                log.error("[getExchangeDispatch] exception : {}", exception.getMessage());
            }
        }
        return exchangeDispatchPoint;
    }

    private ReturnRefundResponseDTO fetchAndApplyRefundRule(ReturnRefundInputDTO returnRefundInputDTO) {

        DecisionTableRefundDTO decisionTableRefundDTO = constructDecisionTableRefundDTO(returnRefundInputDTO);

        returnRefundInputDTO.setDecisionTableRefundDTO(decisionTableRefundDTO);

        return refundRuleWrapper.fetchRule(decisionTableRefundDTO);
    }

    private DecisionTableRefundDTO constructDecisionTableRefundDTO(ReturnRefundInputDTO returnRefundInputDTO) {
        DecisionTableRefundDTO decisionTableRefundDTO = copyFieldsTo(returnRefundInputDTO);

        OrderInfoResponseDTO orderInfoResponseDTO = returnRefundInputDTO.getRrrServiceRequest().getOrderInfoResponse();

        OrdersHeaderDTO ordersHeaderDTO = orderInfoResponseDTO.getOrdersHeader();

        UwOrderDTO uwOrder = returnRefundInputDTO.getRrrServiceRequest().getUwOrder();

        Integer incrementId = uwOrder.getIncrementId();

        Optional<UwOrderDTO> uwOrderInsurance = orderInfoResponseDTO.getUwOrders().stream().filter(uw -> incrementId.equals(uw.getIncrementId()) && getInsuranceProductId().contains(uw.getProductId())).findFirst();

        decisionTableRefundDTO.setInsurancePolicy(uwOrderInsurance.isPresent());

        if (uwOrder.getNavChannel() != null) {
            decisionTableRefundDTO.setNavChannel(uwOrder.getNavChannel());
        }
        if (returnRefundInputDTO.getReverseType() != null) {
            decisionTableRefundDTO.setReverseType(returnRefundInputDTO.getReverseType());
        }
        if (uwOrder.getClassification() != null) {
            decisionTableRefundDTO.setCategory(uwOrder.getClassification());
        }

        decisionTableRefundDTO.setBrand(uwOrder.getBrand() != null ? uwOrder.getBrand() : "");

        if (StringUtils.isEmpty(uwOrder.getBrand())) {
            decisionTableRefundDTO.setIsBranded(isPrivateBrandsCheck(uwOrder.getBrand()));
        }

        CustomerAccountInfoDTO customerAccountInfo = orderInfoResponseDTO.getCustomerAccountInfoDTO();
        if (customerAccountInfo != null) {
            decisionTableRefundDTO.setBlacklistedPhoneNumbers(customerAccountInfo.getBlacklistFlag());
        } else {
            decisionTableRefundDTO.setBlacklistedPhoneNumbers(false);
        }


        boolean containsMedibuddy = orderInfoResponseDTO.getMasterExchangeOrderPaymentMethods().stream()
                .map(String::toLowerCase)
                .anyMatch(paymentMethod -> paymentMethod.equals(MEDIBUDDY.toLowerCase()));

        for (OrdersDTO order : orderInfoResponseDTO.getOrders()) {
            if (uwOrder.getItemId() == order.getItemId()) {
                if (DecisionTableRefundDTO.RETURN_SOURCE.DIRECT.equalsIgnoreCase(decisionTableRefundDTO.getReturnInitiatedSource()) || returnRefundInputDTO.isBulkOrder()) {
                    decisionTableRefundDTO.setPaymentMethod(paymentMethod);
                } else {
                    decisionTableRefundDTO.setPaymentMethod(order.getMethod());
                }
                if (MEDIBUDDY.equals(order.getMethod())) {
                    decisionTableRefundDTO.setPaymentMethod(MEDIBUDDY);
                } else if (containsMedibuddy) {
                    decisionTableRefundDTO.setPaymentMethod(MEDIBUDDY);
                }
            }
        }

        setBlacklistedPincodes(decisionTableRefundDTO, orderInfoResponseDTO, ordersHeaderDTO);

        decisionTableRefundDTO.setAmountValidity(uwOrder.getTotalPrice());

        ExchangeOrdersDTO exchangeOrder = orderInfoResponseDTO.getExchangeOrdersDTO();
        if (exchangeOrder != null) {
            decisionTableRefundDTO.setExchangeAllowed(exchangeOrder.getCountExchangeItems());
        } else {
            decisionTableRefundDTO.setExchangeAllowed(0);
        }

        boolean doReturnPeriodCalc = true;
        if (returnRefundInputDTO.getReturnId() != null) {
            decisionTableRefundDTO.setReturnId(returnRefundInputDTO.getReturnId());
            ReturnRefundRule returnRefundRuleLatest = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(returnRefundInputDTO.getUwItemId());
            if (returnRefundRuleLatest != null && returnRefundRuleLatest.getCreatedAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().isBefore(LocalDate.now().plusDays(60))) {
                String decisionValues = returnRefundRuleLatest.getDecisionValues();
                if (!StringUtils.isEmpty(decisionValues)) {
                    try {
                        JsonNode jsonNode = objectMapper.readTree(decisionValues);
                        Integer prevReturnPeriod = jsonNode.get("returnPeriod").asInt();
                        decisionTableRefundDTO.setReturnPeriod(prevReturnPeriod);
                        doReturnPeriodCalc = false;
                    } catch (Exception e) {
                        log.error(" Exception occurred while getting return period from ReturnRefundRule");
                    }
                }
            }
        }
        if(doReturnPeriodCalc) {
            decisionTableRefundDTO.setReturnPeriod((int) deliveredDaysBefore(uwOrder, returnRefundInputDTO.getRrrServiceRequest().getShippingStatuses()));
        }

        if (ordersHeaderDTO != null && StringUtils.isNotBlank(ordersHeaderDTO.getLkCountry())) {
            decisionTableRefundDTO.setCountryCode(ordersHeaderDTO.getLkCountry());
        } else {
            decisionTableRefundDTO.setCountryCode("IN");
        }

        //if (StringUtils.isEmpty(returnRefundInputDTO.getReturnReason())) {
            /*ReturnReason returnReason = returnReasonRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(returnRefundInputDTO.getUwItemId());
            if (null != returnReason) {
                return returnReason.getSecondryReason();
            }*/
            decisionTableRefundDTO.setReturnReasons("anyReason");
        //}

        decisionTableRefundDTO.setItemWarrantyPeriod(getItemWarrantyPeriod(returnRefundInputDTO.getRrrServiceRequest()));
        decisionTableRefundDTO.setCustomerScore(returnRefundInputDTO.getCustomerScore());
        decisionTableRefundDTO.setStoreScore(returnRefundInputDTO.getStoreScore());

        return decisionTableRefundDTO;
    }

    private Integer getItemWarrantyPeriod(RRRServiceRequest rrrServiceRequest) {
        Integer warrantyPeriod = null;
        try {
            Integer uwItemId = null;
            if (rrrServiceRequest.getUwOrderWH() != null) {
                uwItemId = rrrServiceRequest.getUwOrderWH().getUwItemId();
            } else {
                uwItemId = rrrServiceRequest.getUwOrder().getUwItemId();
            }

            if (null != uwItemId && !CollectionUtils.isEmpty(rrrServiceRequest.getOrderInfoResponse().getUwOrderAttributes())) {
                Integer finalUwItemId = uwItemId;
                UwOrderAttributeDTO uwOrderAttribute = rrrServiceRequest.getOrderInfoResponse().getUwOrderAttributes().stream().filter(u -> u.getUwItemId().equals(finalUwItemId) && Constant.UwOrderAttribute_AttributeName.WARRANTY_PERIOD.equals(u.getAttributeName())).findFirst().orElse(null);
                if (null != uwOrderAttribute && null != uwOrderAttribute.getAttributeValue()) {
                    warrantyPeriod = Integer.parseInt(uwOrderAttribute.getAttributeValue());
                }
            }
        } catch (Exception e) {
            log.error("[getItemWarrantyPeriod] Exception: {}", e.getMessage(), e);
        }
        log.info("[ReturnRefundRuleServiceImpl][getItemWarrantyPeriod] warrantyPeriod: {}", warrantyPeriod);
        return warrantyPeriod;
    }

    @Override
    public long deliveredDaysBefore(UwOrderDTO uwOrder, List<ShippingStatusDetail> shippingStatusesMaster) {
        long diffDays = 0;
        Date zeroTime = new Date(0000, 00, 00, 0, 0, 0);
        Date completeTime = null;
        Date deliveredDate = null;
        List<ShippingStatusDetail> shippingStatuses = shippingStatusesMaster.stream().filter(s -> s.getUnicomOrderCode().equals(uwOrder.getUnicomOrderCode()) && s.getOrderNo().equals(uwOrder.getIncrementId())).toList();
        log.debug("[ExchangeRefundMethodServiceImpl][deliveredDaysBefore] calculating return period for item {} shipping status data is as follows: {}", uwOrder.getUwItemId(), shippingStatuses);
        if (null != shippingStatuses && !shippingStatuses.isEmpty()) {
            for (ShippingStatusDetail shippingStatus : shippingStatuses) {
                if (shippingStatus.getComplete_time() != null && !shippingStatus.getComplete_time().equals(zeroTime)) {
                    completeTime = shippingStatus.getComplete_time();
                }
                if (shippingStatus.getDeliveredDate() != null && !shippingStatus.getDeliveredDate().equals(zeroTime)) {
                    deliveredDate = shippingStatus.getDeliveredDate();
                }
            }
            if (deliveredDate == null) {
                if (completeTime == null) {
                    log.debug("[ExchangeRefundMethodServiceImpl][deliveredDaysBefore] Delivery/ dispatch info not available. Using created dadte +7");
                    long diff = new Date().getTime() - uwOrder.getCreatedAt().getTime();
                    diffDays = ((diff / (24 * 60 * 60 * 1000)) - 7) <= 0 ? 0 : ((diff / (24 * 60 * 60 * 1000)) - 7);
                } else {
                    log.debug("[ExchangeRefundMethodServiceImpl][deliveredDaysBefore] Delivery date has not been recorded. Completion date is {}", completeTime);
                    if (!uwOrder.getProductDeliveryType().isEmpty() &&
                            uwOrder.getProductDeliveryType().equalsIgnoreCase(Constant.PRODUCT_DELIVERY_TYPE.OTC)) {
                        log.debug("[ExchangeRefundMethodServiceImpl][deliveredDaysBefore] It's and OTC order");
                        long diff = new Date().getTime() - completeTime.getTime();
                        diffDays = (diff / (24 * 60 * 60 * 1000));
                    } else {
                        long diff = new Date().getTime() - completeTime.getTime();
                        diffDays = (diff / (24 * 60 * 60 * 1000)) + 3;
                    }
                }
            } else {
                log.debug("[ExchangeRefundMethodServiceImpl][deliveredDaysBefore] Delivery date is: {}", deliveredDate);
                long diff = new Date().getTime() - deliveredDate.getTime();
                diffDays = (diff / (24 * 60 * 60 * 1000));
            }
        } else {
            log.debug(("No info found in inventory.shipping_status table for the order " + uwOrder.getIncrementId() + ". Assuming delivery date to be 7 days hence from date of order creation"));
            long diff = new Date().getTime() - uwOrder.getCreatedAt().getTime();
            diffDays = ((diff / (24 * 60 * 60 * 1000)) - 7) <= 0 ? 0 : ((diff / (24 * 60 * 60 * 1000)) - 7);
        }
        log.info("[ExchangeRefundMethodServiceImpl][deliveredDaysBefore] returnPeriod is {}", diffDays);
        return diffDays;
    }

    private void setBlacklistedPincodes(DecisionTableRefundDTO decisionTableRefundDTO, OrderInfoResponseDTO orderInfoResponseDTO, OrdersHeaderDTO ordersHeaderDTO) {
        List<OrderAddressUpdateDTO> orderAddressUpdateDTOS = orderInfoResponseDTO.getOrderAddressUpdates();
        if (!CollectionUtils.isEmpty(orderAddressUpdateDTOS)) {
            Optional<OrderAddressUpdateDTO> orderAddressUpdateDTO = orderAddressUpdateDTOS.stream().filter(o -> o.getOrderId().equals(ordersHeaderDTO.getOrderId()) && "shipping".equalsIgnoreCase(o.getAddressType())).findFirst();
            if (orderAddressUpdateDTO.isPresent() && orderAddressUpdateDTO.get().getPostcode().contains(blacklistPincode)) {
                decisionTableRefundDTO.setBlacklistedPincodes(true);
            } else {
                decisionTableRefundDTO.setBlacklistedPincodes(false);
            }
        } else {
            decisionTableRefundDTO.setBlacklistedPincodes(false);
        }
    }


    public List<Integer> getInsuranceProductId() {

        String[] productids;
        List<Integer> productInsuranceIdList = new ArrayList<>();
        SystemPreference sysPref1 = systemPreferenceRepository.findTopByGroupAndKey(Constant.SYSTEM_PREFERENCE_KEYS.PRODUCT_INSURANCE, Constant.SYSTEM_PREFERENCE_KEYS.PRODUCT_ID_INSURANCE);
        productids = sysPref1.getValue().split(","); //"123, 232, 32"
        for (String pid : productids) {
            productInsuranceIdList.add(Integer.parseInt(pid));
            log.debug("[InsuranceServiceImpl][getInsuranceProductId] getInsuranceProductId() insurance pid added to list : " + pid);
        }
        log.info("[InsuranceServiceImpl][getInsuranceProductId] productInsuranceIdList " + productInsuranceIdList);
        return productInsuranceIdList;
    }

    public Boolean isPrivateBrandsCheck(String productBrand) {
        SystemPreference privateBrands = systemPreferenceRepository.findAllByKey(Constant.SYSTEM_PREFERENCE_KEYS.PRIVATE_BRANDS);
        if (privateBrands == null) {
            return false;
        }
        String privateBrandArrayList = privateBrands.getValue();
        log.debug("[ReturnRefundRuleServiceImpl][isPrivateBrandsCheck] inside getPrivateBrands for {} <<<<<<<<with brand>>>>>> {}", privateBrandArrayList, productBrand);
        if (StringUtils.isNotEmpty(privateBrandArrayList)) {
            log.debug("[ReturnRefundRuleServiceImpl][isPrivateBrandsCheck] privateBrandCache is true.");
            String str[] = privateBrandArrayList.split(",");
            for (String brand : str) {
                if (StringUtils.isNotBlank(brand) && StringUtils.isNotBlank(productBrand) && brand.trim().equalsIgnoreCase(productBrand.trim())) {
                    return true;
                }
            }
        }
        return false;
    }

    private DecisionTableRefundDTO copyFieldsTo(ReturnRefundInputDTO returnRefundInputDTO) {
        if (returnRefundInputDTO != null) {
            DecisionTableRefundDTO decisionTableRefundDTO = new DecisionTableRefundDTO();
            decisionTableRefundDTO.setUwItemId(returnRefundInputDTO.getUwItemId());
            decisionTableRefundDTO.setTriggerPoint(returnRefundInputDTO.getTriggerPoint());
            decisionTableRefundDTO.setReverseType(returnRefundInputDTO.getReverseType());
            decisionTableRefundDTO.setIsPseudoGatepass(returnRefundInputDTO.isPseudoGatepass());
            decisionTableRefundDTO.setIsBranded(returnRefundInputDTO.isBranded());
            decisionTableRefundDTO.setIsQcPass(returnRefundInputDTO.isQcPass());
            decisionTableRefundDTO.setReturnInitiatedSource(returnRefundInputDTO.getReturnInitiatedSource());
            decisionTableRefundDTO.setIsAccessoryMissing(returnRefundInputDTO.isAccessoryMissing());
            decisionTableRefundDTO.setReturnPeriod(returnRefundInputDTO.getReturnPeriod());
            decisionTableRefundDTO.setIsLastPiece(returnRefundInputDTO.isLastPiece());
            decisionTableRefundDTO.setIsLensOnly(returnRefundInputDTO.isLensOnly());
            decisionTableRefundDTO.setReverseType(returnRefundInputDTO.getReverseType());
            decisionTableRefundDTO.setNavChannel(returnRefundInputDTO.getNavChannel());
            decisionTableRefundDTO.setAmountValidity(returnRefundInputDTO.getAmountValidity());
            decisionTableRefundDTO.setTriggerPoint(returnRefundInputDTO.getTriggerPoint());
            decisionTableRefundDTO.setBlacklistedPincodes(returnRefundInputDTO.isBlacklistedPincodes());
            decisionTableRefundDTO.setBlacklistedPhoneNumbers(returnRefundInputDTO.isBlacklistedPhoneNumbers());
            decisionTableRefundDTO.setRuleCalledFrom(returnRefundInputDTO.getRuleCalledFrom());
            decisionTableRefundDTO.setDraftReturnMethod(returnRefundInputDTO.getDraftReturnMethod());
            log.info("[ReturnRefundRuleServiceImpl][copyFieldsTo] [returnRefundInputDTO coppied in decisionTableRefundDTO]  {}", new Gson().toJson(decisionTableRefundDTO));
            return decisionTableRefundDTO;
        } else {
            log.info("[ReturnRefundRuleServiceImpl][copyFieldsTo] returnRefundInput is null.");
            return new DecisionTableRefundDTO();
        }
    }

    private void updateReturnRefundInputDTO(ReturnRefundInputDTO returnRefundInputDTO, AtomicBoolean updateTriggerPoint, ReturnDetail returnOrder) {
        if (returnRefundInputDTO.isPseudoGatepass()) {
            returnRefundInputDTO.setReturnInitiatedSource(DecisionTableRefundDTO.RETURN_SOURCE.DIRECT);
            returnRefundInputDTO.setTriggerPoint(TriggerPoint.WHReceiving.toString());
        }

        setTriggerPointBasedOnReturnOrder(returnRefundInputDTO, updateTriggerPoint, returnOrder);

        log.info("[ReturnRefundRuleServiceImpl][updateReturnRefundInputDTO] Final triggerPoint is " + returnRefundInputDTO.getTriggerPoint());
        String returnSource = getReturnSource(returnOrder, returnRefundInputDTO);
        log.info("[ReturnRefundRuleServiceImpl][updateReturnRefundInputDTO] returnSource is " + returnSource);
        if (StringUtils.isNotEmpty(returnSource)) {
            returnRefundInputDTO.setReturnInitiatedSource(returnSource.toLowerCase());
            log.info("[ReturnRefundRuleServiceImpl][updateReturnRefundInputDTO] ReturnInitiatedSource is " + returnRefundInputDTO.getReturnInitiatedSource());

            if ((returnOrder != null && StringUtils.isNotEmpty(returnOrder.getReturnMethod()) && DecisionTableRefundDTO.RETURN_SOURCE.ONLINE_WEB.equalsIgnoreCase(returnSource)) &&
                    ((returnOrder.getReturnMethod().contains(Constant.RETURN_METHOD.RETURN_TO_STORE)) || (returnOrder.getReturnMethod().contains(Constant.RETURN_METHOD.RPU)) || (returnOrder.getReturnMethod().contains(Constant.RETURN_METHOD.SHIP_TO_LENSKRT)))) {
                log.info("[ReturnRefundRuleServiceImpl][updateReturnRefundInputDTO] DraftReturnMethod is " + returnOrder.getReturnMethod());
                returnRefundInputDTO.setDraftReturnMethod(returnOrder.getReturnMethod());
            } else {
                log.info("[ReturnRefundRuleServiceImpl][updateReturnRefundInputDTO] DraftReturnMethod is set as NA for " + returnRefundInputDTO.getUwItemId());
                returnRefundInputDTO.setDraftReturnMethod("NA");
            }
        } else {
            log.info("[ReturnRefundRuleServiceImpl][updateReturnRefundInputDTO] ReturnInitiatedSource is default as web ");
            returnRefundInputDTO.setReturnInitiatedSource(DecisionTableRefundDTO.RETURN_SOURCE.ONLINE_WEB);
        }

        if (returnRefundInputDTO.getRrrServiceRequest().getUwOrder() != null) {
            returnRefundInputDTO.setFraud(false);

            boolean isBulkOrder = isBulkOrder(returnRefundInputDTO.getRrrServiceRequest());

            returnRefundInputDTO.setBulkOrder(isBulkOrder);

            if (!isBulkOrder) {
                if (!RuleCalledFrom.STATUS_UPDATE_SYNC_API.getValue().equalsIgnoreCase(returnRefundInputDTO.getRuleCalledFrom())) {
                    FraudCustomerDTO fraudCheck = returnRefundInputDTO.getRrrServiceRequest().getOrderInfoResponse().getFraudCustomerDTO();
                    if (fraudCheck != null) {
                        returnRefundInputDTO.setReturnCount(fraudCheck.getReturnCount());
                        returnRefundInputDTO.setExchangeCount(fraudCheck.getExchangeCount());
                        returnRefundInputDTO.setRefundCount(fraudCheck.getRefundCount());
                        returnRefundInputDTO.setFraud(fraudCheck.isFraud());
                        log.info("[ReturnRefundRuleServiceImpl][updateReturnRefundInputDTO] fraud Customer value is set in ReturnInputDTO.");
                    }
                }
            }
        }
    }


    public boolean isBulkOrder(RRRServiceRequest rrrServiceRequest) {
        OrdersHeaderDTO ordersHeader = rrrServiceRequest.getOrderInfoResponse().getOrdersHeader();
        Boolean isFranchiseBulk = rrrServiceRequest.getOrderInfoResponse().getOrders().stream()
                .anyMatch(o -> rrrServiceRequest.getItemRequest().getMagentoItemId().equals(o.getMagentoItemId()) && o.getChannel().equalsIgnoreCase("FranchiseBulk"));
        if ((isFranchiseBulk || (ordersHeader != null && ordersHeader.getIsBulkOrder()))) {
            return true;
        } else {
            return false;
        }
    }

    private String getReturnSource(ReturnDetail returnOrder, ReturnRefundInputDTO returnRefundInputDTO) {
        String intiatedSource = null;
        boolean returnOrderSourceSet = false;

        if (returnOrder != null) {
            log.debug("setting source as " + returnOrder.getSource() + " instead of source received in request for rule engine " + returnRefundInputDTO.getReturnInitiatedSource());
            if (Constant.DIRECT_RECEIVING.equalsIgnoreCase(returnOrder.getSource())) {
                intiatedSource = DecisionTableRefundDTO.RETURN_SOURCE.DIRECT;
            } else {
                intiatedSource = returnOrder.getSource();
            }
            returnOrderSourceSet = true;
            returnRefundInputDTO.setReverseType(returnOrder.getReturnType());
            if (Status.AWAITED_RTO.getValue().equalsIgnoreCase(returnOrder.getReturnType()) && "rtoReceiving".equalsIgnoreCase(returnRefundInputDTO.getRuleCalledFrom())) {
                returnRefundInputDTO.setReverseType(ReverseType.RTO.getName());
            }
        }

        if (returnOrderSourceSet && StringUtils.isNotBlank(intiatedSource)) {
            return intiatedSource;
        }

        if (StringUtils.isNotEmpty(returnRefundInputDTO.getReturnInitiatedSource())) {
            intiatedSource = returnRefundInputDTO.getReturnInitiatedSource();
        } else {
            log.debug("[ReturnRefundRuleServiceImpl][getReturnSource] Sourcane is default as web  ");
            intiatedSource = DecisionTableRefundDTO.RETURN_SOURCE.ONLINE_WEB;
        }
        log.info("[getReturnSource][refundRequest] intiatedSource " + intiatedSource + " for uwItemd " + returnRefundInputDTO.getUwItemId());
        return intiatedSource;
    }


    public void setTriggerPointBasedOnReturnOrder(ReturnRefundInputDTO returnRefundInputDTO, AtomicBoolean updateTriggerPoint, ReturnDetail returnOrder) {

        if (returnOrder != null) {
            returnRefundInputDTO.setReturnId(returnOrder.getId());
            ReturnRefundRule returnRefundRuleLatest = returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(returnRefundInputDTO.getUwItemId());
            if (null != returnRefundRuleLatest && null != returnRefundRuleLatest.getReturnId()) {

                String returnOrderStatus = returnOrderActionService.getReturnOrderStatusById(returnRefundRuleLatest.getReturnId());

                if (StringUtils.isNotEmpty(returnOrderStatus)) {
                    if ("web".equalsIgnoreCase(returnRefundInputDTO.getReturnInitiatedSource())) {
                        if ((ReturnStatus.RETURN_NEED_APPROVAL.getStatus().equalsIgnoreCase(returnOrderStatus)) ||
                                (ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus().equalsIgnoreCase(returnOrderStatus)) ||
                                (ReturnStatus.RETURN_EXPECTED_POS.getStatus().equalsIgnoreCase(returnOrderStatus)) ||
                                (ReturnStatus.RETURN_EXPECTED_WH.getStatus().equalsIgnoreCase(returnOrderStatus))) {
                            updateTriggerPoint.set(false);
                        }
                    }
                    if (IS_RETURN_NOT_REJECTED_AND_CANCELLED_AND_RESHIP.test(returnOrderStatus) && updateTriggerPoint.get()) {
                        TriggerPoint currentTrigger = TriggerPoint.getTriggerPointEnum(returnRefundRuleLatest.getTriggerPoint());
                        TriggerPoint requestedTriggerPoint = TriggerPoint.getTriggerPointEnum(returnRefundInputDTO.getTriggerPoint());
                        log.debug("[ReturnRefundRuleServiceImpl][fetchReturnRefundRule] currentTriggerPoint in table is " + currentTrigger + " and requestedTriggerPoint is " + requestedTriggerPoint);

                        if (currentTrigger.getPriority() > requestedTriggerPoint.getPriority()) {
                            returnRefundInputDTO.setTriggerPoint(currentTrigger.getName());
                        }
                    }
                }
            }
        }
    }
}
