package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;

import java.util.Date;
import java.util.List;

public interface IReturnHeadingPlaceHolderValueFetchStrategy {
    void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse);
    void getReturnTimelinePlaceHolderValue(ReturnTimeLine returnTimeLine, ReturnDetailsResponse returnDetailsResponse);
    void getRefundTimelinePlaceHolderValue(RefundTimeLine refundTimeLine, ReturnDetailsResponse returnDetailsResponse);
    void getExchangeTimelinePlaceHolderValue(ExchangeTimeLine exchangeTimeLine, ReturnDetailsResponse returnDetailsResponse);
    void getReturnHistoryPlaceHolderValue(ReturnStatusHistory returnStatusHistory, ReturnDetailsResponse returnDetailsResponse);
    void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse, List<RefundDetails> refundDetailsList, ReturnStatusHeadingDetail returnStatusHeadingDetail, Long incrementId, Date createdAt, String anyReceivingPoint);
}
