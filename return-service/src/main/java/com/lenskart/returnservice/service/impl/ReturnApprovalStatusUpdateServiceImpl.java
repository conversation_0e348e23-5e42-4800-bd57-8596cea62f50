package com.lenskart.returnservice.service.impl;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returncommon.model.dto.DualRefundRequest;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IReturnApprovalStatusUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ReturnApprovalStatusUpdateServiceImpl implements IReturnApprovalStatusUpdateService {
    @Autowired
    private IKafkaService kafkaService;
    private static final String RETURN_APPROVAL_STATUS = "Return-Approval-Status";

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean updateApprovalStatus(ApprovalStatusRequest approvalStatusRequest) {
        if (isValidRequest(approvalStatusRequest)) {
            try {
                pushStatusSyncToKafka(approvalStatusRequest);
                return true;
            } catch (Exception e) {
                log.info("Caught exception whiile pushing to kafka ", e);
            }
        }
        return false;
    }

    @Override
    public void notifyCustomerRefund(DualRefundRequest refundRequest) {
        log.info("notifying POS for refund action for approval return cycle as retrun status is " + refundRequest.getReturnStatus()
                + " for increment id " + refundRequest.getIncrementId());
        for (Integer uwItemId : refundRequest.getItemIds()) {
            ApprovalStatusRequest approvalStatusRequest = null;
            ResponseEntity<PurchaseOrderDetailsDTO> purchaseOrderDetails = orderOpsFeignClient.getPurchaseOrderDetails(IdentifierType.UW_ITEM_ID.name(), String.valueOf(uwItemId));
            if(purchaseOrderDetails.getStatusCode().is2xxSuccessful() && purchaseOrderDetails.getBody() != null){
                PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = purchaseOrderDetails.getBody();
                List<OrdersDTO> orders = purchaseOrderDetailsDTO.getOrders();
                if(!CollectionUtils.isEmpty(orders)){
                    int magentoItemId = orders.get(0).getMagentoItemId();
                    approvalStatusRequest = new ApprovalStatusRequest();
                    approvalStatusRequest.setMagentoItemId(magentoItemId);
                    approvalStatusRequest.setReturnStatus(refundRequest.getReturnStatus());
                    approvalStatusRequest.setSelectedRefundAction("refund");
                    log.info("[notifyCustomerRefund] : order : {}, approvalRequest : {}",refundRequest.getIncrementId(), approvalStatusRequest);
                    updateApprovalStatus(approvalStatusRequest);
                }
            }
        }
    }

    private boolean isValidRequest(ApprovalStatusRequest approvalStatusRequest) {
        if (approvalStatusRequest == null) {
            log.warn("ApprovalStatusRequest is null");
            return false;
        }

        boolean isValid = approvalStatusRequest.getMagentoItemId() > 0 &&
                (StringUtils.isNotBlank(approvalStatusRequest.getReturnStatus()) ||
                        StringUtils.isNotBlank(approvalStatusRequest.getSelectedRefundAction()));

        if (!isValid) {
            log.info("ApprovalStatusRequest is not valid: {}", approvalStatusRequest);
        }

        return isValid;
    }

    protected void pushStatusSyncToKafka(ApprovalStatusRequest approvalStatusRequest) throws Exception {
        log.info("Pushing approvalStatusRequest to queue: {}", approvalStatusRequest);
        //kafkaService.pushToKafka(RETURN_APPROVAL_STATUS, String.valueOf(approvalStatusRequest.getMagentoItemId()), approvalStatusRequest);
        orderOpsFeignClient.pushData(objectMapper.convertValue(approvalStatusRequest, Map.class),RETURN_APPROVAL_STATUS);
    }
}
