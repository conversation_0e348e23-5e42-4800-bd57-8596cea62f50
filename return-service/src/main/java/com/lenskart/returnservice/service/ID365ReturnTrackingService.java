package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.D365ReturnTrackingRequestDTO;
import com.lenskart.returncommon.model.response.D365ReturnTrackingResponse;
import com.lenskart.returnrepository.entity.D365ReturnTracking;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ID365ReturnTrackingService {
    void saveReturnTrackingEvent(D365ReturnTrackingRequestDTO d365ReturnTrackingRequestDTO);

    D365ReturnTrackingResponse findByReturnId(Integer returnId);

    List<D365ReturnTracking> findDataForReturnRetryForOldData(String fromDate, String toDate, int limit, List<Integer> d365Flag);

    List<D365ReturnTracking> findDataForPSlipRetryForOldData(String fromDate, String toDate, int recordsPerExecution, List<Integer> d365Flags);

}
