package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.dto.NeedApprovalRequest;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnDetailUpdateRequest;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.INeedApprovalProcessorService;
import com.lenskart.returnservice.service.IReturnApprovalStatusUpdateService;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.lenskart.returncommon.model.enums.ReturnStatus.RETURN_APPROVAL_STATUS_UPDATED;
import static com.lenskart.returncommon.utils.Constant.EVENT.REFUND_TRIGGERED;

@Slf4j
@Service
public class NeedApprovalProcessorServiceImpl implements INeedApprovalProcessorService {
    @Autowired
    IReturnApprovalStatusUpdateService returnApprovalStatusUpdate;
    @Autowired
    ReturnUpdateServiceImpl returnUpdateService;
    @Autowired
    DelightActionRepository delightActionRepository;
    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    IReturnOrderActionService returnOrderActionService;

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean isSaveDelightActionProcessed(NeedApprovalRequest needApprovalRequest) throws Exception {
        return processDelightAction(needApprovalRequest);
    }

    private boolean processDelightAction(NeedApprovalRequest needApprovalRequest) throws Exception {
        String currentStatus = returnOrderActionService.getReturnOrderStatusById(needApprovalRequest.getReturnId());
        if (validateRequest(needApprovalRequest)) {
            try {
                updateMagentoItem(needApprovalRequest);

                saveDelightAction(needApprovalRequest, isWarehouseRejection(needApprovalRequest));

                if(needApprovalRequest.getAgentAutoApproval()) {
                    return true;
                }

                ReturnDetailsUpdateResponse returnDetailsUpdateResponse = returnUpdateService.updateReturnStatus(getReturnDetailUpdateRequest(needApprovalRequest));
                log.info("[processDelightAction] ReturnDetailsUpdateResponse: {}", returnDetailsUpdateResponse);
                if (!isWarehouseRejection(needApprovalRequest) && !needApprovalRequest.getAgentAutoApproval()) {
                    boolean isPushedInNeedApprovalQueue = returnApprovalStatusUpdate.updateApprovalStatus(needApprovalRequest.getApprovalStatusRequest());

                    log.info("[processDelightAction] isPushedInNeedApprovalQueue: {}", isPushedInNeedApprovalQueue);

//                    if(isPushedInNeedApprovalQueue){
//                        saveReturnEvent(needApprovalRequest, RETURN_APPROVAL_STATUS_UPDATED.getStatus(), RETURN_APPROVAL_STATUS_UPDATED.getStatus());
//                    }

                    boolean isSaveDelightActionSuccess = isPushedInNeedApprovalQueue && Constant.SUCCESS.equalsIgnoreCase(returnDetailsUpdateResponse.getStatus());
                    handleActionResponse(isSaveDelightActionSuccess, isPushedInNeedApprovalQueue);
                } else {
                    boolean isSaveDelightActionSuccess = Constant.SUCCESS.equalsIgnoreCase(returnDetailsUpdateResponse.getStatus());
                    handleActionResponse(isSaveDelightActionSuccess, true);
                }

                return true;
            } catch (Exception e) {
                log.error("[processDelightAction] Error processing delight action", e);
                saveReturnEvent(needApprovalRequest, Constant.DELIGHT_ACTION_FAILED, e.getMessage());
                saveReturnEvent(needApprovalRequest, currentStatus, e.getMessage());
                throw new Exception(Constant.DELIGHT_ACTION_FAILED);
            }
        } else {
            saveReturnEvent(needApprovalRequest, Constant.DELIGHT_ACTION_REQUEST_VALIDATION_FAILED, Constant.DELIGHT_ACTION_REQUEST_VALIDATION_FAILED);
            throw new Exception(Constant.DELIGHT_ACTION_REQUEST_VALIDATION_FAILED);
        }
    }

    private void updateMagentoItem(NeedApprovalRequest needApprovalRequest) {
        log.info("[updateMagentoItem] : request : {}", needApprovalRequest);
        int returnId = needApprovalRequest.getReturnId();
        List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnEvents)){
            Integer returnRequestId = returnEvents.get(0).getReturnRequestId();
            Optional<ReturnRequest> returnRequestOptional = returnRequestRepository.findById(returnRequestId);
            if(returnRequestOptional.isPresent()){
                ReturnRequest returnRequest = returnRequestOptional.get();
                String identifierValue = returnRequest.getIdentifierValue();
                log.info("[updateMagentoItem] magento : {}", identifierValue);
                needApprovalRequest.getApprovalStatusRequest().setMagentoItemId(Long.parseLong(identifierValue));
            }
        }
    }


    private void handleActionResponse(boolean isSaveDelightActionSuccess, boolean isPushedInNeedApprovalQueue) throws Exception {
        log.info("[processDelightAction] Save delight action success: {}", isSaveDelightActionSuccess);
        if (!isSaveDelightActionSuccess) {
            String exceptionMessage = !isPushedInNeedApprovalQueue ? Constant.DELIGHT_ACTION_PUSHED_QUEUE_EXCEPTION : Constant.DELIGHT_ACTION_RETURN_STATUS_UPDATE_FAILED_EXCEPTION;
            throw new Exception(exceptionMessage);
        }
    }

    private boolean isWarehouseRejection(NeedApprovalRequest needApprovalRequest) {
        return needApprovalRequest.getStatus().equalsIgnoreCase(ReturnStatus.RETURN_NEED_APPROVAL.getStatus()) &&
                needApprovalRequest.getDelightAction().equalsIgnoreCase(Constant.DELIGHT_ACTION_REJECT);
    }


    private ReturnDetailUpdateRequestDto getReturnDetailUpdateRequest(NeedApprovalRequest needApprovalRequest) {
        ReturnDetailUpdateRequestDto request = new ReturnDetailUpdateRequestDto();
        request.setReturnId(needApprovalRequest.getReturnId());
        request.setComments(needApprovalRequest.getComments());
        request.setUsername(needApprovalRequest.getUsername());
        request.setStatus(needApprovalRequest.getStatus());
        return request;
    }


    private boolean validateRequest(NeedApprovalRequest needApprovalRequest) {
        return (Objects.nonNull(needApprovalRequest)
                && Objects.nonNull(needApprovalRequest.getApprovalStatusRequest())
                && needApprovalRequest.getReturnId() != 0
                && StringUtils.isNotBlank(needApprovalRequest.getDelightAction()));
    }

    private void saveDelightAction(NeedApprovalRequest needApprovalRequest, boolean isWarehouseRejection) {
        DelightAction delightAction = getOrCreateDelightAction(needApprovalRequest.getReturnId());
        delightAction.setDelightAction(needApprovalRequest.getDelightAction());
        delightAction.setDelightMethod(needApprovalRequest.getDelightMethod());
        delightAction.setCreatedAt(Timestamp.valueOf(LocalDateTime.now()));
        delightAction.setComments(needApprovalRequest.getComments());
        delightAction.setWarehouseComments(isWarehouseRejection ? needApprovalRequest.getDelightComment() : null);
        delightAction.setReturnId(needApprovalRequest.getReturnId());
        log.info("[saveDelightAction] Saving DelightAction: {}", delightAction);
        delightActionRepository.save(delightAction);
        saveReturnEvent(needApprovalRequest, "DELIGHT_ACTION_"+needApprovalRequest.getDelightAction(), needApprovalRequest.getComments());
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTopByReturnId(needApprovalRequest.getReturnId());
        if(returnDetailItem != null){
            String productDeliveryType = returnDetailItem.getProductDeliveryType();
            if("B2B".equalsIgnoreCase(productDeliveryType)){
                log.info("[saveDelightAction] B2B case: {}", needApprovalRequest.getReturnId());
                Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnDetailItem.getReturnId());
                Integer b2BUwItemId = null;
                if(returnDetailOptional.isPresent()){
                    ReturnDetail returnDetail = returnDetailOptional.get();
                    ResponseEntity<PurchaseOrderDetailsDTO> purchaseOrderDetails = orderOpsFeignClient.getPurchaseOrderDetails("INCREMENT_ID", String.valueOf(returnDetail.getIncrementId()));
                    if(purchaseOrderDetails.getStatusCode().is2xxSuccessful() && purchaseOrderDetails.getBody() != null){
                        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = purchaseOrderDetails.getBody();
                        List<UwOrderDTO> uwOrders = purchaseOrderDetailsDTO.getUwOrders();
                        if(!CollectionUtils.isEmpty(uwOrders)){
                            Optional<UwOrderDTO> uwOrderDTO = uwOrders.stream().filter(uw -> Objects.equals(uw.getB2bRefrenceItemId(), returnDetailItem.getUwItemId())).findAny();
                            if(uwOrderDTO.isPresent()){
                                b2BUwItemId = uwOrderDTO.get().getUwItemId();
                                log.info("[saveDelightAction] B2B case: uwItemId : {}", b2BUwItemId);
                            }
                        }
                    }
                }
                log.info("[saveDelightAction] b2BUwItemId : {}", b2BUwItemId);
                if(b2BUwItemId != null){
                    ReturnDetailItem detailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(b2BUwItemId);
                    if(detailItem != null){
                        Integer returnId = detailItem.getReturnId();
                        delightAction = getOrCreateDelightAction(returnId);
                        delightAction.setDelightAction(needApprovalRequest.getDelightAction());
                        delightAction.setDelightMethod(needApprovalRequest.getDelightMethod());
                        delightAction.setCreatedAt(Timestamp.valueOf(LocalDateTime.now()));
                        delightAction.setComments(needApprovalRequest.getComments());
                        delightAction.setWarehouseComments(isWarehouseRejection ? needApprovalRequest.getDelightComment() : null);
                        delightAction.setReturnId(returnId);
                        log.info("[saveDelightAction] Saving DelightAction: {}", delightAction);
                        delightActionRepository.save(delightAction);
                    }
                }
            }
        }
    }

    private void saveReturnEvent(NeedApprovalRequest request, String event, String remarks) {
        List<ReturnEvent> returnEvents = returnEventRepository.findByReturnIdOrderByIdDesc(request.getReturnId());
        Integer requestId = returnEvents.stream().map(ReturnEvent::getReturnRequestId).filter(Objects::nonNull).findAny().orElse(null);
        returnEventService.createReturnEvent(requestId, request.getReturnId(), event, remarks);
    }

    private DelightAction getOrCreateDelightAction(Integer returnId) {
        DelightAction delightAction = delightActionRepository.findTop1ByReturnIdOrderByIdDesc(returnId);
        return Objects.nonNull(delightAction) ? delightAction : new DelightAction();
    }
}
