package com.lenskart.returnservice.service.helper;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.ordermetadata.dto.ReturnHistoryDTO;
import com.lenskart.orderops.model.SbrtOrderItem;
import com.lenskart.platform.fl.utils.dto.returnPayloads.salesOrder.MiscChargesHeader;
import com.lenskart.platform.fl.utils.dto.returnPayloads.salesOrder.MiscChargesLine;
import com.lenskart.platform.fl.utils.dto.returnPayloads.salesOrder.SalesOrderHeader;
import com.lenskart.platform.fl.utils.dto.returnPayloads.salesOrder.SoLine;
import com.lenskart.platform.fl.utils.enums.EventStatus;
import com.lenskart.platform.fl.utils.finance.service.FinanceService;
import com.lenskart.platform.fl.utils.jpa.dto.FLEventsTrackerDto;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.NavChannel;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE;
import com.lenskart.returncommon.utils.Constant.SYSTEM_PREFERENCE_GROUPS;
import com.lenskart.returncommon.utils.Constant.SYSTEM_PREFERENCE_KEYS;
import com.lenskart.returnrepository.entity.ReturnHistory;
import com.lenskart.returnrepository.entity.ReturnOrder;
import com.lenskart.returnrepository.repository.ReturnHistoryRepository;
import com.lenskart.returnservice.feignclient.CentralPosFeignClient;
import com.lenskart.returnservice.feignclient.NexsFeignClient;
import com.lenskart.returnservice.feignclient.POSFeignClient;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.impl.SystemPreferenceServiceImpl;
import com.lenskart.returnservice.utils.ProductUtil;
import com.lenskart.returnservice.utils.SbrtOrderMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.lenskart.returncommon.utils.Constant.*;
import static com.lenskart.returncommon.utils.Constant.CHANNEL.AQUALENS;
import static com.lenskart.returncommon.utils.Constant.CHANNEL.MARKETPLACE;
import static com.lenskart.returncommon.utils.Constant.Country.*;
import static com.lenskart.returncommon.utils.Constant.PAYMENT_METHOD.COD_MODE;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_METHOD.RPU;

@Component
@Slf4j
@RequiredArgsConstructor
public class ReturnEventServiceHelper {

    DateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);

    private final CentralPosFeignClient centralPosFeignClient;

    private final POSFeignClient posFeignClient;

    @Value("${pos.app.key:valyoo123}")
    private String lenskartApiKey;

    @Value("${pos.app.id:connect}")
    private String lenskartAppId;

    @Value("${pos.session.user:<EMAIL>}")
    private String posSessionUser;

    @Value("${pos.session.password:welcome2}")
    private String posSessionPass;

    @Value("${online-order.storeCode:LKST4293}")
    private String onlineOrderStoreCode;

    private final NexsFeignClient nexsFeignClient;

    private final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    private final SystemPreferenceServiceImpl systemPreferenceService;

    private final Gson gson;

    private final ReturnHistoryRepository returnHistoryRepository;

    private final IReturnEventService returnEventService;

    private final FinanceService financeService;

    @Value("#{'${finance.classification.blank.hsncode:11356}'.split(',')}")
    private Set<String> blankHsnCodeClassifications;

    @Value("#{'${confirmed.receipt.date.valid.status:awb_assigned}'.split(',')}")
    private List<String> VALID_STATUS_FOR_SETTING_CONFIRMED_RECEIPT_DATE;

    @Value("${saleOrder.sbrt.sync.enabled:true}")
    private boolean isSBRTFlowEnabledForSalesOrder;

    @Value("${sbrt.item.flag:SBRT}")
    private String SBRTItemFlag;

    @Value("${non-sbrt.item.flag:NON_SBRT}")
    private String nonSBRTItemFlag;

    @Value("${default.cost.price.flag:true}")
    private boolean defaultCostPriceFlag;

    @Value("${default.cost.price:100.00}")
    private String defaultCostPrice;

    @Value("${default.b2c.customerId:999999}")
    private String B2C_CUSTOMER_ID;

    @Value("#{'${finance.intCountries:SA,SG,AE,US,TH}'.split(',')}")
    private List<String> intCountries;

    @Value("#{'${finance.nexsFacilities:NXS1,NXS2,DXB1,NFT001,NFT003,NFT003,SGNXS1,INSR,EXE2,SAU001,QNXS2,0QNXS,UAE1,PBR01,PBR02,BR01,BR02}'.split(',')}")
    private  List<String> nexsFacilities;

    @Value("#{'${finance.manesarFacilities:DK02,01,FT0003,FT0001}'.split(',')}")
    private List<String> MANESAR_FACILITY_CODE;

    @Value("${finance.sbrtWebEnabled:true}")
    private boolean IS_SBRT_WEB_ENABLED;

    DecimalFormat decimalFormat = new DecimalFormat("#0.00");

    private final InventoryDataHelper inventoryDataHelper;

    public void setReturnSpecificDetailsToReturnSales(ReturnDetailAddressUpdateDTO returnOrderAddressUpdate, SalesOrderHeader salesOrderHeader, OrdersHeaderDTO ordersHeader, OrdersDTO order) {
        log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales] inside setReturnSpecificDetailsToReturnSales");
        try {
            if (Objects.isNull(returnOrderAddressUpdate)) {
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales] returnOrderAddressUpdate found NULL, adding blank values in address");
                prepareBlankDataForReturnPayload(salesOrderHeader);
            } else {
                salesOrderHeader.setFirstName(StringUtils.defaultString(returnOrderAddressUpdate.getFirstName()));
                salesOrderHeader.setMiddleName("");
                salesOrderHeader.setLastName(StringUtils.defaultString(returnOrderAddressUpdate.getLastName()));
                salesOrderHeader.setExportReason(getTaxCheck(returnOrderAddressUpdate.getCountry(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setIsWithTax(getTaxCheck(returnOrderAddressUpdate.getCountry(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setDeliveryName(StringUtils.defaultString(getDeliveryNameForReturn(order, returnOrderAddressUpdate)));
                setAddressForReturn(salesOrderHeader, order, returnOrderAddressUpdate);
                salesOrderHeader.setContactPersonId(StringUtils.defaultString(returnOrderAddressUpdate.getEmail()));
                salesOrderHeader.setEmail(StringUtils.defaultString(returnOrderAddressUpdate.getEmail()));
                salesOrderHeader.setPhone(StringUtils.defaultString(returnOrderAddressUpdate.getTelephone()));
                salesOrderHeader.setOrderType(ORDER_TYPE);
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales] exit from setReturnSpecificDetailsToReturnSales");
            }
        } catch (Exception e) {
            log.error("setReturnSpecificDetailsToReturnSales : exception found : ",e);
            throw e;
        }
    }

    private void prepareBlankDataForReturnPayload(SalesOrderHeader salesOrderHeader) {
        log.info("[ReturnOrderServiceImpl][prepareBlankDataForReturnPayload] adding blank values for address");
        salesOrderHeader.setFirstName(BLANK);
        salesOrderHeader.setMiddleName(BLANK);
        salesOrderHeader.setLastName(BLANK);
        salesOrderHeader.setExportReason(BLANK);
        salesOrderHeader.setIsWithTax(BLANK);
        salesOrderHeader.setDeliveryName(BLANK);
        salesOrderHeader.setAddressStreet(BLANK);
        salesOrderHeader.setAddressZipCode(BLANK);
        salesOrderHeader.setAddressCountryRegionId(BLANK);
        salesOrderHeader.setAddressState(BLANK);
        salesOrderHeader.setAddressCity(BLANK);
        salesOrderHeader.setContactPersonId(BLANK);
        salesOrderHeader.setEmail(BLANK);
        salesOrderHeader.setPhone(BLANK);
        salesOrderHeader.setOrderType(ORDER_TYPE);
        log.info("[ReturnOrderServiceImpl][prepareBlankDataForReturnPayload] added blank values for address");
    }

    private String getTaxCheck(String returnAddressCountry, String legalOwnerCountry) {
        if(Objects.nonNull(returnAddressCountry) && Objects.nonNull(legalOwnerCountry)) {
            String formattedCountry = returnAddressCountry.equalsIgnoreCase("india") ? "IN" : returnAddressCountry;
            return formattedCountry.equals(legalOwnerCountry) ? "yes" : "no";
        }
        return "no";
    }

    private String getDeliveryNameForReturn(OrdersDTO order, ReturnDetailAddressUpdateDTO returnOrderAddressUpdate) {
        return returnOrderAddressUpdate.getFirstName() + " " + returnOrderAddressUpdate.getLastName();
    }

    private void setAddressForReturn(SalesOrderHeader salesOrderHeader, OrdersDTO order, ReturnDetailAddressUpdateDTO returnOrderAddressUpdate) {
        if (!order.getShipToStoreRequired()) {
            // STC
            salesOrderHeader.setAddressStreet(returnOrderAddressUpdate.getStreet1());
            salesOrderHeader.setAddressZipCode(String.valueOf(returnOrderAddressUpdate.getPostcode()));
            if (returnOrderAddressUpdate.getCountry().equals("IN")) {
                salesOrderHeader.setAddressCountryRegionId("IND");
                salesOrderHeader.setAddressState(ProductUtil.StateMapping.getOrDefault(returnOrderAddressUpdate.getState().toUpperCase().replaceAll("\\s", ""), ""));
                salesOrderHeader.setAddressCity(returnOrderAddressUpdate.getCity());
            } else {
                salesOrderHeader.setAddressCountryRegionId(returnOrderAddressUpdate.getCountry());
                salesOrderHeader.setAddressState("INT");
            }
        }
    }

    public void setReturnSpecificDetailsToReturnSales(SalesOrderHeader salesOrderHeader,OrderAddressUpdateDTO orderAddressUpdate,OrdersHeaderDTO ordersHeader,OrdersDTO order) {
        try {
            if (Objects.isNull(orderAddressUpdate)) {
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales1] orderAddressUpdate found NULL, adding blank values in address");
                prepareBlankDataForReturnPayload(salesOrderHeader);
            } else {
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales1] ordersHeader : " + ordersHeader.toString());
                salesOrderHeader.setFirstName(StringUtils.defaultString(orderAddressUpdate.getFirstName()));
                salesOrderHeader.setMiddleName("");
                salesOrderHeader.setLastName(StringUtils.defaultString(orderAddressUpdate.getLastName()));
                salesOrderHeader.setExportReason(getTaxCheck(orderAddressUpdate.getCountryId(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setIsWithTax(getTaxCheck(orderAddressUpdate.getCountryId(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setDeliveryName(StringUtils.defaultString(getDeliveryName(order, orderAddressUpdate)));
                setAddress(salesOrderHeader, order, orderAddressUpdate);
                salesOrderHeader.setContactPersonId(StringUtils.defaultString(orderAddressUpdate.getEmail()));
                salesOrderHeader.setEmail(StringUtils.defaultString(orderAddressUpdate.getEmail()));
                salesOrderHeader.setPhone(StringUtils.defaultString(orderAddressUpdate.getTelephone()));
                salesOrderHeader.setOrderType("Returned order");
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales1] exit from setReturnSpecificDetailsToReturnSales");
            }
        } catch (Exception e) {
            log.info("setReturnSpecificDetailsToReturnSales1 : exception found : ",e);
            throw e;
        }

    }

    private String getDeliveryName(OrdersDTO order, OrderAddressUpdateDTO orderAddressUpdate) {
        return orderAddressUpdate.getFirstName() + " " + orderAddressUpdate.getLastName();
    }

    private void setAddress(SalesOrderHeader salesOrderHeader, OrdersDTO order, OrderAddressUpdateDTO orderAddressUpdate){
        salesOrderHeader.setAddressStreet(orderAddressUpdate.getStreet());
        salesOrderHeader.setAddressZipCode(orderAddressUpdate.getPostcode());
        if(orderAddressUpdate.getCountryId().equals("IN")){
            salesOrderHeader.setAddressCountryRegionId("IND");
            salesOrderHeader.setAddressState(ProductUtil.StateMapping.getOrDefault(orderAddressUpdate.getRegion().toUpperCase().replaceAll("\\s", ""),""));
            salesOrderHeader.setAddressCity(orderAddressUpdate.getCity());
        }
        else {
            salesOrderHeader.setAddressCountryRegionId(orderAddressUpdate.getCountryId());
            salesOrderHeader.setAddressState("INT");
        }
    }

    public void addReturnSalesDetails(UwOrderDTO uwOrder, OrdersDTO order, OrdersHeaderDTO ordersHeader, SalesOrderHeader salesOrderHeader, List<SoLine> soLines, List<MiscChargesLine> miscChargesLines, List<MiscChargesHeader> miscChargesHeader, String salesOrderNumber, String rmaNumber, com.lenskart.returnrepository.entity.ReturnOrder returnOrder, ReturnCreateRequest returnRequestDto, boolean sbrtFlag) throws Exception {
        log.info("[ReturnOrderServiceImpl][addReturnSalesDetails] inside addReturnSalesDetails");
        SoLine soLine;
        soLine = new SoLine();
        // MiscChargesHeader miscChargesHeaderPayload = new MiscChargesHeader();
        // MiscChargesLine miscChargesLine = new MiscChargesLine();
        String salesChannel = "";
        String subChannel = "";
        boolean isOTC = false;
        String inventLocationIdForReturn = "";
        String fulfillmentWh = "";
        Integer uwItemId = uwOrder.getUwItemId();

        try {
            List<String> hubMasterFacilityCodes = inventoryDataHelper.getHubMasterFacilityCodes();
            UwOrderDTO b2bCounterPart = getB2BCounterPart(uwOrder);

            salesChannel = getSalesChannel(uwOrder, ordersHeader);
            subChannel = getSubChannel(uwOrder, order);

            log.info("ReturnOrderServiceImpl : preparing salesOrderHeader for : {}", uwItemId);
            salesOrderHeader.setIsUpdateSalesOrder(0);
            salesOrderHeader.setSalesOrderNumber(StringUtils.defaultString(salesOrderNumber));
            salesOrderHeader.setRmaNumber(StringUtils.defaultString(rmaNumber));

            isOTC = OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType());
            if(Boolean.TRUE.equals(isOTC)){
                inventLocationIdForReturn = salesChannel;
            }else{
                inventLocationIdForReturn = getInventLocationIdForReturn(uwOrder, ordersHeader,hubMasterFacilityCodes);
                fulfillmentWh = getFulfillmentWH(uwOrder, hubMasterFacilityCodes, b2bCounterPart);
            }

            salesOrderHeader.setInventLocationId(inventLocationIdForReturn);
            setLegalEntity(uwOrder, salesOrderHeader, ordersHeader, sbrtFlag);
            setModeOfPayment(order, salesOrderHeader, uwOrder);
            salesOrderHeader.setMethodOfPayment("Prepaid");
            setCourierCode(uwOrder, b2bCounterPart, returnOrder, salesOrderHeader);
            setRefInvoices(uwOrder, b2bCounterPart, salesOrderHeader);
            salesOrderHeader.setSalesChannel(salesChannel);
            salesOrderHeader.setCustomerGroup(GROUP);
            salesOrderHeader.setGender(GENDER);
            salesOrderHeader.setStores(getReturnStoreCode(ordersHeader, uwOrder));
            salesOrderHeader.setSubChannel(StringUtils.defaultString(subChannel));
            salesOrderHeader.setCurrencyCode(ProductUtil.CountryCurrencyMapping.get(ordersHeader.getLkCountry()));
            salesOrderHeader.setModeOfDelivery(MODE_OF_DELIVERY);
            salesOrderHeader.setWebOrderNo(String.valueOf(order.getIncrementId()));
            salesOrderHeader.setTcsGroup(BLANK);
            salesOrderHeader.setTdsGroup(BLANK);
            if (Objects.nonNull(returnOrder)) {
                salesOrderHeader.setOrderCreationDate(dateFormat.format(returnOrder.getReturnCreateDatetime()));
            }
            salesOrderHeader.setFulfillmentWH(fulfillmentWh);
            salesOrderHeader.setIsIntegrated(1);
            setCustomerDetails(salesOrderHeader, ordersHeader, order, uwOrder);
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to prepare salesOrderHeader for : {} : ", uwItemId, e);
            throw e;
        }

        try {
            List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
            uwOrderDTOList.add(uwOrder);
            List<UwOrderDTO> lensUwOrders = inventoryDataHelper.getLensUWOrdersList(uwOrder.getIncrementId(),uwItemId);
            uwOrderDTOList.addAll(lensUwOrders);
            Set<Integer> productIds = new HashSet<>();
            Set<Integer> b2bReferenceItemIds = new HashSet<>();

            for (UwOrderDTO uw : uwOrderDTOList) {
                productIds.add(uw.getProductId());
                if (B2B.equals(uw.getProductDeliveryType())) {
                    b2bReferenceItemIds.add(uw.getB2bRefrenceItemId());
                }
            }

            List<UwOrderDTO> b2bUwItemOrders = getB2BUwOrders(b2bReferenceItemIds.stream().toList());
            Map<Integer, UwOrderDTO> b2bUwItemOrdersMap = b2bUwItemOrders.stream()
                    .collect(Collectors.toMap(
                            UwOrderDTO::getUwItemId,
                            Function.identity()
                    ));
            // Products
            List<ProductDto> productList = inventoryDataHelper.getProductsByIds(new ArrayList<>(productIds));
            Map<Integer, ProductDto> productMap = productList.stream()
                    .collect(Collectors.toMap(ProductDto::getProductId, p -> p));

            Set<Integer> allClassIds = productList.stream()
                    .map(ProductDto::getClassification)
                    .collect(Collectors.toSet());

            List<ClassificationDto> classificationList = inventoryDataHelper.getClassificationsByIds(new ArrayList<>(allClassIds));
            Map<Integer, ClassificationDto> classificationMap = classificationList.stream()
                    .collect(Collectors.toMap(ClassificationDto::getId, c -> c));

            if (!CollectionUtils.isEmpty(uwOrderDTOList)) {
                for (UwOrderDTO uwOrderDTO : uwOrderDTOList) {
                    UwOrderDTO preSbrtUwOrder = cloneUwOrders(uwOrderDTO);
                    UwOrderDTO b2bReferenceOrder = b2bUwItemOrdersMap.get(uwOrderDTO.getB2bRefrenceItemId());
                    setSbrtUwOrderDetails(uwOrderDTO, sbrtFlag);
                    log.info("preparing soLine for uwItemId : {}", uwOrderDTO.getUwItemId());
                    soLine = prepareSoLinesPayload(uwOrderDTO, productMap, classificationMap, ordersHeader, order, preSbrtUwOrder, b2bReferenceOrder, salesOrderHeader, returnOrder);
                    // miscChargesLine = prepareMiscChargesLines();
                    // miscChargesHeaderPayload = prepareMiscChargesHeader();
                    log.info("soLine prepared for uwItemId : {} : {}", uwOrderDTO.getUwItemId(), gson.toJson(soLine));
                    // log.info("miscChargesLine prepared for uwItemId : {} : {}", uwOrderDTO.getUwItemId(), gson.toJson(miscChargesLine));
                    // log.info("miscChargesHeaderPayload prepared for uwItemId : {} : {}", uwOrderDTO.getUwItemId(), gson.toJson(miscChargesHeaderPayload));
                    soLines.add(soLine);
                    // miscChargesHeader.add(miscChargesHeaderPayload);
                    // miscChargesLines.add(miscChargesLine);
                    log.info("final soLines prepared for uwItemId : {} : {}", uwOrderDTO.getUwItemId(), gson.toJson(soLines));
                }
            }
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to prepare soLine for : {} : ",uwItemId,e);
            throw e;
        }

        salesOrderHeader.setSoLines(soLines);
        salesOrderHeader.setMiscChargesLines(miscChargesLines);
        salesOrderHeader.setMiscChargesHeader(miscChargesHeader);
        log.info("[ReturnOrderServiceImpl] salesOrderHeader prepared for uwItemId : {} : {}", uwItemId, gson.toJson(salesOrderHeader));
    }

    public UwOrderDTO getB2BCounterPart(UwOrderDTO uwOrder) {
        if(B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            List<UwOrderDTO> b2bCounterPartList = inventoryDataHelper.getFilteredUwOrdersByGivenUwItemId(uwOrder.getB2bRefrenceItemId());
            if(b2bCounterPartList != null && b2bCounterPartList.size() > 0) {
                return b2bCounterPartList.get(0);
            }
        }
        return null;
    }

    private MiscChargesHeader prepareMiscChargesHeader(){
        return MiscChargesHeader.builder()
                .salesChargeCode(BLANK)
                .chargeLineNumber(BLANK)
                .chargeCategory(BLANK)
                .salesTaxGroupCode(BLANK)
                .salesTaxItemGroupCode(BLANK)
                .fixedChargeAmount(BLANK)
                .build();
    }

    private MiscChargesLine prepareMiscChargesLines(){
        return MiscChargesLine.builder()
                .salesChargeCode(BLANK)
                .chargeLineNumber(BLANK)
                .chargeCategory(BLANK)
                .salesTaxItemGroupCode(BLANK)
                .salesTaxGroupCode(BLANK)
                .fixedChargeAmount(BLANK)
                .allocateAfter(BLANK)
                .cardNo(BLANK)
                .categoryOfProducts(BLANK)
                .chargeAccountingCurrencyCode(BLANK)
                .consumableItem(BLANK)
                .costPriceWithoutTax(BLANK)
                .costPriceWithTax(BLANK)
                .coverage(BLANK)
                .customerInvoiceDate(BLANK)
                .customerReference(BLANK)
                .defaultLedgerDimensionDisplayValue(BLANK)
                .deliveryAddressLocationId(BLANK)
                .dlvMode(BLANK)
                .dropShipSalesLineNumber(BLANK)
                .exportOrder(BLANK)
                .giftVoucher(BLANK)
                .goldMembershipEndDate(NULL)
                .goldMembershipNumber(BLANK)
                .goldMembershipStartDate(NULL)
                .goldMembershipType(BLANK)
                .incrementId(BLANK)
                .insuranceProduct(BLANK)
                .integration(BLANK)
                .integrationProcessStatus(BLANK)
                .invoiceDate(NULL)
                .irnCode(BLANK)
                .isItemCreate(BLANK)
                .issuanceFromDate(NULL)
                .issuanceToDate(NULL)
                .itemTemplateNameStr(BLANK)
                .lineDiscountPercentage(BLANK)
                .markupLineSalesLineNumber(BLANK)
                .modeOfPaym(BLANK)
                .orgInvoiceNo(BLANK)
                .purchPriceStr(BLANK)
                .receiptDateConfirmed(NULL)
                .returnItemNum(BLANK)
                .salesUnitSymbol(BLANK)
                .sellingPriceWithTax(BLANK)
                .text(BLANK)
                .txnId(BLANK)
                .updateHsnAndSacCodeLineNumber(BLANK)
                .vendorInvoiceDate(NULL)
                .warrantyProduct(BLANK)
                .build();
    }

    private SoLine prepareSoLinesPayload(UwOrderDTO uwOrder, Map<Integer, ProductDto> productMap,Map<Integer, ClassificationDto> classificationMap,OrdersHeaderDTO ordersHeader,OrdersDTO order,UwOrderDTO preSbrtUwOrder,UwOrderDTO b2bUwItemOrder,SalesOrderHeader salesOrderHeader, com.lenskart.returnrepository.entity.ReturnOrder returnOrder) throws Exception {
        Integer uwItemId = uwOrder.getUwItemId();
        SoLine soLine = null;
        int classificationId = 0;
        String salesChannel = "";
        String subChannel = "";
        ProductDto productDto = null;
        ClassificationDto classificationDto = null;
        log.info("ReturnOrderServiceImpl : preparing soLines for uwItemId : {}",uwItemId);
        try {
            soLine = new SoLine();
            log.info("soLine uwOrder : {}",uwOrder);
            productDto = productMap.get(uwOrder.getProductId());
            log.info("soLine product : {}",productDto);
            if (Objects.nonNull(productDto)) {
                classificationId = productDto.getClassification();
            }
            classificationDto = classificationMap.get(classificationId);
            log.info("soLine classificationDisplayName : {}, {}",uwItemId, classificationDto.getClassificationName());
            salesChannel = getSalesChannel(uwOrder, ordersHeader);
            subChannel = getSubChannel(uwOrder, order);
            log.info("soLine salesChannel subChannel : {}, {}, {}",uwItemId, salesChannel, subChannel);
            setSoLineBasicDetails(uwOrder, order, soLine);
            setSoLinePrices(preSbrtUwOrder, soLine,b2bUwItemOrder);
            soLine.setInventLocationId(salesOrderHeader.getInventLocationId());
            soLine.setStores(getReturnStoreCode(ordersHeader, uwOrder));
            setSoLineDiscount(uwOrder, soLine);
            setSoLineConfirmedReceiptDate(returnOrder, uwOrder, soLine);
            soLine.setInventSiteId(BLANK);
            soLine.setBrand(productDto.getBrand().trim());
            soLine.setSubChannel(subChannel);
            log.info("ReturnOrderServiceImpl : step1 completed for uwItemId : {}",uwItemId);
            if (B2B.equals(uwOrder.getProductDeliveryType()) && !(FOFOB2B.equals(uwOrder.getNavChannel()))
                    && !(intCountries.contains(ordersHeader.getLkCountry()))) {
                soLine.setDeliveryType(StringUtils.defaultString(DIRECT_DELIVERY));
            } else {
                soLine.setDeliveryType(STOCK);
            }
            if (order.getProductDeliveryType().equals(B2B) && !uwOrder.getNavChannel().equals(FOFOB2B)
                    && !(intCountries.contains(ordersHeader.getLkCountry()))) {
                soLine.setSourcingVendor(SOURCING_VENDOR);
            } else {
                soLine.setSourcingVendor(BLANK);
            }
            soLine.setSalesChannel(salesChannel);
            soLine.setMagentoItemId(order.getMagentoItemId());
            soLine.setItemClassification(classificationDto.getDisplayName());
            setSoLineHsnAndSacCode(productDto, soLine, classificationId, uwOrder);
            setSoLineTax(uwOrder, soLine,b2bUwItemOrder);
            setSoLinePurchPrice(uwOrder, soLine,b2bUwItemOrder);
            soLine.setItemTemplateName(BLANK);
            soLine.setDeliveryModeCode(BLANK);
            List<Integer> SBRTUwItemIds =null;
            if(isSBRTFlowEnabledForSalesOrder){
                SBRTUwItemIds = inventoryDataHelper.fetchUwItemIdsFromSBRTOrderItem(uwItemId);
            }
            setSBRTFlagForLineItem(uwOrder, SBRTUwItemIds, soLine);
            soLine.setCostCenter(BLANK);
            soLine.setPartnerType(BLANK);
            soLine.setEmployee(BLANK);
            soLine.setLkReferenceWh(getLkReferenceWhForReturn(uwOrder,ordersHeader, returnOrder,b2bUwItemOrder));
            soLine.setBarcode(uwOrder.getBarcode());
            String lkPurchPrice = "";
            if (B2B.equals(uwOrder.getProductDeliveryType())) {
                if (b2bUwItemOrder != null) {
                    lkPurchPrice = getUnitPrice(b2bUwItemOrder);
                    if(!StringUtils.isEmpty(lkPurchPrice)) {
                        soLine.setLkPurchasePrice(Double.valueOf(lkPurchPrice));
                    }
                }
            }

            log.info("ReturnOrderServiceImpl : soLines prepared for uwItemId : {} : {}",uwItemId, gson.toJson(soLine));
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to prepare soLine : {}",uwItemId, e);
            throw e;
        }
        return soLine;
    }

    private UwOrderDTO cloneUwOrders(UwOrderDTO uwOrder) {
        UwOrderDTO newUwOrder =  new UwOrderDTO();
        newUwOrder.setNavChannel(uwOrder.getNavChannel());
        newUwOrder.setProductDeliveryType(uwOrder.getProductDeliveryType());
        newUwOrder.setUwItemId(uwOrder.getUwItemId());
        newUwOrder.setBarcode(uwOrder.getBarcode());
        newUwOrder.setFacilityCode(uwOrder.getFacilityCode());
        newUwOrder.setB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
        newUwOrder.setIncrementId(uwOrder.getIncrementId());
        newUwOrder.setCreatedAt(uwOrder.getCreatedAt());
        return newUwOrder;
    }

    private String getSalesChannel(UwOrderDTO uwOrder, OrdersHeaderDTO ordersHeader) {
        log.info("ReturnUtil : getSalesChannel for uwOrder : {}", uwOrder.getUwItemId());
        String salesChannel= "";
        Integer uwItemId = uwOrder.getUwItemId();

        try {
            if (uwOrder.getIsLocalFittingRequired()) {
                log.info("ReturnUtil : localFit order found for uwOrder : {}", uwOrder.getUwItemId());
                if (ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
                    salesChannel = "SGDK01";
                } else {
                    log.info("getSalesChannel : fetching details from pos for uwOrder : {}", uwOrder.getUwItemId());
                    salesChannel = "DK02";
                }
            } else {
                log.info("getSalesChannel : fetching details from ordersHeader for uwOrder : {}", uwOrder.getUwItemId());
                salesChannel = getStoreCode(ordersHeader);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : exception found to get salesChannel for uwItemid : {}", uwItemId);
        }
        log.info("ReturnUtil : salesChannel for uwOrder : {} : {}",uwItemId, salesChannel);
        return salesChannel;
    }

    private String getStoreCode(OrdersHeaderDTO ordersHeader){
        String storeCode = ordersHeader.getFacilityCode();
        if(null!=storeCode && !("0".equalsIgnoreCase(storeCode))) {
            return storeCode;
        }
        return "";
    }

    private String getSubChannel(UwOrderDTO uwOrder,OrdersDTO order){
        String saleSource = order.getSaleSource();
        if(saleSource.contains(AQUALENS) || uwOrder.getNavChannel().equalsIgnoreCase(MARKETPLACE))
            return saleSource;
        return uwOrder.getNavChannel();
    }

    public String getInventLocationIdForReturn(UwOrderDTO uwOrder,OrdersHeaderDTO ordersHeader,List<String>hubMasterFacilityCodes){
        if (uwOrder != null && uwOrder.getIsLocalFittingRequired()) {
            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                return "DK02";
            } else if (ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
                return "SGDK01";
            }

        } else {
            String type= ordersHeader.getStoreType();
            String storeCode = "";
            String productDeliveryType = getProductDeliveryType(uwOrder.getNavChannel());
            if(DTC.equalsIgnoreCase(productDeliveryType) || BULK.equalsIgnoreCase(productDeliveryType)){
                storeCode = uwOrder.getFacilityCode();
                return  String.format("%s%s", storeCode, hubMasterFacilityCodes.contains(storeCode) ? INTRANSIT : "");
            }
            else{
                return uwOrder.getFacilityCode();
            }
        }
        return uwOrder.getFacilityCode();
    }

    private String getProductDeliveryType(String navChannel){
        String productDeliveryType = "";
        if(StringUtils.isNotBlank(navChannel)){
            if(navChannel.contains(B2B)){
                productDeliveryType = B2B;
            } else if (navChannel.contains(DTC)) {
                productDeliveryType = DTC;
            }else if(navChannel.contains(OTC)){
                productDeliveryType = OTC;
            }else if(navChannel.toLowerCase().contains("bulk")){
                productDeliveryType = BULK;
            }
        }
        return productDeliveryType;
    }

    public String getFulfillmentWH(UwOrderDTO uwOrder,List<String>hubMasterFacilityCodes,UwOrderDTO b2bCounterPart){
        String fulFillmentWH = "";

        String productDeliveryType = getProductDeliveryType(uwOrder.getNavChannel());
        if(productDeliveryType.equalsIgnoreCase("B2B")){
            Optional<String> physicalFacility = getPhysicalFacilityForB2BOrder(uwOrder,b2bCounterPart,hubMasterFacilityCodes);
            if (physicalFacility.isPresent()) {
                fulFillmentWH = physicalFacility.get()+INTRANSIT;
            }
        }
        if(productDeliveryType.equalsIgnoreCase("bulk")){
            log.info("[getFulfillmentWH] {}", uwOrder.getFacilityCode());
            if (hubMasterFacilityCodes.contains(uwOrder.getFacilityCode())) {
                fulFillmentWH = uwOrder.getFacilityCode()+INTRANSIT;
            }
        }
        if(productDeliveryType.equalsIgnoreCase("DTC")){
            log.info("[getFulfillmentWH] {}", uwOrder.getFacilityCode());
            if (hubMasterFacilityCodes.contains(uwOrder.getFacilityCode())) {
                fulFillmentWH = uwOrder.getFacilityCode()+INTRANSIT;
            } else {
                fulFillmentWH = uwOrder.getFacilityCode();
            }
        }
        if(productDeliveryType.equalsIgnoreCase("OTC")){
            log.info("[getFulfillmentWH] {}", uwOrder.getFacilityCode());
            if (hubMasterFacilityCodes.contains(uwOrder.getFacilityCode())) {
                fulFillmentWH = uwOrder.getFacilityCode()+INTRANSIT;
            } else {
                fulFillmentWH = uwOrder.getFacilityCode();
            }
        }
        log.info("[getFulfillmentWH] uwItemId : {}, fulfillmentWH : {}", uwOrder.getUwItemId(), fulFillmentWH);
        return fulFillmentWH;
    }

    private Optional<String> getPhysicalFacilityForB2BOrder(UwOrderDTO uwOrder,UwOrderDTO b2bCounterPart,List<String>hubMasterFacilityCodes) {
        return Stream.of(uwOrder.getFacilityCode(), b2bCounterPart.getFacilityCode()).filter(hubMasterFacilityCodes::contains).findFirst();
    }

    private void setLegalEntity(UwOrderDTO uwOrder, SalesOrderHeader salesOrderHeader, OrdersHeaderDTO ordersHeader, boolean isSbrt) {
        log.info("ReturnUtil : Setting LegalEntity for uwOrder : {}",uwOrder.getUwItemId());
        try {
            if (isSbrt) {
                salesOrderHeader.setLegalEntity(LK + ordersHeader.getLkCountry());
            }
            else {
                if ((B2B.equals(uwOrder.getProductDeliveryType()) && !FOFOB2B.equals(uwOrder.getNavChannel())) || OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        salesOrderHeader.setLegalEntity(LK + ordersHeader.getLkCountry());
                    } else {
                        salesOrderHeader.setLegalEntity(LEGAL_ENTITY_DEALSKART);
                    }
                } else {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        salesOrderHeader.setLegalEntity(LK + ordersHeader.getLkCountry());
                    } else {
                        salesOrderHeader.setLegalEntity(LEGAL_ENTITY_LENSKART);
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set LegalEntity for uwOrder : {}",uwOrder.getUwItemId());
        }
    }

    private void setModeOfPayment(OrdersDTO order, SalesOrderHeader salesOrderHeader, UwOrderDTO uwOrder) {
        log.info("ReturnUtil : Setting ModeOfPayment for uwItemId : {}",uwOrder.getUwItemId());
        try {
            if (order != null) {
                if (order.getMethod().equalsIgnoreCase("cashondelivery")) {
                    salesOrderHeader.setModeOfPayment(COD_MODE);
                    salesOrderHeader.setTermsOfPayment(COD_MODE);
                } else {
                    salesOrderHeader.setModeOfPayment(StringUtils.defaultString(order.getMethod()).trim());
                    salesOrderHeader.setTermsOfPayment("");
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set ModeOfPayment for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    private void setCourierCode(UwOrderDTO uwOrder, UwOrderDTO uwOrderB2B, com.lenskart.returnrepository.entity.ReturnOrder returnOrder, SalesOrderHeader salesOrderHeader) {
        log.info("ReturnUtil : Setting CourierCode for uwItemId : {}",uwOrder.getUwItemId());
        String courierCode = "";
        PosApiResponse posApiResponse = null;
        Integer uwItemId = uwOrder.getUwItemId();
        String errorMessage = "";
        try {
            if (uwOrder.getIsLocalFittingRequired()) {
                courierCode = "";
            } else {
                if (Objects.nonNull(returnOrder)) {
                    if (returnOrder.getReturnMethod() != null && returnOrder.getReturnMethod().equalsIgnoreCase(RPU)) {
                        courierCode = StringUtils.defaultString(returnOrder.getReverseCourier());
                    } else if (returnOrder.getReturnMethod() != null &&
                            (returnOrder.getReturnMethod().equalsIgnoreCase("StoreReceiving"))) {

                        if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                            log.info("ReturnUtil : Fetching details from pos api for B2B uwItemId : {}", uwItemId);
                            if (uwOrderB2B != null) {
                                log.info("ReturnUtil : Fetching details from pos api for uwItemId : {}", uwItemId);
                                posApiResponse = getDetailsFromPos(uwOrderB2B.getUwItemId());
                            }

                        } else {
                            log.info("ReturnUtil : Fetching details from pos api for uwItemId : {}", uwItemId);
                            posApiResponse = getDetailsFromPos(uwOrder.getUwItemId());
                        }
                        log.info("ReturnUtil : posApiResponse for uwItemId : {} : {}", uwItemId, posApiResponse);
                        if (posApiResponse != null && StringUtils.isEmpty(posApiResponse.getErrorMessage())) {
                            if (!StringUtils.isEmpty(posApiResponse.getCourier())) {
                                courierCode = posApiResponse.getCourier();
                            } else if (!StringUtils.isEmpty(posApiResponse.getPosGatepass())) {
                                courierCode = "Gatepass";
                            }
                        }
                    }
                }
            }
            log.info("ReturnUtil : courierCode for uwItemId : {} : {}", uwItemId, courierCode);
            salesOrderHeader.setCourierCode(courierCode);
        } catch (Exception e) {
            if (posApiResponse != null && !StringUtils.isEmpty(posApiResponse.getErrorMessage())) {
                errorMessage = posApiResponse.getErrorMessage();
            }
            log.error("ReturnUtil : Exception found to get CourierCode for uwItemId : {} : ",uwItemId, e);
        }
    }

    public PosApiResponse getDetailsFromPos(Integer uwItemId) {
        log.info("Fetching details from POS API for uwItemId: {}", uwItemId);
        try {
            Map<String, Object> response = centralPosFeignClient.getPosDetails(
                    uwItemId,
                    "Application",
                    lenskartApiKey,
                    lenskartAppId,
                    getSessionToken()
            );

            Map<String, Object> content = (Map<String, Object>) response.get("content");

            if (content == null) {
                log.error("No content found in POS response for uwItemId: {}", uwItemId);
                return null;
            }

            PosApiResponse posApiResponse = new PosApiResponse();
            posApiResponse.setCourier((String) content.getOrDefault("courier", ""));
            posApiResponse.setTrackingNum((String) content.getOrDefault("trackingNum", ""));
            posApiResponse.setPosGatepass((String) content.getOrDefault("posGatepass", ""));
            posApiResponse.setItemCode((String) content.getOrDefault("itemCode", ""));
            posApiResponse.setSellerFacility((String) content.getOrDefault("sellarFacility", ""));
            posApiResponse.setReturnFacility((String) content.getOrDefault("returnFacility", ""));

            return posApiResponse;
        } catch (Exception e) {
            log.error("Exception calling POS for uwItemId {}: {}", uwItemId, e.getMessage());
            PosApiResponse posApiResponse = new PosApiResponse();
            posApiResponse.setErrorMessage(e.getMessage());
            return posApiResponse;
        }
    }

    private String getSessionToken() {
        String sessionToken = "";
        try {
            PosSessionRequest request = new PosSessionRequest();
            request.setEncodingRequired(true);
            request.setUserName(posSessionUser);
            request.setPassword(posSessionPass);

            Map<String, Object> response = posFeignClient.getSessionToken(
                    "Application",
                    lenskartApiKey,
                    lenskartAppId,
                    request
            );

            sessionToken = (String) response.get("sessionToken");

        } catch (Exception e) {
            log.error("Exception while calling POS Session API: ", e);
        }

        return sessionToken;
    }

    // need discussion on invoice details ????
    private void setRefInvoices(UwOrderDTO uwOrder,UwOrderDTO b2bCounterPart, SalesOrderHeader salesOrderHeader) {
        log.info("ReturnUtil : Setting RefInvoice for uwItemId : {}",uwOrder.getUwItemId());
        try {
            InvoiceDetailsDto invoiceDetails = null;
            if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                invoiceDetails = inventoryDataHelper.getInvoiceDetails(b2bCounterPart.getShippingPackageId());
                log.info("Invoice details found for uwOrder : {}",b2bCounterPart.getUwItemId());
                if (invoiceDetails != null) {
                    salesOrderHeader.setRefInvoiceNo(invoiceDetails.getInvoiceCode());
                    salesOrderHeader.setOrgInvoiceNo(invoiceDetails.getInvoiceCode());
                }

            } else {
                invoiceDetails = inventoryDataHelper.getInvoiceDetails(uwOrder.getShippingPackageId());
                salesOrderHeader.setRefInvoiceNo(invoiceDetails.getInvoiceCode());
                salesOrderHeader.setOrgInvoiceNo(invoiceDetails.getInvoiceCode());
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set RefInvoice for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    private String getReturnStoreCode(OrdersHeaderDTO ordersHeader, UwOrderDTO uwOrder) {
        log.info("ReturnUtil : getReturnStoreCode for uwItemId : {}", uwOrder.getUwItemId());
        String storeCode = "";
        try {
            if (ordersHeader != null) {
                storeCode = getStoreCode(ordersHeader);
                if(Objects.nonNull(storeCode) && storeCode.isEmpty()) {
                    storeCode = onlineOrderStoreCode;
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : getReturnStoreCode for uwItemId : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : storeCode for uwItemId : {} {}",uwOrder.getUwItemId(), storeCode);
        return storeCode;
    }

    private void setCustomerDetails(SalesOrderHeader salesOrderHeader, OrdersHeaderDTO ordersHeader, OrdersDTO order, UwOrderDTO uwOrder) {
        log.info("ReturnUtil : Setting CustomerDetails for uwItemId : {}",uwOrder.getUwItemId());
        try {
            String customerId = String.valueOf(order.getCustomerId());
            log.info("setCustomerDetails : isBulkOrder : " + ordersHeader.getIsBulkOrder());
            String storeCode = Objects.equals(getStoreCode(ordersHeader), "") ? null : getStoreCode(ordersHeader);
            log.info("setCustomerDetails : storeCode : " + storeCode);
            if (ordersHeader.getIsBulkOrder() && !salesOrderHeader.getSubChannel().equalsIgnoreCase(COCOBulk)) {
                String saleSource = order.getSaleSource();
                log.info("setCustomerDetails : saleSource : " + order.getSaleSource());
                if (saleSource.contains(AQUALENS)) {
                    salesOrderHeader.setCustomerAccount(customerId);
                    salesOrderHeader.setInvoiceAccount(customerId);
                } else {
                    salesOrderHeader.setCustomerAccount(storeCode);
                    salesOrderHeader.setInvoiceAccount(storeCode);
                }
            } else if (salesOrderHeader.getSubChannel().equalsIgnoreCase(COCOBulk)
                    && ordersHeader.getLkCountry().equalsIgnoreCase(IN)
                    || ordersHeader.getLkCountry().equalsIgnoreCase(SG)
                    || ordersHeader.getLkCountry().equalsIgnoreCase(AE)
                    || ordersHeader.getLkCountry().equalsIgnoreCase(US)) {
                if (ordersHeader.getLkCountry().equalsIgnoreCase(IN)) {
                    salesOrderHeader.setCustomerAccount(DK + ordersHeader.getLkCountry());
                    salesOrderHeader.setInvoiceAccount(DK + ordersHeader.getLkCountry());
                } else {
                    salesOrderHeader.setCustomerAccount(LK + ordersHeader.getLkCountry());
                    salesOrderHeader.setInvoiceAccount(LK + ordersHeader.getLkCountry());
                }
            } else if(PRODUCT_DELIVERY_TYPE.DTC.equalsIgnoreCase(uwOrder.getProductDeliveryType()) 
                    || PRODUCT_DELIVERY_TYPE.OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                salesOrderHeader.setCustomerAccount(B2C_CUSTOMER_ID);
                salesOrderHeader.setInvoiceAccount(B2C_CUSTOMER_ID);
            } else {
                salesOrderHeader.setCustomerAccount(customerId);
                salesOrderHeader.setInvoiceAccount(customerId);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set CustomerDetails for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    private void setSBRTFlagForLineItem(UwOrderDTO uwOrder, List<Integer> sbrtUwItemIds, SoLine soLine) {
        log.info("isSBRTFlowEnabledForSalesOrder : {}",isSBRTFlowEnabledForSalesOrder);
        if(!isSBRTFlowEnabledForSalesOrder){
            soLine.setUnits(BLANK);
            return;
        }
        if(!CollectionUtils.isEmpty(sbrtUwItemIds) && sbrtUwItemIds.contains(uwOrder.getUwItemId())){
            soLine.setUnits(SBRTItemFlag);
        }else{
            soLine.setUnits(nonSBRTItemFlag);
        }
    }

    public String getUnitPrice(UwOrderDTO uwOrder) throws Exception {
        if(Objects.isNull(uwOrder) || StringUtils.isEmpty(uwOrder.getBarcode())) {
            return getDefaultCostPrice(uwOrder);
        }
        String unitPrice = null;
        BarcodePriceRequestDto barcodePriceRequestDto = BarcodePriceRequestDto.builder()
                .barcodeList(Arrays.asList(uwOrder.getBarcode()))
                .facilityCode(uwOrder.getFacilityCode())
                .build();
        try{
            ResponseEntity<String> responseEntity = nexsFeignClient.getBarcodePrice(barcodePriceRequestDto);
            log.info("[ReturnUtil][getUnitPrice] order: {} , response : {} ", uwOrder.getIncrementId(), responseEntity);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                List<Map<String, Object>> resultMap = objectMapper.readValue(responseEntity.getBody(), new TypeReference<List<Map<String, Object>>>() {});
                if(!CollectionUtils.isEmpty(resultMap)){
                    unitPrice = resultMap.get(0).get("price") != null ? resultMap.get(0).get("price").toString() : "0";
                    if ("0".equalsIgnoreCase(unitPrice) && isManesarFacility(uwOrder.getFacilityCode())) {
                        unitPrice =  getUnitPriceWithoutTax(uwOrder);
                    }
                    if("0".equalsIgnoreCase(unitPrice)){
                        return getDefaultCostPrice(uwOrder);
                    }
                } else if (isManesarFacility(uwOrder.getFacilityCode())) {
                    unitPrice = getUnitPriceWithoutTax(uwOrder);
                } else {
                    return getDefaultCostPrice(uwOrder);
                }
            }else{
                return getDefaultCostPrice(uwOrder);
            }
            log.info("[ReturnUtil][getUnitPrice] order: {} , unitPrice : {} ", uwOrder.getIncrementId(), unitPrice);
        }catch (Exception e){
            log.error("[ReturnUtil][getUnitPrice] order : {}, error : {}", uwOrder.getIncrementId(), e);
            return getDefaultCostPrice(uwOrder);
        }
        return unitPrice;
    }

    private String getDefaultCostPrice(UwOrderDTO uwOrder) throws Exception {
        if (defaultCostPriceFlag) {
            log.info("[ReturnUtil][getDefaultCostPrice] using default cost price : {}", defaultCostPrice);
            return defaultCostPrice;
        }

        log.error("[ReturnUtil][getUnitPrice] order : {}, error : {}", uwOrder.getIncrementId(), "/get/barcode/price api didn't give result");
        throw new Exception("/get/barcode/price api didn't give result");
    }

    private String getUnitPriceWithoutTax(UwOrderDTO uwOrder) {
        log.info("[ReturnUtil][getUnitPrice] checking unitPriceWithoutTax from UniReportStockEntry for manesar_facilities : {} ", MANESAR_FACILITY_CODE);
        UniReportStockEntryDto uniReportStockEntry = inventoryDataHelper.fetchUniReportStockEntity(uwOrder.getBarcode(),MANESAR_FACILITY_CODE);
        log.info("[ReturnUtil][getUnitPrice] uniReportStockEntry : {}",gson.toJson(uniReportStockEntry));
        return uniReportStockEntry != null && uniReportStockEntry.getUnitPriceWithoutTax() != null ? uniReportStockEntry.getUnitPriceWithoutTax() : "0";
    }

    private boolean isManesarFacility(String facilityCode) {
        log.info("[ReturnUtil][isManesarFacility] checking if facilityCode is among manesar_facilities code : {} ", MANESAR_FACILITY_CODE);
        return MANESAR_FACILITY_CODE.contains(facilityCode);
    }

    public String getLkReferenceWhForReturn(UwOrderDTO uwOrder, OrdersHeaderDTO ordersHeader, ReturnOrder returnOrder, UwOrderDTO b2bUwOrder) {
        String inventLocationId = "";
        Integer uwItemId = uwOrder.getUwItemId();
        PosApiResponse posApiResponse = null;
        String courier = "Courier";
        String direct = "Direct";

        if(isNexsUwOrder(uwOrder,b2bUwOrder)){
            courier = "BH-Courier";
            direct = "BH-Direct";
        }

        try {
            if (Objects.nonNull(returnOrder)) {
                if (!StringUtils.isEmpty(returnOrder.getReturnMethod())) {
                    if (returnOrder.getReturnMethod().equalsIgnoreCase(RPU) || returnOrder.getReturnMethod().equalsIgnoreCase("VSM:schedule_pickup")) {
                        inventLocationId = courier;
                    } else if (returnOrder.getReturnMethod().equals("StoreReceiving")
                            || returnOrder.getReturnMethod().equalsIgnoreCase("returntoNearbyStore")
                            || returnOrder.getReturnMethod().equalsIgnoreCase("vsm:store_return")) {
                        if (!StringUtils.isEmpty(returnOrder.getFacilityCode())) {
                            inventLocationId = returnOrder.getFacilityCode();
                        } else {
                            log.info("ReturnUtil : fetching returnFacility from POS for uwItemId : {}", uwItemId);
                            posApiResponse = getDetailsFromPos(uwItemId);
                            log.info("ReturnUtil : POS api response for uwItemId : {} : {}", uwItemId, posApiResponse);
                            if (posApiResponse != null && !StringUtils.isEmpty(posApiResponse.getReturnFacility())) {
                                inventLocationId = posApiResponse.getReturnFacility();
                            }
                        }
                    } else if (returnOrder.getReturnMethod().equals("DirectReceiving")
                            || returnOrder.getReturnMethod().equals("ShiptoLenskrt")
                            || uwOrder.getNavChannel().contains("FOFO")) {
                        if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                            inventLocationId = direct;
                        } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                            inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                        }
                    }
                } else if (StringUtils.isEmpty(returnOrder.getReturnMethod())) {
                    if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()) && uwOrder.getNavChannel().equalsIgnoreCase("WebB2B")) {
                        if (0 == uwOrder.getParentUw()) {
                            inventLocationId = getFacilityCodeForB2BOrders(uwOrder, b2bUwOrder);
                        }

                    } else {
                        if (!StringUtils.isEmpty(returnOrder.getSource()) && (returnOrder.getSource().equals("WAREHOUSE"))
                                || uwOrder.getNavChannel().contains("FOFO")) {
                            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                                inventLocationId = direct;
                            } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                                inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                            }
                        } else if (returnOrder.getReturnType().equalsIgnoreCase("rto")) {
                            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                                inventLocationId = direct;
                            } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                                inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                            }
                        } else if (returnOrder.getReturnType().equalsIgnoreCase("awaited_rto")
                                || (returnOrder.getSource().equalsIgnoreCase("vsm"))) {
                            inventLocationId = courier;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set InventLocationId for uwOrder : {} : ", uwItemId, e);
        }
        log.info("ReturnUtil : InventLocationId for uwOrder : {} : {}", uwItemId, inventLocationId);
        return inventLocationId;
    }

    private boolean isNexsUwOrder(UwOrderDTO uwOrder,UwOrderDTO b2bUwOrder) {
        try {
            if (Objects.nonNull(uwOrder) && StringUtils.isNotBlank(uwOrder.getFacilityCode()) && !CollectionUtils.isEmpty(nexsFacilities) && nexsFacilities.contains(uwOrder.getFacilityCode())) {
                return true;
            }
            if (StringUtils.isNotBlank(uwOrder.getProductDeliveryType()) && uwOrder.getProductDeliveryType().equalsIgnoreCase(B2B)) {
                if(Objects.nonNull(b2bUwOrder) && StringUtils.isNotBlank(b2bUwOrder.getFacilityCode())) {
                    return nexsFacilities.contains(b2bUwOrder.getFacilityCode());
                }
            }
        }
        catch (Exception e){
            log.error("[isNexsUwOrder] exception "+e);
        }
        return false;
    }

    public String getFacilityCodeForB2BOrders(UwOrderDTO uwOrder,UwOrderDTO b2bUwOrder) {
        log.info("ReturnUtil : Finding facilityCode for B2B order for uwOrder : {}", uwOrder.getUwItemId());
        UwOrderDTO custUwOrder = null;
        String facilityCode = "";
        try {
            if (uwOrder.getParentUw() == 0) {
                custUwOrder = b2bUwOrder;
            } else {
                custUwOrder = uwOrder;
            }

            if (custUwOrder != null) {
                facilityCode = custUwOrder.getFacilityCode();
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get B2B return details for uwOrder : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : facilityCode for B2B order for uwOrder : {} : {}", uwOrder.getUwItemId(), facilityCode);
        return facilityCode;
    }

    private void setSoLineBasicDetails(UwOrderDTO uwOrder, OrdersDTO order, SoLine soLine) {
        log.info("ReturnUtil : setSoLineBasicDetails for uwItemId : {}",uwOrder.getUwItemId());
        try {
            soLine.setLineNumber(Long.valueOf(uwOrder.getUwItemId()));
            soLine.setItemNumber(StringUtils.defaultString(String.valueOf(uwOrder.getProductId())));
            soLine.setQtyOrdered(1);
            soLine.setOriginalSaleOrderNo(order.getIncrementId());
            soLine.setOriginalSaleOrderLineNo(uwOrder.getUwItemId());
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setSoLineBasicDetails for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    private void setSoLinePrices(UwOrderDTO uwOrder, SoLine soLine,UwOrderDTO b2bUwOrder) throws Exception {
        log.info("ReturnUtil : setSoLinePrices for uwItemId : {}",uwOrder.getUwItemId());
        Double salesPrice = getSalePrice(uwOrder.getUwItemId());
        soLine.setSalesPrice(decimalFormat.format(salesPrice));

        if (OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            log.info("ReturnUtil : setSoLinePrices fetching return cost price for OTC order uwItemId : {}",uwOrder.getUwItemId());
            UwOrderDTO bulkUwOrder = getBulkUwOrderByBarcode(uwOrder);
            if (Objects.nonNull(bulkUwOrder)) {
                Double bulkSalesPrice = getSalePrice(bulkUwOrder.getUwItemId());
                soLine.setReturnCostPrice(bulkSalesPrice);
            }
            else {
                soLine.setReturnCostPrice(Double.valueOf(getUnitPrice(uwOrder)));
            }
        }
        else if(b2bUwOrder != null && B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()))  {
            log.info("ReturnUtil : setSoLinePrices fetching return cost price for B2B order uwItemId : {}",uwOrder.getUwItemId());
            soLine.setReturnCostPrice(Double.valueOf(getUnitPrice(b2bUwOrder)));
        }
        else {
            //fetch return cost price from nexs api for rest of the order types
            soLine.setReturnCostPrice(Double.valueOf(getUnitPrice(uwOrder)));
        }
    }

    private Double getSalePrice(Integer uwItemId) throws Exception {
        log.info("ReturnUtil : fetching salesPrice for uwItemId : {} ", uwItemId);
        Double salesPrice = 0.0;
        OrderItemGSTDetailDto orderItemGSTDetail = inventoryDataHelper.getOrderItemGSTDetails(InventoryRequestDto.builder().uwItemId(uwItemId).build());

        if (Objects.nonNull(orderItemGSTDetail)) {
            salesPrice = orderItemGSTDetail.getCostPerItem();
        }
        log.info("ReturnUtil : salesPrice for uwItemId : {} : {}", uwItemId, salesPrice);
        return salesPrice;
    }

    public UwOrderDTO getBulkUwOrderByBarcode(UwOrderDTO uwOrder) {
        String barcode = uwOrder.getBarcode();
        log.info("[getBulkUwOrderByBarcode] getting bulk UwOrder by barcode");
        if (StringUtils.isBlank(barcode)) {
            log.error("[getBulkUwOrderByBarcode] barcode is empty with barcode");
            return null;
        }

        List<UwOrderDTO> uwOrderList = inventoryDataHelper.getFilteredUwOrdersByBarcode(barcode,uwOrder.getIncrementId());

        if (uwOrderList.isEmpty()) {
            log.error("[getBulkUwOrderByBarcode] uwOrderList is empty with barcode {}", barcode);
            return null;
        }

        List<UwOrderDTO> bulkUwOrderList = uwOrderList.stream().filter(u -> u.getNavChannel() != null && u.getNavChannel().toLowerCase().contains("bulk")).collect(Collectors.toList());

        if (bulkUwOrderList.isEmpty()) {
            log.error("[getBulkUwOrderByBarcode] bulkUwOrderList is empty with barcode {} and nav channel bulk", barcode);
            return null;
        }

        Optional<UwOrderDTO> uwOrderOp = bulkUwOrderList.stream().max((o1, o2) -> o1.getCreatedAt().after(o2.getCreatedAt()) ? 1 : 0);

        if (!uwOrderOp.isPresent()) {
            log.error("uwOrderOp is empty with barcode {} and nav channel bulk", barcode);
            return null;
        }
        UwOrderDTO uwOrderOpsResult = uwOrderOp.get();
        Gson gson = new Gson();
        log.info("Fetched bulk order: {}", gson.toJson(uwOrderOpsResult));
        return uwOrderOpsResult;
    }

    public void setSoLineDiscount(UwOrderDTO uwOrder, SoLine soLine) {
        log.info("ReturnUtil : setSoLineDiscount for uwItemId : {}",uwOrder.getUwItemId());
        try {
            ItemWisePriceDetailsDTO itemWisePriceDetails = null;
            itemWisePriceDetails = inventoryDataHelper.getItemDetails(InventoryRequestDto.builder()
                            .itemId(uwOrder.getItemId())
                    .build());
            log.info("ReturnUtil : itemWisePriceDetails : {}",itemWisePriceDetails);
            if (itemWisePriceDetails != null) {
                soLine.setLineDiscountAmount(String.valueOf(itemWisePriceDetails.getLenskartDiscount()));
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setSoLineDiscount for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    public void setSoLineConfirmedReceiptDate(com.lenskart.returnrepository.entity.ReturnOrder returnOrder, UwOrderDTO uwOrder, SoLine soLine) {
        log.info("ReturnUtil : fetching ConfirmedReceiptDate for uwItemId : {}", uwOrder.getUwItemId());
        try {
            ReturnHistory returnHistory = null;
            if (Objects.nonNull(returnOrder)) {
                returnHistory = returnHistoryRepository.findTopByEntityIdAndEntityTypeAndCurrentStatusInOrderByCreatedAtAsc(returnOrder.getReturnId(),RETURN_ORDER,VALID_STATUS_FOR_SETTING_CONFIRMED_RECEIPT_DATE);
                if (returnHistory != null) {
                    log.info("Setting ConfirmedReceiptDate and ConfirmedShipDate from returnHistory.");
                    soLine.setConfirmedReceiptDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreatedAt())));
                    soLine.setConfirmedShipDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreatedAt())));
                } else {
                    List<ReturnHistoryDTO> returnHistoryDTOList = getReturnHistory(returnOrder.getReturnId());
                    returnHistory = getLatestReturnHistory(returnHistoryDTOList, returnOrder.getReturnId(),VALID_STATUS_FOR_SETTING_CONFIRMED_RECEIPT_DATE);
                    if (returnHistory != null) {
                        log.info("Setting ConfirmedReceiptDate and ConfirmedShipDate from returnHistory.");
                        soLine.setConfirmedReceiptDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreatedAt())));
                        soLine.setConfirmedShipDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreatedAt())));
                    }else{
                        log.info("Setting ConfirmedReceiptDate and ConfirmedShipDate from returnOrder's returnCreateDateTime.");
                        soLine.setConfirmedReceiptDate(StringUtils.defaultString(dateFormat.format(returnOrder.getReturnCreateDatetime())));
                        soLine.setConfirmedShipDate(StringUtils.defaultString(dateFormat.format(returnOrder.getReturnCreateDatetime())));
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : exception found to fetch ConfirmedReceiptDate for uwItemId : {}", uwOrder.getUwItemId());
        }
    }

    private ReturnHistory getLatestReturnHistory(List<ReturnHistoryDTO> returnHistoryDTOList, Integer returnId, List<String> validStatuses) {
        ReturnHistory returnHistory = null;
        if(!CollectionUtils.isEmpty(returnHistoryDTOList)){
            returnHistoryDTOList = returnHistoryDTOList.stream().filter(returnHistoryDTO -> returnHistoryDTO.getCurrentStatus() != null && validStatuses.contains(returnHistoryDTO.getCurrentStatus().toLowerCase())).collect(Collectors.toList());
            log.info("[getLatestReturnHistory] returnHistoryDTOList : {}", returnHistoryDTOList);
            if(!CollectionUtils.isEmpty(returnHistoryDTOList)){
                ReturnHistoryDTO returnHistoryDTO = returnHistoryDTOList.get(returnHistoryDTOList.size()-1);
                returnHistory = getReturnHistory(returnHistoryDTO, returnId);
            }
        }
        return returnHistory;
    }

    public List<ReturnHistoryDTO> getReturnHistory(Integer returnId){
        log.info("[getReturnHistory] returnId : {}, getting from headless returns", returnId);
        return returnEventService.getReturnHistory(returnId);
    }

    private ReturnHistory getReturnHistory(ReturnHistoryDTO returnHistoryDTO, Integer returnId) {
        log.info("[getReturnHistory] returnId : {}, returnHistoryDTO1 : {}", returnId, returnHistoryDTO);
        ReturnHistory returnHistory = null;
        if(Objects.nonNull(returnHistoryDTO)){
            returnHistory = new ReturnHistory();
            returnHistory.setAddedBy(returnHistoryDTO.getAddedBy());
            returnHistory.setComment(returnHistoryDTO.getComment());
            returnHistory.setCourier(returnHistoryDTO.getCourier());
            returnHistory.setCreatedAt(returnHistoryDTO.getCreatedAt());
            returnHistory.setCurrentStatus(returnHistoryDTO.getCurrentStatus());
            returnHistory.setEntityType(returnHistoryDTO.getEntityType());
            returnHistory.setReverseAwb(returnHistoryDTO.getReverseAwb());
            returnHistory.setReversePickupReferenceId(returnHistoryDTO.getReversePickupReferenceId());
            returnHistory.setEntityId(returnId);
        }
        log.info("[getReturnHistory] returnId : {}, returnHistoryDTO : {}", returnId, returnHistoryDTO);
        return returnHistory;
    }

    public void setSoLineHsnAndSacCode(ProductDto product, SoLine soLine, int classificationId, UwOrderDTO uwOrder) {
        log.info("ReturnUtil : setSoLineHsnAndSacCode for uwItemId : {}",uwOrder.getUwItemId());
        String hsncode = "";
        OrderItemGSTDetailDto orderItemGSTDetail = null;
        try {
            orderItemGSTDetail = inventoryDataHelper.getOrderItemGSTDetails(InventoryRequestDto.builder().uwItemId(uwOrder.getUwItemId()).build());
            if (Objects.nonNull(orderItemGSTDetail)) {
                hsncode = orderItemGSTDetail.getHsn();
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setSoLineHsnAndSacCode for uwItemId : {}",uwOrder.getUwItemId());
        }
        if(StringUtils.isBlank(hsncode) && blankHsnCodeClassifications.contains(String.valueOf(classificationId))){
            hsncode = ProductUtil.getHSNCode(product.getHsnCode());
        }
        log.info("ReturnUtil : hsnCode for uwItemId : {} : {}",uwOrder.getUwItemId(),hsncode);
        soLine.setHsnCode(hsncode);
        soLine.setSacCode("");
    }

    public void setSoLineTax(UwOrderDTO uwOrder, SoLine soLine,UwOrderDTO b2bReferenceUwOrder) {
        log.info("ReturnUtil : setSoLineTax for uwItemId : {}",uwOrder.getUwItemId());
        OrderItemGSTDetailDto orderItemGSTDetail = null;
        Double taxRate = 0.00D;
        UwOrderDTO uwOrder2 = null;

        try {
            if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                  uwOrder2 = b2bReferenceUwOrder;
            } else {
                uwOrder2 = uwOrder;
            }
            if (Objects.nonNull(uwOrder2)) {
                orderItemGSTDetail = inventoryDataHelper.getOrderItemGSTDetails(InventoryRequestDto.builder().uwItemId(uwOrder2.getUwItemId()).build());
            }
            if (Objects.nonNull(orderItemGSTDetail)) {
                taxRate = orderItemGSTDetail.getCgstPc()
                        + orderItemGSTDetail.getIgstPc()
                        + orderItemGSTDetail.getSgstPc()
                        + orderItemGSTDetail.getUgstPc();
                log.info("ReturnUtil : TaxRate for uwItemId : {} : {}",uwOrder.getUwItemId(), taxRate);
                soLine.setSalesOrderItemCode(String.valueOf(uwOrder.getUwItemId()));
                soLine.setTaxRateType(String.format("%.2f", taxRate));
                soLine.setItemSalesTaxGrp(String.format("%.2f", taxRate));
                soLine.setSalesTaxGrp(String.format("%.2f", taxRate));
            } else {
                log.error("ReturnUtil : setSoLineTax orderItemGSTDetailList found empty : {}",uwOrder.getUwItemId());
            }

        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get taxRate from order-ops-client for uwItemId : {}", uwOrder.getUwItemId());
        }
    }

    public void setSoLinePurchPrice(UwOrderDTO uwOrder, SoLine soLine,UwOrderDTO b2bUwOrder) {
        log.info("ReturnUtil : setSoLinePurchPrice for uwItemId : {}", uwOrder.getUwItemId());
        Double purchPrice = 0.0;
        OrderItemGSTDetailDto orderItemGSTDetail = null;
        UwOrderDTO uwOrder2 = null;
        try {
            if (B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                uwOrder2 = b2bUwOrder;
                if (Objects.nonNull(uwOrder2)) {
                    orderItemGSTDetail = inventoryDataHelper.getOrderItemGSTDetails(InventoryRequestDto.builder().uwItemId(uwOrder2.getUwItemId()).build());
                    if (Objects.nonNull(orderItemGSTDetail)) {
                        purchPrice = orderItemGSTDetail.getCostPerItem();
                    } else {
                        log.error("ReturnUtil : purchPrice not found for uwItemId : {}",uwOrder.getUwItemId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get purchPrice for uwItemId : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : purchPrice for uwItemId : {} : {}", uwOrder.getUwItemId(),purchPrice);
        soLine.setPurchPrice(String.valueOf(purchPrice));
    }

    public boolean getSBRTFlag(UwOrderDTO uwOrder,OrdersHeaderDTO ordersHeader) {
        log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}", uwOrder.getUwItemId());
        if (NavChannel.COCOBulk.name().equalsIgnoreCase(uwOrder.getNavChannel())
            || NavChannel.COCOB2B.name().equalsIgnoreCase(uwOrder.getNavChannel())) {
            log.info("[ReturnUtil][getSBRTFlag] Found COCOBulk/COCOB2B Order, setting as SBRT : {}", uwOrder.getUwItemId());
            return true;
        }
        else if (uwOrder.getNavChannel().toLowerCase().startsWith("fofo")) {
            log.info("[ReturnUtil][getSBRTFlag] Found FOFO Order, setting as non SBRT : {}", uwOrder.getUwItemId());
            return false;
        }
        try {
            if (isSBRTOrder(uwOrder)) return true;
            log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, Not a SBRT enabled order", uwOrder.getUwItemId());

            if (uwOrder.getNavChannel().startsWith(WEB_PREFIX)) {
                if(
                        Boolean.parseBoolean(systemPreferenceService.getSystemPreferenceValues(
                                SYSTEM_PREFERENCE_KEYS.IS_SBRT_WEB_ENABLED,
                                SYSTEM_PREFERENCE_GROUPS.SBRT)
                        )
                ) return true;
            }
            else {

                if(getStoreSBRTStatus(uwOrder,ordersHeader)) return true;
            }

            log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, Not a web or store sbrt enabled order", uwOrder.getUwItemId());
        }
        catch (Exception e) {
            log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, error : {}", uwOrder.getUwItemId(), e);
        }

        log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, isSbrt : {}", uwOrder.getUwItemId(), false);
        return false;
    }

    public boolean isSBRTOrder(UwOrderDTO uwOrder) {
        log.info("[ReturnUtil][isSBRTOrder] checking uwItemId in sbrt table : {}", uwOrder.getUwItemId());
        SbrtOrderItem sbrtOrderItem = inventoryDataHelper.fetchSBRTOrderItem(uwOrder.getUwItemId());
        return sbrtOrderItem != null;
    }

    public boolean getStoreSBRTStatus(UwOrderDTO uwOrder,OrdersHeaderDTO ordersHeader) {
        log.info("[ReturnUtil][getStoreSBRTStatus] uwItemId : {}", uwOrder.getUwItemId());
        String facilityCode = uwOrder.getFacilityCode();
        if (uwOrder.getNavChannel().toLowerCase().contains("bulk") || uwOrder.getProductDeliveryType().equalsIgnoreCase(DTC)) {
            if (ordersHeader != null) {
                facilityCode = ordersHeader.getFacilityCode();
            }
        }
        PosFranchiseSbrtResponse posFranchiseSbrtResponse = getStoreSbrtStatus(facilityCode);
        log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, isSBRT: {}", uwOrder.getUwItemId(), posFranchiseSbrtResponse != null && posFranchiseSbrtResponse.isSbrtStore());
        return posFranchiseSbrtResponse != null && posFranchiseSbrtResponse.isSbrtStore();
    }

    public PosFranchiseSbrtResponse getStoreSbrtStatus(String facilityCode) {
        log.info("PosSbrtService : Fetching SBRT status for facilityCode {}", facilityCode);
        try {
            String sessionToken = getSessionToken();
            PosFranchiseSbrtResponse response = posFeignClient.getStoreSbrtStatus(
                    "Application",
                    lenskartApiKey,
                    lenskartAppId,
                    sessionToken,
                    facilityCode
            );
            log.info("PosSbrtService : SBRT response received : {}", response);
            return response;
        } catch (Exception e) {
            log.error("PosSbrtService : Error while calling POS SBRT API", e);
            return null;
        }
    }

    public void setSbrtUwOrderDetails(UwOrderDTO uwOrder, boolean isSbrt) {
        uwOrder.setProductDeliveryType(
                getSbrtProductDeliveryType(uwOrder.getProductDeliveryType(),uwOrder.getNavChannel(), isSbrt)
        );
        uwOrder.setNavChannel(
                getSbrtNavChannel(uwOrder.getNavChannel(), isSbrt)
        );
    }

    private String getSbrtProductDeliveryType(String deliveryType, String navChannel, boolean sbrtFlag) {
        SbrtOrderMapper mapper = SbrtOrderMapper.getMapping(navChannel);
        if (mapper != null && sbrtFlag) {
            return mapper.getSbrtDeliveryType();
        }

        return deliveryType;
    }

    private String getSbrtNavChannel(String navChannel, boolean sbrtFlag) {
        SbrtOrderMapper mapper = SbrtOrderMapper.getMapping(navChannel);
        if (mapper != null && sbrtFlag) {
            return mapper.getSbrtNavChannel();
        }

        return navChannel;
    }

    public List<UwOrderDTO> getB2BUwOrders(List<Integer> list) {
        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        for (Integer uwItemId : list) {
            uwOrderDTOList.addAll(inventoryDataHelper.getFilteredUwOrdersByGivenUwItemId(uwItemId));
        }
        return uwOrderDTOList;
    }

    public Long createFinanceEvent(ReturnCreateRequest returnRequestDto, String eventType) throws Exception {
        FLEventsTrackerDto flEventsTrackerDto = FLEventsTrackerDto.builder()
        .eventType(eventType)
        .eventIdentifierType(UW_ITEM_ID)
        .eventIdentifierValue(String.valueOf(returnRequestDto.getUwItemId()))
        .createdBy(CES)
        .build();
        return financeService.createEvent(flEventsTrackerDto);
    }

    public void updateFinanceEvent(ReturnCreateRequest returnRequestDto, String eventType, EventStatus eventStatus, Long eventId) throws Exception {
        FLEventsTrackerDto flEventsTrackerDto = FLEventsTrackerDto.builder()
        .id(eventId)
        .eventType(eventType)
        .eventIdentifierType(UW_ITEM_ID)
        .eventIdentifierValue(String.valueOf(returnRequestDto.getUwItemId()))
        .status(eventStatus)
        .createdBy(CES)
        .build();
        financeService.updateEventStatus(flEventsTrackerDto);
    }

}
