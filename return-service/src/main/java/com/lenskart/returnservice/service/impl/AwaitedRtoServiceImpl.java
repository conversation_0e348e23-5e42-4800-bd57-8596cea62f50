package com.lenskart.returnservice.service.impl;


import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.ordermetadata.dto.ReturnOrderItemDTO;
import com.lenskart.ordermetadata.dto.request.OldRefundRequestCreateDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.refund.client.model.dto.RefundAmount;
import com.lenskart.refund.client.model.enums.*;
import com.lenskart.refund.client.model.request.CreateRefundRequest;
import com.lenskart.refund.client.model.request.RefundMethodRequest;
import com.lenskart.refund.client.model.response.RefundMethodResponse;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returncommon.model.enums.LKCountry;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.ReturnGroupRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

import static com.lenskart.returncommon.model.enums.ReturnStatus.RETURN_UNDER_FOLLOWUP;
import static com.lenskart.returncommon.model.enums.Status.*;
import static com.lenskart.returncommon.utils.Constant.EVENT.AWAITED_RTO_REQUEST_CREATED;
import static com.lenskart.returncommon.utils.Constant.EVENT.REFUND_REQUEST_CREATED;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.STATUS_UPDATE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.AWAITED_RTO;

@Service
@Slf4j
public class AwaitedRtoServiceImpl implements IAwaitedRtoService {

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private IReturnOrderItemService returnOrderItemService;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private ReturnUtil returnUtil;

    @Value("${refund.service.baseurl}")
    private String baseUrlRefundService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired
    private AmountUtil amountUtil;

    @Autowired
    private ID365FinanceService financeService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ReturnGroupRepository returnGroupRepository;

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Override
    public Map<String, Object> markAwaitedRto(ReturnOrderRequestDTO returnOrderRequest) {
        boolean isValidRequest = validateRequest(returnOrderRequest);
        Map<String, Object> result = new HashMap<>();
        if (!isValidRequest) {
            result.put("success", isValidRequest);
            return result;
        }
        ReturnRequest returnRequest = createReturnRequest(returnOrderRequest);
        PurchaseOrderDetailsDTO orderInfoResponseDTO = new PurchaseOrderDetailsDTO();
        orderInfoResponseDTO.setUwOrders(returnOrderRequest.getUwOrderDTOs());
        orderInfoResponseDTO.setOrders(returnOrderRequest.getOrderDTOs());
        orderInfoResponseDTO.setOrdersHeaderDTO(returnOrderRequest.getOrdersHeaderDTO());
        if (CollectionUtils.isEmpty(returnOrderRequest.getUwOrderDTOs())) {
            ResponseEntity<PurchaseOrderDetailsDTO> ordersDetails = orderOpsFeignClient.getPurchaseOrderDetails(IdentifierType.ORDER_ID.name(), String.valueOf(returnOrderRequest.getIncrementId()));
            if (ordersDetails.getStatusCode().is2xxSuccessful()) {
                orderInfoResponseDTO = ordersDetails.getBody();
            } else {
                log.error("[AwaitedRtoServiceImpl] couldn't fetch order details for -  {} : {} , status code : {}", returnRequest.getIdentifierType(),
                        returnRequest.getIdentifierValue(), ordersDetails.getStatusCode());
                result.put("success", false);
                result.put("failure_reason", "couldn't fetch order details");
            }
        }


        if (orderInfoResponseDTO == null || CollectionUtils.isEmpty(orderInfoResponseDTO.getUwOrders())) {
            log.error("[AwaitedRtoServiceImpl] record not found!!! for -  {} : {}", returnRequest.getIdentifierType(),
                    returnRequest.getIdentifierValue());
            result.put("success", false);
            result.put("failure_reason", "record not found!!!");
            return result;
        }
        List<Integer> items = orderInfoResponseDTO.getUwOrders().stream()
                .filter(uwOrderDTO -> uwOrderDTO.getUnicomOrderCode().equalsIgnoreCase(returnOrderRequest.getReferenceOrderCode()))
                .map(UwOrderDTO::getUwItemId)
                .toList();
        log.info("[AwaitedRtoServiceImpl] items : {}", items);
        List<ReturnItemRequestDTO> returnItemRequests = new ArrayList<>();
        for (Integer item : items) {
            ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
            returnItemRequest.setItemId(item);
            returnItemRequest.setQcStatus("pass");
            returnItemRequest.setReasonDetail("anyReason");
            returnItemRequests.add(returnItemRequest);
        }
        log.info("[AwaitedRtoServiceImpl]return item requests : {}", returnItemRequests);
        returnOrderRequest.setItems(returnItemRequests);

        for (ReturnItemRequestDTO returnItemRequest : returnOrderRequest.getItems()) {
            AwaitedRtoRefundRequestDTO refundRequest = createAwaitedRtoReturn(returnRequest, returnItemRequest, returnOrderRequest, orderInfoResponseDTO);
            Integer returnId = null;
            if (refundRequest != null) {
                returnId = refundRequest.getReturnId();
                createAwaitedRtoRefund(returnRequest, result, returnId, orderInfoResponseDTO, returnItemRequest, refundRequest, items);
                syncReturn(refundRequest.getUwOrder());
                statusUpdate(returnOrderRequest, refundRequest.getUwOrder());
            }
        }
        result.put("success", true);

        return result;
    }

    private void statusUpdate(ReturnOrderRequestDTO returnOrderRequest, UwOrderDTO uwOrder) {
        OrderStatusUpdateDetails request = new OrderStatusUpdateDetails();
        request.setState("complete");
        String status;
        if (uwOrder != null && CLOSED.getValue().equalsIgnoreCase(uwOrder.getShipmentStatus())){
            request.setState("closed");
            status = CLOSED_AWAITED_RTO.getValue();
        }
        else {
            request.setState("complete");
            status = COMPLETE_LOST_BY_COURIER.getValue().equalsIgnoreCase(returnOrderRequest.getReasonDetail()) ? COMPLETE_LOST_BY_COURIER.getValue() : AWAITED_RTO;
        }
        request.setStatus(status);
        request.setIncrementId(returnOrderRequest.getIncrementId());
        request.setUnicomOrderCode(returnOrderRequest.getReferenceOrderCode());
        try{
            kafkaService.pushToKafka(STATUS_UPDATE,String.valueOf(returnOrderRequest.getIncrementId()),objectMapper.writeValueAsString(request));
        }catch (Exception exception){
            log.error("[statusUpdate] exception while pushing to kafka : "+exception);
        }
    }

    private void syncReturn(UwOrderDTO uwOrder) {
        financeService.syncReturn(uwOrder);
    }

    public String getRefundMethodAtRto(PurchaseOrderDetailsDTO orderInfoResponseDTO, UwOrderDTO uwOrderDTO) {
        Integer incrementId = orderInfoResponseDTO.getOrdersHeaderDTO().getIncrementId();
        OrdersDTO ordersDTO = getOrder(orderInfoResponseDTO);
        String paymentMethod = returnUtil.getPaymentMethod(ordersDTO);
        RefundMethodRequest refundMethodRequest = new RefundMethodRequest();
        refundMethodRequest.setPaymentMethod(paymentMethod);
        refundMethodRequest.setPaymentMode(orderInfoResponseDTO.getOrdersHeaderDTO().getPaymentMode());
        refundMethodRequest.setCountry(orderInfoResponseDTO.getOrdersHeaderDTO().getLkCountry());
        refundMethodRequest.setRefundInitiatedAt(RefundInitiatedAt.AWAITED_RTO.getValue());
        refundMethodRequest.setNavChannel(uwOrderDTO.getNavChannel());
        if(orderInfoResponseDTO.getOrdersHeaderDTO().getIsExchangeOrder()!=null && orderInfoResponseDTO.getOrdersHeaderDTO().getIsExchangeOrder()){
            refundMethodRequest.setRefundInitiatedAt(RefundInitiatedAt.EXCHANGE_AWAITED_RTO.getValue());
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "Application");

        try{
            HttpEntity<RefundMethodRequest> httpEntity = new HttpEntity<>(refundMethodRequest, headers);
            String url=baseUrlRefundService+"/v1.0/get-refund-method";
            log.info("[AwaitedRtoServiceImpl][getRefundMethod] increment_id:{}, url:{}, payload:{}",incrementId,url,refundMethodRequest);
            ResponseEntity<RefundMethodResponse> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, RefundMethodResponse.class);
            if(responseEntity.getStatusCode().is2xxSuccessful()){
                if(Objects.nonNull(responseEntity.getBody()) && responseEntity.getBody().isSuccess()){
                    log.info("[AwaitedRtoServiceImpl][getRefundMethod] increment_id:{}, refund_method:{}",incrementId,responseEntity.getBody());
                    return responseEntity.getBody().getAutoRefundMethod();
                } else {
                    log.info("[AwaitedRtoServiceImpl][getRefundMethod] Failed to fetch refund method, increment_id:{}",incrementId);
                    return null;
                }
            }
        }catch (Exception exception){
            log.error("[AwaitedRtoServiceImpl][getRefundMethod] increment_id:{}, Exception:{}",incrementId,exception.getMessage());
        }
        return null;
    }

    private OrdersDTO getOrder(PurchaseOrderDetailsDTO orderInfoResponseDTO) {

        List<String> physicalFacilities = returnUtil.getPhysicalFacilityCodes();
        log.info("[getOrder] physicalFacilities : {}", physicalFacilities);

        List<UwOrderDTO> uwOrders = orderInfoResponseDTO.getUwOrders()
                .stream()
                .filter(uwOrderDTO -> "B2B".equalsIgnoreCase(uwOrderDTO.getProductDeliveryType()) != physicalFacilities.contains(uwOrderDTO.getFacilityCode()))
                .toList();
        log.info("[getOrder] uwOrders : {}", uwOrders);

        List<Integer> customerFacingItemIds = uwOrders.stream().map(UwOrderDTO::getItemId).toList();

        List<OrdersDTO> ordersDTOs = orderInfoResponseDTO.getOrders()
                .stream()
                .filter(ordersDTO -> customerFacingItemIds.contains(ordersDTO.getItemId()))
                .toList();

        log.info("[getOrder] ordersDTOs : {}", ordersDTOs);
        return ordersDTOs.isEmpty() ? null : ordersDTOs.get(0);
    }

    private void createAwaitedRtoRefund(ReturnRequest returnRequest, Map<String, Object> result, int returnId, PurchaseOrderDetailsDTO orderInfoResponseDTO, ReturnItemRequestDTO returnItemRequest, AwaitedRtoRefundRequestDTO refundRequest, List<Integer> uwItemIdList) {
        UwOrderDTO uwOrder = refundRequest.getUwOrder();
        int customerReturnId = refundRequest.getCustomerReturnId();
        int customerUwItemId = refundRequest.getCustomerUwItemId();

        log.info("[AwaitedRtoServiceImpl:createAwaitedRtoRefund] fetching item refundable amount by fastRefundHistoryService for increment id {}", uwOrder.getIncrementId());

        try{
            String refundMethodAtRto = getRefundMethodAtRto(orderInfoResponseDTO, uwOrder);
            boolean isNewRefundFlow = refundUtilsService.refundServiceSwitch(IdentifierType.RETURN_ID.name(), String.valueOf(returnId));
            log.info("[AwaitedRtoServiceImpl][createAwaitedRtoRefund] refundMethod : {}, isNewRefundFlow : {}", refundMethodAtRto, isNewRefundFlow);
            if(isNewRefundFlow){
                ItemWiseAmountDTO itemWiseAmount = amountUtil.getItemAmount(false, uwOrder.getIncrementId(), uwItemIdList);
                Double price = 0.0d;
                String currency = "INR";
                OrdersDTO ordersDTO = orderInfoResponseDTO.getOrders().get(0);
                OrdersHeaderDTO ordersHeaderDTO = orderInfoResponseDTO.getOrdersHeaderDTO();
                if(itemWiseAmount != null){
                    price = itemWiseAmount.getTotalItemAmount();
                    Byte storeId = ordersDTO.getStoreId();
                    LKCountry lkCountry = LKCountry.byValue(ordersHeaderDTO.getLkCountry());
                    currency = storeId == 8 || LKCountry.SG==lkCountry ? "SGD" : "INR";
                    CreateRefundRequest refundRequestPayload = createRefundRequestPayload(customerReturnId, ordersDTO.getIncrementId(), refundMethodAtRto, price, currency, returnRequest.getSource(), returnItemRequest.getReasonDetail());
                    if(Objects.nonNull(refundRequestPayload)) {
                        refundUtilsService.initiateRefundRequest(refundRequestPayload);
                    }
                }
            }else{
                OldRefundRequestCreateDTO oldRefundRequestCreateDTO = getOldRefundRequestCreateDTO(returnId, returnItemRequest, uwOrder, customerUwItemId, refundMethodAtRto);
                createRefundRequest(oldRefundRequestCreateDTO);
            }
            List<ReturnEvent> returnEvents = returnEventService.getReturnEvent(returnId);
            Integer requestId = returnEvents.stream().map(ReturnEvent::getReturnRequestId).filter(Objects::nonNull).findAny().orElse(null);
            returnEventService.createReturnEvent(requestId, returnId, REFUND_REQUEST_CREATED, "");
        }catch (Exception exception){
            log.error("[AwaitedRtoServiceImpl][createAwaitedRtoRefund] error occurred : "+exception);
        }

    }

    private void createRefundRequest(OldRefundRequestCreateDTO oldRefundRequestCreateDTO) {
        kafkaService.pushToKafka(Constant.RETURN_TOPICS.CREATE_REFUND_REQUEST_QUEUE, String.valueOf(oldRefundRequestCreateDTO.getIncrementId()), oldRefundRequestCreateDTO);
    }

    private static OldRefundRequestCreateDTO getOldRefundRequestCreateDTO(int returnId, ReturnItemRequestDTO returnItemRequest, UwOrderDTO uwOrder, int customerUwItemId, String refundMethodAtRto) {
        OldRefundRequestCreateDTO oldRefundRequestCreateDTO = new OldRefundRequestCreateDTO();
        oldRefundRequestCreateDTO.setAwaitedRtoItem(true);
        oldRefundRequestCreateDTO.setDoRefund(true);
        oldRefundRequestCreateDTO.setIsDualCo(true);
        oldRefundRequestCreateDTO.setNewFlowFlag(1);
        oldRefundRequestCreateDTO.setIncrementId(uwOrder.getIncrementId());
        oldRefundRequestCreateDTO.setSource("vsm");
        oldRefundRequestCreateDTO.setRefundMethod(refundMethodAtRto);
        oldRefundRequestCreateDTO.setOrderStatus("awaited_rto");
        oldRefundRequestCreateDTO.setQcStatus(returnItemRequest.getQcStatus());
        oldRefundRequestCreateDTO.setReasonDetail(returnItemRequest.getReasonDetail());
        oldRefundRequestCreateDTO.setCustomerUwItemId(customerUwItemId);
        oldRefundRequestCreateDTO.setReturnId(returnId);
        oldRefundRequestCreateDTO.setUwItemId(returnItemRequest.getItemId());
        return oldRefundRequestCreateDTO;
    }

    private CreateRefundRequest createRefundRequestPayload(Integer returnId, Integer incrementId, String refundMethod, Double price, String currency, String source, String remarks){
        try{

            CreateRefundRequest createRefundRequest = new CreateRefundRequest();
            createRefundRequest.setIdentifierType(com.lenskart.refund.client.model.enums.IdentifierType.RETURN_ID);
            createRefundRequest.setIdentifierValue(String.valueOf(returnId));
            createRefundRequest.setRefundTriggerPoint(RefundTriggerPoint.RETURN_INITIATION.getName());
            createRefundRequest.setOrderId(Long.valueOf(incrementId));
            createRefundRequest.setRefundTarget(RefundTarget.getByValue(refundMethod));
            createRefundRequest.setRefundAmount(new RefundAmount(BigDecimal.valueOf(price), currency));
            createRefundRequest.setType(RefundType.CREDIT);
            createRefundRequest.setRefundReason(RefundReason.AWAITED_RTO);
            createRefundRequest.setRemarks(remarks);
            createRefundRequest.setClient("Narvar");
            return createRefundRequest;
        } catch (Exception e){
            log.error("[RefundServiceImpl][createRefundRequestPayload] Exception:{}",e.getMessage());
        }
        return null;
    }


    private AwaitedRtoRefundRequestDTO createAwaitedRtoReturn(ReturnRequest returnRequest, ReturnItemRequestDTO returnItemRequest, ReturnOrderRequestDTO returnOrderRequest, PurchaseOrderDetailsDTO orderInfoResponseDTO) {
        UwOrderDTO uwOrderDTO = orderInfoResponseDTO.getUwOrders()
                .stream()
                .filter(dto -> Objects.equals(dto.getUwItemId(), returnItemRequest.getItemId()))
                .findAny().orElse(null);
        AwaitedRtoRefundRequestDTO awaitedRtoRefundRequestDTO = null;
        if(uwOrderDTO != null){
            if(uwOrderDTO.getParentUw() != 0){
                return null;
            }
//            ReturnDetailItem returnOrderItemExisting = returnOrderItemService.findByUwItemId(uwOrderDTO.getUwItemId());
//            if(returnOrderItemExisting != null && returnOrderItemExisting.getId() != null){
//                return null;
//            }
            returnOrderRequest.setFacility(uwOrderDTO.getFacilityCode());
            CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse = null;
            Long groupId = returnGroupRepository.save(new ReturnGroup()).getId();
            log.info("[createAwaitedRtoReturn] order : {} , groupId : {}", uwOrderDTO.getIncrementId(), groupId);
            returnOrderRequest.setGroupId(groupId);
            createUpdateReturnOrderResponse = returnOrderActionService.createAwaitedRtoReturnOrder(returnRequest,  returnOrderRequest,
                    Constant.STATUS.RETURN_UNDER_FOLLOWUP, AWAITED_RTO, uwOrderDTO);
            if(createUpdateReturnOrderResponse == null){
                return null;
            }
            Integer returnId = createUpdateReturnOrderResponse.getReturnId();
            ReturnDetailItem returnOrderItem = createReturnItemForAwaitedRto(uwOrderDTO, returnId, returnRequest.getReturnReason());
            returnEventService.createReturnEvent(returnRequest.getId(), returnId, RETURN_UNDER_FOLLOWUP.getStatus(),"awaited_rto of uw_item_id : " + (returnOrderItem !=null ? returnOrderItem.getUwItemId() : null));
            kafkaService.pushToKafka("d365_return_tracking_event", String.valueOf(uwOrderDTO.getIncrementId()), new D365ReturnTrackingRequestDTO(returnId, RETURN_UNDER_FOLLOWUP.getStatus()));
            awaitedRtoRefundRequestDTO = createB2BReturnItems(orderInfoResponseDTO, uwOrderDTO, returnRequest, returnItemRequest, returnOrderRequest, returnId, returnOrderItem);
        }
        return awaitedRtoRefundRequestDTO;
    }

    private AwaitedRtoRefundRequestDTO createB2BReturnItems(PurchaseOrderDetailsDTO orderInfoResponseDTO, UwOrderDTO uwOrderDTO, ReturnRequest returnRequest, ReturnItemRequestDTO returnItemRequest, ReturnOrderRequestDTO returnOrderRequest, Integer returnId, ReturnDetailItem returnOrderItem) {
        Integer customerReturnId = returnId;
        Integer customerUwItemId = uwOrderDTO.getUwItemId();
        if (Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(uwOrderDTO.getProductDeliveryType())) {
            log.info("[markOrderAwaitedRto] create b2b return entries");
            UwOrderDTO uwOrderB2b = orderInfoResponseDTO.getUwOrders()
                    .stream()
                    .filter(uwOrderDTO1 -> Objects.equals(uwOrderDTO1.getB2bRefrenceItemId(), uwOrderDTO.getUwItemId()))
                    .findAny()
                    .orElse(null);

            log.info("[createB2BReturnItems] returnOrderRequest : {}", returnOrderRequest);
            ReturnOrderRequestDTO returnOrderRequestB2bB = objectMapper.convertValue(returnOrderRequest, ReturnOrderRequestDTO.class);
            log.info("[createB2BReturnItems] returnOrderRequestB2bB : {}", returnOrderRequestB2bB);
            if(uwOrderB2b != null){
                log.info("[createB2BReturnItems] setGroupId : {}", returnOrderRequestB2bB.getGroupId());
                returnOrderRequestB2bB.setReferenceOrderCode(uwOrderB2b.getUnicomOrderCode());
                CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse = null;
                createUpdateReturnOrderResponse = returnOrderActionService.createAwaitedRtoReturnOrder(returnRequest,  returnOrderRequestB2bB,
                        Constant.STATUS.RETURN_UNDER_FOLLOWUP, AWAITED_RTO, uwOrderB2b);
                if(createUpdateReturnOrderResponse == null){
                    return null;
                }
                returnId = createUpdateReturnOrderResponse.getReturnId();
                ReturnDetailItem returnOrderItemB2B = createReturnItemForAwaitedRto(uwOrderB2b, returnId, returnItemRequest.getReasonDetail());
                returnEventService.createReturnEvent(returnRequest.getId(), returnId, RETURN_UNDER_FOLLOWUP.getStatus(), "awaited_rto of uw_item_id : "+returnOrderItemB2B.getUwItemId());
                String facilityCode = systemPreferenceService.findOneByGroupAndKey("revised_edd", "facility").getValue();
                List<String> facilityCodeList = Arrays.asList(facilityCode.split("\\s*,\\s*"));
                if(!facilityCodeList.contains(uwOrderB2b.getFacilityCode())){
                    customerReturnId = returnOrderItemB2B.getReturnId();
                    customerUwItemId = uwOrderB2b.getUwItemId();
                }

            }
        }
        ReturnOrderItemDTO returnOrderItemDTO = null;
        try{
            returnOrderItemDTO = objectMapper.convertValue(returnOrderItem, ReturnOrderItemDTO.class);
        }catch (IllegalArgumentException exception){
            log.error("[createB2BReturnItems] error while converting"+exception);
        }

        return AwaitedRtoRefundRequestDTO.builder()
                .customerReturnId(customerReturnId)
                .customerUwItemId(customerUwItemId)
                .returnOrderItem(returnOrderItemDTO)
                .returnId(returnId)
                .uwOrder(uwOrderDTO)
                .build();
    }

    private ReturnDetailItem createReturnItemForAwaitedRto(UwOrderDTO uwOrderDTO, Integer returnId, String returnReason) {
        return returnOrderItemService.createReturnItemForAwaitedRto("Pass",uwOrderDTO, returnId, returnReason);
    }

    private ReturnRequest createReturnRequest(ReturnOrderRequestDTO returnOrderRequest) {
        String identifierType = returnOrderRequest.getShippingPackageId() != null ? IdentifierType.SHIPPING_PACKAGE_ID.name() : IdentifierType.UNICOM_ORDER_CODE.name();
        String identifierValue = IdentifierType.SHIPPING_PACKAGE_ID.name().equalsIgnoreCase(identifierType) ? returnOrderRequest.getShippingPackageId() : returnOrderRequest.getReferenceOrderCode();
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setReturnReason("AWAITED_RTO");
        returnRequest.setIdentifierType(identifierType);
        returnRequest.setIdentifierValue(identifierValue);
        returnRequest.setIncrementId(returnOrderRequest.getIncrementId());
        returnRequest.setSource(StringUtils.isEmpty(returnOrderRequest.getSource()) ? "Narvar Callback" : returnOrderRequest.getSource());
        returnRequest.setReturnIntention("source");
        returnRequest.setCreatedAt(new Date());
        try {
            ReturnOrderRequestDTO returnOrderRequestDTO = objectMapper.convertValue(returnOrderRequest, ReturnOrderRequestDTO.class);
            returnOrderRequestDTO.setUwOrderDTOs(null);
            returnOrderRequestDTO.setOrderDTOs(null);
            returnOrderRequestDTO.setOrdersHeaderDTO(null);
            returnRequest.setReturnCreationRequest(objectMapper.writeValueAsString(returnOrderRequestDTO));
        } catch (JsonProcessingException e) {
            log.error("[createReturnRequest] json parse  exception");
        }
        returnRequest = returnRequestRepository.save(returnRequest);
        returnEventService.createReturnEvent(returnRequest.getId(), null, AWAITED_RTO_REQUEST_CREATED, "");
        return returnRequest;
    }

    private boolean validateRequest(ReturnOrderRequestDTO returnOrderRequest) {
        boolean isValidRequest = true;
        if(returnOrderRequest == null){
            isValidRequest = false;
        }
        else if(returnOrderRequest.getShippingPackageId() == null && returnOrderRequest.getReferenceOrderCode() == null){
            log.error("[validateRequest] both shipping package id and unicom order code are null");
            isValidRequest = false;
        }
        if(returnOrderRequest != null){
            ReturnDetail returnOrder = returnOrderActionService.findTop1ByOrderNoAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(returnOrderRequest.getIncrementId(), returnOrderRequest.getReferenceOrderCode(), "awaited_rto");
            if(null != returnOrder){
                isValidRequest = false;
            }
        }
        return isValidRequest;
    }
}
