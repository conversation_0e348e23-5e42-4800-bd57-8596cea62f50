package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.DelightActionDTO;
import com.lenskart.returncommon.model.response.DelightActionResponse;
import com.lenskart.returncommon.model.response.ReturnResponse;
import com.lenskart.returnrepository.entity.DelightAction;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.repository.DelightActionRepository;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IDelightActionService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class DelightActionServiceImpl implements IDelightActionService {

    @Autowired
    private DelightActionRepository delightActionRepository;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;


    @Override
    public DelightAction getDelightAction(Integer returnId) {
        log.info("[getDelightAction] returnId : {}", returnId);
        DelightAction delightAction  = delightActionRepository.findTop1ByReturnIdOrderByIdDesc(returnId);
        log.info("[getDelightAction] returnId : {} , delightAction : {}", returnId, delightAction);
        return delightAction;
    }

    @Override
    public ReturnResponse getReturnDelightDetails(Integer returnId) {
        log.info("[getReturnDelightDetails] returnId : {}", returnId);
        ReturnResponse returnResponse = new ReturnResponse();
        Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnId);
        if (returnDetailOptional.isEmpty()) {
            log.info("[getReturnDelightDetails] returnId : {} , fetching delight details from order-ops", returnId);
            ResponseEntity<DelightActionDTO> delightActionDetails = orderOpsFeignClient.getDelightActionDetails(returnId);
            if (delightActionDetails.getStatusCode().is2xxSuccessful()) {
                DelightActionDTO delightActionDTO = delightActionDetails.getBody();
                if (delightActionDTO != null) {
                    returnResponse.setReturnId(returnId);
                    returnResponse.setReturnCreationDate(delightActionDTO.getReturnCreateDateTime());
                    returnResponse.setStatus(delightActionDTO.getReturnStatus());
                    DelightActionResponse delightActionResponse = new DelightActionResponse();
                    delightActionResponse.setDelightMethod(delightActionDTO.getDelightMethod());
                    delightActionResponse.setApprovalStatus(delightActionDTO.getDelightAction());
                    returnResponse.setDelightActionResponse(delightActionResponse);
                }
            }
        } else {
            log.info("[getReturnDelightDetails] returnId : {}, fetching from headless returns", returnId);
            ReturnDetail returnDetail = returnDetailOptional.get();
            DelightAction delightAction = getDelightAction(returnId);
            returnResponse.setReturnCreationDate(returnDetail.getReturnCreateDatetime());
            returnResponse.setStatus(returnOrderActionService.getReturnOrderStatusById(returnId));
            returnResponse.setReturnId(returnId);
            if (delightAction != null) {
                DelightActionResponse delightActionResponse = new DelightActionResponse();
                delightActionResponse.setDelightMethod(delightAction.getDelightMethod());
                delightActionResponse.setApprovalStatus(delightAction.getDelightAction());
                returnResponse.setDelightActionResponse(delightActionResponse);
            }
        }
        log.info("[getReturnDelightDetails] returnId : {}, delightActionResponse : {}", returnId, returnResponse);
        return returnResponse;
    }
}
