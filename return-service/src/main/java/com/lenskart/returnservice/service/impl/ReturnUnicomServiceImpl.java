package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.return_refund_rules.model.ReturnItemRequest;
import com.lenskart.returncommon.exception.ReturnRequestFailException;
import com.lenskart.returnservice.service.IReturnUnicomService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ReturnUnicomServiceImpl implements IReturnUnicomService {
    public static final String ACTION_CODE_WAC = "WAC";
    public static final String QC_PASS = "QC Pass";
    public static final String QC_NO = "QC No";
    public static final String MARK_RETURN_UNICOM ="/returns/createReversePickUp";
    @Value("${inventoryAdapterClient.baseurl}")
    private String baseUrl;
    private RestTemplate restTemplate;
    private ObjectMapper objectMapper;

    @PostConstruct
    public void init(){
        restTemplate = new RestTemplate();
    }
    @Override
    public ResponseEntity<String> createReversePickup(List<ReturnItemRequestDTO> returnItemRequest, Object shippingAddress, String saleOrderCode, String facility, Boolean rtoItem) throws ReturnRequestFailException {
        ResponseEntity<String> responseEntity = null;
        if (null != returnItemRequest && null != saleOrderCode && null != facility) {
            Map<String, Object> payload = new HashMap<String, Object>();
            preparePayload(returnItemRequest, shippingAddress, saleOrderCode, facility, rtoItem, payload);
            try {
                log.info("[createReversePickup] payload : " + payload);
                String url = baseUrl + MARK_RETURN_UNICOM;
                HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(payload);
                responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            } catch (Exception e) {
                log.error("Unable to create reverse pickup in unicommerce for saleorder :" + "", e);
                throw new ReturnRequestFailException(e);
            }
        } else {
            throw new ReturnRequestFailException("UNICOM_RETURN_EXCEPTION", "Missing Parameter");
        }
        return responseEntity;
    }

    private void preparePayload(List<ReturnItemRequestDTO> returnItemRequest, Object shippingAddress, String saleOrderCode, String facility, Boolean rtoItem,Map<String, Object> request) {
        List<Object> customFieldValues = getCustomFieldValues(returnItemRequest, rtoItem);
        log.info("[createReversePickup] Started");
        Map<String, Object> payload = new HashMap<>();
        payload.put("actionCode", ACTION_CODE_WAC);
        payload.put("customFieldValues", customFieldValues);
        payload.put("saleOrderCode", saleOrderCode);
        payload.put("shippingAddress", shippingAddress);
        List<Map<String, Object>> reversePickItems =returnItemRequest.stream().map(item->{
            Map<String, Object> reversePickItem = new HashMap<>();
            String reason = ReturnItemRequest.QC_STATUS.PASS.equalsIgnoreCase(item.getQcStatus()) ? QC_PASS : QC_NO;
            reversePickItem.put("saleOrderItemCode", item.getItemId());
            reversePickItem.put("reason", reason);
            return reversePickItem;
        }).toList();
        payload.put("reversePickItems", reversePickItems);

        request.put("payload", payload);
        request.put("facility", facility);
    }

    private static List<Object> getCustomFieldValues(List<ReturnItemRequestDTO> returnItemRequest, Boolean rtoItem) {
        List<Object> customFieldValues = new ArrayList<Object>();
        Map<String, Object> customField = new HashMap<String, Object>();
        customField.put("name", "customerReason");
        customField.put("value", returnItemRequest.get(0).getReasonDetail());
        customFieldValues.add(customField);
        if(Boolean.TRUE.equals(rtoItem)) {
            Map<String, Object> customField2 = new HashMap<String, Object>();
            customField2.put("name", "shelf");
            customField2.put("value", "DKS RTO");
            customFieldValues.add(customField2);
        }
        return customFieldValues;
    }
}
