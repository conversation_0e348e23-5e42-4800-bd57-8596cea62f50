package com.lenskart.returnservice.service;

import com.lenskart.returnrepository.entity.CommunicationTemplate;

import java.util.List;
import java.util.Map;

public interface ICommunicationTemplateService {
    CommunicationTemplate findByEventTypeAndConditions(String eventType, Map<String, String> conditionsMap);

    List<CommunicationTemplate> findAll();

    void reloadCommunicationTemplateCache();
}
