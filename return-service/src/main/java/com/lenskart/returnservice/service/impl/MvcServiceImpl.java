package com.lenskart.returnservice.service.impl;


import com.lenskart.returncommon.model.dto.MVCResponseDTO;
import com.lenskart.returncommon.model.dto.MvcOrderDTO;
import com.lenskart.returncommon.model.request.CustomerStoreProfileRequest;
import com.lenskart.returncommon.model.response.CustomerStoreProfileResponse;
import com.lenskart.returncommon.model.response.ReturnAutoApprovalRules;
import com.lenskart.returnservice.feignclient.RMSFeignClient;
import com.lenskart.returnservice.service.IMvcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class MvcServiceImpl implements IMvcService {

    @Autowired
    private RMSFeignClient rmsFeignClient;

    @Value("${rms.base.url:https://rms.scm.preprod.lenskart.com/}")
    private String rmsBaseUrl;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public CustomerStoreProfileResponse getMvcMvsStats(Long customerId, String storeId) {

        try {
            CustomerStoreProfileRequest customerStoreProfileRequest = new CustomerStoreProfileRequest();
            customerStoreProfileRequest.setCustomerId(customerId);
            customerStoreProfileRequest.setStoreId(storeId);
            log.info("[getMvcMvsStats]  customerId: {}, storeId:{},",customerStoreProfileRequest.getCustomerId(),customerStoreProfileRequest.getStoreId());
            ResponseEntity<CustomerStoreProfileResponse> response = rmsFeignClient.fetchMvcAndMvsScore(customerStoreProfileRequest);

            if(response.getStatusCode().is2xxSuccessful()){
                log.info("[getMvcMvsStats] response : {}", response);
                return response.getBody();
            }

        } catch (Exception e) {
            log.error("[getMvcMvsStats] Exception occurred while getting rms response: "+ e);
        }

        return null;
    }

    @Override
    public MvcOrderDTO getMvcOrdersResponse(String identifierType, String identifierValue) {
        log.info("[getMvcOrdersResponse] {} : {}", identifierType, identifierValue);
        try{
            ResponseEntity<MvcOrderDTO> response = rmsFeignClient.getMvcOrders(identifierType,identifierValue);

            if(response.getStatusCode().is2xxSuccessful()){
                log.info("[getMvcOrdersResponse] response : {}", response);
                return response.getBody();
            }

        }catch (Exception exception){
            log.error("[getMvcOrdersResponse] error : {}", exception.getMessage());
        }
        return null;
    }

    @Override
    public ReturnAutoApprovalRules getApprovalStatusAndRefundMethod(Long customerId, Integer incrementId, Integer uwItemId, String modelName, String eyeWearType,String secondaryReturnReason,Integer returnId) {
        ReturnAutoApprovalRules returnAutoApprovalRules = new ReturnAutoApprovalRules();

        try {
            String apiUrl = rmsBaseUrl + "return/autoApprovalEligibility";

            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("customerId", customerId);
            requestMap.put("modelName", modelName);
            requestMap.put("uw_item_id", uwItemId);
            requestMap.put("incrementId", incrementId);
            requestMap.put("eyeWearType", eyeWearType);
            requestMap.put("secondaryReturnReason", secondaryReturnReason);
            requestMap.put("returnId", returnId);

            log.info("[MvcServiceImpl][getApprovalStatusAndRefundMethod] request : {}", requestMap);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestMap, headers);

            ResponseEntity<ReturnAutoApprovalRules> response = restTemplate.exchange(
                    apiUrl,
                    HttpMethod.POST,
                    entity,
                    ReturnAutoApprovalRules.class
            );
            returnAutoApprovalRules = response.getBody();
            log.info("[MvcServiceImpl][getApprovalStatusAndRefundMethod] response : {}", returnAutoApprovalRules);
        } catch (Exception e) {
            log.error("Exception occurred while getting ApprovalStatusAndRefundMethod response: "+ e);
        }
        return returnAutoApprovalRules;
    }
}
