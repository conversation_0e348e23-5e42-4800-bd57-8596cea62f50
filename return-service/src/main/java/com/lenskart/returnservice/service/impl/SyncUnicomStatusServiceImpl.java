package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.returncommon.model.dto.SyncUnicomStatusDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.ISyncUnicomStatusService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
public class SyncUnicomStatusServiceImpl implements ISyncUnicomStatusService {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private IKafkaService kafkaService;

    private ObjectMapper objectMapper;

    @PostConstruct
    public void init(){
        objectMapper = new ObjectMapper();
    }

    @Override
    public boolean syncUnicomStatus(SyncUnicomStatusDTO syncUnicomStatusDTO) {
        boolean flag = false;
        if(syncUnicomStatusDTO == null){
            return flag;
        }
        List<UwOrderDTO> uwOrderDTOs = syncUnicomStatusDTO.getUwOrderDTOs();
        OrdersHeaderDTO ordersHeaderDTO = syncUnicomStatusDTO.getOrdersHeaderDTO();
        ExchangeItemDTO exchangedItem = syncUnicomStatusDTO.getExchangeItemDTO();
        if (!CollectionUtils.isEmpty(uwOrderDTOs)) {
            ResponseEntity<Boolean> syncUnicomStatusResponseEntity = null;
            try{
                syncUnicomStatusResponseEntity = orderOpsFeignClient.syncUnicomStatus(uwOrderDTOs);
            }catch (Exception exception){
                log.error("[syncUnicomStatus] error occurred , order : {} , exception : {} ", ordersHeaderDTO.getIncrementId(), exception);
            }
            if(syncUnicomStatusResponseEntity != null && syncUnicomStatusResponseEntity.getStatusCode().is2xxSuccessful()){
                flag = syncUnicomStatusResponseEntity.getBody() != null && syncUnicomStatusResponseEntity.getBody();
            }
        }
        log.info("[syncUnicomStatus] flag : {} ", flag);
        return flag;
    }
}
