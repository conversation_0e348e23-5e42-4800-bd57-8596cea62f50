package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.RefundDetails;
import com.lenskart.returncommon.model.dto.ReturnStatusHeadingDetail;
import com.lenskart.returncommon.model.request.UpdateRefundHeadingRequest;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;

import java.util.Date;
import java.util.List;

public interface IReturnSubHeaderTimelineDateFetchService {
    void getDateInMilliseconds(ReturnDetailsResponse returnDetailsResponse);

    ReturnStatusHeadingDetail updateRefundHeading(UpdateRefundHeadingRequest refundHeadingRequest);

    void getDateInMilliseconds(ReturnDetailsResponse returnDetailsResponse, ReturnStatusHeadingDetail returnStatusHeadingDetail, Date createdAt, String anyReceivingPoint, Integer incrementId, List<RefundDetails> refundList);
}
