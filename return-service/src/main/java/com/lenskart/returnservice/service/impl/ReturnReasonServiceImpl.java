package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.ReturnReasonDTO;
import com.lenskart.returncommon.model.dto.SecondaryReasonDTO;
import com.lenskart.returncommon.model.response.ReturnReasonErrorResponse;
import com.lenskart.returncommon.model.response.ReturnReasonsResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.service.ICacheDataService;
import com.lenskart.returnservice.service.IReturnReasonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lenskart.returncommon.utils.Constant.BLANK;
import static com.lenskart.returncommon.utils.Constant.REASONS.NOT_AVAILABLE;

@Service
@Slf4j
public class ReturnReasonServiceImpl implements IReturnReasonService {

    @Autowired
    private ReturnReasonRepository returnReasonRepository;
    @Autowired
    private PrimaryReturnReasonsRepository primaryReturnReasonsRepository;
    @Autowired
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;
    @Autowired
    private ExchangeApprovalReasonRepository exchangeApprovalReasonRepository;

    @Autowired
    IExchangeReturnReasonRepository exchangeReturnReasonRepository;
    private static final String APPROVAL = "APPROVAL";
    private static final String PREFIX_REASON="REASON:";

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    ICacheDataService cacheDataService;

    @Autowired
    private ReturnReasonOldRepository returnReasonOldRepository;

    @Override
    public List<ReturnDetailReason> saveReturnReasonsForItem(String source, Integer incrementId, Integer returnId, ReturnItemDTO item, int uwItemId) throws Exception {
        log.info("[saveReturnReasonsForItem] Save return reasons for item : {}",item);
        List<ReturnDetailReason> returnReasonList = new ArrayList<ReturnDetailReason>();
        try{
            Date date = new Date();
            for(com.lenskart.ordermetadata.dto.request.Reasons reason : item.getReasons()){
                ReturnDetailReason returnReason = new ReturnDetailReason();
                if("APPROVAL".equalsIgnoreCase(reason.getType())) {
                    ApprovalReasons approvalReasons = exchangeApprovalReasonRepository.findById(reason.getPrimaryReasonId()).orElseThrow(() -> new Exception("NO RECORDS FOUND"));
                    returnReason.setPrimaryReason(approvalReasons.getReason());
                    returnReason.setSecondaryReason(NOT_AVAILABLE);
                }else{
                    PrimaryReturnReasons primaryReturnReasons = primaryReturnReasonsRepository.findByPrimaryReasonId(reason.getPrimaryReasonId());
                    SecondaryReturnReason secondryReturnReasons = secondaryReturnReasonRepository.findBySecondaryReasonId(reason.getSecondaryReasonId());
                    if(primaryReturnReasons != null){
                        returnReason.setPrimaryReason(primaryReturnReasons.getReason());
                    }else{
                        returnReason.setPrimaryReason("N/A");
                    }
                    if(secondryReturnReasons != null){
                        returnReason.setSecondaryReason(secondryReturnReasons.getReason());
                    }else{
                        returnReason.setSecondaryReason("N/A");
                    }
                }

                returnReason.setCreatedAt(date);
                returnReason.setOrderId(incrementId);
                returnReason.setSource("reversepickuprequest");
                returnReason.setUwItemId(uwItemId);
                returnReason.setUser(BLANK);
                returnReason.setReturnId(returnId);
                returnReason.setType(reason.getType());
                returnReason = returnReasonRepository.save(returnReason);
                returnReasonList.add(returnReason);
            }
        }catch (Exception exception){
            log.error("[saveReturnReasonsForItem] exception : {}", exception.getMessage());
        }
        return returnReasonList;
    }

    @Override
    public ReturnReasonsResponse getReturnReasons(String platform, String categoryId) {
        ReturnReasonsResponse returnReasonsResponse = new ReturnReasonsResponse();
        String cacheKey = PREFIX_REASON + platform + categoryId;
        if (validateRequest(platform, categoryId, returnReasonsResponse)) {
            return returnReasonsResponse;
        }

        if (redisTemplate.hasKey(cacheKey)) {
            log.info("Fetching return reasons from cache against key " + cacheKey);
            returnReasonsResponse = (ReturnReasonsResponse) redisTemplate.opsForValue().get(cacheKey);
        } else {
            if (getAndUpdateReasonsListInCache(platform, categoryId, returnReasonsResponse, cacheKey))
                return returnReasonsResponse;
        }
        return returnReasonsResponse;
    }

    @Override
    public ReturnDetailReason saveReturnReason(Integer uwItemId, Integer returnId) {
        ReturnDetailReason returnReason = returnReasonRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
        if(returnReason != null){
            returnReason.setReturnId(returnId);
            returnReasonRepository.save(returnReason);
        }
        return returnReason;
    }

    @Override
    public ReturnReasonsResponse getApprovalReasons() {
        List<ApprovalReasons> approvalReasons = null;
        List<ReturnReasonDTO> returnReasonDTOS = null;
        ReturnReasonsResponse returnReasonsResponse = new ReturnReasonsResponse();
        ValueOperations<String, Object> valueOps = redisTemplate.opsForValue();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(APPROVAL))) {
            log.info("fetching approval reasons from cache");
            approvalReasons = (List<ApprovalReasons>) valueOps.get(APPROVAL);
        } else {
            approvalReasons = exchangeApprovalReasonRepository.findAll();
            valueOps.set(APPROVAL, approvalReasons, 1, TimeUnit.HOURS);
        }
        returnReasonDTOS = approvalReasons != null ? approvalReasons.stream().map(ar -> {
            ReturnReasonDTO returnReasonDTO = new ReturnReasonDTO();
            returnReasonDTO.setId(ar.getId());
            returnReasonDTO.setReason(ar.getReason());
            return returnReasonDTO;
        }).toList() : null;
        returnReasonsResponse.setReturn_reasons(returnReasonDTOS);
        return returnReasonsResponse;
    }

    @Override
    public ReturnReasonDTO getReturnReason(Integer returnId) {
        log.info("[getReturnReason] returnId : {}", returnId);
        ReturnReasonDTO returnReasonDTO = new ReturnReasonDTO();
        List<ReturnDetailReason> returnReasons = returnReasonRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnReasons)){
            ReturnDetailReason returnReason = returnReasons.get(0);
            returnReasonDTO.setReturnId(returnId);
            returnReasonDTO.setSecondaryReason(returnReason.getSecondaryReason());
            returnReasonDTO.setPrimaryReason(returnReason.getPrimaryReason());
            returnReasonDTO.setOrderId(returnReason.getOrderId());
            returnReasonDTO.setUwItemId(returnReason.getUwItemId());
        }else{
            log.info("[getReturnReasonOld] fetching returnId : {}", returnId);
            List<ReturnReason> returnReasonsOld = returnReasonOldRepository.findByReturnId(returnId);
            log.info("[getReturnReasonOld] done fetching returnId : {}", returnId);
            if(!CollectionUtils.isEmpty(returnReasonsOld)){
                ReturnReason returnReason = returnReasonsOld.get(0);
                returnReasonDTO.setReturnId(returnId);
                returnReasonDTO.setSecondaryReason(returnReason.getSecondaryReason());
                returnReasonDTO.setPrimaryReason(returnReason.getPrimaryReason());
                returnReasonDTO.setOrderId(returnReason.getOrderId());
                returnReasonDTO.setUwItemId(returnReason.getUwItemId());
            }
        }
        return returnReasonDTO;
    }

    @Override
    public Map<String, String> getReturnReasons(Integer returnId) {
        return returnReasonRepository.getReasons(returnId);
    }

    @Override
    public List<ReturnReasonDTO> getReturnReasonsList(Integer orderId) {
        log.info("[getReturnReasonsList] fetching orderId : {}", orderId);
        List<ReturnReasonDTO> returnReasonDTOS = new ArrayList<>();
        List<ReturnDetailReason> returnDetailReasons = returnReasonRepository.findByOrderId(orderId);
        if(!CollectionUtils.isEmpty(returnDetailReasons)){
            for(ReturnDetailReason returnDetailReason : returnDetailReasons){
                ReturnReasonDTO returnReasonDTO = new ReturnReasonDTO();
                returnReasonDTO.setReturnId(returnDetailReason.getReturnId());
                returnReasonDTO.setSecondaryReason(returnDetailReason.getSecondaryReason());
                returnReasonDTO.setPrimaryReason(returnDetailReason.getPrimaryReason());
                returnReasonDTO.setOrderId(returnDetailReason.getOrderId());
                returnReasonDTO.setUwItemId(returnDetailReason.getUwItemId());
                returnReasonDTOS.add(returnReasonDTO);
            }
        }else{
            log.info("[getReturnReasonOld] fetching returnId : {}", orderId);
            List<ReturnReason> returnReasonsList = returnReasonOldRepository.findByOrderId(orderId);
            log.info("[getReturnReasonOld] done fetching orderId : {}", returnReasonsList);
            if(!CollectionUtils.isEmpty(returnReasonsList)){
                for(ReturnReason returnReason : returnReasonsList){
                    ReturnReasonDTO returnReasonDTO = new ReturnReasonDTO();
                    returnReasonDTO.setReturnId(returnReason.getReturnId());
                    returnReasonDTO.setSecondaryReason(returnReason.getSecondaryReason());
                    returnReasonDTO.setPrimaryReason(returnReason.getPrimaryReason());
                    returnReasonDTO.setOrderId(returnReason.getOrderId());
                    returnReasonDTO.setUwItemId(returnReason.getUwItemId());
                    returnReasonDTOS.add(returnReasonDTO);
                }
            }
        }
        log.info("[getReturnReasonsList] orderId : {}, returnReasonDTOS : {}", orderId, returnReasonDTOS);
        return returnReasonDTOS;
    }

    @Override
    public ReturnReasonDTO getReturnReason(Integer uwItemId, Integer returnId) {
        log.info("[getReturnReason] returnId : {} , uwItemId : {}", returnId, uwItemId);
        ReturnReasonDTO returnReasonDTO = new ReturnReasonDTO();
        ReturnDetailReason returnReason = returnReasonRepository.findTop1ByUwItemIdAndReturnIdOrderByCreatedAtDesc(uwItemId, returnId);
        if(returnReason != null){
            returnReasonDTO.setReturnId(returnId);
            returnReasonDTO.setSecondaryReason(returnReason.getSecondaryReason());
            returnReasonDTO.setPrimaryReason(returnReason.getPrimaryReason());
            returnReasonDTO.setOrderId(returnReason.getOrderId());
            returnReasonDTO.setUwItemId(returnReason.getUwItemId());
        }else{
            log.info("[getReturnReasonOld] fetching returnId : {}", returnId);
            ReturnReason returnReasonsOld = returnReasonOldRepository.findTop1ByUwItemIdAndReturnIdOrderByCreatedAtDesc(uwItemId, returnId);
            log.info("[getReturnReasonOld] done fetching returnId : {}", returnId);
            if(returnReasonsOld != null){
                returnReasonDTO.setReturnId(returnId);
                returnReasonDTO.setSecondaryReason(returnReasonsOld.getSecondaryReason());
                returnReasonDTO.setPrimaryReason(returnReasonsOld.getPrimaryReason());
                returnReasonDTO.setOrderId(returnReasonsOld.getOrderId());
                returnReasonDTO.setUwItemId(returnReasonsOld.getUwItemId());
            }
        }
        log.info("[getReturnReason] returnId : {} , uwItemId : {}, returnReasonDTO : {}", returnId, uwItemId, returnReasonDTO);
        return returnReasonDTO;
    }

    private boolean validateRequest(String platform, String category, ReturnReasonsResponse returnReasonsList) {
        if (StringUtils.isEmpty(platform) || StringUtils.isEmpty(category)) {
            ReturnReasonErrorResponse errorResponse = new ReturnReasonErrorResponse();
            if (StringUtils.isEmpty(platform)) {
                errorResponse.setError_msg("Invalid Platform.");
            }
            if (StringUtils.isEmpty(category)) {
                errorResponse.setError_msg("Invalid Category.");
            }
            returnReasonsList.setError_response(errorResponse);
            returnReasonsList.setResponse_status(HttpStatus.BAD_REQUEST);
            log.error("Invalid request param " + platform + ", " + category);
            return true;
        }
        return false;
    }

    private boolean getAndUpdateReasonsListInCache(String platform, String category, ReturnReasonsResponse returnReasonsResponse, String cacheKey) {
        List<ReturnReasonsMapping> returnReasonsMappings = getReturnReasonsMappings(platform, category);
        if (null == returnReasonsMappings || returnReasonsMappings.isEmpty()) {
            log.info("Empty reasons list from DB against platform " + platform + " and category " + category);
            ReturnReasonErrorResponse errorResponse = new ReturnReasonErrorResponse();
            errorResponse.setError_msg("Invalid Platform " + platform + " or Category " + category);
            returnReasonsResponse.setError_response(errorResponse);
            returnReasonsResponse.setResponse_status(HttpStatus.BAD_REQUEST);
            log.error("Invalid Platform " + platform + " or Category " + category);
            return true;
        }
        List<ReturnReasonDTO> reasonList = mapPrimaryToSecondaryReasons(returnReasonsMappings);

        returnReasonsResponse.setReturn_reasons(reasonList);
        returnReasonsResponse.setCategory(category);
        returnReasonsResponse.setPlatform(platform);
        returnReasonsResponse.setResponse_status(HttpStatus.OK);
        cacheDataService.saveMessage(cacheKey, returnReasonsResponse, "60");
        return false;
    }

    private List<ReturnReasonsMapping> getReturnReasonsMappings(String platform, String category) {
        List<ReturnReasonsMapping> returnReasonsMappings;
        log.info("Fetching return reasons from DB against platform " + platform + " and category " + category);
        try {
            int categoryId = Integer.parseInt(category);
            returnReasonsMappings = exchangeReturnReasonRepository.getReturnReasonsMappingsByPlatformAndClassification(platform, categoryId);
        } catch (NumberFormatException nfe) {
            returnReasonsMappings = exchangeReturnReasonRepository.getReasonsByPlatformAndClassificationVal(platform, category);
        }
        return returnReasonsMappings;
    }

    private List<ReturnReasonDTO> mapPrimaryToSecondaryReasons(List<ReturnReasonsMapping> returnReasonsMappings) {
        List<List<ReturnReasonsMapping>> list = returnReasonsMappings.stream().collect(Collectors.groupingBy(ReturnReasonsMapping::getPrimaryReturnReasons)).values().stream().toList();
        Map<PrimaryReturnReasons, List<SecondaryReturnReason>> primaryReturnReasonsListMap = new HashMap<>();
        list.forEach(r -> r.forEach(reason -> updateReasonsList(reason, primaryReturnReasonsListMap)));
        List<ReturnReasonDTO> reasonList = new ArrayList<>();
        for (Map.Entry<PrimaryReturnReasons, List<SecondaryReturnReason>> entry : primaryReturnReasonsListMap.entrySet()) {
            ReturnReasonDTO primaryReason = new ReturnReasonDTO();
            primaryReason.setId(entry.getKey().getPrimaryReasonId());
            primaryReason.setReason(entry.getKey().getReason());
            if (entry.getKey().getIsInsuranceReason() != null && entry.getKey().getIsInsuranceReason()) {
                primaryReason.setReason_tag(Constant.INSURANCE);
            } else {
                primaryReason.setReason_tag("");
            }
            List<SecondaryReasonDTO> secondaryReasonDetails = entry.getValue().stream().map(this::updateReasonDetails).collect(Collectors.toList());
            primaryReason.setSecondary_reasons(secondaryReasonDetails);
            reasonList.add(primaryReason);
        }
        return reasonList;
    }

    private SecondaryReasonDTO updateReasonDetails(SecondaryReturnReason reason) {
        SecondaryReasonDTO secondaryReason = new SecondaryReasonDTO();
        secondaryReason.setId(reason.getSecondaryReasonId());
        secondaryReason.setReason(reason.getReason());
        return secondaryReason;
    }

    private void updateReasonsList(ReturnReasonsMapping reason, Map<PrimaryReturnReasons, List<SecondaryReturnReason>> primaryReturnReasonsListMap) {
        PrimaryReturnReasons primaryReturnReasons = reason.getPrimaryReturnReasons();
        SecondaryReturnReason secondryReturnReason = reason.getSecondaryReturnReasons();
        if (reason.getIsInsuranceReason()) {
            primaryReturnReasons.setIsInsuranceReason(true);
        }
        if (primaryReturnReasonsListMap.containsKey(primaryReturnReasons)) {
            primaryReturnReasonsListMap.get(primaryReturnReasons).add(secondryReturnReason);
        } else {
            List<SecondaryReturnReason> list = new ArrayList<>();
            list.add(secondryReturnReason);
            primaryReturnReasonsListMap.put(primaryReturnReasons, list);
        }
    }
}
