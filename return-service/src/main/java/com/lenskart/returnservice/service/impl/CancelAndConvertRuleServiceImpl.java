package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto;
import com.lenskart.returncommon.model.dto.RuleContextDTO;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.response.CancelAndConvertEventRuleResponse;
import com.lenskart.returnrepository.repository.ICancelAndConvertRuleRepo;
import com.lenskart.returnservice.config.EventRuleConfiguration;
import com.lenskart.returnservice.service.ICancelAndConvertRuleService;
import com.lenskart.returnservice.service.IEventDecisionService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Slf4j
@Service
public class CancelAndConvertRuleServiceImpl implements ICancelAndConvertRuleService {
    @Autowired
    ICancelAndConvertRuleRepo cancelAndConvertRuleRepo;
    @Autowired
    IEventDecisionService decisionService;

    @Override
    public CancelAndConvertEventRuleResponse evaluateAndGetActions(PaymentMode paymentMode, RuleContextDTO request) {
        log.info("CancelAndConvertRuleServiceImpl.evaluateAndGetActions paymentMode:{}, ruleContextDTO:{}",paymentMode, request);
        CancelAndConvertEventRuleResponse cancelAndConvertEventRuleResponse = null;
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = getEventRules();
        try {
            cancelAndConvertEventRuleResponse = decisionService.getActionsResult(paymentMode, request, eventRules);
        }
        catch (Exception e){
            log.error("Error while evaluating next event for {} from {}", paymentMode.name(), e);
        }
        return cancelAndConvertEventRuleResponse;
    }




    @Override
    public Map<PaymentMode, List<CancelAndConvertRuleDto>> fetchAndPopulateRules() {
        List<CancelAndConvertRuleDto> rules = cancelAndConvertRuleRepo.getAllRules();
        log.info("fetchAndPopulateRules from db :{}", rules);
        return rules.stream().collect(Collectors.groupingBy(CancelAndConvertRuleDto::getPaymentMode));
    }

    private Map<PaymentMode, List<CancelAndConvertRuleDto>> getEventRules() {
        EventRuleConfiguration ruleConfiguration = EventRuleConfiguration.getInstance();
        if(ruleConfiguration.getEventRules() == null || ruleConfiguration.getEventRules().isEmpty()) {
            ruleConfiguration.setEventRules(fetchAndPopulateRules());
        }
        return ruleConfiguration.getEventRules();
    }
}
