package com.lenskart.returnservice.service.impl.enrichers;

import com.lenskart.returncommon.enrichers.ReturnOrderEnricher;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnrepository.repository.ReturnCourierDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CourierDetailEnricher implements ReturnOrderEnricher {

    @Autowired
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    private Map<Integer, ReturnCourierDetail> data = new HashMap<>();

    private ReturnOrderEnricher nextEnricher;

    @Override
    public void setNextEnricher(ReturnOrderEnricher nextEnricher) {
        this.nextEnricher = nextEnricher;
    }

    @Override
    public void enrich(ReturnOrderDTO dto) {
        ReturnCourierDetail detail = data != null ? data.get(dto.getId()) : null;
        if (detail != null && detail.getReverseAwbNumber() != null && detail.getReversePickupReferenceId() != null) {
            dto.setReverseAwb(detail.getReverseAwbNumber());
            dto.setReversePickupReferenceId(detail.getReversePickupReferenceId());
            log.info("[CourierDetailEnricher] Added tracking details revAwbNumber:{},revPickupId :{} for returnId:{}", detail.getReverseAwbNumber(), detail.getReversePickupReferenceId(), dto.getId());
        } else if (nextEnricher != null) {
            log.info("Checking next enricher: {}, for returnId: {}", nextEnricher.getClass().getName(),dto.getId());
            nextEnricher.enrich(dto);
        }
    }

    public void preload(List<Integer> returnIds) {
        List<ReturnCourierDetail> details = Optional.ofNullable(returnCourierDetailRepository.findByReturnIdIn(returnIds))
                .orElse(Collections.emptyList());
        /**
         * prepares map of <returnId,ReturnCourierDetail>
         */
        this.data = details.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        ReturnCourierDetail::getReturnId,
                        Function.identity(),
                        (existing, replacement) -> replacement
                ));
    }
}
