package com.lenskart.returnservice.service;

import com.lenskart.orderops.model.Canceledorders;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.response.RefundAmountDetail;
import com.lenskart.refund.client.model.response.RefundDetailing;
import com.lenskart.refund.client.model.response.RefundDetailsResponse;
import com.lenskart.refund.client.model.response.StoreCreditResponse;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.ExchangeMasterMapping;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.StoreCreditRefundDetails;
import com.lenskart.returnrepository.entity.ExchangeEventsTracker;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.repository.ExchangeEventTrackerRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.RefundFeignClient;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
@Slf4j
public class CancelAndConvertServiceImpl implements ICancelAndConvertService{

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    List<String> facilityCodeList;

    @Autowired
    private RefundFeignClient refundFeignClient;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private ExchangeEventTrackerRepository exchangeEventTrackerRepository;

    @PostConstruct
    public void init() {
        String systemPreferenceValues = systemPreferenceService.getSystemPreferenceValues( "B2B_WH_FACILITY","NEW_REFUND_FLOW");
        if (systemPreferenceValues != null) {
            facilityCodeList = Arrays.asList(systemPreferenceValues.split("\\s*,\\s*"));
        }
    }

    @Override
    public StoreCreditRefundDetails getScRefundDetails(Long magentoId) {
        ResponseEntity<PurchaseOrderDetailsDTO> orderDetails = orderOpsFeignClient.getPurchaseOrderDetails("MAGENTO_ITEM_ID", String.valueOf(magentoId));
        Integer uwItemId = null;
        String unicomOrderCode = null;
        StoreCreditRefundDetails storeCreditRefundDetails = null;
        if(orderDetails.getStatusCode().is2xxSuccessful() && orderDetails.getBody() != null){
            PurchaseOrderDetailsDTO orderDetailsDTO = orderDetails.getBody();
            List<UwOrderDTO> uwOrders = orderDetailsDTO.getUwOrders();
            if(!CollectionUtils.isEmpty(uwOrders)){
                for(UwOrderDTO uwOrderDTO : uwOrders){
                    if("B2B".equalsIgnoreCase(uwOrderDTO.getProductDeliveryType()) && facilityCodeList.contains(uwOrderDTO.getFacilityCode())){
                        uwItemId = uwOrderDTO.getUwItemId();
                        unicomOrderCode = uwOrderDTO.getUnicomOrderCode();
                    }else if(uwOrderDTO.getParentUw() == 0){
                        uwItemId = uwOrderDTO.getUwItemId();
                        unicomOrderCode = uwOrderDTO.getUnicomOrderCode();
                    }
                }

            }
        }
        storeCreditRefundDetails = getScRefundDetails(uwItemId, unicomOrderCode);
        return storeCreditRefundDetails;
    }

    @Override
    public StoreCreditRefundDetails getScRefundDetails(Integer uwItemId, String unicomOrderCode) {
        log.info("[getScRefundDetails] uwItemId : {}, unicomOrderCode : {}", uwItemId, unicomOrderCode);

        if(uwItemId == null && unicomOrderCode == null){
            return null;
        }

        ReturnDetail returnDetail = returnOrderActionService.findReturnIdByReturnTypeUwItemID(Arrays.asList("rto","awaited_rto","reverse"), uwItemId);
        Integer returnId = null;
        if(returnDetail != null){
            returnId = returnDetail.getId();
        }

        double refundedScAmount = 0.0;
        boolean rtoFlag = false, returnFlag = false, partialCancellationFlag = false, fastRefundFlag = false;
        StoreCreditResponse storeCreditResponse = null;
        Long refundId = null;
        Integer orderId = null;

        CheckRefundInitiatedRequest request = new CheckRefundInitiatedRequest();
        if(returnId != null){
            request.setIdentifierType(IdentifierType.RETURN_ID);
            request.setIdentifierValue(String.valueOf(returnId));
            storeCreditResponse = getStoreCredit(request);
            refundedScAmount = getScRefundedAmount(storeCreditResponse);
            returnFlag  = "reverse".equalsIgnoreCase(returnDetail.getReturnType()) && refundedScAmount > 0.0d;
            rtoFlag = Arrays.asList("rto","awaited_rto").contains(returnDetail.getReturnType()) && refundedScAmount > 0.0d;
        }

        if(refundedScAmount == 0.0d){
            request.setIdentifierType(IdentifierType.UW_ITEM_ID);
            request.setIdentifierValue(String.valueOf(uwItemId));
            storeCreditResponse = getStoreCredit(request);
            refundedScAmount = getScRefundedAmount(storeCreditResponse);
            fastRefundFlag = refundedScAmount > 0.0d;
        }

        if(refundedScAmount == 0.0d){
            request.setIdentifierType(IdentifierType.SHIPMENT_ID);
            request.setIdentifierValue(String.valueOf(unicomOrderCode));
            storeCreditResponse = getStoreCredit(request);
            refundedScAmount = getScRefundedAmount(storeCreditResponse);
            partialCancellationFlag = refundedScAmount > 0.0d;
        }

        if(storeCreditResponse != null){
            refundId = storeCreditResponse.getRefundId();
            orderId = storeCreditResponse.getOrderId();
        }

        StoreCreditRefundDetails storeCreditRefundDetails = StoreCreditRefundDetails.builder()
                .partialCancellationFlag(partialCancellationFlag)
                .fastRefundFlag(fastRefundFlag)
                .rtoFlag(rtoFlag)
                .returnFlag(returnFlag)
                .refundedScAmount(refundedScAmount)
                .storeCreditResponse(storeCreditResponse)
                .refundId(refundId)
                .orderId(orderId)
                .build();

        log.info("[getScRefundDetails] storeCreditRefundDetails : {}",storeCreditRefundDetails);

        return storeCreditRefundDetails;
    }

    @Override
    public List<Long> getExchangeCreatedButNotCancelledDetails(List<Long> magentoList) throws Exception {
        List<Long> exchangeCreatedButNotCancelledMagentos = new ArrayList<>();
        for(Long magento : magentoList){
            ExchangeEventsTracker exchangeEventsTracker = exchangeEventTrackerRepository.findTopByMasterMagentoItemId(magento);
            if(exchangeEventsTracker != null){
                List<Canceledorders> canceledOrders = orderOpsFeignClient.getCanceledOrders(Math.toIntExact(exchangeEventsTracker.getExchangeOrderId()));
                if(CollectionUtils.isEmpty(canceledOrders)){
                    log.info("[getExchangeCreatedButNotCancelledDetails] magentoItemId : {} --- exchange-order : {} is not cancelled, hence adding to list", magento, exchangeEventsTracker.getExchangeOrderId());
                    exchangeCreatedButNotCancelledMagentos.add(magento);
                }
            }
        }
        log.info("[getExchangeCreatedButNotCancelledDetails] magentoList : {} , exchangeCreatedButNotCancelledMagentos : {}",  magentoList, exchangeCreatedButNotCancelledMagentos);
        return exchangeCreatedButNotCancelledMagentos;
    }

    @Override
    public ExchangeMasterMapping getExchangeMasterMapping(List<Long> magentoList) throws Exception {
        ExchangeMasterMapping exchangeMasterMapping = new ExchangeMasterMapping();
        for(Long magento : magentoList){
            ExchangeEventsTracker exchangeEventsTracker = exchangeEventTrackerRepository.findTopByMasterMagentoItemId(magento);
            if(exchangeEventsTracker != null){
                List<Canceledorders> canceledOrders = orderOpsFeignClient.getCanceledOrders(Math.toIntExact(exchangeEventsTracker.getExchangeOrderId()));
                if(CollectionUtils.isEmpty(canceledOrders)){
                    log.info("[getExchangeMasterMapping] magentoItemId : {} --- exchange-order : {} is not cancelled, hence adding to map", magento, exchangeEventsTracker.getExchangeOrderId());
                    Map<Long, Integer> exchangeMasterMappingMap = exchangeMasterMapping.getExchangeMasterMappingMap();
                    exchangeMasterMappingMap.put(magento, Math.toIntExact(exchangeEventsTracker.getExchangeOrderId()));
                }
            }
        }
        log.info("[getExchangeMasterMapping] magentoList : {} --- exchangeMasterMappingMap : {}", magentoList, exchangeMasterMapping);
        return exchangeMasterMapping;
    }

    private StoreCreditResponse getStoreCredit(CheckRefundInitiatedRequest request){
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type","application/json");
        StoreCreditResponse storeCreditResponse = null;
        log.info("[getScRefunded] request : {}", request);
        ResponseEntity<RefundDetailing> refundDetailsResponse = refundFeignClient.getRefundDetails(request, headers);
        log.info("[getScRefunded] refund response : {}", refundDetailsResponse);
        if(refundDetailsResponse.getStatusCode().is2xxSuccessful() && refundDetailsResponse.getBody() != null){
            RefundDetailing refundDetailing = refundDetailsResponse.getBody();
            List<RefundDetailsResponse> refundDetailsList = refundDetailing.getRefundDetailsList();
            if(!CollectionUtils.isEmpty(refundDetailsList)){
                RefundAmountDetail refundAmountDetail = refundDetailsList.get(0).getRefundAmountDetail();
                storeCreditResponse = refundAmountDetail.getStoreCreditResponse();
                storeCreditResponse.setRefundId(refundDetailsList.get(0).getRefundId());
            }
        }
        return storeCreditResponse;
    }

    private double getScRefundedAmount(StoreCreditResponse storeCreditResponse){
        double balance = 0.0d;
        if(storeCreditResponse != null){
            String status = storeCreditResponse.getStatus();
            if("STATUS_ACTIVE".equalsIgnoreCase(status)){
                balance = storeCreditResponse.getBalance();
            }
        }
        return balance;
    }
}
