package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;

public interface IReturnInitiationService {
    ReturnCreationResponse createReturn(ReturnCreationRequestDTO returnCreationRequest) throws Exception;

    ReverseCourierDetail assignReverseCourier(Integer orderId, Integer incrementId, Boolean isNewAddress, Integer pincodeInRequest, int offset, QCType qcType, boolean newFlow, OrderInfoResponseDTO orderInfoResponseDTO) throws Exception;

    Integer getQcRuleCount();
}