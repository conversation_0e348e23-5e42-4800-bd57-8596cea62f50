package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.ActionRuleContextDTO;
import com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto;
import com.lenskart.returncommon.model.dto.RuleContextDTO;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.response.CancelAndConvertEventRuleResponse;
import com.lenskart.returnservice.service.IEventDecisionAlgorithm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventDecisionAlgorithmImpl implements IEventDecisionAlgorithm {

    @Override
    public CancelAndConvertEventRuleResponse getActionsResult(PaymentMode paymentMode, RuleContextDTO context, Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules) {
        log.info("[getActionsResult] request paymentMode {} context {} eventRules {}", paymentMode, context, eventRules);
        if (eventRules == null || eventRules.isEmpty() || eventRules.get(paymentMode) == null || eventRules.get(paymentMode).isEmpty()) {
            log.info("EventDecisionAlgorithmImpl.getNextEventType Empty event rules");
            return new CancelAndConvertEventRuleResponse();
        }

        CancelAndConvertRuleDto finalEventRuleDto = null;
        for(CancelAndConvertRuleDto rule : eventRules.get(paymentMode)){
            if(rule.getPaymentMode().equals(paymentMode) && rule.conditionsMet(context)){
                finalEventRuleDto = rule;
                break;
            }
        }
        if(finalEventRuleDto!=null){
            CancelAndConvertEventRuleResponse response = new CancelAndConvertEventRuleResponse();
            log.info("[getActionsResult] response {}", finalEventRuleDto);
            response.setActionRuleContextDTO(ActionRuleContextDTO.builder()
                    .conditions(finalEventRuleDto.getActionMap())
                    .build());
            return response;
        }
        return new CancelAndConvertEventRuleResponse();
    }
}
