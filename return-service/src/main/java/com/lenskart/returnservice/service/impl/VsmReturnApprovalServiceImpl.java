package com.lenskart.returnservice.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returncommon.model.dto.NeedApprovalRequest;
import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnservice.service.INeedApprovalProcessorService;
import com.lenskart.returnservice.service.IReturnInitiationService;
import com.lenskart.returnservice.service.IReturnUpdateService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service(value = "VSM_ReturnApprovalServiceImpl")
public class VsmReturnApprovalServiceImpl extends ReturnInitiationAbstractionService implements IReturnInitiationService {

    @Value("${source.nav.channel:BRWEBDTC}")
    private String sourceNavChannels;

    @Value("${redis.repeatedReturnApproval.expirationTime:6}")
    Integer repeatedReturnExpTime;

    private final INeedApprovalProcessorService needApprovalProcessorService;

    @Autowired
    @Qualifier("ReturnInitiationServiceImpl")
    private IReturnInitiationService webReturnInitiationService;

    @Autowired
    RedisTemplate<String,Object> redisTemplate;

    @Autowired
    IReturnUpdateService returnUpdateService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));


    @Override
    public ReturnCreationResponse createReturn(ReturnCreationRequestDTO returnCreationRequest) throws Exception {
        ReturnCreationRequestDTO initialReturnCreationRequest = mapper.convertValue(returnCreationRequest, ReturnCreationRequestDTO.class);
        Map<Long, ReturnItemDTO> magentoItemToReturnItemMap = new HashMap<>();
        Map<Long, String> returnIntentMap = new HashMap<>();
        List<ReturnItemDTO> exchangeItemList = new ArrayList<>();
        List<ReturnItemDTO> returnItemsUpdated = new ArrayList<>();
        List<UwOrderDTO> uwOrders = new ArrayList<>();
        Map<ReturnItemDTO, Integer> productIdsMap = new HashMap<>();
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = getOrderDetails(returnCreationRequest, magentoItemToReturnItemMap, returnIntentMap, exchangeItemList, returnItemsUpdated, uwOrders, productIdsMap);
        String identifierType = purchaseOrderDetailsDTO.getIdentifierType();
        String identifierValue = purchaseOrderDetailsDTO.getIdentifierValue();
        Integer incrementId = purchaseOrderDetailsDTO.getOrdersHeaderDTO().getIncrementId();

        for (ReturnItemDTO item : returnCreationRequest.getItems()) {
            Integer uwItemId = item.getUwItemId();
            if(isRepeatedReturnTransaction(uwItemId)){
                log.error("[VsmReturnApprovalService:createReturn] repeated return txn call : for uwItemId : {}", uwItemId);
                throw new Exception("Repeated Transaction for uwItemId "+uwItemId);
            } else{
                storeReturnTransaction(uwItemId);
            }
        }

        ReturnRequest returnRequest = createReturnRequest(returnCreationRequest, magentoItemToReturnItemMap, returnIntentMap, identifierType, identifierValue, incrementId);
        enrichReturnCreationRequest(returnCreationRequest, productIdsMap, returnItemsUpdated, incrementId);
        ReverseCourierDetail reverseCourierDetail = getReverseCourierDetail();
        ReturnCreationResponse returnCreationResponse = createReturn(purchaseOrderDetailsDTO, returnCreationRequest, reverseCourierDetail, returnRequest);
        returnCreationResponse = getReturnCreationResponse(reverseCourierDetail, returnCreationResponse, incrementId, uwOrders);
        boolean isApproved = proceedForNeedApproval(returnCreationResponse, returnRequest, returnCreationRequest);
        if(isApproved) {
            initialReturnCreationRequest.getItems().forEach(item -> item.setNeedApproval(false));
            return webReturnInitiationService.createReturn(initialReturnCreationRequest);
        }
        return returnCreationResponse;
    }

    @Override
    public ReverseCourierDetail assignReverseCourier(Integer orderId, Integer incrementId, Boolean isNewAddress, Integer pincodeInRequest, int offset, QCType qcType, boolean newFlow, OrderInfoResponseDTO orderInfoResponseDTO) throws Exception {
        return null;
    }

    @Override
    public Integer getQcRuleCount() {
        return null;
    }

    private ReverseCourierDetail getReverseCourierDetail() {
        return new ReverseCourierDetail();
    }

    private Boolean proceedForNeedApproval(ReturnCreationResponse returnCreationResponse, ReturnRequest returnRequest, ReturnCreationRequestDTO returnCreationRequest) {
        boolean isApproved = false;
        for(ReturnItemDTO item : returnCreationRequest.getItems())  {
            NeedApprovalRequest needApprovalRequest = createNeedApprovalRequest(returnCreationResponse.getReturnId(), item.getRefundMethodRequest(), returnCreationRequest.getRequestApprover(), item.getApproverComment(), item.isApproved());
            isApproved = isApproved ? isApproved : item.isApproved();
            try {
                saveDelightAction(needApprovalRequest, returnCreationRequest, item);
            } catch (Exception e) {
                log.error("[proceedForNeedApproval] returnId : {}, item : {} error: {}", returnCreationResponse.getReturnId(), item, e.getMessage());
            }
        }
        return isApproved;
    }

    private NeedApprovalRequest createNeedApprovalRequest(Integer returnId, String refundAction, String user, String comments, Boolean isApproved) {
        String returnStatus = isApproved ? ReturnStatus.RETURN_ACCEPTED.getStatus() : ReturnStatus.RETURN_REJECTED.getStatus();
        String delightAction = isApproved ? "APPROVE" : "REJECT";
        refundAction = isApproved ? refundAction : "";
        NeedApprovalRequest needApprovalRequest = new NeedApprovalRequest();
        needApprovalRequest.setReturnId(returnId);
        needApprovalRequest.setStatus(returnStatus);
        needApprovalRequest.setDelightAction(delightAction);
        needApprovalRequest.setDelightMethod(refundAction);
        needApprovalRequest.setComments(String.format("Pending approval %s by : %s with reason - %s", returnStatus, user, comments));
        needApprovalRequest.setDelightComment(String.format("Return %s via VSM by :: %s", delightAction, user));
        needApprovalRequest.setUsername(user);
        needApprovalRequest.setAgentAutoApproval(true);

        ApprovalStatusRequest approvalStatusRequest = new ApprovalStatusRequest();
        approvalStatusRequest.setReturnStatus(returnStatus);
        approvalStatusRequest.setRetryCount(0);
        approvalStatusRequest.setSelectedRefundAction(refundAction);

        needApprovalRequest.setApprovalStatusRequest(approvalStatusRequest);
        return needApprovalRequest;
    }

    private boolean isRepeatedReturnTransaction(Integer incrementId){
        String key = incrementId+"_ReturnApproval";
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    private void storeReturnTransaction(Integer incrementId){
        String key = incrementId+"_ReturnApproval";
        redisTemplate.opsForValue().set(key, incrementId, repeatedReturnExpTime, TimeUnit.SECONDS);
    }

    private ReturnDetailUpdateRequestDto getReturnDetailUpdateRequest(NeedApprovalRequest needApprovalRequest, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, Integer uwItemId) {
        ReturnDetailUpdateRequestDto request = new ReturnDetailUpdateRequestDto();
        request.setReturnId(needApprovalRequest.getReturnId());
        request.setComments(needApprovalRequest.getComments());
        request.setUsername(needApprovalRequest.getUsername());
        request.setStatus(needApprovalRequest.getStatus());

        UwOrderDTO uwOrder = purchaseOrderDetailsDTO.getUwOrders()
        .stream()
        .filter(uw -> Objects.equals(uw.getUwItemId(), uwItemId))
        .findFirst()
        .get();
        
        request.setUwOrderDTO(uwOrder);
        return request;
    }

    private void saveDelightAction(NeedApprovalRequest needApprovalRequest, ReturnCreationRequestDTO returnCreationRequest, ReturnItemDTO item) throws Exception {
        needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
        ReturnDetailsUpdateResponse returnDetailsUpdateResponse = returnUpdateService.updateReturnStatus(getReturnDetailUpdateRequest(needApprovalRequest,returnCreationRequest.getPurchaseOrderDetailsDTO(),item.getUwItemId()));
        log.info("[saveDelightAction] ReturnDetailsUpdateResponse: {}", returnDetailsUpdateResponse);
    }

}
