package com.lenskart.returnservice.service.impl;

import com.lenskart.returnrepository.entity.ReturnHeadingSubheadingMapping;
import com.lenskart.returnrepository.repository.ReturnHeadingSubheadingMappingRepository;
import com.lenskart.returnservice.service.ReturnHeadingSubheadingMappingCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReturnHeadingSubheadingMappingCacheServiceImpl implements ReturnHeadingSubheadingMappingCacheService {

    @Autowired
    ReturnHeadingSubheadingMappingRepository returnHeadingSubheadingMappingRepository;

    @Override
    @Cacheable(value = "return_status_heading", keyGenerator = "return_status_heading_key_gen")
    public ReturnHeadingSubheadingMapping getMapping(String returnSource, String returnStatus, String dispatchPoint, String refundMethod, String refundMode, Boolean exchangeExpired, Boolean exchangeCreated, Boolean exchangeOrderDispatched, String refundStatus) {
        ReturnHeadingSubheadingMapping returnHeadingSubheadingMapping = null;
        try {
            returnHeadingSubheadingMapping = returnHeadingSubheadingMappingRepository.findByParams(
                    returnSource, returnStatus, dispatchPoint, refundMethod, refundMode,
                    exchangeExpired, exchangeCreated, exchangeOrderDispatched, refundStatus
            );
        } catch (Exception e) {
            log.error("An error occurred while retrieving mapping", e);
        }
        return returnHeadingSubheadingMapping;
    }

    @Cacheable(value = "return_status_heading", keyGenerator = "return_status_heading_key_gen")
    @Override
    public ReturnHeadingSubheadingMapping getReturnHeadingSubheadingMapping(String returnSource, String returnStatus, String refundStatus, String refundMethod, String refundMode) {
        ReturnHeadingSubheadingMapping returnHeadingSubheadingMapping = null;
        try {
            returnHeadingSubheadingMapping = returnHeadingSubheadingMappingRepository.getReturnHeadingSubheadingMapping(returnSource, returnStatus, refundStatus, refundMethod, refundMode);
        } catch (Exception e) {
            log.error("An error occurred while retrieving mapping", e);
        }
        return returnHeadingSubheadingMapping;
    }
}
