package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lenskart.core.model.Refund;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.ReturnEventDTO;
import com.lenskart.orderops.model.RefundRequest;
import com.lenskart.returncommon.model.enums.TransactionType;
import com.lenskart.returncommon.model.request.*;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.response.*;
import com.lenskart.ordermetadata.dto.response.ReturnReasonTableDTO;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.response.CheckRefundInitiatedResponse;
import com.lenskart.return_refund_rules.model.ReturnItemRequest;
import com.lenskart.returncommon.exception.ReturnNotFound;
import com.lenskart.returncommon.exception.ReturnRequestException;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.response.*;
import com.lenskart.returncommon.model.dto.ReturnDetailsDTO;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnOrderItemDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.predicate.RefundPredicate;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnrepository.specifications.ReturnDetailSpecification;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.RefundFeignClient;
import com.lenskart.returnservice.service.*;
import com.lenskart.returnservice.utils.ReturnOrderItemMapper;
import com.lenskart.returnservice.utils.ReturnReasonMapper;
import com.lenskart.returnservice.utils.ReverseCourierDetailMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.lenskart.returncommon.predicate.RefundPredicate.IS_RETURN_NOT_IN_RPU_CYCLE;
import static com.lenskart.returncommon.utils.Constant.EVENT.*;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.OTC;
import static com.lenskart.returncommon.utils.Constant.RECEIVING_FLAG.YES;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.REVERSE;
import static com.lenskart.returncommon.utils.Constant.STATUS.INITIATED_STOCKIN;
import static com.lenskart.returncommon.utils.Constant.TRACKING_STATUS.REFUND_COMPLETED;
import static com.lenskart.returncommon.utils.ReturnPredicate.IS_RETURN_NOT_REJECTED_AND_CANCELLED_AND_RESHIP;

@Slf4j
@Service
public class ReturnOrderActionServiceImpl implements IReturnOrderActionService {

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    @Autowired
    private ReturnOrderItemRepository returnOrderItemRepository;

    @Autowired
    private ReturnReasonOldRepository returnReasonOldRepository;

    @Autowired
    private ReturnOrderAddressUpdateOldRepository returnOrderAddressUpdateOldRepository;

    @Autowired
    private ReturnHistoryRepository returnHistoryRepository;

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private ReverseTrackingEventRepository reverseTrackingEventRepository;

    private static final ThreadLocal<Map<Integer, String>> returnIdToStatusMap = ThreadLocal.withInitial(HashMap::new);


    @Autowired
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @Autowired
    private INexsFacilityService nexsFacilityService;

    @Autowired
    private ReturnReasonRepository returnReasonRepository;

    @Autowired
    private IRefundAuthorizationService authorizationService;

    @Autowired
    private CombinedReturnDetailsRepository combinedReturnDetailsRepository;

    @Autowired
    ReversePickupPincodeRepository reversePickupPincodeRepository;

    @Autowired
    ReverseCourierMappingRepository reverseCourierMappingRepository;

    @Autowired
    ReturnOrderEnrichmentService returnOrderEnrichmentService;

    @Autowired
    ReturnOrderItemMapper returnOrderItemMapper;

    @Autowired
    ReverseCourierDetailMapper reverseCourierDetailMapper;

    @Autowired
    ReturnReasonMapper returnReasonMapper;

    @Autowired
    ModelPromptRepository modelPromptRepository;


    public static final List<String> newReturnStatus = Arrays.asList(ReturnStatus.CUSTOMER_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED.getStatus(),
            ReturnStatus.CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED.getStatus(), ReturnStatus.RETURN_RESHIP.getStatus(),
            ReturnStatus.RETURN_REFUND_REJECTED.getStatus(), ReturnStatus.EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED_TBYB.getStatus(),
            ReturnStatus.PARTIAL_EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus());

    static List<String> returnStatusList = Arrays.asList(ReturnStatus.CUSTOMER_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED.getStatus(),
            ReturnStatus.CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED.getStatus(), ReturnStatus.RETURN_RESHIP.getStatus(),
            ReturnStatus.RETURN_REFUND_REJECTED.getStatus(), ReturnStatus.EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_CLOSED_TBYB.getStatus(), ReturnStatus.PARTIAL_EASY_REFUND_GIVEN_PICKUP_CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus());

    private static final List<String> reuseReturnStatus = Arrays.asList(ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_UNDER_FOLLOWUP.getStatus(),
            ReturnStatus.RETURN_FOLLOWED_UP.getStatus(), ReturnStatus.RETURN_PENDING_APPROVAL.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus(),
            ReturnStatus.NEW_REVERSE_PICKUP.getStatus(), ReturnStatus.REFERENCE_ID_ISSUED.getStatus(), ReturnStatus.AWB_ASSIGNED.getStatus(),
            ReturnStatus.SC_REFUNDED_RECEIVING_PENDING.getStatus(), ReturnStatus.SELFDISPATCHED.getStatus(), ReturnStatus.RETURN_REFUNDED_RECEIVING_PENDING.getStatus(), ReturnStatus.INITIATED_REVERSE.getStatus(),
            ReturnStatus.RETURN_EXPECTED_POS.getStatus(), ReturnStatus.RETURN_EXPECTED_WH.getStatus(), ReturnStatus.RETURN_ACCEPTED.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_RECEIVED_ACTION_PENDING.getStatus());

    private static final List<ReturnStatus> DRAFTED_STATUS = Arrays.asList(ReturnStatus.RETURN_EXPECTED_WH, ReturnStatus.RETURN_EXPECTED_POS);

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReturnGroupRepository returnGroupRepository;

    @Value("#{'${bypassReturnReceivingCountries:SG}'.split(',')}")
    private List<String> bypassReturnReceivingCountries;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    private static Gson gson = new GsonBuilder().setPrettyPrinting().create();

    private static ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Autowired
    private RefundFeignClient refundFeignClient;

    @Value("${feign.client.domain.order-ops:https://order-ops.scm.preprod.lenskart.com}")
    private String orderOpsBaseUrl;

    private List<String> facilityCodeList = new ArrayList<>();

    @Autowired
    private ReturnRequestRepository returnRequestRepository;
    @Autowired
    private RefundRulesRepository refundRulesRepository;

    @PostConstruct
    public void init() {
        SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey("NEW_REFUND_FLOW", "B2B_WH_FACILITY");
        if (systemPreference != null) {
            String facilityCode = systemPreference.getValue();
            facilityCodeList = Arrays.asList(facilityCode.split("\\s*,\\s*"));
        }
    }

    @Override
    public boolean isExisitingReturnCancelled(Integer uwItemId, String source) {
        Optional<ReturnDetail> returnDetailsOpt = getExistingReturnDetailsByItem(uwItemId);
        if (returnDetailsOpt.isPresent()) {
            log.debug("Existing return details found in db for uwItemId {}", uwItemId);
            ReturnDetail returnDetail = returnDetailsOpt.get();

            return checkIsReturnOrderCancelled(returnDetail);
        } else {
            log.info("No existing return details found in db for uwItemId {}", uwItemId);
            return true;
        }
    }

    @Override
    public boolean checkIsReturnOrderCancelled(ReturnDetail returnDetail) {
        String status = getReturnOrderStatus(returnDetail);
        if (newReturnStatus.contains(status)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isExisitingReturnNotRejectedCancelledReshiped(ReturnDetail returnOrder) {
        String status = getReturnOrderStatus(returnOrder);
        if (IS_RETURN_NOT_REJECTED_AND_CANCELLED_AND_RESHIP.test(status)) {
            return true;
        }
        return false;
    }

    @Override
    public String getReturnOrderStatus(ReturnDetail returnOrder) {
        if (returnOrder == null) {
            return null;
        }

        if (!StringUtils.isEmpty(returnOrder.getStatus())) {
            return returnOrder.getStatus();
        }

        List<String> eventTypes = Arrays.stream(ReturnStatus.values()).map(ReturnStatus::getName).toList();

        ReturnEvent returnEvent = returnEventRepository.findTopByReturnIdAndEventInOrderByIdDesc(returnOrder.getId(), eventTypes);

        String status = Optional.ofNullable(returnEvent)
                .map(event -> ReturnStatus.valueOf(event.getEvent().toUpperCase()).getStatus())
                .orElse(null);

        log.info("[getReturnOrderStatus] order : {}, status : {}", returnOrder.getIncrementId(), status);
        return status;
    }

    @Override
    public Map<Integer, String> getReturnOrderStatus(List<ReturnDetail> returnOrders) {
        if (CollectionUtils.isEmpty(returnOrders)) {
            return Collections.emptyMap();
        }

        // Filter out returnOrders that already have status
        Map<Integer, String> result = returnOrders.stream()
                .filter(ro -> StringUtils.isNotEmpty(ro.getStatus()))
                .collect(Collectors.toMap(ReturnDetail::getId, ReturnDetail::getStatus));

        // Get remaining returnIds that need lookup from ReturnEvent
        List<Integer> missingStatusReturnIds = returnOrders.stream()
                .filter(ro -> StringUtils.isEmpty(ro.getStatus()))
                .map(ReturnDetail::getId)
                .toList();

        if (!missingStatusReturnIds.isEmpty()) {
            List<String> eventTypes = Arrays.stream(ReturnStatus.values())
                    .map(ReturnStatus::getName)
                    .toList();

            List<ReturnEvent> latestEvents = returnEventRepository.findLatestEventsByReturnIdsAndEventTypes(missingStatusReturnIds, eventTypes);

            Map<Integer, String> returnIdToStatusFromEvents = latestEvents.stream()
                    .collect(Collectors.toMap(
                            ReturnEvent::getReturnId,
                            event -> ReturnStatus.valueOf(event.getEvent().toUpperCase()).getStatus()
                    ));

            result.putAll(returnIdToStatusFromEvents);
        }

        return result;
    }


    @Override
    public ReturnEvent getReturnOrderEvent(Integer returnId) {
        if (returnId == null) {
            return null;
        }

        List<String> eventTypes = Arrays.stream(ReturnStatus.values()).map(ReturnStatus::getName).toList();

        return returnEventRepository.findTopByReturnIdAndEventInOrderByIdDesc(returnId, eventTypes);
    }

    @Override
    public Optional<ReturnDetail> getExistingReturnDetailsByItem(Integer uwItemId) {

        ReturnDetailItem returnOrderItem = findTopReturnOrderItemByUwItemId(uwItemId);
        if (null != returnOrderItem) {
            return findReturnOrderById(returnOrderItem.getReturnId());
        }

        return Optional.empty();
    }

    @Override
    public Optional<ReturnDetail> getValidReturnDetailsByItem(Integer uwItemId) {

        ReturnDetailItem returnOrderItem = findEligibilityClientsTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
        if (null != returnOrderItem) {
            Optional<ReturnDetail> returnOrder = findReturnOrderById(returnOrderItem.getReturnId());
            if (returnOrder.isPresent() && isExisitingReturnNotRejectedCancelledReshiped(returnOrder.get())) {
                return returnOrder;
            }
        }

        return Optional.empty();
    }

    @Override
    public String getReturnOrderStatusById(Integer returnId) {
        Optional<ReturnDetail> returnDetail = returnDetailRepository.findById(returnId);
        String status = "";
        if(returnDetail.isPresent()){
            status = returnDetail.map(this::getReturnOrderStatus).orElse(null);
        }else{
            Optional<ReturnOrder> returnOrder = returnOrderRepository.findById(returnId);
            if(returnOrder.isPresent()){
                status = returnOrder.get().getStatus();
            }
        }
        return status;
    }

    @Override
    public void setDraftStatusAndRefundMethodCaptured(UwOrderDTO uwOrder, ReturnRefundResponseDTO refundResponseDTO) {
        ReturnDetailItem returnOrderItem = findTopReturnOrderItemByUwItemId(uwOrder.getUwItemId());
        if (null != returnOrderItem) {
            Optional<ReturnDetail> returnOrderOptional = findReturnOrderById(returnOrderItem.getReturnId());
            ReturnDetail returnOrder = returnOrderOptional.orElse(null);
            if (null != returnOrder) {
                String status = getReturnOrderStatus(returnOrder);
                Set<String> expectedStatuses = new HashSet<>(Arrays.asList(
                        ReturnStatus.NEW_REVERSE_PICKUP.getStatus(),
                        ReturnStatus.RETURN_EXPECTED_POS.getStatus(),
                        ReturnStatus.RETURN_EXPECTED_WH.getStatus(),
                        ReturnStatus.REFERENCE_ID_ISSUED.getStatus(),
                        ReturnStatus.RETURN_ACCEPTED.getStatus(),
                        ReturnStatus.AWB_ASSIGNED.getStatus()
                ));
                boolean isStatusValid = expectedStatuses.contains(status);
                refundResponseDTO.setDraftStatus(isStatusValid);

                if (!newReturnStatus.contains(status)) {
                    String refundMethod = null;
                    Date refundIntentCreatedAt = null;
                    CheckRefundInitiatedRequest checkRefundInitiatedRequest = new CheckRefundInitiatedRequest();
                    checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(returnOrderItem.getReturnId()));
                    checkRefundInitiatedRequest.setIdentifierType(IdentifierType.RETURN_ID);
                    Map<String, Object> headers = authorizationService.addRefundAuthorizationHeaders(checkRefundInitiatedRequest, new HashMap<>());
                    ResponseEntity<CheckRefundInitiatedResponse> checkRefundInitiatedResponseResponseEntity = refundFeignClient.checkRefundInitiated(checkRefundInitiatedRequest, headers);
                    if (checkRefundInitiatedResponseResponseEntity.getStatusCode().is2xxSuccessful() && checkRefundInitiatedResponseResponseEntity.getBody() != null) {
                        if (checkRefundInitiatedResponseResponseEntity.getBody().getRefundRequestDTO() != null) {
                            if (checkRefundInitiatedResponseResponseEntity.getBody().getRefundRequestDTO().getRefundTarget() != null) {
                                refundMethod = checkRefundInitiatedResponseResponseEntity.getBody().getRefundRequestDTO().getRefundTarget().getCode();
                            }
                            refundIntentCreatedAt = checkRefundInitiatedResponseResponseEntity.getBody().getRefundRequestDTO().getCreatedAt();
                            log.info("[setDraftStatusAndRefundMethodCaptured] refund Method from refund service : {}", refundMethod);
                        }
                    }
                    if (refundMethod == null) {
                        ResponseEntity<RefundIntentDTO> refundMethodIntentResponse = orderOpsFeignClient.getRefundMethodIntentByCustomer(uwOrder.getUwItemId(), returnOrder.getId());
                        if (refundMethodIntentResponse.getStatusCode().is2xxSuccessful() && refundMethodIntentResponse.getBody() != null) {
                            refundMethod = refundMethodIntentResponse.getBody().getRefundIntent();
                            refundIntentCreatedAt = refundMethodIntentResponse.getBody().getCreatedAt();
                        }
                        log.info("[setDraftStatusAndRefundMethodCaptured] refund Method from order-ops service : {}", refundMethod);
                    }
                    refundResponseDTO.setRefundMethodRequest(refundMethod);
                    refundResponseDTO.setRefundIntentCreatedAt(refundIntentCreatedAt);
                }
            } else {
                refundResponseDTO.setDraftStatus(true);
            }
        } else {
            refundResponseDTO.setDraftStatus(true);
        }
    }

    @Override
    public void setReturnRefundabilityBasedOnExchangeDone(UwOrderDTO uwOrder, OrderExchangeCancellationDetails orderExchangeCancellationDetails, ReturnRefundResponseDTO refundResponseDTO) {
        log.info("[setReturnRefundabilityBasedOnExchangeDone] refundResponseDTO  : {}", refundResponseDTO);
        final List<ExchangeOrdersDTO> exchangeOrdersDTOs = orderExchangeCancellationDetails.getExchangeOrdersDTOList()
                .stream()
                .filter(exchg -> Objects.equals(exchg.getUwItemId(), uwOrder.getUwItemId()))
                .toList();
        final List<CanceledOrdersDTO> canceledOrdersDTOs = orderExchangeCancellationDetails.getCanceledOrdersDTOList();


//        boolean isExchangeCreatedAndCanceled = getIsExchangeCreatedAndCanceled(exchangeOrdersDTOs, canceledOrdersDTOs);
//        boolean isExchangeCreated = getIsExchangeCreated(exchangeOrdersDTOs);

        boolean isExchangeCreatedAndCanceled = false;
        boolean isExchangeCreated = getIsExchangeCreated(exchangeOrdersDTOs);


        if(!CollectionUtils.isEmpty(canceledOrdersDTOs)){
            for(CanceledOrdersDTO canceledOrdersDTO : canceledOrdersDTOs){
                Optional<ExchangeOrdersDTO> exchangeOrdersDTO = orderExchangeCancellationDetails.getExchangeOrdersDTOList()
                        .stream()
                        .filter(exchg -> Objects.equals(exchg.getUwItemId(), uwOrder.getUwItemId()))
                        .findAny();
                if(exchangeOrdersDTO.isPresent() && Objects.equals(exchangeOrdersDTO.get().getExchangeIncrementId(), canceledOrdersDTO.getIncrementId())){
                    isExchangeCreatedAndCanceled = true;
                }
            }
        }

        log.info("[setReturnRefundabilityBasedOnExchangeDone] isExchangeCreatedAndCanceled: {}, isExchangeCreated: {}, refundMethods: {}", isExchangeCreatedAndCanceled, isExchangeCreated, refundResponseDTO.getRefundMethods());
        if (isExchangeCreatedAndCanceled) {
            refundResponseDTO.setExchange(true);
            refundResponseDTO.setDoRefund(true);
            refundResponseDTO.setExchangeCreatedAndCancelled(true);
        } else if (isExchangeCreated) {
            refundResponseDTO.setExchange(false);
            refundResponseDTO.setDoRefund(false);
            refundResponseDTO.setAmountToRefund(0.0);
            refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.ITEM_NOT_EXCHANGEABLE_EXCHANGE_ALREADY_CREATED);
            refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_EXCHANGE_ALREADY_CREATED);
        } else {
            if (!CollectionUtils.isEmpty(refundResponseDTO.getRefundMethods()) && refundResponseDTO.getRefundMethods().contains("exchange")) {
                refundResponseDTO.setExchange(true);
                refundResponseDTO.getRefundMethods().remove("exchange");
            } else {
                refundResponseDTO.setExchange(false);
                refundResponseDTO.setErrorForNotExchangeable(Constant.RefundEligibilityError.ITEM_NOT_EXCHANGEABLE_AS_REFUND_METHOD_NOT_GIVEN);
            }
        }

        List<String> refundMethods = refundResponseDTO.getRefundMethods();
        log.info("[setReturnRefundabilityBasedOnExchangeDone] refundMethods: {}", refundMethods);
        if (CollectionUtils.isEmpty(refundMethods) ||
                (refundMethods.size() == 1 && "NA".equalsIgnoreCase(refundMethods.iterator().next()))) {
            log.info("setting doRefund as false as refundMethods is null or empty OR doRefund flag is false");

            refundResponseDTO.setDoRefund(false);
            refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_AS_REFUND_METHOD_NOT_GIVEN);
        }else{
//            List<String> refundMethods = refundResponseDTO.getRefundMethods();
            Set<String> remainingRefundMethods = refundMethods.stream().filter(refundMethod -> !refundMethod.contains("exchange")).collect(Collectors.toSet());
            log.info("[setReturnRefundabilityBasedOnExchangeDone] remainingRefundMethods : {}",remainingRefundMethods);
            if(remainingRefundMethods.isEmpty()){
                log.info("setting doRefund as false as refundMethods doesn't contain any valid refund method");
                refundResponseDTO.setDoRefund(false);
                refundResponseDTO.setErrorForNotRefundable(Constant.RefundEligibilityError.ITEM_NOT_REFUNDABLE_AS_NO_VALID_REFUND_METHOD_FOUND);
            }
        }
    }

    @Override
    public ReturnDetail getReturnOrder(Integer uwItemId) {
        ReturnDetailItem returnDetailItem = findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
        if(returnDetailItem != null){
            return findReturnOrderById(returnDetailItem.getReturnId()).orElse(null);
        }
        return null;
    }

    @Override
    public ReturnDetailItem findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId) {
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
        if (returnDetailItem == null) {
            returnDetailItem = convertToReturnDetailItem(returnOrderItemRepository.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId));
            if(returnDetailItem == null){
                com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory(Constant.IDENTIFIER_TYPE.UWITEM_ID, String.valueOf(uwItemId));
                returnDetailItem = convertToReturnDetailItem(returnOrderDTO);
            }
        }
        return returnDetailItem;
    }

    @Override
    public ReturnDetailItem findTop1ByUwItemIdOrderByReturnCreateDatetimeDescNonInventory(Integer uwItemId) {
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
        if (returnDetailItem == null) {
            returnDetailItem = convertToReturnDetailItem(returnOrderItemRepository.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId));
        }
        return returnDetailItem;
    }

    @Override
    public ReturnDetailItem findEligibilityClientsTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId) {
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
        if (returnDetailItem == null) {
            returnDetailItem = convertToReturnDetailItem(returnOrderItemRepository.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId));
        }
        return returnDetailItem;
    }

    private ReturnDetailItem convertToReturnDetailItem(com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO) {
        if(returnOrderDTO == null){
            return null;
        }
        ReturnDetailItem returnDetailItem = new ReturnDetailItem();
        if(returnOrderDTO.getReturnId() != null){
            returnDetailItem.setReturnId(Math.toIntExact(returnOrderDTO.getReturnId()));
        }
        if(returnOrderDTO.getUwItemId() != null) {
            returnDetailItem.setUwItemId(Math.toIntExact(returnOrderDTO.getUwItemId()));
        }
        returnDetailItem.setStatus(returnOrderDTO.getReturnStatus());
        if(returnOrderDTO.getProductId() != null){
            returnDetailItem.setProductId(Long.valueOf(returnOrderDTO.getProductId()));
        }
        log.info("[convertToReturnDetailItem] returnDetailItem : {}", returnDetailItem);
        return returnDetailItem;
    }

    @Override
    public List<ReturnDetailItem> findByGroupIdOrderByReturnCreateDatetimeDesc(Long groupId) {
        List<ReturnDetailItem> returnDetailItemList = returnDetailItemRepository.findByGroupIdIn(Collections.singletonList(groupId));
        if (CollectionUtils.isEmpty(returnDetailItemList)) {
            List<ReturnOrderItem> returnOrderItems = returnOrderItemRepository.findByGroupIdIn(Collections.singletonList(groupId));
            if(CollectionUtils.isEmpty(returnOrderItems)){
                com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory(Constant.IDENTIFIER_TYPE.GROUP_ID, String.valueOf(groupId));
                if(returnOrderDTO != null){
                    returnDetailItemList = Collections.singletonList(convertToReturnDetailItem(returnOrderDTO));
                }
            }else{
                for(ReturnOrderItem returnOrderItem : returnOrderItems){
                    ReturnDetailItem returnDetailItem = convertToReturnDetailItem(returnOrderItem);
                    returnDetailItemList.add(returnDetailItem);
                }
            }
        }
        return returnDetailItemList;
    }

    private ReturnDetailItem convertToReturnDetailItem(ReturnOrderItem returnOrderItem) {
        log.info("[convertToReturnDetailItem] returnOrderItem : {}", returnOrderItem);
        if (returnOrderItem == null) {
            return null;
        }

        ReturnDetailItem returnDetailItem = new ReturnDetailItem();
        returnDetailItem.setReturnId(returnOrderItem.getReturnId());
        returnDetailItem.setUwItemId(returnOrderItem.getUwItemId());
        returnDetailItem.setItemId(returnOrderItem.getItemId());
        returnDetailItem.setReturnCreateDatetime(returnOrderItem.getReturnCreateDatetime());
        returnDetailItem.setQcStatus(returnOrderItem.getQcStatus());
        returnDetailItem.setQcComment(returnOrderItem.getQcComment());
        returnDetailItem.setQcFailReason(returnOrderItem.getQcFailReason());
        returnDetailItem.setReasonForReturn(returnOrderItem.getReasonForReturn());
        returnDetailItem.setProductId(Long.valueOf(returnOrderItem.getProductId()));
        returnDetailItem.setStatus(returnOrderItem.getStatus());
        returnDetailItem.setId(returnOrderItem.getId());
        returnDetailItem.setClassification(returnOrderItem.getClassification());
        returnDetailItem.setIsFranchise(returnOrderItem.getIsFranchise());
        returnDetailItem.setMethod(returnOrderItem.getMethod());
        returnDetailItem.setProductDeliveryType(returnOrderItem.getProductDeliveryType());
        returnDetailItem.setChannel(returnOrderItem.getChannel());
        returnDetailItem.setCsohUpdatedFlag(returnOrderItem.getCsohUpdatedFlag());
        returnDetailItem.setTbybPrescription(returnOrderItem.getTbybPrescription());
        returnDetailItem.setItemSelectedFlag(returnOrderItem.getItemSelectedFlag());
        returnDetailItem.setUpdatedAt(returnOrderItem.getActionDatetime());
        return returnDetailItem;
    }

    @Override
    public ReturnDetail findByReturnIdAndStatus(Integer returnId, String status) {
        Optional<ReturnDetail> returnDetailOpt = returnDetailRepository.findById(returnId);
        if(returnDetailOpt.isPresent()){
            ReturnDetail returnDetail = returnDetailOpt.get();
            String returnOrderStatus = getReturnOrderStatus(returnDetail);
            if(status!=null && status.equalsIgnoreCase(returnOrderStatus)) return returnDetail;
        }
        return null;
    }

    @Override
    public ReshipItemDetail findByReturnId(Integer returnId) {
        return null;
    }

    private boolean getIsExchangeCreatedAndCanceled(List<ExchangeOrdersDTO> exchangeOrdersDTOS, List<CanceledOrdersDTO> canceledOrdersDTOs) {
        return !CollectionUtils.isEmpty(exchangeOrdersDTOS) && !CollectionUtils.isEmpty(canceledOrdersDTOs);
    }

    private boolean getIsExchangeCreated(List<ExchangeOrdersDTO> exchangeOrdersDTOS) {
        return !CollectionUtils.isEmpty(exchangeOrdersDTOS);
    }

    @Override
    public boolean containsInReturnStatusList(String returnOrderStatus) {
        return returnStatusList.contains(returnOrderStatus);
    }

    @Override
    public List<ReturnDetail> fetchReturnOrdersOfLastTwoMonths(Long pickupId) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -2);
        List<ReturnDetail> byGroupIdAndDate = returnDetailRepository.findByGroupIdAndDate(pickupId, cal.getTime());
        if (CollectionUtils.isEmpty(byGroupIdAndDate)) {
            return returnOrderRepository.findBygroupIdAndDate(pickupId, cal.getTime()).stream().map(this::convertToReturnDetail).toList();
        }
        return byGroupIdAndDate;
    }

    @Override
    public ReturnDetailItem findTopByReturnId(Integer returnId) {
        log.info("[findTopByReturnId] returnId : {}", returnId);
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTopByReturnId(returnId);
        log.info("[findTopByReturnId] returnId : {}, returnFromNewService : {}", returnId, returnDetailItem != null);
        if (returnDetailItem == null) {
            return convertToReturnDetailItem(returnOrderItemRepository.findTopByReturnId(returnId));
        }
        return returnDetailItem;
    }

    @Override
    public ReturnDetailItem findTopByNewReturnId(Integer returnId) {
        log.info("[findTopByNewReturnId] returnId : {}", returnId);
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTopByReturnId(returnId);
        log.info("[findTopByNewReturnId] returnId : {}, returnFromNewService : {}", returnId, returnDetailItem != null);
        return returnDetailItem;
    }

    @Override
    public Optional<ReturnDetail> findReturnOrderById(Integer id) {
        Optional<ReturnDetail> returnDetail = returnDetailRepository.findById(id);
        if (returnDetail.isEmpty()) {
            ReturnOrder returnOrder = returnOrderRepository.findByReturnId(id);
            log.info("[findReturnOrderById] returnOrder : {}", returnOrder);
            if(returnOrder == null){
                com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderInventoryDTO = getReturnFromInventory("RETURN_ID", String.valueOf(id));
                if(returnOrderInventoryDTO != null){
                    returnOrder = convertToReturnOrder(returnOrderInventoryDTO);
                }
            }
            if (returnOrder != null) {
                return Optional.of(convertToReturnDetail(returnOrder));
            }
        }
        return returnDetail;
    }

    @Override
    public Optional<ReturnDetail> findReturnOrderByIdNonInventory(Integer id) {
        Optional<ReturnDetail> returnDetail = returnDetailRepository.findById(id);
        if (returnDetail.isEmpty()) {
            ReturnOrder returnOrder = returnOrderRepository.findByReturnId(id);
            log.info("[findReturnOrderById] returnOrder : {}", returnOrder);
            if (returnOrder != null) {
                return Optional.of(convertToReturnDetail(returnOrder));
            }
        }
        return returnDetail;
    }

    private ReturnOrder convertToReturnOrder(com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderInventoryDTO) {
        if(returnOrderInventoryDTO == null){
            return null;
        }
        ReturnOrder returnOrder = new ReturnOrder();
        returnOrder.setReturnId(Math.toIntExact(returnOrderInventoryDTO.getReturnId()));
        returnOrder.setGroupId(Long.valueOf(returnOrderInventoryDTO.getGroupId()));
        returnOrder.setSource(returnOrderInventoryDTO.getSource());
        returnOrder.setStatus(returnOrderInventoryDTO.getReturnStatus());
        returnOrder.setReturnType(returnOrderInventoryDTO.getReturnType());
        returnOrder.setReturnMethod(returnOrderInventoryDTO.getReturnMethod());
        returnOrder.setReceivingFlag(String.valueOf(returnOrderInventoryDTO.getReceivingFlag()));
        returnOrder.setFacilityCode(returnOrderInventoryDTO.getFacilityCode());
        returnOrder.setReturnCreateDatetime(returnOrderInventoryDTO.getReturnCreatedAt());
        returnOrder.setIsInsurance(returnOrderInventoryDTO.isInsuranceClaimed());
        returnOrder.setReverseAwb(returnOrderInventoryDTO.getReverseAwbNo());
        returnOrder.setOrderNo(returnOrderInventoryDTO.getOrderNo());

        return returnOrder;
    }

    private ReturnDetail convertToReturnDetail(ReturnOrder returnOrder) {
        if (returnOrder == null) {
            return null;
        }

        ReturnDetail returnDetail = new ReturnDetail();
        returnDetail.setId(returnOrder.getReturnId());
        returnDetail.setAgentEmail(returnOrder.getAgentEmail());
        returnDetail.setReturnMethod(returnOrder.getReturnMethod());
        returnDetail.setReturnType(returnOrder.getReturnType());
        returnDetail.setReturnCreateDatetime(returnOrder.getReturnCreateDatetime());
        returnDetail.setIncrementId(returnOrder.getOrderNo());
        returnDetail.setSource(returnOrder.getSource());
        returnDetail.setGroupId(returnOrder.getGroupId());
        returnDetail.setUnicomOrderCode(returnOrder.getUnicomOrderCode());
        returnDetail.setFacilityCode(returnOrder.getFacilityCode());
        returnDetail.setIsAutoCancelEnable(returnOrder.getIsAutoCancelEnable());
        returnDetail.setIsInsurance(returnOrder.getIsInsurance());
        returnDetail.setIsQcAtDoorstep(returnOrder.getIsQcAtDoorstep());
        returnDetail.setReceivingFlag(returnOrder.getReceivingFlag());
        returnDetail.setBulkType(returnOrder.getBulkType());
        returnDetail.setLastFollowupDatetime(returnOrder.getLastFollowupDatetime());
        returnDetail.setReversePuFollowupCnt(returnOrder.getReversePuFollowupCnt());
        returnDetail.setStatus(returnOrder.getStatus());
        returnDetail.setReverseCourier(returnOrder.getReverseCourier());
        returnDetail.setLastUpdateDatetime(returnOrder.getLastUpdateDatetime());
        returnDetail.setOverrideComment(returnOrder.getOverrideComment());

        return returnDetail;
    }

    private ReturnDetail convertToReturnDetail(com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrder) {
        if (returnOrder == null) {
            return null;
        }

        ReturnDetail returnDetail = new ReturnDetail();
        returnDetail.setId(Math.toIntExact(returnOrder.getReturnId()));
        returnDetail.setReturnMethod(returnOrder.getReturnMethod());
        returnDetail.setReturnType(returnOrder.getReturnType());
        returnDetail.setReturnCreateDatetime(returnOrder.getReturnCreatedAt());
        returnDetail.setIncrementId(returnOrder.getOrderNo());
        returnDetail.setSource(returnOrder.getSource());
        returnDetail.setGroupId(Long.valueOf(returnOrder.getGroupId()));
        returnDetail.setFacilityCode(returnOrder.getFacilityCode());
        returnDetail.setIsInsurance(returnOrder.isInsuranceClaimed());
        returnDetail.setReceivingFlag(String.valueOf(returnOrder.getReceivingFlag()));
        returnDetail.setStatus(returnOrder.getReturnStatus());
        return returnDetail;
    }

    @Override
    public ReturnDetailItem findTopReturnOrderItemByUwItemId(Integer uwItemId) {
        return findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
    }

    @Override
    public List<ReturnDetailItem> findReturnOrderItemByUwItemId(Integer uwItemId) {
        return returnDetailItemRepository.findByUwItemId(uwItemId);
    }


    @Override
    public List<ReturnDetail> findAllByIncrementId(Integer incrementId) {
        List<ReturnDetail> byIncrementId = returnDetailRepository.findByIncrementId(incrementId);
        if (CollectionUtils.isEmpty(byIncrementId)) {
            return returnOrderRepository.findByOrderNo(incrementId).stream().map(this::convertToReturnDetail).toList();
        }
        return byIncrementId;
    }

    @Override
    public List<ReturnDetail> getReturnOrders(List<Integer> orderIds, boolean callInventory) {
        List<ReturnDetail> returnOrders = returnDetailRepository.getReturnOrders(orderIds);
        if (CollectionUtils.isEmpty(returnOrders)) {
            returnOrders = returnOrderRepository.findByOrderNoIn(orderIds).stream().map(this::convertToReturnDetail).toList();
            if(CollectionUtils.isEmpty(returnOrders) && callInventory){
                returnOrders = new ArrayList<>();
                for(Integer orderId : orderIds){
                    com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory("ORDER_ID", String.valueOf(orderId));
                    if(returnOrderDTO != null){
                        returnOrders.add(convertToReturnDetail(returnOrderDTO));
                    }
                }
            }
        }
        return returnOrders;
    }

    @Override
    public ReturnCourierDetail findTop1ByReturnId(Integer returnId) {
        log.info("[findTop1ByReturnId] returnId : {}", returnId);
        ReturnCourierDetail returnCourierDetail = returnCourierDetailRepository.findTopByReturnId(returnId);
        if (returnCourierDetail == null) {
            return convertToReturnCourienrDetail(returnOrderRepository.findByReturnId(returnId));
        }
        return returnCourierDetail;
    }

    private ReturnCourierDetail convertToReturnCourienrDetail(ReturnOrder returnOrder) {
        if (returnOrder == null) {
            return null;
        }
        ReturnCourierDetail returnCourierDetail = new ReturnCourierDetail();
        returnCourierDetail.setCreatedAt(returnOrder.getReturnCreateDatetime());
        returnCourierDetail.setReverseCourier(returnOrder.getReverseCourier());
        returnCourierDetail.setReverseAwbNumber(returnOrder.getReverseAwb());
        returnCourierDetail.setReversePickupReferenceId(returnOrder.getReversePickupReferenceId());
        returnCourierDetail.setReturnId(returnOrder.getReturnId());
        returnCourierDetail.setPickupEstimate(returnOrder.getPickupEstimate());
        return returnCourierDetail;
    }

    @Override
    public CreateUpdateReturnOrderResponseDTO createAwaitedRtoReturnOrder(ReturnRequest returnRequest, ReturnOrderRequestDTO returnOrderRequest, String status, String returnType, UwOrderDTO uwOrderDTO) {
        CreateUpdateReturnOrderResponseDTO response = new CreateUpdateReturnOrderResponseDTO();
        try {
            ReturnDetail returnOrder = null;
            log.info("[ReturnOrderActionServiceImpl][createUpdateReturnOrder]:: returnOrderRequest is: {}", returnOrderRequest);
            returnOrder = new ReturnDetail();
            returnOrder.setRequestId(returnRequest.getId());
            returnOrder.setReturnCreateDatetime(new Date());
            returnOrder.setIncrementId(returnRequest.getIncrementId());
            returnOrder.setReturnType(returnType);
            returnOrder.setAgentEmail(returnRequest.getAgentId() != null ? returnRequest.getAgentId() : "Unassigned");
            returnOrder.setReceivingFlag(Constant.RECEIVING_FLAG.NO);
            if (!StringUtils.isEmpty(returnRequest.getSource())) {
                returnOrder.setSource(returnRequest.getSource());
            }
            returnOrder.setGroupId(returnOrderRequest.getGroupId());
            returnOrder.setUnicomOrderCode(returnOrderRequest.getReferenceOrderCode());
            returnOrder = returnDetailRepository.save(returnOrder);
            returnEventService.persistEvent(returnRequest.getId(), returnOrder.getId(), RETURN_INITIATED, "awaited-rto of uw_item_id " + uwOrderDTO.getUwItemId());
            response.setReturnId(returnOrder.getId());
            response.setReturnStatus(getReturnOrderStatus(returnOrder));
        } catch (Exception exception) {
            log.error("[createAwaitedRtoReturnOrder] exception : " + exception);
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUpdateReturnOrderResponseDTO createUpdateReturnOrder(ReturnOrderRequestDTO returnRequest, Integer returnId, String status, String returnType, String returnOrderOriginalStatus, Integer requestId) {
        CreateUpdateReturnOrderResponseDTO response = new CreateUpdateReturnOrderResponseDTO();
        try {
            log.info("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}", gson.toJson(returnRequest));
            log.info("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}", returnId);
            StringBuilder statusBuilder = new StringBuilder(status);

            ReturnDetail returnOrder = getOrCreateReturnOrder(returnId);
            updateReturnOrderDetails(returnOrder, returnRequest, returnType, statusBuilder, requestId);

            returnOrder = returnDetailRepository.save(returnOrder);
            response.setReturnId(returnOrder.getId());
            response.setReturnStatus(getReturnOrderStatus(returnOrder));

            //persistReturnEvent(returnOrder.getRequestId(), returnOrder.getId(), statusBuilder);

            return response;
        } catch (Exception e) {
            log.error("[ReturnOrderServiceImpl][createUpdateReturnOrder] Exception Occurred ", e);
            return response;
        }
    }

    private void persistReturnEvent(Integer requestId, Integer returnId, StringBuilder status) {
        returnEventService.createReturnEvent(requestId, returnId, status.toString(), " ");
    }

    private ReturnDetail getOrCreateReturnOrder(Integer returnId) {
        if (returnId != null) {
            return findReturnOrderById(returnId)
                    .orElseGet(() -> {
                        log.info("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: No ReturnOrder found for returnId: {}", returnId);
                        return createNewReturnOrder();
                    });
        } else {
            return createNewReturnOrder();
        }
    }

    private ReturnDetail createNewReturnOrder() {
        ReturnDetail returnOrder = new ReturnDetail();
        returnOrder.setReturnCreateDatetime(new Date());
        ReturnGroup returnGroup = new ReturnGroup();
        long groupId = returnGroupRepository.save(returnGroup).getId();
        returnOrder.setGroupId(groupId);
        return returnOrder;
    }

    private void updateReturnOrderDetails(ReturnDetail returnOrder, ReturnOrderRequestDTO returnRequest, String returnType, StringBuilder status, Integer request) {
        String returnOrderStatus = getReturnOrderStatus(returnOrder);

        if (shouldUpdateStatusToRefunded(returnOrderStatus, returnType)) {
            log.info("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: status is : {}", returnOrderStatus);
            status.replace(0, status.length(), Constant.RETURN_STATUS.RETURN_REFUNDED);
            log.info("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: status updated to: {}", status);
        }

        returnOrder.setReturnType(returnType);
        returnOrder.setReceivingFlag(YES);
        returnOrder.setRequestId(request);
        returnOrder.setIncrementId(returnRequest.getIncrementId());
        if (!StringUtils.isEmpty(returnRequest.getSource())) {
            returnOrder.setSource(returnRequest.getSource());
        }

        returnOrder.setUnicomOrderCode(returnRequest.getReferenceOrderCode());
        log.info("[setReturnGroupId] order: {} , groupId : {}", returnOrder.getIncrementId(), returnOrder.getGroupId());
    }

    private boolean shouldUpdateStatusToRefunded(String returnOrderStatus, String returnType) {
        return (Constant.RETURN_STATUS.RETURN_REFUNDED_RECEIVING_PENDING.equalsIgnoreCase(returnOrderStatus)
                || Constant.RETURN_STATUS.RETURN_REFUNDED.equalsIgnoreCase(returnOrderStatus)
                || Constant.RETURN_STATUS.RETURN_RECEIVED_ACTION_PENDING.equalsIgnoreCase(returnOrderStatus))
                && REVERSE.equalsIgnoreCase(returnType);
    }

    @Override
    public void createReturnItem(ReturnItemRequestDTO returnItemRequest, UwOrderDTO uwOrder, Integer returnId, String returnType, Integer requestId) throws ReturnRequestException {
        try {
            log.info("[createReturnItem] uwOrder : {}, returnItemRequest : {}",uwOrder, returnItemRequest);
            ReturnDetail returnOrder = new ReturnDetail();
            ReturnDetailItem returnOrderItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(uwOrder.getUwItemId());
            if (returnOrderItem == null) {
                returnOrderItem = new ReturnDetailItem();
                returnOrderItem.setReturnId(returnId);
                returnOrderItem.setReturnCreateDatetime(new Date());
            }

            returnOrderItem.setItemId(uwOrder.getItemId());
            returnOrderItem.setUwItemId(returnItemRequest.getItemId());
            returnOrderItem.setProductId((long) uwOrder.getProductId());
            returnOrderItem.setQcStatus(returnItemRequest.getQcStatus());
            returnOrderItem.setProductDeliveryType(uwOrder.getProductDeliveryType());
            returnOrderItem.setClassification(uwOrder.getClassification() != null ? Integer.parseInt(uwOrder.getClassification()) : 0);
            returnOrderItem.setQcFailReason(returnItemRequest.getQcFailReason());
            returnOrderItem.setReasonForReturn(returnItemRequest.getReasonDetail()==null?"":returnItemRequest.getReasonDetail());
            returnOrderItem.setCsohUpdatedFlag(0);
            returnOrderItem.setItemSelectedFlag(0);
            returnOrderItem.setIsFranchise(uwOrder.getIsFranchise());
            returnOrderItem.setMethod(uwOrder.getMethod());
            returnOrderItem.setChannel(uwOrder.getChannel());
            log.info("createReturnItem returnOrderItem : {}",returnOrderItem);
            Optional<ReturnDetail> optionalReturnOrder = findReturnOrderById(returnId);
            if (optionalReturnOrder.isPresent()) returnOrder = optionalReturnOrder.get();

            returnOrder.setReturnType(returnType);
            returnOrder = returnDetailRepository.save(returnOrder);

            if (returnItemRequest.getQcStatus() != null && ReturnItemRequest.QC_STATUS.FAIL.equalsIgnoreCase(returnItemRequest.getQcStatus())) {
                returnOrderItem.setQcStatus(ReturnItemRequest.QC_STATUS.FAIL);
            }

            returnOrderItem.setStatus(INITIATED_STOCKIN);
            //returnEventService.createReturnEvent(requestId, returnOrder.getId(), INITIATED_STOCKIN, returnItemRequest.getQcFailReason());
            returnDetailItemRepository.save(returnOrderItem);
        } catch (Exception e) {
            log.error("[createReturnItem] exception : {}",e.getMessage());
            throw new ReturnRequestException("Unable to Save data in ReturnOrderItem");
        }
    }

    private Integer getRequestIdFromReturnId(Integer returnId) {
        return returnEventRepository.findByReturnId(returnId).stream().map(ReturnEvent::getReturnRequestId).filter(Objects::nonNull).findAny().orElse(null);
    }

    @Override
    public void updateReturnCourierDetailByIncrementIdAndGroupId(String status, String waybill, Long groupId, Integer incrementId, Integer qcType, ReverseCourierDetail reassignedCourier) {
        List<ReturnDetail> returnOrders = findAllByGroupIdAndIncrementId(groupId, incrementId);
        if (!CollectionUtils.isEmpty(returnOrders)) {
            for (ReturnDetail returnOrder : returnOrders) {
                returnOrder.setIsQcAtDoorstep(qcType); //try before consumer
                returnDetailRepository.save(returnOrder);

                ReturnCourierDetail returnCourierDetail;
                if (reassignedCourier != null) {
                    returnCourierDetail = returnCourierDetailRepository.findTopByReturnIdAndReverseCourierOrderByIdDesc(returnOrder.getId(), reassignedCourier.getCourier());
                    if (returnCourierDetail == null) {
                        returnCourierDetail = new ReturnCourierDetail();
                        returnCourierDetail.setCreatedAt(new Date());
                    }
                    returnCourierDetail.setReverseCourier(reassignedCourier.getCourier());
                } else {
                    returnCourierDetail = findTopByReturnIdOrderByIdDesc(returnOrder.getId());
                }
                if (returnCourierDetail != null) {
                    returnCourierDetail.setReturnId(returnOrder.getId());
                    returnCourierDetail.setReversePickupReferenceId(waybill);
                    returnCourierDetailRepository.save(returnCourierDetail);
                }
            }
        }
    }

    @Override
    public List<ReturnDetail> findAllByGroupIdAndIncrementId(long groupId, Integer incrementId) {
        List<ReturnDetail> allByGroupIdAndIncrementId = returnDetailRepository.findAllByGroupIdAndIncrementId(groupId, incrementId);
        if (CollectionUtils.isEmpty(allByGroupIdAndIncrementId)) {
            return returnOrderRepository.findAllByGroupIdAndOrderNo(groupId, incrementId).stream().map(this::convertToReturnDetail).toList();
        }
        return allByGroupIdAndIncrementId;
    }

    @Override
    public CreateUpdateReturnOrderResponseDTO createUpdateReturnOrder(ReturnOrderRequestDTO returnRequest, Integer returnId, String status, String returnType, Integer requestId, UwOrderDTO uwOrderDTO, String receiving_flag) throws ReturnRequestException {
        CreateUpdateReturnOrderResponseDTO response = new CreateUpdateReturnOrderResponseDTO();
        try {
            ReturnDetail returnOrder = null;
            String existingStatus = null;
            log.info("[ReturnOrderActionServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}, returnId is : {}", returnRequest, returnId);
            if (returnId != null) {
                returnOrder = findReturnOrderById(returnId).orElse(null);
                existingStatus = getReturnOrderStatus(returnOrder);
            } else {
                returnOrder = new ReturnDetail();
                returnOrder.setReturnCreateDatetime(new Date());
                ReturnGroup returnGroup = new ReturnGroup();
                long groupId = returnGroupRepository.save(returnGroup).getId();
                returnOrder.setGroupId(groupId);
                returnOrder.setReturnMethod("DirectReceiving");
            }
            returnOrder.setRequestId(requestId);
            returnOrder.setIncrementId(returnRequest.getIncrementId());
            returnOrder.setReturnType(returnType);
            returnOrder.setReceivingFlag(receiving_flag);
            if (!StringUtils.isEmpty(returnRequest.getSource())) {
                returnOrder.setSource(returnRequest.getSource());
            }
            returnOrder.setGroupId(returnRequest.getGroupId());
            returnOrder = returnDetailRepository.save(returnOrder);
            returnEventService.persistEvent(requestId, returnOrder.getId(), RETURN_QUALITY_CHECK_DONE, "");
            returnEventService.persistEvent(requestId, returnOrder.getId(), RETURN_RECEIVED_AT_WAREHOUSE, "");
            kafkaService.pushToKafka("d365_return_tracking_event", String.valueOf(returnOrder.getIncrementId()), new D365ReturnTrackingRequestDTO(returnId, status));
            response.setReturnId(returnOrder.getId());
            response.setReturnStatus(getReturnOrderStatus(returnOrder));
        } catch (Exception exception) {
            log.error("[createAwaitedRtoReturnOrder] exception : " + exception);
            throw new ReturnRequestException(exception.getMessage());
        }
        return response;
    }

    @Override
    public List<ReturnDetail> findAllByGroupIdOrderByReturnCreateDatetimeDesc(long groupId) {
        List<ReturnDetail> allByGroupIdOrderByReturnCreateDatetimeDesc = returnDetailRepository.findAllByGroupIdOrderByReturnCreateDatetimeDesc(groupId);
        if (CollectionUtils.isEmpty(allByGroupIdOrderByReturnCreateDatetimeDesc)) {
            return returnOrderRepository.findAllByGroupIdOrderByReturnCreateDatetimeDesc(groupId).stream().map(this::convertToReturnDetail).toList();
        }
        return allByGroupIdOrderByReturnCreateDatetimeDesc;
    }

    @Override
    public List<ReturnDetail> getReturnsForAutoCancellation(Date startDate, Date endDate, Integer batchSizeForReturnAutoCancelJob) {
        List<ReturnDetail> returnsForAutoCancellation = returnDetailRepository.getReturnsForAutoCancellation(startDate, endDate, batchSizeForReturnAutoCancelJob);
        if (CollectionUtils.isEmpty(returnsForAutoCancellation)) {
            return returnOrderRepository.getReturnsForAutoCancellation(startDate, endDate, batchSizeForReturnAutoCancelJob).stream().map(this::convertToReturnDetail).toList();
        }
        return returnsForAutoCancellation;
    }

    @Override
    public List<ReturnDetail> findByReturnIdIn(List<Integer> returnIds, boolean callInventory) {
        List<ReturnDetail> returnDetailItems = returnDetailRepository.findByReturnIdIn(returnIds);
        if (CollectionUtils.isEmpty(returnDetailItems)) {
            returnDetailItems = returnOrderRepository.findByReturnIdIn(returnIds).stream().map(this::convertToReturnDetail).toList();
            if(CollectionUtils.isEmpty(returnDetailItems) && callInventory){
                returnDetailItems = new ArrayList<>();
                for(Integer returnId : returnIds){
                    com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory("RETURN_ID", String.valueOf(returnId));
                    if(returnOrderDTO != null){
                        returnDetailItems.add(convertToReturnDetail(returnOrderDTO));
                    }
                }
            }
        }
        return returnDetailItems;
    }

    @Override
    public ReturnDetail findTop1ByOrderNoAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(Integer incrementId, String referenceOrderCode, String awaitedRto) {
        ReturnDetail returnDetail = returnDetailRepository.findTop1ByIncrementIdAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(incrementId, referenceOrderCode, awaitedRto);
        if (returnDetail == null) {
            return convertToReturnDetail(returnOrderRepository.findTop1ByOrderNoAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(incrementId, referenceOrderCode, awaitedRto));
        }
        return returnDetail;
    }

    @Override
    public ReturnDetail saveReturnOrder(ReturnDetail returnOrder) {
        return returnDetailRepository.save(returnOrder);
    }

    @Override
    public List<String> findReturnOrderReceivingFlagByByUwItemId(List<Integer> itemIds, List<String> returnItemStatus, String reverse) {
        List<String> returnOrderReceivingFlagByByUwItemId = returnDetailItemRepository.findReturnOrderReceivingFlagByByUwItemId(itemIds, returnItemStatus, reverse);
        if (CollectionUtils.isEmpty(returnOrderReceivingFlagByByUwItemId)) {
            return returnOrderItemRepository.findReturnOrderReceivingFlagByByUwItemId(itemIds, returnItemStatus, reverse);
        }
        return returnOrderReceivingFlagByByUwItemId;
    }

    @Override
    public ReturnDetailItem saveReturnOrderItem(ReturnDetailItem returnOrderItem) {
        return returnDetailItemRepository.save(returnOrderItem);
    }

    @Override
    public Integer findReturnIdByReturnTypeUwItemID(String returnType, Integer itemId) {
        Integer returnIdByReturnTypeUwItemID = returnDetailItemRepository.findReturnIdByReturnTypeUwItemID(returnType, itemId);
        if (returnIdByReturnTypeUwItemID == null) {
            return returnOrderItemRepository.findReturnIdByReturnTypeUwItemID(returnType, itemId);
        }
        return returnIdByReturnTypeUwItemID;
    }

    @Override
    public ReturnDetail findReturnIdByReturnTypeUwItemID(List<String> returnType, Integer itemId) {
        return returnDetailRepository.findReturnIdByReturnTypeInAndUwItemID(returnType, itemId);
    }

    @Override
    public List<ReturnDetailItem> findByUwItemIdInAndStatusInAndReturnType(List<Integer> itemIds, List<String> returnItemStatus, String rto) {
        List<ReturnDetailItem> byUwItemIdInAndStatusInAndReturnType = returnDetailItemRepository.findByUwItemIdInAndStatusInAndReturnType(itemIds, returnItemStatus, rto);
        if (CollectionUtils.isEmpty(byUwItemIdInAndStatusInAndReturnType)) {
            return returnOrderItemRepository.findByUwItemIdInAndStatusInAndReturnType(itemIds, returnItemStatus, rto).stream().map(this::convertToReturnDetailItem).toList();
        }
        return byUwItemIdInAndStatusInAndReturnType;
    }

    @Override
    public Integer getDistinctDatesOfPickupFailed(Integer returnId) {
        List<String> returnStatuses = new ArrayList<>(Arrays.asList("new_reverse_pickup", "reference_id_issued"));
        List<String> pickupFailedStatuses = new ArrayList<>(Arrays.asList("Pickup Failed", "PICKUP FAILED"));
        List<Date> pickupFailedDateList = reverseTrackingEventRepository.findPickupFailedByReturnIdAndReverseMappedStatus(returnStatuses, returnId, pickupFailedStatuses);
        Set<LocalDate> pickupFailedLocalDates = pickupFailedDateList.stream()
                .map(pickupFailedDate -> pickupFailedDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                .collect(Collectors.toSet());
        return pickupFailedLocalDates.size();
    }

    private boolean validateRequest(String identifierType, List<String> identifierValues){
        boolean isRequestValid = true;
        try{
            if (StringUtils.isEmpty(identifierType)) {
                isRequestValid = false;
            }else{
                if(CollectionUtils.isEmpty(identifierValues) || identifierValues.contains("null") || identifierValues.contains(null)){
                    isRequestValid = false;
                } else {
                    try {
                        List<String> modifiableList = new ArrayList<>(identifierValues);
                        // Clean identifier values: remove from first URL-encoded pattern onwards
                        for (int i = 0; i < modifiableList.size(); i++) {
                            String value = modifiableList.get(i);
                            if (value != null) {
                                // Remove anything from the first URL-encoded pattern onward
                                value = value.replaceFirst("%[0-9A-Fa-f]{2}.*", "");
                                modifiableList.set(i, value);
                            }
                        }
                        identifierValues = modifiableList;
                    } catch (Exception e) {
                        log.error("[validateRequest] Exception occurred while identifierValues triming", e);
                        throw e;
                    }
                }
            }
        }catch (Exception exception){
            log.error("[validateRequest] Exception occurred for identifierType:{} identifierValues:{} ", identifierType, identifierValues, exception);
            isRequestValid = false;
        }
        log.info("[validateRequest] Returning with isRequestValid: {} for identifierType: {}, identifierValues: {}", isRequestValid, identifierType, identifierValues);
        return isRequestValid;
    }
    @Override
    public ReturnDetailsDTO getReturnDetailsByIdentifier(String identifierType, List<String> identifierValues, boolean callInventory) {
        ReturnDetailsDTO returnDetailsDTO = new ReturnDetailsDTO();

        if (!validateRequest(identifierType, identifierValues)) {
            return returnDetailsDTO;
        }

        List<ReturnDetail> returnOrders = null;
        List<ReturnDetailItem> returnOrderItems = null;

        try {
        switch (identifierType) {
            case "UW_ITEM_ID":
                returnOrderItems = findByUwItemIdIn(identifierValues.stream().map(Integer::valueOf).toList(), callInventory);
                if(!CollectionUtils.isEmpty(returnOrderItems)){
                    returnOrders = findByReturnIdIn(returnOrderItems.stream().map(ReturnDetailItem::getReturnId).toList(), callInventory);
                }
                break;
            case "INCREMENT_ID":
                returnOrders = getReturnOrders(identifierValues.stream().map(Integer::valueOf).toList(), callInventory);
                if(!CollectionUtils.isEmpty(returnOrders)){
                    returnOrderItems = findReturnOrderItemByReturnIdIn(returnOrders.stream().map(ReturnDetail::getId).toList(), callInventory);
                }
                break;
            case "ORDER_ID":
                returnOrders = getReturnOrders(identifierValues.stream().map(Integer::valueOf).toList(), callInventory);
                if(!CollectionUtils.isEmpty(returnOrders)){
                    returnOrderItems = findReturnOrderItemByReturnIdIn(returnOrders.stream().map(ReturnDetail::getId).toList(), callInventory);
                }
                break;
            case "RETURN_ID":
                returnOrders = findByReturnIdIn(identifierValues.stream().map(Integer::valueOf).toList(), callInventory);
                if(!CollectionUtils.isEmpty(returnOrders)){
                    returnOrderItems = findReturnOrderItemByReturnIdIn(returnOrders.stream().map(ReturnDetail::getId).toList(), callInventory);
                }
                break;
            case "GROUP_ID":
                returnOrders = findByGroupIdIn(identifierValues.stream().map(Long::valueOf).toList(), callInventory);
                if(!CollectionUtils.isEmpty(returnOrders)){
                    returnOrderItems = findReturnOrderItemByGroupIdIn(identifierValues.stream().map(Long::valueOf).toList(), callInventory);
                }
        }
            returnDetailsDTO.setReturnOrders(objectMapper.convertValue(returnOrders, new TypeReference<List<ReturnOrderDTO>>() {
            }));
            returnDetailsDTO.setReturnOrderItems(objectMapper.convertValue(returnOrderItems, new TypeReference<List<ReturnOrderItemDTO>>() {
            }));
            populateReturnOrderStatus(returnDetailsDTO.getReturnOrders());
            populateExtraDetails(returnDetailsDTO.getReturnOrders());
        }catch (Exception exception){
            log.error("[getReturnDetailsByIdentifier] exception occurred for identifierValues: {} ", identifierValues, exception);
        }

        return returnDetailsDTO;
    }

    private void populateExtraDetails(List<ReturnOrderDTO> returnOrders) {
        try {
            //set tracking details
            returnOrderEnrichmentService.populateTrackingDetails(returnOrders);
        } catch (Exception e) {
            log.error("Error occurred while populating extra details for return orders", e);
        }
    }

    private void populateReturnOrderStatus(List<ReturnOrderDTO> returnOrders) {
        if (!CollectionUtils.isEmpty(returnOrders)) {
            returnOrders.forEach(this::updateReturnOrderEvent);
        }
    }

    private void updateReturnOrderEvent(ReturnOrderDTO returnOrderDTO) {
        ReturnCourierDetail returnCourierDetail = returnCourierDetailRepository.findTopByReturnIdOrderByIdDesc(returnOrderDTO.getId());
        if (returnCourierDetail != null) {
            returnOrderDTO.setReverseCourier(returnCourierDetail.getReverseCourier());
            returnOrderDTO.setReverseAwb(returnCourierDetail.getReverseAwbNumber());
        }
        ReturnEvent returnEvent = getReturnOrderEvent(returnOrderDTO.getId());
        if(returnEvent != null) {
            returnOrderDTO.setStatus(ReturnStatus.valueOf(returnEvent.getEvent().toUpperCase()).getStatus());
            returnOrderDTO.setLastUpdateDatetime(returnEvent.getCreatedAt());
            returnOrderDTO.setOverrideComment(returnEvent.getRemarks());
        }
    }

    public List<ReturnDetailItem> findByUwItemIdIn(List<Integer> uwItemIds, boolean callInventory) {
        List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByUwItemIdInOrderByIdDesc(uwItemIds);
        if (CollectionUtils.isEmpty(returnDetailItems)) {
            returnDetailItems = returnOrderItemRepository.findByUwItemIdIn(uwItemIds).stream().map(this::convertToReturnDetailItem).toList();
            if(CollectionUtils.isEmpty(returnDetailItems) && callInventory){
                returnDetailItems = new ArrayList<>();
                for(Integer uwItemId : uwItemIds){
                    com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory("UW_ITEM_ID", String.valueOf(uwItemId));
                    if(returnOrderDTO != null){
                        returnDetailItems.add(convertToReturnDetailItem(returnOrderDTO));
                    }
                }
            }
        }
        return returnDetailItems;
    }

    public List<ReturnDetailItem> findReturnOrderItemByReturnIdIn(List<Integer> returnIds, boolean callInventory) {
        List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByReturnIdIn(returnIds);
        if (CollectionUtils.isEmpty(returnDetailItems)) {
            returnDetailItems = returnOrderItemRepository.findByReturnIdIn(returnIds).stream().map(this::convertToReturnDetailItem).toList();
            if(CollectionUtils.isEmpty(returnDetailItems) && callInventory){
                returnDetailItems = new ArrayList<>();
                for(Integer returnId : returnIds){
                    com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory("RETURN_ID", String.valueOf(returnId));
                    if(returnOrderDTO != null){
                        returnDetailItems.add(convertToReturnDetailItem(returnOrderDTO));
                    }
                }
            }
        }
        return returnDetailItems;
    }


    public List<ReturnDetailItem> findReturnOrderItemByGroupIdIn(List<Long> groupIds, boolean callInventory) {
        List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByGroupIdIn(groupIds);
        if (CollectionUtils.isEmpty(returnDetailItems)) {
            returnDetailItems = returnOrderItemRepository.findByGroupIdIn(groupIds).stream().map(this::convertToReturnDetailItem).toList();
            if(CollectionUtils.isEmpty(returnDetailItems) && callInventory){
                returnDetailItems = new ArrayList<>();
                for(Long groupId : groupIds){
                    com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory("GROUP_ID", String.valueOf(groupId));
                    if(returnOrderDTO != null){
                        returnDetailItems.add(convertToReturnDetailItem(returnOrderDTO));
                    }
                }
            }
        }
        return returnDetailItems;
    }

    @Override
    public Integer getUwItemIdByReturnId(Integer returnId) {
        Integer uwItemIdByReturnId = returnDetailItemRepository.findUwItemIdByReturnId(returnId);
        if (uwItemIdByReturnId == null) {
            return returnOrderItemRepository.findUwItemIdByReturnId(returnId);
        }
        return uwItemIdByReturnId;
    }

    @Override
    public Map<String, Integer> getItemDetailsByReturnId(Integer returnId) {
        Map<String, Integer> itemDetailsMap = new HashMap<>();
        List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnDetailItems)){
            Integer itemId = returnDetailItems.get(0).getItemId();
            Integer uwItemId = returnDetailItems.get(0).getUwItemId();
            Long productId = returnDetailItems.get(0).getProductId();
            itemDetailsMap.put("itemId", itemId);
            itemDetailsMap.put("uwItemId", uwItemId);
            itemDetailsMap.put("productId", Math.toIntExact(productId));
        }
        else{
            List<ReturnOrderItem> returnOrderItems = returnOrderItemRepository.findByReturnId(returnId);
            if(!CollectionUtils.isEmpty(returnOrderItems)){
                Integer itemId = returnOrderItems.get(0).getItemId();
                Integer uwItemId = returnOrderItems.get(0).getUwItemId();
                Integer productId = returnOrderItems.get(0).getProductId();
                itemDetailsMap.put("itemId", itemId);
                itemDetailsMap.put("uwItemId", uwItemId);
                itemDetailsMap.put("productId", Math.toIntExact(productId));
            }
        }
        return itemDetailsMap;
    }

    public List<ReturnDetail> findByGroupIdIn(List<Long> groupIds, boolean callInventory) {
        List<ReturnDetail> returnDetails = returnDetailRepository.findByGroupIdIn(groupIds);
        if (CollectionUtils.isEmpty(returnDetails)) {
            returnDetails = returnOrderRepository.findByGroupIdIn(groupIds).stream().map(this::convertToReturnDetail).toList();
            if(CollectionUtils.isEmpty(returnDetails) && callInventory){
                returnDetails = new ArrayList<>();
                for(Long groupId : groupIds){
                    com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = getReturnFromInventory("GROUP_ID", String.valueOf(groupId));
                    if(returnOrderDTO != null){
                        returnDetails.add(convertToReturnDetail(returnOrderDTO));
                    }
                }
            }
        }
        return returnDetails;
    }

    @Override
    public Map<String, Boolean> getIsReturnFromNewFlow(String identifierType, String identifierValue) {
        log.info("[getIsReturnFromNewFlow] {} : {}", identifierType, identifierValue);
        Map<String, Boolean> map = new HashMap<>();
        boolean isReturnExist = false;
        map.put(Constant.NEW_RETURN_FLOW, false);
        try {
            isReturnExist = switch (identifierType) {
                case Constant.IDENTIFIER_TYPE.ORDER_ID ->
                        returnDetailRepository.getReturnCountByIncrementId(Integer.valueOf(identifierValue)) > 0;
                case Constant.IDENTIFIER_TYPE.RETURN_ID ->
                        returnDetailRepository.getReturnCountByReturnId(Integer.valueOf(identifierValue)) > 0;
                case Constant.IDENTIFIER_TYPE.UWITEM_ID ->
                        returnDetailItemRepository.getReturnOrderItemCountByUwItemId(Integer.valueOf(identifierValue)) > 0;
                default -> false;
            };
        } catch (Exception e) {
            log.error("[NEW_RETURN_FLOW] check error :" + e);
        }
        map.put(Constant.NEW_RETURN_FLOW, isReturnExist);
        log.info("[getIsReturnFromNewFlow] identifierValue : {}, returnedMap : {}", identifierValue, map);
        return map;
    }

    @Override
    public List<ReturnReasonTableDTO> getReturnReason(List<Integer> uwItemIds) {
        List<ReturnDetailReason> returnReason = returnReasonRepository.getReturnReasonsByItemId(uwItemIds);
        return returnReason.stream()
                .map(reason -> objectMapper.convertValue(reason, ReturnReasonTableDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public String getReturnSource(Integer uwItemId) {
        String returnSource = returnDetailItemRepository.getReturnSourceFromUwItemId(uwItemId);
        log.info("[getReturnSource] returnSource :{}", returnSource);
        return returnSource;
    }

    @Override
    public void updateReceivingFlag(String flag, Integer returnId, Integer incrementId) {
        addFlagUpdateEvent(returnId);
        returnDetailRepository.updateReceivingFlag(incrementId, returnId, flag);
    }

    @Override
    public ReturnOrderItemDTO getReturnOrderItemByUwItemId(Integer uwItemId) {
        ReturnDetailItem returnOrderItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(uwItemId);
        return objectMapper.convertValue(returnOrderItem, ReturnOrderItemDTO.class);
    }

    @Override
    public Map<String, Boolean> getIdentifierValuesMappingWithNewFlow(String identifierType, List<String> identifier) {
        Map<String, Boolean> identifierValuesMapping = new HashMap<>();
        identifier.forEach(value -> {
            Map<String, Boolean> response = getIsReturnFromNewFlow(identifierType, value);
            Boolean newFlowValue = response.get(Constant.NEW_RETURN_FLOW);
            identifierValuesMapping.put(value, newFlowValue);
        });
        return identifierValuesMapping;
    }

    @Override
    public com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO getReturnOrderDetails(String identifierType, String identifier) {
        com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO returnDetailsDTO = new com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO();
        try {
            log.info("[RefundUtilServiceImpl][getReturnOrderDetails] getReturnOrderDetails for identifier-type:{}, identifier:{}", identifierType, identifier);
            if (!validateRequest(identifierType, Collections.singletonList(identifier))) {
                return null;
            }
            List<Integer> returnIds = getReturnId(identifierType, identifier);
            log.info("[RefundUtilServiceImpl][getReturnOrderDetails] returnIds : {}",returnIds);
            if (CollectionUtils.isEmpty(returnIds)) {
                return null;
            }
            List<com.lenskart.ordermetadata.dto.ReturnOrderDTO> returnOrderDTOs = new ArrayList<>();
            ReturnDetail returnOrder = findReturnOrderById(returnIds.get(0)).orElse(null);
            if (returnOrder != null) {
                PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = orderOpsFeignClient.getPurchaseOrderDetails(Constant.IDENTIFIER_TYPE.INCREMENT_ID, String.valueOf(returnOrder.getIncrementId())).getBody();
                if (purchaseOrderDetailsDTO != null && !CollectionUtils.isEmpty(purchaseOrderDetailsDTO.getUwOrders())) {
                    List<UwOrderDTO> uwOrderDTOS = purchaseOrderDetailsDTO.getUwOrders();
                    for (Integer returnId : returnIds) {
                        List<com.lenskart.ordermetadata.dto.ReturnOrderDTO> returnOrderDTOList = getReturnOrderList(returnId, uwOrderDTOS, purchaseOrderDetailsDTO.getShippingStatusDetailList());
                        if (!returnOrderDTOList.isEmpty()) {
                            returnOrderDTOs.addAll(returnOrderDTOList);
                        }
                    }
                }
                returnDetailsDTO.setReturnOrderDTO(returnOrderDTOs);
            }
        } catch (Exception e) {
            log.info("[RefundUtilServiceImpl][getReturnOrderDetails] Exception:" + e);
        }
        if(CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrderDTO()) || CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrderDTO().get(0).getReturnHistoryDTOList())){
            //call order-ops return details and return the result
            log.info("[getReturnOrderDetails] fetching from order-ops");
            ResponseEntity<com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO> returnDetailsDTOResponseEntity = orderOpsFeignClient.getReturnOrderDetails(identifierType, identifier);
            if(returnDetailsDTOResponseEntity.getStatusCode().is2xxSuccessful()){
                returnDetailsDTO = returnDetailsDTOResponseEntity.getBody();
            }
        }
        log.info("[RefundUtilServiceImpl][getReturnOrderDetails] getReturnOrderDetails for identifier-type:{}, identifier:{}, returnDetailsDTO : {}", identifierType, identifier, returnDetailsDTO);
        return returnDetailsDTO;
    }

    @Override
    public Long findGroupIdByReturnId(Integer b2bReturnId) {
        Long groupIdByReturnId = returnDetailRepository.findGroupIdByReturnId(b2bReturnId);
        if (groupIdByReturnId == null) {
            return returnOrderRepository.findGroupIdByReturnId(b2bReturnId);
        }
        return groupIdByReturnId;
    }

    @Override
    public Integer getReturnId(Integer uwItemId) {
        Integer returnId = returnDetailItemRepository.findTop1ReturnIdByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
        if (returnId == null) {
            return returnOrderItemRepository.findTop1ReturnIdByUwItemIdOrderByReturnCreateDatetimeDesc(uwItemId);
        }
        return returnId;
    }

    private List<com.lenskart.ordermetadata.dto.ReturnOrderDTO> getReturnOrderList(Integer returnId, List<UwOrderDTO> uwOrders, List<ShippingStatusDetail> shippingStatusDetailList) {
        List<com.lenskart.ordermetadata.dto.ReturnOrderDTO> returnOrderList = new ArrayList<>();
        try {
            ReturnDetail returnOrder = findReturnOrderById(returnId).orElse(null);
            if (returnOrder != null) {
                List<Integer> uwItemIds = findUWItemIdByReturnId(returnId);
                log.info("[getReturnOrderList] uwItemIds : {}",uwItemIds);
//                log.info("[getReturnOrderList] uwOrders1 : {}", uwOrders);
//                if (!CollectionUtils.isEmpty(uwOrders) && uwOrders.size() > 1) {
//                    uwOrders = uwOrders.stream().filter(uw -> uwItemIds.contains(uw.getUwItemId())).filter(this::filterUwOrderByParentUw).filter(this::filterUwOrderByFacilityCode).collect(Collectors.toList());
//                }

                log.info("[getReturnOrderList] uwOrders : {}", uwOrders);
                for (UwOrderDTO uwOrder : uwOrders) {
                    if(uwItemIds.contains(uwOrder.getUwItemId())){
                        com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = new com.lenskart.ordermetadata.dto.ReturnOrderDTO();
                        returnOrderDTO.setReturnId((long) returnId);
                        returnOrderDTO.setUwItemId(Long.valueOf(uwOrder.getUwItemId()));
                        returnOrderDTO.setGroupId(Math.toIntExact(returnOrder.getGroupId()));
                        returnOrderDTO.setReturnCreatedAt(returnOrder.getReturnCreateDatetime());
                        returnOrderDTO.setReturnMethod(returnOrder.getReturnMethod());
                        returnOrderDTO.setReturnType(returnOrder.getReturnType());
                        returnOrderDTO.setReturnStatus(getReturnOrderStatus(returnOrder));
                        returnOrderDTO.setSource(returnOrder.getSource());
                        returnOrderDTO.setFacilityCode(returnOrder.getFacilityCode());
                        returnOrderDTO.setReceivingFlag("yes".equalsIgnoreCase(returnOrder.getReceivingFlag()));
                        returnOrderDTO.setNewFlowFlag(1);
                        List<ReturnEvent> returnHistoryList = returnEventRepository.findByReturnId(returnId);
                        List<ReturnHistoryDTO> returnHistoryDtoList = new ArrayList<>();
                        if(!CollectionUtils.isEmpty(returnHistoryList)){
                            returnHistoryDtoList = returnHistoryList.stream().map(e -> {
                                ReturnHistoryDTO returnHistoryDTO = new ReturnHistoryDTO();
                                String event = ReturnStatus.RETURN_RECEIVED_AT_WAREHOUSE.getName().equalsIgnoreCase(e.getEvent())
                                        || ReturnStatus.RETURN_RECEIVED_AT_STORE.getName().equalsIgnoreCase(e.getEvent())
                                        ? ReturnStatus.RETURN_RECEIVED.getName() :  e.getEvent();
                                returnHistoryDTO.setCurrentStatus(event);
                                returnHistoryDTO.setComment(e.getRemarks());
                                returnHistoryDTO.setCreatedAt(e.getCreatedAt());
                                return returnHistoryDTO;
                            }).collect(Collectors.toList());
                        }else{
                            List<ReturnHistory> returnHistories = returnHistoryRepository.findByEntityId(returnId);
                            if(!CollectionUtils.isEmpty(returnHistories)){
                                returnHistoryDtoList = returnHistories.stream().map(retHist -> {
                                    ReturnHistoryDTO returnHistoryDTO = new ReturnHistoryDTO();
                                    returnHistoryDTO.setCurrentStatus(retHist.getCurrentStatus());
                                    returnHistoryDTO.setComment(retHist.getComment());
                                    returnHistoryDTO.setCreatedAt(retHist.getCreatedAt());
                                    returnHistoryDTO.setSource(retHist.getSource());
                                    returnHistoryDTO.setCourier(retHist.getCourier());
                                    returnHistoryDTO.setReversePickupReferenceId(retHist.getReversePickupReferenceId());
                                    returnHistoryDTO.setReverseAwb(retHist.getReverseAwb());
                                    returnHistoryDTO.setEntityType(retHist.getEntityType());
                                    return returnHistoryDTO;
                                }).collect(Collectors.toList());
                            }
                            if(isBeforeMarchFirst2025(returnOrder)){
                                ResponseEntity<List<ReturnHistoryDTO>> returnHistoryResponse = orderOpsFeignClient.getReturnHistory(returnId);
                                if(returnHistoryResponse.getStatusCode().is2xxSuccessful()){
                                    returnHistoryDtoList = returnHistoryResponse.getBody();
                                }
                            }
                        }
                        returnOrderDTO.setReturnHistoryDTOList(returnHistoryDtoList);
                        returnOrderDTO.setReverseAwbNo(getTrackingNumber(returnId, uwOrder, shippingStatusDetailList));
                        returnOrderDTO.setOrderNo(uwOrder.getIncrementId());
                        returnOrderDTO.setProductId(uwOrder.getProductId());
                        returnOrderDTO.setFacilityCode(returnOrder.getFacilityCode());
                        returnOrderList.add(returnOrderDTO);
                    }
                }
                log.info("[getReturnOrderList] returnOrderList : {}",returnOrderList);
            }
        } catch (Exception e) {
            log.info("[RefundUtilServiceImpl][getReturnOrder] Exception:" + e);
        }
        return returnOrderList;
    }

    private boolean isBeforeMarchFirst2025(ReturnDetail returnOrder) {
        log.info("[isBeforeMarchFirst2025] checking if order is before March 2025 : {}",returnOrder.getIncrementId());
        // Set the reference date: 1st March 2025
        Calendar calendar = Calendar.getInstance();
        calendar.set(2025, Calendar.MARCH, 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date referenceDate = calendar.getTime();

        boolean isOlderReturn = returnOrder.getReturnCreateDatetime().before(referenceDate);
        log.info("[isBeforeMarchFirst2025] checking if order is before March 2025 : {} , {}",returnOrder.getIncrementId(),isOlderReturn);
        return isOlderReturn;
    }

    private String getTrackingNumber(Integer returnId, UwOrderDTO uwOrderDTO, List<ShippingStatusDetail> shippingStatusDetail) {
        log.info("[ReturnOrderActionServiceImpl][getTrackingNumber] returnId:{}", returnId);
        Integer uwItemId = getUwItemId(returnId);
        log.info("[ReturnOrderActionServiceImpl][getTrackingNumber] uwItemId: {}",uwItemId);
        if (uwItemId == null) {
            return null;
        }
        String unicomOrderCode = uwOrderDTO!=null ? uwOrderDTO.getUnicomOrderCode() : null;
        log.info("[ReturnOrderActionServiceImpl][getTrackingNumber] unicomOrderCode: {}",unicomOrderCode);
        if (unicomOrderCode == null) {
            return null;
        }

        if (shippingStatusDetail == null || shippingStatusDetail.isEmpty()) {
            log.warn("[ReturnOrderActionServiceImpl][getTrackingNumber] shippingStatusDetail is null or empty.");
            return null;
        }

        Optional<ShippingStatusDetail> matchedItem = shippingStatusDetail.stream()
                .filter(s -> s.getUnicomOrderCode().equalsIgnoreCase(unicomOrderCode))
                .findFirst();

        String trackingNumber = matchedItem.map(ShippingStatusDetail::getTrackingNo).orElse(null);
        log.info("[ReturnOrderActionServiceImpl][getTrackingNumber] trackingNumber: {}",trackingNumber);
        return trackingNumber;
    }

    private Integer getUwItemId(Integer returnId) {
        Integer uwItemId = Optional.ofNullable(returnDetailItemRepository.findUwItemIdByReturnId(returnId))
                .orElseGet(() -> returnOrderItemRepository.findUwItemIdByReturnId(returnId));

        if (uwItemId == null) {
            log.info("[ReturnOrderActionServiceImpl][getUwItemId] uwItemId is null for returnId: {}", returnId);
        }
        return uwItemId;
    }

    private boolean filterUwOrderByFacilityCode(ReturnDetailItem returnDetailItem) {
        try {
            if (Objects.isNull(returnDetailItem)) {
                return false;
            }
            return !Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(returnDetailItem.getProductDeliveryType()) || !"franchisecredit".contains(returnDetailItem.getMethod());
        } catch (Exception e) {
            log.info("[RefundUtilServiceImpl][filterUwOrderByFacilityCode] Exception:" + e);
        }
        return false;
    }

    private List<Integer> findUWItemIdByReturnId(Integer returnId) {
        List<Integer> uwItemIdByReturnId = returnDetailItemRepository.findUWItemIdByReturnId(returnId);
        if (CollectionUtils.isEmpty(uwItemIdByReturnId)) {
            return returnOrderItemRepository.findUWItemIdByReturnId(returnId);
        }
        return uwItemIdByReturnId;
    }

    private boolean filterUwOrderByParentUw(UwOrderDTO uwOrder) {
        if (Objects.isNull(uwOrder)) {
            return false;
        }
        return uwOrder.getParentUw() == 0;
    }

    private boolean filterUwOrderByFacilityCode(UwOrderDTO uwOrder) {
        try {
            if (Objects.isNull(uwOrder) || StringUtils.isEmpty(uwOrder.getFacilityCode())) {
                return false;
            }
            return (Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()) && !facilityCodeList.contains(uwOrder.getFacilityCode()))
                    || !Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType());
        } catch (Exception e) {
            log.info("[RefundUtilServiceImpl][filterUwOrderByFacilityCode] Exception:" + e);
        }
        return false;
    }

    private List<Integer> getReturnId(String identifierType, String identifier) {
        List<Integer> returnIds = new ArrayList<>();
        List<Integer> returnIdList = null;
        switch (identifierType) {
            case Constant.IDENTIFIER_TYPE.RETURN_ID:
                returnIds.add(Integer.valueOf(identifier));
                break;
            case Constant.IDENTIFIER_TYPE.INCREMENT_ID:
                returnIdList = findReturnIdsByIncrementId(Integer.valueOf(identifier));
                returnIds.addAll(returnIdList);
                break;
            case Constant.IDENTIFIER_TYPE.ORDER_ID:
                returnIdList = findReturnIdsByIncrementId(Integer.valueOf(identifier));
                returnIds.addAll(returnIdList);
                break;
            case Constant.IDENTIFIER_TYPE.UWITEM_ID:
                ReturnDetailItem returnOrderItem = findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(Integer.valueOf(identifier));
                returnIds.add(returnOrderItem.getReturnId());
                break;
            case Constant.IDENTIFIER_TYPE.GROUP_ID:
                List<ReturnDetailItem> returnDetailItemList = findByGroupIdOrderByReturnCreateDatetimeDesc(Long.valueOf(identifier));
                returnIds.addAll(returnDetailItemList.stream().map(ReturnDetailItem::getReturnId).toList());
                break;
        }
        return returnIds;
    }

    @Override
    public List<Integer> findReturnIdsByIncrementId(Integer orderNo) {
        List<Integer> returnIds = returnDetailRepository.findReturnIdsByIncrementId(orderNo);
        if (CollectionUtils.isEmpty(returnIds)) {
            returnIds = returnOrderRepository.findReturnIdsByOrderNo(orderNo);
            if(CollectionUtils.isEmpty(returnIds)){
                returnIds = getReturnIdsFromInventory(Constant.IDENTIFIER_TYPE.ORDER_ID, String.valueOf(orderNo));
            }
        }
        return returnIds;
    }

    @Override
    public List<Integer> getReturnIdsFromInventory(String identifierType, String identifierValue){
        log.info("[getReturnIdsFromInventory] identifierType : {} , identifierValue : {}", identifierType, identifierValue);
        List<Integer> returnIds = null;
        try{
            ResponseEntity<com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO> returnOrderDetails = orderOpsFeignClient.getReturnOrderDetails(identifierType, String.valueOf(identifierValue));
            log.info("[getReturnIdsFromInventory] identifierValue : {} , returnOrderDetails : {}", identifierValue , returnOrderDetails);
            if(returnOrderDetails.getStatusCode().is2xxSuccessful()){
                com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO returnDetailsDTO = returnOrderDetails.getBody();
                if(returnDetailsDTO != null && !CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrderDTO())){
                    com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = returnDetailsDTO.getReturnOrderDTO().get(0);
                    returnIds = Collections.singletonList(returnOrderDTO.getReturnId().intValue());
                }
            }
        }catch (Exception exception){
            log.error("[getReturnIdsFromInventory] exception : "+exception.getMessage());
        }
        log.info("[getReturnIdsFromInventory] identifierType : {} , identifierValue : {} , returnIds : {}", identifierType, identifierValue, returnIds);
        return returnIds;
    }

    @Override
    public com.lenskart.ordermetadata.dto.ReturnOrderDTO getReturnFromInventory(String identifierType, String identifierValue){
        log.info("[getReturnFromInventory] identifierType : {} , identifierValue : {}", identifierType, identifierValue);
        com.lenskart.ordermetadata.dto.ReturnOrderDTO returnOrderDTO = null;
        try{
            ResponseEntity<com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO> returnOrderDetails = orderOpsFeignClient.getReturnOrderDetails(identifierType, String.valueOf(identifierValue));
            if(returnOrderDetails.getStatusCode().is2xxSuccessful()){
                com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO returnDetailsDTO = returnOrderDetails.getBody();
                if(returnDetailsDTO != null && !CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrderDTO())){
                    returnOrderDTO = returnDetailsDTO.getReturnOrderDTO().get(0);
                }
            }
        }catch (Exception exception){
            log.error("[getReturnFromInventory] exception : "+exception.getMessage());
        }
        log.info("[getReturnFromInventory] identifierType : {} , identifierValue : {} , returnOrderDTO : {}", identifierType, identifierValue, returnOrderDTO);
        return returnOrderDTO;
    }

    private void addFlagUpdateEvent(Integer returnId) {
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setReturnId(returnId);
        returnEvent.setEvent("RECEIVING_FLAG_UPDATED");
        returnEventService.createTrackingEvent(returnEvent);
    }

    @Override
    public List<ReturnDetailItem> findAllByReturnId(Integer returnId) {
        return returnDetailItemRepository.findByReturnId(returnId);
    }

    @Override
    public ReturnDetail persistB2BReturn(UwOrderDTO uwOrderB2B, long group_id, ReturnDetail returnOrder, QCType qcType, boolean existingReturnDetailUsed, Integer returnRequestId, String facilityCode, ReturnItemDTO item) {
        log.info("[persistB2BReturn] persisting B2B return for order : {}, item : {}", uwOrderB2B.getIncrementId(), uwOrderB2B.getUwItemId());
        Optional<ReturnDetail> returnDetailB2BOpt = getExistingReturnDetailsByItem(uwOrderB2B.getUwItemId());
        ReturnDetail returnDetailB2B;
        if (existingReturnDetailUsed) {
            log.info("Existing b2b return detail found. ");
            returnDetailB2B = returnDetailB2BOpt.get();
        } else {
            log.info("Creating new b2b return detail object. ");
            returnDetailB2B = new ReturnDetail();
            returnDetailB2B.setReturnCreateDatetime(new Date());
        }
        returnDetailB2B.setGroupId(group_id);
        returnDetailB2B.setIncrementId(returnOrder.getIncrementId());
        returnDetailB2B.setUnicomOrderCode(uwOrderB2B.getUnicomOrderCode());
        returnDetailB2B.setRequestId(returnOrder.getRequestId());
        returnDetailB2B.setAgentEmail(returnOrder.getAgentEmail());
        returnDetailB2B.setReturnType(REVERSE);
        if (StringUtils.isEmpty(returnOrder.getReceivingFlag())) {
            returnDetailB2B.setReceivingFlag("no");
        }
        if (!StringUtils.isEmpty(returnOrder.getReturnMethod())) {
            returnDetailB2B.setReturnMethod(returnOrder.getReturnMethod());
        }
        returnDetailB2B.setSource(returnOrder.getSource());
        returnDetailB2B.setIsQcAtDoorstep(qcType != null ? qcType.getValue() : QCType.NON_QC.getValue());
        returnDetailB2B.setFacilityCode(facilityCode);
        returnDetailB2B.setIsInsurance(item.getClaimInsurance() != null ? item.getClaimInsurance() : false);
        returnDetailB2B = returnDetailRepository.save(returnDetailB2B);
        returnEventService.persistEvent(returnRequestId, returnDetailB2B.getId(), RETURN_INITIATED, "Return initiated for uw_item_id " + uwOrderB2B.getUwItemId() + " through api");
        return returnDetailB2B;
    }

    @Override
    public ReturnDetail persistReturn(boolean isNewReturnBeCreated, Optional<ReturnDetail> returnDetailsOpt, ReturnCreationRequestDTO returnCreationRequest, UwOrderDTO uwOrder, OrdersHeaderDTO ordersHeaderDTO, ReturnStatus returnStatus, ReturnItemDTO item, QCType qcType, Integer requestId) {
        log.info("[persistReturn] order : {}", uwOrder.getIncrementId());
        ReturnDetail returnDetail = null;
        Long groupId;
        if (isNewReturnBeCreated) {
            returnDetail = new ReturnDetail();
            returnDetail.setReturnCreateDatetime(new Date());
            ReturnGroup returnGroup = new ReturnGroup();
            groupId = returnGroupRepository.save(returnGroup).getId();
            if (!StringUtils.isEmpty(returnCreationRequest.getReturnMethod())) {
                returnDetail.setReturnMethod(returnCreationRequest.getReturnMethod());
            }
            returnDetail.setSource(returnCreationRequest.getReturnSource().getSource());
        } else {
            returnDetail = returnDetailsOpt.get();
            groupId = returnDetail.getGroupId();
        }

        returnDetail.setGroupId(groupId);
        returnDetail.setRequestId(requestId);
        returnDetail.setIncrementId(uwOrder.getIncrementId());
        returnDetail.setUnicomOrderCode(uwOrder.getUnicomOrderCode());
        returnDetail.setFacilityCode(returnCreationRequest.getFacilityCode());
        log.info("Saving return details in return_order " + returnDetail);
        if (OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType()) && (item.getNeedApproval() != null && !item.getNeedApproval()) && !DRAFTED_STATUS.contains(returnStatus) && !ReturnStatus.NEW_REVERSE_PICKUP.equals(returnStatus)) {
            String lkCountry = getLkCountry(ordersHeaderDTO);
            if (lkCountry == null || bypassReturnReceivingCountries.stream().noneMatch(lkCountry::equalsIgnoreCase)) {
                returnDetail.setReceivingFlag("yes");
            }
        }
        returnDetail.setIsInsurance(item.getClaimInsurance() != null ? item.getClaimInsurance() : false);
        if (Objects.nonNull(qcType)) {
            returnDetail.setIsQcAtDoorstep(qcType.getValue());
        }
        returnDetail.setReturnType(REVERSE);
        if (StringUtils.isEmpty(returnDetail.getReceivingFlag())) {
            returnDetail.setReceivingFlag("no");
        }
        returnDetail = returnDetailRepository.save(returnDetail);
        if(isNewReturnBeCreated){
            returnEventService.createReturnEvent(requestId, returnDetail.getId(), RETURN_INITIATED, "Return initiated for order " + returnDetail.getIncrementId() + " through api");
        }
        log.info("[persistReturn] item return details saved : {}", returnDetail);
        return returnDetail;
    }

    private String getLkCountry(OrdersHeaderDTO ordersHeaderDTO) {
        String lkCountry = ordersHeaderDTO.getLkCountry();
        log.info("[getLkCountry] lkCountry : {}", lkCountry);
        return lkCountry;
    }

    @Override
    public boolean shouldReturnBeCreated(ReturnItemDTO item, Optional<ReturnDetail> returnDetailsOpt) throws ReturnNotFound {

        if (returnDetailsOpt.isPresent()) {
            log.debug("Existing return details found in db for uwItemId {}", item.getUwItemId());
            ReturnDetail returnDetail = returnDetailsOpt.get();
            String status = getReturnOrderStatus(returnDetail);
            if (newReturnStatus.contains(status)) {
                log.debug("Return details found in db for uwItemId {} but not reusable.", item.getUwItemId());
                return true;
            } else if (reuseReturnStatus.contains(status)) {
                if ("yes".equalsIgnoreCase(returnDetail.getReceivingFlag())) {
                    throw new ReturnNotFound("REFUND_REQUEST_ALREADY_EXISTS_IN_PROCESSED_STATUS", "Item already received.");
                }
                return false;
            } else {
                throw new ReturnNotFound("REFUND_REQUEST_ALREADY_EXISTS_IN_PROCESSED_STATUS", "Item already processed.");
            }

        }
        log.debug("No existing return details found in db for uwItemId {}", item.getUwItemId());
        return true;
    }

    @Override
    public List<ReturnDetail> findByGroupId(long groupId) {
        List<ReturnDetail> byGroupId = returnDetailRepository.findByGroupId(groupId);
        if (CollectionUtils.isEmpty(byGroupId)) {
            return returnOrderRepository.findByGroupId(groupId).stream().map(this::convertToReturnDetail).toList();
        }
        return byGroupId;
    }

    @Override
    public ReturnCourierDetail findTopByReturnIdOrderByIdDesc(Integer id) {
        ReturnCourierDetail topByReturnIdOrderByIdDesc = returnCourierDetailRepository.findTopByReturnIdOrderByIdDesc(id);
        if (topByReturnIdOrderByIdDesc == null) {
            return convertToReturnCourienrDetail(returnOrderRepository.findByReturnId(id));
        }
        return topByReturnIdOrderByIdDesc;
    }

    @Override
    public void saveReturnCourier(ReturnCourierDetail returnCourierDetail) {
        returnCourierDetailRepository.save(returnCourierDetail);
    }

    @Override
    public List<ReturnDetail> getReturnOrdersByStatusOrderAndCourier(Integer orderNo, String reverseCourier, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        List<ReturnDetail> returnDetailList = new ArrayList<>();
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByOrderAndCourier(orderNo, reverseCourier, fromTime, toTime, pageSize, pageNo);
        filterReturnsByStatusOrderAndCourier(returnDetails, orderNo, reverseCourier, status, returnDetailList, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnOrdersByStatusOrderAndCourier(orderNo, reverseCourier, status, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetailList;
    }

    @Override
    public List<ReturnDetail> getReturnsByOrderAndCourier(Integer orderNo, String reverseCourier, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByOrderAndCourier(orderNo, reverseCourier, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnsByOrderAndCourier(orderNo, reverseCourier, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetails;
    }

    @Override
    public List<ReturnDetail> getReturnOrdersByStatusAndOrder(Integer orderNo, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        List<ReturnDetail> returnDetailList = new ArrayList<>();
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByOrder(orderNo, fromTime, toTime, pageSize, pageNo);
        filterReturnsByStatusAndOrder(returnDetails, returnDetailList, orderNo, status, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnOrdersByStatusAndOrder(orderNo, status, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetails;
    }

    @Override
    public List<ReturnDetail> getReturnsByOrder(Integer orderNo, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        log.info("[getReturnsByOrder] orderNo : {}, fromTime : {}, toTime : {}, pageSize : {}, pageNo : {}", orderNo, fromTime, toTime, pageSize, pageNo);
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByOrder(orderNo, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnsByOrder(orderNo, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetails;
    }

    @Override
    public List<ReturnDetail> getReturnsByCourierAndStatus(String reverseCourier, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        List<ReturnDetail> returnDetailList = new ArrayList<>();
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByCourier(reverseCourier, fromTime, toTime, pageSize, pageNo);
        filterReturnsByCourierAndStatus(returnDetails, returnDetailList, reverseCourier, status, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnsByCourierAndStatus(reverseCourier, status, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetails;
    }

    @Override
    public List<ReturnDetail> getReturnsByCourier(String reverseCourier, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByCourier(reverseCourier, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnsByCourier(reverseCourier, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetails;
    }

    @Override
    public List<ReturnDetail> getReturnsByStatus(String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        log.info("[getReturnsByStatus] status : {}", status);
        List<ReturnDetail> returnDetailList = new ArrayList<>();
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByStatus( fromTime, toTime, pageSize, pageNo);
        filterReturnsByStatus(returnDetails, status, returnDetailList, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnsByStatus(status, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetailList;
    }

    private void filterReturnsByStatus(List<ReturnDetail> returnDetails, String status, List<ReturnDetail> returnDetailList, Date fromTime, Date toTime, Integer pageSize, Integer pageNo){
        if(!CollectionUtils.isEmpty(returnDetails)){
            filterReturnsByStatus(status, returnDetailList, returnDetails);
            log.info("[filterReturnsByStatus] returnDetailList size : {}", returnDetailList.size());
            while(!returnDetails.isEmpty() && returnDetailList.size() < pageSize){
                returnDetails = returnDetailRepository.getReturnsByStatus(fromTime, toTime, pageSize, ++pageNo);
                if(!CollectionUtils.isEmpty(returnDetails)){
                    filterReturnsByStatus(status, returnDetailList, returnDetails);
                }
                log.info("[filterReturnsByStatus] returnDetailList size : {}, returnDetails size : {}", returnDetailList.size(), returnDetails.size());
            }
        }
    }

    private void filterReturnsByStatusOrderAndCourier(List<ReturnDetail> returnDetails, Integer orderNo, String reverseCourier, String status, List<ReturnDetail> returnDetailList, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        if(!CollectionUtils.isEmpty(returnDetails)){
            filterReturnsByStatus(status, returnDetailList, returnDetails);
            log.info("[filterReturnsByStatusOrderAndCourier] returnDetailList size : {}", returnDetailList.size());
            while(!returnDetails.isEmpty() && returnDetailList.size() < pageSize){
                returnDetails = returnDetailRepository.getReturnsByOrderAndCourier(orderNo, reverseCourier, fromTime, toTime, pageSize, ++pageNo);
                if(!CollectionUtils.isEmpty(returnDetails)){
                    filterReturnsByStatus(status, returnDetailList, returnDetails);
                }
                log.info("[filterReturnsByStatusOrderAndCourier] returnDetailList size : {}, returnDetails size : {}", returnDetailList.size(), returnDetails.size());
            }
        }
    }

    private void filterReturnsByStatus(String status, List<ReturnDetail> returnDetailList, List<ReturnDetail> returnDetails) {
        List<ReturnDetail> detailList = returnDetails.stream().filter(returnDetail -> status.equalsIgnoreCase(getReturnOrderStatusById(returnDetail.getId()))).toList();
        if(!CollectionUtils.isEmpty(detailList)){
            returnDetailList.addAll(detailList);
        }
    }

    private void filterReturnsByStatusAndOrder(List<ReturnDetail> returnDetails, List<ReturnDetail> returnDetailList, Integer orderNo, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        if(!CollectionUtils.isEmpty(returnDetails)){
            filterReturnsByStatus(status, returnDetailList, returnDetails);
            log.info("[filterReturnsByStatusAndOrder] returnDetailList size : {}", returnDetailList.size());
            while(!returnDetails.isEmpty() && returnDetailList.size() < pageSize){
                returnDetails = returnDetailRepository.getReturnsByOrder(orderNo, fromTime, toTime, pageSize, ++pageNo);
                if(!CollectionUtils.isEmpty(returnDetails)){
                    filterReturnsByStatus(status, returnDetailList, returnDetails);
                }
                log.info("[filterReturnsByStatusAndOrder] returnDetailList size : {}, returnDetails size : {}", returnDetailList.size(), returnDetails.size());
            }
        }
    }

    private void filterReturnsByCourierAndStatus(List<ReturnDetail> returnDetails, List<ReturnDetail> returnDetailList, String reverseCourier, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        if(!CollectionUtils.isEmpty(returnDetails)){
            filterReturnsByStatus(status, returnDetailList, returnDetails);
            log.info("[filterReturnsByCourierAndStatus] returnDetailList size : {}", returnDetailList.size());
            while(!returnDetails.isEmpty() && returnDetailList.size() < pageSize){
                returnDetails = returnDetailRepository.getReturnsByCourier(reverseCourier, fromTime, toTime, pageSize, ++pageNo);
                if(!CollectionUtils.isEmpty(returnDetails)){
                    filterReturnsByStatus(status, returnDetailList, returnDetails);
                }
                log.info("[filterReturnsByCourierAndStatus] returnDetailList size : {}, returnDetails size : {}", returnDetailList.size(), returnDetails.size());
            }
        }
    }
    
    @Override
    public ReturnDetail getReturnOrderBasedOnUwItemIdAndStatus(Integer uwItemId) {
        ReturnDetail returnDetail = returnDetailRepository.getReturnOrderBasedOnUwItemIdAndStatus(uwItemId);
        if (returnDetail == null) {
            returnDetail =  convertToReturnDetail(returnOrderRepository.getReturnOrderBasedOnUwItemIdAndStatus(uwItemId));
        }
        return returnDetail;
    }

    @Override
    public List<Map<String, Object>> getReturnOrderResponse(Integer incrementId, List<Integer> non2B2UwItemIds) {

        List<Map<String, Object>> returnOrderResponseList = new ArrayList<>();
        for(Integer uwItemId : non2B2UwItemIds){
            log.info("[getReturnOrderResponse] uwItemId : {}", uwItemId);
            ReturnDetailItem returnDetailItem = findTop1ByUwItemIdOrderByIdDesc(uwItemId);
            if(returnDetailItem != null){
                Integer returnId = returnDetailItem.getReturnId();
                String status = getReturnOrderStatusById(returnId);
                Integer reversePickupFlag = findTopByReturnId(returnId) != null ? 1 : 0;
                ReturnEvent returnEvent = returnEventRepository.findTopByReturnIdOrderByIdDesc(returnId);
                Date lastUpdateTime = returnEvent != null ? returnEvent.getCreatedAt() : null;
                Map<String, Object> returnOrderResponse = getReturnDetails(incrementId, uwItemId, status, lastUpdateTime, reversePickupFlag);
                if(!CollectionUtils.isEmpty(returnOrderResponse)){
                    returnOrderResponseList.add(returnOrderResponse);
                }
            }
        }
        log.info("[getReturnOrderResponse] returnOrderResponseList : {}",returnOrderResponseList);
        return returnOrderResponseList;
    }

    private ReturnDetailItem findTop1ByUwItemIdOrderByIdDesc(Integer uwItemId) {
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(uwItemId);
        if(returnDetailItem == null){
            returnDetailItem = convertToReturnDetailItem(returnOrderItemRepository.findTop1ByUwItemIdOrderByIdDesc(uwItemId));
        }
        return returnDetailItem;
    }

    private Map<String, Object> getReturnDetails(Integer incrementId, Integer uwItemId, String status, Date lastUpdateTime, Integer reversePickupFlag) {
        Map<String, Object> returnOrderResponse = returnDetailRepository.getReturnOrderResponse(incrementId, uwItemId, status, lastUpdateTime, reversePickupFlag);
        if(CollectionUtils.isEmpty(returnOrderResponse)){
            returnOrderResponse = returnOrderRepository.getReturnOrderResponse(incrementId, uwItemId);
        }
        return returnOrderResponse;
    }

    @Override
    public Map<String, Object> getReturnTrackerOrderItemDtos(Integer uwItemId, ProductMetaDataResponse productInfoBody) {
        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(uwItemId);
        String returnReason = "";
        Map<String, Object> returnTrackerMap = null;
        if(returnDetailItem != null){
            Integer returnId = returnDetailItem.getReturnId();
            List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnId);
            if(!CollectionUtils.isEmpty(returnEvents)){
                Optional<Integer> returnRequestOpt = returnEvents.stream().map(ReturnEvent::getReturnRequestId).filter(Objects::nonNull).findAny();
                if(returnRequestOpt.isPresent()){
                    Integer integer = returnRequestOpt.get();
                    ReturnRequest returnRequest = returnRequestRepository.findById(integer).get();
                    returnReason = returnRequest.getReturnReason();
                }
            }
            returnTrackerMap = returnDetailItemRepository.getReturnItemProductInfo(uwItemId, productInfoBody.getWebsite(), productInfoBody.getValue(), productInfoBody.getProductImage(), productInfoBody.getProductUrl(), productInfoBody.getSku(), productInfoBody.getClassification(), returnReason);
        }else{
            returnTrackerMap = returnOrderItemRepository.getReturnItemProductInfo(uwItemId,  productInfoBody.getWebsite(), productInfoBody.getValue(), productInfoBody.getProductImage(), productInfoBody.getProductUrl(), productInfoBody.getSku(), productInfoBody.getClassification());
        }
        return returnTrackerMap;
    }

    @Override
    public Map<Integer, String> getReturnIdByStatusMapping(Integer incrementId){
        List<ReturnDetail> returnDetails = returnDetailRepository.findByIncrementId(incrementId);
        Map<Integer, String> returnIdStatusMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(returnDetails)){
            for(ReturnDetail returnDetail : returnDetails){
                Integer returnDetailId = returnDetail.getId();
                String status = getReturnOrderStatusById(returnDetailId);
                returnIdStatusMap.put(returnDetailId, status);
            }
        }else{
            List<ReturnOrder> returnOrders = returnOrderRepository.findByOrderNo(incrementId);
            if(!CollectionUtils.isEmpty(returnOrders)){
                for(ReturnOrder returnOrder : returnOrders){
                    Integer returnId = returnOrder.getReturnId();
                    String status = returnOrder.getStatus();
                    returnIdStatusMap.put(returnId, status);
                }
            }
        }
        return returnIdStatusMap;
    }

    @Override
    public List<ReturnDetail> getLatestReturns(Integer pageSize, Integer pageNo){
        return returnDetailRepository.getLatestReturns(pageSize, pageNo);
    }

    @Override
    public GetReturnsResponse getReturns(GetReturnsRequest returnsRequest) {
        List<ReturnDetailsVSM> returnDetails = getReturnDetailsByFilter(returnsRequest);
        return new GetReturnsResponse(returnDetails);
    }

    @Override
    public void updateOrderStatusForRefund(DualRefundRequest refundRequest) {

        log.info("[updateOrderStatusForRefund] start - order : {}", refundRequest.getIncrementId());
        if(CollectionUtils.isEmpty(refundRequest.getItemIds()) && refundRequest.getReturnId()!=null){
            com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO returnDetails = getReturnOrderDetails(IdentifierType.RETURN_ID.name(), String.valueOf(refundRequest.getReturnId()));
            refundRequest.setItemIds(returnDetails.getReturnOrderDTO().stream().map(com.lenskart.ordermetadata.dto.ReturnOrderDTO::getUwItemId).map(Long::intValue).collect(Collectors.toList()));
        }

        updateReturnOrderStatus(refundRequest);

        if(!(RefundRequest.STATUS.AWAITING_NEFT_INFO.equalsIgnoreCase(refundRequest.getStatus()))) {
            String refundStatus = "return_refunded_neft";
            if ("POS".equalsIgnoreCase(refundRequest.getSource()) && !refundRequest.isReceived()) {
                if (refundRequest.getRefundMethod().equalsIgnoreCase(RefundRequest.REFUND_METHOD.STORECREDIT))
                    refundStatus = "return_refunded_sc_recv_pending";
                else if (refundRequest.getRefundMethod().equalsIgnoreCase(RefundRequest.REFUND_METHOD.SOURCE) || refundRequest.getRefundMethod().equalsIgnoreCase(Refund.REFUND_METHOD.ONLINE))
                    refundStatus = "return_refunded_online_recv_pending";
            } else {
                if (refundRequest.getRefundMethod().equalsIgnoreCase(RefundRequest.REFUND_METHOD.STORECREDIT))
                    refundStatus = "return_refunded_store_credit";
                else if (refundRequest.getRefundMethod().equalsIgnoreCase(RefundRequest.REFUND_METHOD.SOURCE) || refundRequest.getRefundMethod().equalsIgnoreCase(Refund.REFUND_METHOD.ONLINE))
                    refundStatus = "return_refunded_online";
            }

            refundRequest.setStatus(refundStatus);
            saveRefundStatus(refundRequest, refundStatus);
            updateOrderStatus(refundRequest, refundStatus);
        }
        log.info("[updateOrderStatusForRefund] end - order : {}", refundRequest.getIncrementId());
    }

    @Override
    public void saveReturnOrder(ReturnOrder returnOrder) {
        log.info("[saveReturnOrder] order : {} - start", returnOrder.getOrderNo());
        returnOrderRepository.save(returnOrder);
        log.info("[saveReturnOrder] order : {} - end ", returnOrder.getOrderNo());
    }

    @Override
    public void saveReturnOrderItem(ReturnOrderItem returnOrderItem) {
        log.info("[saveReturnOrderItem] uwItemId : {} - start", returnOrderItem.getUwItemId());
        returnOrderItemRepository.save(returnOrderItem);
        log.info("[saveReturnOrderItem] uwItemId : {} - end ", returnOrderItem.getUwItemId());
    }

    @Override
    public void saveReturnOrderItemAll(List<ReturnOrderItem> returnOrderItems) {
        returnOrderItemRepository.saveAll(returnOrderItems);
        log.info("[saveReturnOrderItemAll] saved size : {} ", returnOrderItems.size());
    }

    @Override
    public void saveReturnReason(ReturnReason returnReason) {
        log.info("[saveReturnReason] uwItemId : {} - start", returnReason.getUwItemId());
        returnReasonOldRepository.save(returnReason);
        log.info("[saveReturnReason] uwItemId : {} - end ", returnReason.getUwItemId());
    }

    @Override
    public void saveReturnOrderAddressUpdate(ReturnOrderAddressUpdate returnOrderAddressUpdate) {
        log.info("[saveReturnOrderAddressUpdate] increment_id : {} - start", returnOrderAddressUpdate.getIncrement_id());
        returnOrderAddressUpdateOldRepository.save(returnOrderAddressUpdate);
        log.info("[saveReturnOrderAddressUpdate] increment_id : {} - end ", returnOrderAddressUpdate.getIncrement_id());
    }

    @Override
    public void saveReturnHistory(ReturnHistory returnHistory){
        log.info("[saveReturnHistory] returnId : {} - start", returnHistory.getEntityId());
        returnHistoryRepository.save(returnHistory);
        log.info("[saveReturnHistory] returnId : {} - end ", returnHistory.getEntityId());
    }

    @Override
    public ReturnDetail getAlternateB2BReturnDetails(Integer returnId) {
        return returnDetailRepository.getAlternateB2BReturnDetails(returnId);
    }

    private void updateOrderStatus(DualRefundRequest refundRequest, String refundStatus) {
        log.info("[updateOrderStatus] updating order status for returnId : {}, refundStatus : {}", refundRequest.getReturnId(), refundStatus);
        kafkaService.pushToKafka("order_status_update_queue",String.valueOf(refundRequest.getIncrementId()),refundRequest);
    }

    private void saveRefundStatus(DualRefundRequest refundRequest, String refundStatus) {
        log.info("[saveRefundStatus] updating return status in new returns for returnId : {}, refundStatus : {}", refundRequest.getReturnId(), refundStatus);
        ReturnEventDTO returnEventDTO = new ReturnEventDTO();
        returnEventDTO.setReturnId(Math.toIntExact(refundRequest.getReturnId()));
        returnEventDTO.setEvent(REFUND_COMPLETED);
        returnEventDTO.setRemarks("Refund Completed and order status will be changed to : "+refundStatus);
        kafkaService.pushToKafka("return_event_queue",String.valueOf(refundRequest.getIncrementId()),returnEventDTO);
    }

    private void updateReturnOrderStatus(DualRefundRequest refundRequest) {
        log.info("[updateReturnOrderStatus] order : {}, status : {}, itemIds : {}", refundRequest.getIncrementId(), refundRequest.getStatus(), refundRequest.getItemIds());
        String returnOrderStatus = null;
        ReturnDetail returnDetail = null;
        ReturnDetailItem returnDetailItem = null;
        String status = null;
        if (!TransactionType.REFUND_ON_CANCELLATION.getValue().equalsIgnoreCase(refundRequest.getTransactionType())) {
            if (null != refundRequest.getIncrementId() && null != refundRequest.getReturnId()) {
                for (Integer uwItemId : refundRequest.getItemIds()) {
                    returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(uwItemId);
                    if (returnDetailItem != null) {
                        Optional<ReturnDetail> returnDetailOpt = returnDetailRepository.findById(returnDetailItem.getReturnId());
                        if (returnDetailOpt.isPresent()) {
                            returnDetail = returnDetailOpt.get();
                            status = getReturnOrderStatusById(returnDetail.getId());
                            if (ReturnStatus.RETURN_EXCHANGE.getStatus().equalsIgnoreCase(status)) {
                                return;
                            }
                            if (!RefundPredicate.IS_RETURN_IN_DRAFTED_REJECTED_STATE.test(status)
                                    && IS_RETURN_NOT_IN_RPU_CYCLE.test(status)) {
                                if (Refund.STATUS.REFUND_CANCELLED.equalsIgnoreCase(refundRequest.getStatus())) {
                                    returnOrderStatus = ReturnStatus.RETURN_RECEIVED.getStatus();
                                } else {
                                    if (com.lenskart.orderops.model.ReturnOrder.RECEIVING_FLAG.NO.equalsIgnoreCase(returnDetail.getReceivingFlag())) {
                                        refundRequest.setReceived(false);
                                    } else {
                                        refundRequest.setReceived(true);
                                    }
                                    log.info("return order status is ===---========-----===== {}", returnDetail.getReceivingFlag());
                                    returnOrderStatus = ReturnStatus.RETURN_REFUNDED.getStatus();
                                }

                                log.info("[updateReturnOrderStatus] order : {} , returnOrderStatus : {}", refundRequest.getIncrementId(), returnOrderStatus);
                                if (StringUtils.isNotEmpty(returnOrderStatus)) {
                                    refundRequest.setReturnStatus(returnOrderStatus);
                                    Integer requestId = returnEventService.getRequestId(returnDetail.getId());
                                    returnEventService.createReturnEvent(requestId, returnDetail.getId(), returnOrderStatus, returnOrderStatus);
                                }

                            }
                            if (RefundPredicate.IS_RETURN_IN_DRAFTED_REJECTED_STATE.test(returnOrderStatus)) {
                                notifyRefundToCustomer(refundRequest, returnOrderStatus);
                            }
                        }
                    }
                }
            }
        }

    }

    private void notifyRefundToCustomer(DualRefundRequest refundRequest, String status) {
        log.info("notifying POS for refund action for approval return cycle as retrun status is " + status
                + " for increment id " + refundRequest.getIncrementId());

        kafkaService.pushToKafka("notify_refund_customer_queue",String.valueOf(refundRequest.getIncrementId()),refundRequest);
    }

    private List<ReturnDetailsVSM> getReturnDetailsByFilter(GetReturnsRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("[getReturnDetailsByFilter] Started getReturnDetailsByFilter at {}", startTime);

        if (request.getOrderId() == null) {
            try {
                if (request.getFromSearch() == null || request.getToSearch() == null) {
                    Date today = new Date();
                    request.setFromSearch(DateUtil.getStartOfDay(today));
                    request.setToSearch(DateUtil.getEndOfDay(today));
                }
                DateUtil.validateDateRange(request.getFromSearch(), request.getToSearch());
            } catch (IllegalArgumentException e) {
                log.error("[getReturnDetailsByFilter] Invalid date range, max 15 days diff is allowed", e);
            }
        }

        String returnStatus = request.getReturnStatus();
        String optDate = request.getOptDate();
        String returnType = request.getReturnType();
        Integer orderNo = request.getOrderId();
        String qcStatus = request.getQcStatus();
        String agentEmail = request.getAgentEmail();
        String bulkType = request.getOrderType() != null ? request.getOrderType().equals("bulk") ? request.getOrderType() : null : null;
        Date fromDate = request.getFromSearch();
        Date toDate = request.getToSearch();
        String returnReasonsPrimary = request.getReturnReasonsPrimary();
        String returnReasonsSecondary = request.getReturnReasonsSecondary();
        Boolean isInsurance = "1".equals(request.getInsuranceOrderType()) ? Boolean.TRUE : "2".equals(request.getInsuranceOrderType()) ? false : null;
        String followedUp = request.getFollowedUp();
        String reportFilterFld1 = request.getReportFilterFld1();
        String reportFilterFld2 = request.getReportFilterFld2();

        List<String> notIncludeEventTypes = Arrays.asList("history", "new_reverse_pickup", "reference_id_issued", "awb_assigned", "cancelled");
        List<String> eventTypesUpper = Arrays.stream(ReturnStatus.values())
                .filter(e -> !notIncludeEventTypes.contains(e.getStatus()))
                .map(ReturnStatus::getName)
                .toList();

        log.info("[getReturnDetailsByFilter] Valid eventTypes upper: {}", eventTypesUpper);

        long newReturnStart = System.currentTimeMillis();
        List<ReturnDetailsVSM> newReturnDetails = returnDetailRepository.fetchReturnOrders(
                returnStatus, optDate, returnType, orderNo, qcStatus, agentEmail, bulkType,
                fromDate, toDate, returnReasonsPrimary, returnReasonsSecondary, isInsurance,
                followedUp, reportFilterFld1, reportFilterFld2, eventTypesUpper
        );
        long newReturnEnd = System.currentTimeMillis();
        log.info("[getReturnDetailsByFilter] New return details fetched in {} ms", (newReturnEnd - newReturnStart));

        newReturnDetails.forEach(nr ->
                Optional.ofNullable(nr.getStatus())
                        .map(status -> ReturnStatus.valueOf(status.toUpperCase()).getStatus())
                        .ifPresent(nr::setStatus)
        );

        List<String> eventTypesLower = Arrays.stream(ReturnStatus.values())
                .map(ReturnStatus::getStatus)
                .filter(status -> !notIncludeEventTypes.contains(status))
                .toList();

        log.info("[getReturnDetailsByFilter] Valid eventTypes lower: {}", eventTypesLower);

        long oldReturnStart = System.currentTimeMillis();
        List<ReturnDetailsVSM> oldReturnDetails = returnOrderRepository.fetchReturnOrders(
                returnStatus, optDate, returnType, orderNo, qcStatus, agentEmail, bulkType,
                fromDate, toDate, returnReasonsPrimary, returnReasonsSecondary, isInsurance,
                followedUp, reportFilterFld1, reportFilterFld2, eventTypesLower
        );
        long oldReturnEnd = System.currentTimeMillis();
        log.info("[getReturnDetailsByFilter] Old return details fetched in {} ms", (oldReturnEnd - oldReturnStart));

        List<ReturnDetailsVSM> combinedReturnDetails = new ArrayList<>(oldReturnDetails.size() + newReturnDetails.size());
        combinedReturnDetails.addAll(oldReturnDetails);
        combinedReturnDetails.addAll(newReturnDetails);

        long endTime = System.currentTimeMillis();
        log.info("[getReturnDetailsByFilter] Completed getReturnDetailsByFilter at {}", endTime);
        log.info("[getReturnDetailsByFilter] Total execution time: {} ms", (endTime - startTime));

        return combinedReturnDetails;
    }


    @Override
    public GetReturnReportResponse getReturnReportDetails(GetReturnsRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("[getReturnReportDetails] Started getReturnReportDetails at {}", startTime);

        GetReturnReportResponse response = new GetReturnReportResponse();

        if (request.getOrderId() == null) {
            try {
                if (request.getFromSearch() == null || request.getToSearch() == null) {
                    Date today = new Date();
                    request.setFromSearch(DateUtil.getStartOfDay(today));
                    request.setToSearch(DateUtil.getEndOfDay(today));
                }
                DateUtil.validateDateRange(request.getFromSearch(), request.getToSearch());
            } catch (IllegalArgumentException e) {
                response.setError(e.getMessage());
            }
        }

        // Extract and preprocess request parameters
        String returnStatus = request.getReturnStatus();
        String optDate = request.getOptDate();
        String returnType = request.getReturnType();
        Integer orderNo = request.getOrderId();
        String category = request.getCategory();
        String storeType = request.getStoreType();
        String country = request.getCountry();
        String qcStatus = request.getQcStatus();
        String agentEmail = request.getAgentEmail();
        String orderType = request.getOrderType();
        Date fromDate = request.getFromSearch();
        Date toDate = request.getToSearch();
        String returnReasonsPrimary = request.getReturnReasonsPrimary();
        String returnReasonsSecondary = request.getReturnReasonsSecondary();
        Boolean isInsurance = request.getInsuranceOrderType() != null ? request.getInsuranceOrderType().equals("1") : null;
        String followedUp = request.getFollowedUp();
        String reportFilterFld1 = request.getReportFilterFld1();
        String reportFilterFld2 = request.getReportFilterFld2();
        String reportFilterFld3 = StringUtils.isEmpty(request.getReportFilterFld3()) ? "12" : request.getReportFilterFld3();
        int timeDiffFlag = Integer.parseInt(reportFilterFld3);

        // Define event type filtering logic
        List<String> notIncludeEventTypes = Arrays.asList("history", "new_reverse_pickup", "reference_id_issued", "awb_assigned", "cancelled");
        List<String> eventTypes = Arrays.stream(ReturnStatus.values())
                .filter(e -> !notIncludeEventTypes.contains(e.getStatus()))
                .map(ReturnStatus::getName)
                .toList();
        log.info("[getReturnReportDetails] Valid eventTypes: {}", eventTypes);

        Integer classification = null;
        if (StringUtils.isNotEmpty(category)) {
            if (category.equals("EG")) {
                classification = 11355;
            } else if (category.equals("SG")) {
                classification = 11357;
            } else if (category.equals("CL")) {
                classification = 11354;
            }
        }

        List<String> franchiseList = null;

        if(StringUtils.isNotEmpty(storeType)) {
            if(storeType.equals("FOFO")) {
                franchiseList = Arrays.asList("1", "2");
            } else {
                franchiseList = Collections.singletonList(storeType);
            }
        }

//        // Filter event types based on optDate logic
//        if ("lastFollowup".equals(optDate)) {
//            eventTypes = Collections.singletonList(returnStatus);
//        } else if ("statusChange".equals(optDate)) {
//            eventTypes = Collections.singletonList(returnStatus);
//        } else if (optDate != null && !optDate.equals("All")) {
//            eventTypes = Collections.singletonList("return_closed");
//        }

        List<String> paymentMethods = getPaymentMethods(request.getPaymentMethod());

        Pageable pageable = PageRequest.of(request.getPageNumber(), request.getPageSize());

        long queryStartTime = System.currentTimeMillis();
        // Fetch results using a simplified query with pagination
        Page<Object[]> newReturnDetails =  combinedReturnDetailsRepository.fetchCombinedReturnOrders(
                returnStatus,
                optDate,
                fromDate,
                toDate,
                returnType,
                orderNo,
                qcStatus,
                agentEmail,
                orderType,
                returnReasonsPrimary,
                returnReasonsSecondary,
                isInsurance,
                followedUp,
                reportFilterFld1,
                reportFilterFld2,
                eventTypes,
                classification,
                franchiseList,
                country,
                paymentMethods,
                pageable
        );

        long queryEndTime = System.currentTimeMillis();
        log.info("[getReturnReportDetails] Query executed in {} ms, fetched {} records", (queryEndTime - queryStartTime), newReturnDetails.getContent().size());

        Pagination pagination = new Pagination();
        pagination.setPage(pageable.getPageNumber());
        pagination.setPageSize(pageable.getPageSize());
        pagination.setTotalRecords(newReturnDetails.getTotalElements());

        long processingStartTime = System.currentTimeMillis();
        List<ReturnDetailsVSM> returnDetailsVSMS = getReturnDetailsVSMs(newReturnDetails);
        long processingEndTime = System.currentTimeMillis();
        log.info("[getReturnReportDetails] Processing return details took {} ms", (processingEndTime - processingStartTime));
//        List<ReportDetail> reportDetails = ReportGenerator.processReturns(returnDetailsVSMS, timeDiffFlag, pageable.getOffset(), pagination.getTotalRecords(), pagination.getPageSize());

        List<ReturnDetailVSM> returnDetails = convertToReturnDetailVSMList(returnDetailsVSMS);
        response.setReturnDetails(returnDetails);
        response.setPagination(pagination);
//        response.setReportDetails(reportDetails);
        long endTime = System.currentTimeMillis();
        log.info("[getReturnReportDetails] Completed getReturnReportDetails at {}, total execution time: {} ms", endTime, (endTime - startTime));

        return response;
    }

    private List<ReturnDetailsVSM> getReturnDetailsVSMs(Page<Object[]> newReturnDetails) {
        return newReturnDetails.getContent().stream()
                .map(row -> {
                    String status;
                    try {
                        status = ReturnStatus.valueOf(getString(row[11])).getStatus();
                    } catch (Exception e) {
                        status = getString(row[11]);
                    }
                    return new ReturnDetailsVSM(
                            getInteger(row[0]),                        // orderNo
                            getInteger(row[1]),                        // returnId
                            getString(row[2]),                         // primaryReason
                            getString(row[3]),                         // secondaryReason
                            getLong(row[4]),                           // timeDiff
                            getString(row[5]),                         // receivingFlag
                            getString(row[6]),                         // roiStatus
                            getDate(row[7]),                           // statusChangeDate
                            getString(row[8]),                         // returnType
                            getString(row[9]),                         // agentEmail
                            getString(row[10]),                        // reverseAwb
                            status,                        // status
                            getString(row[12]),                        // qcStatus
                            getDate(row[13]),                          // returnCreateDatetime
                            getInteger(row[14]),                       // uwItemId
                            getInteger(row[15])                        // classification
                    );})
                .toList();
    }

    @Override
    public boolean updateReturnAgent(UpdateReturnAgentRequest request) {
        List<Integer> returnIds = new ArrayList<>();
        List<String> emails = new ArrayList<>();
        for (ReturnAssignmentDTO dto : request.getAssignments()) {
            returnIds.add(dto.getReturnId());
            emails.add(dto.getAgentId());
        }
        return (boolean) updateAgentEmailsInReturn(new UpdateAgentEmailReturnRequest(returnIds, emails)).get(Constant.SUCCESS);
    }

    @Override
    public List<ReturnDetail> getReturnOrdersByFacilityAndDateRange(String facility, Date fromTime, Date toTime, Integer pageSize, Integer pageNo) {
        log.info("[getReturnOrdersByFacilityAndDateRange] facility : {}, fromTime : {}, toTime : {}, pageSize : {}, pageNo : {}", facility, fromTime, toTime, pageSize, pageNo);
        List<ReturnDetail> returnDetails = returnDetailRepository.getReturnOrdersByFacilityAndDateRange(facility, fromTime, toTime, pageSize, pageNo);
        if (CollectionUtils.isEmpty(returnDetails)) {
            return returnOrderRepository.getReturnOrdersByFacilityAndDateRange(facility, fromTime, toTime, pageSize, pageNo).stream().map(this::convertToReturnDetail).toList();
        }
        return returnDetails;
    }

    @Override
    public void updateReturnReason(UpdateReturnReasonRequest updateReturnReasonRequest) {
        if (StringUtils.isNotEmpty(updateReturnReasonRequest.getReturnReason()) && updateReturnReasonRequest.getReturnItemId() != null) {
            Optional<ReturnDetailItem> returnDetailItem = returnDetailItemRepository.findById(updateReturnReasonRequest.getReturnItemId());
            if (returnDetailItem.isPresent()) {
                ReturnDetailItem returnDetailItem1 = returnDetailItem.get();
                returnDetailItem1.setReasonForReturn(updateReturnReasonRequest.getReturnReason());
                returnDetailItemRepository.save(returnDetailItem1);
            }
        }
        if (updateReturnReasonRequest != null && updateReturnReasonRequest.getOrderId() != null) {
            List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByUwItemIdInOrderByIdDesc(List.of(updateReturnReasonRequest.getUwItemId()));
            if (!CollectionUtils.isEmpty(returnDetailItems)) {
                ReturnDetailReason returnDetailReason = new ReturnDetailReason();
                returnDetailReason.setPrimaryReason(updateReturnReasonRequest.getPrimaryReason());
                returnDetailReason.setSecondaryReason(updateReturnReasonRequest.getSecondaryReason());
                returnDetailReason.setOrderId(updateReturnReasonRequest.getOrderId());
                returnDetailReason.setUwItemId(updateReturnReasonRequest.getUwItemId());
                returnDetailReason.setUser(updateReturnReasonRequest.getUser());
                returnDetailReason.setSource(updateReturnReasonRequest.getSource());
                returnDetailReason.setCreatedAt(new Date());
                returnDetailReason.setReturnId(returnDetailItems.get(0).getReturnId());
                returnReasonRepository.save(returnDetailReason);
            }
        }

    }

    @Override
    public Map<String, Object> updateAgentEmailsInReturn(UpdateAgentEmailReturnRequest request) {
        Map<String, Object> response = new HashMap<>();
        response.put(Constant.SUCCESS, false);
        if (request != null && !CollectionUtils.isEmpty(request.getReturnIds()) && !CollectionUtils.isEmpty(request.getEmails())) {
            List<ReturnDetail> returnDetails = returnDetailRepository.findByReturnIdIn(request.getReturnIds());
            if (!CollectionUtils.isEmpty(returnDetails)) {
                for (int i = 0 ; i < returnDetails.size() ; i++) {
                    ReturnDetail returnDetail = returnDetails.get(i);
                    String oldEmail = returnDetail.getAgentEmail();
                    String newEmail = request.getEmails().get(i);
                    returnDetail.setAgentEmail(newEmail);
                    returnDetailRepository.save(returnDetail);

                    ReturnEvent returnEvent = new ReturnEvent();
                    returnEvent.setReturnRequestId(returnDetail.getRequestId());
                    returnEvent.setReturnId(returnDetail.getId());
                    returnEvent.setEvent("AGENT_EMAIL_UPDATED");
                    returnEvent.setRemarks("{ \"oldEmail\" : \"" + oldEmail + "\", \"newEmail\" : \"" + newEmail + "\" }");
                    returnEvent.setCreatedAt(new Date());
                    returnEvent.setSource("updateAgentEmailsInReturn API");
                    returnEventRepository.save(returnEvent);
                    response.put(Constant.SUCCESS, true);
                }
            } else {
                response.put(Constant.SUCCESS, true);
            }

        }
        return response;
    }

    @Override
    public int updateReturnOrderItem(UpdateReturnOrderItemRequest updateReturnOrderItemRequest) {
        String newStatus = updateReturnOrderItemRequest.getStatus();
        Integer csohUpdatedFlag = updateReturnOrderItemRequest.getCsohUpdatedFlag();
        UpdateReturnOrderItemRequest.UpdateReturnItemConditions condition = updateReturnOrderItemRequest.getCondition();

        if (condition == null) {
            throw new IllegalArgumentException("Condition cannot be null");
        }

        if (condition.getId() != null && StringUtils.isNotEmpty(condition.getStatus())) {
            return returnDetailItemRepository.updateStatusAndCsohUpdatedFlagWhereIdAndStatus(newStatus, csohUpdatedFlag, condition.getId(), condition.getStatus());
        } else if (condition.getId() != null && !CollectionUtils.isEmpty(condition.getStatusNotIn())) {
            return returnDetailItemRepository.updateStatusAndCsohUpdatedWhereIdAndStatusNotIn(newStatus, csohUpdatedFlag, condition.getId(), condition.getStatusNotIn());
        }

        return 0; // No update criteria matched
    }

    // Utility methods for null-safe conversions
    private Integer getInteger(Object value) {
        return value != null ? Integer.valueOf(String.valueOf(value)) : null;
    }

    private Long getLong(Object value) {
        return value != null ? Float.valueOf(String.valueOf(value)).longValue() : null;
    }

    private String getString(Object value) {
        return value != null ? String.valueOf(value) : null;
    }

    private Date getDate(Object value) {
        return value instanceof Date ? (Date) value : null;
    }

    private List<String> getPaymentMethods(String paymentMethod) {
        Map<String, List<String>> paymentMethods = Map.of(
                "COD", List.of("cashondelivery"),
                "Online", List.of("payu_shared", "citrus", "paytm", "ccavenuepay", "seamless", "paytm_cc", "payuautodebit"),
                "Storecredit", List.of("storecredit"),
                "OfflineCard/Wallet", List.of("offlinecard", "offlinepaytm", "storephonepe", "storecard", "storepaytm", "offlineairtel", "storeairtel", "offlinepart"),
                "OfflineCash", List.of("offlinecash", "storecash"),
                "Exchange", List.of("exchangep"),
                "Other", List.of("payzero", "giftvoucher", "lenskartwallet")
        );

        return (paymentMethod != null && !paymentMethod.isEmpty() && !paymentMethod.contains("All"))
                ? paymentMethods.getOrDefault(paymentMethod, null) : null;
    }

    private List<ReturnDetailVSM> convertToReturnDetailVSMList(List<ReturnDetailsVSM> paginatedList) {
        return CollectionUtils.isEmpty(paginatedList) ? Collections.emptyList() : paginatedList.stream().map(this::convertToReturnDetailVSM).toList();
    }

    private ReturnDetailVSM convertToReturnDetailVSM(ReturnDetailsVSM returnDetail) {
        ReturnDetailVSM returnDetailVSM = new ReturnDetailVSM();
        returnDetailVSM.setReturnId(returnDetail.getReturnId());
        returnDetailVSM.setReturnType(returnDetail.getReturnType());
        returnDetailVSM.setReturnStatus(returnDetail.getStatus());
        returnDetailVSM.setReturnDate(returnDetail.getReturnCreateDatetime());
        returnDetailVSM.setQcStatus(returnDetail.getQcStatus());
        returnDetailVSM.setStatusChangeDate(returnDetail.getStatusChangeDate());
        returnDetailVSM.setIncrementId(returnDetail.getOrderNo());
        returnDetailVSM.setAgentId(returnDetail.getAgentEmail());
        returnDetailVSM.setCategory(getCategory(returnDetail.getClassification()));
        returnDetailVSM.setStoreName("N/A");
        return returnDetailVSM;
    }

    private String getCategory(Integer classification) {
        if (classification == null) {
            return null;
        }
        switch (classification) {
            case 11357:
                return "SG";
            case 11354:
                return "CL";
            case 11355:
            default:
                return "EG";
        }
    }

//    @Override
//    public Page<ReturnDetail> getReturnOrdersCombined(String status, Integer orderNo, String reverseCourier,
//                                                      String facility, Date startDate, Date endDate, Pageable pageable) {
//        // Create specifications for both new and old tables
//        Specification<ReturnDetail> specNew = ReturnDetailSpecification.getFilteredReturns(status, orderNo, reverseCourier, facility, startDate, endDate);
//        Specification<ReturnOrder> specOld = ReturnDetailSpecification.getFilteredReturnOrders(status, orderNo, reverseCourier, facility, startDate, endDate);
//
//        // Fetch paginated results from both tables
//        Page<ReturnDetail> newPage = returnDetailRepository.findAll(specNew, pageable);
//        Page<ReturnOrder> oldPage = returnOrderRepository.findAll(specOld, pageable);
//
//        // Convert old table records to ReturnDetail format
//        List<ReturnDetail> newResults = newPage.getContent();
//        List<ReturnDetail> oldResults = oldPage.getContent().stream().map(this::convertToReturnDetail).toList();
//
//        // Merge and sort by returnCreateDatetime
//        List<ReturnDetail> mergedResults = Stream.concat(newResults.stream(), oldResults.stream())
//                .sorted(Comparator.comparing(ReturnDetail::getReturnCreateDatetime).reversed())
//                .collect(Collectors.toList());
//
//        // Handle cross-source pagination
//        int start = Math.toIntExact(pageable.getOffset());
//        int end = Math.min((start + pageable.getPageSize()), mergedResults.size());
//        List<ReturnDetail> paginatedList = mergedResults.subList(start, end);
//
//        // Correct total elements count
//        long totalNew = newPage.getTotalElements();
//        long totalOld = oldPage.getTotalElements();
//        long totalElements = totalNew + totalOld;  // FIXED total count
//
//        // Return properly paginated response
//        return new PageImpl<>(paginatedList, pageable, totalElements);
//    }

    @Override
    public PagedResponse<ReturnDetail> getReturnOrdersCombined(
            String status, Integer orderNo, String reverseCourier, String facility,
            Date startDate, Date endDate, Integer lastFetchedId, int pageSize) {

        List<String> eventTypesUpper = Arrays.stream(ReturnStatus.values())
                .map(ReturnStatus::getName)
                .toList();
        // Fetch from New Table (return_detail)
        Specification<ReturnDetail> specNew = ReturnDetailSpecification.getFilteredReturns(status, orderNo, reverseCourier, facility, startDate, endDate, eventTypesUpper);
        List<ReturnDetail> newResults = pageSize == 0 ? returnDetailRepository.findAll(specNew) : lastFetchedId == null
                ? returnDetailRepository.findAll(specNew, PageRequest.of(0, pageSize, Sort.by(Sort.Direction.DESC, "id"))).getContent()
                : returnDetailRepository.findAll(specNew.and((root, query, cb) ->
                        cb.lessThan(root.get("id"), lastFetchedId)),
                PageRequest.of(0, pageSize, Sort.by(Sort.Direction.DESC, "id"))).getContent();

        // Fetch from Old Table (return_order)
        Specification<ReturnOrder> specOld = ReturnDetailSpecification.getFilteredReturnOrders(status, orderNo, reverseCourier, facility, startDate, endDate);
        List<ReturnDetail> oldResults = pageSize == 0 ? returnOrderRepository.findAll(specOld).stream().map(this::convertToReturnDetail).toList() : lastFetchedId == null
                ? returnOrderRepository.findAll(specOld, PageRequest.of(0, pageSize, Sort.by(Sort.Direction.DESC, "returnId")))
                .map(this::convertToReturnDetail).getContent()
                : returnOrderRepository.findAll(specOld.and((root, query, cb) ->
                                cb.lessThan(root.get("returnId"), lastFetchedId)),
                        PageRequest.of(0, pageSize, Sort.by(Sort.Direction.DESC, "returnId")))
                .map(this::convertToReturnDetail).getContent();

        // Merge and sort by id DESC
        Stream<ReturnDetail> mergedStream = Stream.concat(newResults.stream(), oldResults.stream())
                .sorted(Comparator.comparing(ReturnDetail::getId).reversed()); // Sorting by id DESC
        List<ReturnDetail> mergedResults = (pageSize > 0) ? mergedStream.limit(pageSize).toList() : mergedStream.toList();

        // Determine new lastFetchedId (smallest ID from this batch)
        Integer newLastFetchedId = null;
        if (pageSize > 0) {
            newLastFetchedId = mergedResults.isEmpty() ? null : mergedResults.get(mergedResults.size() - 1).getId();
        }

        // Check if there are more results to fetch
        boolean hasNext = mergedResults.size() == pageSize;

        // Return custom paginated response
        return new PagedResponse<>(mergedResults, newLastFetchedId, hasNext);
    }

    @Override
    public List<ReturnOrderItem> findReturnOrderItemBatch(int lastProcessedId, int BATCH_SIZE) {
        return returnOrderItemRepository.findReturnOrderItemBatch(lastProcessedId, BATCH_SIZE);
    }

    @Override
    public ReversePickupPincode saveReversePickupPincode(ReversePickupPincode newData) {
        Optional<ReversePickupPincode> existingOpt =
                reversePickupPincodeRepository.findByCourierAndPincode(newData.getCourier(), newData.getPincode());
        if (existingOpt.isPresent()) {
            ReversePickupPincode existing = existingOpt.get();
            existing.setPriority(newData.getPriority());
            existing.setStatus(newData.getStatus());
            existing.setTat(newData.getTat());
            existing.setCpId(newData.getCpId());
            existing.setCpName(newData.getCpName());
            existing.setIsQcAtDoorstep(newData.getIsQcAtDoorstep());

            return reversePickupPincodeRepository.save(existing);
        } else {
            return reversePickupPincodeRepository.save(newData);
        }
    }

    @Override
    public ReverseCourierMapping saveReverseCourierMapping(ReverseCourierMapping reverseCourierMapping) {
        return reverseCourierMappingRepository.save(reverseCourierMapping);
    }

    @Override
    public RefundRules saveRefundRules(RefundRules refundRules) {
        return refundRulesRepository.save(refundRules);
    }

    @Override
    public List<ReturnOrderItemDTO> findReturnOrderItems(List<Integer> returnIds) {
        if (CollectionUtils.isEmpty(returnIds)) {
            return Collections.emptyList();
        }

        List<ReturnDetailItem> detailItems = returnDetailItemRepository.findByReturnIdIn(returnIds);

        Set<Integer> foundIds = detailItems.stream()
                .map(ReturnDetailItem::getReturnId)
                .collect(Collectors.toSet());

        List<Integer> missingIds = returnIds.stream()
                .filter(id -> !foundIds.contains(id))
                .toList();

        List<ReturnOrderItem> fallbackItems = returnOrderItemRepository.findByReturnIdIn(missingIds);

        List<ReturnDetailItem> fallbackConverted = fallbackItems.stream()
                .map(this::convertToReturnDetailItem)
                .toList();

        List<ReturnDetailItem> allItems = new ArrayList<>(detailItems);
        allItems.addAll(fallbackConverted);

        return returnOrderItemMapper.toDtoList(allItems);
    }

    @Override
    public List<ReverseCourierDetailDTO> getReverseCourierDetailDTO(List<Integer> returnIds) {
        if (CollectionUtils.isEmpty(returnIds)) {
            return Collections.emptyList();
        }

        List<ReturnCourierDetail> courierDetails = returnCourierDetailRepository.findByReturnIdIn(returnIds);

        return reverseCourierDetailMapper.toDtoList(courierDetails);
    }

    @Override
    public List<ReturnReasonDTO> getReturnReasonDTO(List<Integer> returnIds) {
        if (CollectionUtils.isEmpty(returnIds)) {
            return Collections.emptyList();
        }

        List<ReturnDetailReason> newReasons = returnReasonRepository.findByReturnIdIn(returnIds);

        Set<Integer> foundNew = newReasons.stream()
                .map(ReturnDetailReason::getReturnId)
                .collect(Collectors.toSet());

        List<Integer> missingIds = returnIds.stream()
                .filter(id -> !foundNew.contains(id))
                .toList();

        List<ReturnReason> oldReasons = returnReasonOldRepository.findByReturnIdIn(missingIds);

        List<ReturnReasonDTO> newReasonDTOs = returnReasonMapper.toDto(newReasons);
        List<ReturnReasonDTO> oldReasonDTOs = returnReasonMapper.toDtoOld(oldReasons);


        List<ReturnReasonDTO> allItems = new ArrayList<>(newReasonDTOs);
        allItems.addAll(oldReasonDTOs);

        return allItems;
    }

    @Override
    public String updateModelPrompt(Integer id, String prompt) {

        int isUpdated= modelPromptRepository.updateModelPrompt(prompt,id);

        if(isUpdated>0){
            return "success";
        }else{
            return "faliure";
        }
    }

    @Override
    public ReturnStatusResponse getReturnResponse(Integer magentoItemId) {
        log.info("[getReturnResponse] magentoItemId : {}", magentoItemId);
        
        ReturnRequest returnRequest = returnRequestRepository
                .findTop1ByIdentifierValueOrderByCreatedAtDesc(String.valueOf(magentoItemId));

        if (returnRequest == null) {
            log.warn("[getReturnResponse] No ReturnRequest found for magentoItemId: {}", magentoItemId);
            return new ReturnStatusResponse();
        }


        List<Integer> returnIds = returnEventService.getReturnIds(returnRequest.getId());
        if (returnIds.isEmpty()) {
            log.warn("[getReturnResponse] No returnIds found for returnRequestId: {}", returnRequest.getId());
            return new ReturnStatusResponse();
        }



//        ReturnDetail returnDetail = returnDetailRepository.findFirstByIdInOrderByReturnCreateDatetimeDesc(returnIds);
//        if (returnDetail == null) {
//            log.warn("[getReturnResponse] No ReturnDetail found for returnIds: {}", returnIds);
//            return new ReturnStatusResponse();
//        }
//
//        String returnStatus = getReturnOrderStatus(returnDetail);


        ReturnMetaDataDTO returnMetaDataDTO = getVirtualReturn(returnIds);
        Integer virtualReturn = returnMetaDataDTO.getReturnId();
        String returnStatus = getReturnOrderStatusById(virtualReturn);

        ReturnStatusResponse response = new ReturnStatusResponse();
        if (!newReturnStatus.contains(returnStatus)) {
            log.info("[getReturnResponse] setting returnStatusResponse for magentoItemId: {}", magentoItemId);
            response.setReturnId(virtualReturn);
            response.setReturnCreationDate(returnMetaDataDTO.getReturnCreationDateTime());
            response.setStatus(returnStatus);
            response.setReturnActive(true);
        }

        log.info("[getReturnResponse] magentoItemId: {}, returnId : {} status: {}",
                magentoItemId, virtualReturn, returnStatus);

        return response;

    }

    private ReturnMetaDataDTO getVirtualReturn(List<Integer> returnIds) {
        ReturnMetaDataDTO returnMetaDataDTO = new ReturnMetaDataDTO();
        Integer returnId = -1;
        Date returnCreationDatetime = null;
        try {
            if (CollectionUtils.isEmpty(returnIds)) {
                return null;
            }

            for(Integer returnsId : returnIds){
                List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByReturnId(returnsId);
                if(!CollectionUtils.isEmpty(returnDetailItems)){
                    ReturnDetailItem returnDetailItem = returnDetailItems.get(0);
                    if(!Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(returnDetailItem.getProductDeliveryType())){
                        returnId = returnDetailItem.getReturnId();
                        returnCreationDatetime = returnDetailItem.getReturnCreateDatetime();
                    }else{
                        Optional<ReturnDetail> returnDetail = returnDetailRepository.findById(returnId);
                        if(returnDetail.isPresent()){
                            String facilityCode = returnDetail.get().getFacilityCode();
                            if(!facilityCodeList.contains(facilityCode)){
                                returnId = returnDetail.get().getId();
                                returnCreationDatetime = returnDetail.get().getReturnCreateDatetime();
                            }
                        }
                    }
                }else{
                    List<ReturnOrderItem> returnOrderItems = returnOrderItemRepository.findByReturnId(returnsId);
                    if(!CollectionUtils.isEmpty(returnOrderItems)){
                        ReturnOrderItem returnOrderItem = returnOrderItems.get(0);
                        if(!Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(returnOrderItem.getProductDeliveryType())){
                            returnId = returnOrderItem.getReturnId();
                            returnCreationDatetime = returnOrderItem.getReturnCreateDatetime();
                        }else{
                            Optional<ReturnOrder> returnDetail = returnOrderRepository.findById(returnId);
                            if(returnDetail.isPresent()){
                                String facilityCode = returnDetail.get().getFacilityCode();
                                if(!facilityCodeList.contains(facilityCode)){
                                    returnId = returnDetail.get().getReturnId();
                                    returnCreationDatetime = returnDetail.get().getReturnCreateDatetime();
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("[getVirtualReturn] Exception:" + e);
        }
        returnMetaDataDTO.setReturnId(returnId);
        returnMetaDataDTO.setReturnCreationDateTime(returnCreationDatetime);
        log.info("[getVirtualReturn] returnMetaDataDTO : {}", returnMetaDataDTO);
        return returnMetaDataDTO;
    }

}
