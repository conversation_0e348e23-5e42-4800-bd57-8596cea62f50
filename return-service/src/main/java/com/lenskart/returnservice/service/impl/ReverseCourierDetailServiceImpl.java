package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.request.PickupAddressDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.response.DispensingDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import com.lenskart.reversemodel.request.kafka.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lenskart.returncommon.model.enums.ReturnStatus.NEW_REVERSE_PICKUP;
import static com.lenskart.returncommon.utils.Constant.ADDRESS_TYPE.SHIPPING;
import static com.lenskart.returncommon.utils.Constant.CLASSIFICATION.SUNGLASS;
import static com.lenskart.returncommon.utils.Constant.COURIER.LKART;
import static com.lenskart.returncommon.utils.Constant.FIELDEZ_STATUS.DISPENSING_DONE_STATUSES;
import static com.lenskart.returncommon.utils.Constant.HUB_DETAILS.HUB_SEARCH_BY_HUB_ID;
import static com.lenskart.returncommon.utils.Constant.RETURN_DAYS.RETURN_CREATE_DATE_CUT_OFF_DAYS;
import static com.lenskart.returncommon.utils.Constant.RETURN_DAYS.RETURN_CREATE_DATE_CUT_OFF_DAYS_FALLBACK;
import static com.lenskart.returncommon.utils.Constant.RETURN_METHOD.RPU;
import static com.lenskart.returncommon.utils.Constant.STORE_ID.JJONLINE_STOREID;
import static com.lenskart.returncommon.utils.Constant.VSM_COMMENTS.*;

@Service
@Slf4j
public class ReverseCourierDetailServiceImpl implements IReverseCourierDetailService {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;

    @Autowired
    private ReverseCourierMappingRepository reverseCourierMappingRepository;

    @Autowired
    ReversePickupPincodeRepository reversePickupPincodeRepository;

    @Autowired
    private IOrderUtilsService orderUtilsService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Value("${dealskart.manesar.facility}")
    private String lenskartFacility;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private IDoorStepQcService doorStepQcService;

    @Autowired
    RedisTemplate<String,Object> redisTemplate;

    @Autowired
    private QcAndWarrantyRulesRepository qcAndWarrantyRulesRepository;

    @Autowired
    private IKafkaService kafkaService;

    public final String QC_AND_WARRANTY_RULE_CONFIG="qc_and_warranty_rule_config";

    private static final String NON_QC_VSM_COMMENT= "Non-QC based pickup arranged for return id:";
    private static final String QC_VSM_COMMENT="QC based pickup arranged for return id:";
    private static final String NON_QC_REASON_COMMENT="Reason: {Not eligible for qc based pickup as none of the rules matched}";
    private  static final double INVOICE_VALUE = 1500.0;

    @Autowired
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @Autowired
    private IReturnReasonService returnReasonService;

    @Autowired
    private PrimaryReturnReasonsRepository primaryReturnReasonsRepository;

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Autowired
    private ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Override
    public ReverseCourierDetail getReverseCourierDetail(ReturnCreationRequestDTO returnCreationRequest, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, Integer incrementId, ReturnRequest returnRequest) {
        log.info("[getReverseCourierDetail] enter order : {}", returnCreationRequest.getIncrementId());
        ReverseCourierDetail reverseCourierDetail = null;
        boolean isNewAddress = returnCreationRequest.getReversePickupAddress() != null && returnCreationRequest.getReversePickupAddress().getPincode() != null;
        Boolean isStoreOrder=purchaseOrderDetailsDTO.getUwOrders().iterator().next().getShipToStoreRequired();
        boolean allowDispensingCourier =
                !(reasonCheckForDispensing(returnCreationRequest) ||
                        returnHasPowerSunglass(purchaseOrderDetailsDTO.getUwOrders()) ||
                        isJJOnlineStore(purchaseOrderDetailsDTO.getOrders()) ||
                        (isStoreOrder != null && isStoreOrder) ||
                        isDispensingCompleteForOrder(returnCreationRequest));
        log.info("[ReverseCourierDetailServiceImpl][getReverseCourierDetail] Dispensing courier assignment : {} for increment id {}",allowDispensingCourier, incrementId);

        if (returnCreationRequest.getReturnMethod() != null && RPU.equalsIgnoreCase(returnCreationRequest.getReturnMethod())) {
            try {
                ReverseCourierMetaData reverseCourierMetaData = returnCreationRequest.getReverseCourierMetaData();
                if (reverseCourierMetaData != null) {
                    Integer qcWarrantyRuleCount = reverseCourierMetaData.getQcWarrantyRuleCount();
                    boolean newQcFlag = false;
                    QCType qcType = QCType.QC_REQUIRED;
                    if (qcWarrantyRuleCount == 0) {
                        boolean checkDoortStepAtQcFlag = reverseCourierMetaData.isCheckDoortStepAtQcFlag();
                        if (!checkDoortStepAtQcFlag) {
                            qcType = QCType.NON_QC;
                        }
                    } else {
                        boolean smartQcEligibilityFlag = reverseCourierMetaData.isSmartQcEligibilityFlag();
                        boolean checkDoortStepAtQcFlag = reverseCourierMetaData.isCheckDoortStepAtQcFlag();
                        qcType = QCType.NON_QC;
                        newQcFlag = true;
                        if (smartQcEligibilityFlag) {
                            qcType = checkDoortStepAtQcFlag ? QCType.QC_REQUIRED : QCType.QC_OVERRIDDEN;
                        }
                    }
                    reverseCourierDetail = allocateReverseCourier(purchaseOrderDetailsDTO.getOrders().get(0).getOrderId(), incrementId, isNewAddress, returnCreationRequest.getReversePickupAddress().getPincode(), 0, qcType, newQcFlag);
                    reverseCourierDetail.setAllowDispensingCourier(allowDispensingCourier);
                    for (ReturnItemDTO returnItem : returnCreationRequest.getItems()) {
                        log.debug("Comments added for reverseCourier details for item id : " + returnItem.getUwItemId());
                        Map<String, String> commentMap = new HashMap<>();
                        commentMap.put("comment", "Return initiated for " + returnItem.getUwItemId() + " from " + returnCreationRequest.getReturnSource() + " and assigned to courier : " + reverseCourierDetail.getCourier());
                        orderUtilsService.insertOrderComment("Return initiated for " + returnItem.getUwItemId() + " from " + returnCreationRequest.getReturnSource() + " and assigned to courier : " + reverseCourierDetail.getCourier(),
                                purchaseOrderDetailsDTO.getOrders().get(0).getIncrementId());

                    }
                }

            } catch (Exception exception) {
                log.error("[ReverseCourierDetailServiceImpl][getReverseCourierDetail] for increment id {}, exception : {}", incrementId, exception);
            }
        }
        log.info("[getReverseCourierDetail] exit order : {}", returnCreationRequest.getIncrementId());
        return reverseCourierDetail;
    }

    @Override
    public ReverseCourierDetail reassignCourier(ReturnCreationRequestDTO returnCreationRequest, ReverseCourierDetail reverseCourierDetail) {
        //Initialize with nulls
        reverseCourierDetail.setPincode(returnCreationRequest.getReversePickupAddress().getPincode());
        reverseCourierDetail.setCourier(returnCreationRequest.getNewCourier());
        reverseCourierDetail.setStatus(false);
        reverseCourierDetail.setTat(null);

        ReversePickupPincode reversePickupPincode = reversePickupPincodeRepository.findTopByPincodeAndStatusAndCourierOrderByPriorityAsc(returnCreationRequest.getReversePickupAddress().getPincode(),
                returnCreationRequest.getNewCourier());
        if(QCType.QC_REQUIRED.equals(reverseCourierDetail.getCourierAssignmentType()) && Boolean.FALSE.equals(reversePickupPincode.getIsQcAtDoorstep())){
            reversePickupPincode=reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAsc(returnCreationRequest.getReversePickupAddress().getPincode(),List.of(),0,1);
            if(Objects.isNull(reversePickupPincode)){
                reversePickupPincode=reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAscNoQCCheck(returnCreationRequest.getReversePickupAddress().getPincode(), List.of(),0);
                reverseCourierDetail.setCourierAssignmentType(QCType.QC_OVERRIDDEN);
            }
        }
        if(reversePickupPincode != null){
            reverseCourierDetail.setTat(reversePickupPincode.getTat());
        }

        ReverseCourierMapping reverseCourierMapping = reverseCourierMappingRepository.findByCourier(returnCreationRequest.getNewCourier());
        if(reverseCourierMapping != null){
            reverseCourierDetail.setCpId(reverseCourierMapping.getCpId());
        }
        log.info("[ReturnInitiationServiceImpl][assignNewCourier] courierAvailabilityResponse : {}",reverseCourierDetail);
        return reverseCourierDetail;
    }

    private Boolean isDispensingCompleteForOrder(ReturnCreationRequestDTO returnCreationRequest) {
        log.info("[ReverseCourierDetailServiceImpl][isDispensingCompleteForOrder] Checking for existing complete dispensing cycles for increment id {}", returnCreationRequest.getIncrementId());

        Integer incrementId = returnCreationRequest.getIncrementId();
        DispensingDTO dispensingDTO = null;
        if(returnCreationRequest.getDispensingDTO() != null){
            dispensingDTO = returnCreationRequest.getDispensingDTO();
        }else{
            ResponseEntity<DispensingDTO> dispensingDetails = orderOpsFeignClient.getDispensingDetails(incrementId);
            if (!dispensingDetails.getStatusCode().is2xxSuccessful() || dispensingDetails.getBody() == null) {
                return false;
            }
            dispensingDTO = dispensingDetails.getBody();
        }

        String ezStatus = dispensingDTO.getFieldEzstatus();

        return ezStatus != null && DISPENSING_DONE_STATUSES.contains(ezStatus);
    }

    private Boolean returnHasPowerSunglass(List<UwOrderDTO> uwOrders) {
        log.info("[ReverseCourierDetailServiceImpl][returnHasPowerSunglass] Checking if order has a power sunglass item for increment id {}", uwOrders.iterator().next().getIncrementId());
        return uwOrders.stream()
                .anyMatch(uwOrder -> StringUtils.isNotEmpty(uwOrder.getClassification())
                        && uwOrder.getClassification().equalsIgnoreCase(String.valueOf(SUNGLASS)));
    }


    private Boolean isJJOnlineStore(List<OrdersDTO> orders) {
        log.info("[ReverseCourierDetailServiceImpl][isJJOnlineStore] Checking if increment id {} is a JJ online order", orders.iterator().next().getIncrementId());

        Optional<Byte> storeIdOptional = orders
                .stream()
                .map(OrdersDTO::getStoreId)
                .findAny();

        return storeIdOptional.map(storeId -> storeId.equals(JJONLINE_STOREID)).orElse(false);
    }


    private Boolean reasonCheckForDispensing(ReturnCreationRequestDTO returnCreationRequest) {

        log.info("[ReverseCourierDetailServiceImpl][reasonCheckForDispensing] Enabling or disabling dispensing courier as per the return reasons for items {}", returnCreationRequest.toString());
        Boolean isDispensingEnabledForReasons = false;
        Set<Integer> secondaryReasonSet = new HashSet<>();

        for (ReturnItemDTO returnItem : returnCreationRequest.getItems()) {
            for(com.lenskart.ordermetadata.dto.request.Reasons reason : returnItem.getReasons()) {
                secondaryReasonSet.add(reason.getSecondaryReasonId());
            }
        }

        log.info("[ReverseCourierDetailServiceImpl][reasonCheckForDispensing] Checking if dispensing required flag is 1 for any of the reasons in the received reason set");

        List<SecondaryReturnReason> dispensingEnabledReasons=secondaryReturnReasonRepository.getDispensingRequiredReasonIdsAmong(secondaryReasonSet);

        if(dispensingEnabledReasons!=null && !dispensingEnabledReasons.isEmpty())
            isDispensingEnabledForReasons=true;

        return isDispensingEnabledForReasons;
    }

    private ReverseCourierDetail allocateReverseCourier(Integer orderId, Integer incrementId, Boolean isNewAddress, Integer pincodeInRequest, int offset, QCType qcType, boolean newQcFlow) throws Exception {

        ReverseCourierDetail courierAvalabilityResponse = new ReverseCourierDetail();
        courierAvalabilityResponse.setQcAtDoorStepEligibleByCourierDetails(false);
        ReversePickupPincode reversePickupPincode=null;
        try {
            if (orderId != null && orderId != 0) {
                Integer pincode=0;
                log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] Fetching pincode for the given orderId {}, qcType : {}", orderId, qcType);
                if (isNewAddress) {
                    log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] New address has been provided");
                    pincode = pincodeInRequest;
                } else {
                    ResponseEntity<OrderAddressUpdateDTO> orderAddressUpdateResponseEntity = orderOpsFeignClient.getOrderAddressUpdate(orderId,SHIPPING);
                    if(orderAddressUpdateResponseEntity.getStatusCode().is2xxSuccessful()){
                        OrderAddressUpdateDTO addressUpdateDTO = orderAddressUpdateResponseEntity.getBody();
                        if(addressUpdateDTO != null){
                            pincode = Integer.valueOf(addressUpdateDTO.getPostcode());
                        }
                    }else{
                        log.error("[ReverseCourierDetailServiceImpl][assignReverseCourier] error occurred : "+orderAddressUpdateResponseEntity);
                    }
                }
                log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] Pincode for the given orderId {} is {}, offset:{}", orderId, pincode,offset);
                if (pincode != null) {
                    List<ReversePickupPincode> activeReversePickupCouriers = reversePickupPincodeRepository.findActiveReversePickupCouriers(pincode);
                    log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] active couriers on pincode : {}",activeReversePickupCouriers);
                    ReverseCourierMapping reverseCourierMapping = null;
                    log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] Looking up for non LKart couriers as the same isn't allowed");
                    if(qcType.getValue()==0 || qcType.getValue()==2){
                        reversePickupPincode = reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAscNoQCCheck(pincode, List.of(LKART), offset);
                    } else if(qcType.getValue()==1){
                        reversePickupPincode = reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAsc(pincode, List.of(LKART), offset,1);
                        if(Objects.isNull(reversePickupPincode)){
                            reversePickupPincode = reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAscNoQCCheck(pincode, List.of(LKART), offset);
                            if(newQcFlow) {
                                qcType = QCType.QC_OVERRIDDEN;
                            }else{
                                qcType=QCType.NON_QC;
                            }
                        }
                    }
                    if (null != reversePickupPincode) {
                        log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] allowDispensingTeamCourier courier details available for pincode {} are {} ", pincode, reversePickupPincode);
                        courierAvalabilityResponse.setPincode(reversePickupPincode.getPincode());
                        courierAvalabilityResponse.setCourier(reversePickupPincode.getCourier());
                        courierAvalabilityResponse.setStatus(true);
                        courierAvalabilityResponse.setTat(reversePickupPincode.getTat());

                        if(qcType.getValue()==0 || qcType.getValue()==2){
                            courierAvalabilityResponse.setQcAtDoorStepEligibleByCourierDetails(false);
                        } else if(qcType.getValue()==1){
                            courierAvalabilityResponse.setQcAtDoorStepEligibleByCourierDetails(true);
                        }
                        courierAvalabilityResponse.setCourierAssignmentType(qcType);

                        reverseCourierMapping = reverseCourierMappingRepository.findByCourier(reversePickupPincode.getCourier());
                        log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] reverseCourierMapping : {}",reverseCourierMapping);
                        if(null != reverseCourierMapping) {
                            courierAvalabilityResponse.setCpId(reverseCourierMapping.getCpId());
                        }else {
                            courierAvalabilityResponse.setCpId(0);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[ReverseCourierDetailServiceImpl][assignReverseCourier] Exception caught ", e);
            throw new Exception("Exception while assigning courier reverse pickup", e);
        }
        log.info("[ReverseCourierDetailServiceImpl][assignReverseCourier] incrementId:{}, offset:{}, reverseCourierDetail:{}",incrementId,offset,courierAvalabilityResponse);
        return courierAvalabilityResponse;
    }

    @Override
    public void assignCourier(ReturnCreationRequestDTO returnCreationRequest, String status, UwOrderDTO uwOrder, List<ReturnDetailReason> returnReasons,
                              ReverseCourierDetail reverseCourierDetail,
                              Integer returnId, Date returnCreationDate, Long groupId, ReturnItemDTO item, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, Integer orderId){
        log.info("[assignCourier] order : {}, status : {}", uwOrder.getIncrementId(), status);
        if(NEW_REVERSE_PICKUP.getStatus().equalsIgnoreCase(status)){
            try {
                List<ReturnItems> returnItemsList = new ArrayList<>();
                List<com.lenskart.ordermetadata.dto.request.Reasons> reasonList = item.getReasons();
                InitiateReverseRequest initiateReverseRequest = new InitiateReverseRequest();

                CourierAssignRetryMetaData courierAssignRetryMetaData = new CourierAssignRetryMetaData();
                courierAssignRetryMetaData.setAllowDispensingCourier(reverseCourierDetail != null && reverseCourierDetail.isAllowDispensingCourier());
                courierAssignRetryMetaData.setRetryCount(0);

                initiateReverseRequest.setAdditional(addAdditionalInfo(returnReasons,returnCreationRequest));
                initiateReverseRequest.setDropInfo(addDropInfo());
                initiateReverseRequest.setPickUpInfo(addPickupInfo(returnCreationRequest, orderId, purchaseOrderDetailsDTO.getOrderAddressUpdateDTOs()));
                initiateReverseRequest.setShipmentDetails(addShipmentDetails(returnItemsList, uwOrder, returnId, returnCreationDate, groupId, reverseCourierDetail));
                initiateReverseRequest.setCourierAssignRetryMetaData(courierAssignRetryMetaData);

                addReturnItemsDetails(initiateReverseRequest,uwOrder, purchaseOrderDetailsDTO.getItemWisePrices());
                log.info("calling addQcAtDoorstepDetails for order {} {}",uwOrder.getUwItemId(), uwOrder.getIncrementId());
                boolean isQcAtDoorstep = doorStepQcService.addQcAtDoorstepDetails(reverseCourierDetail, uwOrder, initiateReverseRequest, reasonList, purchaseOrderDetailsDTO.getItemWiseAmountDTO(), purchaseOrderDetailsDTO.getShippingStatusDetail());
                if(Objects.nonNull(reverseCourierDetail) && initiateReverseRequest.getAdditional() != null && Objects.nonNull(reverseCourierDetail.getCourierAssignmentType())){
                    initiateReverseRequest.getAdditional().setQcType(reverseCourierDetail.getCourierAssignmentType().getCode());
                }
                addCommentsToVSM(reverseCourierDetail,returnId,uwOrder,returnCreationRequest, orderId, purchaseOrderDetailsDTO.isBranded(), purchaseOrderDetailsDTO.isGetIsBlacklisted());
                log.info("completed addQcAtDoorstepDetails for order {} {} {}", uwOrder.getUwItemId(), uwOrder.getIncrementId(), isQcAtDoorstep);

                pushToQueue(initiateReverseRequest);
            }catch (Throwable e){
                log.error("Excepton while pushing data for clickPost",e);
            }
        }
    }

    @Override
    public ReturnCourierDetail updateReverseCourierDetail(Integer returnId, String reverseAwb) {
        ReturnCourierDetail returnCourierDetail = returnCourierDetailRepository.findTopByReturnIdOrderByIdDesc(returnId);
        if(returnCourierDetail != null){
            returnCourierDetail.setReverseAwbNumber(reverseAwb);
            returnCourierDetail = returnCourierDetailRepository.save(returnCourierDetail);
        }
        return returnCourierDetail;
    }

    @Override
    public List<Map<String, Object>> getCourierDetails(String identifierType, String identifierValue) throws ParseException {
        List<Map<String, Object>> courierDetailsMapList = null;
        if("GROUP_ID".equalsIgnoreCase(identifierType)){
            courierDetailsMapList = returnCourierDetailRepository.findCourierDetailsBasedByGroupId(Long.valueOf(identifierValue), DateUtil.getDate(DateUtil.getXMonthOldDate(2)));
        }
        if("RETURN_ID".equalsIgnoreCase(identifierType)){
            Integer returnId = Integer.valueOf(identifierValue);
            Map<String, String> returnReasons = returnReasonService.getReturnReasons(returnId);
            courierDetailsMapList = new ArrayList<>();
            Map<String, Object> courierDetailsMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(returnReasons)){
                String primaryReason = returnReasons.get("primary_reason");
                String secondaryReason = returnReasons.get("secondary_reason");
                PrimaryReturnReasons primaryReturnReason = primaryReturnReasonsRepository.findTopByReason(primaryReason);
                SecondaryReturnReason secondaryReturnReason = secondaryReturnReasonRepository.findTopByReason(secondaryReason);
                if(primaryReturnReason != null){
                    courierDetailsMap.put("primaryReason", primaryReturnReason.getPrimaryReasonId());
                }
                if(secondaryReturnReason != null){
                    courierDetailsMap.put("secondaryReason", secondaryReturnReason.getSecondaryReasonId());
                }
                Optional<ReturnDetail> optionalReturnDetail = returnDetailRepository.findById(returnId);
                if(optionalReturnDetail.isPresent()){
                    ReturnDetail returnDetail = optionalReturnDetail.get();
                    Integer requestId = returnDetail.getRequestId();
                    String magentoId = returnRequestRepository.findById(requestId).get().getIdentifierValue();
                    courierDetailsMap.put("magento",Integer.valueOf(magentoId));
                    courierDetailsMap.put("is_insurance", returnDetail.getIsInsurance());
                    ReturnDetailAddressUpdate returnDetailAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                    courierDetailsMap.put("returnDetailAddressUpdate",returnDetailAddressUpdate);
                }
                courierDetailsMapList.add(courierDetailsMap);
            }

        }
        log.info("[getCourierDetails] courierDetailsMap : {}", courierDetailsMapList);
        return courierDetailsMapList;
    }

    private void pushToQueue(InitiateReverseRequest initiateReverseRequest) {
        kafkaService.pushToKafka(Constant.RETURN_CREATE_COURIER_SERVICE_QUEUE, initiateReverseRequest.getShipmentDetails().getReferenceNumber(), initiateReverseRequest);
    }

    private void addCommentsToVSM(ReverseCourierDetail reverseCourierDetail, Integer returnId, UwOrderDTO uwOrder,
                                  ReturnCreationRequestDTO returnCreationRequest, Integer orderId, boolean isBranded, boolean isBlackListed){
        if(Objects.isNull(reverseCourierDetail)){
            log.info("[ReturnActionImpl][addCommentsToVSM] Unable to add comments to vsm for uw-item-id:{}",uwOrder.getUwItemId());
            return;
        }
        if(Objects.isNull(orderId)){
            log.info("[ReturnActionImpl][addCommentsToVSM] Unable to add comments to vsm for uw-item-id:{} as no entry present in orders table",uwOrder.getUwItemId());
            return;
        }

        log.info("[ReturnActionImpl][addCommentsToVSM] Comments added for reverseCourier details for item id : "+uwOrder.getUwItemId());
        if(QCType.NON_QC.equals(reverseCourierDetail.getCourierAssignmentType())) {
            orderUtilsService.insertOrderComment(
                    NON_QC_VSM_COMMENT+" {" + returnId + "}, product: {" + uwOrder.getProductId() + "}."+NON_QC_REASON_COMMENT, uwOrder.getIncrementId());
        } else if(QCType.QC_REQUIRED.equals(reverseCourierDetail.getCourierAssignmentType())){
            String blackListReason=getBlackListReason(uwOrder, isBranded, isBlackListed);
            if(Objects.nonNull(blackListReason)) {
                orderUtilsService.insertOrderComment(
                        QC_VSM_COMMENT + "{" + returnId + "}, product: {" + uwOrder.getProductId() + "}. Reason: {" + blackListReason + "}", uwOrder.getIncrementId());
            }else{
                orderUtilsService.insertOrderComment(
                        QC_VSM_COMMENT + "{" + returnId + "}, product: {" + uwOrder.getProductId() + "}.", uwOrder.getIncrementId());

            }
        } else if(QCType.QC_OVERRIDDEN.equals(reverseCourierDetail.getCourierAssignmentType())){
            List<com.lenskart.ordermetadata.dto.request.Reasons> reasonList = returnCreationRequest.getItems().get(0).getReasons();
            Integer reasonId = reasonList!=null && !reasonList.isEmpty() ? reasonList.get(0).getSecondaryReasonId() : 0;
            Integer pinCode=returnCreationRequest.getReversePickupAddress().getPincode();
            orderUtilsService.insertOrderComment(
                    NON_QC_VSM_COMMENT+" {" + returnId + "}, product: {" + uwOrder.getProductId() + "}. Reason: {Doorstep qc not available on pincode {"+pinCode+"} or reason {"+reasonId+"} }.", uwOrder.getIncrementId());
        }
    }

    private String getBlackListReason(UwOrderDTO uwOrder, boolean isBranded, boolean isBlackListed){
        String reason=null;
        Integer incrementId = uwOrder.getIncrementId();
        Integer uwItemId = uwOrder.getUwItemId();
        if(!CollectionUtils.isEmpty(getQcAndWarrantyRuleConfig())) {
            QcAndWarrantyRule qcAndWarrantyRule = getQcAndWarrantyRuleConfig().stream().filter(rule -> rule.getRuleType().equalsIgnoreCase(Constant.RULE_TYPE.SMART_QC)).toList().get(0);
            if (qcAndWarrantyRule.getIsBlacklisted()) {
                reason =isBlackListed?BLACKLIST_CUSTOMER_FLAG_TRUE:BLACKLIST_CUSTOMER_FLAG_RULE;
            } else if (qcAndWarrantyRule.getIsBranded()) {
                reason=isBranded?BRANDED_PRODUCT:BRANDED_PRODUCT_RULE;
            } else if (qcAndWarrantyRule.getRefundDispatchPoint()) {
                reason = REFUND_DISPATCH_REACHED;
            } else if (qcAndWarrantyRule.getRefundIntent()) {
                reason = REFUND_INTENT_MATCHED;
            } else if (qcAndWarrantyRule.getCustomerProfilingScoreFrom()) {
                reason = CUSTOMER_PROFILING_SCORE_HIGH;
            } else if (qcAndWarrantyRule.getCustomerProfilingScoreTo()) {
                reason = CUSTOMER_PROFILING_SCORE_LOW;
            } else if (qcAndWarrantyRule.getReturnPercentage()) {
                reason = CUSTOMER_RETURN_PERCENTAGE_HIGH;
            } else if (qcAndWarrantyRule.getReturnCountGreaterThan()) {
                reason = CUSTOMER_RETURN_COUNT_HIGH;
            } else if (qcAndWarrantyRule.getItemValueFrom()) {
                reason = ITEM_PRICE_HIGH;
            } else if (qcAndWarrantyRule.getItemValueTo()) {
                reason = ITEM_PRICE_LOW;
            } else if (qcAndWarrantyRule.getFromDays()) {
                reason = DELIVERY_DATE_GAP_HIGH;
            } else if (qcAndWarrantyRule.getToDays()) {
                reason = DELIVERY_DATE_GAP_LOW;
            }
        }
        return reason;
    }

    private List<QcAndWarrantyRule> getQcAndWarrantyRuleConfig(){
        if(Boolean.TRUE.equals(redisTemplate.hasKey(QC_AND_WARRANTY_RULE_CONFIG))){
            log.info("[SmartQcPickupUtil][getQcAndWarrantyRuleConfig] qc_and_warranty rules fetched from cache");
            return (List<QcAndWarrantyRule>) redisTemplate.opsForValue().get(QC_AND_WARRANTY_RULE_CONFIG);
        }
        log.info("[SmartQcPickupUtil][getQcAndWarrantyRuleConfig] qc_and_warranty rules fetched from DB");
        List<QcAndWarrantyRule> qcAndWarrantyRules= (List<QcAndWarrantyRule>) qcAndWarrantyRulesRepository.findAll();
        if(!CollectionUtils.isEmpty(qcAndWarrantyRules)) {
            redisTemplate.opsForValue().set(QC_AND_WARRANTY_RULE_CONFIG, qcAndWarrantyRules, 1, TimeUnit.MINUTES);
        }
        return qcAndWarrantyRules;
    }

    private DropInfo addDropInfo() {
        return getWarehouseAddressByFacilityCode(lenskartFacility);
    }

    private DropInfo getWarehouseAddressByFacilityCode(String facilityCode) {
        try {
            Map<String, Object> hubDetailsResponse = orderOpsFeignClient.getHubDetails(HUB_SEARCH_BY_HUB_ID, facilityCode);
            List<Map<String, Object>> hubDetails = getHubDetails(hubDetailsResponse);
            if (!CollectionUtils.isEmpty(hubDetails)) {
                Map<String, Object> hubDetail = hubDetails.get(0);
                DropInfo dropInfo = new DropInfo();
                dropInfo.setDropAddress(hubDetail.get("address1") + ", " + hubDetail.get("address2"));
                dropInfo.setDropCity("" + hubDetail.get("city"));
                dropInfo.setDropCountry("" + hubDetail.get("country"));
                dropInfo.setDropName("" + hubDetail.get("name"));
                dropInfo.setDropPhone("9311347796");
                dropInfo.setDropEmail("");
                dropInfo.setDropPincode("" + hubDetail.get("pincode"));
                dropInfo.setDropState("" + hubDetail.get("state"));
                return dropInfo;

            }
        } catch (Exception exception) {
            log.error("[getWarehouseAddressByFacilityCode] error : " + exception);
        }
        return null;
    }

    private List<Map<String, Object>> getHubDetails(Map<String, Object> hubDetailsResponse) {
        if (hubDetailsResponse == null) {
            return null;
        }

        Map<String, Object> response = (Map<String, Object>) hubDetailsResponse.get("response");
        if (response == null) {
            return null;
        }

        return (List<Map<String, Object>>) response.getOrDefault("hub_details", null);
    }


    private Additional addAdditionalInfo(List<ReturnDetailReason> returnReasonList, ReturnCreationRequestDTO returnCreationRequest) {
        if(!CollectionUtils.isEmpty(returnReasonList)){
            ReturnDetailReason returnReason = returnReasonList.get(0);
            Additional additional = new Additional();
            additional.setAsync(false);
            additional.setLabel(false);
            //additional.setQcType("doorstep");
            additional.setOrderId(String.valueOf(returnReason.getOrderId()));
            additional.setDeliveryType("RVP");
            additional.setRvpReason("Return Order");
            additional.setOldCourier(returnCreationRequest.getOldCourier());
            return additional;
        }
        return null;
    }

    private PickUpInfo addPickupInfo(ReturnCreationRequestDTO returnCreationRequest, Integer orderNo, List<OrderAddressUpdateDTO> orderAddressUpdateDTOs) {
        PickUpInfo pickUpInfo = null;
        if(null != returnCreationRequest.getReversePickupAddress()){
            PickupAddressDTO pickupAddress = returnCreationRequest.getReversePickupAddress();
            pickUpInfo = new PickUpInfo();
            pickUpInfo.setEmail(pickupAddress.getEmail());
            pickUpInfo.setPickupAddress(pickupAddress.getStreet1() + ", " + pickupAddress.getStreet2());
            pickUpInfo.setPickupCity(pickupAddress.getCity());
            pickUpInfo.setPickupState(pickupAddress.getState());
            pickUpInfo.setPickupCountry(pickupAddress.getCountry());
            pickUpInfo.setPickupName(pickupAddress.getFirstName() + " " + pickupAddress.getLastName());
            pickUpInfo.setPickupPincode(String.valueOf(pickupAddress.getPincode()));
            pickUpInfo.setPickupPhone(pickupAddress.getTelephone());
        }else {
            Optional<OrderAddressUpdateDTO> orderAddressUpdate = orderAddressUpdateDTOs.stream().filter(orderAddressUpdateDTO -> "billing".equalsIgnoreCase(orderAddressUpdateDTO.getAddressType())).findAny();
            if(orderAddressUpdate.isPresent()) {
                OrderAddressUpdateDTO orderAddressUpdateDTO = orderAddressUpdate.get();
                pickUpInfo = new PickUpInfo();
                pickUpInfo.setEmail(orderAddressUpdateDTO.getEmail());
                pickUpInfo.setPickupAddress(orderAddressUpdateDTO.getStreet());
                pickUpInfo.setPickupCity(orderAddressUpdateDTO.getCity());
                pickUpInfo.setPickupState(orderAddressUpdateDTO.getRegion());
                pickUpInfo.setPickupCountry(Objects.equals(orderAddressUpdateDTO.getCountryId(), "IN") ? "India" : "");
                pickUpInfo.setPickupName(orderAddressUpdateDTO.getFirstName() + " " + orderAddressUpdateDTO.getLastName());
                pickUpInfo.setPickupPincode(String.valueOf(orderAddressUpdateDTO.getPostcode()));
                pickUpInfo.setPickupPhone(orderAddressUpdateDTO.getTelephone());
            }
        }
        assert pickUpInfo != null;
        pickUpInfo.setPickupTime(dateUtil.convertDateToISO8601Format(new Date()));
        return pickUpInfo;
    }

    private ShipmentDetails addShipmentDetails(List<ReturnItems> returnItemsList, UwOrderDTO uwOrder, Integer returnId, Date returnCreationDate, Long groupId, ReverseCourierDetail reverseCourierDetail) {
        ShipmentDetails shipmentDetails = new ShipmentDetails();
        shipmentDetails.setBreadth(29);
        shipmentDetails.setLength(39);
        shipmentDetails.setCodValue(0.0d);
        shipmentDetails.setHeight(2);
        shipmentDetails.setWeight(10);
        shipmentDetails.setItems(returnItemsList);
        shipmentDetails.setOrderType("PREPAID");
        Date cutOffDate = dateUtil.getFormattedDate(new Date(),RETURN_CREATE_DATE_CUT_OFF_DAYS);
        if(null != returnCreationDate && (returnCreationDate.compareTo(cutOffDate)>0)) {
            String pattern = "yyyy-MM-dd";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);

            String date = simpleDateFormat.format(returnCreationDate);
            shipmentDetails.setInvoiceDate(date);
        }
        else {
            String pattern = "yyyy-MM-dd";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
            Date dateToPass = dateUtil.getFormattedDate(new Date(),RETURN_CREATE_DATE_CUT_OFF_DAYS_FALLBACK);
            String date = simpleDateFormat.format(dateToPass);
            shipmentDetails.setInvoiceDate(date);
        }
        shipmentDetails.setInvoiceNumber(String.valueOf(uwOrder.getIncrementId()));
        shipmentDetails.setReferenceNumber(uwOrder.getIncrementId() + "-" + groupId);
        shipmentDetails.setCourierPartner(reverseCourierDetail.getCpId());
        return shipmentDetails;
    }

    private void addReturnItemsDetails(InitiateReverseRequest initiateReverseRequest, UwOrderDTO uwOrder, List<ItemWisePriceDetailsDTO> itemWisePriceDetailsDTOs) {
        List<ReturnItems> returnItemsList = initiateReverseRequest.getShipmentDetails().getItems();
        ReturnItems returnItems = new ReturnItems() ;
        Map<String, String> additionalReturnItem = new HashMap<>();
        if(!CollectionUtils.isEmpty(itemWisePriceDetailsDTOs)){
            ItemWisePriceDetailsDTO itemWisePriceDetails = getItemWisePrices(itemWisePriceDetailsDTOs, uwOrder.getItemId());
            if(itemWisePriceDetails != null){
                double totalLkCashApplied = (itemWisePriceDetails.getLenskartDiscount() + itemWisePriceDetails.getLenskartPlusDiscount());
                double totalSCApplied = itemWisePriceDetails.getStoreCreditDiscount();
                double totalAmountAfterDiscount = itemWisePriceDetails.getItemTotalAfterDiscount();
                double totalGvDiscount = itemWisePriceDetails.getGiftVoucherDiscount();
                returnItems.setPrice(totalSCApplied+totalAmountAfterDiscount+totalLkCashApplied+totalGvDiscount);
            }
        }
        returnItems.setDescription(uwOrder.getProductValue());
        returnItems.setQuantity(1);
        returnItems.setSku(uwOrder.getProductSku());
        additionalReturnItem.put("length","0");
        additionalReturnItem.put("breadth","0");
        additionalReturnItem.put("height","0");
        additionalReturnItem.put("weight","0");

        returnItems.setAdditional(additionalReturnItem);
        returnItemsList.add(returnItems);
        ShipmentDetails shipmentDetails = initiateReverseRequest.getShipmentDetails();
        if(shipmentDetails != null){
            List<ReturnItems> returnItemsList1 = shipmentDetails.getItems();
            Double invoiceAmount = 0.0d;
            if (!CollectionUtils.isEmpty(returnItemsList1)) {
                for (ReturnItems item : returnItemsList1) {
                    invoiceAmount += item.getPrice();
                }
            }
            if ((invoiceAmount.compareTo(0.0)) > 0) {
                shipmentDetails.setInvoiceValue(invoiceAmount);
            } else {
                shipmentDetails.setInvoiceValue(INVOICE_VALUE);
            }
            shipmentDetails.setItems(returnItemsList);
        }
    }

    private ItemWisePriceDetailsDTO getItemWisePrices(List<ItemWisePriceDetailsDTO> itemWisePriceDetailsDTOs, int itemId) {
        log.info("[getItemWisePrices] itemId : {}", itemId);
        ItemWisePriceDetailsDTO itemWisePriceDetailsDTO = null;
        Optional<ItemWisePriceDetailsDTO> optionalItemWisePriceDetailsDTO = itemWisePriceDetailsDTOs.stream()
                .filter(item -> item.getItemId() == itemId)
                .findAny();
        if(optionalItemWisePriceDetailsDTO.isPresent()){
            itemWisePriceDetailsDTO = optionalItemWisePriceDetailsDTO.get();
        }
        return itemWisePriceDetailsDTO;
    }

    @Override
    public List<ReverseAwbCourierMapperDTO> getReverseAwbCourierMap(List<String> reverseAwbs){
        log.info("[getReverseAwbCourierMap] reverseAwbs : {}", reverseAwbs);
        List<ReverseAwbCourierMapperDTO> reverseAwbCourierMapperDTOList = new ArrayList<>();
        for(String reverseAwb : reverseAwbs){
            ReturnCourierDetail returnCourierDetail = returnCourierDetailRepository.findTopByReverseAwbNumber(reverseAwb);
            if(returnCourierDetail != null){
                ReverseAwbCourierMapperDTO reverseAwbCourierMapperDTO = new ReverseAwbCourierMapperDTO();
                reverseAwbCourierMapperDTO.setReverseAwb(returnCourierDetail.getReverseAwbNumber());
                reverseAwbCourierMapperDTO.setCourier(returnCourierDetail.getReverseCourier());
                Optional<ReturnDetail> optionalReturnDetail = returnDetailRepository.findById(returnCourierDetail.getReturnId());
                optionalReturnDetail.ifPresent(returnDetail -> reverseAwbCourierMapperDTO.setIsQcAtDoorStep(returnDetail.getIsQcAtDoorstep()));
                reverseAwbCourierMapperDTOList.add(reverseAwbCourierMapperDTO);
            }
        }
        log.info("[getReverseAwbCourierMap] reverseAwbCourierMapperDTOList : {}", reverseAwbCourierMapperDTOList);
        return reverseAwbCourierMapperDTOList;
    }
}
