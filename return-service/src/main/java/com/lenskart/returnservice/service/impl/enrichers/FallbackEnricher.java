package com.lenskart.returnservice.service.impl.enrichers;

import com.lenskart.returncommon.enrichers.ReturnOrderEnricher;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returnrepository.entity.ReturnOrder;
import com.lenskart.returnrepository.repository.ReturnOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FallbackEnricher implements ReturnOrderEnricher {

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    private ReturnOrderEnricher next;

    private Map<Integer, ReturnOrder> data = new HashMap<>();

    @Override
    public void enrich(ReturnOrderDTO dto) {
        ReturnOrder fallback = data != null ? data.get(dto.getId()) : null;
        if (fallback != null) {
            dto.setReverseAwb(fallback.getReverseAwb());
            dto.setReversePickupReferenceId(fallback.getReversePickupReferenceId());
            log.info("[FallbackEnricher] Added tracking details revAwbNumber:{},revPickupId :{} for returnId:{}", fallback.getReverseAwb(), fallback.getReversePickupReferenceId(), dto.getId());
        } else if (next != null) {
            next.enrich(dto);
        }
    }

    @Override
    public void setNextEnricher(ReturnOrderEnricher nextEnricher) {
        this.next = nextEnricher;
    }

    public void preload(List<Integer> returnIds) {
        /**
         * prepares map of <returnId,ReturnOrder>
         */
        List<ReturnOrder> orders = Optional.ofNullable(returnOrderRepository.findByReturnIdIn(returnIds))
                .orElse(Collections.emptyList());
        this.data = orders.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        ReturnOrder::getReturnId,
                        Function.identity(),
                        (existing, replacement) -> replacement
                ));
    }
}
