package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;

public interface IReturnOrderAddressUpdateService {
    Integer saveReturnAddressDetails(ReturnCreationRequestDTO returnCreationRequest, int groupId, Integer incrementId);
    ReturnDetailAddressUpdateDTO getReturnAddressUpdate(Integer incrementId);
    ReturnDetailAddressUpdateDTO getReturnAddressUpdate(Long groupId);
    ReturnDetailAddressUpdateDTO getReturnAddressUpdateNew(Integer incrementId);
}
