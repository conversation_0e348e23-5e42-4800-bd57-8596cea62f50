package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.ReturnSubHeaderTimelineDatesEnum;
import com.lenskart.returncommon.model.request.UpdateRefundHeadingRequest;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnrepository.entity.ReturnHeadingSubheadingMapping;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import com.lenskart.returnservice.service.IReturnSubHeaderTimelineDateFetchService;
import com.lenskart.returnservice.service.ReturnHeadingSubheadingMappingCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ReturnSubHeaderTimelineDateFetchServiceImpl implements IReturnSubHeaderTimelineDateFetchService {

    @Autowired
    ReturnSubHeaderTimelineDatesFactory returnSubHeaderTimelineDatesFactory;

    @Autowired
    ReturnHeadingSubheadingMappingCacheService returnHeadingSubheadingMappingCacheService;

    @Override
    public void getDateInMilliseconds(ReturnDetailsResponse returnDetailsResponse) {
        try {
            String key = getKeyFromHeading(returnDetailsResponse);
            if (StringUtils.isNotBlank(key)) {
                IReturnHeadingPlaceHolderValueFetchStrategy returnDateFetchStrategyService = returnSubHeaderTimelineDatesFactory
                        .getReturnSubHeaderTimelineDatesStrategy(ReturnSubHeaderTimelineDatesEnum.getReturnSubHeaderTimelineDatesEnum(key));
                log.info("Strategy is :: " + returnDateFetchStrategyService.getClass().getName());
                returnDateFetchStrategyService.getReturnHeadingsPlaceHolderValue(returnDetailsResponse);
            }
            getKeyFromTimelinePredictive(returnDetailsResponse);
            getKeyFromTimelineActual(returnDetailsResponse);
        }catch (Exception e){
            log.error("Exception ", e);
        }
    }

    @Override
    public ReturnStatusHeadingDetail updateRefundHeading(UpdateRefundHeadingRequest refundHeadingRequest) {
        ReturnStatusHeadingDetail returnStatusHeadingDetail = null;
        try {
            ReturnHeadingSubheadingMapping returnHeadingSubheadingMapping = new ReturnHeadingSubheadingMapping();
            returnHeadingSubheadingMapping.setRefundMethod("refund");
            returnHeadingSubheadingMapping.setRefundMode(refundHeadingRequest.getRefundMethodRequest());
            returnHeadingSubheadingMapping.setReturnSource(refundHeadingRequest.getSource());
            returnHeadingSubheadingMapping.setRefundStatus(refundHeadingRequest.getStatus());
            returnHeadingSubheadingMapping.setReturnStatus("order_cancelled");
            ReturnHeadingSubheadingMapping returnHeadingSubheadingMappingResponse = returnHeadingSubheadingMappingCacheService.getReturnHeadingSubheadingMapping(refundHeadingRequest.getSource(), "order_cancelled", refundHeadingRequest.getStatus(), "refund", refundHeadingRequest.getRefundMethodRequest());
            if (null != returnHeadingSubheadingMappingResponse) {
                returnStatusHeadingDetail = new ReturnStatusHeadingDetail();
                returnStatusHeadingDetail.setHeading(returnHeadingSubheadingMappingResponse.getTopHeader());
                List<String> subHeadingList = Arrays.asList(returnHeadingSubheadingMappingResponse.getTopSubHeader().split(":"));
                returnStatusHeadingDetail.setSubHeading(subHeadingList);
                Date createdAt = refundHeadingRequest.getCancelledCreatedAt();
                if (null == createdAt)
                    createdAt = new Date();
                ReturnDetailsResponse returnDetailsResponse = new ReturnDetailsResponse();
                returnDetailsResponse.setOrderAddressUpdates(refundHeadingRequest.getOrderAddressUpdateDTOs());
                returnDetailsResponse.setOrdersHeaderDTO(refundHeadingRequest.getOrdersHeaderDTO());
                getDateInMilliseconds(returnDetailsResponse, returnStatusHeadingDetail, createdAt, "AnyReceivingPoint", refundHeadingRequest.getCancelledIncrementId(), refundHeadingRequest.getRefundDetails());
            }
        }catch (Exception e){
            log.error("[CancelRefundDetailsServiceImpl][updateRefundHeadingNew] Exception:{}",e.getMessage());
        }
        return returnStatusHeadingDetail;
    }

    @Override
    public void getDateInMilliseconds(ReturnDetailsResponse returnDetailsResponse, ReturnStatusHeadingDetail returnStatusHeadingDetail, Date createdAt, String anyReceivingPoint, Integer incrementId, List<RefundDetails> refundList) {
        try{
            if (null != returnStatusHeadingDetail) {
                String key;
                List<String> subStatusList = returnStatusHeadingDetail.getSubHeading();
                if (CollectionUtils.isNotEmpty(subStatusList)) {
                    for (String subStatus : subStatusList) {
                        int keyStartIndex = subStatus.indexOf("{");
                        int keyEndIndex = subStatus.indexOf("}");
                        if (-1 != keyEndIndex && -1 != keyEndIndex) {
                            key = subStatus.substring(keyStartIndex + 1, keyEndIndex);
                            if (StringUtils.isNotBlank(key)) {
                                log.info("key is " + key);
                                IReturnHeadingPlaceHolderValueFetchStrategy returnDateFetchStrategyService = returnSubHeaderTimelineDatesFactory
                                        .getReturnSubHeaderTimelineDatesStrategy(ReturnSubHeaderTimelineDatesEnum.getReturnSubHeaderTimelineDatesEnum(key));
                                log.info("Strategy for returnTimeline is :: " + returnDateFetchStrategyService.getClass().getName());
                                returnDateFetchStrategyService.getReturnHeadingsPlaceHolderValue(returnDetailsResponse, refundList, returnStatusHeadingDetail, Long.valueOf(incrementId), createdAt, anyReceivingPoint);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("Exception ", e);
        }
    }


    private void getKeyFromTimelineActual(ReturnDetailsResponse returnDetailsResponse) {
        try{
            if (null != returnDetailsResponse) {
                List<ReturnStatusHistory> returnHistoryList = returnDetailsResponse.getReturnStatusHistoryList();
                if (CollectionUtils.isNotEmpty(returnHistoryList)) {
                    String key;
                    for (ReturnStatusHistory returnStatusHistory : returnHistoryList) {
                        String returnSubStatus = returnStatusHistory.getComment();
                        if (StringUtils.isNotBlank(returnSubStatus)) {
                            int keyStartIndex = returnSubStatus.indexOf("{");
                            int keyEndIndex = returnSubStatus.indexOf("}");
                            if (-1 != keyStartIndex && -1 != keyEndIndex && !"sms".equalsIgnoreCase(returnStatusHistory.getReturnStatus())) {
                                key = returnSubStatus.substring(keyStartIndex + 1, keyEndIndex);
                                if (StringUtils.isNotBlank(key)) {
                                    log.info("key is " + key);
                                    IReturnHeadingPlaceHolderValueFetchStrategy returnDateFetchStrategyService = returnSubHeaderTimelineDatesFactory
                                            .getReturnSubHeaderTimelineDatesStrategy(ReturnSubHeaderTimelineDatesEnum.getReturnSubHeaderTimelineDatesEnum(key));
                                    log.info("Strategy for returnTimeline is :: " + returnDateFetchStrategyService.getClass().getName());
                                    returnDateFetchStrategyService.getReturnHistoryPlaceHolderValue(returnStatusHistory, returnDetailsResponse);
                                }
                            }
                        }

                    }
                }
            }
        }catch (Exception e){
            log.error("Exception ", e);
        }
    }

    private void getKeyFromTimelinePredictive(ReturnDetailsResponse returnDetailsResponse) {
        if(null != returnDetailsResponse) {
            if (null !=  returnDetailsResponse.getReturnTimeLines()) {
                setEstimateDateToReturnTimeline(returnDetailsResponse);
            }
            if (null !=  returnDetailsResponse.getRefundTimeLines()) {
                setEstimateDateToRefundTimeline(returnDetailsResponse);
            }
            if (null !=  returnDetailsResponse.getExchangeTimelines()) {
                setEstimateDateToExchangeTimeline(returnDetailsResponse);
            }
        }
    }

    private void setEstimateDateToRefundTimeline(ReturnDetailsResponse returnDetailsResponse) {
        try{
            String key;
            List<RefundTimeLine> refundTimeLines = returnDetailsResponse.getRefundTimeLines();
            if (CollectionUtils.isNotEmpty(refundTimeLines)) {
                for (RefundTimeLine refundTimeLine : refundTimeLines) {
                    String refundTimeLineSubStatus = refundTimeLine.getRefundSubStatus();
                    if (StringUtils.isNotBlank(refundTimeLineSubStatus)) {
                        int keyStartIndex = refundTimeLineSubStatus.indexOf("{");
                        int keyEndIndex = refundTimeLineSubStatus.indexOf("}");
                        if (-1 != keyStartIndex && -1 != keyEndIndex) {
                            key = refundTimeLineSubStatus.substring(keyStartIndex + 1, keyEndIndex);
                            if (StringUtils.isNotBlank(key)) {
                                IReturnHeadingPlaceHolderValueFetchStrategy returnDateFetchStrategyService = returnSubHeaderTimelineDatesFactory
                                        .getReturnSubHeaderTimelineDatesStrategy(ReturnSubHeaderTimelineDatesEnum.getReturnSubHeaderTimelineDatesEnum(key));
                                log.info("Strategy for returnTimeline is :: " + returnDateFetchStrategyService.getClass().getName());
                                returnDateFetchStrategyService.getRefundTimelinePlaceHolderValue(refundTimeLine, returnDetailsResponse);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("Exception ", e);
        }
    }

    private void setEstimateDateToExchangeTimeline(ReturnDetailsResponse returnDetailsResponse) {
        try {
            String key;
            List<ExchangeTimeLine> exchangeTimeLines = returnDetailsResponse.getExchangeTimelines();
            if (CollectionUtils.isNotEmpty(exchangeTimeLines)) {
                for (ExchangeTimeLine exchangeTimeLine : exchangeTimeLines) {
                    String exchangeTimeLineSubStatus = exchangeTimeLine.getExchangeSubStatus();
                    if (StringUtils.isNotBlank(exchangeTimeLineSubStatus)) {
                        int keyStartIndex = exchangeTimeLineSubStatus.indexOf("{");
                        int keyEndIndex = exchangeTimeLineSubStatus.indexOf("}");
                        if (-1 != keyStartIndex && -1 != keyEndIndex) {
                            key = exchangeTimeLineSubStatus.substring(keyStartIndex + 1, keyEndIndex);
                            if (StringUtils.isNotBlank(key)) {
                                IReturnHeadingPlaceHolderValueFetchStrategy returnDateFetchStrategyService = returnSubHeaderTimelineDatesFactory.getReturnSubHeaderTimelineDatesStrategy(ReturnSubHeaderTimelineDatesEnum.getReturnSubHeaderTimelineDatesEnum(key));
                                log.info("Strategy for exchangeTimeLines is :: " + returnDateFetchStrategyService.getClass().getName());
                                returnDateFetchStrategyService.getExchangeTimelinePlaceHolderValue(exchangeTimeLine, returnDetailsResponse);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("Exception ", e);
        }
    }

    private void setEstimateDateToReturnTimeline(ReturnDetailsResponse returnDetailsResponse) {
        try {
            String key;
            List<ReturnTimeLine> returnTimeLineList = returnDetailsResponse.getReturnTimeLines();
            if (CollectionUtils.isNotEmpty(returnTimeLineList)) {
                for (ReturnTimeLine returnTimeLine : returnTimeLineList) {
                    String returnSubStatus = returnTimeLine.getReturnSubStatus();
                    if (StringUtils.isNotBlank(returnSubStatus)) {
                        int keyStartIndex = returnSubStatus.indexOf("{");
                        int keyEndIndex = returnSubStatus.indexOf("}");
                        if (-1 != keyStartIndex && -1 != keyEndIndex) {
                            key = returnSubStatus.substring(keyStartIndex + 1, keyEndIndex);
                            if (StringUtils.isNotBlank(key)) {
                                IReturnHeadingPlaceHolderValueFetchStrategy returnDateFetchStrategyService = returnSubHeaderTimelineDatesFactory.getReturnSubHeaderTimelineDatesStrategy(ReturnSubHeaderTimelineDatesEnum.getReturnSubHeaderTimelineDatesEnum(key));
                                log.info("Strategy for returnTimeline is :: " + returnDateFetchStrategyService.getClass().getName());
                                returnDateFetchStrategyService.getReturnTimelinePlaceHolderValue(returnTimeLine, returnDetailsResponse);
                            }
                        }
                    }

                }
            }
        } catch (Exception e){
            log.error("Exception ", e);
        }
    }

    private String getKeyFromHeading(ReturnDetailsResponse returnDetailsResponse) {
        try{
            if(null != returnDetailsResponse) {
                if (null !=  returnDetailsResponse.getReturnStatusHeading()) {
                    List<String> returnSubHeadingList = returnDetailsResponse.getReturnStatusHeading().getSubHeading();
                    if(CollectionUtils.isNotEmpty(returnSubHeadingList)) {
                        for(String subHeading : returnSubHeadingList) {
                            int keyStartIndex = subHeading.indexOf("{");
                            int keyEndIndex = subHeading.indexOf("}");
                            if( -1 != keyStartIndex && -1 != keyEndIndex){
                                return subHeading.substring(keyStartIndex + 1, keyEndIndex);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("Exception ", e);
        }
        return null;
    }
}
