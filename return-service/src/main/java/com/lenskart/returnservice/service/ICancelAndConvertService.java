package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.response.ExchangeMasterMapping;
import com.lenskart.returncommon.model.response.StoreCreditRefundDetails;

import java.util.List;

public interface ICancelAndConvertService {
    StoreCreditRefundDetails getScRefundDetails(Long magentoId);
    StoreCreditRefundDetails getScRefundDetails(Integer uwItemId, String unicomOrderCode);
    List<Long> getExchangeCreatedButNotCancelledDetails(List<Long> magentoList) throws Exception;
    ExchangeMasterMapping getExchangeMasterMapping(List<Long> magentoList) throws Exception;
}
