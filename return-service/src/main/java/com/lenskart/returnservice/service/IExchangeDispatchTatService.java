package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.response.ReturnDetailsResponse;

import java.util.Date;

public interface IExchangeDispatchTatService {
    Integer getExchangeIncrementId(ReturnDetailsResponse returnDetailsResponse);

    Integer getWareHouseProcessingTat(ReturnDetailsResponse returnDetailsResponse);

    Date getExchangeDispatchETA(ReturnDetailsResponse returnDetailsResponse, Integer reverseOrderIncrementId, String returnOrderPincode, Date createdAt);
}
