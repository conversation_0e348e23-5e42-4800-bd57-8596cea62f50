package com.lenskart.returnservice.service.impl;

import com.lenskart.core.model.Refund;
import com.lenskart.refund.client.model.enums.RefundMethod;
import com.lenskart.returncommon.model.dto.RefundDetails;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.service.IRefundMethodLabelService;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class RefundMethodLabelServiceImpl implements IRefundMethodLabelService {
    @Value("${refund.method.label.mapping}")
    private String refundMethodLabelMapping;
    private Map<String,String> refundMethodLabelMap;

    @PostConstruct
    public void init(){
        refundMethodLabelMap = new HashMap<>();
        String[] refundMethodLabelList = refundMethodLabelMapping.split(",");
        if(null != refundMethodLabelList){
            String[] refundMethodLabelArr = null;
            for(String refundMethodLabel : refundMethodLabelList){
                refundMethodLabelArr = refundMethodLabel.split(":");
                if(null != refundMethodLabelArr) {
                    refundMethodLabelMap.put(refundMethodLabelArr[0].toLowerCase().trim(), refundMethodLabelArr[1]);
                }
            }
        }
    }

    @Override
    public String getRefundMethodLabelByRefundMethod(String refundMethod) {
        return refundMethodLabelMap.get(refundMethod.toLowerCase());
    }

    @Override
    public String getRefundMethodSubLabelByRefundMethod(String refundMethod, RefundDetails refundDetail) {
        if(Refund.REFUND_METHOD.CASHFREE.equalsIgnoreCase(refundMethod))
            return (StringUtils.isNotBlank(refundDetail.getCashFreeLink()) ? Constant.PREFIX_CASHFREE + refundDetail.getCashFreeLink() : refundDetail.getCashFreeLink());
        if(RefundMethod.LKCASH.getCode().equalsIgnoreCase(refundMethod))
            return (StringUtils.isNotBlank(refundDetail.getLkCashReferenceNumber()) ? Constant.PREFIX_LKCASH_OR_LKCASHPLUS + refundDetail.getLkCashReferenceNumber() : refundDetail.getLkCashReferenceNumber());
        if(RefundMethod.LKCASHPLUS.getCode().equalsIgnoreCase(refundMethod))
            return (StringUtils.isNotBlank(refundDetail.getLkCashPlusReferenceNumber()) ? Constant.PREFIX_LKCASH_OR_LKCASHPLUS + refundDetail.getLkCashPlusReferenceNumber() : refundDetail.getLkCashPlusReferenceNumber());
        if(Refund.REFUND_METHOD.STORECREDIT.equalsIgnoreCase(refundMethod))
            return (StringUtils.isNotBlank(refundDetail.getStoreCreditCode()) ? Constant.PREFIX_SC + refundDetail.getStoreCreditCode() : refundDetail.getStoreCreditCode());
        return (StringUtils.isNotBlank(refundDetail.getBankReferenceNumber()) ? Constant.PREFIX_ARN + refundDetail.getBankReferenceNumber() : refundDetail.getBankReferenceNumber());
    }
}
