package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.ReturnHistoryDTO;
import com.lenskart.returncommon.model.dto.ReturnTrackingEventDTO;
import com.lenskart.returncommon.model.request.GetBulkCancellationEventsRequest;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.response.BulkCancellationEvent;
import com.lenskart.returnrepository.entity.ReturnEvent;

import java.util.List;

public interface IReturnEventService {
    void persistEvent( Integer requestId, Integer returnId, String event, String remarks);
    void createReturnEvent(Integer requestId, Integer returnId, String event, String remarks);
    List<ReturnEvent> getReturnEvent(Integer returnId);
    void createTrackingEvent(ReturnEvent returnEvent);
    void createAthenaTrackingEvent(ReturnTrackingEventDTO returnTrackingEventDTO);
    Integer getRequestId(Integer returnId);
    List<Integer> getReturnIds(Integer requestId);

    List<ReturnHistoryDTO> getReturnHistory(Integer returnId);

    List<BulkCancellationEvent> getBulkCancellationHistory(GetBulkCancellationEventsRequest request);
}
