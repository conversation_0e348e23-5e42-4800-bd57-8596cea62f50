package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnPolicyResponse;
import com.lenskart.returnrepository.entity.RefundRules;
import com.lenskart.returnrepository.entity.ReturnRefundRule;

import java.util.List;
import java.util.Map;

public interface IReturnRefundRuleService {
    ReturnRefundResponseDTO fetchReturnRefundRule(ReturnRefundInputDTO inputDTO);

    Map<Integer, ReturnPolicyResponse> fetchReturnPolicy(List<ReturnPolicyInputDTO> returnPolicyInputDTOList);

    ReturnRefundRule getRefundRuleUwItemIdAndTriggerPoint(Integer uwItemId, String triggerPoint);

    void persist(ReturnRefundRule returnRefundRule);

    ReturnRefundRule getRule(Integer uwItemId, String returnInitiatedSource);

    String getRuleEngineResponseForDispatch(Integer uwItemId, Integer returnId);

    String getRuleEngineResponseForRefundDispatch(Integer uwItemId, Integer returnId);

    ReturnRefundRule getRuleEngineResponse(Integer uwItemId, Integer returnId);

    void updateReturnRefundRule(Integer uwItemId, Integer returnId, String source);

    Boolean isExchangeItemDispatchable(ExchangedItem exchangedItem);
    Boolean isExchangeItemDispatchable(ExchangeItemDispatchableDTO exchangeItemDispatchableDTO);

    String getRuleEngineResponseForExchangeOrderDispatch(Integer uwItemId, Integer returnId);

    Boolean isDispatchable(Integer returnId, String ruleEngineStatusForDispatch);

    Boolean isRefundDispatchable(ExchangedItem exchangedItem);
    ReturnRefundRule getReturnRefundRule(Integer returnId, String source);

    String getRefundDispatch(ReturnRefundRule returnRefundRule);

    String getExchangeDispatch(ReturnRefundRule returnRefundRule);

    long deliveredDaysBefore(UwOrderDTO uwOrder, List<ShippingStatusDetail> shippingStatusesMaster);
}
