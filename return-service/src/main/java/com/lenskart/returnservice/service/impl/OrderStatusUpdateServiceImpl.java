package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.orderops.model.ItemsStateStatusDto;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.returncommon.model.dto.OrderStatusUpdateDTO;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IOrderStatusUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OrderStatusUpdateServiceImpl implements IOrderStatusUpdateService {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private IKafkaService kafkaService;

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    private record Priority(int priority, int closedStatusPriority) {
    }

    @Override
    public void updateOrderStatus(List<Map<String, Object>> orderStatusUpdateDTOS) {
        log.info("[updateOrderStatus] enter orderStatusUpdateDTOS : {}", orderStatusUpdateDTOS);
        try {
            ResponseEntity<List<Map<String, String>>> response = orderOpsFeignClient.getOrderStatusPriorityDetails();
            log.info("[updateOrderStatus] orderStatusPriorityDetails : {}", response);
            List<Map<String, String>> priorityDetails = response.getBody();

            if (response.getStatusCode().is2xxSuccessful() && !CollectionUtils.isEmpty(priorityDetails)) {
                log.info("[updateOrderStatus] statusPriorityDetailsBody : {}", priorityDetails);
                for (Map<String, Object> map : orderStatusUpdateDTOS) {
                    OrderStatusUpdateDTO dto = objectMapper.convertValue(map, OrderStatusUpdateDTO.class);

                    OrderStatusUpdateDetails updateDetails = buildOrderStatusUpdateDetails(dto, priorityDetails);

                    log.info("[updateOrderStatus] orderStatusUpdateDetails : {}", updateDetails);
                    ResponseEntity<Boolean> updateResponse = orderOpsFeignClient.orderStatusUpdate(updateDetails);
                    log.info("[updateOrderStatus] response : {}", updateResponse);

                    saveUpdateOrderStatusEvent(map, updateDetails);
                }
            }
        } catch (Exception e) {
            log.error("[updateOrderStatus] error : ", e);
        }
        log.info("[updateOrderStatus] exit");
    }

    private OrderStatusUpdateDetails buildOrderStatusUpdateDetails(OrderStatusUpdateDTO dto, List<Map<String, String>> priorityDetails) {
        OrderStatusUpdateDetails updateDetails = new OrderStatusUpdateDetails();
        updateDetails.setIncrementId(dto.getIncrementId());
        updateDetails.setUnicomOrderCode(dto.getUnicomOrderCode());

        setOrderLevelStatus(dto.getCurrentStatus(), priorityDetails, updateDetails);
        updateDetails.setItemsStateStatusDto(setItemLevelStatuses(dto.getMagentoItems(), priorityDetails));

        return updateDetails;
    }

    private void setOrderLevelStatus(String currentStatus, List<Map<String, String>> priorityDetails, OrderStatusUpdateDetails details) {
        Priority priority = getPriority(priorityDetails, currentStatus);
        if (priority.priority() > priority.closedStatusPriority()) {
            details.setState("closed");
            details.setStatus(currentStatus);
        } else {
            details.setState("closed");
            details.setStatus("closed");
        }
    }

    private List<ItemsStateStatusDto> setItemLevelStatuses(List<ItemsStateStatusDto> items, List<Map<String, String>> priorityDetails) {
        if (CollectionUtils.isEmpty(items)) {
            return items;
        }

        for (ItemsStateStatusDto item : items) {
            Priority priority = getPriority(priorityDetails, item.getOldStatus());
            if (priority.priority() > priority.closedStatusPriority()) {
                item.setState("closed");
                item.setStatus(item.getOldStatus());
            } else {
                item.setState("closed");
                item.setStatus("closed");
            }
        }
        return items;
    }

    private static Priority getPriority(List<Map<String, String>> priorityDetails, String statusToMatch) {
        int priority = -1;
        int closedPriority = -1;

        for (Map<String, String> entry : priorityDetails) {
            String status = entry.get("status");
            int value = Integer.parseInt(entry.get("priority"));
            if (statusToMatch.equalsIgnoreCase(status)) {
                priority = value;
            }
            if ("closed".equalsIgnoreCase(status)) {
                closedPriority = value;
            }
        }

        return new Priority(priority, closedPriority);
    }

    private void saveUpdateOrderStatusEvent(Map<String, Object> orderStatusUpdateDTO, OrderStatusUpdateDetails orderStatusUpdateDetails) {
        if (orderStatusUpdateDTO.get("returnId") != null) {
            ReturnEvent returnEvent = new ReturnEvent();
            returnEvent.setReturnId(Integer.parseInt(orderStatusUpdateDTO.get("returnId").toString()));
            returnEvent.setEvent("ORDER_STATUS_UPDATED");
            returnEvent.setRemarks("updates status : " + orderStatusUpdateDetails.getStatus() + " , state : " + orderStatusUpdateDetails.getState() + " for unicom_order_code : " + orderStatusUpdateDTO.get("unicomOrderCode"));
            kafkaService.pushToKafka("return_event_queue", String.valueOf(orderStatusUpdateDTO.get("incrementId")), returnEvent);
        }
    }
}
