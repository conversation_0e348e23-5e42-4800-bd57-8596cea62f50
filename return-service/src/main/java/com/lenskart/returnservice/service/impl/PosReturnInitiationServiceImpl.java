package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ccautils.constants.CosmosConstants;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.RefundDispatchRequest;
import com.lenskart.returncommon.model.request.AutoApprovalRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.ordermetadata.dto.response.RefundDispatchResponse;
import com.lenskart.refund.client.model.enums.RefundTriggerPoint;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.enums.RefundMethod;
import com.lenskart.returncommon.model.request.InsuranceReturnKafkaRequest;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import com.lenskart.returnservice.utils.MvcAutoApproveReturnThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Service(value = "POS_ReturnInitiationServiceImpl")
public class PosReturnInitiationServiceImpl extends ReturnInitiationAbstractionService implements IReturnInitiationService {

    @Autowired
    private IReturnOrderActionService returnOrderActionService;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private IRefundUtilsService refundUtilsService;
    @Autowired
    private ReversePickUpServiceImpl reversePickUpService;
    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;
    @Autowired
    private ICommunicationService communicationService;
    @Autowired
    private IReturnEventService returnEventService;
    @Autowired
    private MvcAutoApproveReturnThreadPool mvcAutoApproveReturnThreadPool;

    @Autowired
    private ReturnUtil returnUtil;

    @Autowired
    private IKafkaService kafkaService;

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Value("${source.nav.channel:BRWEBDTC}")
    private String sourceNavChannels;


    @Override
    public ReturnCreationResponse createReturn(ReturnCreationRequestDTO returnCreationRequest) throws Exception {
        Map<Long, ReturnItemDTO> magentoItemToReturnItemMap = new HashMap<>();
        Map<Long, Integer> magentoItemToReturnIdMap = new HashMap<>();
        Map<Long, String> returnIntentMap = new HashMap<>();
        List<ReturnItemDTO> exchangeItemList = new ArrayList<>();
        List<ReturnItemDTO> returnItemsUpdated = new ArrayList<>();
        List<UwOrderDTO> uwOrders = new ArrayList<>();
        Map<ReturnItemDTO, Integer> productIdsMap = new HashMap<>();
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = getOrderDetails(returnCreationRequest, magentoItemToReturnItemMap, returnIntentMap, exchangeItemList, returnItemsUpdated, uwOrders, productIdsMap);
        String identifierType = purchaseOrderDetailsDTO.getIdentifierType();
        String identifierValue = purchaseOrderDetailsDTO.getIdentifierValue();
        Integer incrementId = purchaseOrderDetailsDTO.getOrdersHeaderDTO().getIncrementId();
        ReturnRequest returnRequest = createReturnRequest(returnCreationRequest, magentoItemToReturnItemMap, returnIntentMap, identifierType, identifierValue, incrementId);
        enrichReturnCreationRequest(returnCreationRequest, productIdsMap, returnItemsUpdated, incrementId);
        ReverseCourierDetail reverseCourierDetail = getReverseCourierDetail();
        ReturnCreationResponse returnCreationResponse = createReturn(purchaseOrderDetailsDTO, returnCreationRequest, reverseCourierDetail, returnRequest);
        unHoldExchange(returnCreationResponse, returnRequest);
        triggerRefund(returnCreationResponse);
        getMagentoItemToReturnIdMap(returnCreationResponse, magentoItemToReturnIdMap);
        statusUpdate(returnCreationRequest, uwOrders, returnRequest.getId(), returnCreationResponse.getReturnId());
        updateInsuranceReturnRequest(returnCreationRequest, returnRequest.getId(), returnCreationResponse.getReturnId());
        triggerCommunication(returnCreationRequest, reverseCourierDetail, returnRequest.getId(), purchaseOrderDetailsDTO.getOrders().get(0), returnCreationResponse.getReturnId());
        returnCreationResponse = getReturnCreationResponse(reverseCourierDetail, returnCreationResponse, incrementId, uwOrders);
        performPostRefundActivities(returnCreationRequest, returnCreationResponse, magentoItemToReturnIdMap, returnRequest.getId(), returnCreationResponse.getReturnId());
        autoApproveForMvcCustomers(returnCreationRequest, purchaseOrderDetailsDTO, returnCreationResponse);
        sendDataToNuggetTopic(returnCreationRequest, purchaseOrderDetailsDTO, returnCreationResponse);
        return returnCreationResponse;
    }

    private void triggerRefund(ReturnCreationResponse returnCreationResponse) {
        log.info("[triggerRefund] checking and initiating refund for : {}", returnCreationResponse);
        Optional<ReturnDetail> optionalReturnDetail = returnOrderActionService.findReturnOrderById(returnCreationResponse.getReturnId());
        optionalReturnDetail.ifPresent(returnDetail -> reversePickUpService.checkAndInitiateRefundRequest(returnDetail, "ReturnInitiation"));
    }

    private void autoApproveForMvcCustomers(ReturnCreationRequestDTO returnCreationRequest, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, ReturnCreationResponse returnCreationResponse) {
        try {
            String eventName = returnCreationResponse.getResult().getReturns().get(0).getEventName();
            if (CosmosConstants.Events.RETURN_NEED_APPROVAL.toString().equalsIgnoreCase(eventName) ||
                    CosmosConstants.Events.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.toString().equalsIgnoreCase(eventName)) {
                returnCreationResponse.setPurchaseOrderDetailsDTO(purchaseOrderDetailsDTO);
                log.info("Auto Approval request params- returnId : {} ,returnSource:{} , needApproval:{} , returnMethod:{}", returnCreationResponse.getResult().getReturns().get(0).getReturnId(), returnCreationRequest.getReturnSource().getSource(), returnCreationRequest.getItems().get(0).getNeedApproval(), returnCreationRequest.getReturnMethod());

                AutoApprovalRequest autoApprovalRequest = new AutoApprovalRequest();
                autoApprovalRequest.setReturnCreationRequest(returnCreationRequest);
                autoApprovalRequest.setRefundRequestResponse(returnCreationResponse);
                autoApprovalRequest.setRetryCount(0);
                kafkaService.pushToKafka("return_auto_approval_queue", String.valueOf(returnCreationResponse.getReturnId()), autoApprovalRequest);
            }
        } catch (Exception exception) {
            log.error("[autoApproveForMvcCustomers] exception occurred : {}", exception.getMessage());
        }
    }

    private void sendDataToNuggetTopic(ReturnCreationRequestDTO returnCreationRequest, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, ReturnCreationResponse returnCreationResponse) {
        try {
            String eventName = returnCreationResponse.getResult().getReturns().get(0).getEventName();
            if (CosmosConstants.Events.RETURN_NEED_APPROVAL.toString().equalsIgnoreCase(eventName)) {
                returnCreationResponse.setPurchaseOrderDetailsDTO(purchaseOrderDetailsDTO);
                log.info("sendDataToNuggetTopic request params- returnId : {} ,returnSource:{} , needApproval:{} , returnMethod:{}", returnCreationResponse.getResult().getReturns().get(0).getReturnId(), returnCreationRequest.getReturnSource().getSource(), returnCreationRequest.getItems().get(0).getNeedApproval(), returnCreationRequest.getReturnMethod());

                AutoApprovalRequest autoApprovalRequest = new AutoApprovalRequest();
                autoApprovalRequest.setReturnCreationRequest(returnCreationRequest);
                autoApprovalRequest.setRefundRequestResponse(returnCreationResponse);
                autoApprovalRequest.setRetryCount(0);
                kafkaService.pushToKafka("posting_data_to_nugget_queue", String.valueOf(returnCreationResponse.getReturnId()), autoApprovalRequest);
            }
        } catch (Exception exception) {
            log.error("[sendDataToNuggetTopic] exception occurred : {}", exception.getMessage());
        }
    }

    @Override
    public ReverseCourierDetail assignReverseCourier(Integer orderId, Integer incrementId, Boolean isNewAddress, Integer pincodeInRequest, int offset, QCType qcType, boolean newFlow, OrderInfoResponseDTO orderInfoResponseDTO) throws Exception {
        return null;
    }

    @Override
    public Integer getQcRuleCount() {
        return null;
    }

    private void triggerCommunication(ReturnCreationRequestDTO returnCreationRequest, ReverseCourierDetail reverseCourierDetail, Integer requestId, OrdersDTO ordersDTO, Integer returnId) {
        log.info("[triggerCommunication] returnId : {}", returnId);
        returnCreationRequest.getItems().stream().filter(r -> r.getNeedApproval()
                        && !Constant.COURIER.LKART.equalsIgnoreCase(reverseCourierDetail.getCourier()))
                .forEach(r -> {
                    CommRequestDTO commRequestDTO = new CommRequestDTO();
                    commRequestDTO.setEventType(Constant.EVENT.RETURN_NEED_APPROVAL);
                    String lkCountry = null;
                    if (returnCreationRequest.getPurchaseOrderDetailsDTO() != null && returnCreationRequest.getPurchaseOrderDetailsDTO().getOrdersHeaderDTO() != null) {
                        lkCountry = returnCreationRequest.getPurchaseOrderDetailsDTO().getOrdersHeaderDTO().getLkCountry();
                    }
                    if ("SG".equalsIgnoreCase(lkCountry)) {
                        commRequestDTO.setConditionsMap(Map.of("country", "SG"));
                    }
                    commRequestDTO.setReturnRequestId(requestId);
                    commRequestDTO.setReturnId(returnId);
                    commRequestDTO.setOrderId(ordersDTO.getIncrementId());
                    communicationService.pushToCommunicationTopic(commRequestDTO);
                });
        if (null != ordersDTO) {
            List<ReturnDetail> returnOrderList = returnOrderActionService.findAllByIncrementId(ordersDTO.getIncrementId());
            if (!CollectionUtils.isEmpty(returnOrderList)) {
                returnOrderList.stream().filter(r -> r.getReturnMethod().equalsIgnoreCase("StoreReceiving")
                                && Constant.STATUS.RETURN_REFUNDED.equalsIgnoreCase(returnOrderActionService.getReturnOrderStatusById(r.getId())))
                        .forEach(r -> {
                            Optional<ReturnDetail> returnOrderDTO = returnOrderActionService.findReturnOrderById(returnId);
                            if (returnOrderDTO.isPresent()) {
                                CommRequestDTO commRequestDTO = new CommRequestDTO();
                                commRequestDTO.setEventType(Constant.EVENT.RETURN_STORE_RECEIVING);
                                String currStatus = returnOrderActionService.getReturnOrderStatus(returnOrderDTO.get());
                                if (currStatus.equalsIgnoreCase(Constant.RETURN_STATUS.RETURN_CANCELLED) && returnOrderDTO.get().getSource().equalsIgnoreCase("vsm")) {
                                    commRequestDTO.setConditionsMap(Map.of("status", "cancelled"));
                                } else if (returnOrderDTO.get().getReturnMethod().equalsIgnoreCase("StoreReceiving")) {
                                    commRequestDTO.setConditionsMap(Map.of("returnMethod", "StoreReceiving"));
                                } else {
                                    commRequestDTO.setConditionsMap(Map.of("flow", "dispensing_assigned_master"));
                                }
                                commRequestDTO.setReturnRequestId(requestId);
                                commRequestDTO.setReturnId(returnId);
                                commRequestDTO.setOrderId(ordersDTO.getIncrementId());
                                communicationService.pushToCommunicationTopic(commRequestDTO);
                            }
                        });
            }
        }

        CommRequestDTO commRequestDTO = new CommRequestDTO();
        commRequestDTO.setEventType(Constant.EVENT.DELIGHT_NEED_APPROVAL);
        commRequestDTO.setReturnRequestId(requestId);
        commRequestDTO.setReturnId(returnId);
        commRequestDTO.setOrderId(ordersDTO.getIncrementId());
        log.info("[triggerCommunication] commRequestDTO : {}", commRequestDTO);
        communicationService.pushToCommunicationTopic(commRequestDTO);

    }

    private void performPostRefundActivities(ReturnCreationRequestDTO returnCreationRequest, ReturnCreationResponse returnCreationResponse, Map<Long, Integer> magentoItemToReturnIdMap, Integer requestId, Integer returnId) {
        try{
            boolean isNewFlow = refundUtilsService.refundServiceSwitch(Constant.IDENTIFIER_TYPE.ORDER_ID, String.valueOf(returnCreationRequest.getIncrementId()));
            if (isNewFlow) {
                log.info("[PosInitiateServiceImpl][postRefundNew]: returnCreationRequest={} , refundRequestResponse={}", returnCreationRequest, returnCreationResponse);
                for (ReturnItemDTO item : returnCreationRequest.getItems()) {
                    log.info("[PosInitiateServiceImpl][postRefundNew] : refund method : {} uw_item_id : {}", item.getRefundMethod(), item.getUwItemId());
                    log.info("[PosInitiateServiceImpl][postRefundNew] postRefund : refund_method={} UwItemId={} ReturnId={}", item.getRefundMethod(), item.getUwItemId(), returnId);
                    RefundDispatchRequest refundItemDispatchableReqDTO = new RefundDispatchRequest();
                    refundItemDispatchableReqDTO.setUwItemId(item.getUwItemId());
                    refundItemDispatchableReqDTO.setReturnId(magentoItemToReturnIdMap.get(item.getMagentoId()));
                    refundItemDispatchableReqDTO.setRefundTarget(item.getRefundMethod());
                    refundItemDispatchableReqDTO.setRefundTriggerPoint(RefundTriggerPoint.RETURN_INITIATION.getName());
                    RefundDispatchResponse refundItemDispatchableRespDTO = refundUtilsService.isRefundDispatchableAtPOS(refundItemDispatchableReqDTO);
                    if (!"exchange".equalsIgnoreCase(item.getRefundMethod()) && item.getDoRefund() && !item.getNeedApproval()
                            && refundItemDispatchableRespDTO.getIsRefundDispatchable()) {
                        if (!returnCreationRequest.isEnforceRefundAtStore()) {
                            returnEventService.createReturnEvent(requestId, returnId, "ORDER_STATUS_UPDATED", "status updated to closed for uw_item_id : {}" + item.getUwItemId());
                            statusUpdate(returnCreationResponse.getUwOrders());
                        }
                    } else {
                        log.info("[PosInitiateServiceImpl][postRefundNew] Sending sms for successful return at POS uw_item_id={}", item.getUwItemId());
                        CommRequestDTO commRequestDTO = new CommRequestDTO();
                        commRequestDTO.setEventType(Constant.EVENT.RETURN_SUCCESS_MESSAGE_POS);
                        commRequestDTO.setReturnRequestId(requestId);
                        commRequestDTO.setReturnId(returnId);
                        commRequestDTO.setOrderId(returnCreationRequest.getIncrementId());
                        String refundMethod = item.getRefundMethod();
                        if(refundMethod != null){
                            switch (RefundMethod.getName(refundMethod)) {
                                case CASHFREE -> {
                                    commRequestDTO.setConditionsMap(Map.of("refundMethod", RefundMethod.CASHFREE.getName()));
                                    communicationService.pushToCommunicationTopic(commRequestDTO);
                                }
                                case STORECREDIT -> {
                                    commRequestDTO.setConditionsMap(Map.of("refundMethod", RefundMethod.STORECREDIT.getName()));
                                    communicationService.pushToCommunicationTopic(commRequestDTO);
                                }
                                case SOURCE -> {
                                    commRequestDTO.setConditionsMap(Map.of("refundMethod", RefundMethod.SOURCE.getName()));
                                    communicationService.pushToCommunicationTopic(commRequestDTO);
                                }
                                default -> {
                                }
                            }
                        }
                        if (!returnCreationRequest.isEnforceRefundAtStore()) {
                            log.info("[PosInitiateServiceImpl][postRefundNew] Updating order status for uw_item_id={}", item.getUwItemId());
                            statusUpdate(returnCreationResponse.getUwOrders());
                        }
                    }
                }
            }
        }catch (Exception exception){
            log.error("[performPostRefundActivities] exception : "+exception);
        }
    }

    private void getMagentoItemToReturnIdMap(ReturnCreationResponse returnCreationResponse, Map<Long, Integer> magentoItemToReturnIdMap) {
        if (returnCreationResponse != null
                && returnCreationResponse.getResult() != null
                && !CollectionUtils.isEmpty(returnCreationResponse.getResult().getReturns())) {
            for (Returns returns : returnCreationResponse.getResult().getReturns()) {
                magentoItemToReturnIdMap.put(returns.getMagentoItemId(), returns.getReturnId());
            }
        }
    }

    private void statusUpdate(ReturnCreationRequestDTO returnCreationRequest, List<UwOrderDTO> uwOrders, Integer requestId, Integer returnId) {
        log.info("[statusUpdate] returnId : {}, uwORders : {}", returnId, uwOrders);
        returnCreationRequest.getItems().stream()
                .filter(item -> "exchange".equalsIgnoreCase(item.getRefundMethod()))
                .findFirst()
                .ifPresent(item -> {
                    statusUpdate(uwOrders, returnCreationRequest.getItems(), returnId);
                });
    }

    private void unHoldExchange(ReturnCreationResponse returnCreationResponse, ReturnRequest request) {
        log.info("[unHoldExchange] order : {}, returnCreationResponse : {}", request.getIncrementId(), returnCreationResponse);
        for(Returns returns : returnCreationResponse.getResult().getReturns()){
            ExchangeUnholdHelperDTO exchangeUnholdHelperDTO = new ExchangeUnholdHelperDTO(returns.getReturnId(), returns.getUwItemId(), request.getId(), request.getIncrementId());
            kafkaService.pushToKafka("exchange_order_unhold_helper_queue", String.valueOf(request.getIncrementId()), exchangeUnholdHelperDTO);
        }
    }


    private void updateInsuranceReturnRequest(ReturnCreationRequestDTO returnCreationRequest, Integer requestId, Integer returnId) {
        returnCreationRequest.getItems().stream().filter(r -> null != r
                        && r.getNeedApproval()
                        && r.getClaimInsurance() != null
                        && r.getClaimInsurance())
                .forEach(r -> {
                    InsuranceReturnKafkaRequest insuranceReturnKafkaRequest = new InsuranceReturnKafkaRequest();
                    insuranceReturnKafkaRequest.setMagentoItemId(r.getMagentoId());
                    insuranceReturnKafkaRequest.setStoreEmail(returnCreationRequest.getStoreEmail());
                    insuranceReturnKafkaRequest.setFacilityCode(returnCreationRequest.getFacilityCode());
                    insuranceReturnKafkaRequest.setRetryCount(1);
                    insuranceReturnKafkaRequest.setReturnId(returnId);
                    returnEventService.createReturnEvent(requestId, returnId, Constant.EVENT.PUSHED_TO_INSURANCE_QUEUE, "magentoId is : " + r.getMagentoId());
                    insuranceService.pushInsuranceReturnRequestToKafka(insuranceReturnKafkaRequest);
                });
    }

    private ReverseCourierDetail getReverseCourierDetail() {
        return new ReverseCourierDetail();
    }
}
