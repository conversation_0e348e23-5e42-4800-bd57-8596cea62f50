package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailAddressUpdate;
import com.lenskart.returnrepository.repository.ReturnOrderAddressUpdateRepository;
import com.lenskart.returnservice.service.IRefundDispatchTatService;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class ReturnHeadingPlaceHolderValueFetchRefundDateStrategy implements IReturnHeadingPlaceHolderValueFetchStrategy {

    @Autowired
    ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Autowired
    IReturnOrderActionService returnOrderActionService;
    @Autowired
    IRefundDispatchTatService refundDispatchTatService;

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse) {
        List<RefundDetails> refundDetailsList = returnDetailsResponse.getRefundDetails();
        ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
        ;
        Date refundEstimateDate = null;
        try {
            if (CollectionUtils.isNotEmpty(refundDetailsList)) {
                Map<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();
                for (RefundDetails refundDetails : refundDetailsList) {
                    //  if (!Refund.STATUS.REFUND_PENDING.equalsIgnoreCase(refundDetails.getStatus())) {
                    ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
                    if (returnDetail != null) {
                        if (Constant.RETURN_METHOD.SHIP_TO_LENSKRT.equalsIgnoreCase(returnDetail.getReturnMethod())) {
                            returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                        } else {
                            if (Constant.RETURN_METHOD.SHIP_TO_LENSKRT.equalsIgnoreCase(returnDetail.getReturnMethod()) ||
                                    Constant.RETURN_METHOD.RETURN_TO_STORE.equalsIgnoreCase(returnDetail.getReturnMethod())) {
                                returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                            } else {
                                ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                                if (null != returnOrderAddressUpdate && null != returnOrderAddressUpdate.getPostcode()) {
                                    OrdersHeaderDTO ordersHeader = returnDetailsResponse.getOrdersHeaderDTO();
                                    String paymentMode = ordersHeader.getPaymentMode();//to be changed to paymentMode
                                    refundMethodToActualPaymentMethodMap.put(refundDetails.getRefundMethod(), paymentMode);
                                    String status = Constant.REFUND_STATUS.REFUND_PENDING.equalsIgnoreCase(refundDetails.getStatus())
                                            ? Constant.REFUND_STATUS.REFUND_COMPLETE : refundDetails.getStatus();
                                    Date currentRefundDispatchDate = refundDispatchTatService.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, returnDetailsResponse.getRefundDispatchPoint(),
                                            String.valueOf(returnOrderAddressUpdate.getPostcode()), status, returnDetail.getReturnCreateDatetime(), returnDetailsResponse.getReturnId());
                                    if (null != currentRefundDispatchDate) {
                                        if (null == refundEstimateDate) {
                                            refundEstimateDate = currentRefundDispatchDate;
                                        } else {
                                            if (currentRefundDispatchDate.after(refundEstimateDate)) {
                                                refundEstimateDate = currentRefundDispatchDate;
                                            }
                                        }
                                    }
                                }
                                // }
                            }
                        }
                    }
                }
            }
            if (null != refundEstimateDate) {
                if (null != returnDetailsResponse) {
                    returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
                    if (null != returnStatusHeadingDetail) {
                        returnStatusHeadingDetail.setEstimatedDate(refundEstimateDate.getTime());
                    }
                }
            } else {
                if (null != returnDetailsResponse) {
                    returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
                    if (null != returnStatusHeadingDetail) {
                        returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                    }
                }
            }
        } catch (Throwable e) {
            log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
            if (null != returnStatusHeadingDetail) {
                returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
            }
        }
    }

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse, List<RefundDetails> refundDetailsList, ReturnStatusHeadingDetail returnStatusHeadingDetail, Long incrementId, Date createdAt, String anyReceivingPoint) {
        Date refundEstimateDate = null;
        try {
            if (CollectionUtils.isNotEmpty(refundDetailsList)) {
                Map<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();
                for (RefundDetails refundDetails : refundDetailsList) {
                    //  if (!Refund.STATUS.REFUND_PENDING.equalsIgnoreCase(refundDetails.getStatus())) {
                    OrderAddressUpdateDTO orderAddressUpdate = returnDetailsResponse.getOrderAddressUpdates().stream().filter(oa -> oa.getAddressType().equalsIgnoreCase("Shipping")).findFirst().orElse(null);
                    if (null != orderAddressUpdate && null != orderAddressUpdate.getPostcode()) {
                        OrdersHeaderDTO ordersHeader = returnDetailsResponse.getOrdersHeaderDTO();
                        String paymentMode = ordersHeader.getPaymentMode();//to be changed to paymentMode
                        refundMethodToActualPaymentMethodMap.put(refundDetails.getRefundMethod(), paymentMode);
                        String status = Constant.REFUND_STATUS.REFUND_PENDING.equalsIgnoreCase(refundDetails.getStatus())
                                ? Constant.REFUND_STATUS.REFUND_COMPLETE : refundDetails.getStatus();
                        Date currentRefundDispatchDate = refundDispatchTatService.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, anyReceivingPoint,
                                String.valueOf(orderAddressUpdate.getPostcode()), status, createdAt, null);
                        if (null != currentRefundDispatchDate) {
                            if (null == refundEstimateDate) {
                                refundEstimateDate = currentRefundDispatchDate;
                            } else {
                                if (currentRefundDispatchDate.after(refundEstimateDate)) {
                                    refundEstimateDate = currentRefundDispatchDate;
                                }
                            }
                        }
                    }
                }
            }
            if (null != refundEstimateDate) {
                if (null != returnStatusHeadingDetail) {
                    returnStatusHeadingDetail.setEstimatedDate(refundEstimateDate.getTime());
                }
            } else {
                if (null != returnStatusHeadingDetail) {
                    returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                }
            }
        } catch (Throwable e) {
            log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
            if (null != returnStatusHeadingDetail) {
                returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
            }
        }
    }

    @Override
    public void getReturnTimelinePlaceHolderValue(ReturnTimeLine returnTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            try {
                if (null != returnStatusHeadingDetail) {
                    if (null != returnStatusHeadingDetail.getEstimatedDate()) {
                        returnTimeLine.setReturnEstimatedDate(returnStatusHeadingDetail.getEstimatedDate());
                    } else {
                        returnTimeLine.setReturnSubStatus("");
                    }
                }
            } catch (Throwable e) {
                log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                if (null != returnTimeLine) {
                    returnTimeLine.setReturnSubStatus("");
                }
            }
        }
    }

    @Override
    public void getRefundTimelinePlaceHolderValue(RefundTimeLine refundTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            try {
                if (null != returnStatusHeadingDetail) {
                    Date refundEstimateDate = null;
                    List<RefundDetails> refundDetailsList = returnDetailsResponse.getRefundDetails();
                    try {
                        if (CollectionUtils.isNotEmpty(refundDetailsList)) {
                            Map<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();
                            for (RefundDetails refundDetails : refundDetailsList) {
                                //  if (!Refund.STATUS.REFUND_PENDING.equalsIgnoreCase(refundDetails.getStatus())) {
                                ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
                                if (returnDetail != null) {
                                    if (Constant.RETURN_METHOD.SHIP_TO_LENSKRT.equalsIgnoreCase(returnDetail.getReturnMethod()) ||
                                            Constant.RETURN_METHOD.RETURN_TO_STORE.equalsIgnoreCase(returnDetail.getReturnMethod())) {
                                        refundTimeLine.setRefundSubStatus("");
                                    } else {
                                        ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                                        if (null != returnOrderAddressUpdate && null != returnOrderAddressUpdate.getPostcode()) {
                                            OrdersHeaderDTO ordersHeader = returnDetailsResponse.getOrdersHeaderDTO();
                                            String paymentMode = ordersHeader.getPaymentMode();//to be changed to paymentMode
                                            refundMethodToActualPaymentMethodMap.put(refundDetails.getRefundMethod(), paymentMode);
                                            Date currentRefundDispatchDate = refundDispatchTatService.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, returnDetailsResponse.getRefundDispatchPoint(), String.valueOf(returnOrderAddressUpdate.getPostcode()),
                                                    refundTimeLine.getRefundStatus(), returnDetail.getReturnCreateDatetime(), returnDetailsResponse.getReturnId());
                                            if (null != currentRefundDispatchDate) {
                                                if (null == refundEstimateDate) {
                                                    refundEstimateDate = currentRefundDispatchDate;
                                                } else {
                                                    if (currentRefundDispatchDate.after(refundEstimateDate)) {
                                                        refundEstimateDate = currentRefundDispatchDate;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            String refundMethod = returnDetailsResponse.getRefundMethodRequest();
                            if (StringUtils.isNotBlank(refundMethod)) {
                                Map<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();
                                ReturnDetail returnDetail = returnOrderActionService.findReturnOrderById(returnDetailsResponse.getReturnId()).orElse(null);
                                if (returnDetail != null) {
                                    if (Constant.RETURN_METHOD.SHIP_TO_LENSKRT.equalsIgnoreCase(returnDetail.getReturnMethod()) ||
                                            Constant.RETURN_METHOD.RETURN_TO_STORE.equalsIgnoreCase(returnDetail.getReturnMethod())) {
                                        refundTimeLine.setRefundSubStatus("");
                                    } else {
                                        ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(returnDetail.getGroupId());
                                        if (null != returnOrderAddressUpdate && null != returnOrderAddressUpdate.getPostcode()) {
                                            OrdersHeaderDTO ordersHeader = returnDetailsResponse.getOrdersHeaderDTO();
                                            String paymentMode = ordersHeader.getPaymentMode();//to be changed to paymentMode
                                            refundMethodToActualPaymentMethodMap.put(refundMethod, paymentMode);
                                            Date currentRefundDispatchDate = refundDispatchTatService.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, returnDetailsResponse.getRefundDispatchPoint(), String.valueOf(returnOrderAddressUpdate.getPostcode()),
                                                    refundTimeLine.getRefundStatus(), returnDetail.getReturnCreateDatetime(), returnDetailsResponse.getReturnId());
                                            if (null != currentRefundDispatchDate) {
                                                refundEstimateDate = currentRefundDispatchDate;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (null != refundEstimateDate) {
                            refundTimeLine.setRefundEstimatedDate(refundEstimateDate.getTime());
                        } else {
                            refundTimeLine.setRefundSubStatus("");
                        }
                    } catch (Throwable e) {
                        log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                        if (null != refundTimeLine) {
                            refundTimeLine.setRefundSubStatus("");
                        }
                    }
                }
            } catch (Throwable e) {
                log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                if (null != refundTimeLine) {
                    refundTimeLine.setRefundSubStatus("");
                }
            }
        }

    }

    @Override
    public void getExchangeTimelinePlaceHolderValue(ExchangeTimeLine exchangeTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            try {
                if (null != returnStatusHeadingDetail) {
                    if (null != returnStatusHeadingDetail.getEstimatedDate()) {
                        exchangeTimeLine.setExchangeDispatchDate(returnStatusHeadingDetail.getEstimatedDate());
                    } else {
                        exchangeTimeLine.setExchangeSubStatus("");
                    }
                }
            } catch (Throwable e) {
                log.error("Exception in getReturnHeadingsPlaceHolderValue", e);
                if (null != exchangeTimeLine) {
                    exchangeTimeLine.setExchangeSubStatus("");
                }
            }
        }
    }

    Date getRefundDispatchETA(Map<String, String> refundMethodToActualPaymentMethodMap, String refundDispatchPoint, String reversePincode) {
        return null;
    }

    public void getReturnHistoryPlaceHolderValue(ReturnStatusHistory returnStatusHistory, ReturnDetailsResponse returnDetailsResponse) {

    }
}
