package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.CommunicationTemplate;
import com.lenskart.returnrepository.repository.ICommunicationTemplateRepo;
import com.lenskart.returnservice.service.ICommunicationTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class CommunicationTemplateServiceImpl implements ICommunicationTemplateService {

    @Autowired
    private ICommunicationTemplateRepo communicationTemplateRepo;

    @Lazy
    @Autowired
    private ICommunicationTemplateService communicationTemplateService;

    @Override
    public CommunicationTemplate findByEventTypeAndConditions(String eventType, Map<String, String> conditionsMap) {
        List<CommunicationTemplate> communicationTemplateList = communicationTemplateService.findAll();
        if (communicationTemplateList != null && !communicationTemplateList.isEmpty()) {
            Optional<CommunicationTemplate> communicationTemplate = communicationTemplateList.stream()
                    .filter(t -> t.getEventType().equalsIgnoreCase(eventType) && t.conditionsMet(conditionsMap))
                    .findFirst();

            return communicationTemplate.orElse(null);

        }
        return null;
    }

    @Override
    @Cacheable(value = Constant.COMM_TEMPLATE_CACHE_NAME, key = "'templates'")
    public List<CommunicationTemplate> findAll() {
        return communicationTemplateRepo.findAll();
    }

    @Override
    @CacheEvict(value = Constant.COMM_TEMPLATE_CACHE_NAME, allEntries = true)
    public void reloadCommunicationTemplateCache() {
    }

}
