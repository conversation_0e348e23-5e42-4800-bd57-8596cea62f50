package com.lenskart.returnservice.service.impl;
/* Created by rajiv on 07/02/25 */

import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnservice.service.CosmosEventPayloadProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.lenskart.returnservice.service.impl.RefundUtilsServiceImpl.gson;

@Component
@Slf4j
public class ReturnPickupCosmosEventPayLoadProvider implements CosmosEventPayloadProvider {

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    public Map<String, Object> getPayLoadForPickupScheduled(long groupId, String reverseCourier) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("checkpoint", "headless_return_ReturnCreateCourierMessage");
        eventData.put("groupId", groupId);
        eventData.put("reverseCourier", reverseCourier);
        eventData.put("customerCareNo", "**********");
        return eventData;
    }

    private Long getGroupId(Integer returnId) {
        ReturnDetail returnDetail = getReturnDetail(returnId);
        return returnDetail.getGroupId();
    }

    private ReturnDetail getReturnDetail(Integer returnId) {
        Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnId);
        return returnDetailOptional.orElse(null);
    }

    @Override
    public Map<String, Object> getPayload(Object... params) {
        if (params == null || params.length < 3) {
            log.warn("[ReturnPickupCosmosEventPayLoadProvider] Invalid parameters provided to getPayload. Params received: {}", Arrays.toString(params));
            return Collections.emptyMap();
        }
        Long groupId = getGroupId((Integer) params[2]);
        String reverseCourier = params[3] != null ? (String) params[3] : null;
        return getPayLoadForPickupScheduled(groupId, reverseCourier);
    }
}
