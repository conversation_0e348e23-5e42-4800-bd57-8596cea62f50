package com.lenskart.returnservice.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class DateUtil {
    public static String dayName(String inputDate, String format){
        Date date = null;
        try {
            date = new SimpleDateFormat(format).parse(inputDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return new SimpleDateFormat("EEEE", Locale.ENGLISH).format(date);
    }

    public static void validateDateRange(Date fromDate, Date toDate) throws IllegalArgumentException {

        // Calculate the difference in milliseconds
        long diffInMillis = toDate.getTime() - fromDate.getTime();

        // Convert the difference to days
        long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);

        // Check if the difference exceeds 15 days
        if (diffInDays > 15) {
            throw new IllegalArgumentException("The date range cannot be more than 15 days.");
        }

        if (diffInDays < 0) {
            throw new IllegalArgumentException("toDate cannot be earlier than fromDate.");
        }
    }

    public static void validateDateRange(Date fromDate, Date toDate, int daysLimit) throws IllegalArgumentException {

        // Calculate the difference in milliseconds
        long diffInMillis = toDate.getTime() - fromDate.getTime();

        // Convert the difference to days
        long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);

        // Check if the difference exceeds daysLimit
        if (diffInDays > daysLimit) {
            throw new IllegalArgumentException("The date range cannot be more than 15 days.");
        }

        if (diffInDays < 0) {
            throw new IllegalArgumentException("toDate cannot be earlier than fromDate.");
        }
    }

    // Helper method to get start of the day (00:00:00)
    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    // Helper method to get end of the day (23:59:59)
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public String convertDateToISO8601Format(Date date){

        if(date == null){
            return null;
        }
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        df.setTimeZone(TimeZone.getTimeZone("UTC"));
        return df.format(date);
    }

    public Date getFormattedDate(Date date,Integer noOfDays){
        // convert date to calendar
        Calendar calender = Calendar.getInstance();
        calender.setTime(date);
        // Go back AGE no. of days back in time.
        calender.add(Calendar.DATE, noOfDays);
        return calender.getTime();
    }

    public static String getXMonthOldDate(int month){
        LocalDate currentDate = LocalDate.now();
        LocalDate oneMonthBefore = currentDate.minusMonths(month);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return oneMonthBefore.format(formatter);
    }

    public static String getXMonthAfterDate(int month){
        LocalDate currentDate = LocalDate.now();
        LocalDate oneMonthAfter = currentDate.plusMonths(month);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return oneMonthAfter.format(formatter);
    }

    public String getCurrentDate(){
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return currentDate.format(formatter);
    }

    public static Date getDate(String fromTime) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.parse(fromTime);
    }

    public static String formatDate(String inputDate, String inputPattern, String outputPattern, int daysToAdjust) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(inputPattern);
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(outputPattern);

        LocalDate date = LocalDate.parse(inputDate, inputFormatter);
        LocalDate adjustedDate = date.plusDays(daysToAdjust);

        return outputFormatter.format(adjustedDate);
    }

    public static String getCurrentDateTimeForLog() {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar1 = Calendar.getInstance();
        return sdf1.format(calendar1.getTime());
    }

    public static boolean isWithinThreeMonths(Date date) {
        if (date == null) {
            return false; // null date is not valid
        }

        Calendar cal = Calendar.getInstance();

        cal.add(Calendar.MONTH, -3);
        Date threeMonthsAgo = cal.getTime();

        return !date.before(threeMonthsAgo) && !date.after(new Date());
    }
}
