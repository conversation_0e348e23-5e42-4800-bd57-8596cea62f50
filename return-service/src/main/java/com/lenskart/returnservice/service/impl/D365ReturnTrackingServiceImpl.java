package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.D365ReturnTrackingRequestDTO;
import com.lenskart.returncommon.model.response.D365ReturnTrackingDTO;
import com.lenskart.returncommon.model.response.D365ReturnTrackingResponse;
import com.lenskart.returnrepository.entity.D365ReturnTracking;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnOrder;
import com.lenskart.returnrepository.repository.D365ReturnTrackingRepository;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnrepository.repository.ReturnOrderRepository;
import com.lenskart.returnservice.service.ID365ReturnTrackingService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class D365ReturnTrackingServiceImpl implements ID365ReturnTrackingService {

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    private List<String> receivingStatuses = List.of("return_received","return_refunded");
    private List<String> receivingExchangeStatuses = List.of("return_received","return_exchange");
    private List<String> awaitedRtoStatuses = List.of("return_under_followup","return_pending_approval");
    private List<String> warehouseStatuses = List.of("return_received","return_refunded","return_exchange");
    private List<String> rpuReceivingStatuses = List.of("return_received","awb_assigned","return_exchange");

    @Autowired
    private D365ReturnTrackingRepository d365ReturnTrackingRepository;

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Override
    public void saveReturnTrackingEvent(D365ReturnTrackingRequestDTO d365ReturnTrackingRequestDTO) {
        log.info("[D365ReturnTrackingServiceImpl][saveReturnTrackingEvent] returnId : {}, d365ReturnTrackingRequestDTO : {}", d365ReturnTrackingRequestDTO.getReturnId(), d365ReturnTrackingRequestDTO);
        ReturnOrder returnOrder = returnOrderRepository.findByReturnId(d365ReturnTrackingRequestDTO.getReturnId());
        if(returnOrder != null){
            log.info("[D365ReturnTrackingServiceImpl][saveReturnTrackingEvent] returnId : {}, return created in inventory, hence not saving to d365 table in returndb", d365ReturnTrackingRequestDTO.getReturnId());
            return;
        }
        Integer returnId = d365ReturnTrackingRequestDTO.getReturnId();
        String newStatus = d365ReturnTrackingRequestDTO.getNewStatus();
        String oldStatus = d365ReturnTrackingRequestDTO.getOldStatus();
        oldStatus = StringUtils.isEmpty(oldStatus) ? returnOrderActionService.getReturnOrderStatusById(returnId) : oldStatus;
        newStatus = StringUtils.isEmpty(newStatus) ? oldStatus : newStatus;
        log.info("[D365ReturnTrackingServiceImpl][saveReturnTrackingEvent] returnId : {}, newStatus : {}, oldStatus : {}", d365ReturnTrackingRequestDTO.getReturnId(), newStatus, oldStatus);
        Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnId);
        if(returnDetailOptional.isPresent()){
            ReturnDetail returnDetail = returnDetailOptional.get();
            String returnType = returnDetail.getReturnType();
            String returnMethod = returnDetail.getReturnMethod();
            String source = returnDetail.getSource();
            log.info("[saveReturnTrackingEvent] returnType : {} , returnMethod : {} , source : {} , returnId : {}, oldStatus : {}, newStatus : {}", returnType, returnMethod, source, returnId, oldStatus, newStatus);


            if(("StoreReceiving".equalsIgnoreCase(returnMethod) && receivingExchangeStatuses.contains(newStatus.toLowerCase()))
                    || ("RPU".equalsIgnoreCase(returnMethod) && rpuReceivingStatuses.contains(newStatus.toLowerCase()))
                    || ("rto".equalsIgnoreCase(returnType) && receivingStatuses.contains(newStatus.toLowerCase()))
                    || ("awaited_rto".equalsIgnoreCase(returnType) && awaitedRtoStatuses.contains(newStatus.toLowerCase()))
                    || ("WAREHOUSE".equalsIgnoreCase(source) && warehouseStatuses.contains(newStatus.toLowerCase()))){
                List<D365ReturnTracking> d365ReturnTrackingList = d365ReturnTrackingRepository.findByReturnId(returnId);
                if(!CollectionUtils.isEmpty(d365ReturnTrackingList)){
                    for(D365ReturnTracking d365ReturnTracking : d365ReturnTrackingList){
                        updateD365SyncData(d365ReturnTracking, d365ReturnTrackingRequestDTO);
                        d365ReturnTrackingRepository.save(d365ReturnTracking);
                    }
                }
                else {
                    D365ReturnTracking d365ReturnTracking = new D365ReturnTracking();
                    d365ReturnTracking.setReturnId(returnId);
                    d365ReturnTracking.setIncrementId(returnDetail.getIncrementId());
                    d365ReturnTracking.setCreatedAt(new Date());
                    d365ReturnTracking.setNewStatus(newStatus.toLowerCase());
                    d365ReturnTracking.setOldStatus(oldStatus.toLowerCase());
                    d365ReturnTracking.setD365Flag(0);
                    d365ReturnTracking.setSource(returnDetail.getSource());
                    d365ReturnTracking.setPslipCreated(0);
                    d365ReturnTracking.setRetryCount(0);
                    d365ReturnTracking.setMovJournalFlag(0);
                    d365ReturnTracking.setPSlipRetryCount(0);
                    d365ReturnTracking.setTjournalCreated(0);
                    updateD365SyncData(d365ReturnTracking, d365ReturnTrackingRequestDTO);
                    d365ReturnTrackingRepository.save(d365ReturnTracking);
                }
            }

        }
    }

    @Override
    public D365ReturnTrackingResponse findByReturnId(Integer returnId) {
        log.info("[D365ReturnTrackingServiceImpl][findByReturnId] returnId : {}", returnId);
        if (returnId == null) {
            return null;
        }
        D365ReturnTrackingResponse d365ReturnTrackingResponse = null;
        List<D365ReturnTracking> returnTrackings = d365ReturnTrackingRepository.findByReturnId(returnId);
        if(!CollectionUtils.isEmpty(returnTrackings)){
            List<D365ReturnTrackingDTO> d365ReturnTrackingDTOS = new ArrayList<>();
            d365ReturnTrackingResponse = new D365ReturnTrackingResponse();
            for(D365ReturnTracking d365ReturnTracking : returnTrackings){
                D365ReturnTrackingDTO d365ReturnTrackingDTO = mapper.convertValue(d365ReturnTracking, D365ReturnTrackingDTO.class);
                d365ReturnTrackingDTOS.add(d365ReturnTrackingDTO);
            }
            d365ReturnTrackingResponse.setD365ReturnTracking(d365ReturnTrackingDTOS);
        }
        log.info("[D365ReturnTrackingServiceImpl][findByReturnId] returnId : {}, d365ReturnTrackingResponse : {}", returnId, d365ReturnTrackingResponse);
        return d365ReturnTrackingResponse;
    }

    @Override
    public List<D365ReturnTracking> findDataForReturnRetryForOldData(String fromDate, String toDate, int limit, List<Integer> d365Flag) {
        return d365ReturnTrackingRepository.findDataForReturnRetryForOldData(fromDate, toDate, limit, d365Flag);
    }

    @Override
    public List<D365ReturnTracking> findDataForPSlipRetryForOldData(String fromDate, String toDate, int recordsPerExecution, List<Integer> d365Flags) {
        return d365ReturnTrackingRepository.findDataForPSlipRetryForOldData(fromDate, toDate, recordsPerExecution, d365Flags);
    }

    private void updateD365SyncData(D365ReturnTracking d365ReturnTracking, D365ReturnTrackingRequestDTO d365ReturnTrackingRequestDTO) {
        log.info("[D365ReturnTrackingServiceImpl][updateD365SyncData] d365ReturnTrackingRequestDTO {}", d365ReturnTrackingRequestDTO);

        if (d365ReturnTrackingRequestDTO.getD365Flag() != null) {
            d365ReturnTracking.setD365Flag(d365ReturnTrackingRequestDTO.getD365Flag());
            d365ReturnTracking.setReturnSyncMessage(d365ReturnTrackingRequestDTO.getReturnSyncMessage());
            d365ReturnTracking.setUpdatedAt(d365ReturnTrackingRequestDTO.getUpdatedAt());
            d365ReturnTracking.setReturnRetryUpdatedAt(d365ReturnTrackingRequestDTO.getReturnRetryUpdatedAt());
        }

        if(d365ReturnTrackingRequestDTO.getPslipCreated() != null){
            d365ReturnTracking.setPslipCreated(d365ReturnTrackingRequestDTO.getPslipCreated());
            d365ReturnTracking.setUpdatedAt(d365ReturnTrackingRequestDTO.getUpdatedAt());
            d365ReturnTracking.setPslipRetryUpdatedAt(d365ReturnTrackingRequestDTO.getPslipRetryUpdatedAt());
            d365ReturnTracking.setPslipSyncMessage(d365ReturnTrackingRequestDTO.getPslipSyncMessage());
        }
    }
}