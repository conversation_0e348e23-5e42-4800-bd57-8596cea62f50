package com.lenskart.returnservice.service.impl;

import com.lenskart.core.model.UwOrder;
import com.lenskart.returncommon.exception.UniCommerceException;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.return_refund_rules.model.ReturnItemRequest;
import com.lenskart.returncommon.exception.ReturnRequestFailException;
import com.lenskart.returncommon.model.dto.NexsReturnDTO;
import com.lenskart.returncommon.model.dto.NexsReturnOrderDTO;
import com.lenskart.returncommon.model.request.NexsWmsApiRequest;
import com.lenskart.returncommon.model.request.NexsWmsOrderItems;
import com.lenskart.returncommon.model.response.NexsReturnResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.service.IReturnNexsService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReturnNexsServiceImpl implements IReturnNexsService {
    @Value("${wms.base.url}")
    private String wmsBaseUrl;
    RestTemplate restTemplate;

    @PostConstruct
    public void init(){
        restTemplate = new RestTemplate();
    }

    @Override
    public NexsReturnResponse createReversePickupNexs(List<ReturnItemRequestDTO> returnItemRequestList, String saleOrderCode, Integer incrementId, List<UwOrderDTO> uwOrderDTOS) throws ReturnRequestFailException {
        NexsReturnResponse response = null;
        if (!CollectionUtils.isEmpty(returnItemRequestList) && saleOrderCode != null && incrementId != null) {

            try {
                NexsReturnDTO nexsReturnDTO = new NexsReturnDTO();
                nexsReturnDTO.setIncrementId(incrementId);
                nexsReturnDTO.setOperation("RETURNED");
                Map<Integer, String> itemsRequestLists = new HashMap<Integer, String>();
                for (ReturnItemRequestDTO returnOrderLists : returnItemRequestList) {
                    itemsRequestLists.put(returnOrderLists.getItemId(), returnOrderLists.getQcStatus());
                }
                Map<String, List<UwOrderDTO>> unicomOrderCodeToUwOrderDtoMap = new HashMap<>();
                uwOrderDTOS.forEach(uw -> {
                    unicomOrderCodeToUwOrderDtoMap
                            .computeIfAbsent(uw.getUnicomOrderCode(), uwList -> new ArrayList<>())
                            .add(uw);
                });
                log.info("[createReversePickupNexs] for list of itemsRequestLists : {}", itemsRequestLists);
                List<UwOrderDTO> uwOrderDTOList = unicomOrderCodeToUwOrderDtoMap.get(saleOrderCode);
                List<NexsReturnOrderDTO> nexsReturnOrderDTOS = (!CollectionUtils.isEmpty(uwOrderDTOList)
                        ? getReturnOrderListIfUwOrderDTOListIsNotEmpty(uwOrderDTOList, itemsRequestLists)
                        : getReturnOrderListIfUwOrderDTOListIsEmpty(returnItemRequestList, saleOrderCode)
                );
                nexsReturnOrderDTOS.forEach(s -> log.info("uwItemId : " + s.getOrderItemId() + " orderCode : " + s.getOrderCode()));
                nexsReturnDTO.setOrderItems(nexsReturnOrderDTOS);
                RequestEntity<NexsReturnDTO> requestEntity = new RequestEntity<>(nexsReturnDTO, HttpMethod.PUT,
                        URI.create(wmsBaseUrl + "/order/returnOrCancel"));
                log.info("[createReversePickupNexs][requestEntity] :  " + requestEntity);
                ResponseEntity<NexsReturnResponse> responseEntity = restTemplate.exchange(requestEntity, NexsReturnResponse.class);
                response = responseEntity.getBody();
                log.info("getting response form  wms API :: " + response);
            } catch (Exception ex) {
                log.error(ex.toString(), ex);
                throw new ReturnRequestFailException("Create Return failed exception while creating return for nexs order :", ex.getMessage());
            }
        }
        return response;
    }

    @Override
    public NexsReturnResponse createReturnInNexsV2(List<ReturnItemRequestDTO> returnItemRequestList, String saleOrderCode, Integer incrementId, String facility, List<UwOrderDTO> uwOrderDTOS) throws UniCommerceException {
        NexsReturnResponse response;
        if(returnItemRequestList != null && saleOrderCode != null && incrementId != null) {

            try {
                NexsWmsApiRequest nexsWmsApiRequest = new NexsWmsApiRequest();
                nexsWmsApiRequest.setIncrementId(incrementId);
                nexsWmsApiRequest.setOperation("RETURNED");
                nexsWmsApiRequest.setReceivingFacility(facility);
                nexsWmsApiRequest.setClient("warehouse");

                Map <Integer, String> itemsRequestLists = new HashMap <Integer, String>();
                for (ReturnItemRequestDTO returnOrderLists : returnItemRequestList) {
                    itemsRequestLists.put(returnOrderLists.getItemId(),returnOrderLists.getQcStatus());
                }
                log.info("[createReversePickupNexs] for list of itemsRequestLists : {}", itemsRequestLists);
                List<UwOrderDTO> uwOrderList = uwOrderDTOS.stream().filter(uwOrder -> uwOrder.getUnicomOrderCode().equalsIgnoreCase(saleOrderCode)).collect(Collectors.toList());
                List<NexsWmsOrderItems> returnOrderList = new ArrayList<>();
                if(!CollectionUtils.isEmpty(uwOrderList)) {
                    nexsWmsApiRequest.setDispatchFacility(uwOrderList.get(0).getFacilityCode());
                    List<Integer> frOrderlist = new ArrayList<>();
                    for (UwOrderDTO uwOrderData : uwOrderList) {
                        if(itemsRequestLists.containsKey(uwOrderData.getUwItemId())){
                            if(uwOrderData.getFitting().equalsIgnoreCase("reqd")){
                                frOrderlist.add(uwOrderData.getUwItemId());
                            }
                        }
                    }
                    log.info("[createReversePickupNexs] for list of frOrderlist : {}", frOrderlist);
                    for (UwOrderDTO uwOrderData : uwOrderList) {
                        if(itemsRequestLists.containsKey(uwOrderData.getUwItemId())){
                            NexsWmsOrderItems nexsWmsOrderItems = new NexsWmsOrderItems();
                            nexsWmsOrderItems.setOrderItemId(uwOrderData.getUwItemId());
                            nexsWmsOrderItems.setOrderCode(uwOrderData.getUnicomOrderCode());
                            nexsWmsOrderItems.setBarcode(uwOrderData.getBarcode().toUpperCase());
                            nexsWmsOrderItems.setProductId(String.valueOf(uwOrderData.getProductId()));
                            if(itemsRequestLists.get(uwOrderData.getUwItemId()).equalsIgnoreCase("Pass")){
                                if (!CollectionUtils.isEmpty(frOrderlist)){
                                    if(frOrderlist.contains(uwOrderData.getUwItemId()) && uwOrderData.getFitting().equalsIgnoreCase("reqd")){
                                        nexsWmsOrderItems.setCondition("GOOD");
                                    }else if(frOrderlist.contains(uwOrderData.getParentUw()) && uwOrderData.getFitting().equalsIgnoreCase("not_reqd")){
                                        nexsWmsOrderItems.setCondition("BAD");
                                    }else{
                                        nexsWmsOrderItems.setCondition("GOOD");
                                    }
                                }else{
                                    nexsWmsOrderItems.setCondition("GOOD");
                                }
                            }else{
                                nexsWmsOrderItems.setCondition("BAD");
                            }
                            returnOrderList.add(nexsWmsOrderItems);
                        }
                    }
                    log.info("[createReversePickupNexs] for list from uworders : {}", returnOrderList);
                }else{
                    NexsWmsOrderItems nexsWmsOrderItems = new NexsWmsOrderItems();
                    for (ReturnItemRequestDTO returnOrderLists : returnItemRequestList) {
                        nexsWmsApiRequest.setDispatchFacility(returnOrderLists.getDispatchFacility());
                        nexsWmsOrderItems.setOrderItemId(returnOrderLists.getItemId());
                        nexsWmsOrderItems.setOrderCode(saleOrderCode);
                        nexsWmsOrderItems.setBarcode(returnOrderLists.getBarcode().toUpperCase());
                        nexsWmsOrderItems.setProductId(returnOrderLists.getProductId());
                        if(returnOrderLists.getQcStatus().equalsIgnoreCase("Pass")){
                            nexsWmsOrderItems.setCondition("GOOD");
                        }else{
                            nexsWmsOrderItems.setCondition("BAD");
                        }
                        returnOrderList.add(nexsWmsOrderItems);
                    }
                    log.info("[createReversePickupNexs] for list from itemlist request : {}", returnOrderList);
                }
                returnOrderList.stream().forEach(s -> log.info("uwItemId : " + s.getOrderItemId()+" orderCode : " + s.getOrderCode()));
                nexsWmsApiRequest.setOrderItems(returnOrderList);
                RequestEntity<NexsWmsApiRequest> requestEntity = new RequestEntity<>(nexsWmsApiRequest, HttpMethod.PUT,
                        URI.create(wmsBaseUrl + "/order/return"));
                log.info("[createReturnInNexsV2] requestEntity: {}", requestEntity);
                ResponseEntity<NexsReturnResponse> responseEntity = new RestTemplate().exchange(requestEntity, NexsReturnResponse.class);
                response = responseEntity.getBody();
                log.info("[createReturnInNexsV2] getting response form wms API: {}", response);
            } catch (Exception ex){
                log.error("[createReturnInNexsV2] exception: ", ex);
                throw new UniCommerceException(ex);
            }
        }else {
            throw new UniCommerceException("Missing Parameter");
        }
        return response;
    }

    private List<NexsReturnOrderDTO> getReturnOrderListIfUwOrderDTOListIsEmpty(List<ReturnItemRequestDTO> returnItemRequestList, String saleOrderCode) {
        List<NexsReturnOrderDTO> nexsReturnOrderDTOS = new ArrayList<>();
        NexsReturnOrderDTO nexsReturnOrderDTO = new NexsReturnOrderDTO();
        returnItemRequestList.forEach(r -> {
            nexsReturnOrderDTO.setOrderItemId(r.getItemId());
            nexsReturnOrderDTO.setOrderCode(saleOrderCode);
            nexsReturnOrderDTO.setCondition(Constant.QC_STATUS.QC_PASS.equalsIgnoreCase(r.getQcStatus()) ? Constant.QC_STATUS.qcStatusGood : Constant.QC_STATUS.qcStatusBad);
            nexsReturnOrderDTOS.add(nexsReturnOrderDTO);
        });
        return nexsReturnOrderDTOS;
    }

    private List<NexsReturnOrderDTO> getReturnOrderListIfUwOrderDTOListIsNotEmpty(List<UwOrderDTO> uwOrderDTOList, Map<Integer, String> itemsRequestLists) {
        List<NexsReturnOrderDTO> nexsReturnOrderDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(uwOrderDTOList)) {
            List<Integer> frOrderlist = uwOrderDTOList.stream()
                    .filter(uwOrderData -> itemsRequestLists.containsKey(uwOrderData.getUwItemId()))
                    .filter(uwOrderData -> "reqd".equalsIgnoreCase(uwOrderData.getFitting()))
                    .map(UwOrderDTO::getUwItemId)
                    .collect(Collectors.toList());
            log.info("[createReversePickupNexs] for list of frOrderlist : {}", frOrderlist);
            uwOrderDTOList.forEach(uw -> {

            });
            nexsReturnOrderDTOS.addAll(
                    uwOrderDTOList.stream()
                            .filter(uwOrderData -> itemsRequestLists.containsKey(uwOrderData.getUwItemId()))
                            .map(uwOrderData -> {
                                NexsReturnOrderDTO nexsReturnOrderDTO = new NexsReturnOrderDTO();
                                nexsReturnOrderDTO.setOrderItemId(uwOrderData.getUwItemId());
                                nexsReturnOrderDTO.setOrderCode(uwOrderData.getUnicomOrderCode());
                                String condition = "BAD";
                                if (itemsRequestLists.get(uwOrderData.getUwItemId()).equalsIgnoreCase("Pass")) {
                                    condition = (!frOrderlist.isEmpty() && frOrderlist.contains(uwOrderData.getUwItemId()) && uwOrderData.getFitting().equalsIgnoreCase("reqd"))
                                            ? "GOOD"
                                            : (!frOrderlist.isEmpty() && frOrderlist.contains(uwOrderData.getParentUw()) && uwOrderData.getFitting().equalsIgnoreCase("not_reqd"))
                                            ? "BAD"
                                            : "GOOD";
                                }
                                nexsReturnOrderDTO.setCondition(condition);

                                return nexsReturnOrderDTO;
                            })
                            .toList()
            );
            log.info("[createReversePickupNexs] for list from uworders : {}", nexsReturnOrderDTOS);
        }
        return nexsReturnOrderDTOS;
    }
}
