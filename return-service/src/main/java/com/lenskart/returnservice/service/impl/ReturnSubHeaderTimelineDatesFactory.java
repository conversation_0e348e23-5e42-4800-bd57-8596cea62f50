package com.lenskart.returnservice.service.impl;


import com.lenskart.returncommon.model.enums.ReturnSubHeaderTimelineDatesEnum;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class ReturnSubHeaderTimelineDatesFactory {

    @Autowired
    @Qualifier("returnHeadingPlaceHolderValueFetchCourierDetails")
    IReturnHeadingPlaceHolderValueFetchStrategy returnHeadingPlaceHolderValueFetchCourierDetails;

    @Autowired
    @Qualifier("returnHeadingPlaceHolderValueFetchExchangeDispatchDateStrategy")
    IReturnHeadingPlaceHolderValueFetchStrategy returnHeadingPlaceHolderValueFetchExchangeDispatchDateStrategy;

    @Autowired
    @Qualifier("returnHeadingPlaceHolderValueFetchPickupDateStrategy")
    IReturnHeadingPlaceHolderValueFetchStrategy returnHeadingPlaceHolderValueFetchPickupDateStrategy;

    @Autowired
    @Qualifier("returnHeadingPlaceHolderValueFetchRefundDateStrategy")
    IReturnHeadingPlaceHolderValueFetchStrategy returnHeadingPlaceHolderValueFetchRefundDateStrategy;

    @Autowired
    @Qualifier("returnHeadingPlaceHolderValueFetchRefundModeStrategy")
    IReturnHeadingPlaceHolderValueFetchStrategy returnHeadingPlaceHolderValueFetchRefundModeStrategy;

    @Autowired
    @Qualifier("returnHeadingPlaceHolderValueFetchWorkingDaysStrategy")
    IReturnHeadingPlaceHolderValueFetchStrategy returnHeadingPlaceHolderValueFetchWorkingDaysStrategy;

    public IReturnHeadingPlaceHolderValueFetchStrategy getReturnSubHeaderTimelineDatesStrategy(ReturnSubHeaderTimelineDatesEnum returnSubHeaderTimelineDatesEnum) {
        return switch (returnSubHeaderTimelineDatesEnum) {
            case PIKCUP_DATE -> returnHeadingPlaceHolderValueFetchPickupDateStrategy;
            case REFUND_DATE -> returnHeadingPlaceHolderValueFetchRefundDateStrategy;
            case REFUND_MODE -> returnHeadingPlaceHolderValueFetchRefundModeStrategy;
            case EXCHANGE_DISPATCH_DATE -> returnHeadingPlaceHolderValueFetchExchangeDispatchDateStrategy;
            case WORKING_DAYS -> returnHeadingPlaceHolderValueFetchWorkingDaysStrategy;
            case COURIER_NAME -> returnHeadingPlaceHolderValueFetchCourierDetails;
            case TRACKING_ID -> returnHeadingPlaceHolderValueFetchCourierDetails;
            case TRACKING_URL -> returnHeadingPlaceHolderValueFetchCourierDetails;
        };
    }
}
