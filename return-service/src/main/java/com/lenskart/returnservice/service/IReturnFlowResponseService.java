package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.response.ReturnFlowResponse;
import com.lenskart.returncommon.model.response.ThresholdCheckResponse;

import java.util.List;

public interface IReturnFlowResponseService {
    ReturnFlowResponse getReturnFlowResponse(Integer incrementId, boolean isEligibleRequired);

    Boolean getReturnFlowResponseResult(Integer incrementId, boolean isEligibleRequired);
    ThresholdCheckResponse isFraudThresholdReached(Integer incrementId, List<Integer> uwItemIds, String triggerPoint);
}
