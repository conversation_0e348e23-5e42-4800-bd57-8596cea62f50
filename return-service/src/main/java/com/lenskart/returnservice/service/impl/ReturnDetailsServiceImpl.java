package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.returncommon.model.AwbReverseDto;
import com.lenskart.returncommon.model.dto.ReturnOrderItemDTO;
import com.lenskart.returncommon.model.request.AwbNumberValidationRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.request.ReturnDetailInfoUtilRequestDto;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.*;
import com.lenskart.orderops.model.Canceledorders;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.request.GetRefundAmountRequest;
import com.lenskart.refund.client.model.response.CheckRefundInitiatedResponse;
import com.lenskart.refund.client.model.response.GetMethodWiseRefundDetailsResponse;
import com.lenskart.refund.client.model.response.GetRefundAmountResponse;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.response.ReturnCancellabilityResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.cache.ReturnStatusTimelineMappingCache;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import com.lenskart.returnservice.utils.ReturnOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_METHOD.SHIP_TO_LENSKRT;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.CANCELLED;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.RETURN_RESHIP;

@Slf4j
@Service
public class ReturnDetailsServiceImpl implements IReturnDetailsService {
    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Autowired
    IReturnOrderActionService returnOrderActionService;

    @Autowired
    OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    OrderAdaptorFeignClient orderAdaptorFeignClient;

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired
    private IReturnDetailsEnrichService returnDetailsEnrichService;

    @Autowired
    private ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private ReturnStatusTimelineMappingCache returnStatusTimelineMappingCache;

    @Autowired
    private ReverseTrackingEventRepository reverseTrackingEventRepository;

    @Autowired
    private ReturnReasonRepository returnReasonRepository;

    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    ISystemPreferenceService systemPreferenceService;

    @Autowired
    private PrimaryReturnReasonsRepository primaryReturnReasonsRepository;

    @Autowired
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;

    @Autowired
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @Autowired
    private ReturnOrderMapper returnOrderMapper;

    @Value("${dealskart.manesar.facility}")
    private String facilityCode;

    private static final List<String> RETURN_PICKED_OR_RECEIVED_STATUSES = new ArrayList<String>(
            Arrays.asList(Constant.RETURN_STATUS.AWB_ASSIGNED, Constant.RETURN_ORDER_STATUS.RETURN_RECEIVED,
                    Constant.RETURN_STATUS.RETURN_RECEIVED_ACTION_PENDING));

    private static final List<String> CANCELLED_LISTS = new ArrayList<String>(
            Arrays.asList("canceled", "canceled_payment_check", "predelivery_cancellation", "Cancelled", "order_not_confirmed", "predelivery_refund"));

    private static final String trackingUrl = "https://lenskart.clickpost.ai/reverse?waybill=";

    private final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnReasonService returnReasonService;

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    @Autowired
    private ReturnOrderItemRepository returnOrderItemRepository;

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    @Autowired
    private ReturnUtil returnUtil;

    @Override
    public ReturnDetailsResponse getReturnDetailsByMagentoId(Long magentoItemId, ReturnDetailInfoUtilRequestDto returnDetailInfoUtilRequestDto) {
        log.info("[getReturnDetailsByMagentoId] magentoItemId {}", magentoItemId);
        UwOrderDTO uwOrderDTO = null;
        OrderExchangeCancellationDetails orderExchangeCancellationDetails = null;
        OrderInfoResponseDTO orderInfoResponseDTO = null;
        try {
            if (returnDetailInfoUtilRequestDto != null) {
                uwOrderDTO = getUwOrderFromRequestDto(returnDetailInfoUtilRequestDto);
                orderInfoResponseDTO = returnDetailInfoUtilRequestDto.getOrderInfoResponseDTO();
                orderExchangeCancellationDetails = returnDetailInfoUtilRequestDto.getOrderExchangeCancellationDetails();
            }
            if(Objects.isNull(uwOrderDTO)) {
                List<UwOrderDTO> uwOrderDTOList = orderAdaptorFeignClient.getUwOrdersDTO("MAGENTO_ITEM_ID", String.valueOf(magentoItemId)).getBody();
                log.info("[getReturnDetailsByMagentoId] uwOrderDTOList: {} for magentoItemId: {}", uwOrderDTOList, magentoItemId);
                if (CollectionUtils.isNotEmpty(uwOrderDTOList)) {
                    uwOrderDTO = uwOrderDTOList.stream().filter(uw -> uw.getParentUw() == 0).findFirst().orElse(null);
                }
            }

            ReturnDetailItem returnOrderItem = returnOrderActionService.findTop1ByUwItemIdOrderByReturnCreateDatetimeDescNonInventory(uwOrderDTO.getUwItemId());

            Optional<ReturnDetail> returnOrder = null;
            if(returnOrderItem != null){
                returnOrder = returnOrderActionService.findReturnOrderByIdNonInventory(returnOrderItem.getReturnId());
            }

            if(Objects.isNull(orderInfoResponseDTO)) {
                boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(uwOrderDTO.getIncrementId(), "order-info.rollout.percentage");
                ResponseEntity<OrderInfoResponseDTO> responseEntity = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(uwOrderDTO.getIncrementId(), List.of(ORDERS_HEADER_DTO, ORDER_ADDRESS_UPDATE_DTO, ITEM_WISE_FAST_REFUND_DTO))
                        : orderOpsFeignClient.getOrderDetails(uwOrderDTO.getIncrementId());
                orderInfoResponseDTO = responseEntity.getBody();
            }
            if(Objects.isNull(orderExchangeCancellationDetails) && returnOrderItem != null) {
                log.info("[getReturnDetailsByMagentoId] magentoItemId : {}, returnId : {}, fetching details from order-ops", magentoItemId, returnOrderItem.getReturnId());
                orderExchangeCancellationDetails = orderOpsFeignClient.getExchangeAndCancellationDetails(uwOrderDTO.getUwItemId(), returnOrderItem.getReturnId()).getBody();
            }

            if (returnOrder != null && returnOrder.isPresent()) {
                return buildReturnDetailResponseByReturnOrder(magentoItemId, uwOrderDTO, returnOrderItem, returnOrder.get(), orderInfoResponseDTO, orderExchangeCancellationDetails);
            } else {
                return buildReturnDetailResponseByCancellation(magentoItemId, uwOrderDTO, orderInfoResponseDTO);
            }

        } catch (Exception ex) {
            log.error("exception occured in getReturnDetailsByMagentoId ", ex);
            return null;
        }
    }

    private ReturnDetailsResponse buildReturnDetailResponseByCancellation(
            Long magentoItemId,
            UwOrderDTO uwOrderDTO,
            OrderInfoResponseDTO orderInfoResponseDTO) throws Exception {

        try {
            log.info("Adding cancellation and refund details for UW order id {} and shipment state is {}",
                    uwOrderDTO.getUwItemId(), uwOrderDTO.getShipmentState());

            if (isOrderCanceled(uwOrderDTO.getShipmentState())) {
                List<Canceledorders> canceledOrders = orderOpsFeignClient.getCanceledOrders(uwOrderDTO.getIncrementId());

                if (CollectionUtils.isNotEmpty(canceledOrders)) {
                    ReturnDetailsResponse response = createReturnDetailsResponse(magentoItemId, uwOrderDTO, canceledOrders);
                    updateResponseDetails(orderInfoResponseDTO, response);
                    return response;
                }
            }
        } catch (Exception e) {
            log.error("Exception while getting cancellation and refund details for cancelled order", e);
            throw e;
        }

        return null;
    }

    private boolean isOrderCanceled(String shipmentState) {
        return Constant.ORDER_STATE_OR_STATUS.CANCELED.equalsIgnoreCase(shipmentState)
                || Constant.ORDER_STATE_OR_STATUS.ORDER_CANCELED.equalsIgnoreCase(shipmentState)
                || Constant.ORDER_STATE_OR_STATUS.PREDELIVERY_CANCEL.equalsIgnoreCase(shipmentState)
                || Constant.ORDER_STATE_OR_STATUS.CLOSED.equalsIgnoreCase(shipmentState);
    }

    private ReturnDetailsResponse createReturnDetailsResponse (
            Long magentoItemId,
            UwOrderDTO uwOrderDTO,
            List<Canceledorders> canceledOrders) {

        ReturnDetailsResponse response = new ReturnDetailsResponse();
        response.setUwItemId(uwOrderDTO.getUwItemId());
        response.setMagentoItemId(magentoItemId);
        response.setItemId(uwOrderDTO.getItemId());

        List<CancellationStatusHistory> cancellationStatusHistories = new ArrayList<>();
        boolean isPartialCancel = false;

        for (Canceledorders order : canceledOrders) {
            if (Objects.equals(magentoItemId, order.getMagentoItemId()) &&
                    Constant.CANCEL_ORDER_TYPE.PARTIAL_CANCEL.equalsIgnoreCase(order.getType())) {
                isPartialCancel = true;
            }
            cancellationStatusHistories.add(getCancellationStatusHistory(order));
        }

        response.setCancellationStatusHistories(cancellationStatusHistories);

        String identifierValue = String.valueOf(isPartialCancel ? uwOrderDTO.getUnicomOrderCode() : uwOrderDTO.getIncrementId());
        IdentifierType identifierType = isPartialCancel ? IdentifierType.SHIPMENT_ID : IdentifierType.ORDER_ID;

        updateRefundDetails(response, identifierType, identifierValue, null);
        updateItemWiseFastRefundDetails(uwOrderDTO, response);

        return response;
    }

    private void updateResponseDetails(
            OrderInfoResponseDTO orderInfoResponseDTO,
            ReturnDetailsResponse response) throws Exception {

        returnDetailsEnrichService.updateAmountToRefund(response, orderInfoResponseDTO);
        updateCancellationTimeLines(response);
        returnDetailsEnrichService.updateRefundTimeLines(null, response);
        updateFinalCompletionDateIfRefundTimeLineExist(response);
    }

    private void updateCancellationTimeLines(ReturnDetailsResponse response){
        if(CollectionUtils.isNotEmpty(response.getCancellationStatusHistories())){
            List<CancellationTimeLine> cancellationTimeLines = new ArrayList<>();
            for(CancellationStatusHistory cancellationStatusHistory : response.getCancellationStatusHistories()){
                CancellationTimeLine cancellationTimeLine = new CancellationTimeLine();
                cancellationTimeLine.setCancellationStatus(cancellationStatusHistory.getCancellationStatus());
                cancellationTimeLine.setCancellationSubStatus(cancellationStatusHistory.getCancellationStatus());
                cancellationTimeLine.setCancellationDispatchDate(cancellationStatusHistory.getDate().getTime());
                cancellationTimeLines.add(cancellationTimeLine);
            }
            response.setCancellationTimeLines(cancellationTimeLines);
        }
    }

    private CancellationStatusHistory getCancellationStatusHistory(Canceledorders canceledorders) {
        CancellationStatusHistory cancellationStatusHistory = new CancellationStatusHistory();
        cancellationStatusHistory.setCancelId(canceledorders.getId());
        cancellationStatusHistory.setCancellationStatus(Constant.ORDER_STATE_OR_STATUS.CANCELED);
        cancellationStatusHistory.setCancellationReason(canceledorders.getCancelReason());
        cancellationStatusHistory.setCancellationType(canceledorders.getType());
        cancellationStatusHistory.setCancelledBy(canceledorders.getUserId());
        cancellationStatusHistory.setDate(canceledorders.getCreatedAt());
        return cancellationStatusHistory;
    }

    private UwOrderDTO getUwOrderFromRequestDto(ReturnDetailInfoUtilRequestDto returnDetailInfoUtilRequestDto) {
        return Optional.ofNullable(returnDetailInfoUtilRequestDto.getOrderInfoResponseDTO())
                .map(orderInfo -> returnDetailInfoUtilRequestDto.getPurchaseOrderDetailsDTO()
                        .getUwOrders().stream()
                        .filter(uw -> uw.getParentUw() == 0)
                        .findFirst()
                        .orElse(null))
                .orElse(null);
    }

    private ReturnDetailsResponse buildReturnDetailResponseByReturnOrder(Long magentoItemId, UwOrderDTO uwOrder, ReturnDetailItem returnOrderItem, ReturnDetail returnOrder, OrderInfoResponseDTO orderInfoResponseDTO, OrderExchangeCancellationDetails orderExchangeCancellationDetails) {
        ReturnDetailsResponse response = new ReturnDetailsResponse();
        response.setMagentoItemId(magentoItemId);
        response.setReturnId(returnOrderItem.getReturnId());
        response.setGroupId(returnOrder.getGroupId());
//        response.setReturnLabelGenerated(returnOrder.getReturnLabelGenerated());
        response.setItemId(uwOrder.getItemId());
        response.setUwItemId(uwOrder.getUwItemId());
        response.setReturnStatus(returnOrderActionService.getReturnOrderStatus(returnOrder));
        response.setInsuranceApplied(Optional.ofNullable(returnOrder.getIsInsurance()).orElse(false));
        response.setReturnType(resolveReturnType(returnOrder.getReturnType()));
        response.setFacilityCode(returnOrder.getFacilityCode());
        response.setReturnSource(returnOrder.getSource());
        response.setReturnCancellable(getReturnCancellabilityForItem(uwOrder, orderExchangeCancellationDetails).getIsCancellable());
        response.setReturnMethod(resolveReturnMethod(returnOrder.getReturnMethod(), response));
        response.setInitiationDate(returnOrder.getReturnCreateDatetime());

        ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTopByReturnIdOrderByIdDesc(returnOrder.getId());
        if(returnCourierDetail != null){
            response.setCourierName("pos".equalsIgnoreCase(returnOrder.getSource()) ? null : returnCourierDetail.getReverseCourier());
            response.setTrackingNumber(returnCourierDetail.getReverseAwbNumber());
            response.setPickupDate(returnCourierDetail.getPickupEstimate());
            response.setPickupStatus(resolvePickupStatus(returnOrderActionService.getReturnOrderStatusById(returnOrder.getId())));
        }
        response.setReceivedAtWarehouse(returnOrder.getReceivingFlag());
        response.setOrdersHeaderDTO(orderInfoResponseDTO.getOrdersHeader());
        response.setOrderAddressUpdates(orderInfoResponseDTO.getOrderAddressUpdates());
        response.setRefundAmount(0d);

        Long b2bReturnOrderGroupId = getB2bReturnOrderGroupIdByUwOrder(uwOrder);

        updateRefundDetails(response, IdentifierType.RETURN_ID, String.valueOf(returnOrderItem.getReturnId()), returnOrder);
        updateExchangeDetails(response, orderExchangeCancellationDetails);
        updateItemWiseFastRefundDetails(uwOrder, response);
        updatePickUpAddressDetails(returnOrder, response, b2bReturnOrderGroupId);
        createReturnEvent(uwOrder, response);
        enrichReturnDetails(response, returnOrder, orderInfoResponseDTO); //left
        updateFinalCompletionDateIfRefundTimeLineExist(response);

        log.info("[getReturnDetailsByMagentoId] response {} , {},  {},  {}", response, response.getReturnTimeLines(), response.getRefundTimeLines(), response.getExchangeTimelines());
        return response;
    }

    private void updateFinalCompletionDateIfRefundTimeLineExist(ReturnDetailsResponse response){
        if(CollectionUtils.isNotEmpty(response.getRefundTimeLines())){
            OptionalLong totalRefundCompleteDate =  response.getRefundTimeLines().stream().filter(r -> null!=r.getRefundEstimatedDate()).mapToLong(RefundTimeLine::getRefundEstimatedDate).max();
            if(totalRefundCompleteDate.isPresent()){
                response.setTotalRefundCompleteDate(totalRefundCompleteDate.getAsLong());
            }
        }
    }

    private Long getB2bReturnOrderGroupIdByUwOrder(UwOrderDTO uwOrder) {
        Long b2bReturnOrderGroupId = null;
        if (Constant.PRODUCT_DELIVERY_TYPE.B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()) &&
                Objects.nonNull(uwOrder.getB2bRefrenceItemId())
                && uwOrder.getB2bRefrenceItemId() != 0) {
            Integer b2bReturnId = returnOrderActionService.getReturnId(uwOrder.getB2bRefrenceItemId());
            b2bReturnOrderGroupId = returnOrderActionService.findGroupIdByReturnId(b2bReturnId);
        }
        return b2bReturnOrderGroupId;
    }

    private ReturnCancellabilityResponse getReturnCancellabilityForItem(UwOrderDTO uwOrder, OrderExchangeCancellationDetails orderExchangeCancellationDetails) {
        ReturnCancellabilityResponse response = new ReturnCancellabilityResponse();
        response.setIsCancellable(true);

        String returnCancelledStatus = systemPreferenceRepository.findAllByKey("return_cancelled_list").getValue();
        List<String> returnCancelledList = Arrays.asList(returnCancelledStatus.split(","));
        try {
            ReturnDetail returnOrder = fetchReturnOrder(uwOrder.getUwItemId());
            if (returnOrder == null) {
                return buildErrorResponse(response, "NO_RETURN_FOUND", "No return exists against this item");
            }
            response.setReturnId(returnOrder.getId());

            if (isOrderCancellable(returnOrder, uwOrder, returnCancelledList, response, orderExchangeCancellationDetails)) {
                response.setIsCancellable(true);
                response.setErrorType("EXCHANGE_CREATED");
                response.setErrorMessage("An exchange order has been created against this item's return");
                return response;
            }
            if (refundUtilsService.isAlreadyRefunded(uwOrder.getIncrementId(), uwOrder.getUwItemId())) {
                return buildErrorResponse(response, "RETURN_ALREADY_REFUNDED", "Partial or complete refund has already been given for this item's return");
            }
        } catch (Exception e) {
            log.error("[getReturnCancellabilityForItem] Caught exception: ", e);
            return buildErrorResponse(response, "ERROR_FETCHING_CANCELLABILITY", "Error fetching cancellability");
        }
        log.info("[getReturnCancellabilityForItem] Returning with return cancellability response: {}", response);
        return response;
    }

    @Override
    public ReturnCancellabilityResponse getReturnCancellabilityForItem(Integer uwItemId) {
        log.info("[ReturnServiceImpl][getReturnCancellabilityForItem]: Fetching return cancellability for item id {}", uwItemId);

        ReturnCancellabilityResponse response = new ReturnCancellabilityResponse();
        response.setIsCancellable(true);

        String returnCancelledStatus = systemPreferenceRepository.findAllByKey("return_cancelled_list").getValue();
        List<String> returnCancelledList = Arrays.asList(returnCancelledStatus.split(","));

        try {
            UwOrderDTO uwOrder = fetchUwOrder(uwItemId);

            if (uwOrder == null) {
                return buildErrorResponse(response, "NO_ITEM_FOUND", "No item exists by the given identifier");
            }

            ReturnDetail returnOrder = fetchReturnOrder(uwOrder.getUwItemId());

            if (returnOrder == null) {
                return buildErrorResponse(response, "NO_RETURN_FOUND", "No return exists against this item");
            }
            response.setReturnId(returnOrder.getId());

            if (isOrderCancellable(returnOrder, uwOrder, returnCancelledList, response, null)) {
                response.setIsCancellable(true);
                response.setErrorType("EXCHANGE_CREATED");
                response.setErrorMessage("An exchange order has been created against this item's return");
                return response;
            }

            if (refundUtilsService.isAlreadyRefunded(uwOrder.getIncrementId(), uwOrder.getUwItemId())) {
                return buildErrorResponse(response, "RETURN_ALREADY_REFUNDED", "Partial or complete refund has already been given for this item's return");
            }

        } catch (Exception e) {
            log.error("[ReturnServiceImpl][getReturnCancellabilityForItem]: Error : {} ", e.getMessage(), e);
            return buildErrorResponse(response, "ERROR_FETCHING_CANCELLABILITY", "Error fetching cancellability");
        }

        return response;
    }

    @Override
    public ReturnDetailsForSmsDTO getRefundDetailsForSMS(CommRequestDTO commRequestDTO) {
        ReturnDetailsForSmsDTO returnDetailsForSmsDTO = new ReturnDetailsForSmsDTO();
        Optional<ReturnRequest> returnRequest = returnRequestRepository.findById(commRequestDTO.getReturnRequestId());

        if (returnRequest.isPresent()) {
            try{
                returnDetailsForSmsDTO.setReturnCreationRequest(objectMapper.readValue(returnRequest.get().getReturnCreationRequest(), ReturnCreationRequestDTO.class));
                returnDetailsForSmsDTO.setReturnCreationDate(returnRequest.get().getCreatedAt());
                ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTopByReturnIdOrderByIdDesc(commRequestDTO.getReturnId());
                if(returnCourierDetail != null){
                    returnDetailsForSmsDTO.setReturnPickupCourierName(returnCourierDetail.getReverseCourier());
                    returnDetailsForSmsDTO.setReturnPickupAwb(returnCourierDetail.getReverseAwbNumber());
                    if("return_pickup_done_awb_assigned_master".equalsIgnoreCase(commRequestDTO.getTemplateName())){
                        returnDetailsForSmsDTO.setReturnPickupDate(com.lenskart.returncommon.utils.DateUtil.getCurrentDate());
                    }else {
                        returnDetailsForSmsDTO.setReturnPickupDate(getEstimatedReversePickUpDate(commRequestDTO.getReturnId()));
                    }
                }
            }catch (Exception exception){
                log.error("[getRefundDetailsForSMS] json parse exception : {}", exception.getMessage());
            }
        }

        return returnDetailsForSmsDTO;
    }

    public String getEstimatedReversePickUpDate(Integer returnId) {
        String formattedDate = null;
        log.info("[getEstimatedReversePickUpDate] groupId : {}", returnId);
        Optional<ReturnDetail> returnOrder = returnOrderActionService.findReturnOrderById(returnId);
        if (returnOrder.isPresent()) {
            Date returnCreationTime = returnOrder.get().getReturnCreateDatetime();
            Integer addDate = 3;
            try {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String strDate = dateFormat.format(returnCreationTime);
                log.info("[getEstimatedReversePickUpDate] strDate : {}", strDate);

                String dayName = DateUtil.dayName(strDate, "yyyy-M-d");
                if (dayName.equalsIgnoreCase("Saturday")) {
                    addDate = 5;
                } else if (dayName.equalsIgnoreCase("Sunday")) {
                    addDate = 4;
                }

                //Getting current date
                Calendar cal = Calendar.getInstance();
                cal.setTime(dateFormat.parse(strDate));

                //Number of Days to add
                cal.add(Calendar.DAY_OF_MONTH, addDate);
                //Date after adding the days to the current date
                String newDate = dateFormat.format(cal.getTime());

                LocalDate localDate = LocalDate.parse(newDate);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM yyyy");
                formattedDate = localDate.format(formatter);
                log.info("[getEstimatedReversePickUpDate] formattedDate : {}", formattedDate);
            } catch (Exception e) {
                log.error("[getEstimatedReversePickUpDate] Exception occurred : ", e);
            }
        }
        return formattedDate;
    }

    @Override
    public List<ReturnDTO> getReturnDetails(Map<String, Object> filterParams) throws ParseException {
        String status = getStringParam(filterParams, "returnStatus");
        String fromTime = getStringParam(filterParams, "fromSearch", getOneMonthOldDate());
        String toTime = getStringParam(filterParams, "toSearch", getCurrentDate());
        Integer orderNo = getIntegerParam(filterParams, "orderId");
        String reverseCourier = getStringParam(filterParams, "courier");
        Long groupId = getLongParam(filterParams, "groupId");
        Integer pageSize = getIntegerParam(filterParams, "pageSize", 25);
        Integer pageNo = getIntegerParam(filterParams, "pageNo", 1) - 1; // Adjust for 0-based index
        String facility = getStringParam(filterParams, "facility");
        Integer lastFetchedId = getIntegerParam(filterParams, "lastFetchedId");

        Date startDate = DateUtil.getDate(fromTime);
        Date endDate = DateUtil.getDate(toTime);

        log.info("[getReturnDetails] status: {}, startDate: {}, endDate: {}, orderNo: {}, reverseCourier: {}, pageSize: {}, pageNo: {}, facility: {}",
                status, startDate, endDate, orderNo, reverseCourier, pageSize, pageNo, facility);

        // Query returns dynamically with a unified method
//        Page<ReturnDetail> returnOrdersPaginated = returnOrderActionService.getReturnOrdersCombined(status, orderNo, reverseCourier, facility, startDate, endDate, pageable);

        List<ReturnDetail> returnOrders;
        if (facility != null) {
            returnOrders = returnOrderActionService.getReturnOrdersByFacilityAndDateRange(facility, startDate, endDate, pageSize, pageNo);
        } else if (groupId != null) {
            returnOrders = returnOrderActionService.findByGroupId(groupId);
        } else {
            DateUtil.validateDateRange(startDate, endDate, 32);
            PagedResponse<ReturnDetail> returnOrdersPaginated = returnOrderActionService.getReturnOrdersCombined(status, orderNo, reverseCourier, facility, startDate, endDate, lastFetchedId, pageSize);
            log.info("[getReturnDetails] returnOrdersPaginated size :{}", returnOrdersPaginated.getContent().size());
            returnOrders = returnOrdersPaginated.getContent();
        }

        List<ReturnDTO> returnDTOS = new ArrayList<>();

        if (!CollectionUtils.isEmpty(returnOrders)) {
            Map<Integer, String> returnIdToStatusMap = returnOrderActionService.getReturnOrderStatus(returnOrders);

            returnOrders = returnOrders.stream()
                    .peek(r -> r.setStatus(returnIdToStatusMap.get(r.getId())))
                    .filter(r -> status == null || status.equalsIgnoreCase(r.getStatus()))
                    .toList();

            List<Integer> returnIds = returnOrders.stream().map(ReturnDetail::getId).toList();
            Map<Integer, ReturnOrderDTO> returnOrderMap = getReturnOrderDTO(returnOrders);
            Map<Integer, ReturnOrderItemDTO> returnItemMap = getReturnOrderItemDTO(returnIds);
            Map<Integer, ReverseCourierDetailDTO> courierMap = getReverseCourierDetailDTO(returnIds);
            Map<Integer, ReturnReasonDTO> reasonMap = getReturnReasonDTO(returnIds);

            returnDTOS = returnIds.stream()
                    .map(returnId -> {
                        ReturnDTO dto = new ReturnDTO();
                        dto.setReturnOrderDTO(returnOrderMap.get(returnId));
                        dto.setReturnOrderItemDTO(returnItemMap.get(returnId));
                        dto.setReverseCourierDetailDTO(courierMap.get(returnId));
                        dto.setReturnReasonDTO(reasonMap.get(returnId));
                        return dto;
                    })
                    .toList();
        }
        log.info("[getReturnDetails] - {}", returnDTOS);
        return returnDTOS;
    }


    // Utility Methods for Parameter Extraction
    private String getStringParam(Map<String, Object> params, String key) {
        return (params.containsKey(key) && params.get(key) != null && StringUtils.isNotEmpty(params.get(key).toString()))
                ? params.get(key).toString().trim()
                : null;
    }

    private String getStringParam(Map<String, Object> params, String key, String defaultValue) {
        return (params.containsKey(key) && params.get(key) != null && StringUtils.isNotEmpty(params.get(key).toString()))
                ? params.get(key).toString().trim()
                : defaultValue;
    }

    private Integer getIntegerParam(Map<String, Object> params, String key) {
        return (params.containsKey(key) && params.get(key) != null && StringUtils.isNotEmpty(params.get(key).toString()))
                ? Integer.parseInt(params.get(key).toString().trim())
                : null;
    }

    private Long getLongParam(Map<String, Object> params, String key) {
        return (params.containsKey(key) && params.get(key) != null && StringUtils.isNotEmpty(params.get(key).toString()))
                ? Long.parseLong(params.get(key).toString().trim())
                : null;
    }

    private Integer getIntegerParam(Map<String, Object> params, String key, Integer defaultValue) {
        return (params.containsKey(key) && params.get(key) != null && StringUtils.isNotEmpty(params.get(key).toString()))
                ? Integer.parseInt(params.get(key).toString().trim())
                : defaultValue;
    }

    private ReturnOrderItemDTO getReturnOrderItemDTO(ReturnDetail returnOrder) {
        log.info("[getReturnOrderItemDTO] returnId : {}", returnOrder.getId());
        ReturnDetailItem returnDetailItem = returnOrderActionService.findTopByReturnId(returnOrder.getId());
        ReturnOrderItemDTO returnOrderItemDTO = new ReturnOrderItemDTO();
        if(returnDetailItem != null){
            BeanUtils.copyProperties(returnDetailItem, returnOrderItemDTO);
        }
        return returnOrderItemDTO;
    }

    private Map<Integer, ReturnOrderItemDTO> getReturnOrderItemDTO(List<Integer> returnIds) {
        log.info("[getReturnOrderItemDTO] returnIds : {}", returnIds);
        return returnOrderActionService.findReturnOrderItems(returnIds).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ReturnOrderItemDTO::getReturnId, Function.identity(), (a, b) -> a));
    }

    @Override
    public GetFiltersResponse getFilters() {
        GetFiltersResponse response = new GetFiltersResponse();
        SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey(Constant.SYSTEM_PREFERENCE_KEYS.RETURN_FILTER_CONFIG, Constant.SYSTEM_PREFERENCE_KEYS.RETURN_FILTER_CONFIG);
        if (systemPreference != null) {
            try {
                response.setFilters(objectMapper.readValue(systemPreference.getValue(), FilterOptionsDTO.class));
                List<String> primaryReturnReasons = primaryReturnReasonsRepository.findAllDistinct();
                List<String> secondaryReturnReasons = secondaryReturnReasonRepository.findAllDistinct();
                if (CollectionUtils.isNotEmpty(primaryReturnReasons)) {
                    List<FilterOptionsDTO.FilterOption> primaryReasons = new ArrayList<>();
                    primaryReturnReasons.forEach(primaryReturnReason -> primaryReasons.add(new FilterOptionsDTO.FilterOption(primaryReturnReason, primaryReturnReason)));
                    response.getFilters().setReturnReasonsPrimary(primaryReasons);
                }
                if (CollectionUtils.isNotEmpty(secondaryReturnReasons)) {
                    List<FilterOptionsDTO.FilterOption> secondaryReasons = new ArrayList<>();
                    secondaryReturnReasons.forEach(secondaryReturnReason -> secondaryReasons.add(new FilterOptionsDTO.FilterOption(secondaryReturnReason, secondaryReturnReason)));
                    response.getFilters().setReturnReasonsSecondary(secondaryReasons);
                }
                if(response.getFilters().getAgentEmail() != null) {
                    List<String> agentIds = orderOpsFeignClient.getReturnAgents("Return");
                    List<FilterOptionsDTO.FilterOption> agentEmail = response.getFilters().getAgentEmail();
                    agentIds.forEach(agentId -> agentEmail.add(new FilterOptionsDTO.FilterOption(agentId, agentId)));
                    log.info("[getFilters] agentIds : {}", agentEmail);
                    response.getFilters().setAgentEmail(agentEmail);
                }
            } catch (JsonProcessingException e) {
                log.error("JsonProcessingException occurred while getting filters : {}", e.getMessage(), e);
            } catch (Exception e) {
                log.error("Exception occurred while getting filters : {}", e.getMessage(), e);
            }
        }
        return response;
    }

    @Override
    public AwbReverseDto validateReturnTypeReverse(AwbNumberValidationRequest validationRequest) {
        AwbReverseDto awbReverseDto = new AwbReverseDto();

        log.info("[validateTrackingNumber returns] trackingNumber Validation Request is " + validationRequest);
        try {
            if (Objects.nonNull(validationRequest) && StringUtils.isNotEmpty(validationRequest.getTrackingNumber())) {
                Map<String, Object> results = returnCourierDetailRepository
                        .findReturnTypeByReverseAwbNumber(validationRequest.getTrackingNumber(), validationRequest.getReturnType());

                log.info("[validateTrackingNumber returns] trackingNumber Validation result is " + results);

                if (!results.isEmpty()) {
                    ReturnCourierDto dto = new ReturnCourierDto();

                    dto.setIncrementId(results.get("order_no") != null ? ((Number) results.get("order_no")).intValue() : null);
                    dto.setCourier(results.get("courier_name") != null ? results.get("courier_name").toString() : null);

                    log.info("[validateTrackingNumber returns] trackingNumber incrementId " + dto.getIncrementId() +
                            ", reverseCourier: " + dto.getCourier());

                    awbReverseDto.setIncrementId(dto.getIncrementId());
                    awbReverseDto.setReverseCourier(dto.getCourier());
                } else {
                    awbReverseDto.setReverseCourier(null);
                    awbReverseDto.setIncrementId(null);

                }
                log.info("[validateTrackingNumber returns] trackingNumber Validation awbReverseDto is " + awbReverseDto);

            }
        } catch (Exception e) {
            log.error("[validateTrackingNumber returns] exception while validating tracking number is " + e);
        }
        return awbReverseDto;
    }


    private ReturnReasonDTO getReturnReason(ReturnDetail returnOrder) {
        return returnReasonService.getReturnReason(returnOrder.getId());
    }

    private Map<Integer, ReverseCourierDetailDTO> getReverseCourierDetailDTO(List<Integer> returnIds) {
        return returnOrderActionService.getReverseCourierDetailDTO(returnIds).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ReverseCourierDetailDTO::getReturnId, Function.identity(), (a, b) -> a));
    }

    private Map<Integer, ReturnReasonDTO> getReturnReasonDTO(List<Integer> returnIds) {
        return returnOrderActionService.getReturnReasonDTO(returnIds).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ReturnReasonDTO::getReturnId, Function.identity(), (a, b) -> a));
    }

    private ReverseCourierDetailDTO getReverseCourierDetailDTO(ReturnDetail returnOrder) {
        ReverseCourierDetailDTO reverseCourierDetailDTO = new ReverseCourierDetailDTO();
        if(returnOrder != null){
            ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTop1ByReturnId(returnOrder.getId());
            if(returnCourierDetail != null){
                BeanUtils.copyProperties(returnCourierDetail, reverseCourierDetailDTO);
            }
        }
        return reverseCourierDetailDTO;
    }

    private ReturnOrderDTO getReturnOrderDTO(ReturnDetail returnOrder) {
        ReturnOrderDTO returnOrderDTO = new ReturnOrderDTO();
        log.info("[getReturnOrderDTO] groupId : {}", returnOrder.getGroupId());
        BeanUtils.copyProperties(returnOrder, returnOrderDTO);
        if(returnOrder.getGroupId() != null){
            returnOrderDTO.setGroupId(Math.toIntExact(returnOrder.getGroupId()));
        }
        return returnOrderDTO;
    }

    private Map<Integer, ReturnOrderDTO> getReturnOrderDTO(List<ReturnDetail> returnDetails) {
        return returnOrderMapper.toDtoList(returnDetails).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ReturnOrderDTO::getId, Function.identity(), (a, b) -> a));
    }

    private String getOneMonthOldDate(){
        LocalDate currentDate = LocalDate.now();
        LocalDate oneMonthBefore = currentDate.minusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return oneMonthBefore.format(formatter);
    }

    private String getCurrentDate(){
        LocalDate currentDate = LocalDate.now().plusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return currentDate.format(formatter);
    }


    private static String generateDynamicQueryForCourier(String fromTime, String toTime, Integer orderNo, String reverseCourier) {
        StringBuilder query = new StringBuilder("select ro.* from return_courier_detail rcd inner join return_order ro ON rcd.return_id = ro.id where rcd.reverse_courier = "+reverseCourier);

        if (orderNo != null) {
            query.append(" AND order_no = ").append(orderNo);
        }

        if (fromTime != null) {
            query.append(" AND order_time >= '").append(fromTime).append("'");
        }

        if (toTime != null) {
            query.append(" AND order_time <= '").append(toTime).append("'");
        }

        return query.toString();
    }

    private static String generateDynamicQueryForOrder(String fromTime, String toTime, Integer orderNo, String reverseCourier) {
        StringBuilder query = new StringBuilder("SELECT * FROM return_order WHERE order_no="+orderNo);


        if (fromTime != null) {
            query.append(" AND order_time >= '").append(fromTime).append("'");
        }

        if (toTime != null) {
            query.append(" AND order_time <= '").append(toTime).append("'");
        }

        if (reverseCourier != null && !reverseCourier.isEmpty()) {
            query.append(" AND reverse_courier = '").append(reverseCourier).append("'");
        }

        query.append(" ORDER BY ro.return_create_datetime desc");

        return query.toString();
    }

    private UwOrderDTO fetchUwOrder(Integer uwItemId) {
        ResponseEntity<PurchaseOrderDetailsDTO> response = orderOpsFeignClient.getPurchaseOrderDetails("UW_ITEM_ID", String.valueOf(uwItemId));
        UwOrderDTO uwOrder = null;
        if(response.getStatusCode().is2xxSuccessful() && response.getBody() != null && !CollectionUtils.isEmpty(response.getBody().getUwOrders())){
            uwOrder = response.getBody().getUwOrders().stream().filter(uw -> uw.getParentUw() == 0).findFirst().orElse(null);
//            if (uwOrder == null) {
//                response = orderOpsFeignClient.getPurchaseOrderDetails("MAGENTO_ITEM_ID", String.valueOf(uwItemId));
//                List<OrdersDTO> orders = response.getBody().getOrders();
//                if (orders != null) {
//                    for (OrdersDTO order : orders) {
//                        UwOrderDTO uwOrderElement = response.getBody().getUwOrders().stream().filter(uw -> uw.getItemId() == order.getItemId()).findFirst().orElse(null);
//                        if (isValidOrder(order, uwOrderElement)) {
//                            if (uwOrderElement != null) {
//                                return uwOrderElement;
//                            }
//                        }
//                    }
//                }
//            }
        }
        log.info("[fetchUwOrder] uwOrder : {} for uwItemId: {}", uwOrder, uwItemId);
        return uwOrder;
    }

    private boolean isValidOrder(OrdersDTO order, UwOrderDTO uwOrderElement) {
        return !"11356".equalsIgnoreCase(uwOrderElement.getClassification()) &&
                (!Constant.CHANNEL.B2B.equalsIgnoreCase(order.getChannel()) || order.getFacilityCode().equalsIgnoreCase(facilityCode));
    }

    private ReturnDetail fetchReturnOrder(Integer uwItemId) {
        log.info("[fetchReturnOrder] uwItemId : {}",uwItemId);
        ReturnDetailItem returnOrderItem = returnOrderActionService.findTop1ByUwItemIdOrderByReturnCreateDatetimeDescNonInventory(uwItemId);
        if (returnOrderItem != null) {

            Optional<ReturnDetail> returnOrder = returnOrderActionService.findReturnOrderByIdNonInventory(returnOrderItem.getReturnId());
            if (returnOrder.isPresent()) {
                return returnOrder.get();
            }

        }
        return null;
    }

    private boolean isOrderCancellable(ReturnDetail returnOrder, UwOrderDTO uwOrder, List<String> returnCancelledList, ReturnCancellabilityResponse response, OrderExchangeCancellationDetails orderExchangeCancellationDetails) {
        List<ReturnEvent> returnHistoryList = returnEventRepository.findByReturnIdAndEventOrderByIdDesc(
                returnOrder.getId(), Constant.RETURN_ORDER_STATUS.AWB_ASSIGNED);
        String status = returnOrderActionService.getReturnOrderStatus(returnOrder);
        if (orderExchangeCancellationDetails == null) {
            orderExchangeCancellationDetails = orderOpsFeignClient.getExchangeAndCancellationDetails(uwOrder.getUwItemId(), returnOrder.getId()).getBody();
        }

        if (returnHistoryList != null && !CollectionUtils.isEmpty(returnHistoryList)) {
            if ("cancelled".equalsIgnoreCase(status)) {
                if (orderExchangeCancellationDetails != null && CollectionUtils.isNotEmpty(orderExchangeCancellationDetails.getExchangeOrdersDTOList()) && CollectionUtils.isNotEmpty(orderExchangeCancellationDetails.getCanceledOrdersDTOList())) {
                        return false;
                }
            }
        }

        boolean isSourceVsmWeb = "vsm".equalsIgnoreCase(returnOrder.getSource()) || "web".equalsIgnoreCase(returnOrder.getSource());
        boolean isReturnExchangeCase = false;
        if(isSourceVsmWeb && "RETURN_EXCHANGE".equalsIgnoreCase(status) && CollectionUtils.isEmpty(returnHistoryList)){
            isReturnExchangeCase = true;
        }

        List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnOrder.getId());
        boolean isReturnReceived = false;
        if(!org.springframework.util.CollectionUtils.isEmpty(returnEvents)){
            isReturnReceived = returnEvents
                    .stream()
                    .map(ReturnEvent::getEvent)
                    .anyMatch(event -> "RETURN_RECEIVED_AT_STORE".equalsIgnoreCase(event) || "RETURN_RECEIVED_AT_WAREHOUSE".equalsIgnoreCase(event) || "AWB_ASSIGNED".equalsIgnoreCase(event));
        }

        log.info("[isOrderCancellable] isSourceVsmWeb : {}, isReturnExchangeCase : {}, isReturnReceived: {}", isSourceVsmWeb, isReturnExchangeCase, isReturnReceived);
        if (CANCELLED_LISTS.contains(uwOrder.getShipmentStatus()) ||
                RETURN_PICKED_OR_RECEIVED_STATUSES.contains(status) ||
                RETURN_RESHIP.equalsIgnoreCase(status) ||
                CANCELLED.equalsIgnoreCase(status) ||
                returnCancelledList.contains(status) ||
                isReturnReceived) {
            logCancellabilityIssues(status, uwOrder, response, isReturnExchangeCase, isReturnReceived);
            return false;
        }

        if (orderExchangeCancellationDetails != null && CollectionUtils.isNotEmpty(orderExchangeCancellationDetails.getExchangeOrdersDTOList())) {
            List<UwOrderDTO> uwOrderDTOList = orderAdaptorFeignClient.getUwOrdersDTO(IdentifierType.ORDER_ID.name(), String.valueOf(orderExchangeCancellationDetails.getExchangeOrdersDTOList().get(0).getExchangeIncrementId())).getBody();
            if (CollectionUtils.isNotEmpty(uwOrderDTOList)) {
                UwOrderDTO uwOrderDTO = uwOrderDTOList.stream().filter(uw -> uw.getParentUw() == 0).findFirst().orElse(null);
                return returnHistoryList != null && uwOrderDTO != null && !returnHistoryList.isEmpty() && !CANCELLED_LISTS.contains(uwOrderDTO.getShipmentStatus());
            }
        }

        return false;
    }

    private void logCancellabilityIssues(String status, UwOrderDTO uwOrder, ReturnCancellabilityResponse response, boolean isReturnExchangeCase, boolean isReturnReceived) {
        if (CANCELLED_LISTS.contains(uwOrder.getShipmentStatus())) {
            log.info("[ReturnDetailsServiceImpl][logCancellabilityIssues]: The corresponding order for this item is already in cancelled state");
            response.setIsCancellable(false);
            response.setErrorType("ITEM_ALREADY_CANCELLED");
            response.setErrorMessage("The corresponding order for this item is already in cancelled state");
        } else if (RETURN_PICKED_OR_RECEIVED_STATUSES.contains(status) || RETURN_RESHIP.equalsIgnoreCase(status)) {
            log.info("[ReturnDetailsServiceImpl][logCancellabilityIssues]: This return has been picked by courier or received at lenskart");
            response.setIsCancellable(false);
            response.setErrorType("RETURN_PICKED_OR_RECEIVED_OR_RESHIP");
            response.setErrorMessage("This return has been picked by courier or received at lenskart");
        } else if (CANCELLED.equalsIgnoreCase(status)) {
            log.info("[ReturnServiceImpl][logCancellabilityIssues]: The return against this item is already in cancelled state");
            response.setIsCancellable(false);
            response.setErrorType("RETURN_ALREADY_CANCELLED");
            response.setErrorMessage("The return against this item is already in cancelled state");
        } else {
            if(isReturnExchangeCase){
                log.info("[ReturnDetailsServiceImpl][logCancellabilityIssues]: return_order Item status: {} and isReturnExchangeCase : {} ", status, isReturnExchangeCase);
            }else if(isReturnReceived){
                log.info("[ReturnDetailsServiceImpl][logCancellabilityIssues]: return_order Item status: {} and isReturnReceived : {} ", status, isReturnReceived);
                response.setIsCancellable(false);
                response.setErrorType(status);
                response.setErrorMessage("Cancellability false as item is received or pickedup");
                log.info("[ReturnDetailsServiceImpl][logCancellabilityIssues]: Cancellability false as item is received or pickedup: {}", status);
            }else{
                response.setIsCancellable(false);
                response.setErrorType(status);
                response.setErrorMessage("Cancellability false as item status is "+response);
                log.info("[ReturnDetailsServiceImpl][logCancellabilityIssues]: return_order Item status: {}", status);
            }
        }
    }



    private ReturnCancellabilityResponse buildErrorResponse(ReturnCancellabilityResponse response, String errorType, String errorMessage) {
        response.setIsCancellable(false);
        response.setErrorType(errorType);
        response.setErrorMessage(errorMessage);
        return response;
    }

    private void createReturnEvent(UwOrderDTO uwOrder, ReturnDetailsResponse response) {
        List<ReturnStatusHistory> returnStatusHistoryList = new ArrayList<>();

        //if we need return history list for all returns then use below code
        List<ReturnDetailReason> returnReasonItemsList = returnReasonRepository.findUniqueByUwItemId(uwOrder.getUwItemId());
        if (null != returnReasonItemsList) {
            for (ReturnDetailReason returnItem : returnReasonItemsList) {
                List<ReturnEvent> returnHistoryList = returnEventRepository.findByReturnId(returnItem.getReturnId());
                addUniqueStatusHistory(response,returnStatusHistoryList, returnHistoryList); //left
                getPickupAttemptHistories(response);
            }
        }
    }

    private void getPickupAttemptHistories(ReturnDetailsResponse response) {
        if(ReturnStatus.NEW_REVERSE_PICKUP.getStatus().equalsIgnoreCase(response.getReturnStatus())||
                ReturnStatus.REFERENCE_ID_ISSUED.getStatus().equalsIgnoreCase(response.getReturnStatus())||
                ReturnStatus.AWB_ASSIGNED.getStatus().equalsIgnoreCase(response.getReturnStatus())){
            List<PickupAttemptHistory> pickupAttemptHistoryList = null;
            List<ReverseDetailTrackingEvent> reverseTrackingEventList = reverseTrackingEventRepository.findByReturnId(response.getReturnId());
            if(!CollectionUtils.isEmpty(reverseTrackingEventList)){
                int count = 0;
                for(ReverseDetailTrackingEvent reverseTrackingEvent: reverseTrackingEventList) {
                    if(null == pickupAttemptHistoryList){
                        pickupAttemptHistoryList = new ArrayList<>();
                    }
                    PickupAttemptHistory pickupAttemptHistory = new PickupAttemptHistory();
                    pickupAttemptHistory.setPickupStatus(reverseTrackingEvent.getReverseMappedStatus());
                    pickupAttemptHistory.setReason(reverseTrackingEvent.getNonPickupReason());
                    pickupAttemptHistory.setDate(reverseTrackingEvent.getPickupDateTime() == null ? 0 : reverseTrackingEvent.getPickupDateTime().getTime());
                    if(count == (reverseTrackingEventList.size()-1)) {
                        PickupTrackingDetails pickupTrackingDetails = new PickupTrackingDetails();
                        ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTopByReturnIdOrderByIdDesc(response.getReturnId());

                        if (returnCourierDetail != null) {
                            pickupTrackingDetails.setCourier(returnCourierDetail.getReverseCourier());
                            pickupTrackingDetails.setTrackingId(returnCourierDetail.getReversePickupReferenceId());
                            pickupTrackingDetails.setTrackingLink(trackingUrl + returnCourierDetail.getReversePickupReferenceId());
                            pickupAttemptHistory.setPikupTrackingDetails(pickupTrackingDetails);
                        }
                    }
                    pickupAttemptHistoryList.add(pickupAttemptHistory);
                    count++;
                }
            }
            log.info("[getPickupAttemptHistories] : {} ", pickupAttemptHistoryList);
            response.setPickupAttemptHistory(pickupAttemptHistoryList);
        }
    }

    private void addUniqueStatusHistory(ReturnDetailsResponse response, List<ReturnStatusHistory> returnStatusHistoryList, List<ReturnEvent> returnHistoryList) {
        if (null != returnHistoryList) {
            for (int lastStatus = 0; lastStatus < returnHistoryList.size(); lastStatus++) {
                ReturnEvent returnEvent = returnHistoryList.get(lastStatus);
                if ((lastStatus == 0 || !returnEvent.getEvent().equalsIgnoreCase(
                        returnHistoryList.get(lastStatus - 1).getEvent()))) {
                    ReturnStatusHistory returnStatusHistory = new ReturnStatusHistory();
                    returnStatusHistory.setReturnStatus(returnEvent.getEvent().toLowerCase());
                    returnStatusHistory.setDate(returnEvent.getCreatedAt());
                    returnStatusHistory.setComment(returnEvent.getRemarks());
                    ReturnStatusTimelineMapping returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus(returnEvent.getEvent());
                    if(null != returnStatusTimelineMapping){
                        returnStatusHistory.setComment(returnStatusTimelineMapping.getTimelineSubheaderActual());
                    }
                    returnStatusHistoryList.add(returnStatusHistory);
                }
            }
            response.setReturnStatusHistoryList(returnStatusHistoryList);
        }
    }

    private void updatePickUpAddressDetails(ReturnDetail returnOrder, ReturnDetailsResponse response, Long b2bReturnOrderGroupId) {
        if (null != returnOrder.getGroupId()) {
            PickUpAddressDetails pickUpAddressDetails = new PickUpAddressDetails();
            ReturnDetailAddressUpdate returnOrderAddressUpdate = returnOrderAddressUpdateRepository
                    .findByGroupId(returnOrder.getGroupId());
            if (Objects.isNull(returnOrderAddressUpdate) && Objects.nonNull(b2bReturnOrderGroupId) && b2bReturnOrderGroupId != 0) {
                returnOrderAddressUpdate = returnOrderAddressUpdateRepository.findByGroupId(b2bReturnOrderGroupId);
            }
            if (null != returnOrderAddressUpdate) {
                pickUpAddressDetails.setCity(returnOrderAddressUpdate.getCity());
                pickUpAddressDetails.setCountry(returnOrderAddressUpdate.getCountry());
                pickUpAddressDetails.setCountryCode(returnOrderAddressUpdate.getCountry());
                pickUpAddressDetails.setEmail(returnOrderAddressUpdate.getEmail());
                pickUpAddressDetails.setFirstName(returnOrderAddressUpdate.getFirstName());
                pickUpAddressDetails.setLastName(returnOrderAddressUpdate.getLastName());
                pickUpAddressDetails.setTelephone(returnOrderAddressUpdate.getTelephone());
                pickUpAddressDetails.setPostCode(returnOrderAddressUpdate.getPostcode() != null ? String.valueOf(returnOrderAddressUpdate.getPostcode()) : null);
                pickUpAddressDetails.setStreet1(returnOrderAddressUpdate.getStreet1());
                pickUpAddressDetails.setStreet2(returnOrderAddressUpdate.getStreet2());
                pickUpAddressDetails.setState(returnOrderAddressUpdate.getState());
            }
            response.setPickUpAddressDetails(pickUpAddressDetails);
        }
    }

    private void updateItemWiseFastRefundDetails(UwOrderDTO uwOrder, ReturnDetailsResponse response) {
        GetRefundAmountResponse getRefundAmountResponse = refundUtilsService.getOrderRefundAmount(uwOrder.getIncrementId());
        if(Objects.nonNull(getRefundAmountResponse) && Objects.nonNull(getRefundAmountResponse.getRefundDetailsDTO())){
            double refundedAmount = getRefundAmountResponse.getRefundDetailsDTO().getTotalRefundedAmount().getAmount().doubleValue();
            response.setOrderRefundedAmount(refundedAmount < 0 ? 0.0d : refundedAmount);
        }
    }

    private String resolveReturnType(String returnType) {
        return switch (returnType) {
            case Constant.RETURN_TYPE.AWAITED_RTO -> "Awaited_RTO";
            case Constant.RETURN_TYPE.RTO -> "RTO";
            case Constant.RETURN_TYPE.REVERSE -> "Reverse";
            default -> returnType;
        };
    }

    private String resolveReturnMethod(String returnMethod, ReturnDetailsResponse response) {
        if (returnMethod != null) {
            response.setIsSelfDispatch(returnMethod.equalsIgnoreCase(SHIP_TO_LENSKRT));
            return returnMethod;
        } else {
            response.setIsSelfDispatch(false);
            return Constant.OLD_RETURN;
        }
    }

    private String resolvePickupStatus(String status) {
        if(status == null) return null;
        return switch (status) {
            case "new_reverse_pickup" -> "Out for pickup";
            case "reference_id_issued" -> "Pickup scheduled";
            case "awb_assigned" -> "Pickup done";
            case "cancelled" -> "Pickup canceled";
            default -> status;
        };
    }

    private void updateRefundDetails(ReturnDetailsResponse response, IdentifierType identifierType, String identifierValue, ReturnDetail returnOrder) {
        CheckRefundInitiatedRequest checkRefundInitiatedRequest = new CheckRefundInitiatedRequest();
        checkRefundInitiatedRequest.setIdentifierValue(identifierValue);
        checkRefundInitiatedRequest.setIdentifierType(identifierType);

        CheckRefundInitiatedResponse checkRefundInitiatedResponse = refundUtilsService.isRefundInitiated(checkRefundInitiatedRequest);
        GetMethodWiseRefundDetailsResponse getMethodWiseRefundDetailsResponse;
        if (checkRefundInitiatedResponse != null && checkRefundInitiatedResponse.isRefundInitiated()) {
            GetRefundAmountRequest request = new GetRefundAmountRequest();
            request.setIdentifierType(identifierType);
            request.setIdentifierValues(Collections.singletonList(identifierValue));
            getMethodWiseRefundDetailsResponse = refundUtilsService.getMethodWiseRefundDetails(request);

            if (getMethodWiseRefundDetailsResponse != null) {
                getMethodWiseRefundDetailsResponse.getMethodSpecificRefundDetailsDTOMap().entrySet().stream()
                        .map(Map.Entry::getValue)
                        .filter(Objects::nonNull)
                        .flatMap(dto -> dto.getRefundDetailsList().stream())
                        .findFirst()
                        .ifPresent(refundDetails -> {
                            response.setRefundArn(refundDetails.getArn());
                            response.setRefundStatus(refundDetails.getStatus());
                            response.setRefundSource(getMethodWiseRefundDetailsResponse.getMethodSpecificRefundDetailsDTOMap().entrySet().stream()
                                    .filter(entry -> entry.getValue().equals(refundDetails))
                                    .map(entry -> entry.getKey().getCode())
                                    .findFirst()
                                    .orElse(null));
                        });
            }
        } else {
            getMethodWiseRefundDetailsResponse = null;
        }

        refundUtilsService.getAndUpdateOrderRefundDetailsV2(getMethodWiseRefundDetailsResponse, response);

        if (returnOrder == null || !returnOrderActionService.checkIsReturnOrderCancelled(returnOrder)) {
            if (checkRefundInitiatedResponse != null && checkRefundInitiatedResponse.getRefundRequestDTO() != null && checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget() != null) {
                response.setRefundMethodRequest(checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget().getCode());
                response.setRefundIntentCreatedAt(checkRefundInitiatedResponse.getRefundRequestDTO().getCreatedAt());
                response.setRefundAmount(checkRefundInitiatedResponse.getRefundRequestDTO().getRefundAmount().getAmount().doubleValue());
            }
        }
    }

    private void updateExchangeDetails(ReturnDetailsResponse response, OrderExchangeCancellationDetails orderExchangeCancellationDetails) {
        List<ExchangeOrdersDTO> exchangeOrders = orderExchangeCancellationDetails.getExchangeOrdersDTOList();
        if (!CollectionUtils.isEmpty(exchangeOrders)) {
            response.setExchangeOrdersDTO(exchangeOrders.get(0));
            List<UwOrderDTO> uwOrderDTOList = orderAdaptorFeignClient.getUwOrdersDTO(IdentifierType.ORDER_ID.name(), String.valueOf(exchangeOrders.get(0).getExchangeIncrementId())).getBody();
            if (CollectionUtils.isNotEmpty(uwOrderDTOList)) {
                response.setExchangeUwOrderDTO(uwOrderDTOList.stream().filter(uw -> uw.getParentUw() == 0).findFirst().orElse(null));
                if (response.getExchangeUwOrderDTO() != null) {
                    response.setExchangeStatus(response.getExchangeUwOrderDTO().getShipmentStatus());
                }
            }
        }
        response.setExchangeDetails(orderExchangeCancellationDetails.getExchangeDetailsForBackSync());
    }

    private void enrichReturnDetails(ReturnDetailsResponse response, ReturnDetail returnDetail, OrderInfoResponseDTO orderInfoResponseDTO) {
        returnDetailsEnrichService.enrichReturnDetails(returnDetail, response, orderInfoResponseDTO);
        if (!returnOrderActionService.checkIsReturnOrderCancelled(returnDetail)) {
            CheckRefundInitiatedRequest checkRefundInitiatedRequest = new CheckRefundInitiatedRequest();
            checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(response.getReturnId()));
            checkRefundInitiatedRequest.setIdentifierType(IdentifierType.RETURN_ID);

            CheckRefundInitiatedResponse checkRefundInitiatedResponse = refundUtilsService.isRefundInitiated(checkRefundInitiatedRequest);
            if (checkRefundInitiatedResponse != null && checkRefundInitiatedResponse.getRefundRequestDTO() != null && checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget() != null) {
                response.setRefundMethodRequest(checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget().getCode());
            }
        }
    }

    @Override
    public List<ReturnDetailedResponse> getReturnsDetailedResponse(OrderTelephoneMapperRequestDTO returnDetailsRequest) {
        List<ReturnDetailedResponse> detailedResponses = null;
        boolean validatedReturnRequest = validateReturnRequest(returnDetailsRequest);
        if(validatedReturnRequest){
            if(!StringUtils.isEmpty(returnDetailsRequest.getPhoneNumber())){
                ResponseEntity<List<OrderTelephoneMapperDTO>> orderDetailsByTelephoneResponse = orderOpsFeignClient.getOrderDetailsByTelephone(returnDetailsRequest);
                log.info("[getReturnsDetailedResponse] orderDetailsByTelephoneResponse : {}", orderDetailsByTelephoneResponse);
                if(orderDetailsByTelephoneResponse.getStatusCode().is2xxSuccessful() && !CollectionUtils.isEmpty(orderDetailsByTelephoneResponse.getBody())){
                    List<OrderTelephoneMapperDTO> orderTelephoneMapperDTOS = orderDetailsByTelephoneResponse.getBody();
                    detailedResponses = getReturnsDetailedResponse(orderTelephoneMapperDTOS, returnDetailsRequest);
                }
            }else{
                detailedResponses = getReturnsDetailBasedOnOrderId(returnDetailsRequest);
            }
        }
        log.info("[getReturnsDetailedResponse] detailedResponses : {}", detailedResponses);
        return detailedResponses;
    }


    private boolean validateReturnRequest(OrderTelephoneMapperRequestDTO returnDetailsRequest) {
        if(returnDetailsRequest == null || (returnDetailsRequest.getPhoneNumber() == null && returnDetailsRequest.getOrderId() == null)){
            log.error("[validateReturnRequest] key param missing in request ---- returnDetailsRequest : {}", returnDetailsRequest);
            return false;
        }
        return true;
    }

    private List<ReturnDetailedResponse> getReturnsDetailedResponse(List<OrderTelephoneMapperDTO> orderTelephoneMapperDTOS, OrderTelephoneMapperRequestDTO returnDetailsRequest){
        List<ReturnDetailedResponse> returnDetailedResponses = new ArrayList<>();
        for(OrderTelephoneMapperDTO orderTelephoneMapperDTO : orderTelephoneMapperDTOS){
            Integer incrementId = orderTelephoneMapperDTO.getIncrementId();
            returnDetailsRequest.setOrderId(incrementId);
            returnDetailedResponses.addAll(getReturnsDetailBasedOnOrderId(returnDetailsRequest));
        }
        return returnDetailedResponses;
    }

    private List<ReturnDetailedResponse> getReturnsDetailBasedOnOrderId(OrderTelephoneMapperRequestDTO returnDetailsRequest) {
        List<ReturnDetailedResponse> detailedResponses = new ArrayList<>();
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date startTime = sdf.parse(returnDetailsRequest.getStartDate());
            Date endTime = sdf.parse(returnDetailsRequest.getEndDate());
            List<ReturnDetail> returnDetails = returnDetailRepository.getReturnsByOrder(returnDetailsRequest.getOrderId(), startTime, endTime, returnDetailsRequest.getPageSize(), returnDetailsRequest.getPageNo());
            if(!CollectionUtils.isEmpty(returnDetails)){
                for(ReturnDetail returnDetail : returnDetails){
                    ReturnDetailedResponse returnDetailedResponse = new ReturnDetailedResponse();
                    returnDetailedResponse.setIncrementId(returnDetail.getIncrementId());
                    returnDetailedResponse.setReturnId(returnDetail.getId());
                    List<ReturnDetailItem> returnDetailItems = returnDetailItemRepository.findByReturnId(returnDetail.getId());
                    if(!CollectionUtils.isEmpty(returnDetailItems)){
                        ReturnDetailItem returnDetailItem = returnDetailItems.get(0);
                        returnDetailedResponse.setChannel(returnDetailItem.getChannel());
                        returnDetailedResponse.setReturnReason(returnDetailItem.getReasonForReturn());
                        returnDetailedResponse.setProductId(Math.toIntExact(returnDetailItem.getProductId()));
                        returnDetailedResponse.setItemId(returnDetailItem.getItemId());
                        returnDetailedResponse.setCreationDate(returnDetailItem.getReturnCreateDatetime());
                    }
                    detailedResponses.add(returnDetailedResponse);
                }
            }else{
                List<ReturnOrder> returnOrders = returnOrderRepository.getReturnsByOrder(returnDetailsRequest.getOrderId(), startTime, endTime, returnDetailsRequest.getPageSize(), returnDetailsRequest.getPageNo());
                if(!CollectionUtils.isEmpty(returnOrders)){
                    for(ReturnOrder returnDetail : returnOrders){
                        ReturnDetailedResponse returnDetailedResponse = new ReturnDetailedResponse();
                        returnDetailedResponse.setIncrementId(returnDetail.getOrderNo());
                        returnDetailedResponse.setReturnId(returnDetail.getReturnId());
                        List<ReturnOrderItem> returnDetailItems = returnOrderItemRepository.findByReturnId(returnDetail.getReturnId());
                        if(!CollectionUtils.isEmpty(returnDetailItems)){
                            ReturnOrderItem returnDetailItem = returnDetailItems.get(0);
                            returnDetailedResponse.setChannel(returnDetailItem.getChannel());
                            returnDetailedResponse.setReturnReason(returnDetailItem.getReasonForReturn());
                            returnDetailedResponse.setProductId(Math.toIntExact(returnDetailItem.getProductId()));
                            returnDetailedResponse.setItemId(returnDetailItem.getItemId());
                            returnDetailedResponse.setCreationDate(returnDetailItem.getReturnCreateDatetime());
                        }
                        detailedResponses.add(returnDetailedResponse);
                    }
                }
            }
        }catch (Exception exception){
            log.error("[getReturnsDetailBasedOnOrderId] exception : {}", exception.getMessage());
        }
        return detailedResponses;
    }

}
