package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;

import java.util.List;

public interface ICommunicationService {
    void triggerCommunication(ReverseCourierDetail reverseCourierDetail, ReturnCreationRequestDTO returnCreationRequest, Long groupId, List<OrdersDTO> returnedOrders,
                              List<UwOrderDTO> uwOrders, List<ReturnItemDTO> exchangeItemList, Integer requestId, Integer returnId, Boolean isExchangeCreated);
    void sendReturnUpdateCommunication(ReturnOrderDTO returnOrderDTO, OrdersDTO ordersDTO, ReturnCourierDetail returnCourierDetail, String status , String currStatus);
    void pushToCommunicationTopic(CommRequestDTO commRequestDTO);

    void sendCommunication(CommRequestDTO commRequestDTO);
}
