package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.service.IReturnInitiationService;
import com.lenskart.returnservice.service.IReturnMethodResolver;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ReturnMethodResolverImpl implements IReturnMethodResolver {

    @Resource(name = "ReturnInitiationServiceImpl")
    IReturnInitiationService vsmOrPosService;

    @Resource(name = "POS_ReturnInitiationServiceImpl")
    IReturnInitiationService posReturnService;

    private Map<String, IReturnInitiationService> returnInitiationServiceMap;

    @PostConstruct
    public void populateReturnMethodMap() {
        returnInitiationServiceMap = new HashMap<>();
        returnInitiationServiceMap.put(Constant.RETURN_SOURCE.VSM, vsmOrPosService);
        returnInitiationServiceMap.put(Constant.RETURN_SOURCE.WEB, vsmOrPosService);
        returnInitiationServiceMap.put(Constant.RETURN_SOURCE.POS, posReturnService);
        returnInitiationServiceMap.put(Constant.RETURN_SOURCE.WAREHOUSE, vsmOrPosService);
    }

    @Override
    public IReturnInitiationService getReturnMethod(String returnMethod) {
        return returnInitiationServiceMap.get(returnMethod);
    }

}
