package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.platform.fl.utils.dto.FinanceEventDto;
import com.lenskart.platform.fl.utils.dto.returnPayloads.packingSlip.PackingSlip;
import com.lenskart.platform.fl.utils.dto.returnPayloads.packingSlip.PackingSlipContract;
import com.lenskart.platform.fl.utils.dto.returnPayloads.packingSlip.SalesLineList;
import com.lenskart.platform.fl.utils.dto.returnPayloads.salesOrder.*;
import com.lenskart.platform.fl.utils.enums.EventChannel;
import com.lenskart.platform.fl.utils.enums.EventStatus;
import com.lenskart.platform.fl.utils.finance.service.FinanceService;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.FinanceReturnEvent;
import com.lenskart.returncommon.model.enums.SyncStatus;
import com.lenskart.returncommon.model.request.PackageSlipCreationRequest;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.response.KafkaProducerResponse;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.D365ReturnTrackingRepository;
import com.lenskart.returncommon.utils.Constant.IDENTIFIER_TYPE;
import com.lenskart.returnrepository.repository.FinanceSyncReturnTrackingRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import com.lenskart.returnservice.service.helper.InventoryDataHelper;
import com.lenskart.returnservice.service.helper.ReturnEventPackingSlipHelper;
import com.lenskart.returnservice.service.helper.ReturnEventServiceHelper;
import com.lenskart.returnservice.utils.ProductUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lenskart.returncommon.utils.Constant.*;
import static com.lenskart.returncommon.utils.Constant.EVENT.RETURN_EINVOICE_SYNC_TRIGGERED;
import static com.lenskart.returncommon.utils.Constant.EVENT.D365_RETURN_ORDER_SYNCED;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.B2B;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.D365_FINANCE_CONSUMER_RETURN_EINVOICE_TOPIC;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.INVOICE_CREATE_REQUEST;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.FL_SALE_ORDER_RETURN;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.FL_PACKAGE_SLIP;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.FL_GIFT_CARD;

@Slf4j
@Service
public class D365FinanceServiceImpl implements ID365FinanceService {

    @Autowired
    IKafkaService kafkaService;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnOrderItemService returnOrderItemService;

    @Autowired
    private IReturnEventService returnEventService;

    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${feign.client.domain.order-ops: https://order-ops.scm.preprod.lenskart.com/}")
    private String orderOpsBaseUrl;

    @Autowired
    private D365ReturnTrackingRepository d365ReturnTrackingRepository;

    @Autowired
    private ReturnEventServiceHelper returnEventServiceHelper;

    @Autowired
    private InventoryDataHelper inventoryDataHelper;

    @Autowired
    private IReturnOrderAddressUpdateService returnOrderAddressUpdateService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private Environment env;

    @Autowired
    private ReturnEventPackingSlipHelper returnEventPackingSlipHelper;
    
    @Autowired
    private FinanceSyncReturnTrackingRepository financeSyncReturnTrackingRepository;

    @Override
    public Boolean sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest returnCreateRequest) {
        log.info("[D365FinanceServiceImpl][sendDataToFinanceConsumer] inside D365FinanceServiceImpl for courier return");
        boolean dataProcessedToConsumer = false;
        if (returnCreateRequest != null) {
            ResponseEntity<KafkaProducerResponse> kafkaResponse = null;
            log.info("[D365FinanceServiceImpl][sendDataToFinanceConsumer] returnCreateRequest for courier return {} ",returnCreateRequest.toString());
            try {
                log.info("[D365FinanceServiceImpl][sendDataToFinanceConsumer] Pushing data to D365ReturnTopic for courier return for uwItemId {} ",returnCreateRequest.getUwItemId());
                Integer returnId = returnOrderItemService.getReturnId(returnCreateRequest.getUwItemId());
                List<D365ReturnTracking> d365ReturnTrackings = d365ReturnTrackingRepository.findByReturnId(returnId);
                if(CollectionUtils.isEmpty(d365ReturnTrackings)){
                    log.info("[D365FinanceServiceImpl][sendDataToFinanceConsumer] creating entry for d365_return_tracking for uwItemId {} ",returnCreateRequest.getUwItemId());
                    D365ReturnTracking d365ReturnTracking = createD365ReturnTracking(returnId);
                    d365ReturnTrackingRepository.save(d365ReturnTracking);
                }
                Map<String,Object> d365ReturnSyncDTO = new HashMap<>();
                d365ReturnSyncDTO.put("uwItemId", returnCreateRequest.getUwItemId());
                String baseUrl = orderOpsBaseUrl.endsWith("/") ? orderOpsBaseUrl : orderOpsBaseUrl + "/";
                String orderOpsUrl = String.format("%spush-data/%s", baseUrl, Constant.D365_FINANCE_CONSUMER_RETURN_TOPIC);
                HttpEntity<Map<String, Object>> httpEntity = new HttpEntity(d365ReturnSyncDTO);
                log.info("[sendDataToFinanceConsumerForAwbReturn] orderOpsUrl : {}, httpEntity : {}", orderOpsUrl, httpEntity);
                ResponseEntity<String> responseEntity = restTemplate.exchange(orderOpsUrl, HttpMethod.POST, httpEntity, String.class);
                log.info("[sendDataToFinanceConsumerForAwbReturn] responseEntity : {}", responseEntity);
                if(responseEntity.getStatusCode().is2xxSuccessful()){
                    dataProcessedToConsumer = responseEntity.getBody() != null;
                    returnEventService.createReturnEvent(null, returnId, D365_RETURN_ORDER_SYNCED, "");
                }
            } catch (Exception e) {
                log.error("[D365FinanceServiceImpl][sendDataToFinanceConsumer] Exception occurred & Data not pushed to D365ReturnTopic for for courier return uwItemId {} ", returnCreateRequest.getUwItemId());
                log.error("[D365FinanceServiceImpl][sendDataToFinanceConsumer] Exception message for courier return {} ", e.getMessage());
            } 
            sendToNewFinanceArchitecture(returnCreateRequest);
        } else {
            log.error("[D365FinanceServiceImpl][sendDataToFinanceConsumer] returnCreateRequest cannot be NULL for courier return");
        }
        log.info("[D365FinanceServiceImpl][sendDataToFinanceConsumer] dataProcessedToConsumer for courier return {} ",dataProcessedToConsumer);
        return dataProcessedToConsumer;
    }

    private D365ReturnTracking createD365ReturnTracking(Integer returnId) {
        D365ReturnTracking d365ReturnTracking = new D365ReturnTracking();
        d365ReturnTracking.setReturnId(returnId);
        d365ReturnTracking.setPslipCreated(0);
        d365ReturnTracking.setTjournalCreated(0);
        d365ReturnTracking.setD365Flag(0);
        d365ReturnTracking.setPSlipRetryCount(0);
        d365ReturnTracking.setMovJournalFlag(0);
        d365ReturnTracking.setRetryCount(0);
        String status = returnOrderActionService.getReturnOrderStatusById(returnId);
        d365ReturnTracking.setOldStatus(status);
        Optional<ReturnDetail> returnDetail = returnOrderActionService.findReturnOrderById(returnId);
        returnDetail.ifPresent(detail -> {
            d365ReturnTracking.setIncrementId(detail.getIncrementId());
            d365ReturnTracking.setSource(detail.getSource());
        });
        d365ReturnTracking.setCreatedAt(new Date());
        return d365ReturnTracking;
    }
    
    private void sendToNewFinanceArchitecture(ReturnCreateRequest returnCreateRequest) {
        try {
            if(Boolean.TRUE.equals(env.getProperty(NEW_ARCHITECTURE_FLAG, Boolean.class))) {
                kafkaService.pushToKafka(FL_SALE_ORDER_RETURN, String.valueOf(returnCreateRequest.getUwItemId()), returnCreateRequest);
                kafkaService.pushToKafka(FL_GIFT_CARD, String.valueOf(returnCreateRequest.getUwItemId()), returnCreateRequest);
            }
        } catch (Exception e) {
            log.error("[D365FinanceServiceImpl][sendDataToFinanceConsumer][NEW_ARCHITECTURE] Exception occurred & Data not pushed to D365ReturnTopic for for courier return uwItemId {} ", returnCreateRequest.getUwItemId());
            log.error("[D365FinanceServiceImpl][sendDataToFinanceConsumer][NEW_ARCHITECTURE] Exception message for courier return {} ", e.getMessage());
        }
    }

    @Override
    public Boolean createReturnEInvoice(Integer returnId, Integer uwItemId, String sourceFacility) {
        log.info("[createReturnEInvoice] returnId : {}, uwItemId : {}, sourceFacility : {}", returnId, uwItemId, sourceFacility);
        Optional<ReturnDetail> returnOrderOpt = returnOrderActionService.findReturnOrderById(returnId);
        if (returnOrderOpt.isEmpty()) {
            return false;
        }
        if (uwItemId == null) {
            ReturnDetailItem returnOrderItem = returnOrderActionService.findTopByReturnId(returnId);
            if (returnOrderItem != null) {
                uwItemId = returnOrderItem.getUwItemId();
            }
        }
        ReturnDetail returnOrder = returnOrderOpt.get();

        String flag = null;
        SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey("return_einvoicing", "return_einvoicing_new_flow_enabled");
        if(systemPreference != null){
            flag = systemPreference.getValue();
        }

        log.info("[createReturnEInvoice] flag : {}",flag);
        if("true".equalsIgnoreCase(flag)){
            log.info("[createReturnEInvoice] returnId : {} , pushing to : {}",returnId, INVOICE_CREATE_REQUEST);
            ReturnEInvoiceRequest returnEInvoiceRequest = new ReturnEInvoiceRequest();
            String destinationFacility = returnOrder.getFacilityCode();
            if(StringUtils.isEmpty(destinationFacility)){
                destinationFacility = sourceFacility;
                if(StringUtils.isEmpty(destinationFacility)){
                    //call order-ops inventory to get the facility
                    destinationFacility = getFacility(uwItemId);
                }
            }
            log.info("[createReturnEInvoice] returnId : {}, destinationFacility : {}", returnId, destinationFacility);
            returnEInvoiceRequest.setFacility(destinationFacility);
            returnEInvoiceRequest.setDocumentSource("RETURN");
            returnEInvoiceRequest.setDocumentProvider("FDS");
            returnEInvoiceRequest.setDocumentType("CREDIT_NOTE");
            returnEInvoiceRequest.setDocumentSourceReferenceId(String.valueOf(returnId));
            try{
                orderOpsFeignClient.pushData(objectMapper.convertValue(returnEInvoiceRequest, Map.class), INVOICE_CREATE_REQUEST);
            }catch (Exception exception){
                log.error("[createReturnEInvoice] returnId : {}, exception : {}", returnId, exception.getMessage());
            }
            returnEventService.createReturnEvent(null, returnId, RETURN_EINVOICE_SYNC_TRIGGERED, "");
        }else{
            ReturnEInvoicingKafkaRequest returnEInvoicingKafkaRequest = new ReturnEInvoicingKafkaRequest();
            returnEInvoicingKafkaRequest.setReturnId(returnId);
            returnEInvoicingKafkaRequest.setUwItemIds(Collections.singletonList(uwItemId));
            returnEInvoicingKafkaRequest.setReturnType(returnOrder.getReturnType());
            returnEInvoicingKafkaRequest.setReturnCreatedAt(returnOrder.getReturnCreateDatetime());
            returnEInvoicingKafkaRequest.setIncrementId(returnOrder.getIncrementId());
            orderOpsFeignClient.pushData(objectMapper.convertValue(returnEInvoicingKafkaRequest, Map.class), D365_FINANCE_CONSUMER_RETURN_EINVOICE_TOPIC);
            returnEventService.createReturnEvent(null, returnId, RETURN_EINVOICE_SYNC_TRIGGERED, "");
        }

        return true;
    }

    private String getFacility(Integer uwItemId) {
        log.info("[getFacility] uwItemId : {}", uwItemId);
        String facility = "";
        try{
            PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = orderOpsFeignClient.getPurchaseOrderDetails(Constant.IDENTIFIER_TYPE.UWITEM_ID, String.valueOf(uwItemId)).getBody();
            if (purchaseOrderDetailsDTO != null && !CollectionUtils.isEmpty(purchaseOrderDetailsDTO.getUwOrders())) {
                UwOrderDTO uwOrderDTO = purchaseOrderDetailsDTO.getUwOrders().get(0);
                facility = uwOrderDTO.getFacilityCode();
            }
        }catch (Exception exception){
            log.error("[getFacility] exception : {}", exception.getMessage());
        }
        log.info("[getFacility] uwItemId : {}, facility : {}", uwItemId, facility);
        return facility;
    }

    @Override
    public void syncReturn(UwOrderDTO uwOrder) {
        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
        returnCreateRequest.setUwItemId(uwOrder.getUwItemId());
        ReturnDetailItem returnOrderItem = returnOrderItemService.findByUwItemId(uwOrder.getUwItemId());
        ReturnOrderItemDTO returnOrderItemDTO = null;
        try{
            returnOrderItemDTO = objectMapper.convertValue(returnOrderItem, ReturnOrderItemDTO.class);
        }catch (Exception exception){
            log.error("[syncReturn] exception : "+exception);
        }
        returnCreateRequest.setReturnOrderItem(returnOrderItemDTO);
        sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
    }

    public void generatePayloadForReturn(ReturnCreateRequest returnRequestDto) throws Exception {
        log.info("Generating payload for uwItemId : {}", returnRequestDto.getUwItemId());
        try {
            Long clientEventId = returnEventServiceHelper.createFinanceEvent(returnRequestDto, RETURN_SALE_ORDER);
            com.lenskart.orderops.model.ReturnOrderItem returnOrderItem = null;
            Integer uwItemId = returnRequestDto.getUwItemId();
            Integer returnId = null;
            if(returnRequestDto.getReturnOrderItem() != null) {
                returnId = returnRequestDto.getReturnOrderItem().getReturnId();
            }
            ReturnOrder returnOrder = null;
            SalesOrderHeader salesOrderHeader = new SalesOrderHeader();
            ReturnDetailAddressUpdateDTO returnDetailAddressUpdateDTO = null;
            UwOrderDTO uwOrderDTO = null;
            OrdersDTO ordersDTO = null;
            OrderAddressUpdateDTO orderAddressUpdateDto = null;
            OrdersHeaderDTO ordersHeaderDTO = null;
            ArrayList<SoLine> soLines = new ArrayList<SoLine>();
            ArrayList<MiscChargesLine> miscChargesLines = new ArrayList<MiscChargesLine>();
            ArrayList<MiscChargesHeader> miscChargesHeader = new ArrayList<MiscChargesHeader>();
            boolean sbrtFlag = false;
            PurchaseOrderDetailsDTO purchaseOrderDetails = inventoryDataHelper.getOrdersDetailsInfo("UW_ITEM_ID", String.valueOf(uwItemId));
            ordersHeaderDTO = purchaseOrderDetails.getOrdersHeaderDTO();
            log.info("fetching details from returnRequestDto for uwItemId : {}", uwItemId);
            try {
                ReturnDetailsDTO returnDetailsDTO = returnOrderActionService.getReturnDetailsByIdentifier("UW_ITEM_ID", Collections.singletonList(String.valueOf(uwItemId)), true);
                returnOrderItem = returnOrderItemService.getReturnOrderItem(returnDetailsDTO);
                returnId = (Objects.nonNull(returnOrderItem)) ? returnOrderItem.getReturnId() : returnId;
                if (null == returnId && null == returnOrderItem) {
                    throw new Exception("No entry found in returnOrderItem table for uw_item_id " + uwItemId);
                }
                returnDetailsDTO = returnOrderActionService.getReturnDetailsByIdentifier("RETURN_ID", Collections.singletonList(String.valueOf(returnId)), true);
                returnOrder = returnOrderItemService.getReturnOrder(returnDetailsDTO);
                if (Objects.nonNull(returnOrder)) {
                    returnRequestDto.getReturnOrderItem().setReturnId(returnOrder.getReturnId());
                }
                uwOrderDTO = getUwOrderFromList(purchaseOrderDetails.getUwOrders());

                List<OrdersDTO> ordersDTOsList = purchaseOrderDetails.getOrders();
                ordersDTO = ordersDTOsList.get(0);

                sbrtFlag = returnEventServiceHelper.getSBRTFlag(uwOrderDTO,ordersHeaderDTO);

                returnEventServiceHelper.setSbrtUwOrderDetails(uwOrderDTO, sbrtFlag);

                if(Objects.nonNull(uwOrderDTO) && StringUtils.isNotBlank(uwOrderDTO.getProductDeliveryType()) && B2B.equalsIgnoreCase(uwOrderDTO.getProductDeliveryType()) && isB2BPhysicalItem(uwOrderDTO)){
                    log.info("[generatePayloadForReturn] not triggering d365 return for physical item uwItemId: {}", uwOrderDTO.getUwItemId());
                    returnEventServiceHelper.updateFinanceEvent(returnRequestDto, RETURN_SALE_ORDER, EventStatus.SKIPPED, clientEventId);
                    return;
                }

                List<OrderAddressUpdateDTO> orderAddressUpdateDTOList = purchaseOrderDetails.getOrderAddressUpdateDTOs();
                if(!CollectionUtils.isEmpty(orderAddressUpdateDTOList)) {
                    orderAddressUpdateDto = orderAddressUpdateDTOList.get(0);
                }

                if (null != returnOrder) {
                    log.info("fetching returnOrderAddressUpdate for " + returnOrder.getReturnId());
                    returnDetailAddressUpdateDTO = returnOrderAddressUpdateService.getReturnAddressUpdateNew(returnOrder.getOrderNo());
                }

            } catch (Exception e) {
                log.error("Exception found to get details from returnRequestDto for uwItemId : {} : ", uwItemId, e);
                saveReturnTrackingEvent(returnId,uwItemId,FinanceReturnEvent.RETURN_SALE_ORDER, SyncStatus.FAILED, e.getMessage(), returnRequestDto.getRetryCount());
                throw e;
            }

            ProductUtil.setCountryMapping();
            ProductUtil.setStateMapping();
            String salesOrderNumber = returnId + "_" + uwItemId;
            log.info("[ReturnOrderServiceImpl][createReturn] salesOrderNumber : " + salesOrderNumber);
            String rmaNumber = returnId + "_" + uwOrderDTO.getIncrementId();
            log.info("[ReturnOrderServiceImpl][createReturn] rmaNumber : " + rmaNumber);
            if (Objects.nonNull(returnOrder) && null != returnOrder.getReturnMethod() && "RPU".equalsIgnoreCase(returnOrder.getReturnMethod())) {
                returnEventServiceHelper.setReturnSpecificDetailsToReturnSales(returnDetailAddressUpdateDTO, salesOrderHeader, ordersHeaderDTO, ordersDTO);
            } else {
                returnEventServiceHelper.setReturnSpecificDetailsToReturnSales(salesOrderHeader, orderAddressUpdateDto, ordersHeaderDTO, ordersDTO);
            }
            returnEventServiceHelper.addReturnSalesDetails(uwOrderDTO, ordersDTO, ordersHeaderDTO, salesOrderHeader, soLines, miscChargesLines, miscChargesHeader, salesOrderNumber, rmaNumber, returnOrder, returnRequestDto,sbrtFlag);
            log.info("salesOrderHeader generated for uwItemId : " + returnRequestDto.getUwItemId());
            if(!returnRequestDto.isPackageSlip()){
                sendSaleOrderEventToFL(salesOrderHeader,ordersHeaderDTO,uwItemId,clientEventId);
                // saveReturnTrackingEvent(returnId,uwItemId,FinanceReturnEvent.RETURN_SALE_ORDER, SyncStatus.SUCCESS, null, returnRequestDto.getRetryCount());
            }
    
            PackageSlipCreationRequest packageSlipCreationRequest = new PackageSlipCreationRequest();
            packageSlipCreationRequest.setReturnCreateRequest(returnRequestDto);
            packageSlipCreationRequest.setSalesOrderHeader(salesOrderHeader);
    
            kafkaService.pushToKafka(FL_PACKAGE_SLIP, String.valueOf(returnRequestDto.getUwItemId()), packageSlipCreationRequest);
        } catch (Exception e) {
            log.error("exception found to get details from returnRequestDto for uwItemId : {} : ", returnRequestDto.getUwItemId(), e);
            saveReturnTrackingEvent(null,returnRequestDto.getUwItemId(),FinanceReturnEvent.RETURN_SALE_ORDER, SyncStatus.FAILED, e.getMessage(), returnRequestDto.getRetryCount());
            throw e;
        }
    }

    private void sendSaleOrderEventToFL(SalesOrderHeader salesOrderHeader,OrdersHeaderDTO ordersHeaderDTO, Integer uwItemId, Long clientEventId) throws Exception {
        Map<String, Object> map = objectMapper.convertValue(salesOrderHeader, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> saleOrderMap = new HashMap<>();
        saleOrderMap.put(RETURN_SALE_ORDER_HEADER, map);
        saleOrderMap.put(TYPE, RETURN_SALE_ORDER_TYPE);

        log.info("SaleOrderMap:{}", saleOrderMap);
        SalesOrder salesOrder =  objectMapper.convertValue(saleOrderMap, SalesOrder.class);
        salesOrder.setInventLocationId(salesOrder.getSalesOrderHeader().getInventLocationId());
        salesOrder.setStore(salesOrder.getSalesOrderHeader().getStores());
        salesOrder.setEventCorrelationIdentifier(salesOrder.getSalesOrderHeader().getSalesOrderNumber());
        salesOrder.setEventUniqueIdentifier(salesOrder.getSalesOrderHeader().getSalesOrderNumber());
        String rawEvent = null;
        try {
            rawEvent = objectMapper.writeValueAsString(salesOrder);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize saleOrder to JSON string", e);
        }
        log.info("SaleOrder Raw Payload:{}",rawEvent);
        FinanceEventDto financeEventDto = FinanceEventDto.builder()
                .legalEntity(salesOrderHeader.getLegalEntity())
                .channel(EventChannel.KAFKA)
                .country(ordersHeaderDTO.getLkCountry())
                .rawEventData(rawEvent)
                .clientEventId(String.valueOf(clientEventId))
                .eventData(salesOrder)
                .source(CES)
                .eventType(RETURN_SALE_ORDER)
                .createdBy(CES)
                .build();
        log.info("Finance Event Dto for Sale Order:{}",financeEventDto);
        financeService.saveEvent(financeEventDto);
    }

    private UwOrderDTO getUwOrderFromList(List<UwOrderDTO> uwOrderDTOList) {
        if(!CollectionUtils.isEmpty(uwOrderDTOList)) {
            return uwOrderDTOList.get(0);
        }
        return null;
    }

    @Override
    public void generatePayloadForPackingSlip(PackageSlipCreationRequest packageSlipCreationRequest) throws Exception {
        ReturnCreateRequest returnRequestDto = packageSlipCreationRequest.getReturnCreateRequest();
        SalesOrderHeader returnPayload = packageSlipCreationRequest.getSalesOrderHeader();
        try {
            Long clientEventId = returnEventServiceHelper.createFinanceEvent(returnRequestDto, RETURN_PACKING_SLIP);
            PackingSlipContract packingSlip = new PackingSlipContract();
            SalesLineList salesLineList;
            ArrayList<SalesLineList> salesLineLists = new ArrayList<>();
            Integer uwItemId = returnRequestDto.getUwItemId();
            PurchaseOrderDetailsDTO purchaseOrderDetails = inventoryDataHelper.getOrdersDetailsInfo("UW_ITEM_ID", String.valueOf(uwItemId));
            OrdersHeaderDTO ordersHeaderDTO = purchaseOrderDetails.getOrdersHeaderDTO();
            log.info("preparing packingSlip payload : {}", packingSlip);
            UwOrderDTO uwOrder = getUwOrderFromList(purchaseOrderDetails.getUwOrders());

            UwOrderDTO b2bCounterPart = returnEventServiceHelper.getB2BCounterPart(uwOrder);

            boolean sbrtFlag = returnEventServiceHelper.getSBRTFlag(uwOrder,ordersHeaderDTO);

            Integer returnId = returnRequestDto.getReturnOrderItem().getReturnId();
            ReturnDetailsDTO returnDetailsDTO = returnOrderActionService.getReturnDetailsByIdentifier(IDENTIFIER_TYPE.RETURN_ID, Collections.singletonList(String.valueOf(returnId)), true);
            ReturnOrder returnOrder = returnOrderItemService.getReturnOrder(returnDetailsDTO);

            packingSlip.setSalesId(returnRequestDto.getReturnOrderItem().getReturnId() + "_" + returnRequestDto.getUwItemId());
            packingSlip.setPackingSlipId(returnEventPackingSlipHelper.getShippingPackageId(uwOrder,b2bCounterPart));
            packingSlip.setLegalEntity(returnEventPackingSlipHelper.getPSlipLegalEntity(uwOrder, ordersHeaderDTO, returnPayload, sbrtFlag));
            returnEventPackingSlipHelper.setPSlipDate(uwOrder, packingSlip,returnOrder);
            returnEventPackingSlipHelper.setPSlipTrackingNo(uwOrder, packingSlip,returnOrder);
            returnEventPackingSlipHelper.setPSlipSalesInvoiceNo(uwOrder, packingSlip,b2bCounterPart);
            packingSlip.setDescription(String.valueOf(uwOrder.getIncrementId()));

            //SalesLine
            List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
            uwOrderDTOList.add(uwOrder);
            List<UwOrderDTO> lensUwOrders = inventoryDataHelper.getLensUWOrdersList(uwOrder.getIncrementId(),uwItemId);
            uwOrderDTOList.addAll(lensUwOrders);
            Set<Integer> b2bReferenceItemIds = new HashSet<>();

            for (UwOrderDTO uw : uwOrderDTOList) {
                if (B2B.equals(uw.getProductDeliveryType())) {
                    b2bReferenceItemIds.add(uw.getB2bRefrenceItemId());
                }
            }

            List<UwOrderDTO> b2bUwItemOrders = returnEventServiceHelper.getB2BUwOrders(b2bReferenceItemIds.stream().toList());

            Map<Integer, UwOrderDTO> b2bUwItemOrdersMap = b2bUwItemOrders.stream()
                    .collect(Collectors.toMap(
                            UwOrderDTO::getUwItemId,
                            Function.identity()
                    ));

            if (!CollectionUtils.isEmpty(uwOrderDTOList)) {
                for (UwOrderDTO uwOrderDTO:uwOrderDTOList) {
                    UwOrderDTO b2bReferenceItemOrder = b2bUwItemOrdersMap.get(uwOrderDTO.getB2bRefrenceItemId());
                    salesLineList = returnEventPackingSlipHelper.prepareSoLinesForPSlip(uwOrderDTO, returnRequestDto,b2bReferenceItemOrder);
                    salesLineLists.add(salesLineList);
                }
            }

            packingSlip.setSalesLineList(salesLineLists);
            String packSlipJson = new ObjectMapper().writeValueAsString(packingSlip);
            log.info("PackingSlip payload prepared {} ", packSlipJson);
            sendPackingSlipEventToFL(packingSlip,ordersHeaderDTO,uwItemId,clientEventId);
            // saveReturnTrackingEvent(returnRequestDto.getReturnOrderItem().getReturnId(),uwItemId,FinanceReturnEvent.RETURN_PACKING_SLIP, SyncStatus.SUCCESS, null, returnRequestDto.getRetryCount());
        } catch (Exception e) {
            log.error("[ReturnOrderServiceImpl][createReturn] Exception found to create packingSlip: " + e.getMessage());
            saveReturnTrackingEvent(returnRequestDto.getReturnOrderItem().getReturnId(),returnRequestDto.getUwItemId(),FinanceReturnEvent.RETURN_PACKING_SLIP, SyncStatus.FAILED, e.getMessage(), returnRequestDto.getRetryCount());
            throw e;
        }
    }

    private void sendPackingSlipEventToFL(PackingSlipContract packingSlip, OrdersHeaderDTO ordersHeaderDTO, Integer uwItemId, Long clientEventId) throws Exception {
        Map<String, Object> map = objectMapper.convertValue(packingSlip, new TypeReference<Map<String, Object>>() {});
        Map<String, Object> packingSlipMap = new HashMap<>();
        packingSlipMap.put(CONTRACT, map);
        packingSlipMap.put(TYPE, RETURN_PACKING_SLIP_TYPE);

        log.info("PackingSlipMap:{}", packingSlipMap);
        PackingSlip packingSlipContract =  objectMapper.convertValue(packingSlipMap, PackingSlip.class);
        packingSlipContract.setEventCorrelationIdentifier(packingSlipContract.getContract().getSalesId());
        packingSlipContract.setEventUniqueIdentifier(packingSlipContract.getContract().getSalesId());
        String rawEvent = null;
        try {
            rawEvent = objectMapper.writeValueAsString(packingSlipContract);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize packingSlip to JSON string", e);
        }
        log.info("PackingSLip Raw Payload:{}",rawEvent);
        FinanceEventDto financeEventDto = FinanceEventDto.builder()
                .legalEntity(packingSlip.getLegalEntity())
                .channel(EventChannel.KAFKA)
                .country(ordersHeaderDTO.getLkCountry())
                .rawEventData(rawEvent)
                .eventData(packingSlipContract)
                .clientEventId(String.valueOf(clientEventId))
                .source(CES)
                .eventType(RETURN_PACKING_SLIP)
                .createdBy(CES)
                .build();
        log.info("Finance Event Dto for Packing Slip:{}",financeEventDto);
        financeService.saveEvent(financeEventDto);
    }

    private void saveReturnTrackingEvent(Integer returnId, Integer uwItemId, FinanceReturnEvent eventType, SyncStatus status, String statusRemarks, Integer retryCount) {
        FinanceSyncReturnTrackingEvent returnTrackingEvent = FinanceSyncReturnTrackingEvent.builder()
                .returnId(returnId)
                .uwItemId(uwItemId)
                .identifierType(eventType.name())
                .status(status.name())
                .statusRemarks(statusRemarks)
                .retryCount(retryCount)
                .build();
        financeSyncReturnTrackingRepository.save(returnTrackingEvent);
    }

    private boolean isB2BPhysicalItem(UwOrderDTO uwOrder) {
        List<String> hubMasterFacilityCodes = inventoryDataHelper.getHubMasterFacilityCodes();
        try {
            if(Objects.nonNull(uwOrder) && StringUtils.isNotBlank(uwOrder.getFacilityCode())){
                return hubMasterFacilityCodes.stream().anyMatch(uwOrder.getFacilityCode()::equalsIgnoreCase);
            }
        }
        catch (Exception e){
            log.error("[isB2BPhysicalItem] exception: {}", e.getMessage());
        }
        return false;
    }

}
