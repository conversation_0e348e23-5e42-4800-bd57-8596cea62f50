package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.ItemWiseFastRefunResponseDTO;
import com.lenskart.ordermetadata.dto.ItemWiseGetAmountDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.response.ExchangeDetailsHistory;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.RefundMethod;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.enums.ReturnTimeLines;
import com.lenskart.returncommon.model.enums.TriggerPoint;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.entity.ReturnHeadingSubheadingMapping;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnStatusTimelineMapping;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.cache.ReturnStatusTimelineMappingCache;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class ReturnDetailsEnrichServiceImpl implements IReturnDetailsEnrichService {

    @Autowired
    IReturnRefundRuleService returnRefundRuleService;
    @Autowired
    ReturnStatusTimelineMappingCache returnStatusTimelineMappingCache;
    @Autowired
    ReturnEventRepository returnEventRepository;
    @Autowired
    IReturnOrderActionService returnOrderActionService;

    @Autowired
    OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReturnHeadingSubheadingMappingCacheService returnHeadingSubheadingMappingCacheService;

    @Autowired
    IReturnSubHeaderTimelineDateFetchService returnSubHeaderTimelineDateFetchService;

    @Value("${exchange.eligible.period:0}")
    private Integer exchangeEligibilityPeriodValue;

    private final String ANY_RECEIVING_POINT = "AnyReceivingPoint";

    private final String COURIER_PICKUP = "CourierPickup";
    private final String AWB_ASSIGNED = "awb_assigned";
    private final String RETURN_RECEIVED = "return_received";
    private final String POS_RECEIVING = "POSReceiving";
    private final String WH_RECEIVING = "WHReceiving";
    private static final List<String> PROCESSING_STATE_LIST = new ArrayList<String>(Arrays.asList("new", "pending_payment", "holded", "processing"));
    private static final List<String> DISPATHED_STATE = new ArrayList<String>(Arrays.asList("complete", "closed"));

    @Override
    public void enrichReturnDetails(ReturnDetail returnDetail, ReturnDetailsResponse returnDetailsResponse, OrderInfoResponseDTO orderInfoResponseDTO) {
        log.info("[enrichReturnDetails] returnId {}", returnDetail.getId());
        updateAmountToRefund(returnDetailsResponse, orderInfoResponseDTO);
        updateExchangeDispatchPoint(returnDetailsResponse);
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = null;
        try{
            if(returnDetailsResponse.getExchangeDetails() != null){
                purchaseOrderDetailsDTO = orderOpsFeignClient.getPurchaseOrderDetails(IdentifierType.ORDER_ID.name(), returnDetailsResponse.getExchangeDetails().getExchangeChildId()).getBody();
            }
        }catch (Exception exception){
            log.error("[enrichReturnDetails] returnId " + returnDetail.getId() +" ,failed in the getPurchaseOrderDetails api : "+exception);
        }
        updateRefundDispatchPoint(returnDetailsResponse);
        updateReturnHeadings(returnDetail, returnDetailsResponse, purchaseOrderDetailsDTO); //left
        if (!(ReturnStatus.CANCELLED.getStatus().equalsIgnoreCase(returnDetailsResponse.getReturnStatus()) ||
                ReturnStatus.CUSTOMER_CANCELLED.getStatus().equalsIgnoreCase(returnDetailsResponse.getReturnStatus()) ||
                ReturnStatus.RETURN_REJECTED.getStatus().equalsIgnoreCase(returnDetailsResponse.getReturnStatus()) ||
                ReturnStatus.RETURN_REFUND_REJECTED.getStatus().equalsIgnoreCase(returnDetailsResponse.getReturnStatus()))) {
            updateReturnTimeLines(returnDetail, returnDetailsResponse);
            updateRefundTimeLines(returnDetail, returnDetailsResponse);
            updateExchangeTimeLines(returnDetail, returnDetailsResponse, purchaseOrderDetailsDTO);
            updateDatesInTimeLinesAndHeadings(returnDetail, returnDetailsResponse);
        }
    }

    private void updateDatesInTimeLinesAndHeadings(ReturnDetail returnDetail, ReturnDetailsResponse returnDetailsResponse) {
        returnSubHeaderTimelineDateFetchService.getDateInMilliseconds(returnDetailsResponse);
    }

    private void updateReturnHeadings(ReturnDetail returnDetail, ReturnDetailsResponse returnDetailsResponse, PurchaseOrderDetailsDTO orderInfoResponseDTO) {
        try {
            ReturnHeadingSubheadingMapping returnHeadingSubheadingMapping = new ReturnHeadingSubheadingMapping();
            returnHeadingSubheadingMapping.setReturnSource(returnDetail.getSource());
            returnHeadingSubheadingMapping.setReturnStatus(returnOrderActionService.getReturnOrderStatus(returnDetail));
            if (null != returnDetailsResponse.getExchangeDetails() || RefundMethod.EXCHANGE.getName().equalsIgnoreCase(returnDetailsResponse.getRefundMethodRequest())) {
                String dispatchPoint = returnDetailsResponse.getExchangeDispatchPoint();
                if (StringUtils.isNotBlank(returnDetailsResponse.getExchangeDispatchPoint()) || !"NA".equalsIgnoreCase(returnDetailsResponse.getExchangeDispatchPoint())) {
                    dispatchPoint = Objects.equals(returnDetailsResponse.getExchangeDispatchPoint(), ANY_RECEIVING_POINT) ? ANY_RECEIVING_POINT : "Receiving";
                }
                returnHeadingSubheadingMapping.setDispatchPoint(dispatchPoint);
                returnHeadingSubheadingMapping.setRefundMethod(RefundMethod.EXCHANGE.getName());
                returnHeadingSubheadingMapping.setRefundMode("na");
                returnHeadingSubheadingMapping.setExchangeExpired(checkExchangeExpired(returnDetailsResponse.getMagentoItemId(), returnDetailsResponse.getRefundIntentCreatedAt()));
                if (null != returnDetailsResponse.getExchangeDetails()) {
                    if (orderInfoResponseDTO != null && !CollectionUtils.isEmpty(orderInfoResponseDTO.getUwOrders())) {
                        UwOrderDTO uwOrder = orderInfoResponseDTO.getUwOrders().get(0);
                        if (Constant.ORDER_STATE_OR_STATUS.CANCELED.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.PREDELIVERY_REFUND.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.PREDELIVERY_CANCEL.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.ORDER_NOT_CONFIRMED.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.ORDER_CANCELED.equalsIgnoreCase(uwOrder.getShipmentStatus())) {
                            ReturnStatusHeadingDetail returnStatusHeadingDetail = new ReturnStatusHeadingDetail();
                            returnStatusHeadingDetail.setHeading("Exchange Order Cancelled");
                            List<String> subHeadingList = new ArrayList<>();
                            returnStatusHeadingDetail.setSubHeading(subHeadingList);
                            returnDetailsResponse.setReturnStatusHeading(returnStatusHeadingDetail);
                            return;
                        }
                    }
                    returnHeadingSubheadingMapping.setExchangeCreated(true);
                    returnHeadingSubheadingMapping.setExchangeExpired(false);
                    if (!CollectionUtils.isEmpty(returnDetailsResponse.getExchangeDetails().getExchangeDetailsHistories())) {
                        List<ExchangeDetailsHistory> exchangeDetailsHistoryList = returnDetailsResponse.getExchangeDetails().getExchangeDetailsHistories();
                        for (ExchangeDetailsHistory exchangeDetailsHistory : exchangeDetailsHistoryList) {
                            if (DISPATHED_STATE.contains(exchangeDetailsHistory.getExchangeState())) {
                                returnHeadingSubheadingMapping.setExchangeOrderDispatched(true);
                                break;
                            }
                        }
                    }
                }
            } else if (null != returnDetailsResponse.getRefundDetails() ||
                    RefundMethod.SOURCE.getName().equalsIgnoreCase(returnDetailsResponse.getRefundMethodRequest()) ||
                    RefundMethod.STORECREDIT.getName().equalsIgnoreCase(returnDetailsResponse.getRefundMethodRequest()) ||
                    RefundMethod.CASHFREE.getName().equalsIgnoreCase(returnDetailsResponse.getRefundMethodRequest())) {
                String dispatchPoint = returnDetailsResponse.getRefundDispatchPoint();
                if (StringUtils.isNotBlank(returnDetailsResponse.getRefundDispatchPoint()) || !"NA".equalsIgnoreCase(returnDetailsResponse.getRefundDispatchPoint())) {
                    dispatchPoint = returnDetailsResponse.getRefundDispatchPoint() == ANY_RECEIVING_POINT ? ANY_RECEIVING_POINT : "Receiving";
                }
                returnHeadingSubheadingMapping.setRefundMethod("refund");
                returnHeadingSubheadingMapping.setDispatchPoint(dispatchPoint);
                returnHeadingSubheadingMapping.setRefundMode(returnDetailsResponse.getRefundMethodRequest() == null ? "na" : returnDetailsResponse.getRefundMethodRequest());
                boolean returnHeadingSubheadingMappingRefundStatusSet = false;
                for (RefundDetails refund1 : returnDetailsResponse.getRefundDetails()) {
                    if (!Constant.REFUND_STATUS.REFUND_COMPLETE.equalsIgnoreCase(refund1.getStatus())) {
                        returnHeadingSubheadingMapping.setRefundMode(Constant.REFUND_METHOD.getOnlineRefundMethods().contains(refund1.getRefundMethod()) ?
                                Constant.REFUND_METHOD.SOURCE : refund1.getRefundMethod());
                        returnHeadingSubheadingMapping.setRefundStatus(refund1.getStatus());
                        returnHeadingSubheadingMappingRefundStatusSet = true;
                    }
                }
                if (!returnHeadingSubheadingMappingRefundStatusSet && !CollectionUtils.isEmpty(returnDetailsResponse.getRefundDetails())) {
                    returnHeadingSubheadingMapping.setRefundMode(Constant.REFUND_METHOD.getOnlineRefundMethods().contains(returnDetailsResponse.getRefundDetails().get(0).getRefundMethod()) ?
                            Constant.REFUND_METHOD.SOURCE : returnDetailsResponse.getRefundDetails().get(0).getRefundMethod());
                    returnHeadingSubheadingMapping.setRefundStatus(returnDetailsResponse.getRefundDetails().get(0).getStatus());
                }
                //returnHeadingSubheadingMapping.setRefundStatus("na");

            }
            log.info("[updateReturnHeadings] returnHeadingMapping : {}", returnHeadingSubheadingMapping);
            ReturnHeadingSubheadingMapping returnHeadingSubheadingMappingResponse =  getReturnHeadingSubheadingMapping(returnHeadingSubheadingMapping);
            log.info("[updateReturnHeadings] returnHeadingSubheadingMapping : {}", returnHeadingSubheadingMappingResponse);
            if (null != returnHeadingSubheadingMappingResponse) {
                ReturnStatusHeadingDetail returnStatusHeadingDetail = new ReturnStatusHeadingDetail();
                returnStatusHeadingDetail.setHeading(returnHeadingSubheadingMappingResponse.getTopHeader());
                List<String> subHeadingList = Arrays.asList(returnHeadingSubheadingMappingResponse.getTopSubHeader().split(":"));
                returnStatusHeadingDetail.setSubHeading(subHeadingList);
                returnDetailsResponse.setReturnStatusHeading(returnStatusHeadingDetail);
            }
        } catch (Exception e) {
            log.error("Exception while updating return headings ", e);
        }
    }

    public ReturnHeadingSubheadingMapping getReturnHeadingSubheadingMapping(ReturnHeadingSubheadingMapping returnHeadingSubheadingMapping) {
        log.info("[ReturnDetailsEnrichServiceImpl][getReturnHeadingSubheadingMapping] returnHeadingSubheadingMapping : {}", returnHeadingSubheadingMapping);
        return returnHeadingSubheadingMappingCacheService.getMapping(
                returnHeadingSubheadingMapping.getReturnSource(),
                returnHeadingSubheadingMapping.getReturnStatus(),
                returnHeadingSubheadingMapping.getDispatchPoint(),
                returnHeadingSubheadingMapping.getRefundMethod(),
                returnHeadingSubheadingMapping.getRefundMode(),
                returnHeadingSubheadingMapping.getExchangeExpired(),
                returnHeadingSubheadingMapping.getExchangeCreated(),
                returnHeadingSubheadingMapping.getExchangeOrderDispatched(),
                returnHeadingSubheadingMapping.getRefundStatus()
        );

    }

    private boolean checkExchangeExpired(Long magentoItemId, Date refundIntentCreatedAt) {
        try {
            if(exchangeEligibilityPeriodValue.equals(0) || refundIntentCreatedAt == null) {
                log.info(("[ReturnServiceImpl] Skip :: checkExchangeExpired for the magentoItemId "+ magentoItemId));
                return false;
            }

            Calendar c = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            c.setTime(refundIntentCreatedAt);
            c.add(Calendar.DATE, exchangeEligibilityPeriodValue);
            Date currentDate = new Date();

            String currentDateString = sdf.format(currentDate);
            String eligibilityEndDateString = sdf.format(c.getTime());

            boolean isExchangeExpired = currentDateString.compareTo(eligibilityEndDateString) > 0;

            log.info(("[ReturnServiceImpl] checkExchangeExpired for the magentoItemId "+ magentoItemId +" exchangeEligiblePeriodDate :: " + eligibilityEndDateString + " exchangeEligibilityPeriodValue :: " + exchangeEligibilityPeriodValue + " isExchangeExpired ::" + isExchangeExpired));
            return isExchangeExpired;
        } catch (Exception e) {
            log.error(("[ReturnServiceImpl] Failed at checkExchangeExpired for the magentoItemId "+ magentoItemId +" error :: " + e.getMessage() + " exchangeEligibilityPeriodValue :: " + exchangeEligibilityPeriodValue));
            return false;
        }
    }


    private void updateExchangeTimeLines(ReturnDetail returnDetail, ReturnDetailsResponse response, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO) {
        try {
            if (null != returnDetail) {
                if (null != response.getExchangeDetails() || (StringUtils.isNotBlank(response.getRefundMethodRequest()) &&
                        Constant.REFUND_METHOD.EXCHANGE.equalsIgnoreCase(response.getRefundMethodRequest()))) {
                    if (null != response.getExchangeDetails() && purchaseOrderDetailsDTO != null && !CollectionUtils.isEmpty(purchaseOrderDetailsDTO.getUwOrders())) {
                        UwOrderDTO uwOrder = purchaseOrderDetailsDTO.getUwOrders().get(0);
                        if (Constant.ORDER_STATE_OR_STATUS.CANCELED.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.PREDELIVERY_REFUND.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.PREDELIVERY_CANCEL.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.ORDER_NOT_CONFIRMED.equalsIgnoreCase(uwOrder.getShipmentStatus()) ||
                                Constant.ORDER_STATE_OR_STATUS.ORDER_CANCELED.equalsIgnoreCase(uwOrder.getShipmentStatus())) {
                            List<ExchangeTimeLine> exchangeTimeLines = new ArrayList<>();
                            ExchangeTimeLine exchangeTimeLine = new ExchangeTimeLine();
                            exchangeTimeLine.setExchangeStatus("exchange_cancelled");
                            exchangeTimeLine.setExchangeSubStatus("Exchange Order is Cancelled");
                            exchangeTimeLines.add(exchangeTimeLine);
                            response.setExchangeTimelines(exchangeTimeLines);
                            return;
                        }
                    }
                    List<ExchangeTimeLine> exchangeTimeLines = new ArrayList<>();
                    ExchangeTimeLine exchangeTimeLine = new ExchangeTimeLine();
                    exchangeTimeLine.setExchangeStatus("exchange_created");
                    exchangeTimeLine.setExchangeSubStatus("exchange_created");
                    ReturnStatusTimelineMapping returnStatusTimelineMapping = null;
                    // if(null != response.getExchangeDetails()) {
                    returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus("exchange_created");
                    if (null != returnStatusTimelineMapping) {
                        exchangeTimeLine.setExchangeSubStatus(returnStatusTimelineMapping.getTimelineSubheaderPredective());
                    }
                    // }
                    exchangeTimeLines.add(exchangeTimeLine);
                    exchangeTimeLine = new ExchangeTimeLine();
                    exchangeTimeLine.setExchangeStatus("exchange_dispatched");
                    exchangeTimeLine.setExchangeSubStatus("exchange_dispatched");
                    // if(null != response.getExchangeDetails()) {
                    returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus("exchange_dispatched");
                    if (null != returnStatusTimelineMapping) {
                        exchangeTimeLine.setExchangeSubStatus(returnStatusTimelineMapping.getTimelineSubheaderPredective());
                    }
                    //}
                    exchangeTimeLines.add(exchangeTimeLine);
                    response.setExchangeTimelines(exchangeTimeLines);
                }
            }
        } catch (Exception e) {
            log.error("Exception while updating exchange timelines ", e);
        }
    }

    private void updateExchangeDispatchPoint(ReturnDetailsResponse response) {
        String dispatchPoint = returnRefundRuleService.getRuleEngineResponseForDispatch(response.getUwItemId(), response.getReturnId());
        response.setExchangeDispatchPoint(dispatchPoint);
    }

    @Override
    public void updateAmountToRefund(ReturnDetailsResponse response, OrderInfoResponseDTO orderInfoResponseDTO) {
        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponse = orderInfoResponseDTO.getItemWiseFastRefunResponseDTO();
        if (null != itemWiseFastRefunResponse && itemWiseFastRefunResponse.getItemIds() != null) {
            for (ItemWiseGetAmountDTO itemWiseGetAmount : itemWiseFastRefunResponse.getItemIds()) {
                if (itemWiseGetAmount.getUwItemId().equals(response.getUwItemId())) {
                    double amountTorefund = itemWiseGetAmount.getActualAmount().doubleValue() - itemWiseGetAmount.getRefundAmount().doubleValue();
                    double itemRefundedAmount = itemWiseGetAmount.getRefundAmount().doubleValue();
                    log.info("refunded amount for uwItemId " + response.getUwItemId() + " is " + itemWiseGetAmount.getRefundAmount()
                            + " itemPrice is " + itemWiseGetAmount.getActualAmount() + " and amount to refund is " + amountTorefund);
                    response.setAmountToRefund(amountTorefund);
                }
            }
        }
    }

    private void updateRefundDispatchPoint(ReturnDetailsResponse response) {
        String dispatchPoint = returnRefundRuleService.getRuleEngineResponseForRefundDispatch(response.getUwItemId(), response.getReturnId());
        List<ReturnEvent> returnEvents = null;
        try {
            if (ANY_RECEIVING_POINT.equalsIgnoreCase(dispatchPoint)) {
                dispatchPoint = response.getReturnMethod().equalsIgnoreCase(Constant.RETURN_METHOD.RPU)
                        ? ReturnStatus.AWB_ASSIGNED.getStatus() : ReturnStatus.RETURN_RECEIVED.getStatus();
                returnEvents = returnEventRepository.findByReturnIdOrderByIdDesc(response.getReturnId());
                if (returnEvents != null) {
                    for (ReturnEvent returnEvent : returnEvents) {
                        if (ReturnStatus.AWB_ASSIGNED.getStatus().equals(returnEvent.getEvent())) {
                            dispatchPoint = ReturnStatus.AWB_ASSIGNED.getStatus();
                            break;
                        }
                    }
                }
            } else if (TriggerPoint.ReturnInitiation.getName().equalsIgnoreCase(dispatchPoint)) {
                dispatchPoint = ReturnStatus.RETURN_RECEIVED.getStatus();
                returnEvents = returnEventRepository.findByReturnIdOrderByIdDesc(response.getReturnId());
                if (returnEvents != null) {
                    for (ReturnEvent returnEvent : returnEvents) {
                        if (ReturnStatus.NEW_REVERSE_PICKUP.getStatus().equals(returnEvent.getEvent())) {
                            dispatchPoint = ANY_RECEIVING_POINT;
                            break;
                        }
                    }
                }
            } else {
                if (COURIER_PICKUP.equalsIgnoreCase(dispatchPoint)) {
                    dispatchPoint = ReturnStatus.AWB_ASSIGNED.getStatus();
                } else if (POS_RECEIVING.equalsIgnoreCase(dispatchPoint) || WH_RECEIVING.equalsIgnoreCase(dispatchPoint)) {
                    dispatchPoint = ReturnStatus.RETURN_RECEIVED.getStatus();
                }
            }
        } catch (Exception e) {
            log.error("Caught error while fetching refund dispath point ", e);
        }
        response.setRefundDispatchPoint(dispatchPoint);
    }

    @Override
    public void updateRefundTimeLines(ReturnDetail returnDetail, ReturnDetailsResponse response) {
        log.info("[updateRefundTimeLines]");
        try {
            if (null != returnDetail) {
                if (!CollectionUtils.isEmpty(response.getRefundDetails()) || StringUtils.isNotBlank(response.getRefundMethodRequest())) {
                    boolean refundPendingExist = false;
                    if (!CollectionUtils.isEmpty(response.getRefundDetails()) || StringUtils.isNotBlank(response.getRefundMethodRequest())) {
                        List<RefundTimeLine> refundTimeLineList = new ArrayList<>();
                        RefundTimeLine refundTimeLine = new RefundTimeLine();
                        if (!CollectionUtils.isEmpty(response.getRefundDetails())) {
                            for (RefundDetails refundDetails : response.getRefundDetails()) {
                                refundPendingExist = false;
                                if (!CollectionUtils.isEmpty(refundDetails.getRefundStatusHistoryList())) {
                                    for (RefundStatusHistory refundStatusHistory : refundDetails.getRefundStatusHistoryList()) {
                                        if (Constant.REFUND_STATUS.REFUND_COMPLETE.equalsIgnoreCase(refundStatusHistory.getRefundStatus()) || Constant.REFUND_STATUS.REFUND_ARN_RECEIVED.equalsIgnoreCase(refundStatusHistory.getRefundStatus())) {
                                            refundPendingExist = true;
                                            break;
                                        }
                                    }
                                }
                                if (!refundPendingExist) {
                                    refundTimeLine = new RefundTimeLine();
                                    refundTimeLine.setRefundStatus(Constant.REFUND_STATUS.REFUND_PENDING);
                                    String status = Constant.REFUND_STATUS.REFUND_PENDING;
                                    refundTimeLine.setRefundSubStatus(status);
                                    if ((StringUtils.isNotBlank(response.getRefundMethodRequest()) && Constant.REFUND_METHOD.CASHFREE.equalsIgnoreCase(response.getRefundMethodRequest()))
                                            || (Constant.REFUND_METHOD.CASHFREE.equalsIgnoreCase(refundDetails.getRefundMethod()))) {
                                        status = "cashfree_refund_pending";
                                    }
                                    ReturnStatusTimelineMapping returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus(status);
                                    if (null != returnStatusTimelineMapping) {
                                        refundTimeLine.setRefundSubStatus(returnStatusTimelineMapping.getTimelineSubheaderPredective());
                                        refundTimeLine.setRefundStatus(returnStatusTimelineMapping.getCustomerFacingStatus());
                                    }
                                    refundTimeLineList.add(refundTimeLine);
                                }
                                if (!refundPendingExist) {
                                    refundTimeLine = new RefundTimeLine();
                                    refundTimeLine.setRefundStatus(Constant.REFUND_STATUS.REFUND_COMPLETE);
                                    String status = Constant.REFUND_STATUS.REFUND_COMPLETE;
                                    if (!CollectionUtils.isEmpty(response.getRefundDetails())) {
                                        if (Constant.REFUND_METHOD.CASHFREE.equalsIgnoreCase(refundDetails.getRefundMethod())) {
                                            status = "refund_complete_cashfree";
                                        } else if (Constant.REFUND_METHOD.STORECREDIT.equalsIgnoreCase(refundDetails.getRefundMethod())) {
                                            status = "refund_complete_sc";
                                        }
                                    }
                                    ReturnStatusTimelineMapping returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus(status);
                                    if (null != returnStatusTimelineMapping) {
                                        refundTimeLine.setRefundSubStatus(returnStatusTimelineMapping.getTimelineSubheaderPredective());
                                        refundTimeLine.setRefundStatus(returnStatusTimelineMapping.getCustomerFacingStatus());
                                    }
                                    refundTimeLineList.add(refundTimeLine);
                                }
                            }
                        } else {
                            refundTimeLine = new RefundTimeLine();
                            refundTimeLine.setRefundStatus(Constant.REFUND_STATUS.REFUND_PENDING);
                            String status = Constant.REFUND_STATUS.REFUND_PENDING;
                            refundTimeLine.setRefundSubStatus(status);
                            if (StringUtils.isNotBlank(response.getRefundMethodRequest()) && Constant.REFUND_METHOD.CASHFREE.equalsIgnoreCase(response.getRefundMethodRequest())) {
                                status = "cashfree_refund_pending";
                            }
                            ReturnStatusTimelineMapping returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus(status);
                            if (null != returnStatusTimelineMapping) {
                                refundTimeLine.setRefundSubStatus(returnStatusTimelineMapping.getTimelineSubheaderPredective());
                                refundTimeLine.setRefundStatus(returnStatusTimelineMapping.getCustomerFacingStatus());
                            }
                            refundTimeLineList.add(refundTimeLine);
                            refundTimeLine = new RefundTimeLine();
                            refundTimeLine.setRefundStatus(Constant.REFUND_STATUS.REFUND_COMPLETE);

                            status = Constant.REFUND_STATUS.REFUND_COMPLETE;
                            if (StringUtils.isNotBlank(response.getRefundMethodRequest()) && Constant.REFUND_METHOD.CASHFREE.equalsIgnoreCase(response.getRefundMethodRequest())) {
                                status = "refund_complete_cashfree";
                            } else if (StringUtils.isNotBlank(response.getRefundMethodRequest()) && Constant.REFUND_METHOD.STORECREDIT.equalsIgnoreCase(response.getRefundMethodRequest())) {
                                status = "refund_complete_sc";
                            }
                            returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus(status);
                            if (null != returnStatusTimelineMapping) {
                                refundTimeLine.setRefundSubStatus(returnStatusTimelineMapping.getTimelineSubheaderPredective());
                                refundTimeLine.setRefundStatus(returnStatusTimelineMapping.getCustomerFacingStatus());
                            }
                            refundTimeLineList.add(refundTimeLine);

                        }
                        response.setRefundTimeLines(refundTimeLineList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Excepion while updating refund timelines ", e);
        }
    }

    private void updateReturnTimeLines(ReturnDetail returnOrder, ReturnDetailsResponse response) {
        try {
            if (null != returnOrder) {
                String returnMethod = returnOrder.getReturnMethod();
                String returnOrderStatus = returnOrderActionService.getReturnOrderStatus(returnOrder);
                if (ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus().equalsIgnoreCase(returnOrderStatus)
                        || ReturnStatus.RETURN_NEED_APPROVAL.getStatus().equalsIgnoreCase(returnOrderStatus)
                        || ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus().equalsIgnoreCase(returnOrderStatus)) {
                    returnMethod = returnMethod + "_" + ReturnStatus.getEnumNameByStatus(returnOrderStatus);
                }
                log.info("[returnMethod] returnId : {}, returnMethod : {}", returnOrder.getId(), returnMethod);
                ReturnTimeLines returnTimeLines = ReturnTimeLines.getReturnMethod(returnMethod);
                log.info("[returnMethod] returnId : {}, returnMethod : {}, returnTimeLines : {} ", returnOrder.getId(), returnMethod, returnTimeLines);
                if (null != returnTimeLines) {
                    log.info("[returnMethod] returnId : {}, returnTimeLinesStatusList : {} ", returnOrder.getId(), returnTimeLines.getStatusList());
                    List<ReturnTimeLine> returnTimeLineList = new ArrayList<>();
                    for (String status : returnTimeLines.getStatusList()) {
                        log.info("[returnMethod] returnId : {}, status : {}", returnOrder.getId(), status);
                        ReturnTimeLine returnTimeLine = new ReturnTimeLine();
                        returnTimeLine.setReturnStatus(status);
                        ReturnStatusTimelineMapping returnStatusTimelineMapping = returnStatusTimelineMappingCache.getReturnStatusTimlineByReturnStatus(status);
                        if (null != returnStatusTimelineMapping) {
                            returnTimeLine.setReturnSubStatus(returnStatusTimelineMapping.getTimelineSubheaderPredective());
                        }
                        returnTimeLineList.add(returnTimeLine);
                    }
                    response.setReturnTimeLines(returnTimeLineList);
                }
            }
        } catch (Exception e) {
            log.error("Exception while updating return timeline ", e);
        }
    }
}
