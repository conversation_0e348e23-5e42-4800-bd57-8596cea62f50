package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.response.RefundRequestResponseDTO;
import com.lenskart.returncommon.model.dto.RefundRequestInputDTO;

public interface IRefundRequestService {
    RefundRequestResponseDTO initiateRefund(Integer returnRequestId, ReturnCreationRequestDTO returnCreationRequest, ReturnItemDTO item, UwOrderDTO uwOrder, Integer returnId, boolean isReturnCreatedNow, String returnInitiatedFrom, Integer virtualB2BUwItemId);
    RefundRequestResponseDTO createRefundRequest(RefundRequestInputDTO refundRequestInputDTO);
}
