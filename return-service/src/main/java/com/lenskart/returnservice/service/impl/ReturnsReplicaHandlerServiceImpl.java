package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.lenskart.ordermetadata.dto.ReturnOrderItemExtraInfoDTO;
import com.lenskart.returncommon.utils.UnixTimestampToDateDeserializer;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReturnsReplicaHandlerService;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ReturnsReplicaHandlerServiceImpl implements IReturnsReplicaHandlerService {


    private ObjectMapper mapperOnlyForDebezium = new ObjectMapper()
            .registerModule(new SimpleModule().addDeserializer(Date.class, new UnixTimestampToDateDeserializer()))
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Override
    public void handleReturnsReplica(JSONObject payload) {
        log.info("[handleReturnsReplica] payload : {}", payload);
        if(payload != null){
            Map<String, Object> payloadMap = payload.toMap();
            if(payloadMap.containsKey("order_no")){
                ReturnOrder returnOrder = getReturnOrder(payloadMap);
                returnOrderActionService.saveReturnOrder(returnOrder);
            }
            if(payloadMap.containsKey("qc_fail_reason")){
                ReturnOrderItem returnOrderItem = getReturnOrderItem(payloadMap);
                returnOrderActionService.saveReturnOrderItem(returnOrderItem);
            }
        }
    }

    private Map<String, Object> getEntityMap(JSONObject payload){
        Map<String,Object> entityMap = null;
        JSONObject payloadJson = payload.getJSONObject("payload");
        if(payloadJson != null && payloadJson.has("after")) {
            JSONObject afterJsonMap = payloadJson.getJSONObject("after");
            entityMap = afterJsonMap.toMap();
        }
        return entityMap;
    }


    @Override
    public void handleReturnOrderReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null){
            ReturnOrder returnOrder = getReturnOrder(entityMap);
            try {
                log.info("handleReturnOrderReplica : {} - json : {}", returnOrder.getReturnCreateDatetime(), mapperOnlyForDebezium.writeValueAsString(returnOrder));
            } catch (JsonProcessingException e) {

            }
            returnOrderActionService.saveReturnOrder(returnOrder);
        }
    }

    @Override
    public void handleReturnOrderItemReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null){
            ReturnOrderItem returnOrderItem = getReturnOrderItem(entityMap);
            returnOrderActionService.saveReturnOrderItem(returnOrderItem);
            updateExtraInfo(returnOrderItem);
            returnOrderActionService.saveReturnOrderItem(returnOrderItem);
        }
    }

    @Transactional
    @Override
    public boolean processAndSaveReturnOrderItems(int lastProcessedId) {
        int BATCH_SIZE = 1000;

        while (true) {
            log.info("Fetching batch of size {} starting from ID {}", BATCH_SIZE, lastProcessedId);
            List<ReturnOrderItem> batch = returnOrderActionService.findReturnOrderItemBatch(lastProcessedId, BATCH_SIZE);
            if (batch.isEmpty()) {
                log.info("No more records to process. Exiting loop.");
                break;
            }

            batch.removeIf(item -> item.getReturnId() >= 41801320);
            if (batch.isEmpty()) {
                log.info("All items in batch filtered out. Exiting loop.");
                break;
            }

            batch.forEach(this::updateExtraInfo); // Modify fields as needed

            returnOrderActionService.saveReturnOrderItemAll(batch);
            log.info("Saved {} updated records to database", batch.size());

            lastProcessedId = batch.get(batch.size() - 1).getId();
        }
        log.info("processAndSaveReturnOrderItems completed");
        return true;
    }

    private ReturnOrderItem updateExtraInfo(ReturnOrderItem returnOrderItem) {
        ResponseEntity<ReturnOrderItemExtraInfoDTO> feignClientExtraInfoDetails = orderOpsFeignClient.getExtraInfoDetails(returnOrderItem.getUwItemId());
        if(feignClientExtraInfoDetails.getStatusCode().is2xxSuccessful() && feignClientExtraInfoDetails.getBody() != null){
            ReturnOrderItemExtraInfoDTO returnOrderItemExtraInfoDTO = feignClientExtraInfoDetails.getBody();
            returnOrderItem.setProductDeliveryType(returnOrderItemExtraInfoDTO.getProductDeliveryType());
            returnOrderItem.setChannel(returnOrderItemExtraInfoDTO.getChannel());
            returnOrderItem.setMethod(returnOrderItemExtraInfoDTO.getMethod());
            returnOrderItem.setClassification(returnOrderItemExtraInfoDTO.getClassification());
            returnOrderItem.setIsFranchise(returnOrderItemExtraInfoDTO.getIsFranchise());
        }
        return returnOrderItem;
    }

    @Override
    public void handleReturnReasonReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null) {
            ReturnReason returnReason = getReturnReason(entityMap);
            returnOrderActionService.saveReturnReason(returnReason);
        }
    }

    @Override
    public void handleReturnOrderAddressUpdateReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null) {
            ReturnOrderAddressUpdate returnOrderAddressUpdate = getReturnOrderAddressUpdate(entityMap);
            returnOrderActionService.saveReturnOrderAddressUpdate(returnOrderAddressUpdate);
        }
    }

    @Override
    public void handleReturnHistoryReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null) {
            ReturnHistory returnHistory = getReturnHistory(entityMap);
            returnOrderActionService.saveReturnHistory(returnHistory);
        }
    }

    @Override
    public void handleRefundRulesReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null) {
            RefundRules refundRules = getRefundRules(entityMap);
            returnOrderActionService.saveRefundRules(refundRules);
        }
    }

    private RefundRules getRefundRules(Map<String, Object> payloadMap) {
        RefundRules refundRules = mapperOnlyForDebezium.convertValue(payloadMap, RefundRules.class);
        log.info("getRefundRules : {}", refundRules);
        return refundRules;
    }

    @Override
    public void handleReversePickupPincodeReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null) {
            ReversePickupPincode reversePickupPincode = getReversePickupPincode(entityMap);
            returnOrderActionService.saveReversePickupPincode(reversePickupPincode);
        }
    }

    @Override
    public void handleReverseCourierMappingReplica(JSONObject payload) {
        Map<String, Object> entityMap = getEntityMap(payload);
        if(entityMap != null) {
            ReverseCourierMapping reverseCourierMapping = getReverseCourierMapping(entityMap);
            returnOrderActionService.saveReverseCourierMapping(reverseCourierMapping);
        }
    }

    private ReverseCourierMapping getReverseCourierMapping(Map<String, Object> entityMap) {
       return mapperOnlyForDebezium.convertValue(entityMap, ReverseCourierMapping.class);
    }

    private ReversePickupPincode getReversePickupPincode(Map<String, Object> entityMap) {
        return mapperOnlyForDebezium.convertValue(entityMap, ReversePickupPincode.class);
    }


    private ReturnOrderItem getReturnOrderItem(Map<String, Object> payloadMap) {
        return mapperOnlyForDebezium.convertValue(payloadMap, ReturnOrderItem.class);
    }

    private ReturnOrder getReturnOrder(Map<String, Object> payloadMap) {
        return mapperOnlyForDebezium.convertValue(payloadMap, ReturnOrder.class);
    }

    private ReturnReason getReturnReason(Map<String, Object> payloadMap) {
        return mapperOnlyForDebezium.convertValue(payloadMap, ReturnReason.class);
    }

    private ReturnOrderAddressUpdate getReturnOrderAddressUpdate(Map<String, Object> payloadMap) {
        return mapperOnlyForDebezium.convertValue(payloadMap, ReturnOrderAddressUpdate.class);
    }

    private ReturnHistory getReturnHistory(Map<String, Object> payloadMap) {
        return mapperOnlyForDebezium.convertValue(payloadMap, ReturnHistory.class);
    }
}