package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.response.KafkaProducerResponse;
import com.lenskart.returnrepository.entity.KafkaHistory;
import com.lenskart.returnrepository.repository.KafkaHistoryRepository;
import com.lenskart.returnservice.service.IKafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class KafkaServiceImpl implements IKafkaService {
    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate<String, Object> kafkaProducerTemplate;

    @Autowired
    private KafkaHistoryRepository kafkaHistoryRepo;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public ResponseEntity<KafkaProducerResponse> pushToKafka(String topic, String partitionKey, Object event) {
        save(topic, partitionKey, event);
        KafkaProducerResponse kafkaProducerResponse = new KafkaProducerResponse();
        try {
            log.info("KafkaServiceImpl.pushToKafka topic: {} event: {}", topic, objectMapper.writeValueAsString(event));
        } catch (JsonProcessingException e) {
            log.error("KafkaServiceImpl.pushToKafka exception occurred while generating: ", e);
        }
        kafkaProducerTemplate.send(topic, partitionKey, event);
        kafkaProducerResponse.setSuccess(true);
        kafkaProducerResponse.setError(null);
        return new ResponseEntity<>(kafkaProducerResponse, HttpStatus.OK);
    }

    private KafkaHistory save(KafkaHistory consumerLog) {
        return kafkaHistoryRepo.save(consumerLog);
    }

    @Async
    public void save(String topic, String partitionKey, Object kafkaMessage) {
        try {
            String kafkaMessageJson = objectMapper.writeValueAsString(kafkaMessage);
            KafkaHistory kafkaHistory = KafkaHistory.builder()
                    .topic(topic)
                    .partitionKey(partitionKey)
                    .kakfaMessage(kafkaMessageJson)
                    .build();
            save(kafkaHistory);
        } catch (JsonProcessingException jsonProcessingException) {
            log.error("Exception in saving KafkaHistory {} {} {} {} ", topic, partitionKey, kafkaMessage, jsonProcessingException.getMessage(), jsonProcessingException);
            KafkaHistory kafkaHistory = KafkaHistory.builder()
                    .topic(topic)
                    .partitionKey(partitionKey)
                    .build();
            save(kafkaHistory);
        } catch (Exception e) {
            log.error("Exception in saving KafkaHistory {} {} {} {} ", topic, partitionKey, kafkaMessage, e.getMessage(), e);
        }
    }
}
