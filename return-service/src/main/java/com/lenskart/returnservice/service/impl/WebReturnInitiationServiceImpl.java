package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ccautils.util.CosmosEventPushUtil;
import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.ExchangeAddressDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnrepository.entity.ReverseCourierMapping;
import com.lenskart.returnrepository.entity.ReversePickupPincode;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.factory.cosmospayloadfactory.CosmosEventPayloadFactory;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lenskart.returncommon.utils.Constant.ADDRESS_TYPE.SHIPPING;
import static com.lenskart.returncommon.utils.Constant.CHANNEL.JJONLINE;
import static com.lenskart.returncommon.utils.Constant.REFUND_QUEUE_CONSTANTS.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.EXCHANGE_ORDER_CREATION_QUEUE;

@Service(value = "ReturnInitiationServiceImpl")
@Slf4j
public class WebReturnInitiationServiceImpl extends ReturnInitiationAbstractionService implements IReturnInitiationService {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private INexsFacilityService nexsFacilityService;

    @Autowired
    private ReturnRequestRepository returnRequestRepository;

    @Autowired
    private PrimaryReturnReasonsRepository primaryReturnReasonsRepository;

    @Autowired
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IReverseCourierDetailService reverseCourierDetailService;

    @Autowired
    private ReversePickupPincodeRepository reversePickupPincodeRepository;

    @Autowired
    private ReverseCourierMappingRepository reverseCourierMappingRepository;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;

    @Autowired
    private ReturnUtil returnUtil;

    @Autowired
    private IKafkaService kafkaService;
    @Autowired
    private IExchangeOrderService exchangeOrderService;
    @Autowired
    private ICommunicationService communicationService;
    @Autowired
    private CosmosEventPayloadFactory cosmosEventPayloadFactory;
    @Autowired
    private CosmosEventPushUtil cosmosEventPushUtil;

    @Autowired
    RedisTemplate<String, Object> redisTemplate;
    public static final String QC_RULE_COUNT = "qc_rule_count";


    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private QCWarrantyRuleEngineRepository qcWarrantyRuleEngineRepository;

    @Override
    public ReturnCreationResponse createReturn(ReturnCreationRequestDTO returnCreationRequest) throws Exception {

        Map<Long, ReturnItemDTO> magentoItemToReturnItemMap = new HashMap<>();
        Map<Long, String> returnIntentMap = new HashMap<>();
        List<ReturnItemDTO> exchangeItemList = new ArrayList<>();
        List<ReturnItemDTO> returnItemsUpdated = new ArrayList<>();
        List<UwOrderDTO> uwOrders = new ArrayList<>();
        Map<ReturnItemDTO, Integer> productIdsMap = new HashMap<>();
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = getOrderDetails(returnCreationRequest, magentoItemToReturnItemMap, returnIntentMap, exchangeItemList, returnItemsUpdated, uwOrders, productIdsMap);
        String identifierType = purchaseOrderDetailsDTO.getIdentifierType();
        String identifierValue = purchaseOrderDetailsDTO.getIdentifierValue();
        Integer incrementId = purchaseOrderDetailsDTO.getOrdersHeaderDTO().getIncrementId();

        ReturnRequest returnRequest = createReturnRequest(returnCreationRequest, magentoItemToReturnItemMap, returnIntentMap, identifierType, identifierValue, incrementId);

        enrichReturnCreationRequest(returnCreationRequest, productIdsMap, returnItemsUpdated, incrementId);

        ReverseCourierDetail reverseCourierDetail = getReverseCourierDetail(returnCreationRequest, purchaseOrderDetailsDTO, incrementId, returnRequest);

        ReturnCreationResponse returnCreationResponse = createReturn(purchaseOrderDetailsDTO, returnCreationRequest, reverseCourierDetail, returnRequest);

        Boolean isExchangeCreated = createExchangeOrder(returnCreationRequest, exchangeItemList, returnCreationResponse);
        //based on the response perform following steps

        initiateRefund(returnCreationResponse, incrementId, magentoItemToReturnItemMap, purchaseOrderDetailsDTO, returnRequest, returnCreationResponse.getReturnId());

        statusUpdate(purchaseOrderDetailsDTO.getUwOrders(), returnCreationRequest.getItems(), returnCreationResponse.getReturnId());

        triggerCommunication(exchangeItemList, returnCreationResponse.getReturnId(), returnCreationResponse.getResult().getGroupId(), purchaseOrderDetailsDTO.getUwOrders(), purchaseOrderDetailsDTO.getOrders(), returnCreationRequest, reverseCourierDetail, returnRequest.getId(), isExchangeCreated);

       //CompletableFuture.runAsync(() -> returnUtil.pushCosmosEvents(returnCreationResponse, magentoItemToReturnItemMap, returnCreationRequest));

        return getReturnCreationResponse(reverseCourierDetail, returnCreationResponse, incrementId, purchaseOrderDetailsDTO.getUwOrders());
    }

    @Override
    public ReverseCourierDetail assignReverseCourier(Integer orderId, Integer incrementId, Boolean isNewAddress, Integer pincodeInRequest, int offset, QCType qcType, boolean newQcFlow, OrderInfoResponseDTO orderInfoResponseDTO) throws Exception {

        ReverseCourierDetail courierAvailabilityResponse = new ReverseCourierDetail();
        courierAvailabilityResponse.setQcAtDoorStepEligibleByCourierDetails(false);
        ReversePickupPincode reversePickupPincode = null;
        try {
            if (orderId != null && orderId != 0) {
                Integer pincode = null;
                log.info("[ReturnInitiationServiceImpl][assignReverseCourierNew] Fetching pincode for the given orderId {}", orderId);
                if (isNewAddress) {
                    log.info("[ReturnInitiationServiceImpl][assignReverseCourierNew] New address has been provided");
                    pincode = pincodeInRequest;
                } else {
                    OrderAddressUpdateDTO orderAddressUpdateDTO = orderInfoResponseDTO.getOrderAddressUpdates().stream().filter(oa -> oa.getAddressType().equals(SHIPPING)).findFirst().orElse(null);
                    if (orderAddressUpdateDTO != null) {
                        pincode = Integer.valueOf(orderAddressUpdateDTO.getPostcode());
                    }
                }
                log.info("[ReturnInitiationServiceImpl][assignReverseCourierNew] Pincode for the given orderId {} is {}, offset:{}", orderId, pincode, offset);
                if (pincode != null) {
                    ReverseCourierMapping reverseCourierMapping = null;
                    log.info("[ReturnInitiationServiceImpl][assignReverseCourierNew] Looking up for non LKart couriers as the same isn't allowed");
                    if (qcType.getValue() == 0 || qcType.getValue() == 2) {
                        reversePickupPincode = reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAscNoQCCheck(pincode, Arrays.asList(Constant.COURIER.LKART), offset);
                    } else if (qcType.getValue() == 1) {
                        reversePickupPincode = reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAsc(pincode, Arrays.asList(Constant.COURIER.LKART), offset, 1);
                        if (Objects.isNull(reversePickupPincode)) {
                            reversePickupPincode = reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAscNoQCCheck(pincode, Arrays.asList(Constant.COURIER.LKART), offset);
                            if (newQcFlow) {
                                qcType = QCType.QC_OVERRIDDEN;
                            } else {
                                qcType = QCType.NON_QC;
                            }
                        }
                    }
                    if (null != reversePickupPincode) {
                        log.info("[ReturnInitiationServiceImpl][assignReverseCourierNew] allowDispensingTeamCourier courier details available for pincode {} are {} ", pincode, reversePickupPincode);
                        courierAvailabilityResponse.setPincode(reversePickupPincode.getPincode());
                        courierAvailabilityResponse.setCourier(reversePickupPincode.getCourier());
                        courierAvailabilityResponse.setStatus(true);
                        courierAvailabilityResponse.setTat(reversePickupPincode.getTat());

                        if (qcType.getValue() == 0 || qcType.getValue() == 2) {
                            courierAvailabilityResponse.setQcAtDoorStepEligibleByCourierDetails(false);
                        } else if (qcType.getValue() == 1) {
                            courierAvailabilityResponse.setQcAtDoorStepEligibleByCourierDetails(true);
                        }
                        courierAvailabilityResponse.setCourierAssignmentType(qcType);

                        reverseCourierMapping = reverseCourierMappingRepository.findByCourier(reversePickupPincode.getCourier());
                        log.info("[ReturnInitiationServiceImpl][assignReverseCourierNew] reverseCourierMapping : {}", reverseCourierMapping);
                        if (null != reverseCourierMapping) {
                            courierAvailabilityResponse.setCpId(reverseCourierMapping.getCpId());
                        } else {
                            courierAvailabilityResponse.setCpId(0);
                        }
                        return courierAvailabilityResponse;
                    }
                }
            }
        } catch (Exception e) {
            log.error("[ReturnInitiationServiceImpl][assignReverseCourierNew] Exception caught ", e);
            throw new Exception("Exception while assigning courier reverse pickup", e);
        }
        log.info("[ReturnInitiationServiceImpl][assignReverseCourierNew] incrementId:{}, offset:{}, reverseCourierDetail:{}", incrementId, offset, courierAvailabilityResponse);
        return courierAvailabilityResponse;
    }

    @Override
    public Integer getQcRuleCount() {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(QC_RULE_COUNT))) {
            log.info("[ReturnInitiationServiceImpl][getQcRuleCount] qc_warranty_rule_engine rules count fetched from cache");
            return (int) redisTemplate.opsForValue().get(QC_RULE_COUNT);
        }
        log.info("[ReturnInitiationServiceImpl][getQcRuleCount] qc_warranty_rule_engine rules count fetched from DB");
        Integer ruleCount = qcWarrantyRuleEngineRepository.getRuleCount();
        redisTemplate.opsForValue().set(QC_RULE_COUNT, ruleCount, 1, TimeUnit.MINUTES);
        return ruleCount;
    }

    private Boolean createExchangeOrder(ReturnCreationRequestDTO returnCreationRequest, List<ReturnItemDTO> exchangeItemList, ReturnCreationResponse returnCreationResponse) {

        log.info("[createExchangeOrder] exchangeItemList : {}", exchangeItemList);
        if(!CollectionUtils.isEmpty(exchangeItemList) && !JJONLINE.equalsIgnoreCase(returnCreationRequest.getPurchaseOrderDetailsDTO().getOrders().get(0).getChannel())){
            Integer returnId = returnCreationResponse.getReturnId();
            ExchangeOrderCreationRequest exchangeOrderCreationRequest = new ExchangeOrderCreationRequest();
            exchangeOrderCreationRequest.setPaymentMethod("exchangep");
            exchangeOrderCreationRequest.setReturnId(returnId);
            List<ExchangeItem> exchangeItems = new ArrayList<>();
            for (ReturnItemDTO returnItem : exchangeItemList) {
                ExchangeItem exchangeItem = new ExchangeItem();
                exchangeItem.setExchangeMethod("SAMEPRODUCT");
                exchangeItem.setItemId(returnItem.getMagentoId());
                exchangeItem.setOrderId(returnCreationRequest.getIncrementId());
                exchangeItems.add(exchangeItem);
            }
            exchangeOrderCreationRequest.setItems(exchangeItems);
            if (null != returnCreationRequest.getExchangeAddress() && StringUtils.isNotEmpty(returnCreationRequest.getExchangeAddress().getFirstName())) {
                log.info("[createExchangeOrder] Exchange and pickupAddress not same : {}", returnCreationRequest.getIncrementId());
                exchangeOrderCreationRequest.setShippingAddress(returnCreationRequest.getExchangeAddress());
            } else {
                log.info("[createExchangeOrder] Exchange and pickupAddress same : {}", returnCreationRequest.getIncrementId());
                ExchangeAddressDTO exchangeAddress = new ExchangeAddressDTO();
                exchangeAddress.setFirstName(returnCreationRequest.getReversePickupAddress().getFirstName());
                exchangeAddress.setLastName(returnCreationRequest.getReversePickupAddress().getLastName());
                exchangeAddress.setAddressline1(returnCreationRequest.getReversePickupAddress().getStreet1());
                exchangeAddress.setAddressType(SHIPPING);
                exchangeAddress.setCity(returnCreationRequest.getReversePickupAddress().getCity());
                exchangeAddress.setCountry(returnCreationRequest.getReversePickupAddress().getCountry());
                exchangeAddress.setEmail(returnCreationRequest.getReversePickupAddress().getEmail());
                String telephone = returnCreationRequest.getReversePickupAddress().getTelephone();
                if (telephone.contains("-")) {
                    String[] teleStringArray = telephone.split("-");
                    telephone = teleStringArray[1];
                }
                exchangeAddress.setPhone(telephone);
                exchangeAddress.setPhoneCode(Constant.IND_PHONE_CODE);
                exchangeAddress.setPostcode(returnCreationRequest.getReversePickupAddress().getPincode().toString());
                exchangeAddress.setState(returnCreationRequest.getReversePickupAddress().getState());
                exchangeOrderCreationRequest.setShippingAddress(exchangeAddress);
            }

            kafkaService.pushToKafka(EXCHANGE_ORDER_CREATION_QUEUE,String.valueOf(returnCreationRequest.getIncrementId()),exchangeOrderCreationRequest);
            return true;
        }
        return false;
    }

    private void triggerCommunication(List<ReturnItemDTO> exchangeItemList, Integer returnId, Long groupId, List<UwOrderDTO> uwOrderDTOs, List<OrdersDTO> ordersDTOs, ReturnCreationRequestDTO returnCreationRequest, ReverseCourierDetail reverseCourierDetail, Integer requestId, Boolean isExchangeCreated) {
        communicationService.triggerCommunication(reverseCourierDetail, returnCreationRequest, groupId, ordersDTOs, uwOrderDTOs, exchangeItemList, requestId, returnId, isExchangeCreated);
    }

    private void initiateRefund(ReturnCreationResponse returnCreationResponse, Integer incrementId, Map<Long, ReturnItemDTO> magentoItemToReturnItemMap, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, ReturnRequest returnRequest, Integer returnId) {
        RefundProcessDTO refundProcessDTO = new RefundProcessDTO(returnCreationResponse, purchaseOrderDetailsDTO.getUwOrders(), purchaseOrderDetailsDTO.getOrders(), purchaseOrderDetailsDTO.getOrdersHeaderDTO(), magentoItemToReturnItemMap, incrementId, returnId, returnRequest.getId());
        try{
            kafkaService.pushToKafka(PROCESS_REFUND_ON_RETURN_INITIATION, String.valueOf(incrementId), refundProcessDTO);
        }catch (Exception exception){
            log.error("[initiateRefund] order : {}, issue in refund initiation : {}", incrementId, exception.getMessage());
        }
    }

    private ReverseCourierDetail getReverseCourierDetail(ReturnCreationRequestDTO returnCreationRequest, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO, Integer incrementId, ReturnRequest returnRequest) {
        return reverseCourierDetailService.getReverseCourierDetail(returnCreationRequest, purchaseOrderDetailsDTO, incrementId, returnRequest);
    }

    private static boolean isCourierToBeReassigned(Boolean isCourierReassigned) {
        return isCourierReassigned != null && isCourierReassigned;
    }

}
