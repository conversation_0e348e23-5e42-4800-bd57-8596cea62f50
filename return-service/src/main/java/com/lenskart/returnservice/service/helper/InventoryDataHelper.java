package com.lenskart.returnservice.service.helper;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.lenskart.core.model.Classification;
import com.lenskart.core.model.OrderItemGSTDetail;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.UniReportStockEntry;
import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.orderops.model.S3InvoiceDetails;
import com.lenskart.orderops.model.SbrtOrderItem;
import com.lenskart.returncommon.model.dto.ClassificationDto;
import com.lenskart.returncommon.model.dto.InventoryRequestDto;
import com.lenskart.returncommon.model.dto.InvoiceDetailsDto;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import com.lenskart.returncommon.model.dto.OrderItemGSTDetailDto;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.ProductDto;
import com.lenskart.returncommon.model.dto.UniReportStockEntryDto;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class InventoryDataHelper {

    private final OrderOpsFeignClient orderOpsFeignClient;

    public PurchaseOrderDetailsDTO getOrdersDetailsInfo(String identifierType, String identifierValue) {
        return orderOpsFeignClient.getPurchaseOrderDetails(identifierType,identifierValue).getBody();
    }

    public OrdersDTO getOrders(InventoryRequestDto requestDto) {
        PurchaseOrderDetailsDTO orderInfo = getOrdersDetailsInfo(requestDto.getIdentifierType(), requestDto.getIdentifierValue() != null ? requestDto.getIdentifierValue() : String.valueOf(requestDto.getIncrementId()));
        if(orderInfo != null && orderInfo.getOrders() != null && !orderInfo.getOrders().isEmpty()) {
            return orderInfo.getOrders().get(0);
        }
        return null;
    }

    public OrderAddressUpdateDTO getOrderAddressUpdate(InventoryRequestDto requestDto) {
        PurchaseOrderDetailsDTO orderInfo = getOrdersDetailsInfo(requestDto.getIdentifierType(), requestDto.getIdentifierValue() != null ? requestDto.getIdentifierValue() : String.valueOf(requestDto.getIncrementId()));
        if(orderInfo != null && orderInfo.getOrderAddressUpdateDTOs() != null && !orderInfo.getOrderAddressUpdateDTOs().isEmpty()) {
            return orderInfo.getOrderAddressUpdateDTOs().stream().filter(x->x.getAddressType().equalsIgnoreCase("billing")).findFirst().orElse(null);
        }
        return null;
    }

    public OrdersHeaderDTO getOrderHeader(InventoryRequestDto requestDto) {
        PurchaseOrderDetailsDTO orderInfo = getOrdersDetailsInfo(requestDto.getIdentifierType(), requestDto.getIdentifierValue() != null ? requestDto.getIdentifierValue() : String.valueOf(requestDto.getIncrementId()));
        if(orderInfo != null && orderInfo.getOrdersHeaderDTO() != null) {
            return orderInfo.getOrdersHeaderDTO();
        }
        return null;
    }

    public List<UwOrderDTO> getUwOrders(InventoryRequestDto requestDto) {
        PurchaseOrderDetailsDTO orderInfo = getOrdersDetailsInfo(requestDto.getIdentifierType(), requestDto.getIdentifierValue() != null ? requestDto.getIdentifierValue() : String.valueOf(requestDto.getIncrementId()));
        if(orderInfo != null && orderInfo.getUwOrders() != null && !orderInfo.getUwOrders().isEmpty()) {
            return orderInfo.getUwOrders();
        }
        return null;
    }

    public ItemWisePriceDetailsDTO getItemDetails(InventoryRequestDto requestDto ) {
        PurchaseOrderDetailsDTO orderInfo = getOrdersDetailsInfo(requestDto.getIdentifierType(), requestDto.getIdentifierValue() != null ? requestDto.getIdentifierValue() : String.valueOf(requestDto.getIncrementId()));
        if(orderInfo != null && orderInfo.getItemWisePrices() != null && !orderInfo.getItemWisePrices().isEmpty()) {
            return orderInfo.getItemWisePrices().stream().filter(x->x.getItemId().equals(requestDto.getItemId())).findFirst().orElse(null);
        }
        return null;
    }

    public OrdersDTO getOrders(InventoryRequestDto requestDto, PurchaseOrderDetailsDTO orderInfo) {
        if(orderInfo != null && orderInfo.getOrders() != null && !orderInfo.getOrders().isEmpty()) {
            return orderInfo.getOrders().get(0);
        }
        return null;
    }

    public OrderAddressUpdateDTO getOrderAddressUpdate(InventoryRequestDto requestDto, PurchaseOrderDetailsDTO orderInfo) {
        if(orderInfo != null && orderInfo.getOrderAddressUpdateDTOs() != null && !orderInfo.getOrderAddressUpdateDTOs().isEmpty()) {
            return orderInfo.getOrderAddressUpdateDTOs().stream().filter(x->x.getAddressType().equalsIgnoreCase("billing")).findFirst().orElse(null);
        }
        return null;
    }

    public OrdersHeaderDTO getOrderHeader(InventoryRequestDto requestDto, PurchaseOrderDetailsDTO orderInfo) {
        if(orderInfo != null && orderInfo.getOrdersHeaderDTO() != null) {
            return orderInfo.getOrdersHeaderDTO();
        }
        return null;
    }

    public List<UwOrderDTO> getUwOrders(InventoryRequestDto requestDto, PurchaseOrderDetailsDTO orderInfo) {
        if(orderInfo != null && orderInfo.getUwOrders() != null && !orderInfo.getUwOrders().isEmpty()) {
            return orderInfo.getUwOrders();
        }
        return null;
    }

    public ItemWisePriceDetailsDTO getItemDetails(InventoryRequestDto requestDto, PurchaseOrderDetailsDTO orderInfo) {
        if(orderInfo != null && orderInfo.getItemWisePrices() != null && !orderInfo.getItemWisePrices().isEmpty()) {
            return orderInfo.getItemWisePrices().stream().filter(x->x.getItemId().equals(requestDto.getItemId())).findFirst().orElse(null);
        }
        return null;
    }

    public List<ProductDto> getProductsByIds(List<Integer> productIds) {
        HashMap<String, Product> products = orderOpsFeignClient.getProductsByIds(productIds).getBody();
        if(products != null && !products.isEmpty() && !products.values().isEmpty()) {
            return products.values().stream().map(this::getProductDto).toList();
        }
        return null;
    }

    public List<String> getHubMasterFacilityCodes() {
        return orderOpsFeignClient.getHubMasterFacilityCodes().getBody();
    }

    public InvoiceDetailsDto getInvoiceDetails(String shippingPackageId) {
        S3InvoiceDetails s3InvoiceDetails = orderOpsFeignClient.getInvoiceDetails(shippingPackageId).getBody();
        if(s3InvoiceDetails != null) {
            return InvoiceDetailsDto.builder()
                    .invoiceCode(s3InvoiceDetails.getInvoiceCode())
                    .build();
        }
        return null;
    }

    public List<ClassificationDto> getClassificationsByIds(List<Integer> classificationIds) {
        List<Classification> classifications = orderOpsFeignClient.getClassificationsByIds(classificationIds).getBody();
        if(classifications != null && !classifications.isEmpty()) {
            return classifications.stream().map(this::getClassificationDto).toList();
        }
        return null;
    }

    public OrderItemGSTDetailDto getOrderItemGSTDetails(InventoryRequestDto requestDto) {
        List<OrderItemGSTDetail> itemGSTDetails = orderOpsFeignClient.getOrderItemGSTDetails(List.of(requestDto.getUwItemId())).getBody();
        if(itemGSTDetails != null && !itemGSTDetails.isEmpty()) {
            return getOrderItemGSTDetailDto(itemGSTDetails.get(0));
        }
        return null;
    }

    public UniReportStockEntryDto fetchUniReportStockEntity(String itemCode, Collection<String> facilityCode) {
        UniReportStockEntry uniReportStockEntry = orderOpsFeignClient.fetchUniReportStockEntity(itemCode, facilityCode).getBody();
        if(uniReportStockEntry != null) {
            return UniReportStockEntryDto.builder()
            .unitPriceWithoutTax(uniReportStockEntry.getUnitPriceWithoutTax())
            .build();
        }
        return null;
    }

    public SbrtOrderItem fetchSBRTOrderItem(Integer uwItemId) {
        return orderOpsFeignClient.fetchSBRTOrderItem(uwItemId).getBody();
    }

    public List<Integer> fetchUwItemIdsFromSBRTOrderItem(Integer uwItemId) {
        SbrtOrderItem sbrtOrderItem = fetchSBRTOrderItem(uwItemId);
        if(sbrtOrderItem != null) {
            return List.of(sbrtOrderItem.getUwItemId());
        }
        return null;
    }

    private ProductDto getProductDto(Product product) {
        if (product == null) {
            return null;
        }
    
        return ProductDto.builder()
                .productId(product.getProductId())
                .classification(product.getClassification())
                .groupProductIds(product.getGroupProductIds())
                .value(product.getValue())
                .qty(product.getQty())
                .isInStock(product.getIsInStock())
                .sku(product.getSku())
                .productImage(product.getProductImage())
                .productImageAlt(product.getProductImageAlt())
                .enable(product.getEnable())
                .sellingPrice(product.getSellingPrice())
                .costPrice(product.getCostPrice())
                .costAverage(product.getCostAverage())
                .price(product.getPrice())
                .size(product.getSize())
                .startingSoh(product.getStartingSoh())
                .currentSoh(product.getCurrentSoh())
                .qsi(product.getQsi())
                .wfi(product.getWfi())
                .stockOut(product.getStockOut())
                .stockMissingSupplier(product.getStockMissingSupplier())
                .stockOutSupplier(product.getStockOutSupplier())
                .stockInSupplier(product.getStockInSupplier())
                .stockFoundSupplier(product.getStockFoundSupplier())
                .stockDefectiveSupplier(product.getStockDefectiveSupplier())
                .stockReturnSupplier(product.getStockReturnSupplier())
                .webStockOut(product.getWebStockOut())
                .stockIn(product.getStockIn())
                .stockDefective(product.getStockDefective())
                .stockMissing(product.getStockMissing())
                .stockFound(product.getStockFound())
                .stockReturn(product.getStockReturn())
                .returnVendor(product.getReturnVendor())
                .inventoryStartDate(product.getInventoryStartDate())
                .createdAt(product.getCreatedAt())
                .closedStockOut(product.getClosedStockOut())
                .processingNotStockout(product.getProcessingNotStockout())
                .nqsi(product.getNqsi())
                .repeatRate(product.getRepeatRate())
                .repeatMailerRate(product.getRepeatMailerRate())
                .catIds(product.getCatIds())
                .brand(product.getBrand())
                .stockOffline(product.getStockOffline())
                .productUrl(product.getProductUrl())
                .frameType(product.getFrameType())
                .msl(product.getMsl())
                .isPowerFollowup(product.getIsPowerFollowup())
                .unicomSynStatus(product.getUnicomSynStatus())
                .unicommerceInventory(product.getUnicommerceInventory())
                .blockedUnicomInventory(product.getBlockedUnicomInventory())
                .completeClosedCount(product.getCompleteClosedCount())
                .productImages(product.getProductImages())
                .hsnCode(product.getHsnCode())
                .hsnCodeWithoutPower(product.getHsnCodeWithoutPower())
                .hsnClassification(product.getHsnClassification())
                .transportationFlag(product.getTransportationFlag())
                .frameMaterial(product.getFrameMaterial())
                .alwaysGoForTracing(product.getAlwaysGoForTracing())
                .frameBaseCureveId(product.getFrameBaseCureveId())
                .meiFrameType(product.getMeiFrameType())
                .build();
    }

    private ClassificationDto getClassificationDto(Classification classification) {
        if (classification == null) {
            return null;
        }
        return ClassificationDto.builder()
                .id(classification.getId())
                .classificationName(classification.getClassificationName())
                .website(classification.getWebsite())
                .unicom_syn_status(classification.getUnicom_syn_status())
                .displayName(classification.getDisplayName())
                .UStaxCode(classification.getUStaxCode())
                .UStaxCodeWithoutPower(classification.getUStaxCodeWithoutPower())
                .build();
    }

    private OrderItemGSTDetailDto getOrderItemGSTDetailDto(OrderItemGSTDetail orderItemGSTDetail) {
        if (orderItemGSTDetail == null) {
            return null;
        }
        return OrderItemGSTDetailDto.builder()
                .orderId(orderItemGSTDetail.getOrderId())
                .uwItemId(orderItemGSTDetail.getUwItemId())
                .pid(orderItemGSTDetail.getPid())
                .classificationId(orderItemGSTDetail.getClassificationId())
                .hsn(orderItemGSTDetail.getHsn())
                .prodDesc(orderItemGSTDetail.getProdDesc())
                .costPerItem(toDouble(orderItemGSTDetail.getCostPerItem()))
                .cgstPc(toDouble(orderItemGSTDetail.getCgstPc()))
                .cgstAmount(toDouble(orderItemGSTDetail.getCgstAmount()))
                .sgstPc(toDouble(orderItemGSTDetail.getSgstPc()))
                .sgstAmount(toDouble(orderItemGSTDetail.getSgstAmount()))
                .igstPc(toDouble(orderItemGSTDetail.getIgstPc()))
                .igstAmount(toDouble(orderItemGSTDetail.getIgstAmount()))
                .ugstPc(toDouble(orderItemGSTDetail.getUgstPc()))
                .ugstAmount(toDouble(orderItemGSTDetail.getUgstAmount()))
                .totalAmount(toDouble(orderItemGSTDetail.getTotalAmount()))
                .createdAt(orderItemGSTDetail.getCreatedAt())
                .build();
    }

    private Double toDouble(BigDecimal value) {
        return value != null ? value.doubleValue() : null;
    }

    public List<UwOrderDTO> getFilteredUwOrdersByGivenUwItemId(Integer uwItemId) {
        List<UwOrderDTO> uwOrderDTOList = getUwOrders(InventoryRequestDto.builder().identifierType("UW_ITEM_ID").identifierValue(String.valueOf(uwItemId)).build());
        if (!CollectionUtils.isEmpty(uwOrderDTOList)) {
            return uwOrderDTOList.stream().filter(x -> x.getUwItemId().equals(uwItemId)).findFirst().map(List::of).orElse(Collections.emptyList());
        }
        return Collections.emptyList();
    }

    public List<UwOrderDTO> getFilteredUwOrdersByBarcode(String barcode, Integer incrementId) {
        List<UwOrderDTO> uwOrderDTOList = getUwOrders(InventoryRequestDto.builder().identifierType("INCREMENT_ID").identifierValue(String.valueOf(incrementId)).build());
        if (!CollectionUtils.isEmpty(uwOrderDTOList)) {
            return uwOrderDTOList.stream().filter(x -> x.getBarcode().equalsIgnoreCase(barcode)).findFirst().map(List::of).orElse(Collections.emptyList());
        }
        return Collections.emptyList();
    }

    public List<UwOrderDTO> getLensUWOrdersList(int incrementId, int uwItemId) {
        List<UwOrderDTO> uwOrderDTOList = getUwOrders(InventoryRequestDto.builder().identifierType("INCREMENT_ID").identifierValue(String.valueOf(incrementId)).build());
        if (!CollectionUtils.isEmpty(uwOrderDTOList)) {
            return uwOrderDTOList.stream().filter(x-> x.getParentUw() == uwItemId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    
}
