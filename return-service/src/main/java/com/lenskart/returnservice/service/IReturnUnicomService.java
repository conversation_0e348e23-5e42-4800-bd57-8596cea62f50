package com.lenskart.returnservice.service;

import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.return_refund_rules.model.ReturnItemRequest;
import com.lenskart.returncommon.exception.ReturnRequestFailException;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IReturnUnicomService {
    ResponseEntity<String> createReversePickup(List<ReturnItemRequestDTO> returnItemRequest, Object shippingAddress, String saleOrderCode, String facility, Boolean rtoItem) throws ReturnRequestFailException;

}
