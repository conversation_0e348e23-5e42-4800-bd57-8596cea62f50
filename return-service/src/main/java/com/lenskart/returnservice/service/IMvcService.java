package com.lenskart.returnservice.service;



import com.lenskart.returncommon.model.dto.MvcOrderDTO;
import com.lenskart.returncommon.model.response.CustomerStoreProfileResponse;
import com.lenskart.returncommon.model.response.ReturnAutoApprovalRules;

public interface IMvcService {
    CustomerStoreProfileResponse getMvcMvsStats(Long customerId, String storeId);
    MvcOrderDTO getMvcOrdersResponse(String identifierType, String identifierValue);
    ReturnAutoApprovalRules getApprovalStatusAndRefundMethod(Long customerId, Integer incrementId, Integer uwItemId, String modelName, String eyeWearType, String secondaryReturnReason, Integer returnId);
}
