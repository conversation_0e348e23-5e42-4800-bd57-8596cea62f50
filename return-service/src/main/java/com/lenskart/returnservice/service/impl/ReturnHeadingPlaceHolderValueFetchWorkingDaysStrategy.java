package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnservice.service.IReturnHeadingPlaceHolderValueFetchStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ReturnHeadingPlaceHolderValueFetchWorkingDaysStrategy implements IReturnHeadingPlaceHolderValueFetchStrategy {
    @Value("${dispatch.within.working.days}")
    String working_days;

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            ReturnStatusHeadingDetail returnStatusHeadingDetail = returnDetailsResponse.getReturnStatusHeading();
            try {
                if (null != returnStatusHeadingDetail) {
                    List<String> returnSubHeadingList = returnStatusHeadingDetail.getSubHeading();
                    List<String> returnSubHeadingListNew = null;
                    if (CollectionUtils.isNotEmpty(returnSubHeadingList)) {
                        returnSubHeadingListNew = new ArrayList<>();
                        for (String subHeading : returnSubHeadingList) {
                            int keyStartIndex = subHeading.indexOf("{");
                            int keyEndIndex = subHeading.indexOf("}");
                            if (-1 != keyEndIndex && -1 != keyEndIndex) {
                                subHeading = subHeading.replace(subHeading.substring(keyStartIndex, keyEndIndex + 1), working_days);
                            }
                            returnSubHeadingListNew.add(subHeading);
                        }
                    }
                    returnStatusHeadingDetail.setSubHeading(returnSubHeadingListNew);
                    log.info("ReturnStatusHeadingDetail is :: " + returnStatusHeadingDetail.toString());
                }
            }catch (Throwable e){
                log.error("Exception in getReturnHeadingsPlaceHolderValue",e);
                if(null != returnStatusHeadingDetail){
                    returnStatusHeadingDetail.setSubHeading(Arrays.asList(""));
                }
            }
        }
    }


    @Override
    public void getReturnTimelinePlaceHolderValue(ReturnTimeLine returnTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            if (null != returnTimeLine) {
                try {
                    String returnSubStatus = returnTimeLine.getReturnSubStatus();
                    if (StringUtils.isNotEmpty(returnSubStatus)) {
                        int keyStartIndex = returnSubStatus.indexOf("{");
                        int keyEndIndex = returnSubStatus.indexOf("}");
                        if (-1 != keyEndIndex && -1 != keyEndIndex) {
                            returnSubStatus = returnSubStatus.replace(returnSubStatus.substring(keyStartIndex, keyEndIndex + 1), working_days);
                        }
                        returnTimeLine.setReturnSubStatus(returnSubStatus);
                    }
                }catch (Throwable e){
                    log.error("Exception in getReturnTimelinePlaceHolderValue",e);
                    if(null != returnTimeLine){
                        returnTimeLine.setReturnSubStatus("");
                    }
                }
            }
        }
    }

    @Override
    public void getRefundTimelinePlaceHolderValue(RefundTimeLine refundTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            if (null != refundTimeLine) {
                try {
                    String refundSubStatus = refundTimeLine.getRefundSubStatus();
                    if (StringUtils.isNotEmpty(refundSubStatus)) {
                        int keyStartIndex = refundSubStatus.indexOf("{");
                        int keyEndIndex = refundSubStatus.indexOf("}");
                        if (-1 != keyEndIndex && -1 != keyEndIndex) {
                            refundSubStatus = refundSubStatus.replace(refundSubStatus.substring(keyStartIndex, keyEndIndex + 1), working_days);
                        }
                        refundTimeLine.setRefundSubStatus(refundSubStatus);
                    }
                }catch (Throwable e){
                    log.error("Exception in getRefundTimelinePlaceHolderValue",e);
                    if(null != refundTimeLine){
                        refundTimeLine.setRefundSubStatus("");
                    }
                }
            }
        }
    }

    @Override
    public void getExchangeTimelinePlaceHolderValue(ExchangeTimeLine exchangeTimeLine, ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            if (null != exchangeTimeLine) {
                try {
                    String exchangeSubStatus = exchangeTimeLine.getExchangeSubStatus();
                    if (StringUtils.isNotEmpty(exchangeSubStatus)) {
                        int keyStartIndex = exchangeSubStatus.indexOf("{");
                        int keyEndIndex = exchangeSubStatus.indexOf("}");
                        if (-1 != keyEndIndex && -1 != keyEndIndex) {
                            exchangeSubStatus = exchangeSubStatus.replace(exchangeSubStatus.substring(keyStartIndex, keyEndIndex + 1), working_days);
                        }
                        exchangeTimeLine.setExchangeSubStatus(exchangeSubStatus);
                    }
                }catch (Throwable e){
                    log.error("Exception in getExchangeTimelinePlaceHolderValue",e);
                    if(null != exchangeTimeLine){
                        exchangeTimeLine.setExchangeSubStatus("");
                    }
                }
            }
        }
    }

    @Override
    public void getReturnHistoryPlaceHolderValue(ReturnStatusHistory returnStatusHistory, ReturnDetailsResponse returnDetailsResponse) {
        if (null != returnDetailsResponse) {
            if (null != returnStatusHistory) {
                try {
                    String returnSubStatus = returnStatusHistory.getComment();
                    if (StringUtils.isNotEmpty(returnSubStatus)) {
                        int keyStartIndex = returnSubStatus.indexOf("{");
                        int keyEndIndex = returnSubStatus.indexOf("}");
                        if (-1 != keyEndIndex && -1 != keyEndIndex) {
                            returnSubStatus = returnSubStatus.replace(returnSubStatus.substring(keyStartIndex, keyEndIndex + 1), working_days);
                        }
                        returnStatusHistory.setComment(returnSubStatus);
                    }
                }catch (Throwable e){
                    log.error("Exception in getReturnHistoryPlaceHolderValue",e);
                    if(null != returnStatusHistory){
                        returnStatusHistory.setComment("");
                    }
                }
            }
        }
    }

    @Override
    public void getReturnHeadingsPlaceHolderValue(ReturnDetailsResponse returnDetailsResponse, List<RefundDetails> refundDetailsList, ReturnStatusHeadingDetail returnStatusHeadingDetail, Long incrementId, Date createdAt, String anyReceivingPoint) {

    }
}
