package com.lenskart.returnservice.service;

import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.dto.ReturnReasonDTO;
import com.lenskart.returncommon.model.response.ReturnReasonsResponse;
import com.lenskart.returnrepository.entity.ReturnDetailReason;

import java.util.List;
import java.util.Map;

public interface IReturnReasonService {
    List<ReturnDetailReason> saveReturnReasonsForItem(String source, Integer incrementId, Integer returnId, ReturnItemDTO item, int uwItemId) throws Exception;
    ReturnReasonsResponse getReturnReasons(String platform, String categoryId);

    ReturnDetailReason saveReturnReason(Integer uwItemId, Integer returnId);
    ReturnReasonsResponse getApprovalReasons();
    ReturnReasonDTO getReturnReason(Integer returnId);
    Map<String, String> getReturnReasons(Integer returnId);

    List<ReturnReasonDTO> getReturnReasonsList(Integer orderId);

    ReturnReasonDTO getReturnReason(Integer uwItemId, Integer returnId);
}
