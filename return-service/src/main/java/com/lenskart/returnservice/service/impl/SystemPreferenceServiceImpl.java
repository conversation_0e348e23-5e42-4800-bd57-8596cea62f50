package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.SystemPreferenceRepository;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SystemPreferenceServiceImpl implements ISystemPreferenceService {

    @Override
    public List<SystemPreference> findAll() {
        try {
            return (List<SystemPreference>) systemPreferenceRepository.findAll();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<SystemPreference> findAllByGroup(String group) {
        try {
            return systemPreferenceRepository.findAllByGroup(group);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public SystemPreference findOneByGroupAndKey(String group, String key) {
        try {
            return systemPreferenceRepository.findTopByGroupAndKey(group, key);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<SystemPreference> save(List<SystemPreference> systemPreferences) {
        try {
            for (SystemPreference systemPreference : systemPreferences) {
                systemPreference.setValue(String.valueOf(systemPreference.getValue()));
                systemPreferenceRepository.save(systemPreference);
            }
            return systemPreferences;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<String> getValuesAsList(String group, String key) throws Exception {
        List<String> values = new ArrayList<String>();
        SystemPreference systemPreference = findOneByGroupAndKey(group, key);
        if (Constant.SYSTEM_PREFERENCE_TYPES.LIST.equals(systemPreference.getType())) {
            if (null != systemPreference.getValue() && !systemPreference.getValue().isEmpty()) {
                values = Arrays.asList(systemPreference.getValue().split("\\s*,\\s*"));
            }
        } else {
            throw new RuntimeException("For group -- " + group + " and key -- " + key + " is not of type LIST. Its type is " + systemPreference.getType());
        }
        return values;

    }

    @Override
    public Object getValuesByType(SystemPreference systemPreference) {
        try {
            if( systemPreference != null ) {
                if( Constant.SYSTEM_PREFERENCE_TYPES.LIST.equalsIgnoreCase(systemPreference.getType()) ) {
                    return Arrays.asList(systemPreference.getValue().split("\\s*,\\s*"));
                } else if( Constant.SYSTEM_PREFERENCE_TYPES.INTEGER.equalsIgnoreCase(systemPreference.getType()) ) {
                    return Integer.parseInt(systemPreference.getValue());
                } else if( Constant.SYSTEM_PREFERENCE_TYPES.BOOLEAN.equalsIgnoreCase(systemPreference.getType()) ) {
                    return Boolean.parseBoolean(systemPreference.getValue());
                }
            }
        } catch (Exception e) {
            logger.error("Error in getting values, Exception : " + e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<SystemPreference> findAllByGroupIn(List<String> groups) {
        try {
            return systemPreferenceRepository.findAllByGroupIn(groups);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public SystemPreference findAllByKey(String key) {
        try {
            return systemPreferenceRepository.findAllByKey(key);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public String getSystemPreferenceValues(String key, String group){
        String searchKey = "";
        if(!StringUtils.isEmpty(key) && !StringUtils.isEmpty(group)) {
            searchKey = key +"_"+ group;
        }else {
            return null;
        }

        String value = null;
        try {
            if(redisTemplate.hasKey(searchKey)){
                logger.info("[getSystemPreferenceValues] Fetching from cache searchKey : "+searchKey);
                value = (String) redisTemplate.opsForValue().get(searchKey);
            }else{
                logger.info("[getSystemPreferenceValues] Fetching from DB searchKey : "+searchKey);
                value = getByDatabase(key,group);
                if(StringUtils.isNotEmpty(value)){
                    logger.info("[getSystemPreferenceValues] Setting searchKey : "+searchKey+" and value : "+value);
                    redisTemplate.opsForValue().set(searchKey,value);
                }
            }
        }catch (Exception e){
            logger.error("[getSystemPreferenceValues] Exception in config set ",e);
            value = getByDatabase(key,group);
        }
        return value;
    }

    public List<String> getListFromValue(String group, String key) {
        List<String> list = new ArrayList<>();
        String value = getSystemPreferenceValues(key, group);
        logger.info("getListFromValue : value = "+value);
        if(value!=null){
            list = Arrays.asList(value.split("\\s*,\\s*"));
        }
        return list;
    }

    @Override
    public String getSystemPreferenceValues(String group, String key, int ttl, TimeUnit timeUnit) {
        String searchKey = "";
        if(!StringUtils.isEmpty(key) && !StringUtils.isEmpty(group)) {
            searchKey = key +"_"+ group;
        }else {
            return null;
        }

        String value = null;
        try {
            if(redisTemplate.hasKey(searchKey)){
                value = (String) redisTemplate.opsForValue().get(searchKey);
                logger.info("[getSystemPreferenceValues] Fetching from cache searchKey : "+searchKey + " value="+value);
            }else{
                logger.info("[getSystemPreferenceValues] Fetching from DB searchKey : "+searchKey);
                value = getByDatabase(key,group);
                if(StringUtils.isNotEmpty(value)){
                    logger.info("[getSystemPreferenceValues] Setting searchKey : "+searchKey+" and value : "+value);
                    redisTemplate.opsForValue().set(searchKey,value,ttl,timeUnit);
                }
            }
        }catch (Exception e){
            logger.error("[getSystemPreferenceValues] Exception in config set ",e);
            value = getByDatabase(key,group);
        }
        return value;
    }

    @Override
    public List<SystemPreference> findAllByGroup(String group, int ttl, TimeUnit timeUnit) {
        List<SystemPreference> systemPreferences = null;
        String searchKey = "";
        if(!StringUtils.isEmpty(group)) {
            searchKey = group;
        }else {
            return null;
        }

        try {
            if(redisTemplate.hasKey(searchKey)){
                logger.info("[getSystemPreferenceValues] Fetching from cache searchKey : "+searchKey);
                systemPreferences = (List<SystemPreference>) redisTemplate.opsForValue().get(searchKey);
            }else{
                logger.info("[getSystemPreferenceValues] Fetching from DB searchKey : "+searchKey);
                systemPreferences = findAllByGroup(group);
                if(!CollectionUtils.isEmpty(systemPreferences)){
                    logger.info("[getSystemPreferenceValues] Setting searchKey : "+searchKey+" and value : "+systemPreferences);
                    redisTemplate.opsForValue().set(searchKey,systemPreferences,ttl,timeUnit);
                }
            }
        }catch (Exception e){
            logger.error("[getSystemPreferenceValues] Exception in config set {}",e);
            systemPreferences = findAllByGroup(group);
        }
        return systemPreferences;
    }

    public String getByDatabase(String key, String group) {
        String value = null;
        SystemPreference systemPreference;
        if (!StringUtils.isEmpty(key) && !StringUtils.isEmpty(group)) {
            logger.info("[getByDatabase] findTopByGroupAndKey : " + group + " && " + key);
            systemPreference = systemPreferenceRepository.findTopByGroupAndKey(group, key);
            if (null != systemPreference) {
                value = systemPreference.getValue();
            }
        }
        logger.info("[getByDatabase] value : " + value);
        return value;
    }

    @Override
    public Date getPaymentCaptureLSDate() {
        Date systemPreferenceDate = null;
        try {
            logger.info("[getPaymentCaptureLSDate] : Start");
            SystemPreference systemPreference = null;
            try {
                logger.info("[getPaymentCaptureLSDate] : redis fetch");
                systemPreference = (SystemPreference)redisTemplate.opsForValue().get("PAYMENT_CAPTURE_LK_AFTER");
            } catch(Exception e) {
                logger.error("[getPaymentCaptureLSDate] : redis fetch", e);
            }
            systemPreference = findOneByGroupAndKey("payment_capture","PAYMENT_CAPTURE_LK_AFTER");
            try {
                if(systemPreference != null) {
                    logger.info("[getPaymentCaptureLSDate] : redis set");
                    redisTemplate.opsForValue().set("PAYMENT_CAPTURE_LK_AFTER", systemPreference, 60, TimeUnit.MINUTES);
                }
            } catch(Exception e) {
                logger.error("[getPaymentCaptureLSDate] : redis set", e);
            }
            if( null != systemPreference ) {
                logger.info("[systemPreference] : " + systemPreference.toString());
                if(null != systemPreference && null != systemPreference.getValue() && !systemPreference.getValue().isEmpty()) {
                    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    systemPreferenceDate = df.parse(systemPreference.getValue());
                }
            }
        } catch (ParseException e) {
            logger.error("[getPaymentCaptureLSDate] Exception occurred in while getting System Preference Date. Error =  ", e);
        }
        return systemPreferenceDate;
    }

    @Override
    public boolean isCosmosCommunicationEnabled(String eventName) {
        if(eventName == null) {
            logger.info("[isCosmosCommunicationEnabled-false]:: null event");
            return false;
        }
        SystemPreference systemPreference = findOneByGroupAndKey("cosmos-communication",eventName);
        if (systemPreference == null) {
            logger.info("[isCosmosCommunicationEnabled-true]:: by default event: {}", eventName);
            return true;
        }

        if ("true".equalsIgnoreCase(systemPreference.getValue())) {
            logger.info("[isCosmosCommunicationEnabled-true]:: value: {} event: {}", systemPreference.getValue(), eventName);
            return true;
        }
        logger.info("[isCosmosCommunicationEnabled-false]:: value: {} event: {}", systemPreference, eventName);
        return false;
    }

    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    RedisTemplate redisTemplate;

    private final Logger logger = LoggerFactory.getLogger(SystemPreferenceServiceImpl.class.getName());

}
