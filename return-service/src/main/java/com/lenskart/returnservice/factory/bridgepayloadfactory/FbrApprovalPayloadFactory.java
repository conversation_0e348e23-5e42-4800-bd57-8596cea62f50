package com.lenskart.returnservice.factory.bridgepayloadfactory;

import com.lenskart.returncommon.model.dto.BridgePayloadDTO;
import com.lenskart.returncommon.model.dto.CommRequestDTO;
import com.lenskart.returncommon.model.dto.FBRPayloadDTO;
import com.lenskart.returncommon.model.enums.PayloadName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class FbrApprovalPayloadFactory implements BridgePayloadFactory {

    @Override
    public BridgePayloadDTO createPayload(CommRequestDTO commRequestDTO) {
        Map<String, Object> params = commRequestDTO.getParams();

        FBRPayloadDTO fbrPayloadDTO = new FBRPayloadDTO();
        if (commRequestDTO.getOrderId() != null) {
            fbrPayloadDTO.setOrderId(String.valueOf(commRequestDTO.getOrderId()));
        }

        fbrPayloadDTO.setCustomerMobile(getSafeString(params.get("customerMobile")));
        fbrPayloadDTO.setName(getSafeString(params.get("customerName")));
        fbrPayloadDTO.setCustomerEmail(getSafeString(params.get("customerEmail")));
        fbrPayloadDTO.setGvCode(getSafeString(params.get("gvCode")));
        fbrPayloadDTO.setExpiryDate(getSafeString(params.get("gvExpiry")));
        fbrPayloadDTO.setAmount(getSafeString(params.get("gvAmount")));
        fbrPayloadDTO.setCurrency(getSafeString(params.get("currency")));

        return fbrPayloadDTO;
    }

    private String getSafeString(Object value) {
        return value != null ? value.toString() : null;
    }

    private Integer getSafeInteger(Object value) {
        if (value instanceof Integer i) return i;
        if (value instanceof String s && !s.isBlank()) {
            try {
                return Integer.parseInt(s);
            } catch (NumberFormatException ignored) {
            }
        }
        return null;
    }


    @Override
    public PayloadName getPayloadName() {
        return PayloadName.FRAME_BROKEN_APPROVAL;
    }
}
