package com.lenskart.returnservice.factory.cosmospayloadfactory;

import com.lenskart.ccautils.constants.CosmosConstants;
import com.lenskart.returnservice.service.CosmosEventPayloadProvider;
import com.lenskart.returnservice.service.impl.ReturnCreateCosmosEventPayLoadProvider;
import com.lenskart.returnservice.service.impl.ReturnPickupCosmosEventPayLoadProvider;
import com.lenskart.returnservice.service.impl.ReturnUpdateCosmosEventPayLoadProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class CosmosEventPayloadFactory {
    private static final Map<String, CosmosEventPayloadProvider> providers = new HashMap<>();


    @Autowired
    public CosmosEventPayloadFactory(
            ReturnUpdateCosmosEventPayLoadProvider returnUpdateCosmosEventPayLoadProvider,
            ReturnCreateCosmosEventPayLoadProvider returnCreateCosmosEventPayLoadProvider,
            ReturnPickupCosmosEventPayLoadProvider returnPickupCosmosEventPayLoadProvider) {

        // Populating providers for return-update
        providers.put(CosmosConstants.Events.RETURN_REJECTED.eventName, returnUpdateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_REJECTED_HANDOVER_DONE.eventName, returnUpdateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_APPROVED.eventName, returnUpdateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_EXCHANGED.eventName, returnUpdateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_CANCELLED.eventName, returnUpdateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.PICKUP_FAILED.eventName, returnUpdateCosmosEventPayLoadProvider);

        // Populating providers for return-create
        providers.put(CosmosConstants.Events.RETURN_NEED_APPROVAL.eventName, returnCreateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.eventName, returnCreateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_RECEIVED_AT_STORE.eventName, returnCreateCosmosEventPayLoadProvider);
//        providers.put(CosmosConstants.Events.RETURN_RECEIVED_AT_WAREHOUSE.eventName, returnCreateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_EXPECTED_POS.eventName, returnCreateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_EXPECTED_WH.eventName, returnCreateCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.NEW_RETURN_PICKUP.eventName, returnCreateCosmosEventPayLoadProvider);

        // Populating providers for return-pickup
        providers.put(CosmosConstants.Events.PICKUP_SCHEDULED.eventName, returnPickupCosmosEventPayLoadProvider);
        providers.put(CosmosConstants.Events.RETURN_PICKED.eventName, returnPickupCosmosEventPayLoadProvider);
    }

    public static Map<String, Object> getEventPayload(Object... params) {
        String eventName = (String) params[0];
        CosmosEventPayloadProvider provider = providers.get(eventName);
        if (provider != null) {
            log.info("[getEventPayload] sending params: {}", Arrays.toString(params));
            return provider.getPayload(params);
        } else {
            log.warn("No payload provider found for event: {}", eventName);
            return null;
        }
    }
}
