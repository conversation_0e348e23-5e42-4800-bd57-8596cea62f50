package com.lenskart.returnservice.factory.bridgepayloadfactory;

import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.PayloadName;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returncommon.utils.DateUtil;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IReturnDetailsService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class MasterPayloadFactory implements BridgePayloadFactory {

    @Autowired
    private IReturnDetailsService returnDetailsService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Override
    public BridgePayloadDTO createPayload(CommRequestDTO commRequestDTO) {
        log.info(" createPayload for req :{}", commRequestDTO);
        BasePayloadDTO basePayloadDTO = new BasePayloadDTO();
        basePayloadDTO.setReturnId(commRequestDTO.getReturnId());
        try{
            //if (commRequestDTO.getOrderId() == null) {
            Optional<ReturnDetail> returnDetail = returnOrderActionService.findReturnOrderById(commRequestDTO.getReturnId());
            if (returnDetail.isPresent()) {
                commRequestDTO.setOrderId(returnDetail.get().getIncrementId());
                ReturnDetailItem returnDetailItem = returnOrderActionService.findTopByReturnId(returnDetail.get().getId());
                if (returnDetailItem != null) {
                    commRequestDTO.setUwItemId(returnDetailItem.getUwItemId());
                }
            } else {
                log.info("Invalid Return Id for sending communication");
                return null;
            }
            //}
            basePayloadDTO.setOrderId(String.valueOf(commRequestDTO.getOrderId()));
        }catch (Exception exception){
            log.error("[createPayload] exception : "+exception.getMessage());
        }

        ReturnDetailsForSmsDTO returnDetailsForSms = returnDetailsService.getRefundDetailsForSMS(commRequestDTO);
        log.info(" refundDetailsForSMS :{}", returnDetailsForSms);
        Map<String, Object> request = new HashMap<>();
        request.put("orderId", commRequestDTO.getOrderId());
        request.put("returnId", commRequestDTO.getReturnId());
        request.put("uwItemId", commRequestDTO.getUwItemId());
        request.put("facilityCode", returnDetailsForSms.getReturnCreationRequest().getFacilityCode());
        log.info("[createPayload] request : {}", request);
        ResponseEntity<Map<String, String>> responseEntity = orderOpsFeignClient.getOrderDetailsForReturnSms(request);

        log.info(" order details for sms responseEntity :{}", responseEntity);
        Map<String, String> orderDetailsMap = responseEntity.getBody();

        if (CollectionUtils.isEmpty(orderDetailsMap)) {
            log.info("Invalid order response from OO for request :{}", request);
            return null;
        }


        MasterPayloadDTO masterPayloadDTO = new MasterPayloadDTO(basePayloadDTO);
        masterPayloadDTO.setBrand(orderDetailsMap.get("brand"));
        masterPayloadDTO.setCategory(orderDetailsMap.get("category"));
        masterPayloadDTO.setStoreName(orderDetailsMap.get("store_name"));
        masterPayloadDTO.setStoreLocatorLink(orderDetailsMap.get("store_locator_link"));
        masterPayloadDTO.setStorePhone(orderDetailsMap.get("store_phone"));
        masterPayloadDTO.setStoreOpeningTime(orderDetailsMap.get("store_opening_time"));
        masterPayloadDTO.setStoreClosingTime(orderDetailsMap.get("store_closing_time"));
        masterPayloadDTO.setCountryId(orderDetailsMap.get("country_id"));
        masterPayloadDTO.setOrderPickupEdd(orderDetailsMap.get("pickup_edd"));
        masterPayloadDTO.setCompanyName(orderDetailsMap.get("company_name"));
        masterPayloadDTO.setCustomerSupportNo(orderDetailsMap.get("customer_support_no"));
        masterPayloadDTO.setShort_url_returns_detail_page(orderDetailsMap.get("my_account_refund_link"));
        masterPayloadDTO.setMyaccount_order_tracking_shorturl(orderDetailsMap.get("myaccount_order_tracking_shorturl"));
        masterPayloadDTO.setExchangeOrderPlacementLink(orderDetailsMap.get("my_account_refund_link"));
        masterPayloadDTO.setMy_account_return_url(orderDetailsMap.get("my_account_refund_link"));
        masterPayloadDTO.setNoOfItems(String.valueOf(returnDetailsForSms.getReturnCreationRequest().getItems().size()));

        masterPayloadDTO.setCustomerName(orderDetailsMap.get("customer_name"));
        masterPayloadDTO.setCustomerMobile(orderDetailsMap.get("customer_mobile"));
        masterPayloadDTO.setCustomerEmail(orderDetailsMap.get("customer_email"));

        masterPayloadDTO.setOrder_id(commRequestDTO.getOrderId());

        String refundIntent = returnDetailsForSms.getReturnCreationRequest().getItems().get(0).getRefundMethodRequest();
        if ("exchange".equalsIgnoreCase(refundIntent)) {
            masterPayloadDTO.setReturnMethodChosen(Constant.REFUND_INTENT.EXCHANGE);
        }
        else {
            masterPayloadDTO.setReturnMethodChosen(Constant.REFUND_INTENT.REFUND);
        }
        masterPayloadDTO.setHandoverTime(DateUtil.getCurrentTime());
        masterPayloadDTO.setHandoverDate(DateUtil.getCurrentDate());
        masterPayloadDTO.setReturnPickupAwb(returnDetailsForSms.getReturnPickupAwb());
        masterPayloadDTO.setReturnCreationDate(returnDetailsForSms.getReturnCreationDate());
        masterPayloadDTO.setReturnPickupCourierName(returnDetailsForSms.getReturnPickupCourierName());
        masterPayloadDTO.setReturnPickupDate(returnDetailsForSms.getReturnPickupDate());

        log.info("[order_id] masterPayloadDTO : {}", masterPayloadDTO);
        return masterPayloadDTO;
    }

    @Override
    public PayloadName getPayloadName() {
        return PayloadName.MASTER;
    }
}
