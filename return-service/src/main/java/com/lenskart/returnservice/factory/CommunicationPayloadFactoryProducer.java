package com.lenskart.returnservice.factory;

import com.lenskart.returncommon.model.dto.BridgePayloadDTO;
import com.lenskart.returncommon.model.dto.CommRequestDTO;
import com.lenskart.returncommon.model.enums.PayloadName;
import com.lenskart.returnservice.factory.bridgepayloadfactory.BridgePayloadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CommunicationPayloadFactoryProducer {
    private final Map<PayloadName, BridgePayloadFactory> payloadMap;

    @Autowired
    private CommunicationPayloadFactoryProducer(List<BridgePayloadFactory> payloads) {
        payloadMap = payloads.stream().collect(Collectors.toUnmodifiableMap(BridgePayloadFactory::getPayloadName, Function.identity()));
    }

    public BridgePayloadDTO createBridgePayloadDTO(PayloadName payloadName, CommRequestDTO commRequestDTO) {
        BridgePayloadFactory bridgePayload = payloadMap.get(payloadName);
        if (bridgePayload != null) {
            return bridgePayload.createPayload(commRequestDTO);
        } else {
            throw new IllegalArgumentException(String.format("No factory exist for payload: %s", payloadName));
        }
    }
}