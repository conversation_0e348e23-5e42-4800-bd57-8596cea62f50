package com.lenskart.returnservice.utils;

import com.lenskart.returncommon.model.dto.ReverseCourierDetailDTO;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ReverseCourierDetailMapper {
    ReverseCourierDetailDTO toDto(ReturnCourierDetail detail);

    List<ReverseCourierDetailDTO> toDtoList(List<ReturnCourierDetail> details);
}

