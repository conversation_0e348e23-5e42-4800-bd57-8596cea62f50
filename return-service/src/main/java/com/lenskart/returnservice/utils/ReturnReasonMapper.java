package com.lenskart.returnservice.utils;

import com.lenskart.returncommon.model.dto.ReturnReasonDTO;
import com.lenskart.returnrepository.entity.ReturnDetailReason;
import com.lenskart.returnrepository.entity.ReturnReason;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ReturnReasonMapper {

    ReturnReasonDTO toDto(ReturnDetailReason reason);

    ReturnReasonDTO toDto(ReturnReason reason);

    List<ReturnReasonDTO> toDto(List<ReturnDetailReason> reasons);

    List<ReturnReasonDTO> toDtoOld(List<ReturnReason> reasons);
}

