package com.lenskart.returnservice.utils;

import com.lenskart.returncommon.model.dto.ReturnOrderItemDTO;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ReturnOrderItemMapper {

    ReturnOrderItemDTO toDto(ReturnDetailItem item);

    List<ReturnOrderItemDTO> toDtoList(List<ReturnDetailItem> items);
}

