package com.lenskart.returnservice.utils;


import com.lenskart.returncommon.model.enums.NavChannel;

import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.DTC;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.OTC;

public enum SbrtOrderMapper {
    COCOB2B(OTC, NavChannel.COCOOTC.name()),
    WebB2B(DTC, NavChannel.WebDTC.name()),
    JJOnlineDTC(DTC, NavChannel.WebDTC.name());

    private final String sbrtDeliveryType;
    private final String sbrtNavChannel;

    SbrtOrderMapper(String sbrtDeliveryType, String sbrtNavChannel) {
        this.sbrtDeliveryType = sbrtDeliveryType;
        this.sbrtNavChannel = sbrtNavChannel;
    }

    public String getSbrtDeliveryType() {
        return sbrtDeliveryType;
    }

    public String getSbrtNavChannel() {
        return sbrtNavChannel;
    }

    /*
        Get the Sbrt orderType based on originChannel
        Returns Null if Sbrt Transition not found or flag is false
    */
    public static SbrtOrderMapper getMapping(String name) {
        try {
            return SbrtOrderMapper.valueOf(name);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}