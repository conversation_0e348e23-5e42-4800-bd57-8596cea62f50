package com.lenskart.returnservice.utils;

import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returnrepository.entity.ReturnDetail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ReturnOrderMapper {

    @Mapping(target = "groupId", expression = "java(mapGroupId(returnDetail.getGroupId()))")
    ReturnOrderDTO toDto(ReturnDetail returnDetail);

    List<ReturnOrderDTO> toDtoList(List<ReturnDetail> returnDetails);

    default Integer mapGroupId(Long groupId) {
        return groupId != null ? Math.toIntExact(groupId) : null;
    }
}

