package com.lenskart.returnservice.utils;

import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.refund.client.model.response.RefundMethodResponse;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.response.CustomerStoreProfileResponse;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
@Slf4j
public class MvcAutoApproveReturnThreadPool {

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private IMvcService mvcService;

    @Autowired
    private INeedApprovalProcessorService needApprovalProcessorService;

    @Autowired
    private IRefundUtilsService refundUtilService;

    @Autowired
    private IOrderUtilsService orderUtilsService;

    @Autowired
    private IReturnRefundEligibilityService returnRefundEligibilityServiceImpl;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private INexsFacilityService nexsFacilityService;

    @Value("${dealskart.manesar.facility:LKH03}")
    private String dealskartfacility;

    private static ExecutorService workerThreadPool = Executors.newFixedThreadPool(5);

    public void submitAutoApprovalForMvc(ReturnCreationResponse refundRequestResponse) {
        try {
            Runnable runnable = new Runnable() {
                public void run() {
                    mvcAutoApproveReturnExchange(refundRequestResponse);
                }
            };
            workerThreadPool.execute(runnable);
        } catch (Exception e) {
            log.error("[submitAutoApprovalForMvc] exception", e);
        }
    }

    private void mvcAutoApproveReturnExchange(ReturnCreationResponse refundRequestResponse) {

        try {
            log.info("Inside mvcAutoApproveReturnExchange:{}", refundRequestResponse.getIncrementId());
            populateUwOrderAndUwOrderWH(refundRequestResponse);
            UwOrderDTO uwOrder = refundRequestResponse.getUwOrder();
            log.info("product Id:{}", uwOrder.getProductId());
            if (isProductEyeGlass(uwOrder.getClassification())) {

                String mvcMinValue = systemPreferenceService.getSystemPreferenceValues("mvcMinValue", "mvc_customer");
                log.info("System preference value for mvcMinValue is {}", mvcMinValue);
                int mvcMinimumValue = 9;
                if (Objects.nonNull(mvcMinValue) && !StringUtils.isEmpty(mvcMinValue)) {
                    mvcMinimumValue = Integer.parseInt(mvcMinValue);
                }
                boolean isMvc = checkMvcCustomer(refundRequestResponse.getIncrementId(), mvcMinimumValue, refundRequestResponse.getPurchaseOrderDetailsDTO().getOrders().get(0).getCustomerId());

                if (isMvc) {
                    log.info("is mvc customer,incrementId:{}", refundRequestResponse.getIncrementId());
                    int returnEligibilityPeriod = 30;
                    int exchangeEligibilityPeriod = 365;

                    String returnPeriod = systemPreferenceService.getSystemPreferenceValues("ReturnPeriod", "mvc_return_period");
                    log.info("System preference value for returnPeriod is {}", returnPeriod);
                    if (Objects.nonNull(returnPeriod) && !StringUtils.isEmpty(returnPeriod)) {
                        returnEligibilityPeriod = Integer.parseInt(returnPeriod);
                    }

                    String exchangePeriod = systemPreferenceService.getSystemPreferenceValues("ExchangePeriod", "mvc_return_period");
                    log.info("System preference value for exchangePeriod is {}", exchangePeriod);
                    if (Objects.nonNull(exchangePeriod) && !StringUtils.isEmpty(exchangePeriod)) {
                        exchangeEligibilityPeriod = Integer.parseInt(exchangePeriod);
                    }

                    Calendar c = Calendar.getInstance();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    ResponseEntity<List<ShippingStatusDetail>> shippingResponse = orderOpsFeignClient.getShippingDetails(refundRequestResponse.getIncrementId(), "");
                    String returnEligibileTillDate = returnRefundEligibilityServiceImpl.fetchReturnEligibleTilDate(refundRequestResponse.getUwOrder(), refundRequestResponse.getUwOrderWH(), returnEligibilityPeriod, shippingResponse.getBody(), null, null).getReturnEligiblePeriodDate();
                    log.info("returnEligibileTillDate:{}", returnEligibileTillDate);
                    Date returnEligibilityDate = sdf.parse(returnEligibileTillDate);
                    c.setTime(returnEligibilityDate);
                    c.add(Calendar.DATE, exchangeEligibilityPeriod);
                    c.add(Calendar.DATE, -returnEligibilityPeriod);

                    String exchangeEligibleTillDate = sdf.format(c.getTime());
                    log.info("exchangeEligibleTillDate:{}", exchangeEligibleTillDate);

                    Date currentDate = new Date();
                    String currentDateString = sdf.format(currentDate);

                    boolean isReturnAllowed = returnEligibileTillDate.compareTo(currentDateString) > 0;
                    boolean isExchangedAllowed = exchangeEligibleTillDate.compareTo(currentDateString) > 0;

                    log.info("currentDateString:{}", currentDateString);
                    NeedApprovalRequest needApprovalRequest = createNeedApprovalRequest(refundRequestResponse);
                    log.info("needApprovalRequest:{}", needApprovalRequest.toString());
                    log.info("isReturnAllowed:{}", isReturnAllowed);
                    log.info("isExchangedAllowed:{}", isExchangedAllowed);
                    if (isReturnAllowed) {
                        //allow exchange and refund methods
                        List<String> refundMethods = refundMethodsFromRefundService(uwOrder, refundRequestResponse.getPurchaseOrderDetailsDTO());
                        if (!refundMethods.contains("exchange")) {
                            needApprovalRequest.setDelightMethod(String.join(", ", refundMethods) + ", exchange");
                        } else {
                            needApprovalRequest.setDelightMethod(String.join(", ", refundMethods));
                        }

                        boolean isApprovedActionTaken = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
                        log.info("auto approved by delight through system for refund and exchange:{},incrementId:{}", isApprovedActionTaken, refundRequestResponse.getIncrementId());
                    } else if (isExchangedAllowed) {
                        //allow exchange only
                        needApprovalRequest.setDelightMethod("exchange");

                        boolean isApprovedActionTaken = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
                        log.info("auto approved by delight through system for exchange only:{} ,incrementId:{}", isApprovedActionTaken, refundRequestResponse.getIncrementId());
                    }

                }
            }
        } catch (Exception e) {
            log.error("[mvcAutoApproveReturnExchange] function, Exception message while approving return in  MvcAutoApproveReturnThreadPool = {} incrementId: {}", "scm-return-orders", refundRequestResponse.getIncrementId(), e);
        }

    }

    public NeedApprovalRequest createNeedApprovalRequest(ReturnCreationResponse refundRequestResponse) {
        NeedApprovalRequest needApprovalRequest = new NeedApprovalRequest();
        needApprovalRequest.setReturnId(refundRequestResponse.getResult().getReturns().get(0).getReturnId());
        needApprovalRequest.setStatus("return_accepted");
        needApprovalRequest.setDelightAction("APPROVE");
        needApprovalRequest.setComments("Pending approval return_accepted auto approved by system : <EMAIL> with reason - mvc customer");
        needApprovalRequest.setDelightComment("AutoApproved");
        needApprovalRequest.setUsername("system");

        ApprovalStatusRequest approvalStatusRequest = new ApprovalStatusRequest();
        approvalStatusRequest.setReturnStatus("return_accepted");
        approvalStatusRequest.setMagentoItemId(refundRequestResponse.getResult().getReturns().get(0).getMagentoItemId());
        approvalStatusRequest.setRetryCount(0);
        approvalStatusRequest.setSelectedRefundAction("refund");//confirm with Jitender sir if it should have value "exchange/refund/'' "

        needApprovalRequest.setApprovalStatusRequest(approvalStatusRequest);
        return needApprovalRequest;
    }

    public void populateUwOrderAndUwOrderWH(ReturnCreationResponse returnCreationResponse) {
        UwOrderDTO uwOrder = returnCreationResponse.getUwOrder();
        PurchaseOrderDetailsDTO orderResponse = returnCreationResponse.getPurchaseOrderDetailsDTO();
        if (uwOrder != null && "B2B".equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            boolean isNEXSFacility = nexsFacilityService.getNexsFacilities().contains(uwOrder.getFacilityCode());
            boolean isDealsKartFacility = dealskartfacility.equalsIgnoreCase(uwOrder.getFacilityCode());

            if (isDealsKartFacility || isNEXSFacility) {
                returnCreationResponse.setUwOrderWH(uwOrder); //uwOrder is Warehouse version

                //set other version then warehouse version
                returnCreationResponse.setUwOrder(orderResponse.getUwOrders().stream().filter(uw -> uwOrder.getUwItemId().equals(uw.getB2bRefrenceItemId())).findFirst().orElse(null));
            } else {
                //set warehouse version
                returnCreationResponse.setUwOrderWH(orderResponse.getUwOrders().stream().filter(uw -> uwOrder.getUwItemId().equals(uw.getB2bRefrenceItemId())).findFirst().orElse(null));
            }
        }
    }


    public List<String> refundMethodsFromRefundService(UwOrderDTO uwOrder, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO) {
        List<String> eligibleRefundMethods = null;
        if (purchaseOrderDetailsDTO != null) {
            OrdersHeaderDTO ordersHeader = purchaseOrderDetailsDTO.getOrdersHeaderDTO();
            OrdersDTO order = purchaseOrderDetailsDTO.getOrders().stream().filter(o -> uwOrder.getItemId() == o.getItemId()).findFirst().orElse(null);
            if (order != null) {
                String paymentMethod = orderUtilsService.getPaymentMethod(uwOrder, purchaseOrderDetailsDTO.getUwOrders(), purchaseOrderDetailsDTO.getOrders(), order.getMethod());
                RefundMethodResponse refundMethodResponse = refundUtilService.getRefundMethod("auto_approval_at_pos", ordersHeader.getPaymentMode(), paymentMethod, ordersHeader.getLkCountry(), uwOrder.getIncrementId());
                eligibleRefundMethods = refundMethodResponse != null ? refundMethodResponse.getEligibleRefundMethod() : null;
            }
        }

        if (!CollectionUtils.isEmpty(eligibleRefundMethods)) {
            return eligibleRefundMethods;
        } else {
            return new ArrayList<>();
        }

    }

    private boolean isProductEyeGlass(String classificationId) {
        if (classificationId != null && !classificationId.isEmpty()) {
            return Integer.valueOf(classificationId).equals(11355);

        }
        return false;
    }


    private boolean checkMvcCustomer(Integer incrementId, int mvcMinimumValue, Long customerId) {
        log.info("[MvcAutoApproveReturnThreadpool] customerId:{}", customerId);
        CustomerStoreProfileResponse mvcResponseDTO = mvcService.getMvcMvsStats(customerId, null);
        MvcOrderDTO mvcOrderResponseOld = mvcService.getMvcOrdersResponse("ORDER_ID", String.valueOf(incrementId));

        if (mvcOrderResponseOld != null) {
            if (mvcResponseDTO == null) {
                mvcResponseDTO = new CustomerStoreProfileResponse();
            }
            mvcResponseDTO.setCustomerScore(mvcOrderResponseOld.getCustomerScore());
        }

        log.info("customer score:{}", mvcResponseDTO.getCustomerScore());
        return mvcResponseDTO.getCustomerScore() != null && mvcResponseDTO.getCustomerScore() > mvcMinimumValue;

    }

}
