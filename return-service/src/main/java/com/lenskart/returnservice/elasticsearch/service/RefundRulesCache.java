package com.lenskart.returnservice.elasticsearch.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Result;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.msearch.MultiSearchResponseItem;
import co.elastic.clients.elasticsearch.core.msearch.MultisearchBody;
import co.elastic.clients.elasticsearch.core.msearch.MultisearchHeader;
import co.elastic.clients.elasticsearch.core.msearch.RequestItem;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.lenskart.returncommon.cache.CacheLoadNotifier;
import com.lenskart.returncommon.cache.CacheLoadObserver;
import com.lenskart.returncommon.model.dto.DecisionTableRefundDTO;
import com.lenskart.returncommon.model.enums.RefundRulesAttributesEnum;
import com.lenskart.returncommon.model.response.ReturnPolicyResponse;
import com.lenskart.returnrepository.entity.RefundRules;

import com.lenskart.returnservice.elasticsearch.config.ElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import static com.lenskart.returncommon.utils.Constant.REFUND_RULES_CONSTANT.WEB;

@Slf4j
@Service
public class RefundRulesCache {

    @Autowired
    private ElasticSearchService elasticSearchService;
    private static final String REFUND_RULES_INDEX = "refund_rules";
    private final CacheLoadNotifier notifier = new CacheLoadNotifier();


    @Async
    public void indexRefundRules(List<RefundRules> refundRulesList) {
        log.info("indexing refund rules with size :{}", refundRulesList.size());
        try {
            BulkRequest.Builder br = new BulkRequest.Builder();

            for (RefundRules refundRules : refundRulesList) {
                if (refundRules != null) {
                    br.operations(op -> op
                            .index(idx -> idx
                                    .index(REFUND_RULES_INDEX)
                                    .id(Objects.toString(refundRules.getId()))
                                    .document(refundRules)
                            )
                    );
                }
            }

            BulkResponse result = elasticSearchService.getClient().bulk(br.build());

            // Log errors, if any
            if (result.errors()) {
                log.error("Bulk had errors");
                for (BulkResponseItem item : result.items()) {
                    if (item.error() != null) {
                        log.error(item.error().reason());
                    }
                }
            }
            notifier.notifyObservers();
        } catch (Exception e) {
            log.error("Exception occurred while indexing refund rules in elastic search", e);
        }
    }

    public RefundRules getRefundRulesById(DecisionTableRefundDTO decisionTableRefundDTO) {
        RefundRules matchingDocument = null;

        try {
            // Build the query
            List<Query> mustQueries = new ArrayList<>();
            populateCommonMustQueries(decisionTableRefundDTO, mustQueries);

            BoolQuery boolQuery = BoolQuery.of(b -> b.must(mustQueries));

            // Create the search request
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(REFUND_RULES_INDEX)
                    .query(q -> q.bool(boolQuery))
                    .sort(s -> s.field(f -> f.field(RefundRulesAttributesEnum.ID.getAttributeName()).order(SortOrder.Asc)))
                    .size(1) // We need only the first matching document
                    .build();

            // Execute the search
            SearchResponse<RefundRules> searchResponse = elasticSearchService.getClient().search(searchRequest, RefundRules.class);

            List<Hit<RefundRules>> hits = searchResponse.hits().hits();
            if (!hits.isEmpty()) {
                matchingDocument = hits.get(0).source();
            }

        } catch (IOException e) {
            log.error("Exception occurred while searching for refund rules {}", e, e.getMessage());
        }

        return matchingDocument;
    }

    public Map<Integer, ReturnPolicyResponse> getRefundRulesByIdBulk(Map<Integer, DecisionTableRefundDTO> decisionTableRefundDTOMap) {
        log.info("Entering getRefundRulesByIdBulk with {} requests", decisionTableRefundDTOMap.size());

        if (decisionTableRefundDTOMap.isEmpty()) {
            log.warn("Empty input map, returning early.");
            return Collections.emptyMap();
        }

        Map<Integer, ReturnPolicyResponse> responseMap = new HashMap<>();

        try {
            ElasticsearchClient esClient = elasticSearchService.getEsClientWithCustomTimeout();
            MsearchRequest.Builder msearchBuilder = new MsearchRequest.Builder().index(REFUND_RULES_INDEX);

            Map<Integer, Integer> requestIndexMap = new HashMap<>();
            List<RequestItem> requestItemList = new ArrayList<>();
            int queryIndex = 0;

            for (Map.Entry<Integer, DecisionTableRefundDTO> entry : decisionTableRefundDTOMap.entrySet()) {
                Integer requestKey = entry.getKey();
                DecisionTableRefundDTO dto = entry.getValue();

                List<Query> mustQueries = new ArrayList<>();
                populateCommonMustQueries(dto, mustQueries);

                MultisearchBody multiSearchQuery = new MultisearchBody.Builder()
                        .size(1)
                        .query(q -> q.bool(b -> b.must(mustQueries)))
                        .build();

                requestItemList.add(
                        new RequestItem.Builder()
                                .header(new MultisearchHeader.Builder().build())
                                .body(multiSearchQuery)
                                .build()
                );
                requestIndexMap.put(queryIndex++, requestKey);
            }

            if (requestItemList.isEmpty()) {
                log.warn("No valid queries generated.");
                decisionTableRefundDTOMap.keySet().forEach(k -> responseMap.put(k, getDefaultReturnPolicy(decisionTableRefundDTOMap.get(k))));
                return responseMap;
            }

            msearchBuilder.searches(requestItemList);

            log.info("Executing _msearch with {} queries", requestIndexMap.size());

            long startTime = System.currentTimeMillis();
            MsearchResponse<RefundRules> msearchResponse = esClient.msearch(msearchBuilder.build(), RefundRules.class);
            long executionTime = System.currentTimeMillis() - startTime;
            log.info("_msearch completed in {} ms", executionTime);

            List<MultiSearchResponseItem<RefundRules>> responses = msearchResponse.responses();

            for (int i = 0; i < responses.size(); i++) {
                Integer requestKey = requestIndexMap.get(i);
                MultiSearchResponseItem<RefundRules> searchItem = responses.get(i);

                if (searchItem.isFailure()) {
                    responseMap.put(requestKey, getDefaultReturnPolicy(decisionTableRefundDTOMap.get(i)));
                    continue;
                }

                if (searchItem.result().hits().hits().isEmpty()) {
                    log.info("No hit found for requestKey {}", requestKey);
                    responseMap.put(requestKey, getDefaultReturnPolicy(decisionTableRefundDTOMap.get(i)));
                    continue;
                }

                RefundRules refundRule = searchItem.result().hits().hits().get(0).source();
                responseMap.put(requestKey, convertIntoReturnPolicy(refundRule));
            }

        } catch (Exception e) {
            log.error("Exception in getRefundRulesByIdBulk: {}", e.getMessage(), e);
            // Ensure fallback for ALL keys
            decisionTableRefundDTOMap.keySet().forEach(k -> responseMap.put(k, getDefaultReturnPolicy(decisionTableRefundDTOMap.get(k))));
        }

        return responseMap;
    }





    private ReturnPolicyResponse convertIntoReturnPolicy(RefundRules refundRules){
        ReturnPolicyResponse returnPolicyResponse = new ReturnPolicyResponse();
        returnPolicyResponse.setReturn_days(refundRules.getReturnEligibilityPeriod());
        returnPolicyResponse.setExchange_days(refundRules.getReturnEligibilityPeriod());
        returnPolicyResponse.setWarranty_days(refundRules.getWarantyTo());
        return returnPolicyResponse;
    }

    private ReturnPolicyResponse getDefaultReturnPolicy(DecisionTableRefundDTO decisionTableRefundDTO) {
        ReturnPolicyResponse returnPolicyResponse = new ReturnPolicyResponse();

        boolean isBrandedWebSource = decisionTableRefundDTO != null && Boolean.TRUE.equals(decisionTableRefundDTO.getIsBranded())
                && WEB.equalsIgnoreCase(decisionTableRefundDTO.getReturnInitiatedSource());

        if (isBrandedWebSource) {
            returnPolicyResponse.setReturn_days(0);
            returnPolicyResponse.setExchange_days(0);
            returnPolicyResponse.setWarranty_days(190);
        } else {
            returnPolicyResponse.setReturn_days(14);
            returnPolicyResponse.setExchange_days(14);
            returnPolicyResponse.setWarranty_days(365);
        }

        return returnPolicyResponse;
    }


    public void deleteDocument(RefundRules refundRule) {
        ElasticsearchClient client = elasticSearchService.getClient();
        try {
            DeleteRequest request = DeleteRequest.of(d -> d
                    .index(REFUND_RULES_INDEX)
                    .id(Objects.toString(refundRule.getId()))
            );
            DeleteResponse deleteResponse = client.delete(request);
            // Log response if needed
        } catch (IOException e) {
            log.error("Error deleting document for ruleId: " + refundRule.getId(), e);
        }
    }

    public boolean addDocument(RefundRules refundRule) {
        ElasticsearchClient client = elasticSearchService.getClient();
        try {
            IndexRequest<RefundRules> request = IndexRequest.of(i -> i
                    .index(REFUND_RULES_INDEX)
                    .id(Objects.toString(refundRule.getId()))
                    .document(refundRule)
            );
            IndexResponse response = client.index(request);
            // Log response if needed
            return response.result().equals(Result.Created);
        } catch (IOException e) {
            log.error("Error adding document for ruleId: " + refundRule.getId(), e);
            return false;
        }
    }


    public boolean updateDocument(RefundRules refundRule) {
        ElasticsearchClient client = elasticSearchService.getClient();
        try {
            UpdateRequest<RefundRules, RefundRules> request = UpdateRequest.of(u -> u
                    .index(REFUND_RULES_INDEX)
                    .id(Objects.toString(refundRule.getId()))
                    .doc(refundRule)
            );
            UpdateResponse<RefundRules> response = client.update(request, RefundRules.class);
            // Log response if needed
            return response.result().equals(Result.Updated);
        } catch (IOException e) {
            log.error("Error updating document for ruleId: " + refundRule.getId(), e);
            return false;
        }
    }

    public RefundRules convertHitToRefundRules(Hit<Object> hit) {
        if (hit == null) {
            return null;
        }

        RefundRules refundRules = new RefundRules();

        Map<String, Object> source = (Map<String, Object>) hit.source();

        refundRules.setId(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.ID.getAttributeName()))));
        refundRules.setReverseType((String) source.get(RefundRulesAttributesEnum.REVERSE_TYPE.getAttributeName()));
        refundRules.setNavChannel((String) source.get(RefundRulesAttributesEnum.NAV_CHANNEL.getAttributeName()));
        refundRules.setReturnInitiatedSource((String) source.get(RefundRulesAttributesEnum.RETURN_INITIATE_SOURCE.getAttributeName()));
        refundRules.setTriggerPoint((String) source.get(RefundRulesAttributesEnum.TRIGGER_POINT.getAttributeName()));
        refundRules.setIsPsuedoGatepass(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.IS_PSEUDO_GATEPASS.getAttributeName())));
        refundRules.setIsBranded(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.IS_BRANDED.getAttributeName())));
        refundRules.setIsQcPass(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.IS_QC_PASS.getAttributeName())));
        refundRules.setCategory((String) source.get(RefundRulesAttributesEnum.CATEGORY.getAttributeName()));
        refundRules.setIsAccessoryMissing(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.IS_ACCESSORY_MISSING.getAttributeName())));
        refundRules.setIsLastPiece(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.IS_LAST_PIECE.getAttributeName())));
        refundRules.setIsLensOnly(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.IS_LENS_ONLY.getAttributeName())));
        refundRules.setCountryCode((String) source.get(RefundRulesAttributesEnum.COUNTRY_CODE.getAttributeName()));
        refundRules.setInsurancePolicy(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.INSURANCE_POLICY.getAttributeName())));
        refundRules.setBlacklistedPhoneNumbers(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.BLACKLISTED_PHONE_NUMBERS.getAttributeName())));
        refundRules.setBlacklistedPincodes(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.BLACKLISTED_PINCODES.getAttributeName())));
        refundRules.setDraftReturnMethod((String) source.get(RefundRulesAttributesEnum.DRAFT_RETURN_METHOD.getAttributeName()));
        refundRules.setReturnReasons((String) source.get(RefundRulesAttributesEnum.RETURN_REASONS.getAttributeName()));
        refundRules.setPaymentMethod((String) source.get(RefundRulesAttributesEnum.PAYMENT_METHOD.getAttributeName()));
        refundRules.setExchangeAllowed(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.EXCHANGE_ALLOWED.getAttributeName()))));
        refundRules.setWarantyFrom(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.WARRANTY_FROM.getAttributeName()))));
        refundRules.setWarantyTo(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.WARRANTY_TO.getAttributeName()))));
        refundRules.setAmountFrom(Long.parseLong(String.valueOf(source.get(RefundRulesAttributesEnum.AMOUNT_FROM.getAttributeName()))));
        refundRules.setAmountTill(Long.parseLong(String.valueOf(source.get(RefundRulesAttributesEnum.AMOUNT_TILL.getAttributeName()))));
        refundRules.setIsReturnable(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.IS_RETURNABLE.getAttributeName())));
        refundRules.setAction((String) source.get(RefundRulesAttributesEnum.ACTION.getAttributeName()));
        refundRules.setDoRefund(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.DO_REFUND.getAttributeName())));
        refundRules.setRefundMethod((String) source.get(RefundRulesAttributesEnum.REFUND_METHOD.getAttributeName()));
        refundRules.setExchangeOrderDispatch((String) source.get(RefundRulesAttributesEnum.EXCHANGE_DISPATCH_POINT.getAttributeName()));
        refundRules.setRefundDispatch((String) source.get(RefundRulesAttributesEnum.REFUND_DISPATCH_POINT.getAttributeName()));
        refundRules.setReturnEligibilityPeriod(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.RETURN_ELIGIBILITY_PERIOD.getAttributeName()))));
        refundRules.setOverrideWarrantyPeriod(Boolean.TRUE.equals(source.get(RefundRulesAttributesEnum.OVERRIDE_WARRANTY_PERIOD.getAttributeName())));
        refundRules.setCustomerScoreFrom(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.CUSTOMER_SCORE_FROM.getAttributeName()))));
        refundRules.setCustomerScoreTo(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.CUSTOMER_SCORE_TO.getAttributeName()))));
        refundRules.setStoreScoreFrom(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.STORE_SCORE_FROM.getAttributeName()))));
        refundRules.setStoreScoreTo(Integer.parseInt(String.valueOf(source.get(RefundRulesAttributesEnum.STORE_SCORE_TO.getAttributeName()))));

        return refundRules;
    }

    public RefundRules getRefundRulesByIdAndNotOverrideWarranty(DecisionTableRefundDTO decisionTableRefundDTO) {
        RefundRules matchingDocument = null;

        try {
            // Build the query
            List<Query> mustQueries = new ArrayList<>();
            populateCommonMustQueries(decisionTableRefundDTO, mustQueries);

            mustQueries.add(TermQuery.of(t -> t.field(RefundRulesAttributesEnum.OVERRIDE_WARRANTY_PERIOD.getAttributeName()).value(false))._toQuery());

            BoolQuery boolQuery = BoolQuery.of(b -> b.must(mustQueries));

            // Create the search request
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(REFUND_RULES_INDEX)
                    .query(q -> q.bool(boolQuery))
                    .sort(s -> s.field(f -> f.field(RefundRulesAttributesEnum.ID.getAttributeName()).order(SortOrder.Asc)))
                    .size(1) // We need only the first matching document
                    .build();

            // Execute the search
            SearchResponse<RefundRules> searchResponse = elasticSearchService.getClient().search(searchRequest, RefundRules.class);

            List<Hit<RefundRules>> hits = searchResponse.hits().hits();
            if (!hits.isEmpty()) {
                matchingDocument = hits.get(0).source();
            }

        } catch (IOException e) {
            log.error("Exception occurred while searching for refund rules {}", e, e.getMessage());
        }

        return matchingDocument;
    }

    private static void populateCommonMustQueries(DecisionTableRefundDTO decisionTableRefundDTO, List<Query> mustQueries) {
        if (decisionTableRefundDTO.getReverseType() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.REVERSE_TYPE.getAttributeName())
                            .value(decisionTableRefundDTO.getReverseType().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getNavChannel() != null) {
            mustQueries.add(MatchQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.NAV_CHANNEL.getAttributeName())
                            .query(decisionTableRefundDTO.getNavChannel().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getReturnInitiatedSource() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.RETURN_INITIATE_SOURCE.getAttributeName())
                            .value(decisionTableRefundDTO.getReturnInitiatedSource().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getTriggerPoint() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.TRIGGER_POINT.getAttributeName())
                            .value(decisionTableRefundDTO.getTriggerPoint().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getIsPseudoGatepass() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.IS_PSEUDO_GATEPASS.getAttributeName())
                            .value(decisionTableRefundDTO.getIsPseudoGatepass()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getIsBranded() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.IS_BRANDED.getAttributeName())
                            .value(decisionTableRefundDTO.getIsBranded()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getIsQcPass() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.IS_QC_PASS.getAttributeName())
                            .value(decisionTableRefundDTO.getIsQcPass()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getCategory() != null) {
            mustQueries.add(MatchQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.CATEGORY.getAttributeName())
                            .query(decisionTableRefundDTO.getCategory().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getIsAccessoryMissing() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.IS_ACCESSORY_MISSING.getAttributeName())
                            .value(decisionTableRefundDTO.getIsAccessoryMissing()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getIsLastPiece() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.IS_LAST_PIECE.getAttributeName())
                            .value(decisionTableRefundDTO.getIsLastPiece()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getIsLensOnly() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.IS_LENS_ONLY.getAttributeName())
                            .value(decisionTableRefundDTO.getIsLensOnly()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getCountryCode() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.COUNTRY_CODE.getAttributeName())
                            .value(decisionTableRefundDTO.getCountryCode().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getInsurancePolicy() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.INSURANCE_POLICY.getAttributeName())
                            .value(decisionTableRefundDTO.getInsurancePolicy()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getBlacklistedPhoneNumbers() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.BLACKLISTED_PHONE_NUMBERS.getAttributeName())
                            .value(decisionTableRefundDTO.getBlacklistedPhoneNumbers()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getBlacklistedPincodes() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.BLACKLISTED_PINCODES.getAttributeName())
                            .value(decisionTableRefundDTO.getBlacklistedPincodes()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getDraftReturnMethod() != null) {
            mustQueries.add(TermQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.DRAFT_RETURN_METHOD.getAttributeName())
                            .value(decisionTableRefundDTO.getDraftReturnMethod().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getReturnReasons() != null) {
            mustQueries.add(MatchQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.RETURN_REASONS.getAttributeName())
                            .query(decisionTableRefundDTO.getReturnReasons().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getPaymentMethod() != null) {
            mustQueries.add(MatchQuery.of(t -> t
                            .field(RefundRulesAttributesEnum.PAYMENT_METHOD.getAttributeName())
                            .query(decisionTableRefundDTO.getPaymentMethod().toLowerCase()))
                    ._toQuery());
        }

        if (decisionTableRefundDTO.getExchangeAllowed() != null) {
            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.EXCHANGE_ALLOWED.getAttributeName())
                            .gte(JsonData.of(decisionTableRefundDTO.getExchangeAllowed())))));
        }

        if (decisionTableRefundDTO.getReturnPeriod() != null) {
            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.WARRANTY_FROM.getAttributeName())
                            .lte(JsonData.of(decisionTableRefundDTO.getReturnPeriod())))));

            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.WARRANTY_TO.getAttributeName())
                            .gte(JsonData.of(decisionTableRefundDTO.getReturnPeriod())))));
        }

        if (decisionTableRefundDTO.getAmountValidity() != null) {
            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.AMOUNT_FROM.getAttributeName())
                            .lte(JsonData.of(decisionTableRefundDTO.getAmountValidity())))));

            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.AMOUNT_TILL.getAttributeName())
                            .gte(JsonData.of(decisionTableRefundDTO.getAmountValidity())))));
        }

        if (decisionTableRefundDTO.getCustomerScore() != null) {
            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.CUSTOMER_SCORE_FROM.getAttributeName())
                            .lte(JsonData.of(decisionTableRefundDTO.getCustomerScore())))));

            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.CUSTOMER_SCORE_TO.getAttributeName())
                            .gte(JsonData.of(decisionTableRefundDTO.getCustomerScore())))));
        }

        if (decisionTableRefundDTO.getStoreScore() != null) {
            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.STORE_SCORE_FROM.getAttributeName())
                            .lte(JsonData.of(decisionTableRefundDTO.getStoreScore())))));

            mustQueries.add(Query.of(q -> q
                    .range(r -> r.field(RefundRulesAttributesEnum.STORE_SCORE_TO.getAttributeName())
                            .gte(JsonData.of(decisionTableRefundDTO.getStoreScore())))));
        }
    }

    public void registerCacheObserver(CacheLoadObserver observer) {
        notifier.registerObserver(observer);
    }
}
