package com.lenskart.returnservice.elasticsearch.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponseInterceptor;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;


@Slf4j
@Component
public class ElasticSearchService {

    @Value("${elastic.hosts.new:http://es-app.infra.svc.cluster.local:9200}")
    private String serverUrls;

    private ElasticsearchClient esClient;

    private ElasticsearchClient esClientWithCustomTimeout;

    @Value("${elastic.socket.timeout:100}")
    private int elasticCustomSocketTimeout;

    @PostConstruct
    public void init() {
        this.esClient = makeConnection();
        this.esClientWithCustomTimeout = makeConnection(elasticCustomSocketTimeout);
    }

    private ElasticsearchClient makeConnection() {
        log.info("[makeConnection] Establish the Elastic connections");
        try {

            HttpHost[] hosts = Arrays.stream(serverUrls.split(","))
                    .map(HttpHost::create)
                    .toArray(HttpHost[]::new);



            RestClientBuilder builder = RestClient.builder(hosts)
                    .setHttpClientConfigCallback(httpClientBuilder -> {

                        httpClientBuilder.disableAuthCaching();

                        httpClientBuilder.setDefaultHeaders(List.of(
                                new BasicHeader(
                                        HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString())));

                        httpClientBuilder.addInterceptorLast((HttpResponseInterceptor)
                                (response, context) -> response.addHeader("X-Elastic-Product", "Elasticsearch"));

                        httpClientBuilder
                                .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                                .setDefaultIOReactorConfig(IOReactorConfig.custom()
                                        .setIoThreadCount(1)
                                        .build());
                        return httpClientBuilder;
                    });

            RestClient restClient = builder.build();

            ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());

            this.esClient = new ElasticsearchClient(transport);
            log.info("[makeConnection] successfully created the ES client");
        } catch (Exception e) {
            log.error("Exception occurred while generating elastic connection :{}", e.getMessage(), e);
        }
        return this.esClient;
    }

    private ElasticsearchClient makeConnection(int socketTimeoutInMillis) {
        log.info("[makeConnection] Establishing ES client with socketTimeout={}ms", socketTimeoutInMillis);
        try {

            HttpHost[] hosts = Arrays.stream(serverUrls.split(","))
                    .map(HttpHost::create)
                    .toArray(HttpHost[]::new);


            RestClientBuilder builder = RestClient.builder(hosts)
                    .setHttpClientConfigCallback(httpClientBuilder -> {
                        httpClientBuilder.disableAuthCaching();

                        httpClientBuilder.setDefaultHeaders(List.of(
                                new BasicHeader(
                                        HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString())));

                        httpClientBuilder.addInterceptorLast((HttpResponseInterceptor)
                                (response, context) -> response.addHeader("X-Elastic-Product", "Elasticsearch"));

                        httpClientBuilder.setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy());

                        httpClientBuilder.setDefaultRequestConfig(RequestConfig.custom()
                                .setConnectTimeout(500)
                                .setSocketTimeout(socketTimeoutInMillis)
                                .setConnectionRequestTimeout(500)
                                .build());

                        return httpClientBuilder;
                    });


            RestClient restClient = builder.build();
            ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());

            return new ElasticsearchClient(transport);
        } catch (Exception e) {
            log.error("Exception while creating ES client with timeout {}ms: {}", socketTimeoutInMillis, e.getMessage(), e);
            return null;
        }
    }

    public ElasticsearchClient getClient() {
        if (esClient == null) {
            esClient = makeConnection();
        }
        return esClient;
    }

    public ElasticsearchClient getEsClientWithCustomTimeout() {
        if (esClientWithCustomTimeout == null) {
            esClientWithCustomTimeout = makeConnection();
        }
        return esClientWithCustomTimeout;
    }
}
