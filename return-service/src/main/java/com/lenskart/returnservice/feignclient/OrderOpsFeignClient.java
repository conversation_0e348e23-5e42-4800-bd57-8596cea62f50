package com.lenskart.returnservice.feignclient;

import com.lenskart.core.model.Classification;
import com.lenskart.core.model.OrderItemGSTDetail;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.UniReportStockEntry;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.request.*;
import com.lenskart.ordermetadata.dto.response.*;
import com.lenskart.ordermetadata.dto.response.ReturnDetailsDTO;
import com.lenskart.returncommon.model.dto.ExchangeItemDispatchableDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequest;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.orderops.model.Amount;
import com.lenskart.orderops.model.Canceledorders;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.orderops.model.S3InvoiceDetails;
import com.lenskart.orderops.model.SbrtOrderItem;
import com.lenskart.returncommon.model.dto.UnicomApiLogDto;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.request.BackSyncRequest;
import com.lenskart.returncommon.model.request.CancelRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.model.response.ItemWiseFastRefunResponse;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;
import org.jsondoc.core.annotation.*;
import org.jsondoc.core.pojo.ApiVisibility;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequestDTO;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@FeignClient(name = "orderOpsFeignClient", url = "${feign.client.domain.order-ops:https://order-ops.scm.preprod.lenskart.com/}")
public interface OrderOpsFeignClient {

    @GetMapping("order/{order_id}/details")
    ResponseEntity<OrderInfoResponseDTO> getOrderDetails(@PathVariable("order_id") Integer orderId);

    @GetMapping("order/{order_id}/details/v2")
    ResponseEntity<OrderInfoResponseDTO> getOrderDetailsV2(@PathVariable("order_id") Integer orderId, @RequestParam(value = "required_dto", required = false) List<OrderInfoDtoType> requiredDTOs);

    @GetMapping("order/shipping-details")
    ResponseEntity<List<ShippingStatusDetail>> getShippingDetails(@RequestParam(value = "order_id") Integer orderId, @RequestParam(value = "unicom_order_code") String unicomOrderCode);

    @GetMapping("order/exchange-cancellation-details")
    ResponseEntity<OrderExchangeCancellationDetails> getExchangeAndCancellationDetails(@RequestParam(value = "uw_item_id", required = false) Integer uwItemId, @RequestParam(value = "return_id", required = false) Integer returnId);

    @GetMapping("/insert-order-comment/{order_id}")
    ResponseEntity<Boolean> insertOrderComment(@RequestBody Map<String, String> comment, @PathVariable("order_id") Integer orderId);

    @GetMapping("/return/item-resolution-details")
    ResponseEntity<ItemResolutionFlatDTO> getItemResolutionFlat(@RequestParam(value = "uw_item_id", required = false) Integer uwItemId, @RequestParam(value = "return_id") Integer returnId, @RequestParam(value = "resolution") String resolution);

    @GetMapping("/get-master-order-details/{uwItemId}")
    ResponseEntity<MasterOrderDetailsDTO> getMasterOrderDetails(@PathVariable("uwItemId") Long uwItemId);

    @GetMapping(value = "return/cancellable/get/{order_id}")
    ResponseEntity<Boolean> getIsOrderCancellable(@NotNull @PathVariable(value = "order_id") Integer orderId);

    @GetMapping(value = "refund-method/intent/{uw_item_id}/{return_id}")
    ResponseEntity<RefundIntentDTO> getRefundMethodIntentByCustomer(@PathVariable("uw_item_id") Integer uwItemId, @PathVariable("return_id") Integer returnId);

    @PostMapping(value = "/exchange-item/dispatchable")
    Boolean isExchangeItemDispatchable(@RequestBody ExchangedItem exchangedItem);

    @GetMapping(value = "order/{identifier-type}/{identifier}/details")
    ResponseEntity<PurchaseOrderDetailsDTO> getPurchaseOrderDetails(@PathVariable("identifier-type") String identifierType, @PathVariable("identifier") String identifierValue);

    @GetMapping(value = "order/dispensing/{increment_id}/details")
    ResponseEntity<DispensingDTO> getDispensingDetails(@PathVariable(value = "increment_id") Integer incrementId);

    @GetMapping(value = "return/reverse-courier/meta-data")
    ResponseEntity<ReverseCourierMetaData> getReverseCourierMetaData(@RequestBody ReturnCreationRequest returnCreationRequest);

    @GetMapping(value = "order/address/{order_id}/{address_type}")
    ResponseEntity<OrderAddressUpdateDTO> getOrderAddressUpdate(@PathVariable(value = "order_id") Integer orderId, @PathVariable("address_type") String addressType);

    @GetMapping(value = "v2.0/itemwise-fastrefund/{incrementId}")
    ResponseEntity<ItemWiseFastRefunResponse> getRefundHistoryV2(@PathVariable("incrementId") Integer incrementId);

    @RequestMapping(value = "hub/hub-details", method = RequestMethod.GET)
    Map<String, Object> getHubDetails(@RequestParam("searchBy") String searchBy, @RequestParam("value") String value);

    @RequestMapping(value = "order/sync/update/{increment_id}", method = RequestMethod.POST)
    public ResponseEntity<Boolean> saveOrderSyncDetails(@NonNull @PathVariable(value = "increment_id") Integer incrementId, @RequestParam("status") String status, @RequestParam("tracking_no") String trackingNo );

    @RequestMapping(value = "refund/request/update", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<RefundRequestResponseDTO> initiateRefundProcess(@RequestBody RefundRequestDTO refundRequestDTO );

    @RequestMapping(value = "order/back/sync", method = RequestMethod.PUT)
    public @ApiResponseObject ResponseEntity<Boolean> callBackSyncApi(@RequestBody List<Integer> uwItemIds,@RequestParam("status") String trackingStatus);

    @RequestMapping(value = "order/dispensing/create/{increment_id}/{group_id}", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<Boolean> markOrderDispensing(@PathVariable("increment_id") Integer incrementId,@PathVariable("group_id") Long groupId);

    @RequestMapping(value = "refund/intent-request/get/{uw_item_id}/{return_id}", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<RefundIntentRequestDTO> getRefundIntentRequest(@PathVariable("uw_item_id") Integer uwItemId,@PathVariable("return_id") Integer returnId);

    @RequestMapping(value = "order/status/updates", method = RequestMethod.PUT)
    public @ApiResponseObject ResponseEntity<Void> statusUpdate(@RequestBody List<Integer> uwItemIds);

    @RequestMapping(value = "return/communication", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<Void> sendReturnCommunication(@RequestBody ReturnCommunicationDTO returnCommunicationDTO);

    @ApiAuthToken(roles = "ADMIN,CLIENT", scheme = "Bearer")
    @ApiMethod(path = "{incrementId}/cancel-invoice", description = "Cancels provided order and initiate refund.", produces = {
            MediaType.APPLICATION_JSON_VALUE}, responsestatuscode = "202- if success <br/> 404 - in case if given parameters are incorrect", visibility = ApiVisibility.PRIVATE)
    @RequestMapping(value = "{incrementId}/cancel-invoice", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.ACCEPTED)
    @ResponseBody
    Map<String, String> orderCancel(@RequestBody CancelRequest cancelRequest,
                                    @PathVariable @ApiPathParam(name = "incrementId", description = "Increment Id of order") Integer incrementId,
                                    @RequestParam(value = "newFlowEnabled", defaultValue = "false") boolean newFlowEnabled);

    @ApiAuthToken(roles = "ADMIN,CLIENT", scheme = "Bearer")
    @ApiMethod(path = "{incrementId}/backSyncUpdate", description = "Send order back sync updates to JUNO", consumes = {
            MediaType.ALL_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @RequestMapping(value = "{incrementId}/backSyncUpdate", method = RequestMethod.PUT)
    @ResponseStatus(value = HttpStatus.ACCEPTED)
    @ApiResponseObject
    ResponseEntity<String> backSyncUpdate(
            @PathVariable @ApiPathParam(name = "incrementId", description = "incrementId of order") Integer incrementId,
            @ApiPathParam(description = "Request for shipping address update") @RequestBody BackSyncRequest backSyncRequest);

    //todo need to make api at order-ops
    @RequestMapping(value = "hub-facilities", method = RequestMethod.GET)
    ResponseEntity<List<String>> getHubFacilities();

    //todo need to make order-ops api to save dispensing order
    @ApiMethod(path = "order/dispensing/update/", description = "make entry in dispensing order", produces = {
            MediaType.APPLICATION_JSON_VALUE}, responsestatuscode = "200")
    @RequestMapping(value = "order/dispensing/create/{increment_id}/{group_id}", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<Boolean> updateDispensingOrder(@RequestBody()DispensingDTO dispensingDTO);

    @GetMapping("uw_order/{magento_item_id}/details")
    ResponseEntity<UwOrderDTO> getUwOrderByMagentoItemId(@PathVariable("magento_item_id") Integer magento_item_id);

    @ApiMethod(path = "/refund-point/dispatchable", description = "make entry in dispensing order", produces = {
            MediaType.APPLICATION_JSON_VALUE }, responsestatuscode = "200")
    @RequestMapping(value = "/refund-point/dispatchable", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<RefundDispatchResponse>  refundDispatchResponse(@RequestBody RefundDispatchRequest refundDispatchRequest);


    @RequestMapping(value = "get/item/amount", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<ItemWiseAmountDTO> getItemAmount(@RequestBody ItemAmountGetRequestDTO itemAmountGetRequestDTO);


    @RequestMapping(value = "refund/request/create", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<Boolean> createRefundRequest(@RequestBody OldRefundRequestCreateDTO oldRefundRequestCreateDTO);

    @RequestMapping(value = "orders/statuses/update", method = RequestMethod.PUT)
    public @ApiResponseObject ResponseEntity<Boolean>  statusUpdate(@RequestBody OrderStatusUpdateDetails request);

    @RequestMapping(value = "saveUnicomApiLog",method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<Boolean> saveUnicomApiLog(@RequestBody UnicomApiLogDto unicomApiLogDto);

    @RequestMapping(value = "refund-switch-active/{identifierType}/{identifierValue}", method = RequestMethod.GET)
    public ResponseEntity<CheckRefundSwitchActiveDTO> checkRefundSwitchActive(@PathVariable("identifierType") String identifierType, @PathVariable("identifierValue") String identifierValue);

    @RequestMapping(value = "{orderId}/refund-amount/{returnId}", method = RequestMethod.GET)
    public @ApiResponseObject Amount getRefundAmount(
            @PathVariable @ApiPathParam(name = "orderId", description = "order Id of order") Integer orderId,
            @PathVariable @ApiPathParam(name = "returnId", description = "Return Id of the order") Integer returnId);

    @RequestMapping(value = "/exchange-item/dispatchable/details", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<ExchangeItemDispatchableDTO>  getExchangeItemDispatchableDTO(@RequestBody ExchangedItem exchangedItem);

    @RequestMapping(value = "/sync/unicom/status", method = RequestMethod.POST)
    public @ApiResponseObject ResponseEntity<Boolean>  syncUnicomStatus(@RequestBody List<UwOrderDTO> uwOrderDTOS);

    @PostMapping(value = "/addOrderComment")
    public @ApiResponseObject ResponseEntity<Boolean> addOrderComment(@RequestBody OrderCommentReq orderCommentReq);

    @RequestMapping(value = "reverse/awaited-rto/refund-method/{incrementId}/{itemId}", method = RequestMethod.GET)
    public String getRefundMethodAtRto(@PathVariable Integer incrementId, @PathVariable Integer itemId) throws Exception;

    @RequestMapping(value = "order/{incrementId}/canceled-orders", method = RequestMethod.GET)
    public List<Canceledorders> getCanceledOrders(@PathVariable Integer incrementId) throws Exception;

    @PostMapping("/notification/send-sms/v2")
    ResponseEntity<Boolean> sendSms(@RequestBody SmsPayload request);

    @PostMapping("/getOrderDetailsForRefundSms")
    ResponseEntity<Map<String, String>> getOrderDetailsForReturnSms(@RequestBody Map<String, Object> request);

    @RequestMapping(value = "uw_order/details/{identifier_type}/{identifier_value}", method = RequestMethod.GET)
    ResponseEntity<UwOrderDTO> getUwOrderInfo(@NotNull @PathVariable(value = "identifier_type") String identifierType, @NotNull @PathVariable(value = "identifier_value") String identifierValue);

    @RequestMapping(value = "uw_order/non-b2b-uwitems/{increment_id}/get", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<List<Integer>> getNonB2BUwItemIds(@NotNull @PathVariable(value = "increment_id") Integer incrementId);
    @RequestMapping(value = "product-info/{uw_item_id}/get", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<ProductMetaDataResponse> getProductInfo(@NotNull @PathVariable(value = "uw_item_id") Integer uwItemId);
    @RequestMapping(value = "return-info/{return_id}/get", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<List<Map<String, Object>>> getReturnInfoForInventory(@NotNull @PathVariable(value = "return_id") Integer returnId);

    @PostMapping(value = "/franchise-refund-required")
    public @ApiResponseObject ResponseEntity<Boolean> checkFranchiseRefundRequired(@RequestBody com.lenskart.refund.client.model.dto.RefundRequestDTO refundRequest);

    @RequestMapping(value = "orders/update_status", method = RequestMethod.PUT)
    public @ApiResponseObject ResponseEntity<Boolean>  orderStatusUpdate(@RequestBody OrderStatusUpdateDetails request);

    @RequestMapping(value = "order/status/priority", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<List<Map<String, String>>> getOrderStatusPriorityDetails();

    @GetMapping(value = "/get-order-priority-type")
    public ResponseEntity<String> getOrderPriorityType(@RequestParam(required = false) Integer itemId, @RequestParam(required = false) Integer incrementId);

    @RequestMapping(value = "order/{uw_item_id}/extra/info", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<ReturnOrderItemExtraInfoDTO> getExtraInfoDetails(@PathVariable("uw_item_id") Integer uwItemId);

    @PostMapping(value = "get-refund-amount/{incrementId}")
    public @ApiResponseObject ResponseEntity<Double> getRefundAmount(@NotNull @PathVariable("incrementId") Integer incrementId,@NotNull @RequestBody List<Integer>uwItemIds);

    @RequestMapping(value = "getReturnDetails/{identifier-type}/{identifier}", method = RequestMethod.GET)
    public @ApiResponseObject ResponseEntity<ReturnDetailsDTO> getReturnOrderDetails(@NotNull @PathVariable(value = "identifier-type") String identifierType,
                                                                                     @NotNull @PathVariable(value = "identifier") String identifier);

    @RequestMapping(value = "push-data/{queue}", method = RequestMethod.POST)
    public String pushData(@RequestBody Map<String, Object> data, @PathVariable("queue") String queue);

    @RequestMapping(value = "exchange-refund-dispatch-point/{uwItemId}/{returnId}", method = RequestMethod.GET)
    public @ApiResponseObject @ResponseBody
    ResponseEntity<?> getExchangeRefundDispatchPoint(@PathVariable("uwItemId") Integer uwItemId,@PathVariable("returnId") Integer returnId);

    @GetMapping(value = "return-history/{return_id}")
    ResponseEntity<List<ReturnHistoryDTO>> getReturnHistory(@PathVariable("return_id") Integer returnId);

    @GetMapping(value = "agent-usernames/{department}")
    List<String> getReturnAgents(@PathVariable("department") String department);

    @GetMapping(value = "get/delight-action/{return_id}")
    ResponseEntity<DelightActionDTO> getDelightActionDetails(@PathVariable("return_id") Integer returnId);

    @RequestMapping(value = "eligibility/augment", method = RequestMethod.POST)
    public @ApiResponseObject ReturnRefundEligibilityRequestDTO eligibilityRequestBodyForHeadless(@ApiBodyObject @RequestBody ReturnRefundEligibilityRequest returnRefundEligibilityRequest) throws Exception;

    @PostMapping("orders/mobile")
    ResponseEntity<List<OrderTelephoneMapperDTO>> getOrderDetailsByTelephone(@RequestBody OrderTelephoneMapperRequestDTO orderTelephoneMapperRequestDTO);

    @GetMapping(value = "get/offline-order-status/{uw_item_id}")
    ResponseEntity<List<OfflineStatusDetailsDTO>> getOfflineOrderStatus(@PathVariable("uw_item_id") Integer uwItemId);

    @GetMapping(value = "get/lens-prescription/{magento_id}")
    ResponseEntity<List<CustomOptionsResponseDTO>> getLensPresciption(@PathVariable("magento_id") Long magento_id);

    @GetMapping(value = "/tax/products")
    ResponseEntity<HashMap<String, Product>> getProductsByIds(@RequestParam List<Integer> productIds);

    @GetMapping(value = "/hub-facilities")
    ResponseEntity<List<String>> getHubMasterFacilityCodes();

    @GetMapping(value = "/invoice/shipment/{shippingPackageId}")
    ResponseEntity<S3InvoiceDetails> getInvoiceDetails(@PathVariable("shippingPackageId") String shippingPackageId);

    @GetMapping(value = "/product/classifications")
    ResponseEntity<List<Classification>> getClassificationsByIds(@RequestParam(name = "classificationIds") List<Integer> classificationIds);

    @GetMapping(value = "/order-item-gst-details")
    ResponseEntity<List<OrderItemGSTDetail>> getOrderItemGSTDetails(@RequestParam(name = "uwItemIds") List<Integer> uwItemIds);

    @GetMapping(value = "/uniReport-stock-entry")
    ResponseEntity<UniReportStockEntry> fetchUniReportStockEntity(
            @RequestParam("itemCode") String itemCode,
            @RequestParam("facilityCodes") Collection<String> facilityCode);

    @GetMapping("/sbrt/uwItemId/{uwItemId}")
    ResponseEntity<SbrtOrderItem> fetchSBRTOrderItem(@PathVariable("uwItemId") Integer uwItemId);

}
