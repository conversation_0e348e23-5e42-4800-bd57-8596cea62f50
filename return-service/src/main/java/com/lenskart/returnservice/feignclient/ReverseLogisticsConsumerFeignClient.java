package com.lenskart.returnservice.feignclient;

import com.lenskart.orderops.model.ReturnOrder;
import com.lenskart.reversemodel.request.InitiateReverseRequest;
import com.lenskart.reversemodel.response.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

@FeignClient(name = "reverseLogisticsConsumerFeignClient", url = "${feign.client.domain.reverseLogisticsConsumer:https://reverse-logistics-consumer.scm.preprod.lenskart.com}")
public interface ReverseLogisticsConsumerFeignClient {

    @PostMapping("/reverse/initiate")
    ResponseEntity<Response> processReverseInitiateCourierAssignation(@RequestBody InitiateReverseRequest request);

    @RequestMapping(value = "/reverse/copy/return", method = RequestMethod.POST)
    ResponseEntity<ReturnOrder> copyReturn(@RequestBody ReturnOrder returnOrder);
}
