package com.lenskart.returnservice.feignclient;

import com.lenskart.ordermetadata.dto.ItemResolutionFlatDTO;
import com.lenskart.ordermetadata.dto.request.ItemResoltionFlatRequestDTO;
import com.lenskart.reversemodel.request.InitiateReverseRequest;
import com.lenskart.reversemodel.response.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "reverseReceivingFeignClient", url = "${feign.client.domain.reverseReceiving:https://reversereceiving.scm.preprod.lenskart.com}")
public interface ReverseReceivingFeignClient {

    @GetMapping("/receiving/item/resolution/flat/get")
    ItemResolutionFlatDTO getItemResolutionFlat(@RequestBody ItemResoltionFlatRequestDTO resolutionFlatRequestDTO);
}
