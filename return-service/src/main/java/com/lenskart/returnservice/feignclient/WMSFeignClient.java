package com.lenskart.returnservice.feignclient;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Map;

@FeignClient(name = "wmsFeignClient", url = "${feign.client.domain.wms-nexs:https://wms.nexs.preprod.lenskart.com}")
public interface WMSFeignClient {

    @GetMapping("/nexs/wms/api/v1/orderLevel/details/id/{incrementId}")
    ResponseEntity<Map<String, Object>> getOrderLevelDetails(@PathVariable("incrementId") Integer incrementId);
}
