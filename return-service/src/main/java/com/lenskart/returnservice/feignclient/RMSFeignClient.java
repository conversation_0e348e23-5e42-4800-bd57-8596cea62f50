package com.lenskart.returnservice.feignclient;

import com.lenskart.returncommon.model.dto.MvcOrderDTO;
import com.lenskart.returncommon.model.request.CustomerStoreProfileRequest;
import com.lenskart.returncommon.model.response.CustomerMetaDataResponse;
import com.lenskart.returncommon.model.response.CustomerStoreProfileResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "RMSFeignClient", url = "${rms.base.url:https://rms.scm.preprod.lenskart.com/}")
public interface RMSFeignClient {

    @PostMapping(value = "analytics/most-valuable/customer-store/rating")
    ResponseEntity<CustomerStoreProfileResponse> fetchMvcAndMvsScore(@RequestBody CustomerStoreProfileRequest customerStoreProfileRequest);

    @GetMapping(value = "return/mvc_orders/{IDENTIFIER_TYPE}/{IDENTIFIER_VALUE}/get")
    public ResponseEntity<MvcOrderDTO> getMvcOrders(@PathVariable("IDENTIFIER_TYPE") String identifierType, @PathVariable("IDENTIFIER_VALUE") String identifierValue);

    @PostMapping(value = "delight/customer-metadata")
    ResponseEntity<CustomerMetaDataResponse> fetchCustomerMetaData(@RequestBody CustomerStoreProfileRequest customerStoreProfileRequest);
}