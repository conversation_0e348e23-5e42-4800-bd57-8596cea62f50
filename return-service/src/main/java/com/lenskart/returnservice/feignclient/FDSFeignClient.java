package com.lenskart.returnservice.feignclient;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "fdsFeignClient", url = "${feign.client.domain.fds:https://fds.scm.preprod.lenskart.com}")
public interface FDSFeignClient {
    
        @GetMapping("/fds/api/v1/document/search")
        ResponseEntity<String> searchDocuments(@RequestParam("searchTerms") String searchTerms);
}
