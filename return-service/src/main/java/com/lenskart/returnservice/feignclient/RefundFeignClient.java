package com.lenskart.returnservice.feignclient;

import com.lenskart.ordermetadata.dto.response.CheckRefundSwitchActiveDTO;
import com.lenskart.refund.client.model.dto.RefundRequestDTO;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.request.GetRefundAmountRequest;
import com.lenskart.refund.client.model.request.RefundRejectRequest;
import com.lenskart.refund.client.model.response.*;
import jakarta.validation.Valid;
import org.jsondoc.core.annotation.ApiResponseObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@FeignClient(name = "refundFeignClient", url = "${feign.client.domain.refund:https://refund-api.scm.preprod.lenskart.com/}")
public interface RefundFeignClient {

    @PostMapping("v2/refund/get-refund-amount/order")
    ResponseEntity<GetRefundAmountResponse> getRefundAmountByOrderId(@RequestBody GetRefundAmountRequest request);

    @PostMapping("v1.0/check-refund-initiated")
    ResponseEntity<CheckRefundInitiatedResponse> checkRefundInitiated(@Valid @RequestBody CheckRefundInitiatedRequest checkRefundInitiatedRequest, @RequestHeader() Map<String, Object> headers);

    @PostMapping("v1.0/initiate-exchange-order-refund")
    ResponseEntity<Boolean> initiateExchangeOrderRefund(@RequestBody Map<String, Object> request);

    @PostMapping("v1.0/push-to-topic/{topic}/{partitionKey}")
    ResponseEntity<KafkaProducerResponse> pushToKafkaTopic(@PathVariable("topic") String topic, @PathVariable("partitionKey") String partitionKey, @RequestBody Object body);

    @PostMapping("v2/refund/get-method-wise-refund-details")
    ResponseEntity<GetMethodWiseRefundDetailsResponse> getMethodWiseRefundDetails(@RequestBody GetRefundAmountRequest request);

    @RequestMapping(value = "refund-switch-active/{identifierType}/{identifierValue}", method = RequestMethod.GET)
    ResponseEntity<CheckRefundSwitchActiveDTO> checkRefundSwitchActive(@PathVariable("identifierType") String identifierType, @PathVariable("identifierValue") String identifierValue);

    @PostMapping("v1.0/reject-request")
    ResponseEntity<KafkaProducerResponse> rejectRefundRequest(@RequestBody RefundRejectRequest refundRejectRequest, @RequestHeader() Map<String, Object> headers);

    @PostMapping("v3/refund/get-refund-details")
    ResponseEntity<RefundDetailing> getRefundDetails(@Valid @RequestBody CheckRefundInitiatedRequest checkRefundInitiatedRequest, @RequestHeader() Map<String, Object> headers);

}
