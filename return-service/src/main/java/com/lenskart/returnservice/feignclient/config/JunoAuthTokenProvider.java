package com.lenskart.returnservice.feignclient.config;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Slf4j
@Service
public class JunoAuthTokenProvider {

    @Value("${juno.auth.url:https://api-gateway.juno.preprod.lenskart.com/draco/v1.0/users/auth/token}")
    private String authUrl;

    @Value("${juno.auth.token:Basic cmV0dXJuLWFwaUAzQzI3eDhGc3A4QkdGYU5QOWtKMmI5NU90NldxT0dNLURGcEdYRkxTQkhMY3FhNVllSFRLX3ZjV21oQjBPWnc3OjJTLTJ5c1ktcjhSQkVmUkg5a0lpWTRaTXN2U3BiR1Z0WGxnUVgxS0RCQ09hT1kwTnRXeW5jTWZWVzVpbU1uMzFCRkJzVmJIbWpDV0tyZWN4d2xPeVltTGt1SEhxNmJtT3cyTW9TOWlKOGQw}")
    private String authToken;

    @Value("${callback.lkWalletReversal.sessionUrl}")
    private String junoSessionUrl;

    private final RestTemplate restTemplate = new RestTemplate();

    private String cachedToken;
    private long expiryTimeMillis;

    public String getToken() {
        try {
            if (cachedToken == null || System.currentTimeMillis() >= expiryTimeMillis) {
                log.info("[FBR] before fetching fetchToken :{}", cachedToken);
                fetchToken();
            }
            log.info("[FBR] Getting cached token :{}", cachedToken);
            return cachedToken;
        } catch (Exception ex) {
            log.error("Failed to fetch Juno auth token: {}", ex.getMessage(), ex);
            return null; // Fallback: return null, up to Feign to handle
        }
    }

    private void fetchToken() {
        log.info("[FBR] fetchToken");
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Auth-Token", authToken);

        HttpEntity<Void> request = new HttpEntity<>(headers);

        ResponseEntity<Map<String, Object>> response;
        try {
            log.info("[FBR] fetchToken request: {} authToken: {}", request, authToken);
            response = restTemplate.exchange(authUrl, HttpMethod.POST, request, new ParameterizedTypeReference<>() {
            });
            log.info("[FBR] fetchToken response {}", response);
        } catch (RestClientException e) {
            log.error("Error calling Juno auth API: {}", e.getMessage(), e);
            return;
        }

        if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
            log.error("Invalid response from Juno auth API. Status: {}", response.getStatusCode());
            return;
        }

        Map<String, Object> result = (Map<String, Object>) response.getBody().get("result");
        if (result == null || !result.containsKey("access_token") || !result.containsKey("expires_in")) {
            log.error("Missing expected fields in Juno auth response: {}", result);
            return;
        }
        log.info("[FBR] fetchToken cachedToken :{}", cachedToken);
        cachedToken = "Bearer " + result.get("access_token").toString();
        int expiresIn = (int) result.get("expires_in");
        expiryTimeMillis = System.currentTimeMillis() + expiresIn - 10_000; // 10 sec buffer
        log.info("Fetched new Juno auth token, valid for {} ms", expiresIn);
    }

    public String getSessionToken() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String request = "";
        HttpEntity<String> entity = new HttpEntity<String>(request, headers);
        ResponseEntity<String> response = restTemplate.exchange(junoSessionUrl, HttpMethod.POST, entity, String.class);
        log.info("Response from session: {}", response);
        Gson gson = new Gson();
        JsonElement element = gson.fromJson(response.getBody(), JsonElement.class);
        JsonObject jsonObj = element.getAsJsonObject();
        JsonObject result = jsonObj.get("result").getAsJsonObject();
        String session = result.get("id").getAsString();
        log.info("Value of Session is: {}", session);
        return session;
    }
}


