package com.lenskart.returnservice.feignclient;

import com.lenskart.ordermetadata.dto.OrderInfoDtoType;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "orderAdaptorFeignClient", url = "${feign.client.domain.order-adaptor:https://order-adaptor.scm.preprod.lenskart.com/}")
public interface OrderAdaptorFeignClient {

    @GetMapping("order/{order_id}/details/v2")
    ResponseEntity<OrderInfoResponseDTO> getOrderDetailsV2(@PathVariable("order_id") Integer orderId, @RequestParam(value = "required_dto", required = false) List<OrderInfoDtoType> requiredDTOs);

    @GetMapping(value = "order/uw-order-details/{identifier-type}/{identifier}")
    ResponseEntity<List<UwOrderDTO>> getUwOrdersDTO(@PathVariable("identifier-type") String identifierType, @PathVariable("identifier") String identifierValue);
}
