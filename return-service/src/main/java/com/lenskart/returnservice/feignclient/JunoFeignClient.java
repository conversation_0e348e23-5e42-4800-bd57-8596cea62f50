package com.lenskart.returnservice.feignclient;

import com.lenskart.returncommon.model.request.GiftVoucherRequest;
import com.lenskart.returncommon.model.response.GiftVoucherResponse;
import com.lenskart.returnservice.feignclient.config.JunoFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        name = "junoFeignClient",
        url = "${feign.client.domain.juno:https://api-gateway.juno.preprod.lenskart.com}",
        configuration = JunoFeignConfig.class
)
public interface JunoFeignClient {

    @PostMapping("/v2/money/v1.0/country/IN/gv/FRAME_BROKEN_SYSTEM")
    ResponseEntity<GiftVoucherResponse> issueFrameBrokenVoucher(@RequestBody GiftVoucherRequest request);
}

