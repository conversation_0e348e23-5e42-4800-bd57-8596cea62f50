package com.lenskart.returnservice.feignclient;

import com.lenskart.returncommon.model.dto.BarcodePriceRequestDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "NexsFeignClient", url = "${nexs.base.url:https://grn.nexs.preprod.lenskart.com}")
public interface NexsFeignClient {

    @PostMapping(value = "/nexs/api/grn/v1/get/barcode/price", consumes = "application/json")
    ResponseEntity<String> getBarcodePrice(@RequestBody BarcodePriceRequestDto request);
}
