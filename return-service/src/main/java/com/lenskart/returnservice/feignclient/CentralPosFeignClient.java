package com.lenskart.returnservice.feignclient;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(name = "centralPosClient", url = "${feign.client.domain.centralPos:https://central.pos.preprod.lenskart.com}")
public interface CentralPosFeignClient {

    @GetMapping(value = "/v1/gatepass/getGatepassItemsDetails/{uwItemId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, Object> getPosDetails(
            @PathVariable("uwItemId") Integer uwItemId,
            @RequestHeader("User-Agent") String userAgent,
            @RequestHeader("X-Lenskart-API-Key") String apiKey,
            @RequestHeader("X-Lenskart-App-Id") String appId,
            @RequestHeader("X-Lenskart-Session-Token") String sessionToken
    );
}
