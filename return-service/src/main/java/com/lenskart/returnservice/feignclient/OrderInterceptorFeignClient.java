package com.lenskart.returnservice.feignclient;

import com.lenskart.order.interceptor.dto.OrderEventDto;
import com.lenskart.order.interceptor.request.CreateOrderRequest;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "orderInterceptorFeignClient", url = "${feign.client.domain.order-ops-interceptor:http://order-interceptor.scm.preprod-eks.internal}")
public interface OrderInterceptorFeignClient {

    @GetMapping(value = "/scm/interceptor/api/v1/order/getOrderEvent/{orderId}/{orderEventType}")
    ResponseEntity<OrderEventDto> getIsOrderCancellable(@PathVariable(value = "orderId") String orderId, @PathVariable(value = "orderEventType") String orderEventType);
}
