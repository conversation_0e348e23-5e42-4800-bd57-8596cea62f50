package com.lenskart.returnservice.feignclient.config;

import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
public class JunoFeignConfig {

    private final JunoAuthTokenProvider tokenProvider;

    @Value("${juno.session.token:b97c3c5d-3ff8-4618-890f-d417a8a92cfe}")
    private String sessionToken;

    @Value("${juno.api.client:mobilesite}")
    private String apiClient;

    public JunoFeignConfig(JunoAuthTokenProvider tokenProvider) {
        this.tokenProvider = tokenProvider;
    }

    @Bean
    public RequestInterceptor junoFeignRequestInterceptor() {
        return requestTemplate -> {
            String token = tokenProvider.getToken();
            log.info("[FBR] Got Juno auth token :{}", token);
            sessionToken = tokenProvider.getSessionToken();
            if (token != null && !token.isBlank()) {
                requestTemplate.header("x-auth-token", token);
                requestTemplate.header("x-session-token", sessionToken);
                requestTemplate.header("x-api-client", apiClient);
                log.info("Added Juno X-Auth-Token to request");
            } else {
                log.info("Juno token is null or blank — header not added");
            }
        };
    }
}
