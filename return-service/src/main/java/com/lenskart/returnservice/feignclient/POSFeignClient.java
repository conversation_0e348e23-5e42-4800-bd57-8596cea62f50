package com.lenskart.returnservice.feignclient;

import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returncommon.model.dto.PosFranchiseSbrtResponse;
import com.lenskart.returncommon.model.dto.PosSessionRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


@FeignClient(name = "POSFeignClient", url = "${feign.client.domain.pos:http://webservice.pos.preprod-eks.internal}")
public interface POSFeignClient {

    @PostMapping("/v1/return/approval/status")
    ResponseEntity updateReturnApprovalStatus(@RequestBody ApprovalStatusRequest approvalStatusRequest,
                                              @RequestHeader("Content-Type") String contentType,
                                              @RequestHeader("X-Lenskart-App-Id") String appId,
                                              @RequestHeader("X-Lenskart-API-Key") String apiKey);

    @PostMapping(value = "/v1/auth", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, Object> getSessionToken(
            @RequestHeader("User-Agent") String userAgent,
            @RequestHeader("X-Lenskart-API-Key") String apiKey,
            @RequestHeader("X-Lenskart-App-Id") String appId,
            @RequestBody PosSessionRequest request
    );

    @GetMapping(value = "/v1/franchises/sbrt/status")
    PosFranchiseSbrtResponse getStoreSbrtStatus(
            @RequestHeader("User-Agent") String userAgent,
            @RequestHeader("X-Lenskart-API-Key") String apiKey,
            @RequestHeader("X-Lenskart-App-Id") String appId,
            @RequestHeader("X-Lenskart-Session-Token") String sessionToken,
            @RequestParam("facilityCode") String facilityCode
    );
}

