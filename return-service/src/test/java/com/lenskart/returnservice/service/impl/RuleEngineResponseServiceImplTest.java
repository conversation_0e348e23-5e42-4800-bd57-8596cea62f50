package com.lenskart.returnservice.service.impl;

import com.lenskart.returnrepository.entity.ReturnRefundRule;
import com.lenskart.returnrepository.repository.ReturnRefundRuleRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
@ExtendWith(SpringExtension.class)
class RuleEngineResponseServiceImplTest {

    @InjectMocks
    private RuleEngineResponseServiceImpl ruleEngineResponseService;

    @Mock
    private ReturnRefundRuleRepository returnRefundRuleRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleEngineResponseForDispatch_ReturnsDispatchStatus() {
        // Given
        Integer uwItemId = 1;
        Integer returnId = 2;
        ReturnRefundRule returnRefundRule = new ReturnRefundRule();
        returnRefundRule.setReturnRefundRuleResponse("{\"exchangeOrderDispatch\":\"DISPATCH_STATUS\"}");
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(anyInt())).thenReturn(returnRefundRule);

        // When
        String result = ruleEngineResponseService.getRuleEngineResponseForDispatch(uwItemId, returnId);

        // Then
        assertEquals("DISPATCH_STATUS", result);
        verify(returnRefundRuleRepository, times(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
    }

    @Test
    void getRuleEngineResponseForDispatch_NoRuleFound() {
        // Given
        Integer uwItemId = 1;
        Integer returnId = 2;
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(anyInt())).thenReturn(null);

        // When
        String result = ruleEngineResponseService.getRuleEngineResponseForDispatch(uwItemId, returnId);

        // Then
        assertEquals(null, result);
        verify(returnRefundRuleRepository, times(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
    }

    @Test
    void getRuleEngineResponseForDispatch_ExceptionHandling() {
        // Given
        Integer uwItemId = 1;
        Integer returnId = 2;
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(anyInt())).thenThrow(new RuntimeException("Test Exception"));

        // When
        String result = ruleEngineResponseService.getRuleEngineResponseForDispatch(uwItemId, returnId);

        // Then
        assertEquals(null, result);
        verify(returnRefundRuleRepository, times(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(uwItemId);
    }

}