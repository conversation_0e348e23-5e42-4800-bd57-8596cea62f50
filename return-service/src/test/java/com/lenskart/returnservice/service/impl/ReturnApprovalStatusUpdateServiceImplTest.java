package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returnservice.service.IKafkaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReturnApprovalStatusUpdateServiceImplTest {
    @InjectMocks
    private ReturnApprovalStatusUpdateServiceImpl returnApprovalStatusUpdateService;

    @Mock
    private IKafkaService kafkaService;

    private ApprovalStatusRequest approvalStatusRequest;

    @BeforeEach
    void setUp() {
        approvalStatusRequest = new ApprovalStatusRequest();
        approvalStatusRequest.setMagentoItemId(1);
        approvalStatusRequest.setReturnStatus("Approved");
        approvalStatusRequest.setSelectedRefundAction("Refund");
    }

//    @Test
//    void testUpdateApprovalStatus_ValidRequest() {
//        boolean result = returnApprovalStatusUpdateService.updateApprovalStatus(approvalStatusRequest);
//
//        assertTrue(result);
//        verify(kafkaService,Mockito.times(1)).pushToKafka(anyString(), anyString(), anyString());
//    }

    @Test
    void testUpdateApprovalStatus_InvalidRequest() {
        approvalStatusRequest.setMagentoItemId(0);

        boolean result = returnApprovalStatusUpdateService.updateApprovalStatus(approvalStatusRequest);

        assertFalse(result);
        verify(kafkaService, Mockito.times(0)).pushToKafka(anyString(), anyString(), anyString());
    }

    @Test
    void testUpdateApprovalStatus_NullRequest() {
        approvalStatusRequest = null;

        boolean result = returnApprovalStatusUpdateService.updateApprovalStatus(approvalStatusRequest);

        assertFalse(result);
        verify(kafkaService, Mockito.times(0)).pushToKafka(anyString(), anyString(), anyString());
    }

    @Test
    void testUpdateApprovalStatus_MissingMandatoryFields() {
        approvalStatusRequest.setMagentoItemId(1);
        approvalStatusRequest.setReturnStatus("");
        approvalStatusRequest.setSelectedRefundAction("");

        boolean result = returnApprovalStatusUpdateService.updateApprovalStatus(approvalStatusRequest);

        assertFalse(result);
        verify(kafkaService, Mockito.times(0)).pushToKafka(anyString(), anyString(), anyString());
    }

    @Test
    void testUpdateApprovalStatus_InvalidRequest_LogGenerated() {
        approvalStatusRequest.setMagentoItemId(0);

        boolean result = returnApprovalStatusUpdateService.updateApprovalStatus(approvalStatusRequest);

        assertFalse(result);
        // Assuming logging behavior is tested via indirectly observing that no further actions are taken
    }

//    @Test
//    void testUpdateApprovalStatus_ExceptionThrown() throws Exception {
//        when(kafkaService.pushToKafka(anyString(), anyString(), any())).thenThrow(RuntimeException.class);
//        boolean result = returnApprovalStatusUpdateService.updateApprovalStatus(approvalStatusRequest);
//
//        assertFalse(result);
//        verify(kafkaService, times(1)).pushToKafka(anyString(), anyString(), anyString());
//    }
//
//
//
//    @Test
//    void testPushStatusSyncToKafka_Success() throws Exception {
//        returnApprovalStatusUpdateService.pushStatusSyncToKafka(approvalStatusRequest);
//
//        verify(kafkaService, times(1)).pushToKafka(anyString(), eq("1"), anyString());
//    }

}