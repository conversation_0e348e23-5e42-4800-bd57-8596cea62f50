package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {JunoServiceImpl.class})
@ExtendWith(SpringExtension.class)
class JunoServiceImplTest {
    @MockBean
    private CommunicationUtil communicationUtil;

    @Autowired
    private JunoServiceImpl junoServiceImpl;

    /**
     * Method under test: {@link JunoServiceImpl#getProductDetails(int)}
     */
    @Test
    void testGetProductDetails() {
        // Arrange, Act and Assert
        assertTrue(junoServiceImpl.getProductDetails(1).isEmpty());
        assertTrue(junoServiceImpl.getProductDetails(2).isEmpty());
    }
}
