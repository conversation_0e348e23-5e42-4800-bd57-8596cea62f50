package com.lenskart.returnservice.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

@ContextConfiguration(classes = {CommunicationUtil.class})
@ExtendWith(SpringExtension.class)
public class CommunicationUtilTest {

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private CommunicationUtil communicationUtil;

    private String shortUrlServiceBaseUrl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        communicationUtil.shortUrlServiceBaseUrl = "http://mock-short-url-service/";
    }


    @Test
    void getShortUrlForJpg_WhenRestTemplateReturnsNull() {
        String longUrl = "http://example.com/image.jpg";
        Mockito.when(restTemplate.getForEntity(anyString(), eq(String.class)))
                .thenReturn(null);

        String result = communicationUtil.getShortUrlForJpg(longUrl);

        assertNull(result);
    }

    @Test
    void getShortUrlForJpg_WhenResponseBodyIsNull() {
        String longUrl = "http://example.com/image.jpg";
        Mockito.when(restTemplate.getForEntity(anyString(), eq(String.class)))
                .thenReturn(ResponseEntity.ok(null));

        String result = communicationUtil.getShortUrlForJpg(longUrl);

        assertNull(result);
    }

    @Test
    void getShortUrlForJpg_WhenExceptionOccurs() {
        String longUrl = "http://example.com/image.jpg";
        Mockito.when(restTemplate.getForEntity(anyString(), eq(String.class)))
                .thenThrow(new RuntimeException("Mock Exception"));

        String result = communicationUtil.getShortUrlForJpg(longUrl);

        assertNull(result);
    }
}