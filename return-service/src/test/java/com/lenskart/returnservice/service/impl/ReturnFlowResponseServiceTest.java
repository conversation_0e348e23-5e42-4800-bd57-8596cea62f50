package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import brave.propagation.CurrentTraceContext;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.response.ReturnFlowResponse;
import com.lenskart.returncommon.model.response.ThresholdCheckResponse;
import com.lenskart.returnrepository.entity.ReturnRefundRule;
import com.lenskart.returnrepository.repository.ReturnRefundRuleRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IReturnFlowResponseService;
import io.lettuce.core.tracing.TracerProvider;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@ContextConfiguration(classes = {ReturnFlowResponseService.class})
@ExtendWith(SpringExtension.class)
class ReturnFlowResponseServiceTest {
    @MockBean
    private CurrentTraceContext currentTraceContext;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReturnFlowResponseService returnFlowResponseService;

    @MockBean
    private ReturnRefundRuleRepository returnRefundRuleRepository;

    @MockBean
    private TracerProvider tracerProvider;

    /**
     * Method under test:
     * {@link ReturnFlowResponseService#getReturnFlowResponse(Integer, boolean)}
     */
    @Test
    void testGetReturnFlowResponse() {
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(null);
        ReturnFlowResponse actualReturnFlowResponse = returnFlowResponseService.getReturnFlowResponse(1, true);

        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        assertNull(actualReturnFlowResponse);
    }

    /**
     * Method under test:
     * {@link ReturnFlowResponseService#getReturnFlowResponse(Integer, boolean)}
     */
    @Test
    void testGetReturnFlowResponse2() {
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(buildResult);
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);
        ReturnFlowResponse actualReturnFlowResponse = returnFlowResponseService.getReturnFlowResponse(1, true);
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        verify(responseEntity).getBody();
        assertNull(actualReturnFlowResponse);
    }

    @Test
    void testGetReturnFlowResponseJJOrder() {
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        List<OrdersDTO> dtoList = new ArrayList<>();
        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setMagentoItemId(1234);
        ordersDTO.setChannel("JJONLINE");
        dtoList.add(ordersDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder  orderDtos = ordersResult.orders(dtoList);

        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        buildResult.setOrders(dtoList);
        ResponseEntity<OrderInfoResponseDTO> responseEntity = new ResponseEntity<>(buildResult,HttpStatus.OK);
        when(orderOpsFeignClient.getOrderDetails(anyInt())).thenReturn(responseEntity);
        //when(responseEntity.getBody()).thenReturn(buildResult);

        // Act
        ReturnFlowResponse actualReturnFlowResponse = returnFlowResponseService.getReturnFlowResponse(1, true);

        // Assert
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        assertNotNull(actualReturnFlowResponse);
    }

    /**
     * Method under test:
     * {@link ReturnFlowResponseService#getReturnFlowResponseResult(Integer, boolean)}
     */
    @Test
    void testGetReturnFlowResponseResult() {
        // Arrange
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(null);

        // Act
        Boolean actualReturnFlowResponseResult = returnFlowResponseService.getReturnFlowResponseResult(1, true);

        // Assert
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        assertNull(actualReturnFlowResponseResult);
    }

    /**
     * Method under test:
     * {@link ReturnFlowResponseService#getReturnFlowResponseResult(Integer, boolean)}
     */
    @Test
    void testGetReturnFlowResponseResult2() {
        // Arrange
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(buildResult);
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        // Act
        Boolean actualReturnFlowResponseResult = returnFlowResponseService.getReturnFlowResponseResult(1, true);

        // Assert
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        verify(responseEntity).getBody();
        assertNull(actualReturnFlowResponseResult);
    }

    /**
     * Method under test:
     * {@link IReturnFlowResponseService#isFraudThresholdReached(Integer, List, String)}
     */
    @Test
    void testIsFraudThresholdReached() {
        // Arrange
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(buildResult);
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        ReturnRefundRule returnRefundRule = new ReturnRefundRule();
        returnRefundRule
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setDecisionValues("42");
        returnRefundRule.setId(1);
        returnRefundRule.setRequest("Request");
        returnRefundRule.setReturnId(1);
        returnRefundRule.setReturnRefundRuleOverriddenResponse("Return Refund Rule Overridden Response");
        returnRefundRule.setReturnRefundRuleResponse("Return Refund Rule Response");
        returnRefundRule.setRuleCalledFrom("<EMAIL>");
        returnRefundRule.setRuleId("42");
        returnRefundRule.setTriggerPoint("Trigger Point");
        returnRefundRule.setUwItemId(1);
        when(returnRefundRuleRepository.findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(Mockito.<Integer>any(),
                Mockito.<String>any())).thenReturn(returnRefundRule);

        ArrayList<Integer> uwItemIds = new ArrayList<>();
        uwItemIds.add(1);

        // Act
        ThresholdCheckResponse actualIsFraudThresholdReachedResult = returnFlowResponseService.isFraudThresholdReached(1,
                uwItemIds, null);

        // Assert
        verify(returnRefundRuleRepository).findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(eq(1),
                eq("ReturnInitiation"));
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        verify(responseEntity).getBody();
        assertEquals("200", actualIsFraudThresholdReachedResult.getStatus());
        assertFalse(actualIsFraudThresholdReachedResult.isResult());
        assertTrue(actualIsFraudThresholdReachedResult.isHasViolatedThreshold());
    }

    /**
     * Method under test:
     * {@link IReturnFlowResponseService#isFraudThresholdReached(Integer, List, String)}
     */
    @Test
    void testIsFraudThresholdReached2() {
        // Arrange
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderInfoResponseDTOBuilder = mock(
                OrderInfoResponseDTO.OrderInfoResponseDTOBuilder.class);
        when(orderInfoResponseDTOBuilder.customerAccountInfoDTO(Mockito.<CustomerAccountInfoDTO>any()))
                .thenReturn(OrderInfoResponseDTO.builder());
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = orderInfoResponseDTOBuilder
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder exchangeOrdersDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO);

        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = exchangeOrdersDTOResult
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(buildResult);
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        ReturnRefundRule returnRefundRule = new ReturnRefundRule();
        returnRefundRule
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setDecisionValues("42");
        returnRefundRule.setId(1);
        returnRefundRule.setRequest("Request");
        returnRefundRule.setReturnId(1);
        returnRefundRule.setReturnRefundRuleOverriddenResponse("Return Refund Rule Overridden Response");
        returnRefundRule.setReturnRefundRuleResponse("Return Refund Rule Response");
        returnRefundRule.setRuleCalledFrom("<EMAIL>");
        returnRefundRule.setRuleId("42");
        returnRefundRule.setTriggerPoint("Trigger Point");
        returnRefundRule.setUwItemId(1);
        when(returnRefundRuleRepository.findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(Mockito.<Integer>any(),
                Mockito.<String>any())).thenReturn(returnRefundRule);

        ArrayList<Integer> uwItemIds = new ArrayList<>();
        uwItemIds.add(1);
        ThresholdCheckResponse actualIsFraudThresholdReachedResult = returnFlowResponseService.isFraudThresholdReached(1,
                uwItemIds, null);
        verify(orderInfoResponseDTOBuilder).customerAccountInfoDTO(isA(CustomerAccountInfoDTO.class));
        verify(returnRefundRuleRepository).findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(eq(1),
                eq("ReturnInitiation"));
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        verify(responseEntity).getBody();
        assertEquals("200", actualIsFraudThresholdReachedResult.getStatus());
        assertFalse(actualIsFraudThresholdReachedResult.isResult());
        assertTrue(actualIsFraudThresholdReachedResult.isHasViolatedThreshold());
    }

    /**
     * Method under test:
     * {@link IReturnFlowResponseService#isFraudThresholdReached(Integer, List, String)}
     */
    @Test
    void testIsFraudThresholdReached3() {
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderInfoResponseDTOBuilder = mock(
                OrderInfoResponseDTO.OrderInfoResponseDTOBuilder.class);
        when(orderInfoResponseDTOBuilder.exchangeOrdersDTO(Mockito.<ExchangeOrdersDTO>any()))
                .thenReturn(OrderInfoResponseDTO.builder());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderInfoResponseDTOBuilder2 = mock(
                OrderInfoResponseDTO.OrderInfoResponseDTOBuilder.class);
        when(orderInfoResponseDTOBuilder2.customerAccountInfoDTO(Mockito.<CustomerAccountInfoDTO>any()))
                .thenReturn(orderInfoResponseDTOBuilder);
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = orderInfoResponseDTOBuilder2
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder exchangeOrdersDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO);

        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = exchangeOrdersDTOResult
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(buildResult);
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        ArrayList<Integer> uwItemIds = new ArrayList<>();
        uwItemIds.add(1);

        ThresholdCheckResponse actualIsFraudThresholdReachedResult = returnFlowResponseService.isFraudThresholdReached(1,
                uwItemIds, null);
        verify(orderInfoResponseDTOBuilder2).customerAccountInfoDTO(isA(CustomerAccountInfoDTO.class));
        verify(orderInfoResponseDTOBuilder).exchangeOrdersDTO(isA(ExchangeOrdersDTO.class));
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        verify(responseEntity).getBody();
        assertEquals("200", actualIsFraudThresholdReachedResult.getStatus());
        assertFalse(actualIsFraudThresholdReachedResult.isHasViolatedThreshold());
        assertFalse(actualIsFraudThresholdReachedResult.isResult());
    }
}
