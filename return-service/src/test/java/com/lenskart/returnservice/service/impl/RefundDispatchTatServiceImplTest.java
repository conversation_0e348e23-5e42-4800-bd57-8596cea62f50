package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.returnservice.service.IReversePickupTatService;
import com.lenskart.returnservice.service.ISystemPreferenceService;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RefundDispatchTatServiceImplTest {
    @Mock
    private IReversePickupTatService iReversePickupTatService;

    @InjectMocks
    private RefundDispatchTatServiceImpl refundDispatchTatServiceImpl;

    @Mock
    private ISystemPreferenceService iSystemPreferenceService;

    /**
     * Test {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}.
     * <p>
     * Method under test: {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}
     */
    @Test
    @DisplayName("Test getRefundDispatchETA(Map, String, String, String, Date, Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchETA() {
        // Arrange
        when(iReversePickupTatService.getPickupTatNew(Mockito.<String>any(), Mockito.<Date>any(), Mockito.<Integer>any()))
                .thenReturn(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        HashMap<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();

        // Act
        refundDispatchTatServiceImpl.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, "Refund Dispatch Point",
                "Reverse Pincode", "Status",
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()), 1);

        // Assert
        verify(iReversePickupTatService).getPickupTatNew(eq("Reverse Pincode"), isA(Date.class), eq(1));
    }

    /**
     * Test {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}.
     * <ul>
     *   <li>Given {@code [getRefundDispatchETA] maxRefundDispatchETA: {}}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}
     */
    @Test
    @DisplayName("Test getRefundDispatchETA(Map, String, String, String, Date, Integer); given '[getRefundDispatchETA] maxRefundDispatchETA: {}'")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchETA_givenGetRefundDispatchETAMaxRefundDispatchETA() {
        // Arrange
        when(iReversePickupTatService.getPickupTatNew(Mockito.<String>any(), Mockito.<Date>any(), Mockito.<Integer>any()))
                .thenReturn(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));

        HashMap<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();
        refundMethodToActualPaymentMethodMap.put("[getRefundDispatchETA] maxRefundDispatchETA: {}",
                "[getRefundDispatchETA] maxRefundDispatchETA: {}");

        // Act
        refundDispatchTatServiceImpl.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, "Refund Dispatch Point",
                "Reverse Pincode", "Status",
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()), 1);

        // Assert
        verify(iReversePickupTatService).getPickupTatNew(eq("Reverse Pincode"), isA(Date.class), eq(1));
    }

    /**
     * Test {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}.
     * <ul>
     *   <li>Given {@code storecredit}.</li>
     *   <li>When {@link HashMap#HashMap()} {@code storecredit} is {@code storecredit}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}
     */
    @Test
    @DisplayName("Test getRefundDispatchETA(Map, String, String, String, Date, Integer); given 'storecredit'; when HashMap() 'storecredit' is 'storecredit'")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchETA_givenStorecredit_whenHashMapStorecreditIsStorecredit() {
        // Arrange
        when(iReversePickupTatService.getPickupTatNew(Mockito.<String>any(), Mockito.<Date>any(), Mockito.<Integer>any()))
                .thenReturn(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));

        HashMap<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();
        refundMethodToActualPaymentMethodMap.put("storecredit", "storecredit");
        refundMethodToActualPaymentMethodMap.put("[getRefundDispatchETA] maxRefundDispatchETA: {}",
                "[getRefundDispatchETA] maxRefundDispatchETA: {}");

        // Act
        refundDispatchTatServiceImpl.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, "Refund Dispatch Point",
                "Reverse Pincode", "Status",
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()), 1);

        // Assert
        verify(iReversePickupTatService).getPickupTatNew(eq("Reverse Pincode"), isA(Date.class), eq(1));
    }

    /**
     * Test {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}.
     * <ul>
     *   <li>Then calls {@link IReversePickupTatService#getPickupTat(String, Date)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}
     */
    @Test
    @DisplayName("Test getRefundDispatchETA(Map, String, String, String, Date, Integer); then calls getPickupTat(String, Date)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchETA_thenCallsGetPickupTat() {
        // Arrange
        when(iReversePickupTatService.getPickupTat(Mockito.<String>any(), Mockito.<Date>any()))
                .thenReturn(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        HashMap<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();

        // Act
        refundDispatchTatServiceImpl.getRefundDispatchETA(refundMethodToActualPaymentMethodMap, "Refund Dispatch Point",
                "Reverse Pincode", "Status",
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()), null);

        // Assert
        verify(iReversePickupTatService).getPickupTat(eq("Reverse Pincode"), isA(Date.class));
    }

    /**
     * Test {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}.
     * <ul>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundDispatchTatServiceImpl#getRefundDispatchETA(Map, String, String, String, Date, Integer)}
     */
    @Test
    @DisplayName("Test getRefundDispatchETA(Map, String, String, String, Date, Integer); then return 'null'")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchETA_thenReturnNull() {
        // Arrange
        when(iReversePickupTatService.getPickupTatNew(Mockito.<String>any(), Mockito.<Date>any(), Mockito.<Integer>any()))
                .thenReturn(null);
        HashMap<String, String> refundMethodToActualPaymentMethodMap = new HashMap<>();

        // Act
        Date actualRefundDispatchETA = refundDispatchTatServiceImpl.getRefundDispatchETA(
                refundMethodToActualPaymentMethodMap, "Refund Dispatch Point", "Reverse Pincode", "Status",
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()), 1);

        // Assert
        verify(iReversePickupTatService).getPickupTatNew(eq("Reverse Pincode"), isA(Date.class), eq(1));
        assertNull(actualRefundDispatchETA);
    }
}
