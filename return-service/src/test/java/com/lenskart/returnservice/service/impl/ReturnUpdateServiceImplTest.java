package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.returncommon.exception.InvalidRequestException;
import com.lenskart.returncommon.exception.OrderNotFound;
import com.lenskart.returncommon.exception.ReturnRequestException;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.repository.ReturnDetailItemRepository;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.ID365FinanceService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.RETURN_RECEIVED;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.AWAITED_RTO;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.RTO;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ReturnUpdateServiceImplTest {
    @InjectMocks
    private ReturnUpdateServiceImpl returnUpdateService;
    @Mock
    private ReturnDetailRepository returnOrderRepository;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;
    @Mock
    private ReturnDetailItemRepository returnOrderItemRepository;

    @Mock
    private IReturnOrderActionService returnOrderActionService;

    @Mock
    private ReturnEventServiceImpl returnEventService;

    @Mock
    private ID365FinanceService financeService;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void setAutoCancelFlagAsFalseWhenReturnOrderPresent() {
        Integer returnId = 11;
        ReturnDetail returnOrder = new ReturnDetail();
        returnOrder.setId(returnId);
//        when(returnOrderRepository.findById(returnId)).thenReturn(Optional.of(returnOrder));
        assertFalse(returnUpdateService.setAutoCancelFlagAsFalse(returnId));
    }

    @Test
    public void setAutoCancelFlagAsFalseWhenReturnOrderPresentNotPresent() {
        Integer returnId = null;
        ReturnDetail returnOrder = new ReturnDetail();
        returnOrder.setId(returnId);
        assertFalse(returnUpdateService.setAutoCancelFlagAsFalse(returnId));
    }

    /**
     * Tests the {@link ReturnUpdateServiceImpl#markRtoItems(ReturnOrderRequestDTO)} method.
     */
//
//    @Test
//    void testMarkRtoItemsSuccess() throws Exception {
//        // Arrange
//        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
//        returnOrderRequest.setIncrementId(123);
//        returnOrderRequest.setReferenceOrderCode("CODE");
//        List<ReturnItemRequestDTO> items = new ArrayList<>();
//        ReturnItemRequestDTO itemRequest = new ReturnItemRequestDTO();
//        itemRequest.setItemId(1);
//        itemRequest.setQcStatus("PASSED");
//        items.add(itemRequest);
//        returnOrderRequest.setItems(items);
//
//        UwOrderDTO uwOrder = new UwOrderDTO();
//        uwOrder.setUnicomOrderCode("CODE");
//        uwOrder.setShipmentStatus("rto");
//
//        ReturnDetail returnOrder = new ReturnDetail();
//        returnOrder.setId(100);
//        CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponseDTO = new CreateUpdateReturnOrderResponseDTO();
//        createUpdateReturnOrderResponseDTO.setReturnId(100);
//
//        when(returnOrderRepository.findTop1ByIncrementIdAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(
//                123, "CODE", AWAITED_RTO)).thenReturn(returnOrder);
//        when(orderOpsFeignClient.getUwOrderInfo(String.valueOf(IdentifierType.UW_ITEM_ID),String.valueOf(1))).thenReturn(ResponseEntity.ok(uwOrder));
//        when(returnOrderActionService.createUpdateReturnOrder(returnOrderRequest, returnOrder.getId(), RETURN_RECEIVED, RTO, null,1))
//                .thenReturn(createUpdateReturnOrderResponseDTO);
//
//
//        // Act
//        Map<String, Object> result = returnUpdateService.markRtoItems(returnOrderRequest);
//
//        // Assert
//        assertTrue((Boolean) result.get("success"));
//        assertEquals(100, result.get("returnId"));
//    }

    /**
     * Tests the {@link ReturnUpdateServiceImpl#markRtoItems(ReturnOrderRequestDTO)} method.
     */

    @Test
    void testMarkRtoItemsInvalidRequest() {
        // Arrange
        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();

        // Act & Assert
        assertThrows(InvalidRequestException.class, () -> returnUpdateService.markRtoItems(returnOrderRequest));
    }

    /**
     * Tests the {@link ReturnUpdateServiceImpl#markRtoItems(ReturnOrderRequestDTO)} method.
     */

    @Test
    void testMarkRtoItemsOrderNotFound() throws Exception {
        // Arrange
        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setIncrementId(123);
        returnOrderRequest.setReferenceOrderCode("REF123");
        List<ReturnItemRequestDTO> items = new ArrayList<>();
        ReturnItemRequestDTO itemRequest = new ReturnItemRequestDTO();
        itemRequest.setItemId(1);
        itemRequest.setQcStatus("PASSED");
        items.add(itemRequest);
        returnOrderRequest.setItems(items);

        when(orderOpsFeignClient.getUwOrderInfo(String.valueOf(IdentifierType.UW_ITEM_ID),String.valueOf(1))).thenReturn(ResponseEntity.ok(null));

        // Act & Assert
        assertThrows(OrderNotFound.class, () -> returnUpdateService.markRtoItems(returnOrderRequest));
    }

//    /**
//     * Tests the {@link ReturnUpdateServiceImpl#markRtoItems(ReturnOrderRequestDTO)} method.
//     */
//    @Test
//    void testMarkRtoItemsDuplicateReturnRequest() throws Exception {
//        // Arrange
//        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
//        returnOrderRequest.setIncrementId(123);
//        returnOrderRequest.setReferenceOrderCode("REF123");
//        List<ReturnItemRequestDTO> items = new ArrayList<>();
//        ReturnItemRequestDTO itemRequest = new ReturnItemRequestDTO();
//        itemRequest.setItemId(1);
//        itemRequest.setQcStatus("PASSED");
//        items.add(itemRequest);
//        returnOrderRequest.setItems(items);
//
//        UwOrderDTO uwOrder = new UwOrderDTO();
//        uwOrder.setUnicomOrderCode("REF123");
//        uwOrder.setShipmentStatus("AWAITED_RTO");
//
//        when(orderOpsFeignClient.getUwOrderInfo(String.valueOf(IdentifierType.UW_ITEM_ID),String.valueOf(1))).thenReturn(ResponseEntity.ok(uwOrder));
////        when(returnOrderItemRepository.findByUwItemIdInAndStatusInAndReturnType(anyList(), anyList(), anyString()))
////                .thenReturn(Arrays.asList(new ReturnDetailItem()));
//
//        // Act & Assert
////        assertThrows(ReturnRequestException.class, () -> returnUpdateService.markRtoItems(returnOrderRequest));
//    }
}
