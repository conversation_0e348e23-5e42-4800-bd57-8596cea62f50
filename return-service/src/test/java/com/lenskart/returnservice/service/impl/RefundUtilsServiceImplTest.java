package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.ordermetadata.dto.CustomerAccountInfoDTO;
import com.lenskart.ordermetadata.dto.CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO.ExchangeOrdersDTOBuilder;
import com.lenskart.ordermetadata.dto.FraudCustomerDTO;
import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.ordermetadata.dto.ItemWiseFastRefunResponseDTO;
import com.lenskart.ordermetadata.dto.ItemWiseGetAmountDTO;
import com.lenskart.ordermetadata.dto.request.RefundDispatchRequest;
import com.lenskart.ordermetadata.dto.response.CheckRefundSwitchActiveDTO;
import com.lenskart.ordermetadata.dto.response.ExchangeDetails;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails.OrderExchangeCancellationDetailsBuilder;
import com.lenskart.ordermetadata.dto.response.RefundDispatchResponse;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail.ShippingStatusDetailBuilder;
import com.lenskart.refund.client.model.dto.RefundAmount;
import com.lenskart.refund.client.model.dto.RefundDetailsForOrderDTO;
import com.lenskart.refund.client.model.dto.RefundRequestDTO;
import com.lenskart.refund.client.model.dto.SpecificRefundDetailsDTO;
import com.lenskart.refund.client.model.enums.IdentifierType;
import com.lenskart.refund.client.model.enums.RefundMethod;
import com.lenskart.refund.client.model.enums.RefundReason;
import com.lenskart.refund.client.model.enums.RefundRequestStatus;
import com.lenskart.refund.client.model.enums.RefundTarget;
import com.lenskart.refund.client.model.enums.RefundTriggerPoint;
import com.lenskart.refund.client.model.enums.RefundType;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.request.CreateRefundRequest;
import com.lenskart.refund.client.model.request.GetRefundAmountRequest;
import com.lenskart.refund.client.model.response.CheckRefundInitiatedResponse;
import com.lenskart.refund.client.model.response.GetMethodWiseRefundDetailsResponse;
import com.lenskart.refund.client.model.response.GetRefundAmountResponse;
import com.lenskart.refund.client.model.response.RefundMethodResponse;
import com.lenskart.returncommon.model.dto.ExchangedItem;
import com.lenskart.returncommon.model.dto.OrderRefundDetails;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.PickUpAddressDetails;
import com.lenskart.returncommon.model.dto.RefundIntentDTO;
import com.lenskart.returncommon.model.dto.RefundProcessDTO;
import com.lenskart.returncommon.model.dto.Result;
import com.lenskart.returncommon.model.dto.ReturnStatusHeadingDetail;
import com.lenskart.returncommon.model.dto.ReversePickupDetails;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO.OrderInfoResponseDTOBuilder;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.entity.ReturnRefundRule;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnrepository.repository.ReturnRefundRuleRepository;
import com.lenskart.returnservice.cache.ReturnStatusTimelineMappingCache;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.RefundFeignClient;
import com.lenskart.returnservice.service.IExchangeItemDispatchService;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IRefundAuthorizationService;
import com.lenskart.returnservice.service.IRefundMethodLabelService;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.ISystemPreferenceService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@ContextConfiguration(classes = {RefundUtilsServiceImpl.class})
@ExtendWith(SpringExtension.class)
class RefundUtilsServiceImplTest {

    @Autowired
    private RefundUtilsServiceImpl refundUtilsServiceImpl;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private RefundFeignClient refundFeignClient;

    @MockBean
    private ISystemPreferenceService iSystemPreferenceService;

    @MockBean
    private ReturnRefundRuleRepository returnRefundRuleRepository;

    @MockBean
    private RestTemplate restTemplate;

    @MockBean
    private IRefundAuthorizationService iRefundAuthorizationService;

    @MockBean
    private ReturnEventRepository returnEventRepository;

    @MockBean
    private IExchangeItemDispatchService iExchangeItemDispatchService;

    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private IRefundMethodLabelService iRefundMethodLabelService;

    @MockBean
    private IReturnEventService iReturnEventService;

    @MockBean
    private IReturnOrderActionService iReturnOrderActionService;

    @MockBean
    private ReturnStatusTimelineMappingCache returnStatusTimelineMappingCache;

    @MockBean
    private ReturnUtil returnUtil;

    /**
     * Test {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}
     */
    @Test
    @DisplayName("Test getOrderRefundAmount(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetOrderRefundAmount() {
        // Arrange
        when(refundFeignClient.getRefundAmountByOrderId(Mockito.<GetRefundAmountRequest>any()))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        GetRefundAmountResponse actualOrderRefundAmount = refundUtilsServiceImpl.getOrderRefundAmount(1);

        // Assert
        verify(refundFeignClient).getRefundAmountByOrderId(isA(GetRefundAmountRequest.class));
        assertNull(actualOrderRefundAmount.getRefundDetailsDTO());
        assertNull(actualOrderRefundAmount.getStatus());
        assertNull(actualOrderRefundAmount.getError());
        assertNull(actualOrderRefundAmount.getMessage());
        assertFalse(actualOrderRefundAmount.isSuccess());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}
     */
    @Test
    @DisplayName("Test getOrderRefundAmount(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetOrderRefundAmount2() {
        // Arrange
        when(refundFeignClient.getRefundAmountByOrderId(Mockito.<GetRefundAmountRequest>any())).thenReturn(null);

        // Act
        GetRefundAmountResponse actualOrderRefundAmount = refundUtilsServiceImpl.getOrderRefundAmount(1);

        // Assert
        verify(refundFeignClient).getRefundAmountByOrderId(isA(GetRefundAmountRequest.class));
        assertNull(actualOrderRefundAmount.getRefundDetailsDTO());
        assertNull(actualOrderRefundAmount.getStatus());
        assertNull(actualOrderRefundAmount.getError());
        assertNull(actualOrderRefundAmount.getMessage());
        assertFalse(actualOrderRefundAmount.isSuccess());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}.
     * <ul>
     *   <li>Then calls {@link ResponseEntity#getStatusCode()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}
     */
    @Test
    @DisplayName("Test getOrderRefundAmount(Integer); then calls getStatusCode()")
    @Tag("MaintainedByDiffblue")
    void testGetOrderRefundAmount_thenCallsGetStatusCode() {
        // Arrange
        ResponseEntity<GetRefundAmountResponse> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getStatusCode()).thenReturn(null);
        when(refundFeignClient.getRefundAmountByOrderId(Mockito.<GetRefundAmountRequest>any())).thenReturn(responseEntity);

        // Act
        GetRefundAmountResponse actualOrderRefundAmount = refundUtilsServiceImpl.getOrderRefundAmount(1);

        // Assert
        verify(refundFeignClient).getRefundAmountByOrderId(isA(GetRefundAmountRequest.class));
        verify(responseEntity).getStatusCode();
        assertNull(actualOrderRefundAmount.getRefundDetailsDTO());
        assertNull(actualOrderRefundAmount.getStatus());
        assertNull(actualOrderRefundAmount.getError());
        assertNull(actualOrderRefundAmount.getMessage());
        assertFalse(actualOrderRefundAmount.isSuccess());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}.
     * <ul>
     *   <li>Then return {@link GetRefundAmountResponse} (default constructor).</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getOrderRefundAmount(Integer)}
     */
    @Test
    @DisplayName("Test getOrderRefundAmount(Integer); then return GetRefundAmountResponse (default constructor)")
    @Tag("MaintainedByDiffblue")
    void testGetOrderRefundAmount_thenReturnGetRefundAmountResponse() {
        // Arrange
        GetRefundAmountResponse getRefundAmountResponse = new GetRefundAmountResponse();
        when(refundFeignClient.getRefundAmountByOrderId(Mockito.<GetRefundAmountRequest>any()))
                .thenReturn(new ResponseEntity<>(getRefundAmountResponse, HttpStatusCode.valueOf(200)));

        // Act
        GetRefundAmountResponse actualOrderRefundAmount = refundUtilsServiceImpl.getOrderRefundAmount(1);

        // Assert
        verify(refundFeignClient).getRefundAmountByOrderId(isA(GetRefundAmountRequest.class));
        assertEquals(getRefundAmountResponse, actualOrderRefundAmount);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundRequestStatus(Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundRequestStatus(Integer)}
     */
    @Test
    @DisplayName("Test getRefundRequestStatus(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundRequestStatus() {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<Object>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new HashMap<>());
        when(refundFeignClient.checkRefundInitiated(Mockito.<CheckRefundInitiatedRequest>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        String actualRefundRequestStatus = refundUtilsServiceImpl.getRefundRequestStatus(1);

        // Assert
        verify(refundFeignClient).checkRefundInitiated(isA(CheckRefundInitiatedRequest.class), isA(Map.class));
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(Object.class), isA(Map.class));
        assertNull(actualRefundRequestStatus);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundRequestStatus(Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundRequestStatus(Integer)}
     */
    @Test
    @DisplayName("Test getRefundRequestStatus(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundRequestStatus2() {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<Object>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new HashMap<>());
        CheckRefundInitiatedResponse checkRefundInitiatedResponse = new CheckRefundInitiatedResponse();
        when(refundFeignClient.checkRefundInitiated(Mockito.<CheckRefundInitiatedRequest>any(),
                Mockito.<Map<String, Object>>any()))
                .thenReturn(new ResponseEntity<>(checkRefundInitiatedResponse, HttpStatusCode.valueOf(200)));

        // Act
        String actualRefundRequestStatus = refundUtilsServiceImpl.getRefundRequestStatus(1);

        // Assert
        verify(refundFeignClient).checkRefundInitiated(isA(CheckRefundInitiatedRequest.class), isA(Map.class));
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(Object.class), isA(Map.class));
        assertNull(actualRefundRequestStatus);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundRequestStatus(Integer)}.
     * <ul>
     *   <li>Given {@link RefundRequestDTO} (default constructor) AgentId is {@code 42}.</li>
     *   <li>Then return {@code pending}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundRequestStatus(Integer)}
     */
    @Test
    @DisplayName("Test getRefundRequestStatus(Integer); given RefundRequestDTO (default constructor) AgentId is '42'; then return 'pending'")
    @Tag("MaintainedByDiffblue")
    void testGetRefundRequestStatus_givenRefundRequestDTOAgentIdIs42_thenReturnPending() {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<Object>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new HashMap<>());

        RefundRequestDTO franchiseRefundRequestDTO = new RefundRequestDTO();
        franchiseRefundRequestDTO.setAgentId("42");
        franchiseRefundRequestDTO.setCreateFranchiseRefundRequest(true);
        franchiseRefundRequestDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        franchiseRefundRequestDTO.setFranchiseRefundRequired(true);
        franchiseRefundRequestDTO.setId(1L);
        franchiseRefundRequestDTO.setIdentifierType(IdentifierType.RETURN_ID);
        franchiseRefundRequestDTO.setIdentifierValue("42");
        franchiseRefundRequestDTO.setOrderId(1L);
        franchiseRefundRequestDTO.setPartitionKey("Partition Key");
        franchiseRefundRequestDTO.setRefundAmount(new RefundAmount());
        franchiseRefundRequestDTO.setRefundReason(RefundReason.FAST_REFUND);
        franchiseRefundRequestDTO.setRefundTarget(RefundTarget.STORECREDIT);
        franchiseRefundRequestDTO.setRefundTriggerPoint("Refund Trigger Point");
        franchiseRefundRequestDTO.setRefundType(RefundType.CREDIT);
        franchiseRefundRequestDTO.setRemarks("Remarks");
        franchiseRefundRequestDTO.setSource("Source");
        franchiseRefundRequestDTO.setStatus(RefundRequestStatus.PENDING);
        franchiseRefundRequestDTO
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));

        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        refundRequestDTO.setAgentId("42");
        refundRequestDTO.setCreateFranchiseRefundRequest(true);
        refundRequestDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        refundRequestDTO.setFranchiseRefundRequired(true);
        refundRequestDTO.setId(1L);
        refundRequestDTO.setIdentifierType(IdentifierType.RETURN_ID);
        refundRequestDTO.setIdentifierValue("42");
        refundRequestDTO.setOrderId(1L);
        refundRequestDTO.setPartitionKey("Partition Key");
        refundRequestDTO.setRefundAmount(new RefundAmount());
        refundRequestDTO.setRefundReason(RefundReason.FAST_REFUND);
        refundRequestDTO.setRefundTarget(RefundTarget.STORECREDIT);
        refundRequestDTO.setRefundTriggerPoint("Refund Trigger Point");
        refundRequestDTO.setRefundType(RefundType.CREDIT);
        refundRequestDTO.setRemarks("Remarks");
        refundRequestDTO.setSource("Source");
        refundRequestDTO.setStatus(RefundRequestStatus.PENDING);
        refundRequestDTO
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));

        CheckRefundInitiatedResponse checkRefundInitiatedResponse = new CheckRefundInitiatedResponse();
        checkRefundInitiatedResponse.setError("An error occurred");
        checkRefundInitiatedResponse.setFranchiseRefundDetailsDTOList(new ArrayList<>());
        checkRefundInitiatedResponse.setFranchiseRefundRequestDTO(franchiseRefundRequestDTO);
        checkRefundInitiatedResponse.setFranchiserRefundInitiated(true);
        checkRefundInitiatedResponse.setMessage("Not all who wander are lost");
        checkRefundInitiatedResponse.setRefundDetailsDTOList(new ArrayList<>());
        checkRefundInitiatedResponse.setRefundInitiated(true);
        checkRefundInitiatedResponse.setRefundRequestDTO(refundRequestDTO);
        checkRefundInitiatedResponse.setStatus(1);
        checkRefundInitiatedResponse.setSuccess(true);
        ResponseEntity<CheckRefundInitiatedResponse> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(checkRefundInitiatedResponse);
        when(refundFeignClient.checkRefundInitiated(Mockito.<CheckRefundInitiatedRequest>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(responseEntity);

        // Act
        String actualRefundRequestStatus = refundUtilsServiceImpl.getRefundRequestStatus(1);

        // Assert
        verify(refundFeignClient).checkRefundInitiated(isA(CheckRefundInitiatedRequest.class), isA(Map.class));
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(Object.class), isA(Map.class));
        verify(responseEntity).getBody();
        assertEquals("pending", actualRefundRequestStatus);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundMethodIntentByCustomer(Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundMethodIntentByCustomer(Integer)}
     */
    @Test
    @DisplayName("Test getRefundMethodIntentByCustomer(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundMethodIntentByCustomer() {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<Object>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new HashMap<>());
        when(refundFeignClient.checkRefundInitiated(Mockito.<CheckRefundInitiatedRequest>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        RefundIntentDTO actualRefundMethodIntentByCustomer = refundUtilsServiceImpl.getRefundMethodIntentByCustomer(1);

        // Assert
        verify(refundFeignClient).checkRefundInitiated(isA(CheckRefundInitiatedRequest.class), isA(Map.class));
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(Object.class), isA(Map.class));
        assertNull(actualRefundMethodIntentByCustomer);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundMethodIntentByCustomer(Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundMethodIntentByCustomer(Integer)}
     */
    @Test
    @DisplayName("Test getRefundMethodIntentByCustomer(Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundMethodIntentByCustomer2() {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<Object>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new HashMap<>());
        CheckRefundInitiatedResponse checkRefundInitiatedResponse = new CheckRefundInitiatedResponse();
        when(refundFeignClient.checkRefundInitiated(Mockito.<CheckRefundInitiatedRequest>any(),
                Mockito.<Map<String, Object>>any()))
                .thenReturn(new ResponseEntity<>(checkRefundInitiatedResponse, HttpStatusCode.valueOf(200)));

        // Act
        RefundIntentDTO actualRefundMethodIntentByCustomer = refundUtilsServiceImpl.getRefundMethodIntentByCustomer(1);

        // Assert
        verify(refundFeignClient).checkRefundInitiated(isA(CheckRefundInitiatedRequest.class), isA(Map.class));
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(Object.class), isA(Map.class));
        assertNull(actualRefundMethodIntentByCustomer);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundMethodIntentByCustomer(Integer)}.
     * <ul>
     *   <li>Then return RefundIntent is {@code storecredit}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundMethodIntentByCustomer(Integer)}
     */
    @Test
    @DisplayName("Test getRefundMethodIntentByCustomer(Integer); then return RefundIntent is 'storecredit'")
    @Tag("MaintainedByDiffblue")
    void testGetRefundMethodIntentByCustomer_thenReturnRefundIntentIsStorecredit() {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<Object>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(new HashMap<>());

        RefundRequestDTO franchiseRefundRequestDTO = new RefundRequestDTO();
        franchiseRefundRequestDTO.setAgentId("42");
        franchiseRefundRequestDTO.setCreateFranchiseRefundRequest(true);
        franchiseRefundRequestDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        franchiseRefundRequestDTO.setFranchiseRefundRequired(true);
        franchiseRefundRequestDTO.setId(1L);
        franchiseRefundRequestDTO.setIdentifierType(IdentifierType.RETURN_ID);
        franchiseRefundRequestDTO.setIdentifierValue("42");
        franchiseRefundRequestDTO.setOrderId(1L);
        franchiseRefundRequestDTO.setPartitionKey("Partition Key");
        franchiseRefundRequestDTO.setRefundAmount(new RefundAmount());
        franchiseRefundRequestDTO.setRefundReason(RefundReason.FAST_REFUND);
        franchiseRefundRequestDTO.setRefundTarget(RefundTarget.STORECREDIT);
        franchiseRefundRequestDTO.setRefundTriggerPoint("Refund Trigger Point");
        franchiseRefundRequestDTO.setRefundType(RefundType.CREDIT);
        franchiseRefundRequestDTO.setRemarks("Remarks");
        franchiseRefundRequestDTO.setSource("Source");
        franchiseRefundRequestDTO.setStatus(RefundRequestStatus.PENDING);
        franchiseRefundRequestDTO
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));

        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        refundRequestDTO.setAgentId("42");
        refundRequestDTO.setCreateFranchiseRefundRequest(true);
        refundRequestDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        refundRequestDTO.setFranchiseRefundRequired(true);
        refundRequestDTO.setId(1L);
        refundRequestDTO.setIdentifierType(IdentifierType.RETURN_ID);
        refundRequestDTO.setIdentifierValue("42");
        refundRequestDTO.setOrderId(1L);
        refundRequestDTO.setPartitionKey("Partition Key");
        refundRequestDTO.setRefundAmount(new RefundAmount());
        refundRequestDTO.setRefundReason(RefundReason.FAST_REFUND);
        refundRequestDTO.setRefundTarget(RefundTarget.STORECREDIT);
        refundRequestDTO.setRefundTriggerPoint("Refund Trigger Point");
        refundRequestDTO.setRefundType(RefundType.CREDIT);
        refundRequestDTO.setRemarks("Remarks");
        refundRequestDTO.setSource("Source");
        refundRequestDTO.setStatus(RefundRequestStatus.PENDING);
        refundRequestDTO
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));

        CheckRefundInitiatedResponse checkRefundInitiatedResponse = new CheckRefundInitiatedResponse();
        checkRefundInitiatedResponse.setError("An error occurred");
        checkRefundInitiatedResponse.setFranchiseRefundDetailsDTOList(new ArrayList<>());
        checkRefundInitiatedResponse.setFranchiseRefundRequestDTO(franchiseRefundRequestDTO);
        checkRefundInitiatedResponse.setFranchiserRefundInitiated(true);
        checkRefundInitiatedResponse.setMessage("Not all who wander are lost");
        checkRefundInitiatedResponse.setRefundDetailsDTOList(new ArrayList<>());
        checkRefundInitiatedResponse.setRefundInitiated(true);
        checkRefundInitiatedResponse.setRefundRequestDTO(refundRequestDTO);
        checkRefundInitiatedResponse.setStatus(1);
        checkRefundInitiatedResponse.setSuccess(true);
        ResponseEntity<CheckRefundInitiatedResponse> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(checkRefundInitiatedResponse);
        when(refundFeignClient.checkRefundInitiated(Mockito.<CheckRefundInitiatedRequest>any(),
                Mockito.<Map<String, Object>>any())).thenReturn(responseEntity);

        // Act
        RefundIntentDTO actualRefundMethodIntentByCustomer = refundUtilsServiceImpl.getRefundMethodIntentByCustomer(1);

        // Assert
        verify(refundFeignClient).checkRefundInitiated(isA(CheckRefundInitiatedRequest.class), isA(Map.class));
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(Object.class), isA(Map.class));
        verify(responseEntity).getBody();
        assertEquals("storecredit", actualRefundMethodIntentByCustomer.getRefundIntent());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundAmount(CheckRefundInitiatedRequest)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundAmount(CheckRefundInitiatedRequest)}
     */
    @Test
    @DisplayName("Test getRefundAmount(CheckRefundInitiatedRequest)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundAmount() {
        // Arrange
        CheckRefundInitiatedRequest checkRefundInitiatedRequest = new CheckRefundInitiatedRequest();
        checkRefundInitiatedRequest.setIdentifierType(IdentifierType.RETURN_ID);
        checkRefundInitiatedRequest.setIdentifierValue("42");
        checkRefundInitiatedRequest.setSignature("Signature");
        checkRefundInitiatedRequest.setSource("Source");
        checkRefundInitiatedRequest.setVersion("1.0.2");

        // Act and Assert
        assertNull(refundUtilsServiceImpl.getRefundAmount(checkRefundInitiatedRequest));
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateExchangeOrderRefund(Integer, Integer)}.
     * <ul>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateExchangeOrderRefund(Integer, Integer)}
     */
    @Test
    @DisplayName("Test initiateExchangeOrderRefund(Integer, Integer); then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testInitiateExchangeOrderRefund_thenReturnFalse() {
        // Arrange
        when(refundFeignClient.initiateExchangeOrderRefund(Mockito.<Map<String, Object>>any())).thenReturn(null);

        // Act
        Boolean actualInitiateExchangeOrderRefundResult = refundUtilsServiceImpl.initiateExchangeOrderRefund(1, 1);

        // Assert
        verify(refundFeignClient).initiateExchangeOrderRefund(isA(Map.class));
        assertFalse(actualInitiateExchangeOrderRefundResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateExchangeOrderRefund(Integer, Integer)}.
     * <ul>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateExchangeOrderRefund(Integer, Integer)}
     */
    @Test
    @DisplayName("Test initiateExchangeOrderRefund(Integer, Integer); then return 'null'")
    @Tag("MaintainedByDiffblue")
    void testInitiateExchangeOrderRefund_thenReturnNull() {
        // Arrange
        when(refundFeignClient.initiateExchangeOrderRefund(Mockito.<Map<String, Object>>any()))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        Boolean actualInitiateExchangeOrderRefundResult = refundUtilsServiceImpl.initiateExchangeOrderRefund(1, 1);

        // Assert
        verify(refundFeignClient).initiateExchangeOrderRefund(isA(Map.class));
        assertNull(actualInitiateExchangeOrderRefundResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateExchangeOrderRefund(Integer, Integer)}.
     * <ul>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateExchangeOrderRefund(Integer, Integer)}
     */
    @Test
    @DisplayName("Test initiateExchangeOrderRefund(Integer, Integer); then return 'true'")
    @Tag("MaintainedByDiffblue")
    void testInitiateExchangeOrderRefund_thenReturnTrue() {
        // Arrange
        when(refundFeignClient.initiateExchangeOrderRefund(Mockito.<Map<String, Object>>any()))
                .thenReturn(new ResponseEntity<>(true, HttpStatusCode.valueOf(200)));

        // Act
        Boolean actualInitiateExchangeOrderRefundResult = refundUtilsServiceImpl.initiateExchangeOrderRefund(1, 1);

        // Assert
        verify(refundFeignClient).initiateExchangeOrderRefund(isA(Map.class));
        assertTrue(actualInitiateExchangeOrderRefundResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getMethodWiseRefundDetails(GetRefundAmountRequest)}.
     * <ul>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getMethodWiseRefundDetails(GetRefundAmountRequest)}
     */
    @Test
    @DisplayName("Test getMethodWiseRefundDetails(GetRefundAmountRequest); then return 'null'")
    @Tag("MaintainedByDiffblue")
    void testGetMethodWiseRefundDetails_thenReturnNull() {
        // Arrange
        when(refundFeignClient.getMethodWiseRefundDetails(Mockito.<GetRefundAmountRequest>any()))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        RefundDetailsForOrderDTO refundDetailsFromInventory = new RefundDetailsForOrderDTO();
        refundDetailsFromInventory.setItemWiseRefundDetailDTOS(new ArrayList<>());
        refundDetailsFromInventory.setRefundDTOS(new ArrayList<>());
        refundDetailsFromInventory.setRefundWalletDTOS(new ArrayList<>());

        GetRefundAmountRequest request = new GetRefundAmountRequest();
        request.setIdentifierType(IdentifierType.RETURN_ID);
        request.setIdentifierValues(new ArrayList<>());
        request.setOrderId("42");
        request.setRefundDetailsFromInventory(refundDetailsFromInventory);
        request.setSignature("Signature");
        request.setSource("Source");
        request.setVersion("1.0.2");

        // Act
        GetMethodWiseRefundDetailsResponse actualMethodWiseRefundDetails = refundUtilsServiceImpl
                .getMethodWiseRefundDetails(request);

        // Assert
        verify(refundFeignClient).getMethodWiseRefundDetails(isA(GetRefundAmountRequest.class));
        assertNull(actualMethodWiseRefundDetails);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse, ReturnDetailsResponse)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse, ReturnDetailsResponse)}
     */
    @Test
    @DisplayName("Test getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse, ReturnDetailsResponse)")
    @Tag("MaintainedByDiffblue")
    void testGetAndUpdateOrderRefundDetailsV2() {
        // Arrange
        GetMethodWiseRefundDetailsResponse getMethodWiseRefundDetailsResponse = new GetMethodWiseRefundDetailsResponse();
        getMethodWiseRefundDetailsResponse.setError("An error occurred");
        getMethodWiseRefundDetailsResponse.setMessage("Not all who wander are lost");
        getMethodWiseRefundDetailsResponse.setMethodSpecificRefundDetailsDTOMap(new HashMap<>());
        getMethodWiseRefundDetailsResponse.setStatus(1);
        getMethodWiseRefundDetailsResponse.setSuccess(true);

        ExchangeDetails exchangeDetails = new ExchangeDetails();
        exchangeDetails.setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetails.setExchangeChildId("42");
        exchangeDetails.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetails.setExchangeParentId("42");

        UwOrderDTO exchangeUwOrderDTO = new UwOrderDTO();
        exchangeUwOrderDTO.setB2bRefrenceItemId(1);
        exchangeUwOrderDTO.setBarcode("Barcode");
        exchangeUwOrderDTO.setBrand("Brand");
        exchangeUwOrderDTO.setChannel("Channel");
        exchangeUwOrderDTO.setClassification("Classification");
        exchangeUwOrderDTO.setClassificationName("Classification Name");
        exchangeUwOrderDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO.setFacilityCode("Facility Code");
        exchangeUwOrderDTO.setFitting("Fitting");
        exchangeUwOrderDTO.setIncrementId(1);
        exchangeUwOrderDTO.setIsFranchise("Is Franchise");
        exchangeUwOrderDTO.setIsLocalFittingRequired(true);
        exchangeUwOrderDTO.setItemId(1);
        exchangeUwOrderDTO.setLensPackage("java.text");
        exchangeUwOrderDTO.setMethod("Method");
        exchangeUwOrderDTO.setNavChannel("Nav Channel");
        exchangeUwOrderDTO.setParentUw(1);
        exchangeUwOrderDTO.setProductDeliveryType("Product Delivery Type");
        exchangeUwOrderDTO.setProductId(1);
        exchangeUwOrderDTO.setProductSku("Product Sku");
        exchangeUwOrderDTO.setProductValue("42");
        exchangeUwOrderDTO.setShipToStoreRequired(true);
        exchangeUwOrderDTO.setShipmentState("Shipment State");
        exchangeUwOrderDTO.setShipmentStatus("Shipment Status");
        exchangeUwOrderDTO.setShippingPackageId("42");
        exchangeUwOrderDTO.setTotalPrice(10.0d);
        exchangeUwOrderDTO.setUnicomOrderCode("Unicom Order Code");
        exchangeUwOrderDTO.setUwItemId(1);
        exchangeUwOrderDTO.setVsmStockout(1);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        PickUpAddressDetails pickUpAddressDetails = new PickUpAddressDetails();
        pickUpAddressDetails.setCity("Oxford");
        pickUpAddressDetails.setCountry("GB");
        pickUpAddressDetails.setCountryCode("GB");
        pickUpAddressDetails.setEmail("<EMAIL>");
        pickUpAddressDetails.setFirstName("Jane");
        pickUpAddressDetails.setLastName("Doe");
        pickUpAddressDetails.setPostCode("OX1 1PT");
        pickUpAddressDetails.setState("MD");
        pickUpAddressDetails.setStreet1("Street1");
        pickUpAddressDetails.setStreet2("Street2");
        pickUpAddressDetails.setTelephone("**********");

        ReturnStatusHeadingDetail returnStatusHeading = new ReturnStatusHeadingDetail();
        returnStatusHeading.setEstimatedDate(1L);
        returnStatusHeading.setHeading("Heading");
        returnStatusHeading.setSubHeading(new ArrayList<>());

        ReturnDetailsResponse response = new ReturnDetailsResponse();
        response.setAmountToRefund(10.0d);
        response.setCancellationStatusHistories(new ArrayList<>());
        response.setCancellationTimeLines(new ArrayList<>());
        response.setCourierName("Courier Name");
        response.setExchangeDetails(exchangeDetails);
        response.setExchangeDispatchPoint("Exchange Dispatch Point");
        response.setExchangeOrdersDTO(new ExchangeOrdersDTO());
        response.setExchangeStatus("Exchange Status");
        response.setExchangeTimelines(new ArrayList<>());
        response.setExchangeUwOrderDTO(exchangeUwOrderDTO);
        response.setFacilityCode("Facility Code");
        response.setGroupId(1L);
        response.setInitiationDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        response.setInsuranceApplied(true);
        response.setIsSelfDispatch(true);
        response.setItemId(1);
        response.setOrderAddressUpdates(new ArrayList<>());
        response.setOrderRefundedAmount(10.0d);
        response.setOrdersHeaderDTO(ordersHeaderDTO);
        response.setPickUpAddressDetails(pickUpAddressDetails);
        response.setPickupAttemptHistory(new ArrayList<>());
        response.setPickupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        response.setPickupStatus("Pickup Status");
        response.setReceivedAtWarehouse("Received At Warehouse");
        response.setRefundAmount(10.0d);
        response.setRefundArn("Refund Arn");
        response.setRefundDetails(new ArrayList<>());
        response.setRefundDispatchPoint("Refund Dispatch Point");
        response.setRefundIntentCreatedAt(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        response.setRefundMethodRequest("Refund Method Request");
        response.setRefundSource("Refund Source");
        response.setRefundStatus("Refund Status");
        response.setRefundTimeLines(new ArrayList<>());
        response.setRefundTotalTypeWise(new ArrayList<>());
        response.setReturnCancelRefundedAmount(10.0d);
        response.setReturnCancellable(true);
        response.setReturnId(1);
        response.setReturnLabelGenerated(true);
        response.setReturnMethod("Return Method");
        response.setReturnSource("Return Source");
        response.setReturnStatus("Return Status");
        response.setReturnStatusHeading(returnStatusHeading);
        response.setReturnStatusHistoryList(new ArrayList<>());
        response.setReturnTimeLines(new ArrayList<>());
        response.setReturnType("Return Type");
        response.setTotalRefundAmount(10.0d);
        response.setTotalRefundCompleteDate(1L);
        response.setTrackingNumber("42");
        response.setUwItemId(1);

        // Act
        refundUtilsServiceImpl.getAndUpdateOrderRefundDetailsV2(getMethodWiseRefundDetailsResponse, response);

        // Assert
        assertEquals(0.0d, response.getReturnCancelRefundedAmount());
        assertEquals(0.0d, response.getTotalRefundAmount());
        assertEquals(0L, response.getTotalRefundCompleteDate().longValue());
        assertTrue(response.getRefundTotalTypeWise().isEmpty());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse, ReturnDetailsResponse)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse, ReturnDetailsResponse)}
     */
    @Test
    @DisplayName("Test getAndUpdateOrderRefundDetailsV2(GetMethodWiseRefundDetailsResponse, ReturnDetailsResponse)")
    @Tag("MaintainedByDiffblue")
    void testGetAndUpdateOrderRefundDetailsV22() {
        // Arrange
        SpecificRefundDetailsDTO specificRefundDetailsDTO = new SpecificRefundDetailsDTO();
        specificRefundDetailsDTO.setRefundDetailsList(new ArrayList<>());
        specificRefundDetailsDTO.setTotalRefundedAmount(new RefundAmount());
        specificRefundDetailsDTO.setUwItemIdToRefundedAmountMap(new HashMap<>());

        HashMap<RefundMethod, SpecificRefundDetailsDTO> methodSpecificRefundDetailsDTOMap = new HashMap<>();
        methodSpecificRefundDetailsDTOMap.put(RefundMethod.CASHFREE, specificRefundDetailsDTO);

        GetMethodWiseRefundDetailsResponse getMethodWiseRefundDetailsResponse = new GetMethodWiseRefundDetailsResponse();
        getMethodWiseRefundDetailsResponse.setError("An error occurred");
        getMethodWiseRefundDetailsResponse.setMessage("Not all who wander are lost");
        getMethodWiseRefundDetailsResponse.setMethodSpecificRefundDetailsDTOMap(methodSpecificRefundDetailsDTOMap);
        getMethodWiseRefundDetailsResponse.setStatus(1);
        getMethodWiseRefundDetailsResponse.setSuccess(true);

        ExchangeDetails exchangeDetails = new ExchangeDetails();
        exchangeDetails.setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetails.setExchangeChildId("42");
        exchangeDetails.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetails.setExchangeParentId("42");

        UwOrderDTO exchangeUwOrderDTO = new UwOrderDTO();
        exchangeUwOrderDTO.setB2bRefrenceItemId(1);
        exchangeUwOrderDTO.setBarcode("Barcode");
        exchangeUwOrderDTO.setBrand("Brand");
        exchangeUwOrderDTO.setChannel("Channel");
        exchangeUwOrderDTO.setClassification("Classification");
        exchangeUwOrderDTO.setClassificationName("Classification Name");
        exchangeUwOrderDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO.setFacilityCode("Facility Code");
        exchangeUwOrderDTO.setFitting("Fitting");
        exchangeUwOrderDTO.setIncrementId(1);
        exchangeUwOrderDTO.setIsFranchise("Is Franchise");
        exchangeUwOrderDTO.setIsLocalFittingRequired(true);
        exchangeUwOrderDTO.setItemId(1);
        exchangeUwOrderDTO.setLensPackage("java.text");
        exchangeUwOrderDTO.setMethod("Method");
        exchangeUwOrderDTO.setNavChannel("Nav Channel");
        exchangeUwOrderDTO.setParentUw(1);
        exchangeUwOrderDTO.setProductDeliveryType("Product Delivery Type");
        exchangeUwOrderDTO.setProductId(1);
        exchangeUwOrderDTO.setProductSku("Product Sku");
        exchangeUwOrderDTO.setProductValue("42");
        exchangeUwOrderDTO.setShipToStoreRequired(true);
        exchangeUwOrderDTO.setShipmentState("Shipment State");
        exchangeUwOrderDTO.setShipmentStatus("Shipment Status");
        exchangeUwOrderDTO.setShippingPackageId("42");
        exchangeUwOrderDTO.setTotalPrice(10.0d);
        exchangeUwOrderDTO.setUnicomOrderCode("Unicom Order Code");
        exchangeUwOrderDTO.setUwItemId(1);
        exchangeUwOrderDTO.setVsmStockout(1);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        PickUpAddressDetails pickUpAddressDetails = new PickUpAddressDetails();
        pickUpAddressDetails.setCity("Oxford");
        pickUpAddressDetails.setCountry("GB");
        pickUpAddressDetails.setCountryCode("GB");
        pickUpAddressDetails.setEmail("<EMAIL>");
        pickUpAddressDetails.setFirstName("Jane");
        pickUpAddressDetails.setLastName("Doe");
        pickUpAddressDetails.setPostCode("OX1 1PT");
        pickUpAddressDetails.setState("MD");
        pickUpAddressDetails.setStreet1("Street1");
        pickUpAddressDetails.setStreet2("Street2");
        pickUpAddressDetails.setTelephone("**********");

        ReturnStatusHeadingDetail returnStatusHeading = new ReturnStatusHeadingDetail();
        returnStatusHeading.setEstimatedDate(1L);
        returnStatusHeading.setHeading("Heading");
        returnStatusHeading.setSubHeading(new ArrayList<>());

        ReturnDetailsResponse response = new ReturnDetailsResponse();
        response.setAmountToRefund(10.0d);
        response.setCancellationStatusHistories(new ArrayList<>());
        response.setCancellationTimeLines(new ArrayList<>());
        response.setCourierName("Courier Name");
        response.setExchangeDetails(exchangeDetails);
        response.setExchangeDispatchPoint("Exchange Dispatch Point");
        response.setExchangeOrdersDTO(new ExchangeOrdersDTO());
        response.setExchangeStatus("Exchange Status");
        response.setExchangeTimelines(new ArrayList<>());
        response.setExchangeUwOrderDTO(exchangeUwOrderDTO);
        response.setFacilityCode("Facility Code");
        response.setGroupId(1L);
        response.setInitiationDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        response.setInsuranceApplied(true);
        response.setIsSelfDispatch(true);
        response.setItemId(1);
        response.setOrderAddressUpdates(new ArrayList<>());
        response.setOrderRefundedAmount(10.0d);
        response.setOrdersHeaderDTO(ordersHeaderDTO);
        response.setPickUpAddressDetails(pickUpAddressDetails);
        response.setPickupAttemptHistory(new ArrayList<>());
        response.setPickupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        response.setPickupStatus("Pickup Status");
        response.setReceivedAtWarehouse("Received At Warehouse");
        response.setRefundAmount(10.0d);
        response.setRefundArn("Refund Arn");
        response.setRefundDetails(new ArrayList<>());
        response.setRefundDispatchPoint("Refund Dispatch Point");
        response.setRefundIntentCreatedAt(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        response.setRefundMethodRequest("Refund Method Request");
        response.setRefundSource("Refund Source");
        response.setRefundStatus("Refund Status");
        response.setRefundTimeLines(new ArrayList<>());
        response.setRefundTotalTypeWise(new ArrayList<>());
        response.setReturnCancelRefundedAmount(10.0d);
        response.setReturnCancellable(true);
        response.setReturnId(1);
        response.setReturnLabelGenerated(true);
        response.setReturnMethod("Return Method");
        response.setReturnSource("Return Source");
        response.setReturnStatus("Return Status");
        response.setReturnStatusHeading(returnStatusHeading);
        response.setReturnStatusHistoryList(new ArrayList<>());
        response.setReturnTimeLines(new ArrayList<>());
        response.setReturnType("Return Type");
        response.setTotalRefundAmount(10.0d);
        response.setTotalRefundCompleteDate(1L);
        response.setTrackingNumber("42");
        response.setUwItemId(1);

        // Act
        refundUtilsServiceImpl.getAndUpdateOrderRefundDetailsV2(getMethodWiseRefundDetailsResponse, response);

        // Assert
        List<OrderRefundDetails> refundTotalTypeWise = response.getRefundTotalTypeWise();
        assertEquals(1, refundTotalTypeWise.size());
        OrderRefundDetails getResult = refundTotalTypeWise.get(0);
        assertEquals("cashfree", getResult.getRefundMethod());
        assertEquals(0.0d, getResult.getRefundAmount());
        assertEquals(0.0d, response.getReturnCancelRefundedAmount());
        assertEquals(0.0d, response.getTotalRefundAmount());
        assertEquals(0L, response.getTotalRefundCompleteDate().longValue());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isAlreadyRefunded(int, Integer)}.
     * <ul>
     *   <li>Given {@link FraudCustomerDTO} (default constructor) ExchangeCount is three.</li>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isAlreadyRefunded(int, Integer)}
     */
    @Test
    @DisplayName("Test isAlreadyRefunded(int, Integer); given FraudCustomerDTO (default constructor) ExchangeCount is three; then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testIsAlreadyRefunded_givenFraudCustomerDTOExchangeCountIsThree_thenReturnFalse() {
        // Arrange
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder().countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());

        ExchangeDetails exchangeDetailsForBackSync = new ExchangeDetails();
        exchangeDetailsForBackSync
                .setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetailsForBackSync.setExchangeChildId("42");
        exchangeDetailsForBackSync.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetailsForBackSync.setExchangeParentId("42");
        OrderExchangeCancellationDetailsBuilder builderResult2 = OrderExchangeCancellationDetails.builder();
        OrderExchangeCancellationDetailsBuilder exchangeDetailsForBackSyncResult = builderResult2
                .canceledOrdersDTOList(new ArrayList<>())
                .exchangeDetailsForBackSync(exchangeDetailsForBackSync);
        OrderExchangeCancellationDetails orderExchangeCancellationDetails = exchangeDetailsForBackSyncResult
                .exchangeOrdersDTOList(new ArrayList<>())
                .build();
        OrderInfoResponseDTOBuilder orderExchangeCancellationDetailsResult = orderAddressUpdatesResult
                .orderExchangeCancellationDetails(orderExchangeCancellationDetails);
        OrderInfoResponseDTOBuilder ordersResult = orderExchangeCancellationDetailsResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
        ordersHeader.setIsExchangeOrder(true);
        ordersHeader.setLkCountry("GB");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
        ordersHeader.setPaymentMode("Payment Mode");
        OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult.uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = new ResponseEntity<>(buildResult,
                HttpStatusCode.valueOf(200));

        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        // Act
        boolean actualIsAlreadyRefundedResult = refundUtilsServiceImpl.isAlreadyRefunded(1, 1);

        // Assert
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        assertFalse(actualIsAlreadyRefundedResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isAlreadyRefunded(int, Integer)}.
     * <ul>
     *   <li>Given {@link ItemWiseGetAmountDTO} (default constructor) ActualAmount is ten.</li>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isAlreadyRefunded(int, Integer)}
     */
    @Test
    @DisplayName("Test isAlreadyRefunded(int, Integer); given ItemWiseGetAmountDTO (default constructor) ActualAmount is ten; then return 'true'")
    @Tag("MaintainedByDiffblue")
    void testIsAlreadyRefunded_givenItemWiseGetAmountDTOActualAmountIsTen_thenReturnTrue() {
        // Arrange
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder().countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseGetAmountDTO itemWiseGetAmountDTO = new ItemWiseGetAmountDTO();
        itemWiseGetAmountDTO.setActualAmount(10.0d);
        itemWiseGetAmountDTO.setRefundAmount(10.0d);
        itemWiseGetAmountDTO.setUwItemId(1);

        ArrayList<ItemWiseGetAmountDTO> itemIds = new ArrayList<>();
        itemIds.add(itemWiseGetAmountDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(itemIds);
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());

        ExchangeDetails exchangeDetailsForBackSync = new ExchangeDetails();
        exchangeDetailsForBackSync
                .setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetailsForBackSync.setExchangeChildId("42");
        exchangeDetailsForBackSync.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetailsForBackSync.setExchangeParentId("42");
        OrderExchangeCancellationDetailsBuilder builderResult2 = OrderExchangeCancellationDetails.builder();
        OrderExchangeCancellationDetailsBuilder exchangeDetailsForBackSyncResult = builderResult2
                .canceledOrdersDTOList(new ArrayList<>())
                .exchangeDetailsForBackSync(exchangeDetailsForBackSync);
        OrderExchangeCancellationDetails orderExchangeCancellationDetails = exchangeDetailsForBackSyncResult
                .exchangeOrdersDTOList(new ArrayList<>())
                .build();
        OrderInfoResponseDTOBuilder orderExchangeCancellationDetailsResult = orderAddressUpdatesResult
                .orderExchangeCancellationDetails(orderExchangeCancellationDetails);
        OrderInfoResponseDTOBuilder ordersResult = orderExchangeCancellationDetailsResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
        ordersHeader.setIsExchangeOrder(true);
        ordersHeader.setLkCountry("GB");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
        ordersHeader.setPaymentMode("Payment Mode");
        OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult.uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = new ResponseEntity<>(buildResult,
                HttpStatusCode.valueOf(200));

        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        // Act
        boolean actualIsAlreadyRefundedResult = refundUtilsServiceImpl.isAlreadyRefunded(1, 1);

        // Assert
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        assertTrue(actualIsAlreadyRefundedResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isAlreadyRefunded(int, Integer)}.
     * <ul>
     *   <li>Given {@link ItemWiseGetAmountDTO} (default constructor) ActualAmount is zero.</li>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isAlreadyRefunded(int, Integer)}
     */
    @Test
    @DisplayName("Test isAlreadyRefunded(int, Integer); given ItemWiseGetAmountDTO (default constructor) ActualAmount is zero; then return 'true'")
    @Tag("MaintainedByDiffblue")
    void testIsAlreadyRefunded_givenItemWiseGetAmountDTOActualAmountIsZero_thenReturnTrue() {
        // Arrange
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder().countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseGetAmountDTO itemWiseGetAmountDTO = new ItemWiseGetAmountDTO();
        itemWiseGetAmountDTO.setActualAmount(10.0d);
        itemWiseGetAmountDTO.setRefundAmount(10.0d);
        itemWiseGetAmountDTO.setUwItemId(1);

        ItemWiseGetAmountDTO itemWiseGetAmountDTO2 = new ItemWiseGetAmountDTO();
        itemWiseGetAmountDTO2.setActualAmount(0.0d);
        itemWiseGetAmountDTO2.setRefundAmount(0.0d);
        itemWiseGetAmountDTO2.setUwItemId(2);

        ArrayList<ItemWiseGetAmountDTO> itemIds = new ArrayList<>();
        itemIds.add(itemWiseGetAmountDTO2);
        itemIds.add(itemWiseGetAmountDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(itemIds);
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());

        ExchangeDetails exchangeDetailsForBackSync = new ExchangeDetails();
        exchangeDetailsForBackSync
                .setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetailsForBackSync.setExchangeChildId("42");
        exchangeDetailsForBackSync.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetailsForBackSync.setExchangeParentId("42");
        OrderExchangeCancellationDetailsBuilder builderResult2 = OrderExchangeCancellationDetails.builder();
        OrderExchangeCancellationDetailsBuilder exchangeDetailsForBackSyncResult = builderResult2
                .canceledOrdersDTOList(new ArrayList<>())
                .exchangeDetailsForBackSync(exchangeDetailsForBackSync);
        OrderExchangeCancellationDetails orderExchangeCancellationDetails = exchangeDetailsForBackSyncResult
                .exchangeOrdersDTOList(new ArrayList<>())
                .build();
        OrderInfoResponseDTOBuilder orderExchangeCancellationDetailsResult = orderAddressUpdatesResult
                .orderExchangeCancellationDetails(orderExchangeCancellationDetails);
        OrderInfoResponseDTOBuilder ordersResult = orderExchangeCancellationDetailsResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
        ordersHeader.setIsExchangeOrder(true);
        ordersHeader.setLkCountry("GB");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
        ordersHeader.setPaymentMode("Payment Mode");
        OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult.uwOrderAttributes(new ArrayList<>());
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(new ArrayList<>()).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = new ResponseEntity<>(buildResult,
                HttpStatusCode.valueOf(200));

        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        // Act
        boolean actualIsAlreadyRefundedResult = refundUtilsServiceImpl.isAlreadyRefunded(1, 1);

        // Assert
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        assertTrue(actualIsAlreadyRefundedResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateRefundAsynchronously(Integer, Integer, Integer, String)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateRefundAsynchronously(Integer, Integer, Integer, String)}
     */
    @Test
    @DisplayName("Test initiateRefundAsynchronously(Integer, Integer, Integer, String)")
    @Tag("MaintainedByDiffblue")
    void testInitiateRefundAsynchronously() {
        // TODO: Diffblue Cover was only able to create a partial test for this method:
        //   Reason: Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     RefundUtilsServiceImpl.authorizationService
        //     RefundUtilsServiceImpl.baseUrlRefundService
        //     RefundUtilsServiceImpl.exchangeItemDispatchService
        //     RefundUtilsServiceImpl.gson
        //     RefundUtilsServiceImpl.kafkaService
        //     RefundUtilsServiceImpl.objectMapper
        //     RefundUtilsServiceImpl.orderOpsFeignClient
        //     RefundUtilsServiceImpl.refundAuthorizationService
        //     RefundUtilsServiceImpl.refundFeignClient
        //     RefundUtilsServiceImpl.refundMethodLabelService
        //     RefundUtilsServiceImpl.restTemplate
        //     RefundUtilsServiceImpl.returnEventRepository
        //     RefundUtilsServiceImpl.returnEventService
        //     RefundUtilsServiceImpl.returnOrderActionService
        //     RefundUtilsServiceImpl.returnRefundRuleRepository
        //     RefundUtilsServiceImpl.returnStatusTimelineMappingCache
        //     RefundUtilsServiceImpl.returnUtil
        //     RefundUtilsServiceImpl.systemPreferenceService

        // Arrange and Act
        (new RefundUtilsServiceImpl()).initiateRefundAsynchronously(1, 1, 1, "Trigger Point");
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}
     */
    @Test
    @DisplayName("Test initiateRefundRequest(CreateRefundRequest)")
    @Tag("MaintainedByDiffblue")
    void testInitiateRefundRequest() throws RestClientException {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<CreateRefundRequest>any(),
                Mockito.<HttpHeaders>any())).thenReturn(new HttpHeaders());
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class)))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        CreateRefundRequest payload = new CreateRefundRequest();
        payload.setClient("Client");
        payload.setCustomerRefundRequired(true);
        payload.setFranchiseRefundRequired(true);
        payload.setIdentifierType(IdentifierType.RETURN_ID);
        payload.setIdentifierValue("42");
        payload.setOrderId(1L);
        payload.setRefundAmount(new RefundAmount());
        payload.setRefundReason(RefundReason.FAST_REFUND);
        payload.setRefundTarget(RefundTarget.STORECREDIT);
        payload.setRefundTriggerPoint("Refund Trigger Point");
        payload.setRemarks("Remarks");
        payload.setType(RefundType.CREDIT);
        payload.setUserId("42");

        // Act
        boolean actualInitiateRefundRequestResult = refundUtilsServiceImpl.initiateRefundRequest(payload);

        // Assert
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(CreateRefundRequest.class),
                isA(HttpHeaders.class));
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/refund-request"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertFalse(actualInitiateRefundRequestResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}
     */
    @Test
    @DisplayName("Test initiateRefundRequest(CreateRefundRequest)")
    @Tag("MaintainedByDiffblue")
    void testInitiateRefundRequest2() throws RestClientException {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<CreateRefundRequest>any(),
                Mockito.<HttpHeaders>any())).thenReturn(new HttpHeaders());
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class)))
                .thenReturn(new ResponseEntity<>("Body", HttpStatusCode.valueOf(200)));

        CreateRefundRequest payload = new CreateRefundRequest();
        payload.setClient("Client");
        payload.setCustomerRefundRequired(true);
        payload.setFranchiseRefundRequired(true);
        payload.setIdentifierType(IdentifierType.RETURN_ID);
        payload.setIdentifierValue("42");
        payload.setOrderId(1L);
        payload.setRefundAmount(new RefundAmount());
        payload.setRefundReason(RefundReason.FAST_REFUND);
        payload.setRefundTarget(RefundTarget.STORECREDIT);
        payload.setRefundTriggerPoint("Refund Trigger Point");
        payload.setRemarks("Remarks");
        payload.setType(RefundType.CREDIT);
        payload.setUserId("42");

        // Act
        boolean actualInitiateRefundRequestResult = refundUtilsServiceImpl.initiateRefundRequest(payload);

        // Assert
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(CreateRefundRequest.class),
                isA(HttpHeaders.class));
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/refund-request"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertFalse(actualInitiateRefundRequestResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}.
     * <ul>
     *   <li>Given {@link ResponseEntity} {@link HttpEntity#getBody()} return {@code Body}.</li>
     *   <li>Then calls {@link HttpEntity#getBody()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}
     */
    @Test
    @DisplayName("Test initiateRefundRequest(CreateRefundRequest); given ResponseEntity getBody() return 'Body'; then calls getBody()")
    @Tag("MaintainedByDiffblue")
    void testInitiateRefundRequest_givenResponseEntityGetBodyReturnBody_thenCallsGetBody() throws RestClientException {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<CreateRefundRequest>any(),
                Mockito.<HttpHeaders>any())).thenReturn(new HttpHeaders());
        ResponseEntity<Object> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn("Body");
        when(responseEntity.getStatusCode()).thenReturn(null);
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class))).thenReturn(responseEntity);

        CreateRefundRequest payload = new CreateRefundRequest();
        payload.setClient("Client");
        payload.setCustomerRefundRequired(true);
        payload.setFranchiseRefundRequired(true);
        payload.setIdentifierType(IdentifierType.RETURN_ID);
        payload.setIdentifierValue("42");
        payload.setOrderId(1L);
        payload.setRefundAmount(new RefundAmount());
        payload.setRefundReason(RefundReason.FAST_REFUND);
        payload.setRefundTarget(RefundTarget.STORECREDIT);
        payload.setRefundTriggerPoint("Refund Trigger Point");
        payload.setRemarks("Remarks");
        payload.setType(RefundType.CREDIT);
        payload.setUserId("42");

        // Act
        boolean actualInitiateRefundRequestResult = refundUtilsServiceImpl.initiateRefundRequest(payload);

        // Assert
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(CreateRefundRequest.class),
                isA(HttpHeaders.class));
        verify(responseEntity).getBody();
        verify(responseEntity).getStatusCode();
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/refund-request"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertFalse(actualInitiateRefundRequestResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}.
     * <ul>
     *   <li>Given {@link RestTemplate} {@link RestTemplate#exchange(String, HttpMethod, HttpEntity, Class, Object[])} return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateRefundRequest(CreateRefundRequest)}
     */
    @Test
    @DisplayName("Test initiateRefundRequest(CreateRefundRequest); given RestTemplate exchange(String, HttpMethod, HttpEntity, Class, Object[]) return 'null'")
    @Tag("MaintainedByDiffblue")
    void testInitiateRefundRequest_givenRestTemplateExchangeReturnNull() throws RestClientException {
        // Arrange
        when(iRefundAuthorizationService.addRefundAuthorizationHeaders(Mockito.<CreateRefundRequest>any(),
                Mockito.<HttpHeaders>any())).thenReturn(new HttpHeaders());
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class))).thenReturn(null);

        CreateRefundRequest payload = new CreateRefundRequest();
        payload.setClient("Client");
        payload.setCustomerRefundRequired(true);
        payload.setFranchiseRefundRequired(true);
        payload.setIdentifierType(IdentifierType.RETURN_ID);
        payload.setIdentifierValue("42");
        payload.setOrderId(1L);
        payload.setRefundAmount(new RefundAmount());
        payload.setRefundReason(RefundReason.FAST_REFUND);
        payload.setRefundTarget(RefundTarget.STORECREDIT);
        payload.setRefundTriggerPoint("Refund Trigger Point");
        payload.setRemarks("Remarks");
        payload.setType(RefundType.CREDIT);
        payload.setUserId("42");

        // Act
        boolean actualInitiateRefundRequestResult = refundUtilsServiceImpl.initiateRefundRequest(payload);

        // Assert
        verify(iRefundAuthorizationService).addRefundAuthorizationHeaders(isA(CreateRefundRequest.class),
                isA(HttpHeaders.class));
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/refund-request"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertFalse(actualInitiateRefundRequestResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}
     */
    @Test
    @DisplayName("Test getRefundMethod(String, String, String, String, Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundMethod() throws RestClientException {
        // Arrange
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class)))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        RefundMethodResponse actualRefundMethod = refundUtilsServiceImpl.getRefundMethod("Refund Initiated At",
                "Payment Mode", "Payment Method", "GB", 1);

        // Assert
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/get-refund-method"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertNull(actualRefundMethod);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}
     */
    @Test
    @DisplayName("Test getRefundMethod(String, String, String, String, Integer)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundMethod2() throws RestClientException {
        // Arrange
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class)))
                .thenReturn(new ResponseEntity<>("Body", HttpStatusCode.valueOf(200)));

        // Act
        RefundMethodResponse actualRefundMethod = refundUtilsServiceImpl.getRefundMethod("Refund Initiated At",
                "Payment Mode", "Payment Method", "GB", 1);

        // Assert
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/get-refund-method"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertNull(actualRefundMethod);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}.
     * <ul>
     *   <li>Given {@link RestTemplate} {@link RestTemplate#exchange(String, HttpMethod, HttpEntity, Class, Object[])} return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}
     */
    @Test
    @DisplayName("Test getRefundMethod(String, String, String, String, Integer); given RestTemplate exchange(String, HttpMethod, HttpEntity, Class, Object[]) return 'null'")
    @Tag("MaintainedByDiffblue")
    void testGetRefundMethod_givenRestTemplateExchangeReturnNull() throws RestClientException {
        // Arrange
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class))).thenReturn(null);

        // Act
        RefundMethodResponse actualRefundMethod = refundUtilsServiceImpl.getRefundMethod("Refund Initiated At",
                "Payment Mode", "Payment Method", "GB", 1);

        // Assert
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/get-refund-method"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertNull(actualRefundMethod);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}.
     * <ul>
     *   <li>Then calls {@link ResponseEntity#getStatusCode()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundMethod(String, String, String, String, Integer)}
     */
    @Test
    @DisplayName("Test getRefundMethod(String, String, String, String, Integer); then calls getStatusCode()")
    @Tag("MaintainedByDiffblue")
    void testGetRefundMethod_thenCallsGetStatusCode() throws RestClientException {
        // Arrange
        ResponseEntity<Object> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getStatusCode()).thenReturn(null);
        when(restTemplate.exchange(Mockito.<String>any(), Mockito.<HttpMethod>any(), Mockito.<HttpEntity<Object>>any(),
                Mockito.<Class<Object>>any(), isA(Object[].class))).thenReturn(responseEntity);

        // Act
        RefundMethodResponse actualRefundMethod = refundUtilsServiceImpl.getRefundMethod("Refund Initiated At",
                "Payment Mode", "Payment Method", "GB", 1);

        // Assert
        verify(responseEntity).getStatusCode();
        verify(restTemplate).exchange(eq("${refund.service.baseurl}/v1.0/get-refund-method"), isA(HttpMethod.class),
                isA(HttpEntity.class), isA(Class.class), isA(Object[].class));
        assertNull(actualRefundMethod);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getIsFranchiseRefundRequired(Integer, RefundTriggerPoint, String, RefundReason)}.
     * <ul>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getIsFranchiseRefundRequired(Integer, RefundTriggerPoint, String, RefundReason)}
     */
    @Test
    @DisplayName("Test getIsFranchiseRefundRequired(Integer, RefundTriggerPoint, String, RefundReason); then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testGetIsFranchiseRefundRequired_thenReturnFalse() {
        // Arrange
        when(orderOpsFeignClient.checkFranchiseRefundRequired(Mockito.<RefundRequestDTO>any()))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        boolean actualIsFranchiseRefundRequired = refundUtilsServiceImpl.getIsFranchiseRefundRequired(1,
                RefundTriggerPoint.RETURN_INITIATION, "Source", RefundReason.FAST_REFUND);

        // Assert
        verify(orderOpsFeignClient).checkFranchiseRefundRequired(isA(RefundRequestDTO.class));
        assertFalse(actualIsFranchiseRefundRequired);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getIsFranchiseRefundRequired(Integer, RefundTriggerPoint, String, RefundReason)}.
     * <ul>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getIsFranchiseRefundRequired(Integer, RefundTriggerPoint, String, RefundReason)}
     */
    @Test
    @DisplayName("Test getIsFranchiseRefundRequired(Integer, RefundTriggerPoint, String, RefundReason); then return 'true'")
    @Tag("MaintainedByDiffblue")
    void testGetIsFranchiseRefundRequired_thenReturnTrue() {
        // Arrange
        when(orderOpsFeignClient.checkFranchiseRefundRequired(Mockito.<RefundRequestDTO>any()))
                .thenReturn(new ResponseEntity<>(true, HttpStatusCode.valueOf(200)));

        // Act
        boolean actualIsFranchiseRefundRequired = refundUtilsServiceImpl.getIsFranchiseRefundRequired(1,
                RefundTriggerPoint.RETURN_INITIATION, "Source", RefundReason.FAST_REFUND);

        // Assert
        verify(orderOpsFeignClient).checkFranchiseRefundRequired(isA(RefundRequestDTO.class));
        assertTrue(actualIsFranchiseRefundRequired);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#initiateRefund(RefundProcessDTO)}.
     * <ul>
     *   <li>Then calls {@link OrderOpsFeignClient#checkRefundSwitchActive(String, String)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#initiateRefund(RefundProcessDTO)}
     */
    @Test
    @DisplayName("Test initiateRefund(RefundProcessDTO); then calls checkRefundSwitchActive(String, String)")
    @Tag("MaintainedByDiffblue")
    void testInitiateRefund_thenCallsCheckRefundSwitchActive() {
        // Arrange
        CheckRefundSwitchActiveDTO checkRefundSwitchActiveDTO = new CheckRefundSwitchActiveDTO();
        checkRefundSwitchActiveDTO.setNewRefundFlow(true);
        ResponseEntity<CheckRefundSwitchActiveDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(checkRefundSwitchActiveDTO);
        when(responseEntity.getStatusCode()).thenReturn(HttpStatusCode.valueOf(200));
        when(orderOpsFeignClient.checkRefundSwitchActive(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(responseEntity);

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftCardDiscount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setInsuranceBenefitDiscount(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmountDTO.setLenskartPlusDiscount(10.0d);
        itemWiseAmountDTO.setPaymentNotCapture(true);
        itemWiseAmountDTO.setPrepaidWeb(10.0d);
        itemWiseAmountDTO.setStoreCredit(10.0d);
        itemWiseAmountDTO.setTPendingPGAmount(10.0d);
        itemWiseAmountDTO.setTotalCodPrice(10.0d);
        itemWiseAmountDTO.setTotalItemAmount(10.0d);
        itemWiseAmountDTO.setTotalOnlinePrice(10.0d);
        itemWiseAmountDTO.setTotalPrepaidAmt(10.0d);
        itemWiseAmountDTO.setTotalPriceOfOrder(10.0d);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("ORDER_ID");

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        purchaseOrderDetailsDTO.setBranded(true);
        purchaseOrderDetailsDTO.setGetIsBlacklisted(true);
        purchaseOrderDetailsDTO.setIdentifierType("ORDER_ID");
        purchaseOrderDetailsDTO.setIdentifierValue("42");
        purchaseOrderDetailsDTO.setItemWiseAmountDTO(itemWiseAmountDTO);
        purchaseOrderDetailsDTO.setItemWisePrices(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrderAddressUpdateDTOs(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrders(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        ShippingStatusDetailBuilder builderResult = ShippingStatusDetail.builder();
        ShippingStatusDetailBuilder DeliveredDateResult = builderResult
                .DeliveredDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        ShippingStatusDetailBuilder codAmountResult = DeliveredDateResult
                .awbAssignedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .carrierCode("Carrier Code")
                .ccFlag(1)
                .ccUploadDate("2020-03-01")
                .codAmount("10");
        ShippingStatusDetailBuilder followedupCountResult = codAmountResult
                .complete_time(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .deleted("Jan 1, 2020 11:00am GMT+0100")
                .docketNumber("42")
                .email("<EMAIL>")
                .execEmailId("42")
                .followedupCount(3);
        ShippingStatusDetailBuilder invoicedTimeResult = followedupCountResult
                .followedupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .invoicedDate("2020-03-01")
                .invoicedTime("Invoiced Time");
        ShippingStatusDetailBuilder phoneResult = invoicedTimeResult
                .lastFollowedupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .leadId("42")
                .linkToOrderNo("Link To Order No")
                .manifestDate("2020-03-01")
                .manifestNumber("42")
                .manifestTime("Manifest Time")
                .name("Name")
                .ndrSmsSent(1)
                .npsReason("Just cause")
                .orderNo(1)
                .packed("Packed")
                .paymentReceived("Payment Received")
                .phone("**********");
        ShippingStatusDetail shippingStatusDetail = phoneResult
                .pickDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .pickedBy("Picked By")
                .receivedBy("Received By")
                .referralApiSent(1)
                .region("us-east-2")
                .routingCode("Routing Code")
                .shipmentStatus("Shipment Status")
                .shipped("Shipped")
                .shippingMode("Shipping Mode")
                .shippingPackageId("42")
                .shipping_time("Shipping time")
                .statusId(1)
                .trackingNo("Tracking No")
                .unicomOrderCode("Unicom Order Code")
                .updatedAt("2020-03-01")
                .updatedCrm("2020-03-01")
                .updatedMagento("2020-03-01")
                .warehouseLocation("Warehouse Location")
                .build();
        purchaseOrderDetailsDTO.setShippingStatusDetail(shippingStatusDetail);
        purchaseOrderDetailsDTO.setUwOrders(new ArrayList<>());

        Result result = new Result();
        result.setDispensingFlag(true);
        result.setGroupId(1L);
        result.setReturnLabelCreated(true);
        result.setReturns(new ArrayList<>());
        result.setSuccess(true);

        ReversePickupDetails reversePickupDetails = new ReversePickupDetails();
        reversePickupDetails.setAwbNumber("42");
        reversePickupDetails.setCourier("ORDER_ID");
        reversePickupDetails.setIncrementId(1);
        reversePickupDetails.setReverseCreatedAt("Jan 1, 2020 8:00am GMT+0100");

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setB2bRefrenceItemId(1);
        uwOrder.setBarcode("ORDER_ID");
        uwOrder.setBrand("ORDER_ID");
        uwOrder.setChannel("ORDER_ID");
        uwOrder.setClassification("ORDER_ID");
        uwOrder.setClassificationName("ORDER_ID");
        uwOrder.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder.setFacilityCode("ORDER_ID");
        uwOrder.setFitting("ORDER_ID");
        uwOrder.setIncrementId(1);
        uwOrder.setIsFranchise("ORDER_ID");
        uwOrder.setIsLocalFittingRequired(true);
        uwOrder.setItemId(1);
        uwOrder.setLensPackage("java.text");
        uwOrder.setMethod("ORDER_ID");
        uwOrder.setNavChannel("ORDER_ID");
        uwOrder.setParentUw(1);
        uwOrder.setProductDeliveryType("ORDER_ID");
        uwOrder.setProductId(1);
        uwOrder.setProductSku("ORDER_ID");
        uwOrder.setProductValue("42");
        uwOrder.setShipToStoreRequired(true);
        uwOrder.setShipmentState("ORDER_ID");
        uwOrder.setShipmentStatus("ORDER_ID");
        uwOrder.setShippingPackageId("42");
        uwOrder.setTotalPrice(10.0d);
        uwOrder.setUnicomOrderCode("ORDER_ID");
        uwOrder.setUwItemId(1);
        uwOrder.setVsmStockout(1);

        UwOrderDTO uwOrderWH = new UwOrderDTO();
        uwOrderWH.setB2bRefrenceItemId(1);
        uwOrderWH.setBarcode("ORDER_ID");
        uwOrderWH.setBrand("ORDER_ID");
        uwOrderWH.setChannel("ORDER_ID");
        uwOrderWH.setClassification("ORDER_ID");
        uwOrderWH.setClassificationName("ORDER_ID");
        uwOrderWH.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderWH
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderWH.setFacilityCode("ORDER_ID");
        uwOrderWH.setFitting("ORDER_ID");
        uwOrderWH.setIncrementId(1);
        uwOrderWH.setIsFranchise("ORDER_ID");
        uwOrderWH.setIsLocalFittingRequired(true);
        uwOrderWH.setItemId(1);
        uwOrderWH.setLensPackage("java.text");
        uwOrderWH.setMethod("ORDER_ID");
        uwOrderWH.setNavChannel("ORDER_ID");
        uwOrderWH.setParentUw(1);
        uwOrderWH.setProductDeliveryType("ORDER_ID");
        uwOrderWH.setProductId(1);
        uwOrderWH.setProductSku("ORDER_ID");
        uwOrderWH.setProductValue("42");
        uwOrderWH.setShipToStoreRequired(true);
        uwOrderWH.setShipmentState("ORDER_ID");
        uwOrderWH.setShipmentStatus("ORDER_ID");
        uwOrderWH.setShippingPackageId("42");
        uwOrderWH.setTotalPrice(10.0d);
        uwOrderWH.setUnicomOrderCode("ORDER_ID");
        uwOrderWH.setUwItemId(1);
        uwOrderWH.setVsmStockout(1);

        ReturnCreationResponse returnCreationResponse = new ReturnCreationResponse();
        returnCreationResponse.setEntity("Entity");
        returnCreationResponse.setIncrementId(1);
        returnCreationResponse.setItemRefundMap(new HashMap<>());
        returnCreationResponse.setPurchaseOrderDetailsDTO(purchaseOrderDetailsDTO);
        returnCreationResponse.setResult(result);
        returnCreationResponse.setReturnId(1);
        returnCreationResponse.setReversePickupDetails(reversePickupDetails);
        returnCreationResponse.setStatus(1);
        returnCreationResponse.setUwOrder(uwOrder);
        returnCreationResponse.setUwOrderWH(uwOrderWH);
        returnCreationResponse.setUwOrders(new ArrayList<>());

        RefundProcessDTO refundProcessDTO = new RefundProcessDTO();
        refundProcessDTO.setReturnCreationResponse(returnCreationResponse);

        // Act
        refundUtilsServiceImpl.initiateRefund(refundProcessDTO);

        // Assert
        verify(orderOpsFeignClient).checkRefundSwitchActive(eq("ORDER_ID"), eq("null"));
        verify(responseEntity, atLeast(1)).getBody();
        verify(responseEntity).getStatusCode();
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}.
     * <ul>
     *   <li>Then throw {@link Exception}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}
     */
    @Test
    @DisplayName("Test isFranchiseRefundEligible(String, RefundTriggerPoint); then throw Exception")
    @Tag("MaintainedByDiffblue")
    void testIsFranchiseRefundEligible_thenThrowException() throws Exception {
        // Arrange
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenThrow(new Exception("[isFranchiseRefundEligible] refundTriggerPoint={}"));

        // Act and Assert
        assertThrows(Exception.class,
                () -> refundUtilsServiceImpl.isFranchiseRefundEligible("Nav Channel", RefundTriggerPoint.RETURN_INITIATION));
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}.
     * <ul>
     *   <li>When empty string.</li>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}
     */
    @Test
    @DisplayName("Test isFranchiseRefundEligible(String, RefundTriggerPoint); when empty string; then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testIsFranchiseRefundEligible_whenEmptyString_thenReturnFalse() throws Exception {
        // Arrange
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        // Act
        boolean actualIsFranchiseRefundEligibleResult = refundUtilsServiceImpl.isFranchiseRefundEligible("",
                RefundTriggerPoint.COURIER_PICKUP);

        // Assert
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertFalse(actualIsFranchiseRefundEligibleResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}.
     * <ul>
     *   <li>When {@code Nav Channel}.</li>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}
     */
    @Test
    @DisplayName("Test isFranchiseRefundEligible(String, RefundTriggerPoint); when 'Nav Channel'; then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testIsFranchiseRefundEligible_whenNavChannel_thenReturnFalse() throws Exception {
        // Arrange
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        // Act
        boolean actualIsFranchiseRefundEligibleResult = refundUtilsServiceImpl.isFranchiseRefundEligible("Nav Channel",
                RefundTriggerPoint.COURIER_PICKUP);

        // Assert
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertFalse(actualIsFranchiseRefundEligibleResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}.
     * <ul>
     *   <li>When {@code null}.</li>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}
     */
    @Test
    @DisplayName("Test isFranchiseRefundEligible(String, RefundTriggerPoint); when 'null'; then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testIsFranchiseRefundEligible_whenNull_thenReturnFalse() throws Exception {
        // Arrange
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        // Act
        boolean actualIsFranchiseRefundEligibleResult = refundUtilsServiceImpl.isFranchiseRefundEligible(null,
                RefundTriggerPoint.COURIER_PICKUP);

        // Assert
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertFalse(actualIsFranchiseRefundEligibleResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}.
     * <ul>
     *   <li>When {@code RETURN_INITIATION}.</li>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isFranchiseRefundEligible(String, RefundTriggerPoint)}
     */
    @Test
    @DisplayName("Test isFranchiseRefundEligible(String, RefundTriggerPoint); when 'RETURN_INITIATION'; then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testIsFranchiseRefundEligible_whenReturnInitiation_thenReturnFalse() throws Exception {
        // Arrange
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        // Act
        boolean actualIsFranchiseRefundEligibleResult = refundUtilsServiceImpl.isFranchiseRefundEligible("Nav Channel",
                RefundTriggerPoint.RETURN_INITIATION);

        // Assert
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertFalse(actualIsFranchiseRefundEligibleResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>Given {@link ReturnEvent} (default constructor) Event is {@code awb_assigned}.</li>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); given ReturnEvent (default constructor) Event is 'awb_assigned'; then return 'true'")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_givenReturnEventEventIsAwbAssigned_thenReturnTrue() {
        // Arrange
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent.setEvent("AnyReceivingPoint");
        returnEvent.setId(1);
        returnEvent.setRemarks("AnyReceivingPoint");
        returnEvent.setReturnId(1);
        returnEvent.setReturnRequestId(1);
        returnEvent.setSource("AnyReceivingPoint");

        ReturnEvent returnEvent2 = new ReturnEvent();
        returnEvent2.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent2.setEvent("awb_assigned");
        returnEvent2.setId(2);
        returnEvent2.setRemarks("awb_assigned");
        returnEvent2.setReturnId(2);
        returnEvent2.setReturnRequestId(2);
        returnEvent2.setSource("awb_assigned");

        ArrayList<ReturnEvent> returnEventList = new ArrayList<>();
        returnEventList.add(returnEvent2);
        returnEventList.add(returnEvent);
        when(returnEventRepository.findByReturnId(Mockito.<Integer>any())).thenReturn(returnEventList);

        // Act
        Boolean actualIsDispatchableResult = refundUtilsServiceImpl.isDispatchable(1, "AnyReceivingPoint");

        // Assert
        verify(returnEventRepository).findByReturnId(eq(1));
        assertTrue(actualIsDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>Given {@link ReturnEventRepository}.</li>
     *   <li>When {@code Rule Engine Status For Dispatch}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); given ReturnEventRepository; when 'Rule Engine Status For Dispatch'")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_givenReturnEventRepository_whenRuleEngineStatusForDispatch() {
        // Arrange, Act and Assert
        assertFalse(refundUtilsServiceImpl.isDispatchable(1, "Rule Engine Status For Dispatch"));
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>Then calls {@link ReturnEventRepository#findByReturnId(Integer)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); then calls findByReturnId(Integer)")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_thenCallsFindByReturnId() {
        // Arrange
        when(returnEventRepository.findByReturnId(Mockito.<Integer>any())).thenReturn(new ArrayList<>());

        // Act
        Boolean actualIsDispatchableResult = refundUtilsServiceImpl.isDispatchable(1, "AnyReceivingPoint");

        // Assert
        verify(returnEventRepository).findByReturnId(eq(1));
        assertFalse(actualIsDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>Then calls {@link ReturnEventRepository#findByReturnId(Integer)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); then calls findByReturnId(Integer)")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_thenCallsFindByReturnId2() {
        // Arrange
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent.setEvent("AnyReceivingPoint");
        returnEvent.setId(1);
        returnEvent.setRemarks("AnyReceivingPoint");
        returnEvent.setReturnId(1);
        returnEvent.setReturnRequestId(1);
        returnEvent.setSource("AnyReceivingPoint");

        ArrayList<ReturnEvent> returnEventList = new ArrayList<>();
        returnEventList.add(returnEvent);
        when(returnEventRepository.findByReturnId(Mockito.<Integer>any())).thenReturn(returnEventList);

        // Act
        Boolean actualIsDispatchableResult = refundUtilsServiceImpl.isDispatchable(1, "AnyReceivingPoint");

        // Assert
        verify(returnEventRepository).findByReturnId(eq(1));
        assertFalse(actualIsDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>When {@code CourierPickup}.</li>
     *   <li>Then calls {@link ReturnEventRepository#findByReturnIdAndEventIn(Integer, List)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); when 'CourierPickup'; then calls findByReturnIdAndEventIn(Integer, List)")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_whenCourierPickup_thenCallsFindByReturnIdAndEventIn() {
        // Arrange
        when(returnEventRepository.findByReturnIdAndEventIn(Mockito.<Integer>any(), Mockito.<List<String>>any()))
                .thenReturn(new ArrayList<>());

        // Act
        Boolean actualIsDispatchableResult = refundUtilsServiceImpl.isDispatchable(1, "CourierPickup");

        // Assert
        verify(returnEventRepository).findByReturnIdAndEventIn(eq(1), isA(List.class));
        assertFalse(actualIsDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>When {@code CourierPickup}.</li>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); when 'CourierPickup'; then return 'true'")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_whenCourierPickup_thenReturnTrue() {
        // Arrange
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent.setEvent("AnyReceivingPoint");
        returnEvent.setId(1);
        returnEvent.setRemarks("AnyReceivingPoint");
        returnEvent.setReturnId(1);
        returnEvent.setReturnRequestId(1);
        returnEvent.setSource("AnyReceivingPoint");

        ArrayList<ReturnEvent> returnEventList = new ArrayList<>();
        returnEventList.add(returnEvent);
        when(returnEventRepository.findByReturnIdAndEventIn(Mockito.<Integer>any(), Mockito.<List<String>>any()))
                .thenReturn(returnEventList);

        // Act
        Boolean actualIsDispatchableResult = refundUtilsServiceImpl.isDispatchable(1, "CourierPickup");

        // Assert
        verify(returnEventRepository).findByReturnIdAndEventIn(eq(1), isA(List.class));
        assertTrue(actualIsDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>When {@code POSReceiving}.</li>
     *   <li>Then calls {@link ReturnEventRepository#findByReturnIdAndEventIn(Integer, List)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); when 'POSReceiving'; then calls findByReturnIdAndEventIn(Integer, List)")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_whenPOSReceiving_thenCallsFindByReturnIdAndEventIn() {
        // Arrange
        when(returnEventRepository.findByReturnIdAndEventIn(Mockito.<Integer>any(), Mockito.<List<String>>any()))
                .thenReturn(new ArrayList<>());

        // Act
        Boolean actualIsDispatchableResult = refundUtilsServiceImpl.isDispatchable(1, "POSReceiving");

        // Assert
        verify(returnEventRepository).findByReturnIdAndEventIn(eq(1), isA(List.class));
        assertFalse(actualIsDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}.
     * <ul>
     *   <li>When {@code WHReceiving}.</li>
     *   <li>Then calls {@link ReturnEventRepository#findByReturnIdAndEventIn(Integer, List)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isDispatchable(Integer, String)}
     */
    @Test
    @DisplayName("Test isDispatchable(Integer, String); when 'WHReceiving'; then calls findByReturnIdAndEventIn(Integer, List)")
    @Tag("MaintainedByDiffblue")
    void testIsDispatchable_whenWHReceiving_thenCallsFindByReturnIdAndEventIn() {
        // Arrange
        when(returnEventRepository.findByReturnIdAndEventIn(Mockito.<Integer>any(), Mockito.<List<String>>any()))
                .thenReturn(new ArrayList<>());

        // Act
        Boolean actualIsDispatchableResult = refundUtilsServiceImpl.isDispatchable(1, "WHReceiving");

        // Assert
        verify(returnEventRepository).findByReturnIdAndEventIn(eq(1), isA(List.class));
        assertFalse(actualIsDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isExchangeItemDispatchable(ExchangedItem)}.
     * <ul>
     *   <li>Then return {@code false}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isExchangeItemDispatchable(ExchangedItem)}
     */
    @Test
    @DisplayName("Test isExchangeItemDispatchable(ExchangedItem); then return 'false'")
    @Tag("MaintainedByDiffblue")
    void testIsExchangeItemDispatchable_thenReturnFalse() {
        // Arrange
        when(orderOpsFeignClient.isExchangeItemDispatchable(Mockito.<ExchangedItem>any())).thenReturn(false);

        ExchangedItem exchangedItem = new ExchangedItem();
        exchangedItem.setCallingPoint("Calling Point");
        exchangedItem.setReturnId(1);
        exchangedItem.setUwItemId(1);

        // Act
        Boolean actualIsExchangeItemDispatchableResult = refundUtilsServiceImpl.isExchangeItemDispatchable(exchangedItem);

        // Assert
        verify(orderOpsFeignClient).isExchangeItemDispatchable(isA(ExchangedItem.class));
        assertFalse(actualIsExchangeItemDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#isExchangeItemDispatchable(ExchangedItem)}.
     * <ul>
     *   <li>Then return {@code true}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#isExchangeItemDispatchable(ExchangedItem)}
     */
    @Test
    @DisplayName("Test isExchangeItemDispatchable(ExchangedItem); then return 'true'")
    @Tag("MaintainedByDiffblue")
    void testIsExchangeItemDispatchable_thenReturnTrue() {
        // Arrange
        when(orderOpsFeignClient.isExchangeItemDispatchable(Mockito.<ExchangedItem>any())).thenReturn(true);

        ExchangedItem exchangedItem = new ExchangedItem();
        exchangedItem.setCallingPoint("Calling Point");
        exchangedItem.setReturnId(1);
        exchangedItem.setUwItemId(1);

        // Act
        Boolean actualIsExchangeItemDispatchableResult = refundUtilsServiceImpl.isExchangeItemDispatchable(exchangedItem);

        // Assert
        verify(orderOpsFeignClient).isExchangeItemDispatchable(isA(ExchangedItem.class));
        assertTrue(actualIsExchangeItemDispatchableResult);
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}
     */
    @Test
    @DisplayName("Test getRefundDispatchPoint(RefundDispatchRequest)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchPoint() throws Exception {
        // Arrange
        ReturnRefundRule returnRefundRule = mock(ReturnRefundRule.class);
        when(returnRefundRule.getDecisionValues()).thenReturn(null);
        when(returnRefundRule.getReturnRefundRuleResponse()).thenReturn("Return Refund Rule Response");
        doNothing().when(returnRefundRule).setCreatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setDecisionValues(Mockito.<String>any());
        doNothing().when(returnRefundRule).setId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setRequest(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleOverriddenResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleCalledFrom(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleId(Mockito.<String>any());
        doNothing().when(returnRefundRule).setTriggerPoint(Mockito.<String>any());
        doNothing().when(returnRefundRule).setUpdatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setUwItemId(Mockito.<Integer>any());
        returnRefundRule
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setDecisionValues("42");
        returnRefundRule.setId(1);
        returnRefundRule.setRequest("Request");
        returnRefundRule.setReturnId(1);
        returnRefundRule.setReturnRefundRuleOverriddenResponse("Return Refund Rule Overridden Response");
        returnRefundRule.setReturnRefundRuleResponse("Return Refund Rule Response");
        returnRefundRule.setRuleCalledFrom("<EMAIL>");
        returnRefundRule.setRuleId("42");
        returnRefundRule.setTriggerPoint("Trigger Point");
        returnRefundRule
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setUwItemId(1);
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(Mockito.<Integer>any()))
                .thenReturn(returnRefundRule);
        when(iSystemPreferenceService.getSystemPreferenceValues(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn("42");
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        RefundDispatchRequest request = new RefundDispatchRequest();
        request.setIncrementId(1);
        request.setIsPosReturn(true);
        request.setIsRefundIntentPresent(true);
        request.setNavChannel("Nav Channel");
        request.setRefundIntentMethod("Refund Intent Method");
        request.setRefundTarget("Refund Target");
        request.setRefundTriggerPoint("Refund Trigger Point");
        request.setReturnId(1);
        request.setUwItemId(1);

        // Act
        RefundDispatchResponse actualRefundDispatchPoint = refundUtilsServiceImpl.getRefundDispatchPoint(request);

        // Assert
        verify(returnRefundRule).getDecisionValues();
        verify(returnRefundRule).getReturnRefundRuleResponse();
        verify(returnRefundRule).setCreatedAt(isA(Date.class));
        verify(returnRefundRule).setDecisionValues(eq("42"));
        verify(returnRefundRule).setId(eq(1));
        verify(returnRefundRule).setRequest(eq("Request"));
        verify(returnRefundRule).setReturnId(eq(1));
        verify(returnRefundRule).setReturnRefundRuleOverriddenResponse(eq("Return Refund Rule Overridden Response"));
        verify(returnRefundRule).setReturnRefundRuleResponse(eq("Return Refund Rule Response"));
        verify(returnRefundRule).setRuleCalledFrom(eq("<EMAIL>"));
        verify(returnRefundRule).setRuleId(eq("42"));
        verify(returnRefundRule).setTriggerPoint(eq("Trigger Point"));
        verify(returnRefundRule).setUpdatedAt(isA(Date.class));
        verify(returnRefundRule).setUwItemId(eq(1));
        verify(returnRefundRuleRepository, atLeast(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(eq(1));
        verify(iSystemPreferenceService, atLeast(1)).getSystemPreferenceValues(Mockito.<String>any(),
                Mockito.<String>any());
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertEquals("Value Return of type java.lang.String cannot be converted to JSONObject",
                actualRefundDispatchPoint.getError());
        assertNull(actualRefundDispatchPoint.getRefundDispatchPoint());
        assertNull(actualRefundDispatchPoint.getRemarks());
        assertFalse(actualRefundDispatchPoint.getIsFranchiseRefundDispatchable());
        assertFalse(actualRefundDispatchPoint.getIsRefundDispatchable());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}
     */
    @Test
    @DisplayName("Test getRefundDispatchPoint(RefundDispatchRequest)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchPoint2() throws Exception {
        // Arrange
        ReturnRefundRule returnRefundRule = mock(ReturnRefundRule.class);
        when(returnRefundRule.getDecisionValues()).thenReturn("");
        when(returnRefundRule.getReturnRefundRuleResponse()).thenReturn("Return Refund Rule Response");
        doNothing().when(returnRefundRule).setCreatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setDecisionValues(Mockito.<String>any());
        doNothing().when(returnRefundRule).setId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setRequest(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleOverriddenResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleCalledFrom(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleId(Mockito.<String>any());
        doNothing().when(returnRefundRule).setTriggerPoint(Mockito.<String>any());
        doNothing().when(returnRefundRule).setUpdatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setUwItemId(Mockito.<Integer>any());
        returnRefundRule
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setDecisionValues("42");
        returnRefundRule.setId(1);
        returnRefundRule.setRequest("Request");
        returnRefundRule.setReturnId(1);
        returnRefundRule.setReturnRefundRuleOverriddenResponse("Return Refund Rule Overridden Response");
        returnRefundRule.setReturnRefundRuleResponse("Return Refund Rule Response");
        returnRefundRule.setRuleCalledFrom("<EMAIL>");
        returnRefundRule.setRuleId("42");
        returnRefundRule.setTriggerPoint("Trigger Point");
        returnRefundRule
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setUwItemId(1);
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(Mockito.<Integer>any()))
                .thenReturn(returnRefundRule);
        when(iSystemPreferenceService.getSystemPreferenceValues(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn("42");
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        RefundDispatchRequest request = new RefundDispatchRequest();
        request.setIncrementId(1);
        request.setIsPosReturn(true);
        request.setIsRefundIntentPresent(true);
        request.setNavChannel("Nav Channel");
        request.setRefundIntentMethod("Refund Intent Method");
        request.setRefundTarget("Refund Target");
        request.setRefundTriggerPoint("Refund Trigger Point");
        request.setReturnId(1);
        request.setUwItemId(1);

        // Act
        RefundDispatchResponse actualRefundDispatchPoint = refundUtilsServiceImpl.getRefundDispatchPoint(request);

        // Assert
        verify(returnRefundRule).getDecisionValues();
        verify(returnRefundRule).getReturnRefundRuleResponse();
        verify(returnRefundRule).setCreatedAt(isA(Date.class));
        verify(returnRefundRule).setDecisionValues(eq("42"));
        verify(returnRefundRule).setId(eq(1));
        verify(returnRefundRule).setRequest(eq("Request"));
        verify(returnRefundRule).setReturnId(eq(1));
        verify(returnRefundRule).setReturnRefundRuleOverriddenResponse(eq("Return Refund Rule Overridden Response"));
        verify(returnRefundRule).setReturnRefundRuleResponse(eq("Return Refund Rule Response"));
        verify(returnRefundRule).setRuleCalledFrom(eq("<EMAIL>"));
        verify(returnRefundRule).setRuleId(eq("42"));
        verify(returnRefundRule).setTriggerPoint(eq("Trigger Point"));
        verify(returnRefundRule).setUpdatedAt(isA(Date.class));
        verify(returnRefundRule).setUwItemId(eq(1));
        verify(returnRefundRuleRepository, atLeast(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(eq(1));
        verify(iSystemPreferenceService, atLeast(1)).getSystemPreferenceValues(Mockito.<String>any(),
                Mockito.<String>any());
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertEquals("Value Return of type java.lang.String cannot be converted to JSONObject",
                actualRefundDispatchPoint.getError());
        assertNull(actualRefundDispatchPoint.getRefundDispatchPoint());
        assertNull(actualRefundDispatchPoint.getRemarks());
        assertFalse(actualRefundDispatchPoint.getIsFranchiseRefundDispatchable());
        assertFalse(actualRefundDispatchPoint.getIsRefundDispatchable());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}
     */
    @Test
    @DisplayName("Test getRefundDispatchPoint(RefundDispatchRequest)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchPoint3() throws Exception {
        // Arrange
        ReturnRefundRule returnRefundRule = mock(ReturnRefundRule.class);
        when(returnRefundRule.getDecisionValues()).thenReturn(null);
        when(returnRefundRule.getReturnRefundRuleResponse()).thenReturn(null);
        doNothing().when(returnRefundRule).setCreatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setDecisionValues(Mockito.<String>any());
        doNothing().when(returnRefundRule).setId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setRequest(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleOverriddenResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleCalledFrom(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleId(Mockito.<String>any());
        doNothing().when(returnRefundRule).setTriggerPoint(Mockito.<String>any());
        doNothing().when(returnRefundRule).setUpdatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setUwItemId(Mockito.<Integer>any());
        returnRefundRule
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setDecisionValues("42");
        returnRefundRule.setId(1);
        returnRefundRule.setRequest("Request");
        returnRefundRule.setReturnId(1);
        returnRefundRule.setReturnRefundRuleOverriddenResponse("Return Refund Rule Overridden Response");
        returnRefundRule.setReturnRefundRuleResponse("Return Refund Rule Response");
        returnRefundRule.setRuleCalledFrom("<EMAIL>");
        returnRefundRule.setRuleId("42");
        returnRefundRule.setTriggerPoint("Trigger Point");
        returnRefundRule
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setUwItemId(1);
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(Mockito.<Integer>any()))
                .thenReturn(returnRefundRule);
        when(iSystemPreferenceService.getSystemPreferenceValues(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn("42");
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        RefundDispatchRequest request = new RefundDispatchRequest();
        request.setIncrementId(1);
        request.setIsPosReturn(true);
        request.setIsRefundIntentPresent(true);
        request.setNavChannel("Nav Channel");
        request.setRefundIntentMethod("Refund Intent Method");
        request.setRefundTarget("Refund Target");
        request.setRefundTriggerPoint("Refund Trigger Point");
        request.setReturnId(1);
        request.setUwItemId(1);

        // Act
        RefundDispatchResponse actualRefundDispatchPoint = refundUtilsServiceImpl.getRefundDispatchPoint(request);

        // Assert
        verify(returnRefundRule).getDecisionValues();
        verify(returnRefundRule).getReturnRefundRuleResponse();
        verify(returnRefundRule).setCreatedAt(isA(Date.class));
        verify(returnRefundRule).setDecisionValues(eq("42"));
        verify(returnRefundRule).setId(eq(1));
        verify(returnRefundRule).setRequest(eq("Request"));
        verify(returnRefundRule).setReturnId(eq(1));
        verify(returnRefundRule).setReturnRefundRuleOverriddenResponse(eq("Return Refund Rule Overridden Response"));
        verify(returnRefundRule).setReturnRefundRuleResponse(eq("Return Refund Rule Response"));
        verify(returnRefundRule).setRuleCalledFrom(eq("<EMAIL>"));
        verify(returnRefundRule).setRuleId(eq("42"));
        verify(returnRefundRule).setTriggerPoint(eq("Trigger Point"));
        verify(returnRefundRule).setUpdatedAt(isA(Date.class));
        verify(returnRefundRule).setUwItemId(eq(1));
        verify(returnRefundRuleRepository, atLeast(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(eq(1));
        verify(iSystemPreferenceService, atLeast(1)).getSystemPreferenceValues(Mockito.<String>any(),
                Mockito.<String>any());
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertEquals("Cannot invoke \"String.length()\" because \"this.in\" is null", actualRefundDispatchPoint.getError());
        assertNull(actualRefundDispatchPoint.getRefundDispatchPoint());
        assertNull(actualRefundDispatchPoint.getRemarks());
        assertFalse(actualRefundDispatchPoint.getIsFranchiseRefundDispatchable());
        assertFalse(actualRefundDispatchPoint.getIsRefundDispatchable());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}.
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}
     */
    @Test
    @DisplayName("Test getRefundDispatchPoint(RefundDispatchRequest)")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchPoint4() throws Exception {
        // Arrange
        ReturnRefundRule returnRefundRule = mock(ReturnRefundRule.class);
        when(returnRefundRule.getDecisionValues()).thenReturn(null);
        when(returnRefundRule.getReturnRefundRuleResponse()).thenReturn("Return Refund Rule Response");
        doNothing().when(returnRefundRule).setCreatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setDecisionValues(Mockito.<String>any());
        doNothing().when(returnRefundRule).setId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setRequest(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleOverriddenResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleCalledFrom(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleId(Mockito.<String>any());
        doNothing().when(returnRefundRule).setTriggerPoint(Mockito.<String>any());
        doNothing().when(returnRefundRule).setUpdatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setUwItemId(Mockito.<Integer>any());
        returnRefundRule
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setDecisionValues("42");
        returnRefundRule.setId(1);
        returnRefundRule.setRequest("Request");
        returnRefundRule.setReturnId(1);
        returnRefundRule.setReturnRefundRuleOverriddenResponse("Return Refund Rule Overridden Response");
        returnRefundRule.setReturnRefundRuleResponse("Return Refund Rule Response");
        returnRefundRule.setRuleCalledFrom("<EMAIL>");
        returnRefundRule.setRuleId("42");
        returnRefundRule.setTriggerPoint("Trigger Point");
        returnRefundRule
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setUwItemId(1);
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(Mockito.<Integer>any()))
                .thenReturn(returnRefundRule);
        when(iSystemPreferenceService.getSystemPreferenceValues(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(null);
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        RefundDispatchRequest request = new RefundDispatchRequest();
        request.setIncrementId(1);
        request.setIsPosReturn(true);
        request.setIsRefundIntentPresent(true);
        request.setNavChannel("Nav Channel");
        request.setRefundIntentMethod("Refund Intent Method");
        request.setRefundTarget("Refund Target");
        request.setRefundTriggerPoint("Refund Trigger Point");
        request.setReturnId(1);
        request.setUwItemId(1);

        // Act
        RefundDispatchResponse actualRefundDispatchPoint = refundUtilsServiceImpl.getRefundDispatchPoint(request);

        // Assert
        verify(returnRefundRule).getDecisionValues();
        verify(returnRefundRule).getReturnRefundRuleResponse();
        verify(returnRefundRule).setCreatedAt(isA(Date.class));
        verify(returnRefundRule).setDecisionValues(eq("42"));
        verify(returnRefundRule).setId(eq(1));
        verify(returnRefundRule).setRequest(eq("Request"));
        verify(returnRefundRule).setReturnId(eq(1));
        verify(returnRefundRule).setReturnRefundRuleOverriddenResponse(eq("Return Refund Rule Overridden Response"));
        verify(returnRefundRule).setReturnRefundRuleResponse(eq("Return Refund Rule Response"));
        verify(returnRefundRule).setRuleCalledFrom(eq("<EMAIL>"));
        verify(returnRefundRule).setRuleId(eq("42"));
        verify(returnRefundRule).setTriggerPoint(eq("Trigger Point"));
        verify(returnRefundRule).setUpdatedAt(isA(Date.class));
        verify(returnRefundRule).setUwItemId(eq(1));
        verify(returnRefundRuleRepository, atLeast(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(eq(1));
        verify(iSystemPreferenceService, atLeast(1)).getSystemPreferenceValues(Mockito.<String>any(),
                Mockito.<String>any());
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertEquals("Value Return of type java.lang.String cannot be converted to JSONObject",
                actualRefundDispatchPoint.getError());
        assertNull(actualRefundDispatchPoint.getRefundDispatchPoint());
        assertNull(actualRefundDispatchPoint.getRemarks());
        assertFalse(actualRefundDispatchPoint.getIsFranchiseRefundDispatchable());
        assertFalse(actualRefundDispatchPoint.getIsRefundDispatchable());
    }

    /**
     * Test {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}.
     * <ul>
     *   <li>Then return Error is {@code End of input at character 0 of}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundUtilsServiceImpl#getRefundDispatchPoint(RefundDispatchRequest)}
     */
    @Test
    @DisplayName("Test getRefundDispatchPoint(RefundDispatchRequest); then return Error is 'End of input at character 0 of'")
    @Tag("MaintainedByDiffblue")
    void testGetRefundDispatchPoint_thenReturnErrorIsEndOfInputAtCharacter0Of() throws Exception {
        // Arrange
        ReturnRefundRule returnRefundRule = mock(ReturnRefundRule.class);
        when(returnRefundRule.getDecisionValues()).thenReturn(null);
        when(returnRefundRule.getReturnRefundRuleResponse()).thenReturn("");
        doNothing().when(returnRefundRule).setCreatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setDecisionValues(Mockito.<String>any());
        doNothing().when(returnRefundRule).setId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setRequest(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnId(Mockito.<Integer>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleOverriddenResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setReturnRefundRuleResponse(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleCalledFrom(Mockito.<String>any());
        doNothing().when(returnRefundRule).setRuleId(Mockito.<String>any());
        doNothing().when(returnRefundRule).setTriggerPoint(Mockito.<String>any());
        doNothing().when(returnRefundRule).setUpdatedAt(Mockito.<Date>any());
        doNothing().when(returnRefundRule).setUwItemId(Mockito.<Integer>any());
        returnRefundRule
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setDecisionValues("42");
        returnRefundRule.setId(1);
        returnRefundRule.setRequest("Request");
        returnRefundRule.setReturnId(1);
        returnRefundRule.setReturnRefundRuleOverriddenResponse("Return Refund Rule Overridden Response");
        returnRefundRule.setReturnRefundRuleResponse("Return Refund Rule Response");
        returnRefundRule.setRuleCalledFrom("<EMAIL>");
        returnRefundRule.setRuleId("42");
        returnRefundRule.setTriggerPoint("Trigger Point");
        returnRefundRule
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRefundRule.setUwItemId(1);
        when(returnRefundRuleRepository.findTop1ByUwItemIdOrderByCreatedAtDesc(Mockito.<Integer>any()))
                .thenReturn(returnRefundRule);
        when(iSystemPreferenceService.getSystemPreferenceValues(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn("42");
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        RefundDispatchRequest request = new RefundDispatchRequest();
        request.setIncrementId(1);
        request.setIsPosReturn(true);
        request.setIsRefundIntentPresent(true);
        request.setNavChannel("Nav Channel");
        request.setRefundIntentMethod("Refund Intent Method");
        request.setRefundTarget("Refund Target");
        request.setRefundTriggerPoint("Refund Trigger Point");
        request.setReturnId(1);
        request.setUwItemId(1);

        // Act
        RefundDispatchResponse actualRefundDispatchPoint = refundUtilsServiceImpl.getRefundDispatchPoint(request);

        // Assert
        verify(returnRefundRule).getDecisionValues();
        verify(returnRefundRule).getReturnRefundRuleResponse();
        verify(returnRefundRule).setCreatedAt(isA(Date.class));
        verify(returnRefundRule).setDecisionValues(eq("42"));
        verify(returnRefundRule).setId(eq(1));
        verify(returnRefundRule).setRequest(eq("Request"));
        verify(returnRefundRule).setReturnId(eq(1));
        verify(returnRefundRule).setReturnRefundRuleOverriddenResponse(eq("Return Refund Rule Overridden Response"));
        verify(returnRefundRule).setReturnRefundRuleResponse(eq("Return Refund Rule Response"));
        verify(returnRefundRule).setRuleCalledFrom(eq("<EMAIL>"));
        verify(returnRefundRule).setRuleId(eq("42"));
        verify(returnRefundRule).setTriggerPoint(eq("Trigger Point"));
        verify(returnRefundRule).setUpdatedAt(isA(Date.class));
        verify(returnRefundRule).setUwItemId(eq(1));
        verify(returnRefundRuleRepository, atLeast(1)).findTop1ByUwItemIdOrderByCreatedAtDesc(eq(1));
        verify(iSystemPreferenceService, atLeast(1)).getSystemPreferenceValues(Mockito.<String>any(),
                Mockito.<String>any());
        verify(iSystemPreferenceService).getValuesAsList(eq("store_refund"), eq("nav_channels"));
        assertEquals("End of input at character 0 of ", actualRefundDispatchPoint.getError());
        assertNull(actualRefundDispatchPoint.getRefundDispatchPoint());
        assertNull(actualRefundDispatchPoint.getRemarks());
        assertFalse(actualRefundDispatchPoint.getIsFranchiseRefundDispatchable());
        assertFalse(actualRefundDispatchPoint.getIsRefundDispatchable());
    }
}
