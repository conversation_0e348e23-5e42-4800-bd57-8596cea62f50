package com.lenskart.returnservice.service.impl;

import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.*;

import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
public class OrderStatusUpdateServiceImplTest {

    @InjectMocks
    private OrderStatusUpdateServiceImpl orderStatusUpdateService;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private IKafkaService kafkaService;

    @Test
    public void updateOrderStatus_HappyPath() {
        // Arrange
        List<Map<String, Object>> orderStatusUpdateDTOS = new ArrayList<>();
        Map<String, Object> orderStatusUpdateDTO = new HashMap<>();
        orderStatusUpdateDTO.put("incrementId", 1);
        orderStatusUpdateDTO.put("unicomOrderCode", "UNICOM123");
        orderStatusUpdateDTOS.add(orderStatusUpdateDTO);

        ResponseEntity<List<Map<String, String>>> response = new ResponseEntity<>(Arrays.asList(), HttpStatus.OK);
        when(orderOpsFeignClient.getOrderStatusPriorityDetails()).thenReturn(response);

        // Act
        orderStatusUpdateService.updateOrderStatus(orderStatusUpdateDTOS);

        // Assert
        verify(orderOpsFeignClient, times(1)).getOrderStatusPriorityDetails();
    }

    @Test
    public void updateOrderStatus_ErrorScenario() {
        // Arrange
        List<Map<String, Object>> orderStatusUpdateDTOS = new ArrayList<>();
        Map<String, Object> orderStatusUpdateDTO = new HashMap<>();
        orderStatusUpdateDTO.put("incrementId", 1);
        orderStatusUpdateDTO.put("unicomOrderCode", "UNICOM123");
        orderStatusUpdateDTOS.add(orderStatusUpdateDTO);

        ResponseEntity<List<Map<String, String>>> response = new ResponseEntity<>(Arrays.asList(), HttpStatus.INTERNAL_SERVER_ERROR);
        when(orderOpsFeignClient.getOrderStatusPriorityDetails()).thenReturn(response);

        // Act
        orderStatusUpdateService.updateOrderStatus(orderStatusUpdateDTOS);

        // Assert
        verify(orderOpsFeignClient, times(1)).getOrderStatusPriorityDetails();
    }

//    @Test
//    public void saveUpdateOrderStatusEvent_HappyPath() {
//        // Arrange
//        Map<String,Object> orderStatusUpdateDTO = new HashMap<>();
//        orderStatusUpdateDTO.put("incrementId", 1);
//        orderStatusUpdateDTO.put("unicomOrderCode", "UNICOM123");
//
//        // Act
//        orderStatusUpdateService.saveUpdateOrderStatusEvent(orderStatusUpdateDTO, "STATUS_UPDATED");
//
//        // Assert
//        verify(kafkaService, times(1)).pushToKafka("Return_event_queue", "1", any());
//    }
}