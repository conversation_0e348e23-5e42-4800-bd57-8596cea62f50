package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.ordermetadata.dto.request.ExchangeAddressDTO;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.returncommon.model.dto.ExchangeItem;
import com.lenskart.returncommon.model.dto.ExchangeOrderCreationRequest;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ExchangeOrderServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ExchangeOrderServiceImplTest {
    @Autowired
    private ExchangeOrderServiceImpl exchangeOrderServiceImpl;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    /**
     * Method under test:
     * {@link ExchangeOrderServiceImpl#createExchangeOrder(ExchangeOrderCreationRequest)}
     */
    @Test
    void testCreateExchangeOrder() {
        // Arrange
        ExchangeAddressDTO shippingAddress = new ExchangeAddressDTO();
        shippingAddress.setAddressType("42 Main St");
        shippingAddress.setAddressline1("42 Main St");
        shippingAddress.setCity("Oxford");
        shippingAddress.setCountry("GB");
        shippingAddress.setEmail("<EMAIL>");
        shippingAddress.setFirstName("Jane");
        shippingAddress.setGender("Gender");
        shippingAddress.setLandmark("Landmark");
        shippingAddress.setLastName("Doe");
        shippingAddress.setLocality("Locality");
        shippingAddress.setPhone("6625550144");
        shippingAddress.setPhoneCode("6625550144");
        shippingAddress.setPostcode("OX1 1PT");
        shippingAddress.setState("MD");

        ExchangeOrderCreationRequest exchangeOrderCreationRequest = new ExchangeOrderCreationRequest();
        exchangeOrderCreationRequest.setItems(new ArrayList<>());
        exchangeOrderCreationRequest.setPaymentMethod("Payment Method");
        exchangeOrderCreationRequest.setReturnId(1);
        exchangeOrderCreationRequest.setShippingAddress(shippingAddress);

        // Act and Assert
        assertFalse(exchangeOrderServiceImpl.createExchangeOrder(exchangeOrderCreationRequest));
    }

    /**
     * Method under test:
     * {@link ExchangeOrderServiceImpl#createExchangeOrder(ExchangeOrderCreationRequest)}
     */
    @Test
    void testCreateExchangeOrder2() {
        // Arrange
        ExchangeAddressDTO shippingAddress = new ExchangeAddressDTO();
        shippingAddress.setAddressType("42 Main St");
        shippingAddress.setAddressline1("42 Main St");
        shippingAddress.setCity("Oxford");
        shippingAddress.setCountry("GB");
        shippingAddress.setEmail("<EMAIL>");
        shippingAddress.setFirstName("Jane");
        shippingAddress.setGender("Gender");
        shippingAddress.setLandmark("Landmark");
        shippingAddress.setLastName("Doe");
        shippingAddress.setLocality("Locality");
        shippingAddress.setPhone("6625550144");
        shippingAddress.setPhoneCode("6625550144");
        shippingAddress.setPostcode("OX1 1PT");
        shippingAddress.setState("MD");
        ExchangeOrderCreationRequest exchangeOrderCreationRequest = mock(ExchangeOrderCreationRequest.class);
        doNothing().when(exchangeOrderCreationRequest).setItems(Mockito.<List<ExchangeItem>>any());
        doNothing().when(exchangeOrderCreationRequest).setPaymentMethod(Mockito.<String>any());
        doNothing().when(exchangeOrderCreationRequest).setReturnId(Mockito.<Integer>any());
        doNothing().when(exchangeOrderCreationRequest).setShippingAddress(Mockito.<ExchangeAddressDTO>any());
        exchangeOrderCreationRequest.setItems(new ArrayList<>());
        exchangeOrderCreationRequest.setPaymentMethod("Payment Method");
        exchangeOrderCreationRequest.setReturnId(1);
        exchangeOrderCreationRequest.setShippingAddress(shippingAddress);

        // Act
        Boolean actualCreateExchangeOrderResult = exchangeOrderServiceImpl
                .createExchangeOrder(exchangeOrderCreationRequest);

        // Assert
        verify(exchangeOrderCreationRequest).setItems(isA(List.class));
        verify(exchangeOrderCreationRequest).setPaymentMethod(eq("Payment Method"));
        verify(exchangeOrderCreationRequest).setReturnId(eq(1));
        verify(exchangeOrderCreationRequest).setShippingAddress(isA(ExchangeAddressDTO.class));
        assertFalse(actualCreateExchangeOrderResult);
    }

    /**
     * Method under test:
     * {@link ExchangeOrderServiceImpl#unholdExchangeOrder(Integer)}
     */
    @Test
    void testUnholdExchangeOrder() {
        // Arrange
        when(orderOpsFeignClient.statusUpdate(Mockito.<OrderStatusUpdateDetails>any())).thenReturn(null);

        // Act
        boolean actualUnholdExchangeOrderResult = exchangeOrderServiceImpl.unholdExchangeOrder(1);

        // Assert
        verify(orderOpsFeignClient).statusUpdate(isA(OrderStatusUpdateDetails.class));
        assertFalse(actualUnholdExchangeOrderResult);
    }

    /**
     * Method under test:
     * {@link ExchangeOrderServiceImpl#unholdExchangeOrder(Integer)}
     */
    @Test
    void testUnholdExchangeOrder2() {
        // Arrange
        ResponseEntity<Boolean> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getStatusCode()).thenReturn(null);
        when(orderOpsFeignClient.statusUpdate(Mockito.<OrderStatusUpdateDetails>any())).thenReturn(responseEntity);

        // Act
        boolean actualUnholdExchangeOrderResult = exchangeOrderServiceImpl.unholdExchangeOrder(1);

        // Assert
        verify(orderOpsFeignClient).statusUpdate(isA(OrderStatusUpdateDetails.class));
        verify(responseEntity).getStatusCode();
        assertFalse(actualUnholdExchangeOrderResult);
    }
}
