package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.return_refund_rules.model.ReturnItemRequest;
import com.lenskart.returncommon.exception.ReturnRequestFailException;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@ContextConfiguration(classes = {ReturnUnicomServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ReturnUnicomServiceImplTest {
    @Autowired
    private ReturnUnicomServiceImpl returnUnicomServiceImpl;

    /**
     * Method under test: {@link ReturnUnicomServiceImpl#init()}
     */
    @Test
    void testInit() {
        returnUnicomServiceImpl.init();
    }

    /**
     * Method under test:
     * {@link ReturnUnicomServiceImpl#createReversePickup(List, Object, String, String, Boolean)}
     */
    @Test
    void testCreateReversePickup() throws ReturnRequestFailException {
        // Arrange
        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("name");
        returnItemRequest.setReasonDetail("Just cause");

        ArrayList<ReturnItemRequestDTO> returnItemRequest2 = new ArrayList<>();
        returnItemRequest2.add(returnItemRequest);
        assertThrows(ReturnRequestFailException.class, () -> returnUnicomServiceImpl.createReversePickup(returnItemRequest2,
                "Shipping Address", "Sale Order Code", "Facility", true));
    }

    /**
     * Method under test:
     * {@link ReturnUnicomServiceImpl#createReversePickup(List, Object, String, String, Boolean)}
     */
    @Test
    void testCreateReversePickup2() throws ReturnRequestFailException {
        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("name");
        returnItemRequest.setReasonDetail("Just cause");

        ReturnItemRequestDTO returnItemRequest2 = new ReturnItemRequestDTO();
        returnItemRequest2.setItemId(2);
        returnItemRequest2.setQcFailReason("U://U@[9U]:{UU?U#U");
        returnItemRequest2.setQcStatus("{UUU}");
        returnItemRequest2.setReasonDetail("U://U@[9U]:{UU?U#U");

        ArrayList<ReturnItemRequestDTO> returnItemRequest3 = new ArrayList<>();
        returnItemRequest3.add(returnItemRequest2);
        returnItemRequest3.add(returnItemRequest);
        assertThrows(ReturnRequestFailException.class, () -> returnUnicomServiceImpl.createReversePickup(returnItemRequest3,
                "Shipping Address", "Sale Order Code", "Facility", true));
    }
}
