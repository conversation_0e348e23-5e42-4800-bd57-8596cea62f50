package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returncommon.model.dto.NeedApprovalRequest;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnDetailUpdateRequest;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.DelightAction;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.DelightActionRepository;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.service.IReturnApprovalStatusUpdateService;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {NeedApprovalProcessorServiceImpl.class})
@ExtendWith(SpringExtension.class)
class NeedApprovalProcessorServiceImplTest {

    @Autowired
    private NeedApprovalProcessorServiceImpl needApprovalProcessorService;

    @MockBean
    private IReturnOrderActionService returnOrderActionService;

    @MockBean
    private ReturnRequestRepository returnRequestRepository;

    @MockBean
    private IReturnApprovalStatusUpdateService returnApprovalStatusUpdate;

    @MockBean
    private ReturnUpdateServiceImpl returnUpdateService;

    @MockBean
    private DelightActionRepository delightActionRepository;

    @MockBean
    private IReturnEventService returnEventService;

    @MockBean
    private ReturnEventRepository returnEventRepository;

    private NeedApprovalRequest needApprovalRequest;
    private ReturnEvent returnEvent;

    @BeforeEach
    void setUp() {
        needApprovalRequest = new NeedApprovalRequest();
        needApprovalRequest.setReturnId(1);
        needApprovalRequest.setApprovalStatusRequest(new ApprovalStatusRequest());
        needApprovalRequest.setDelightAction("APPROVE");
        needApprovalRequest.setDelightMethod("METHOD");
        needApprovalRequest.setComments("COMMENTS");
        needApprovalRequest.setUsername("USER");
        needApprovalRequest.setStatus(ReturnStatus.RETURN_NEED_APPROVAL.getStatus());

        returnEvent = new ReturnEvent();
        returnEvent.setReturnId(1);
    }

    @Test
    void testIsSaveDelightActionProcessed_Success() throws Exception {
        ReturnDetailsUpdateResponse response = new ReturnDetailsUpdateResponse();
        response.setStatus(Constant.SUCCESS);

        when(returnUpdateService.updateReturnStatus(any(ReturnDetailUpdateRequestDto.class))).thenReturn(response);
        when(returnApprovalStatusUpdate.updateApprovalStatus(any())).thenReturn(true);
        when(returnEventRepository.findTopByReturnIdOrderByIdDesc(1)).thenReturn(returnEvent);
        boolean result = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);

        assertTrue(result);

        verify(delightActionRepository, times(1)).save(any(DelightAction.class));
    }

    @Test
    void testIsSaveDelightActionProcessed_Failure() throws Exception {
        ReturnDetailsUpdateResponse response = new ReturnDetailsUpdateResponse();
        response.setStatus("Failure");
        when(returnUpdateService.updateReturnStatus(any(ReturnDetailUpdateRequestDto.class))).thenReturn(response);
        when(returnApprovalStatusUpdate.updateApprovalStatus(any())).thenReturn(false);
        when(returnEventRepository.findTopByReturnIdOrderByIdDesc(1)).thenReturn(returnEvent);
        Exception exception = assertThrows(Exception.class, () -> {
            needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
        });

        assertEquals(Constant.DELIGHT_ACTION_FAILED, exception.getMessage());
        verify(returnEventService, times(1)).createReturnEvent(any(), anyInt(), eq(Constant.DELIGHT_ACTION_FAILED), anyString());
    }



}