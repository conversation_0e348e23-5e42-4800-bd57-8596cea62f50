package com.lenskart.returnservice.service.impl;

import com.lenskart.refund.client.model.request.GetRefundAmountRequest;
import com.lenskart.refund.client.model.response.GetMethodWiseRefundDetailsResponse;
import com.lenskart.returncommon.model.dto.GetDelayedPickupOrdersRequest;
import com.lenskart.returncommon.model.dto.GetDelayedPickupOrdersResponse;
import com.lenskart.returncommon.model.dto.GetReturnRefundMappingResponse;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.response.ReturnReverseInfoResponse;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.service.IRefundUtilsService;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReturnOrderServiceImplTest {

    @InjectMocks
    private ReturnOrderServiceImpl returnOrderService;

    @Mock
    private ReturnOrderActionServiceImpl returnOrderActionService;
    @Mock
    private IRefundUtilsService refundUtilsService;
    @Mock
    private ReturnEventRepository returnEventRepository;

    private ReturnDetail returnOrder;

    private ReturnOrderDTO returnOrderDTO;

    @BeforeEach
    void setUp() {
        returnOrder = new ReturnDetail();
        returnOrder.setId(1);
        returnOrder.setIncrementId(123);

        returnOrderDTO = new ReturnOrderDTO();
        returnOrderDTO.setId(1);
        returnOrderDTO.setIncrementId(123);

    }

    @Test
    void testGetReturnDetailsUsingOrderId_ValidOrderId() {
        when(returnOrderActionService.findAllByIncrementId(123)).thenReturn(List.of(returnOrder));
        List<ReturnOrderDTO> returnOrders = returnOrderService.getReturnDetails(123);

        assertNotNull(returnOrders);
        assertEquals(1, returnOrders.size());
        assertEquals(returnOrderDTO, returnOrders.get(0));
    }

    @Test
    void testGetReturnDetailsUsingOrderId_InvalidOrderId() {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            returnOrderService.getReturnDetails(-1);
        });

        assertEquals("Invalid input: -1", exception.getMessage());
    }

    @Test
    void testGetReturnDetailsUsingOrderId_NoReturnOrdersFound() {
        when(returnOrderActionService.findAllByIncrementId(123)).thenReturn(Collections.emptyList());

        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () -> {
            returnOrderService.getReturnDetails(123);
        });

        assertEquals("No return orders found: 123", exception.getMessage());
    }

    @Test
    void testGetReturnDetailsUsingOrderId_ExceptionHandling() {
        when(returnOrderActionService.findAllByIncrementId(123)).thenThrow(new RuntimeException("Database error"));
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            returnOrderService.getReturnDetails(123);
        });

        assertEquals("Error fetching return orders: Database error", exception.getMessage());
    }

    @Test
    public void testGetDelayedPickupOrdersSuccess() throws Exception {
        GetDelayedPickupOrdersRequest request = new GetDelayedPickupOrdersRequest();
        request.setIncrementIds(List.of(1, 2));
        ReturnDetail returnOrder1 = new ReturnDetail();
        returnOrder1.setIncrementId(1);
        ReturnDetail returnOrder2 = new ReturnDetail();
        returnOrder2.setIncrementId(2);
        List<ReturnDetail> returnOrders = Arrays.asList(returnOrder1, returnOrder2);
        when(returnOrderActionService.getReturnOrders(anyList(), true)).thenReturn(returnOrders);
        when(returnOrderActionService.getReturnOrderStatus(any(ReturnDetail.class)))
                .thenReturn("awb_assigned", "new_reverse_pickup");

        GetDelayedPickupOrdersResponse response = returnOrderService.getDelayedPickupOrders(request);

        assertNotNull(response);
        assertNotNull(response.getResult());
        assertEquals(2, response.getResult().size());

        Map<String, Map<String, Object>> result = response.getResult();
        assertTrue(result.containsKey("1"));
        assertTrue(result.containsKey("2"));

        assertEquals("awb_assigned", result.get("1").get("status"));
        assertEquals("new_reverse_pickup", result.get("2").get("status"));

    }

    @Test
    public void testGetDelayedPickupOrdersEmptyRequest() throws Exception {
        GetDelayedPickupOrdersRequest request = new GetDelayedPickupOrdersRequest();
        request.setIncrementIds(Collections.emptyList());

        GetDelayedPickupOrdersResponse response = returnOrderService.getDelayedPickupOrders(request);

        assertNotNull(response);
        assertNotNull(response.getResult());
        assertTrue(response.getResult().isEmpty());
    }
    @Test
    public void testGetReturnRefundMappingV2() {
        Integer incrementId = 1;
        List<ReturnDetail> returnOrders = new ArrayList<>();
        returnOrders.add(new ReturnDetail());
        when(returnOrderActionService.findAllByIncrementId(incrementId)).thenReturn(returnOrders);

        GetMethodWiseRefundDetailsResponse refundDetailsResponse = new GetMethodWiseRefundDetailsResponse();
        refundDetailsResponse.setMethodSpecificRefundDetailsDTOMap(new HashMap<>());
        when(refundUtilsService.getMethodWiseRefundDetails(any(GetRefundAmountRequest.class)))
                .thenReturn(refundDetailsResponse);

        GetReturnRefundMappingResponse response = returnOrderService.getReturnRefundMappingV2(incrementId);

        assertNotNull(response);
        assertEquals(1, response.getReturnRefundMapping().size());
        assertEquals(0, response.getRefunds().size());
        assertEquals(0, response.getAdditionalRefunds().size());
        assertEquals(0.0, response.getTotal_refund());
    }

    @Test
    void testGetReturnOrderDTOs() {
        Integer incrementId = 1;
        List<ReturnDetail> returnOrders = new ArrayList<>();
        returnOrders.add(new ReturnDetail());
        when(returnOrderActionService.findAllByIncrementId(incrementId)).thenReturn(returnOrders);

        List<ReturnOrderDTO> returnOrderDTOs = returnOrderService.getReturnOrderDTOs(incrementId);

        assertNotNull(returnOrderDTOs);
        assertEquals(1, returnOrderDTOs.size());
    }

    @Test
    void getReturnRefundInfoTest(){
        Integer returnId = 123;
        List<ReturnEvent> eventList = new ArrayList<>();
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setId(1);
        returnEvent.setReturnRequestId(2);
        returnEvent.setReturnId(123);
        returnEvent.setEvent(ReturnStatus.RETURN_RECEIVED.getStatus());
        returnEvent.setSource("POS");
        returnEvent.setRemarks("return received");
        returnEvent.setCreatedAt(new Date());
        eventList.add(returnEvent);
        when(returnEventRepository.findByReturnId(returnId)).thenReturn(eventList);
        ReturnReverseInfoResponse response = returnOrderService.getReturnRefundInfo(returnId);
        assertEquals(response.getReturnReverseCombinedInfoDtos().size(),1);
    }
}