package com.lenskart.returnservice.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.returncommon.model.dto.SyncUnicomStatusDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;

import static com.lenskart.returncommon.utils.Constant.EXCHANGE_ONHOLD_TO_PROCESSING;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.EXCHANGE_ORDER_UNHOLD_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_STATUS_HISTORY_QUEUE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
class SyncUnicomStatusServiceImplTest {
    @InjectMocks
    private SyncUnicomStatusServiceImpl syncUnicomStatusService;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private IKafkaService kafkaService;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        syncUnicomStatusService.init();
    }

    @Test
    void syncUnicomStatus_NullSyncUnicomStatusDTO() {
        // When
        syncUnicomStatusService.syncUnicomStatus(null);

        // Then
        verify(orderOpsFeignClient, never()).syncUnicomStatus(any());
        verify(kafkaService, never()).pushToKafka(anyString(), anyString(), anyString());
    }

    @Test
    void syncUnicomStatus_SuccessfulSync() throws Exception {
        // Given
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        SyncUnicomStatusDTO syncUnicomStatusDTO = new SyncUnicomStatusDTO();
        syncUnicomStatusDTO.setUwOrderDTOs(Collections.singletonList(uwOrderDTO));

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        syncUnicomStatusDTO.setOrdersHeaderDTO(ordersHeaderDTO);

        ExchangeItemDTO exchangeItemDTO = new ExchangeItemDTO();
        exchangeItemDTO.setReturnId(456);
        syncUnicomStatusDTO.setExchangeItemDTO(exchangeItemDTO);

        when(orderOpsFeignClient.syncUnicomStatus(any())).thenReturn(new ResponseEntity<>(true, HttpStatus.OK));

        // When
        syncUnicomStatusService.syncUnicomStatus(syncUnicomStatusDTO);

        // Then
        verify(orderOpsFeignClient, times(1)).syncUnicomStatus(any());
//        verify(kafkaService, times(1)).pushToKafka(eq(EXCHANGE_ORDER_UNHOLD_QUEUE), anyString(), anyString());
//        verify(kafkaService, times(1)).pushToKafka(eq(ORDER_STATUS_HISTORY_QUEUE), anyString(), anyString());
    }

    @Test
    void syncUnicomStatus_FailedSync() throws Exception {
        // Given
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        SyncUnicomStatusDTO syncUnicomStatusDTO = new SyncUnicomStatusDTO();
        syncUnicomStatusDTO.setUwOrderDTOs(Collections.singletonList(uwOrderDTO));

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        syncUnicomStatusDTO.setOrdersHeaderDTO(ordersHeaderDTO);

        when(orderOpsFeignClient.syncUnicomStatus(any())).thenReturn(new ResponseEntity<>(false, HttpStatus.OK));

        // When
        syncUnicomStatusService.syncUnicomStatus(syncUnicomStatusDTO);

        // Then
        verify(orderOpsFeignClient, times(1)).syncUnicomStatus(any());
        verify(kafkaService, never()).pushToKafka(anyString(), anyString(), anyString());
    }

    @Test
    void saveOrderComments_Success() throws Exception {
        // Given
        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);

        OrderCommentReq orderCommentReq = new OrderCommentReq();
        orderCommentReq.setComment(EXCHANGE_ONHOLD_TO_PROCESSING);
        orderCommentReq.setIncrementId(123);
        orderCommentReq.setCommentType("cust");

        String orderCommentReqJson = "{\"incrementId\":123,\"comment\":\"Order is moved from exchange_on_hold to processing\",\"source\":null,\"commentType\":\"cust\"}";
        when(objectMapper.writeValueAsString(any(OrderCommentReq.class))).thenReturn(orderCommentReqJson);

        // When
        //syncUnicomStatusService.saveOrderComments(ordersHeaderDTO);

        // Then
//        verify(kafkaService, times(1)).pushToKafka(eq(ORDER_STATUS_HISTORY_QUEUE), eq("123"), eq(orderCommentReqJson));
    }

    @Test
    void unholdExchangeOrder_Success() throws Exception {
        // Given
        int incrementId = 123;
        Integer returnId = 456;

        ExchangeOrderUnholdRequest exchangeOrderUnholdRequest = new ExchangeOrderUnholdRequest(incrementId, returnId);
        String exchangeOrderUnholdRequestJson = "{\"incrementId\":123,\"returnId\":456}";
        when(objectMapper.writeValueAsString(any(ExchangeOrderUnholdRequest.class))).thenReturn(exchangeOrderUnholdRequestJson);

        // When
        //syncUnicomStatusService.unholdExchangeOrder(incrementId, returnId);

        // Then
//        verify(kafkaService, times(1)).pushToKafka(eq(EXCHANGE_ORDER_UNHOLD_QUEUE), eq("123"), eq(exchangeOrderUnholdRequestJson));
    }


}