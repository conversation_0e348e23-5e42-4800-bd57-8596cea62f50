package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.SystemPreferenceRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {SystemPreferenceServiceImpl.class})
@ExtendWith(SpringExtension.class)
class SystemPreferenceServiceImplTest {
    @MockBean
    private RedisTemplate redisTemplate;

    @MockBean
    private SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    private SystemPreferenceServiceImpl systemPreferenceServiceImpl;

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAll()}
     */
    @Test
    void testFindAll() {
        // Arrange
        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        when(systemPreferenceRepository.findAll()).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllResult = systemPreferenceServiceImpl.findAll();

        // Assert
        verify(systemPreferenceRepository).findAll();
        assertTrue(actualFindAllResult.isEmpty());
        assertSame(systemPreferenceList, actualFindAllResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAll()}
     */
    @Test
    void testFindAll2() {
        // Arrange
        when(systemPreferenceRepository.findAll()).thenThrow(new RuntimeException("foo"));

        // Act
        List<SystemPreference> actualFindAllResult = systemPreferenceServiceImpl.findAll();

        // Assert
        verify(systemPreferenceRepository).findAll();
        assertNull(actualFindAllResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByGroup(String)}
     */
    @Test
    void testFindAllByGroup() {
        // Arrange
        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any())).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group");

        // Assert
        verify(systemPreferenceRepository).findAllByGroup(eq("Group"));
        assertTrue(actualFindAllByGroupResult.isEmpty());
        assertSame(systemPreferenceList, actualFindAllByGroupResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByGroup(String)}
     */
    @Test
    void testFindAllByGroup2() {
        // Arrange
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any())).thenThrow(new RuntimeException("foo"));

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group");

        // Assert
        verify(systemPreferenceRepository).findAllByGroup(eq("Group"));
        assertNull(actualFindAllByGroupResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findAllByGroup(String, int, TimeUnit)}
     */
    @Test
    void testFindAllByGroup3() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);
        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any())).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group", 1,
                TimeUnit.NANOSECONDS);

        // Assert
        verify(systemPreferenceRepository).findAllByGroup(eq("Group"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertTrue(actualFindAllByGroupResult.isEmpty());
        assertSame(systemPreferenceList, actualFindAllByGroupResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findAllByGroup(String, int, TimeUnit)}
     */
    @Test
    void testFindAllByGroup4() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any()))
                .thenThrow(new RuntimeException("[getSystemPreferenceValues] Exception in config set {}"));

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group", 1,
                TimeUnit.NANOSECONDS);

        // Assert
        verify(systemPreferenceRepository).findAllByGroup(eq("Group"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertNull(actualFindAllByGroupResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findAllByGroup(String, int, TimeUnit)}
     */
    @Test
    void testFindAllByGroup5() {
        // Arrange
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(false);
        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any())).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group", 1,
                TimeUnit.NANOSECONDS);

        // Assert
        verify(systemPreferenceRepository).findAllByGroup(eq("Group"));
        verify(redisTemplate).hasKey(isA(Object.class));
        assertTrue(actualFindAllByGroupResult.isEmpty());
        assertSame(systemPreferenceList, actualFindAllByGroupResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findAllByGroup(String, int, TimeUnit)}
     */
    @Test
    void testFindAllByGroup6() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(false);

        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setId(3L);
        systemPreference.setKey("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setType("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setValue("");

        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        systemPreferenceList.add(systemPreference);
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any())).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group", 1,
                TimeUnit.NANOSECONDS);

        // Assert
        verify(systemPreferenceRepository, atLeast(1)).findAllByGroup(eq("Group"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertSame(systemPreferenceList, actualFindAllByGroupResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findAllByGroup(String, int, TimeUnit)}
     */
    @Test
    void testFindAllByGroup7() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(false);

        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setId(3L);
        systemPreference.setKey("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setType("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setValue("");

        SystemPreference systemPreference2 = new SystemPreference();
        systemPreference2.setGroup("[getSystemPreferenceValues] Exception in config set {}");
        systemPreference2.setId(1L);
        systemPreference2.setKey("[getSystemPreferenceValues] Exception in config set {}");
        systemPreference2.setType("[getSystemPreferenceValues] Exception in config set {}");
        systemPreference2.setValue("42");

        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        systemPreferenceList.add(systemPreference2);
        systemPreferenceList.add(systemPreference);
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any())).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group", 1,
                TimeUnit.NANOSECONDS);

        // Assert
        verify(systemPreferenceRepository, atLeast(1)).findAllByGroup(eq("Group"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertSame(systemPreferenceList, actualFindAllByGroupResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findAllByGroup(String, int, TimeUnit)}
     */
    @Test
    void testFindAllByGroup8() {
        // Arrange, Act and Assert
        assertNull(systemPreferenceServiceImpl.findAllByGroup("", 1, TimeUnit.NANOSECONDS));
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findAllByGroup(String, int, TimeUnit)}
     */
    @Test
    void testFindAllByGroup9() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(false);
        SystemPreference systemPreference = mock(SystemPreference.class);
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setId(3L);
        systemPreference.setKey("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setType("com.lenskart.returnrepository.entity.SystemPreference");
        systemPreference.setValue("");

        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        systemPreferenceList.add(systemPreference);
        when(systemPreferenceRepository.findAllByGroup(Mockito.<String>any())).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllByGroupResult = systemPreferenceServiceImpl.findAllByGroup("Group", 1,
                TimeUnit.NANOSECONDS);

        // Assert
        verify(systemPreference).setGroup(eq("com.lenskart.returnrepository.entity.SystemPreference"));
        verify(systemPreference).setId(eq(3L));
        verify(systemPreference).setKey(eq("com.lenskart.returnrepository.entity.SystemPreference"));
        verify(systemPreference).setType(eq("com.lenskart.returnrepository.entity.SystemPreference"));
        verify(systemPreference).setValue(eq(""));
        verify(systemPreferenceRepository, atLeast(1)).findAllByGroup(eq("Group"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertEquals(1, actualFindAllByGroupResult.size());
        assertSame(systemPreferenceList, actualFindAllByGroupResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getByDatabase(String, String)}
     */
    @Test
    void testGetByDatabase() {
        // Arrange
        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        String actualByDatabase = systemPreferenceServiceImpl.getByDatabase("Key", "Group");

        // Assert
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        assertEquals("42", actualByDatabase);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getByDatabase(String, String)}
     */
    @Test
    void testGetByDatabase2() {
        // Arrange
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("42");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        String actualByDatabase = systemPreferenceServiceImpl.getByDatabase("Key", "Group");

        // Assert
        verify(systemPreference).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        assertEquals("42", actualByDatabase);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getByDatabase(String, String)}
     */
    @Test
    void testGetByDatabase3() {
        // Arrange
        SystemPreference systemPreference = mock(SystemPreference.class);
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");

        // Act
        String actualByDatabase = systemPreferenceServiceImpl.getByDatabase("", "Group");

        // Assert
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        assertNull(actualByDatabase);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getByDatabase(String, String)}
     */
    @Test
    void testGetByDatabase4() {
        // Arrange
        SystemPreference systemPreference = mock(SystemPreference.class);
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");

        // Act
        String actualByDatabase = systemPreferenceServiceImpl.getByDatabase("Key", "");

        // Assert
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        assertNull(actualByDatabase);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getPaymentCaptureLSDate()}
     */
    @Test
    void testGetPaymentCaptureLSDate() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);

        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        Date actualPaymentCaptureLSDate = systemPreferenceServiceImpl.getPaymentCaptureLSDate();

        // Assert
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("payment_capture"), eq("PAYMENT_CAPTURE_LK_AFTER"));
        verify(redisTemplate, atLeast(1)).opsForValue();
        assertNull(actualPaymentCaptureLSDate);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getPaymentCaptureLSDate()}
     */
    @Test
    void testGetPaymentCaptureLSDate2() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenThrow(new RuntimeException("[getPaymentCaptureLSDate] : Start"));

        // Act
        Date actualPaymentCaptureLSDate = systemPreferenceServiceImpl.getPaymentCaptureLSDate();

        // Assert
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("payment_capture"), eq("PAYMENT_CAPTURE_LK_AFTER"));
        verify(redisTemplate).opsForValue();
        assertNull(actualPaymentCaptureLSDate);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getPaymentCaptureLSDate()}
     */
    @Test
    void testGetPaymentCaptureLSDate3() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("42");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        Date actualPaymentCaptureLSDate = systemPreferenceServiceImpl.getPaymentCaptureLSDate();

        // Assert
        verify(systemPreference, atLeast(1)).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("payment_capture"), eq("PAYMENT_CAPTURE_LK_AFTER"));
        verify(redisTemplate, atLeast(1)).opsForValue();
        assertNull(actualPaymentCaptureLSDate);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getPaymentCaptureLSDate()}
     */
    @Test
    void testGetPaymentCaptureLSDate4() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn(null);
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        Date actualPaymentCaptureLSDate = systemPreferenceServiceImpl.getPaymentCaptureLSDate();

        // Assert
        verify(systemPreference).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("payment_capture"), eq("PAYMENT_CAPTURE_LK_AFTER"));
        verify(redisTemplate, atLeast(1)).opsForValue();
        assertNull(actualPaymentCaptureLSDate);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getPaymentCaptureLSDate()}
     */
    @Test
    void testGetPaymentCaptureLSDate5() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        Date actualPaymentCaptureLSDate = systemPreferenceServiceImpl.getPaymentCaptureLSDate();

        // Assert
        verify(systemPreference, atLeast(1)).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("payment_capture"), eq("PAYMENT_CAPTURE_LK_AFTER"));
        verify(redisTemplate, atLeast(1)).opsForValue();
        assertNull(actualPaymentCaptureLSDate);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findOneByGroupAndKey(String, String)}
     */
    @Test
    void testFindOneByGroupAndKey() {
        // Arrange
        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        SystemPreference actualFindOneByGroupAndKeyResult = systemPreferenceServiceImpl.findOneByGroupAndKey("Group",
                "Key");

        // Assert
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        assertSame(systemPreference, actualFindOneByGroupAndKeyResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#findOneByGroupAndKey(String, String)}
     */
    @Test
    void testFindOneByGroupAndKey2() {
        // Arrange
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenThrow(new RuntimeException("foo"));

        // Act
        SystemPreference actualFindOneByGroupAndKeyResult = systemPreferenceServiceImpl.findOneByGroupAndKey("Group",
                "Key");

        // Assert
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        assertNull(actualFindOneByGroupAndKeyResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByGroupIn(List)}
     */
    @Test
    void testFindAllByGroupIn() {
        // Arrange
        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        when(systemPreferenceRepository.findAllByGroupIn(Mockito.<List<String>>any())).thenReturn(systemPreferenceList);

        // Act
        List<SystemPreference> actualFindAllByGroupInResult = systemPreferenceServiceImpl
                .findAllByGroupIn(new ArrayList<>());

        // Assert
        verify(systemPreferenceRepository).findAllByGroupIn(isA(List.class));
        assertTrue(actualFindAllByGroupInResult.isEmpty());
        assertSame(systemPreferenceList, actualFindAllByGroupInResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByGroupIn(List)}
     */
    @Test
    void testFindAllByGroupIn2() {
        // Arrange
        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        when(systemPreferenceRepository.findAllByGroupIn(Mockito.<List<String>>any())).thenReturn(systemPreferenceList);

        ArrayList<String> groups = new ArrayList<>();
        groups.add("foo");

        // Act
        List<SystemPreference> actualFindAllByGroupInResult = systemPreferenceServiceImpl.findAllByGroupIn(groups);

        // Assert
        verify(systemPreferenceRepository).findAllByGroupIn(isA(List.class));
        assertTrue(actualFindAllByGroupInResult.isEmpty());
        assertSame(systemPreferenceList, actualFindAllByGroupInResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByGroupIn(List)}
     */
    @Test
    void testFindAllByGroupIn3() {
        // Arrange
        ArrayList<SystemPreference> systemPreferenceList = new ArrayList<>();
        when(systemPreferenceRepository.findAllByGroupIn(Mockito.<List<String>>any())).thenReturn(systemPreferenceList);

        ArrayList<String> groups = new ArrayList<>();
        groups.add("42");
        groups.add("foo");

        // Act
        List<SystemPreference> actualFindAllByGroupInResult = systemPreferenceServiceImpl.findAllByGroupIn(groups);

        // Assert
        verify(systemPreferenceRepository).findAllByGroupIn(isA(List.class));
        assertTrue(actualFindAllByGroupInResult.isEmpty());
        assertSame(systemPreferenceList, actualFindAllByGroupInResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByGroupIn(List)}
     */
    @Test
    void testFindAllByGroupIn4() {
        // Arrange
        when(systemPreferenceRepository.findAllByGroupIn(Mockito.<List<String>>any()))
                .thenThrow(new RuntimeException("foo"));

        // Act
        List<SystemPreference> actualFindAllByGroupInResult = systemPreferenceServiceImpl
                .findAllByGroupIn(new ArrayList<>());

        // Assert
        verify(systemPreferenceRepository).findAllByGroupIn(isA(List.class));
        assertNull(actualFindAllByGroupInResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByKey(String)}
     */
    @Test
    void testFindAllByKey() {
        // Arrange
        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findAllByKey(Mockito.<String>any())).thenReturn(systemPreference);

        // Act
        SystemPreference actualFindAllByKeyResult = systemPreferenceServiceImpl.findAllByKey("Key");

        // Assert
        verify(systemPreferenceRepository).findAllByKey(eq("Key"));
        assertSame(systemPreference, actualFindAllByKeyResult);
    }

    /**
     * Method under test: {@link SystemPreferenceServiceImpl#findAllByKey(String)}
     */
    @Test
    void testFindAllByKey2() {
        // Arrange
        when(systemPreferenceRepository.findAllByKey(Mockito.<String>any())).thenThrow(new RuntimeException("foo"));

        // Act
        SystemPreference actualFindAllByKeyResult = systemPreferenceServiceImpl.findAllByKey("Key");

        // Assert
        verify(systemPreferenceRepository).findAllByKey(eq("Key"));
        assertNull(actualFindAllByKeyResult);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getSystemPreferenceValues(String, String)}
     */
    @Test
    void testGetSystemPreferenceValues() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);

        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        String actualSystemPreferenceValues = systemPreferenceServiceImpl.getSystemPreferenceValues("Key", "Group");

        // Assert
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertEquals("42", actualSystemPreferenceValues);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getSystemPreferenceValues(String, String)}
     */
    @Test
    void testGetSystemPreferenceValues2() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenThrow(new RuntimeException("[getSystemPreferenceValues] Exception in config set "));

        // Act and Assert
        assertThrows(RuntimeException.class, () -> systemPreferenceServiceImpl.getSystemPreferenceValues("Key", "Group"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getSystemPreferenceValues(String, String)}
     */
    @Test
    void testGetSystemPreferenceValues3() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("42");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        String actualSystemPreferenceValues = systemPreferenceServiceImpl.getSystemPreferenceValues("Key", "Group");

        // Assert
        verify(systemPreference).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertEquals("42", actualSystemPreferenceValues);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getSystemPreferenceValues(String, String)}
     */
    @Test
    void testGetSystemPreferenceValues4() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(false);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("42");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        String actualSystemPreferenceValues = systemPreferenceServiceImpl.getSystemPreferenceValues("Key", "Group");

        // Assert
        verify(systemPreference, atLeast(1)).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository, atLeast(1)).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertEquals("42", actualSystemPreferenceValues);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getSystemPreferenceValues(String, String)}
     */
    @Test
    void testGetSystemPreferenceValues5() {
        // Arrange
        SystemPreference systemPreference = mock(SystemPreference.class);
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");

        // Act
        String actualSystemPreferenceValues = systemPreferenceServiceImpl.getSystemPreferenceValues("", "Group");

        // Assert
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        assertNull(actualSystemPreferenceValues);
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getListFromValue(String, String)}
     */
    @Test
    void testGetListFromValue() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);

        SystemPreference systemPreference = new SystemPreference();
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        List<String> actualListFromValue = systemPreferenceServiceImpl.getListFromValue("Group", "Key");

        // Assert
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertEquals(1, actualListFromValue.size());
        assertEquals("42", actualListFromValue.get(0));
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getListFromValue(String, String)}
     */
    @Test
    void testGetListFromValue2() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenThrow(new RuntimeException("[getSystemPreferenceValues] Exception in config set "));

        // Act and Assert
        assertThrows(RuntimeException.class, () -> systemPreferenceServiceImpl.getListFromValue("Group", "Key"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getListFromValue(String, String)}
     */
    @Test
    void testGetListFromValue3() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("42");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        List<String> actualListFromValue = systemPreferenceServiceImpl.getListFromValue("Group", "Key");

        // Assert
        verify(systemPreference).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertEquals(1, actualListFromValue.size());
        assertEquals("42", actualListFromValue.get(0));
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getListFromValue(String, String)}
     */
    @Test
    void testGetListFromValue4() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(false);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("42");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        List<String> actualListFromValue = systemPreferenceServiceImpl.getListFromValue("Group", "Key");

        // Assert
        verify(systemPreference, atLeast(1)).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository, atLeast(1)).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertEquals(1, actualListFromValue.size());
        assertEquals("42", actualListFromValue.get(0));
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getListFromValue(String, String)}
     */
    @Test
    void testGetListFromValue5() {
        // Arrange
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(null);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn("42");
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        List<String> actualListFromValue = systemPreferenceServiceImpl.getListFromValue("Group", "Key");

        // Assert
        verify(systemPreference).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        assertEquals(1, actualListFromValue.size());
        assertEquals("42", actualListFromValue.get(0));
    }

    /**
     * Method under test:
     * {@link SystemPreferenceServiceImpl#getListFromValue(String, String)}
     */
    @Test
    void testGetListFromValue6() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);
        SystemPreference systemPreference = mock(SystemPreference.class);
        when(systemPreference.getValue()).thenReturn(null);
        doNothing().when(systemPreference).setGroup(Mockito.<String>any());
        doNothing().when(systemPreference).setId(Mockito.<Long>any());
        doNothing().when(systemPreference).setKey(Mockito.<String>any());
        doNothing().when(systemPreference).setType(Mockito.<String>any());
        doNothing().when(systemPreference).setValue(Mockito.<String>any());
        systemPreference.setGroup("Group");
        systemPreference.setId(1L);
        systemPreference.setKey("Key");
        systemPreference.setType("Type");
        systemPreference.setValue("42");
        when(systemPreferenceRepository.findTopByGroupAndKey(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(systemPreference);

        // Act
        List<String> actualListFromValue = systemPreferenceServiceImpl.getListFromValue("Group", "Key");

        // Assert
        verify(systemPreference).getValue();
        verify(systemPreference).setGroup(eq("Group"));
        verify(systemPreference).setId(eq(1L));
        verify(systemPreference).setKey(eq("Key"));
        verify(systemPreference).setType(eq("Type"));
        verify(systemPreference).setValue(eq("42"));
        verify(systemPreferenceRepository).findTopByGroupAndKey(eq("Group"), eq("Key"));
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForValue();
        assertTrue(actualListFromValue.isEmpty());
        assertEquals(actualListFromValue, systemPreferenceServiceImpl.findAll());
    }
}
