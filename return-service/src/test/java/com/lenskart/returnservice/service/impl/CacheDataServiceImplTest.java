package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {CacheDataServiceImpl.class})
@ExtendWith(SpringExtension.class)
class CacheDataServiceImplTest {
    @Autowired
    private CacheDataServiceImpl cacheDataServiceImpl;

    @MockBean
    private RedisTemplate redisTemplate;

    /**
     * Method under test:
     * {@link CacheDataServiceImpl#getMessageForHubEngine(String)}
     */
    @Test
    void testGetMessageForHubEngine() {
        // Arrange
        when(redisTemplate.opsForSet()).thenReturn(null);
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(true);

        // Act
        Object actualMessageForHubEngine = cacheDataServiceImpl.getMessageForHubEngine("Key");

        // Assert
        verify(redisTemplate).hasKey(isA(Object.class));
        verify(redisTemplate).opsForSet();
        assertNull(actualMessageForHubEngine);
    }

    /**
     * Method under test:
     * {@link CacheDataServiceImpl#getMessageForHubEngine(String)}
     */
    @Test
    void testGetMessageForHubEngine2() {
        // Arrange
        when(redisTemplate.hasKey(Mockito.<Object>any())).thenReturn(false);

        // Act
        Object actualMessageForHubEngine = cacheDataServiceImpl.getMessageForHubEngine("Key");

        // Assert
        verify(redisTemplate).hasKey(isA(Object.class));
        assertNull(actualMessageForHubEngine);
    }

    /**
     * Method under test:
     * {@link CacheDataServiceImpl#saveMessage(String, Object, String)}
     */
    @Test
    void testSaveMessage() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);

        // Act
        cacheDataServiceImpl.saveMessage("Key", "Value", "Cache Expiry");

        // Assert that nothing has changed
        verify(redisTemplate).opsForValue();
    }

    /**
     * Method under test:
     * {@link CacheDataServiceImpl#saveMessage(String, Object, String)}
     */
    @Test
    void testSaveMessage2() {
        // Arrange
        when(redisTemplate.opsForValue()).thenReturn(null);

        // Act
        cacheDataServiceImpl.saveMessage("Key", "Value", "42");

        // Assert that nothing has changed
        verify(redisTemplate).opsForValue();
    }
}
