package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.returnrepository.entity.ReturnHeadingSubheadingMapping;
import com.lenskart.returnrepository.repository.ReturnHeadingSubheadingMappingRepository;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ReturnHeadingSubheadingMappingCacheServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ReturnHeadingSubheadingMappingCacheServiceImplTest {
    @Autowired
    private ReturnHeadingSubheadingMappingCacheServiceImpl returnHeadingSubheadingMappingCacheServiceImpl;

    @MockBean
    private ReturnHeadingSubheadingMappingRepository returnHeadingSubheadingMappingRepository;

    /**
     * Method under test:
     * {@link ReturnHeadingSubheadingMappingCacheServiceImpl#getMapping(String, String, String, String, String, Boolean, Boolean, Boolean, String)}
     */
    @Test
    void testGetMapping() {
        // Arrange
        ReturnHeadingSubheadingMapping returnHeadingSubheadingMapping = new ReturnHeadingSubheadingMapping();
        returnHeadingSubheadingMapping
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnHeadingSubheadingMapping.setDispatchPoint("Dispatch Point");
        returnHeadingSubheadingMapping.setExchangeCreated(true);
        returnHeadingSubheadingMapping.setExchangeExpired(true);
        returnHeadingSubheadingMapping.setExchangeOrderDispatched(true);
        returnHeadingSubheadingMapping.setId(1);
        returnHeadingSubheadingMapping.setRefundMethod("Refund Method");
        returnHeadingSubheadingMapping.setRefundMode("Refund Mode");
        returnHeadingSubheadingMapping.setRefundStatus("Refund Status");
        returnHeadingSubheadingMapping.setReturnSource("Return Source");
        returnHeadingSubheadingMapping.setReturnStatus("Return Status");
        returnHeadingSubheadingMapping.setTopHeader("Top Header");
        returnHeadingSubheadingMapping.setTopSubHeader("Top Sub Header");
        when(returnHeadingSubheadingMappingRepository.findByParams(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<Boolean>any(),
                Mockito.<Boolean>any(), Mockito.<Boolean>any(), Mockito.<String>any()))
                .thenReturn(returnHeadingSubheadingMapping);

        // Act
        ReturnHeadingSubheadingMapping actualMapping = returnHeadingSubheadingMappingCacheServiceImpl.getMapping(
                "Return Source", "Return Status", "Dispatch Point", "Refund Method", "Refund Mode", true, true, true,
                "Refund Status");

        // Assert
        verify(returnHeadingSubheadingMappingRepository).findByParams(eq("Return Source"), eq("Return Status"),
                eq("Dispatch Point"), eq("Refund Method"), eq("Refund Mode"), eq(true), eq(true), eq(true),
                eq("Refund Status"));
        assertSame(returnHeadingSubheadingMapping, actualMapping);
    }
}
