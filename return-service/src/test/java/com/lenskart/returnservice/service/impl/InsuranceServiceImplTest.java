package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.dto.insurance.*;
import com.lenskart.returncommon.utils.SymboUtil;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.ICacheDataService;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InsuranceServiceImplTest {
    @InjectMocks
    private InsuranceServiceImpl insuranceService;

    @Mock
    private ICacheDataService cacheDataService;

    @Mock
    private ISystemPreferenceService systemPreferenceService;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private SymboUtil symboUtil;

    private InsuranceEligibilityRequest insuranceEligibilityRequest;
    private Integer incrementId;

    @BeforeEach
    public void setUp() {
        incrementId = 12345;
        insuranceEligibilityRequest = new InsuranceEligibilityRequest();
        insuranceEligibilityRequest.setMagentoItemIds(List.of(1, 2, 3));
    }

    @Test
    public void testGetInsurancEligibilityDetailsForItems_ValidRequest() {
        List<OrdersDTO> orders = new ArrayList<>();
        OrdersDTO order = new OrdersDTO();
        order.setProductId(100);
        order.setIncrementId(incrementId);
        orders.add(order);

        OrderInfoResponseDTO orderInfoResponseDTO = mock(OrderInfoResponseDTO.class);
        orderInfoResponseDTO.setOrders(orders);
        ResponseEntity<OrderInfoResponseDTO> responseEntity = ResponseEntity.ok(orderInfoResponseDTO);
        when(orderInfoResponseDTO.getOrders()).thenReturn(orders);
        when(orderOpsFeignClient.getOrderDetails(incrementId)).thenReturn(responseEntity);
        when(symboUtil.getInsuranceClaimDetails(anyInt())).thenReturn(mockExternalOpticalInsuranceClaimDetailResponse());
        when(cacheDataService.getMessageForHubEngine(anyString())).thenReturn(List.of(100));

        InsuranceEligibilityResponse response = insuranceService.getInsurancEligibilityDetailsForItems(incrementId, insuranceEligibilityRequest);

        assertNotNull(response);
        assertEquals(incrementId, response.getIncrementId());
        assertEquals(3, response.getItemsInsuranceDetailList().size());
        verify(orderOpsFeignClient, times(1)).getOrderDetails(anyInt());
        verify(symboUtil, times(1)).getInsuranceClaimDetails(anyInt());
    }

    @Test
    public void testGetInsurancEligibilityDetailsForItems_InvalidRequest() {
        InsuranceEligibilityRequest invalidRequest = new InsuranceEligibilityRequest();
        invalidRequest.setMagentoItemIds(null);

        InsuranceEligibilityResponse response = insuranceService.getInsurancEligibilityDetailsForItems(incrementId, invalidRequest);

        assertNotNull(response);
        assertEquals("Invalid Request: IncrementId or MagentoItemIds are not in the request", response.getErrorMessage());
    }

    @Test
    public void testGetInsurancEligibilityDetailsForItems_OrderNotFound() {
        OrderInfoResponseDTO orderInfoResponseDTO = mock(OrderInfoResponseDTO.class);
        when(orderInfoResponseDTO.getOrders()).thenReturn(Collections.emptyList());
        ResponseEntity<OrderInfoResponseDTO> responseEntity = ResponseEntity.ok(orderInfoResponseDTO);

        when(orderOpsFeignClient.getOrderDetails(anyInt())).thenReturn(responseEntity);

        InsuranceEligibilityResponse response = insuranceService.getInsurancEligibilityDetailsForItems(incrementId, insuranceEligibilityRequest);

        assertNotNull(response);
        assertEquals("Not Found: IncrementId or MagentoItemIds are not found", response.getErrorMessage());
        verify(orderOpsFeignClient, times(1)).getOrderDetails(anyInt());
    }

    private ExternalOpticalInsuranceClaimDetailResponse mockExternalOpticalInsuranceClaimDetailResponse() {
        ExternalOpticalInsuranceClaimDetailResponse response = new ExternalOpticalInsuranceClaimDetailResponse();
        response.setStatus(200);
        LenskartClaimDetailStatusResponse claimDetail = new LenskartClaimDetailStatusResponse();
        claimDetail.setIsClaimAllowed(true);
        response.setClaim(claimDetail);
        List<Product> products = new ArrayList<>();
        Product product = new Product();
        product.setProductId("1");
        product.setSumInsured(1000.0);
        product.setMaximumClaimableAmount(800.0);
        product.setCopayAmount(200.0);
        products.add(product);
        response.setProducts(products);
        return response;
    }

}