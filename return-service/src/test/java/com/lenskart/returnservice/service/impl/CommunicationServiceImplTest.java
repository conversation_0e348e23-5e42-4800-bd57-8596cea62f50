package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.ExchangeAddressDTO;
import com.lenskart.ordermetadata.dto.request.PickupAddressDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnSourcesDTO;
import com.lenskart.returncommon.model.dto.ExchangeAddress;
import com.lenskart.returncommon.model.dto.PickupAddress;
import com.lenskart.returncommon.model.dto.ReturnCommunicationDTO;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnservice.factory.CommunicationPayloadFactoryProducer;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {CommunicationServiceImpl.class})
@ExtendWith(SpringExtension.class)
class CommunicationServiceImplTest {
    @Autowired
    private CommunicationServiceImpl communicationServiceImpl;

    @MockBean
    private IExchangeOrderService iExchangeOrderService;

    @MockBean
    private IRefundUtilsService iRefundUtilsService;

    @MockBean
    private IExchangeRefundMethodService iExchangeRefundMethodService;

    @MockBean
    private CommunicationPayloadFactoryProducer communicationPayloadFactoryProducer;

    @MockBean
    private ICommunicationTemplateService iCommunicationTemplateService;

    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private IReturnEventService iReturnEventService;

    @MockBean
    private ISystemPreferenceService iSystemPreferenceService;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private IReturnOrderActionService returnOrderActionService;
    /**
     * Method under test:
     * {@link CommunicationServiceImpl#sendCommunication(ReturnCommunicationDTO)}
     */
    @Test
    void testSendCommunication() {
        // Arrange
        when(orderOpsFeignClient.sendReturnCommunication(Mockito.<ReturnCommunicationDTO>any())).thenReturn(null);

        OrdersDTO ordersDTO = new OrdersDTO();
//        ordersDTO.setB2bRefrenceItemId("42");
//        ordersDTO.setBaseTotalInvoiced(new BigDecimal("2.3"));
        ordersDTO.setChannel("Channel");
//        ordersDTO.setComments((byte) 'A');
//        ordersDTO.setCouponCode("Coupon Code");
//        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        ordersDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setCustomerEmail("<EMAIL>");
//        ordersDTO.setCustomerId(1L);
//        ordersDTO.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setDiscount(3);
//        ordersDTO.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO.setExtCustomerId("42");
        ordersDTO.setFacilityCode("Facility Code");

//        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
//        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
//        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        ordersDTO.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");

        ExchangeAddressDTO exchangeAddress = new ExchangeAddressDTO();
        exchangeAddress.setAddressType("42 Main St");
        exchangeAddress.setAddressline1("42 Main St");
        exchangeAddress.setCity("Oxford");

        PickupAddressDTO ReversePickupAddress = new PickupAddressDTO();
        ReversePickupAddress.setCity("Oxford");
        ReversePickupAddress.setCountry("GB");

        ReturnCreationRequestDTO returnCreationRequest = new ReturnCreationRequestDTO();
        returnCreationRequest.setCallbackRequiredToSalesman(true);
        returnCreationRequest.setExchangeAddress(exchangeAddress);
        returnCreationRequest.setFacilityCode("Facility Code");
        returnCreationRequest.setIncrementId(1);
        returnCreationRequest.setProductIdsMap(new HashMap<>());
        returnCreationRequest.setReturnMethod("Return Method");
        returnCreationRequest.setReturnSource(ReturnSourcesDTO.WEB);
        returnCreationRequest.setReversePickupAddress(ReversePickupAddress);
        returnCreationRequest.setSalesmanName("Salesman Name");
        returnCreationRequest.setSalesmanNumber("42");
        returnCreationRequest.setStoreEmail("<EMAIL>");
        returnCreationRequest.setStoreFacilityCode("Store Facility Code");

        ReturnOrderDTO returnOrderDTO = new ReturnOrderDTO();
        returnOrderDTO.setFacilityCode("Facility Code");
        returnOrderDTO.setGroupId(1);
        returnOrderDTO.setId(1);
        returnOrderDTO.setIncrementId(1);
        returnOrderDTO.setIsInsurance(true);
        returnOrderDTO.setIsQcAtDoorstep(1);
        returnOrderDTO.setReceivingFlag("Yes");
        returnOrderDTO.setRequestId(1);
        returnOrderDTO
                .setReturnCreateDatetime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrderDTO.setReturnMethod("Return Method");
        returnOrderDTO.setReturnType("Return Type");
        returnOrderDTO.setSource("Source");
        returnOrderDTO.setUnicomOrderCode("Unicom Order Code");

        ReturnCommunicationDTO returnCommunicationDTO = new ReturnCommunicationDTO();
        returnCommunicationDTO.setCommunicationMethod("Communication Method");
        returnCommunicationDTO.setExchangeItemList(new ArrayList<>());
        returnCommunicationDTO.setFlow("Flow");
        returnCommunicationDTO.setGroupId(1L);
        returnCommunicationDTO.setIncrementId(1);
        returnCommunicationDTO.setOrdersDTO(ordersDTO);
        returnCommunicationDTO.setRefundMethod("Refund Method");
        returnCommunicationDTO.setReturnCreationRequest(returnCreationRequest);
        returnCommunicationDTO.setReturnOrderDTO(returnOrderDTO);
        returnCommunicationDTO.setUwItemId(1);
        returnCommunicationDTO.setUwOrderDTOs(new ArrayList<>());

        // Act
        //communicationServiceImpl.sendCommunication(returnCommunicationDTO);

        // Assert that nothing has changed
//        verify(orderOpsFeignClient).sendReturnCommunication(isA(ReturnCommunicationDTO.class));
    }

    /**
     * Method under test:
     * {@link CommunicationServiceImpl#triggerCommunication(ReverseCourierDetail, ReturnCreationRequestDTO, Long, List, List, List, Integer, Integer, Boolean)}
     */
    @Test
    void testTriggerCommunication() {
        // Arrange
        when(iSystemPreferenceService.getSystemPreferenceValues(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn("42");

        ReverseCourierDetail reverseCourierDetail = new ReverseCourierDetail();
        reverseCourierDetail.setAllowDispensingCourier(true);
        reverseCourierDetail.setCourier("Courier");
        reverseCourierDetail.setCourierAssignmentType(QCType.QC_REQUIRED);
        reverseCourierDetail.setCourierReassigned(true);
        reverseCourierDetail.setCpId(1);
        reverseCourierDetail.setCpName("Cp Name");
        reverseCourierDetail.setPincode(1);
        reverseCourierDetail.setQcAtDoorStepEligibleByCourierDetails(true);
        reverseCourierDetail.setStatus(true);
        reverseCourierDetail.setTat(1);

        ExchangeAddressDTO exchangeAddress = new ExchangeAddressDTO();
        exchangeAddress.setAddressType("42 Main St");
        exchangeAddress.setAddressline1("42 Main St");
        exchangeAddress.setCity("Oxford");
        exchangeAddress.setCountry("GB");
        exchangeAddress.setEmail("<EMAIL>");
        exchangeAddress.setFirstName("Jane");
        exchangeAddress.setGender("Gender");
        exchangeAddress.setLandmark("Landmark");
        exchangeAddress.setLastName("Doe");


        PickupAddressDTO ReversePickupAddress = new PickupAddressDTO();
        ReversePickupAddress.setCity("Oxford");
        ReversePickupAddress.setCountry("GB");
        ReversePickupAddress.setEmail("<EMAIL>");
        ReversePickupAddress.setFirstName("Jane");
        ReversePickupAddress.setLastName("Doe");


        ReturnCreationRequestDTO returnCreationRequest = new ReturnCreationRequestDTO();
        returnCreationRequest.setCallbackRequiredToSalesman(true);
        returnCreationRequest.setExchangeAddress(exchangeAddress);
        returnCreationRequest.setFacilityCode("Facility Code");
        returnCreationRequest.setIncrementId(1);
        returnCreationRequest.setInitiatedBy(1);
        returnCreationRequest.setIsCourierReassigned(true);
        returnCreationRequest.setItems(new ArrayList<>());
        returnCreationRequest.setReturnSource(ReturnSourcesDTO.WEB);
        returnCreationRequest.setReversePickupAddress(ReversePickupAddress);
        returnCreationRequest.setSalesmanName("Salesman Name");
        returnCreationRequest.setSalesmanNumber("42");
        returnCreationRequest.setStoreEmail("<EMAIL>");
        returnCreationRequest.setStoreFacilityCode("Store Facility Code");
        ArrayList<OrdersDTO> returnedOrders = new ArrayList<>();
        ArrayList<UwOrderDTO> uwOrders = new ArrayList<>();

        // Act
        communicationServiceImpl.triggerCommunication(reverseCourierDetail, returnCreationRequest, 1L, returnedOrders,
                uwOrders, new ArrayList<>(), 1, 1, true);

        // Assert that nothing has changed
//        verify(iSystemPreferenceService).getSystemPreferenceValues(eq("OrderReturnSms"), eq("communication_migration"));
    }

//    /**
//     * Method under test:
//     * {@link CommunicationServiceImpl#getReturnCommunicationDTO(String, ReturnOrderDTO, OrdersDTO, String)}
//     */
//    @Test
//    void testGetReturnCommunicationDTO() {
//        // Arrange
//        ReturnOrderDTO returnOrderDTO = new ReturnOrderDTO();
//        returnOrderDTO.setFacilityCode("Facility Code");
//        returnOrderDTO.setGroupId(1);
//        returnOrderDTO.setId(1);
//        returnOrderDTO.setIncrementId(1);
//        returnOrderDTO.setIsInsurance(true);
//        returnOrderDTO.setIsQcAtDoorstep(1);
//        returnOrderDTO.setReceivingFlag("Yes");
//        returnOrderDTO.setRequestId(1);
//        Date returnCreateDatetime = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
//        returnOrderDTO.setReturnCreateDatetime(returnCreateDatetime);
//        returnOrderDTO.setReturnMethod("Return Method");
//        returnOrderDTO.setReturnType("Return Type");
//        returnOrderDTO.setSource("Source");
//        returnOrderDTO.setUnicomOrderCode("Unicom Order Code");
//
//        OrdersDTO ordersDTO = new OrdersDTO();
////        ordersDTO.setB2bRefrenceItemId("42");
//        BigDecimal baseTotalInvoiced = new BigDecimal("2.3");
////        ordersDTO.setBaseTotalInvoiced(baseTotalInvoiced);
//        ordersDTO.setChannel("Channel");
////        ordersDTO.setComments((byte) 'A');
////        ordersDTO.setCouponCode("Coupon Code");
////        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        Date createdAt = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setCreatedAt(createdAt);
////        ordersDTO.setCustomerEmail("<EMAIL>");
////        ordersDTO.setCustomerId(1L);
//        Date deliveryDate = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setDeliveryDate(deliveryDate);
////        ordersDTO.setDiscount(3);
//        Date dispatchDate = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setDispatchDate(dispatchDate);
//        BigDecimal emiCharge = new BigDecimal("2.3");
////        ordersDTO.setEmiCharge(emiCharge);
////        ordersDTO.setExtCustomerId("42");
//        ordersDTO.setFacilityCode("Facility Code");
//        BigDecimal grandTotal = new BigDecimal("2.3");
////        ordersDTO.setGrandTotal(grandTotal);
////        ordersDTO.setHubCountry("GB");
////        ordersDTO.setHubFacility("Hub Facility");
//        ordersDTO.setIncrementId(1);
////        ordersDTO.setIsLocalFittingRequired(true);
//        ordersDTO.setItemId(1);
//        ordersDTO.setMethod("Method");
////        ordersDTO.setOffer3orfree("Offer3orfree");
//        BigDecimal orderDiscountAmount = new BigDecimal("2.3");
////        ordersDTO.setOrderDiscountAmount(orderDiscountAmount);
////        ordersDTO.setOrderGrandTotal(10.0d);
//        ordersDTO.setOrderId(1);
////        ordersDTO.setOrderType("Order Type");
////        ordersDTO.setParentId(1);
////        ordersDTO.setPayementCapture(1);
////        ordersDTO.setPriority(1);
//        ordersDTO.setProductDeliveryType("Product Delivery Type");
//        ordersDTO.setProductId(1);
////        ordersDTO.setProductOptions("Product Options");
////        ordersDTO.setQtyOrdered((short) 1);
////        ordersDTO.setRewardPoints((short) 1);
////        ordersDTO.setSaleSource("Sale Source");
////        ordersDTO.setShipToStoreRequired(true);
//        BigDecimal shippingAmount = new BigDecimal("2.3");
////        ordersDTO.setShippingAmount(shippingAmount);
////        ordersDTO.setState("MD");
////        ordersDTO.setStatus("Status");
////        ordersDTO.setStockOutStatus((byte) 'A');
//        ordersDTO.setStoreId((byte) 'A');
//        ordersDTO.setSubProductId(1);
//        BigDecimal subtotal = new BigDecimal("2.3");
////        ordersDTO.setSubtotal(subtotal);
//        BigDecimal taxCollected = new BigDecimal("2.3");
////        ordersDTO.setTaxCollected(taxCollected);
////        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
////        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
////        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        Date updatedAt = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setUpdatedAt(updatedAt);
////        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");
//
//        // Act
//        ReturnCommunicationDTO actualReturnCommunicationDTO = null;
//                //communicationServiceImpl.getReturnCommunicationDTO("Communication Method", returnOrderDTO, ordersDTO, "Flow");
//
//        // Assert
//        assertEquals("Communication Method", actualReturnCommunicationDTO.getCommunicationMethod());
//        assertNull(actualReturnCommunicationDTO.getReturnCreationRequest());
//        assertNull(actualReturnCommunicationDTO.getIncrementId());
//        assertNull(actualReturnCommunicationDTO.getUwItemId());
//        assertNull(actualReturnCommunicationDTO.getGroupId());
//        assertNull(actualReturnCommunicationDTO.getFlow());
//        assertNull(actualReturnCommunicationDTO.getRefundMethod());
//        assertNull(actualReturnCommunicationDTO.getUwOrderDTOs());
//        assertNull(actualReturnCommunicationDTO.getExchangeItemList());
//        assertSame(ordersDTO, actualReturnCommunicationDTO.getOrdersDTO());
//        assertSame(returnOrderDTO, actualReturnCommunicationDTO.getReturnOrderDTO());
////        assertSame(baseTotalInvoiced, ordersDTO.getBaseTotalInvoiced());
////        assertSame(emiCharge, ordersDTO.getEmiCharge());
////        assertSame(grandTotal, ordersDTO.getGrandTotal());
////        assertSame(orderDiscountAmount, ordersDTO.getOrderDiscountAmount());
////        assertSame(shippingAmount, ordersDTO.getShippingAmount());
////        assertSame(subtotal, ordersDTO.getSubtotal());
////        assertSame(taxCollected, ordersDTO.getTaxCollected());
////        assertSame(createdAt, ordersDTO.getCreatedAt());
////        assertSame(deliveryDate, ordersDTO.getDeliveryDate());
////        assertSame(dispatchDate, ordersDTO.getDispatchDate());
////        assertSame(updatedAt, ordersDTO.getUpdatedAt());
//        assertSame(returnCreateDatetime, returnOrderDTO.getReturnCreateDatetime());
//    }

//    /**
//     * Method under test:
//     * {@link CommunicationServiceImpl#getReturnCommunicationDTO(String, ReturnOrderDTO, OrdersDTO, String)}
//     */
//    @Test
//    void testGetReturnCommunicationDTO2() {
//        // Arrange
//        ReturnOrderDTO returnOrderDTO = mock(ReturnOrderDTO.class);
//        doNothing().when(returnOrderDTO).setFacilityCode(Mockito.<String>any());
//        doNothing().when(returnOrderDTO).setGroupId(Mockito.<Integer>any());
//        doNothing().when(returnOrderDTO).setId(Mockito.<Integer>any());
//        doNothing().when(returnOrderDTO).setIncrementId(Mockito.<Integer>any());
//        doNothing().when(returnOrderDTO).setIsInsurance(Mockito.<Boolean>any());
//        doNothing().when(returnOrderDTO).setIsQcAtDoorstep(Mockito.<Integer>any());
//        doNothing().when(returnOrderDTO).setReceivingFlag(Mockito.anyString());
//        doNothing().when(returnOrderDTO).setRequestId(Mockito.<Integer>any());
//        doNothing().when(returnOrderDTO).setReturnCreateDatetime(Mockito.<Date>any());
//        doNothing().when(returnOrderDTO).setReturnMethod(Mockito.<String>any());
//        doNothing().when(returnOrderDTO).setReturnType(Mockito.<String>any());
//        doNothing().when(returnOrderDTO).setSource(Mockito.<String>any());
//        doNothing().when(returnOrderDTO).setUnicomOrderCode(Mockito.<String>any());
//        returnOrderDTO.setFacilityCode("Facility Code");
//        returnOrderDTO.setGroupId(1);
//        returnOrderDTO.setId(1);
//        returnOrderDTO.setIncrementId(1);
//        returnOrderDTO.setIsInsurance(true);
//        returnOrderDTO.setIsQcAtDoorstep(1);
//        returnOrderDTO.setReceivingFlag("Yes");
//        returnOrderDTO.setRequestId(1);
//        returnOrderDTO
//                .setReturnCreateDatetime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        returnOrderDTO.setReturnMethod("Return Method");
//        returnOrderDTO.setReturnType("Return Type");
//        returnOrderDTO.setSource("Source");
//        returnOrderDTO.setUnicomOrderCode("Unicom Order Code");
//
//        OrdersDTO ordersDTO = new OrdersDTO();
////        ordersDTO.setB2bRefrenceItemId("42");
//        BigDecimal baseTotalInvoiced = new BigDecimal("2.3");
////        ordersDTO.setBaseTotalInvoiced(baseTotalInvoiced);
//        ordersDTO.setChannel("Channel");
////        ordersDTO.setComments((byte) 'A');
////        ordersDTO.setCouponCode("Coupon Code");
////        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        Date createdAt = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setCreatedAt(createdAt);
////        ordersDTO.setCustomerEmail("<EMAIL>");
////        ordersDTO.setCustomerId(1L);
//        Date deliveryDate = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setDeliveryDate(deliveryDate);
////        ordersDTO.setDiscount(3);
//        Date dispatchDate = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setDispatchDate(dispatchDate);
//        BigDecimal emiCharge = new BigDecimal("2.3");
////        ordersDTO.setEmiCharge(emiCharge);
////        ordersDTO.setExtCustomerId("42");
//        ordersDTO.setFacilityCode("Facility Code");
//        BigDecimal grandTotal = new BigDecimal("2.3");
////        ordersDTO.setGrandTotal(grandTotal);
////        ordersDTO.setHubCountry("GB");
////        ordersDTO.setHubFacility("Hub Facility");
//        ordersDTO.setIncrementId(1);
////        ordersDTO.setIsLocalFittingRequired(true);
//        ordersDTO.setItemId(1);
//        ordersDTO.setMethod("Method");
////        ordersDTO.setOffer3orfree("Offer3orfree");
//        BigDecimal orderDiscountAmount = new BigDecimal("2.3");
////        ordersDTO.setOrderDiscountAmount(orderDiscountAmount);
////        ordersDTO.setOrderGrandTotal(10.0d);
//        ordersDTO.setOrderId(1);
////        ordersDTO.setOrderType("Order Type");
////        ordersDTO.setParentId(1);
////        ordersDTO.setPayementCapture(1);
////        ordersDTO.setPriority(1);
//        ordersDTO.setProductDeliveryType("Product Delivery Type");
//        ordersDTO.setProductId(1);
////        ordersDTO.setProductOptions("Product Options");
////        ordersDTO.setQtyOrdered((short) 1);
////        ordersDTO.setRewardPoints((short) 1);
////        ordersDTO.setSaleSource("Sale Source");
////        ordersDTO.setShipToStoreRequired(true);
//        BigDecimal shippingAmount = new BigDecimal("2.3");
////        ordersDTO.setShippingAmount(shippingAmount);
////        ordersDTO.setState("MD");
////        ordersDTO.setStatus("Status");
////        ordersDTO.setStockOutStatus((byte) 'A');
//        ordersDTO.setStoreId((byte) 'A');
//        ordersDTO.setSubProductId(1);
//        BigDecimal subtotal = new BigDecimal("2.3");
////        ordersDTO.setSubtotal(subtotal);
//        BigDecimal taxCollected = new BigDecimal("2.3");
////        ordersDTO.setTaxCollected(taxCollected);
////        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
////        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
////        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        Date updatedAt = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
////        ordersDTO.setUpdatedAt(updatedAt);
////        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");
//
//        // Act
//        ReturnCommunicationDTO actualReturnCommunicationDTO = null;
//                communicationServiceImpl.("Communication Method", returnOrderDTO, ordersDTO, "Flow");
//
//        // Assert
//        verify(returnOrderDTO).setFacilityCode(eq("Facility Code"));
//        verify(returnOrderDTO).setGroupId(eq(1));
//        verify(returnOrderDTO).setId(eq(1));
//        verify(returnOrderDTO).setIncrementId(eq(1));
//        verify(returnOrderDTO).setIsInsurance(eq(true));
//        verify(returnOrderDTO).setIsQcAtDoorstep(eq(1));
//        verify(returnOrderDTO).setReceivingFlag(eq("Yes"));
//        verify(returnOrderDTO).setRequestId(eq(1));
//        verify(returnOrderDTO).setReturnCreateDatetime(isA(Date.class));
//        verify(returnOrderDTO).setReturnMethod(eq("Return Method"));
//        verify(returnOrderDTO).setReturnType(eq("Return Type"));
//        verify(returnOrderDTO).setSource(eq("Source"));
//        verify(returnOrderDTO).setUnicomOrderCode(eq("Unicom Order Code"));
//        assertEquals("Communication Method", actualReturnCommunicationDTO.getCommunicationMethod());
//        assertNull(actualReturnCommunicationDTO.getReturnCreationRequest());
//        assertNull(actualReturnCommunicationDTO.getIncrementId());
//        assertNull(actualReturnCommunicationDTO.getUwItemId());
//        assertNull(actualReturnCommunicationDTO.getGroupId());
//        assertNull(actualReturnCommunicationDTO.getFlow());
//        assertNull(actualReturnCommunicationDTO.getRefundMethod());
//        assertNull(actualReturnCommunicationDTO.getUwOrderDTOs());
//        assertNull(actualReturnCommunicationDTO.getExchangeItemList());
//        assertSame(ordersDTO, actualReturnCommunicationDTO.getOrdersDTO());
////        assertSame(baseTotalInvoiced, ordersDTO.getBaseTotalInvoiced());
////        assertSame(emiCharge, ordersDTO.getEmiCharge());
////        assertSame(grandTotal, ordersDTO.getGrandTotal());
////        assertSame(orderDiscountAmount, ordersDTO.getOrderDiscountAmount());
////        assertSame(shippingAmount, ordersDTO.getShippingAmount());
////        assertSame(subtotal, ordersDTO.getSubtotal());
////        assertSame(taxCollected, ordersDTO.getTaxCollected());
////        assertSame(createdAt, ordersDTO.getCreatedAt());
////        assertSame(deliveryDate, ordersDTO.getDeliveryDate());
////        assertSame(dispatchDate, ordersDTO.getDispatchDate());
////        assertSame(updatedAt, ordersDTO.getUpdatedAt());
//        assertSame(returnOrderDTO, actualReturnCommunicationDTO.getReturnOrderDTO());
//    }

    /**
     * Method under test:
     * {@link CommunicationServiceImpl#sendReturnUpdateCommunication(ReturnOrderDTO, OrdersDTO, ReturnCourierDetail, String, String)}
     */
    @Test
    void testSendReturnUpdateCommunication() {
        // Arrange
        when(iSystemPreferenceService.getSystemPreferenceValues(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn("42");

        ReturnOrderDTO returnOrderDTO = new ReturnOrderDTO();
        returnOrderDTO.setFacilityCode("Facility Code");
        returnOrderDTO.setGroupId(1);
        returnOrderDTO.setId(1);
        returnOrderDTO.setIncrementId(1);
        returnOrderDTO.setIsInsurance(true);
        returnOrderDTO.setIsQcAtDoorstep(1);
        returnOrderDTO.setReceivingFlag("Yes");
        returnOrderDTO.setRequestId(1);
        returnOrderDTO
                .setReturnCreateDatetime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrderDTO.setReturnMethod("Return Method");
        returnOrderDTO.setReturnType("Return Type");
        returnOrderDTO.setSource("Source");
        returnOrderDTO.setUnicomOrderCode("Unicom Order Code");

        OrdersDTO ordersDTO = new OrdersDTO();
//        ordersDTO.setB2bRefrenceItemId("42");
//        ordersDTO.setBaseTotalInvoiced(new BigDecimal("2.3"));
        ordersDTO.setChannel("Channel");
//        ordersDTO.setComments((byte) 'A');
//        ordersDTO.setCouponCode("Coupon Code");
//        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        ordersDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setCustomerEmail("<EMAIL>");
//        ordersDTO.setCustomerId(1L);
//        ordersDTO.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setDiscount(3);
//        ordersDTO.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO.setExtCustomerId("42");
        ordersDTO.setFacilityCode("Facility Code");
//        ordersDTO.setGrandTotal(new BigDecimal("2.3"));
//        ordersDTO.setHubCountry("GB");
//        ordersDTO.setHubFacility("Hub Facility");
        ordersDTO.setIncrementId(1);
//        ordersDTO.setIsLocalFittingRequired(true);
        ordersDTO.setItemId(1);
        ordersDTO.setMethod("Method");
//        ordersDTO.setOffer3orfree("Offer3orfree");
//        ordersDTO.setOrderDiscountAmount(new BigDecimal("2.3"));
//        ordersDTO.setOrderGrandTotal(10.0d);
        ordersDTO.setOrderId(1);
//        ordersDTO.setOrderType("Order Type");
//        ordersDTO.setParentId(1);
//        ordersDTO.setPayementCapture(1);
//        ordersDTO.setPriority(1);
        ordersDTO.setProductDeliveryType("Product Delivery Type");
        ordersDTO.setProductId(1);
//        ordersDTO.setProductOptions("Product Options");
//        ordersDTO.setQtyOrdered((short) 1);
//        ordersDTO.setRewardPoints((short) 1);
//        ordersDTO.setSaleSource("Sale Source");
//        ordersDTO.setShipToStoreRequired(true);
//        ordersDTO.setShippingAmount(new BigDecimal("2.3"));
//        ordersDTO.setState("MD");
//        ordersDTO.setStatus("Status");
//        ordersDTO.setStockOutStatus((byte) 'A');
        ordersDTO.setStoreId((byte) 'A');
        ordersDTO.setSubProductId(1);
//        ordersDTO.setSubtotal(new BigDecimal("2.3"));
//        ordersDTO.setTaxCollected(new BigDecimal("2.3"));
//        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
//        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
//        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        ordersDTO.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");

        ReturnCourierDetail returnCourierDetail = new ReturnCourierDetail();
        returnCourierDetail
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnCourierDetail.setId(1);
        returnCourierDetail
                .setPickupEstimate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnCourierDetail.setReturnId(1);
        returnCourierDetail.setReverseAwbNumber("42");
        returnCourierDetail.setReverseCourier("Reverse Courier");
        returnCourierDetail.setReversePickupReferenceId("42");

        // Act
        communicationServiceImpl.sendReturnUpdateCommunication(returnOrderDTO, ordersDTO, returnCourierDetail, "Status",
                "Curr Status");

        // Assert that nothing has changed
//        verify(iSystemPreferenceService).getSystemPreferenceValues(eq("OrderReturnSms"), eq("communication_migration"));
    }

    /**
     * Method under test:
     * {@link CommunicationServiceImpl#pushToReturnSmsQueue(ReturnCommunicationDTO, Integer)}
     */
    @Test
    void testPushToReturnSmsQueue() {
        // Arrange
        when(iKafkaService.pushToKafka(Mockito.<String>any(), Mockito.<String>any(), Mockito.<Object>any()))
                .thenReturn(null);

        OrdersDTO ordersDTO = new OrdersDTO();
//        ordersDTO.setB2bRefrenceItemId("42");
//        ordersDTO.setBaseTotalInvoiced(new BigDecimal("2.3"));
        ordersDTO.setChannel("Channel");
//        ordersDTO.setComments((byte) 'A');
//        ordersDTO.setCouponCode("Coupon Code");
//        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        ordersDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setCustomerEmail("<EMAIL>");
//        ordersDTO.setCustomerId(1L);
//        ordersDTO.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setDiscount(3);
//        ordersDTO.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO.setExtCustomerId("42");
        ordersDTO.setFacilityCode("Facility Code");
//        ordersDTO.setGrandTotal(new BigDecimal("2.3"));
//        ordersDTO.setHubCountry("GB");
//        ordersDTO.setHubFacility("Hub Facility");
        ordersDTO.setIncrementId(1);
//        ordersDTO.setIsLocalFittingRequired(true);
        ordersDTO.setItemId(1);
        ordersDTO.setMethod("Method");
//        ordersDTO.setOffer3orfree("Offer3orfree");
//        ordersDTO.setOrderDiscountAmount(new BigDecimal("2.3"));
//        ordersDTO.setOrderGrandTotal(10.0d);
        ordersDTO.setOrderId(1);
//        ordersDTO.setOrderType("Order Type");
//        ordersDTO.setParentId(1);
//        ordersDTO.setPayementCapture(1);
//        ordersDTO.setPriority(1);
        ordersDTO.setProductDeliveryType("Product Delivery Type");
        ordersDTO.setProductId(1);
//        ordersDTO.setProductOptions("Product Options");
//        ordersDTO.setQtyOrdered((short) 1);
//        ordersDTO.setRewardPoints((short) 1);
//        ordersDTO.setSaleSource("Sale Source");
//        ordersDTO.setShipToStoreRequired(true);
//        ordersDTO.setShippingAmount(new BigDecimal("2.3"));
//        ordersDTO.setState("MD");
//        ordersDTO.setStatus("Status");
//        ordersDTO.setStockOutStatus((byte) 'A');
        ordersDTO.setStoreId((byte) 'A');
        ordersDTO.setSubProductId(1);
//        ordersDTO.setSubtotal(new BigDecimal("2.3"));
//        ordersDTO.setTaxCollected(new BigDecimal("2.3"));
//        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
//        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
//        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        ordersDTO.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");

        ExchangeAddressDTO exchangeAddress = new ExchangeAddressDTO();
        exchangeAddress.setAddressType("42 Main St");
        exchangeAddress.setAddressline1("42 Main St");
        exchangeAddress.setCity("Oxford");
        exchangeAddress.setCountry("GB");
        exchangeAddress.setEmail("<EMAIL>");
        exchangeAddress.setFirstName("Jane");
        exchangeAddress.setGender("Gender");
        exchangeAddress.setLandmark("Landmark");
        exchangeAddress.setLastName("Doe");
        exchangeAddress.setLocality("Locality");
        exchangeAddress.setPhone("*********4");
        exchangeAddress.setPhoneCode("*********4");
        exchangeAddress.setPostcode("OX1 1PT");
        exchangeAddress.setState("MD");

        PickupAddressDTO ReversePickupAddress = new PickupAddressDTO();
        ReversePickupAddress.setCity("Oxford");
        ReversePickupAddress.setCountry("GB");
        ReversePickupAddress.setEmail("<EMAIL>");
        ReversePickupAddress.setFirstName("Jane");
        ReversePickupAddress.setLastName("Doe");
        ReversePickupAddress.setPincode(1);
        ReversePickupAddress.setState("MD");
        ReversePickupAddress.setStreet1("Street1");
        ReversePickupAddress.setStreet2("Street2");
        ReversePickupAddress.setTelephone("*********4");

        ReturnCreationRequestDTO returnCreationRequest = new ReturnCreationRequestDTO();
        returnCreationRequest.setCallbackRequiredToSalesman(true);
        returnCreationRequest.setExchangeAddress(exchangeAddress);
        returnCreationRequest.setFacilityCode("Facility Code");
        returnCreationRequest.setIncrementId(1);
        returnCreationRequest.setInitiatedBy(1);
        returnCreationRequest.setIsCourierReassigned(true);
        returnCreationRequest.setItems(new ArrayList<>());
        returnCreationRequest.setNewCourier("New Courier");
        returnCreationRequest.setOldCourier("Old Courier");
        returnCreationRequest.setProductIdsMap(new HashMap<>());
        returnCreationRequest.setReturnMethod("Return Method");
        returnCreationRequest.setReturnSource(ReturnSourcesDTO.WEB);
        returnCreationRequest.setReversePickupAddress(ReversePickupAddress);
        returnCreationRequest.setSalesmanName("Salesman Name");
        returnCreationRequest.setSalesmanNumber("42");
        returnCreationRequest.setStoreEmail("<EMAIL>");
        returnCreationRequest.setStoreFacilityCode("Store Facility Code");

        ReturnOrderDTO returnOrderDTO = new ReturnOrderDTO();
        returnOrderDTO.setFacilityCode("Facility Code");
        returnOrderDTO.setGroupId(1);
        returnOrderDTO.setId(1);
        returnOrderDTO.setIncrementId(1);
        returnOrderDTO.setIsInsurance(true);
        returnOrderDTO.setIsQcAtDoorstep(1);
        returnOrderDTO.setReceivingFlag("Yes");
        returnOrderDTO.setRequestId(1);
        returnOrderDTO
                .setReturnCreateDatetime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrderDTO.setReturnMethod("Return Method");
        returnOrderDTO.setReturnType("Return Type");
        returnOrderDTO.setSource("Source");
        returnOrderDTO.setUnicomOrderCode("Unicom Order Code");

        ReturnCommunicationDTO returnCommunicationDTO = new ReturnCommunicationDTO();
        returnCommunicationDTO.setCommunicationMethod("Communication Method");
        returnCommunicationDTO.setExchangeItemList(new ArrayList<>());
        returnCommunicationDTO.setFlow("Flow");
        returnCommunicationDTO.setGroupId(1L);
        returnCommunicationDTO.setIncrementId(1);
        returnCommunicationDTO.setOrdersDTO(ordersDTO);
        returnCommunicationDTO.setRefundMethod("Refund Method");
        returnCommunicationDTO.setReturnCreationRequest(returnCreationRequest);
        returnCommunicationDTO.setReturnOrderDTO(returnOrderDTO);
        returnCommunicationDTO.setUwItemId(1);
        returnCommunicationDTO.setUwOrderDTOs(new ArrayList<>());

        // Act
        //communicationServiceImpl.pushToReturnSmsQueue(returnCommunicationDTO, 1);

        // Assert
//        verify(iKafkaService).pushToKafka(eq("Return_Communication"), eq("1"), isA(Object.class));
    }
}
