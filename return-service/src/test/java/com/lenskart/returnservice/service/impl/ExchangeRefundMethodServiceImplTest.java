package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequestDTO;
import com.lenskart.returncommon.model.dto.RefundMethodsResponse;
import com.lenskart.returncommon.model.response.ItemResponse;
import com.lenskart.returncommon.model.response.ReturnRefundEligibilityResponse;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ExchangeRefundMethodServiceImplTest {

    @Mock
    private ReturnRefundEligibilityServiceImpl returnRefundEligibilityService;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @InjectMocks
    private ExchangeRefundMethodServiceImpl exchangeRefundMethodService;

    private UwOrderDTO uwOrderDTO;
    private ReturnRefundEligibilityResponse returnRefundEligibilityResponse;
    private ItemResponse itemResponse;

    @BeforeEach
    public void setUp() {
        uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);

        itemResponse = new ItemResponse();
        itemResponse.setRefundMethods(Collections.singletonList("Refund Method 1"));
        itemResponse.setExchange(true);

        returnRefundEligibilityResponse = new ReturnRefundEligibilityResponse();
        returnRefundEligibilityResponse.setItemsResponseList(Collections.singletonList(itemResponse));
    }

    @Test
    public void testFindExchangeRefundMethod_Success() throws Exception {
//        when(orderOpsFeignClient.getUwOrderByMagentoItemId(anyInt()))
//                .thenReturn(ResponseEntity.ok(uwOrderDTO));
//        when(returnRefundEligibilityService.getReturnRefundEligibility(any(ReturnRefundEligibilityRequestDTO.class)))
//                .thenReturn(returnRefundEligibilityResponse);

        RefundMethodsResponse response = exchangeRefundMethodService.findExchangeRefundMethod(123, "source",null );

        assertNotNull(response);
        assertEquals(null, response.getMagentoItemId());
        assertEquals(null, response.getIsExchange());
    }

    @Test
    public void testFindExchangeRefundMethod_UwOrderNotFound() throws Exception {
//        when(orderOpsFeignClient.getUwOrderByMagentoItemId(123))
//                .thenReturn(ResponseEntity.of(Optional.empty()));

        RefundMethodsResponse response = exchangeRefundMethodService.findExchangeRefundMethod(123, "source", null);

        assertNotNull(response);
        assertNull(response.getMagentoItemId());
        assertNull(response.getRefundMethods());
        assertNull(response.getIsExchange());
    }

    @Test
    public void testFindExchangeRefundMethod_EmptyItemResponses() throws Exception {
//        when(orderOpsFeignClient.getUwOrderByMagentoItemId(anyInt()))
//                .thenReturn(ResponseEntity.ok(uwOrderDTO));
//        when(returnRefundEligibilityService.getReturnRefundEligibility(any(ReturnRefundEligibilityRequestDTO.class)))
//                .thenReturn(new ReturnRefundEligibilityResponse());

        RefundMethodsResponse response = exchangeRefundMethodService.findExchangeRefundMethod(123, "source",null );

        assertNotNull(response);
        assertEquals(null, response.getMagentoItemId());
        assertEquals(null, response.getRefundMethods());
        assertEquals(null, response.getIsExchange());
    }

}