package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.ordermetadata.dto.request.ReturnSourcesDTO;
import com.lenskart.returncommon.exception.ReturnNotFound;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.IReturnOrderSaleDetailsRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;

@ExtendWith(MockitoExtension.class)
class ReturnCreationServiceImplTest {

    @Mock private RedisTemplate<String, Object> redisTemplate;
    @Mock private ReturnRequestRepository returnRequestRepository;
    @Mock private OrderOpsFeignClient orderOpsFeignClient;
    @Mock private KafkaTemplate<String, Object> kafkaProducerTemplate;
    @Mock private IReturnEventService returnEventService;
    @Mock private IReturnOrderActionService returnOrderActionService;
    @Mock private IReverseCourierDetailService reverseCourierDetailService;
    @Mock private INexsFacilityService nexsFacilityService;
    @Mock private IReturnOrderSaleDetailsRepository returnOrderSaleDetailsRepository;
    @Mock private IRefundRequestService refundRequestService;

    @InjectMocks
    private ReturnCreationServiceImpl returnCreationService;

    private PurchaseOrderDetailsDTO purchaseOrderDetails;
    private ReturnCreationRequestDTO returnRequest;
    private ReverseCourierDetail reverseCourierDetail;

    @BeforeEach
    void setUp() {
        // Initialize valid DTOs with required fields
        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setOrderId(123);
        ordersHeader.setLkCountry("IN");

        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setUwItemId(456);
        uwOrder.setIncrementId(123);
        uwOrder.setUnicomOrderCode("UNI123");

        purchaseOrderDetails = new PurchaseOrderDetailsDTO();
        purchaseOrderDetails.setOrders(List.of(ordersDTO));
        purchaseOrderDetails.setUwOrders(List.of(uwOrder));

        ReturnItemDTO returnItem = new ReturnItemDTO();
        returnItem.setUwItemId(456);
        returnItem.setNeedApproval(false);

        ReturnSourcesDTO returnSource = ReturnSourcesDTO.WEB;

        returnRequest = new ReturnCreationRequestDTO();
        returnRequest.setIncrementId(123);
        returnRequest.setItems(List.of(returnItem));
        returnRequest.setReturnSource(returnSource);

        reverseCourierDetail = new ReverseCourierDetail();
        reverseCourierDetail.setCourierAssignmentType(QCType.QC_REQUIRED);
        reverseCourierDetail.setCourier("LKART");
    }

    @Test
    void createReturn_RepeatedTransaction_ThrowsException() {
        when(redisTemplate.hasKey("456_Return")).thenReturn(true);

        Exception ex = assertThrows(Exception.class, () ->
                returnCreationService.createReturn(
                        purchaseOrderDetails,
                        returnRequest,
                        reverseCourierDetail,
                        "WEB",
                        new ReturnRequest()
                ));

        assertTrue(ex.getMessage().contains("Repeated Transaction"));
        verify(redisTemplate).hasKey("456_Return");
    }

}