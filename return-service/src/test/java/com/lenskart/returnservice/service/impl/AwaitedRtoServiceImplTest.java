package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.refund.client.model.request.RefundMethodRequest;
import com.lenskart.refund.client.model.response.RefundMethodResponse;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnGroup;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnrepository.repository.ReturnGroupRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.*;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {AwaitedRtoServiceImpl.class})
@ExtendWith(SpringExtension.class)
class AwaitedRtoServiceImplTest {
    @MockBean
    private ReturnRequestRepository returnRequestRepository;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private IReturnOrderItemService returnOrderItemService;

    @MockBean
    private ReturnDetailRepository returnOrderRepository;

    @MockBean
    private IReturnOrderActionService returnOrderActionService;

    @MockBean
    private IReturnEventService returnEventService;

    @MockBean
    private ISystemPreferenceService systemPreferenceService;

    @MockBean
    private ReturnUtil returnUtil;

    @MockBean
    private RestTemplate restTemplate;

    @MockBean
    private IRefundUtilsService refundUtilsService;

    @MockBean
    private AmountUtil amountUtil;

    @MockBean
    private ID365FinanceService financeService;

    @MockBean
    private IKafkaService kafkaService;


    @Autowired
    private AwaitedRtoServiceImpl awaitedRtoService;

    @MockBean
    private ReturnGroupRepository returnGroupRepository;

    @Test
    void getRefundMethodAtRto_ApiThrowsException() {

        // Arrange
        PurchaseOrderDetailsDTO orderInfoResponseDTO = new PurchaseOrderDetailsDTO();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentMode("phonepe");
        ordersHeaderDTO.setLkCountry("IN");

        List<OrdersDTO> ordersDTOList = new ArrayList<>();
        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(201);
        ordersDTOList.add(ordersDTO);
        ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(202);
        ordersDTOList.add(ordersDTO);

        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DK02");
        uwOrderDTO.setItemId(201);
        uwOrderDTOList.add(uwOrderDTO);
        uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DKS02");
        uwOrderDTO.setItemId(202);
        uwOrderDTOList.add(uwOrderDTO);

        orderInfoResponseDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        orderInfoResponseDTO.setOrders(ordersDTOList);
        orderInfoResponseDTO.setUwOrders(uwOrderDTOList);

        List<String> physicalFacilities = List.of("DK02");
        when(returnUtil.getPhysicalFacilityCodes()).thenReturn(physicalFacilities);
        when(returnUtil.getPaymentMethod(any())).thenReturn("phonepe");
        when(restTemplate.exchange(
                anyString(),
                Mockito.eq(HttpMethod.POST),
                any(),
                Mockito.eq(RefundMethodResponse.class)
        )).thenThrow(RuntimeException.class);

        // Act
        String refundMethodAtRto = awaitedRtoService.getRefundMethodAtRto(orderInfoResponseDTO, uwOrderDTO);
        assertNull(refundMethodAtRto);
    }

    @Test
    void getRefundMethodAtRto_ApiGivesSuccessfulResponse() {

        // Arrange
        PurchaseOrderDetailsDTO orderInfoResponseDTO = new PurchaseOrderDetailsDTO();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentMode("phonepe");
        ordersHeaderDTO.setLkCountry("IN");

        List<OrdersDTO> ordersDTOList = new ArrayList<>();
        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(201);
        ordersDTOList.add(ordersDTO);
        ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(202);
        ordersDTOList.add(ordersDTO);

        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DK02");
        uwOrderDTO.setItemId(201);
        uwOrderDTOList.add(uwOrderDTO);
        uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DKS02");
        uwOrderDTO.setItemId(202);
        uwOrderDTOList.add(uwOrderDTO);

        orderInfoResponseDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        orderInfoResponseDTO.setOrders(ordersDTOList);
        orderInfoResponseDTO.setUwOrders(uwOrderDTOList);

        RefundMethodResponse refundMethodResponse = new RefundMethodResponse();
        refundMethodResponse.setSuccess(true);
        refundMethodResponse.setAutoRefundMethod("phonepe");
        ResponseEntity<RefundMethodResponse> responseEntity = ResponseEntity.ok().body(refundMethodResponse);

        List<String> physicalFacilities = List.of("DK02");
        when(returnUtil.getPhysicalFacilityCodes()).thenReturn(physicalFacilities);
        when(returnUtil.getPaymentMethod(any())).thenReturn("phonepe");
        when(restTemplate.exchange(
                anyString(),
                Mockito.eq(HttpMethod.POST),
                any(),
                Mockito.eq(RefundMethodResponse.class)
        )).thenReturn(responseEntity);

        // Act
        String refundMethodAtRto = awaitedRtoService.getRefundMethodAtRto(orderInfoResponseDTO, uwOrderDTO);
        assertNotNull(refundMethodAtRto);
        assertEquals("phonepe",refundMethodAtRto);
    }

    @Test
    void getRefundMethodAtRto_ApiGivesUnSuccessfulResponse() {

        // Arrange
        PurchaseOrderDetailsDTO orderInfoResponseDTO = new PurchaseOrderDetailsDTO();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentMode("phonepe");
        ordersHeaderDTO.setLkCountry("IN");
        ordersHeaderDTO.setIsExchangeOrder(true);

        List<OrdersDTO> ordersDTOList = new ArrayList<>();
        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(201);
        ordersDTOList.add(ordersDTO);
        ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(202);
        ordersDTOList.add(ordersDTO);

        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DK02");
        uwOrderDTO.setItemId(201);
        uwOrderDTOList.add(uwOrderDTO);
        uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DKS02");
        uwOrderDTO.setItemId(202);
        uwOrderDTOList.add(uwOrderDTO);

        orderInfoResponseDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        orderInfoResponseDTO.setOrders(ordersDTOList);
        orderInfoResponseDTO.setUwOrders(uwOrderDTOList);

        RefundMethodResponse refundMethodResponse = new RefundMethodResponse();
        refundMethodResponse.setSuccess(false);
        refundMethodResponse.setAutoRefundMethod("phonepe");
        ResponseEntity<RefundMethodResponse> responseEntity = ResponseEntity.ok().body(refundMethodResponse);

        List<String> physicalFacilities = List.of("DK02");
        when(returnUtil.getPhysicalFacilityCodes()).thenReturn(physicalFacilities);
        when(returnUtil.getPaymentMethod(any())).thenReturn("phonepe");
        when(restTemplate.exchange(
                anyString(),
                Mockito.eq(HttpMethod.POST),
                any(),
                Mockito.eq(RefundMethodResponse.class)
        )).thenReturn(responseEntity);

        // Act
        String refundMethodAtRto = awaitedRtoService.getRefundMethodAtRto(orderInfoResponseDTO, uwOrderDTO);
        assertNull(refundMethodAtRto);
    }

    private PurchaseOrderDetailsDTO setupPurchaseOrderDetailsDTO() {
        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentMode("Credit Card");
        ordersHeaderDTO.setLkCountry("IN");
        ordersHeaderDTO.setIsExchangeOrder(false);

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setProductDeliveryType("B2C");
        uwOrderDTO.setFacilityCode("FC123");
        uwOrderDTO.setItemId(1);

        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setItemId(1);

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        purchaseOrderDetailsDTO.setUwOrders(Collections.singletonList(uwOrderDTO));
        purchaseOrderDetailsDTO.setOrders(Collections.singletonList(ordersDTO));

        return purchaseOrderDetailsDTO;
    }

    @Test
    void getRefundMethodAtRto_WhenApiReturnsNull() {
        // Arrange
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = setupPurchaseOrderDetailsDTO();
        when(returnUtil.getPhysicalFacilityCodes()).thenReturn(Collections.singletonList("FC123"));
        when(returnUtil.getPaymentMethod(any(OrdersDTO.class))).thenReturn("Credit Card");

        // Mock the REST call to return a null body
        ResponseEntity<RefundMethodResponse> responseEntity = ResponseEntity.ok(null);
        when(restTemplate.exchange(any(String.class), any(HttpMethod.class), any(HttpEntity.class), any(Class.class)))
                .thenReturn(responseEntity);

        // Act
        String refundMethodAtRto = awaitedRtoService.getRefundMethodAtRto(purchaseOrderDetailsDTO, new UwOrderDTO());

        // Assert
        assertNull(refundMethodAtRto);
    }

    @Test
    void testGetRefundMethodAtRtoApiReturnsNull() {
        // Arrange
        PurchaseOrderDetailsDTO orderInfoResponseDTO = mock(PurchaseOrderDetailsDTO.class);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "Application");
        RefundMethodRequest refundMethodRequest = new RefundMethodRequest();

        when(orderInfoResponseDTO.getOrdersHeaderDTO()).thenReturn(mock(OrdersHeaderDTO.class));
        when(orderInfoResponseDTO.getOrdersHeaderDTO().getPaymentMode()).thenReturn("paymentMethod");
        when(orderInfoResponseDTO.getOrdersHeaderDTO().getLkCountry()).thenReturn("country");
        when(returnUtil.getPaymentMethod(any())).thenReturn("paymentMethod");

        // Simulate API call returning null body
        ResponseEntity<RefundMethodResponse> responseEntity = new ResponseEntity<>(HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(RefundMethodResponse.class)))
                .thenReturn(responseEntity);

        // Act
        String result = awaitedRtoService.getRefundMethodAtRto(orderInfoResponseDTO, new UwOrderDTO());

        // Assert
        assertNull(result, "Expected method to return null when API returns null body");
        verify(restTemplate, times(1)).exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(RefundMethodResponse.class));
    }

    @Test
    void getRefundMethodAtRto_WhenOrderOpsApiGivesSuccessfulResponse() {

        PurchaseOrderDetailsDTO orderInfoResponseDTO = new PurchaseOrderDetailsDTO();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentMode("phonepe");
        ordersHeaderDTO.setLkCountry("IN");

        List<OrdersDTO> ordersDTOList = new ArrayList<>();
        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(201);
        ordersDTOList.add(ordersDTO);
        ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(202);
        ordersDTOList.add(ordersDTO);

        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DK02");
        uwOrderDTO.setItemId(201);
        uwOrderDTOList.add(uwOrderDTO);
        uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DKS02");
        uwOrderDTO.setItemId(202);
        uwOrderDTOList.add(uwOrderDTO);

        orderInfoResponseDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        orderInfoResponseDTO.setOrders(ordersDTOList);
        orderInfoResponseDTO.setUwOrders(uwOrderDTOList);

        List<String> physicalFacilities = List.of("DK02");
        when(returnUtil.getPhysicalFacilityCodes()).thenReturn(physicalFacilities);
        when(returnUtil.getPaymentMethod(any())).thenReturn("phonepe");
        when(restTemplate.exchange(
                anyString(),
                Mockito.eq(HttpMethod.POST),
                any(),
                Mockito.eq(RefundMethodResponse.class)
        )).thenThrow(RuntimeException.class);
        ResponseEntity<PurchaseOrderDetailsDTO> responseEntity = ResponseEntity.ok().body(orderInfoResponseDTO);

        when(orderOpsFeignClient.getPurchaseOrderDetails(IdentifierType.ORDER_ID.name(), "123"))
                .thenReturn(responseEntity);

        // Act
        String refundMethodAtRto = awaitedRtoService.getRefundMethodAtRto(orderInfoResponseDTO, uwOrderDTO);
        assertNull(refundMethodAtRto);
    }

    @Test
    void markAwaitedRtoTest_ValidationFailed(){
        ReturnOrderRequestDTO returnOrderRequest = null;
        Map<String, Object> awaitedRto = awaitedRtoService.markAwaitedRto(returnOrderRequest);
        assertNotNull(awaitedRto);
        assertNotNull(awaitedRto.get("success"));
        assertEquals(false, awaitedRto.get("success"));

        returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setIncrementId(123);
        when(returnOrderRepository.findTop1ByIncrementIdAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(returnOrderRequest.getIncrementId(), returnOrderRequest.getReferenceOrderCode(), "awaited_rto"))
                .thenReturn(null);
        awaitedRto = awaitedRtoService.markAwaitedRto(returnOrderRequest);
        assertNotNull(awaitedRto);
        assertNotNull(awaitedRto.get("success"));
        assertEquals(false, awaitedRto.get("success"));
    }

    @Test
    void markAwaitedRtoTest_ValidationSuccess_OOFails(){
        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setReferenceOrderCode("unicom_123");
        returnOrderRequest.setIncrementId(123);

        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setId(101);

        ResponseEntity<PurchaseOrderDetailsDTO> ordersDetails = ResponseEntity.internalServerError().body(null);


        when(returnOrderRepository.findTop1ByIncrementIdAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(returnOrderRequest.getIncrementId(), returnOrderRequest.getReferenceOrderCode(), "awaited_rto"))
                .thenReturn(null);
        when(returnRequestRepository.save(any())).thenReturn(returnRequest);
        doNothing().when(returnEventService).createReturnEvent(anyInt(), anyInt(), anyString(), anyString());
        when(orderOpsFeignClient.getPurchaseOrderDetails(anyString(), anyString()))
                .thenReturn(ordersDetails);
        Map<String, Object> awaitedRto = awaitedRtoService.markAwaitedRto(returnOrderRequest);
        assertNotNull(awaitedRto);
        assertNotNull(awaitedRto.get("success"));
        assertEquals(false, awaitedRto.get("success"));
    }

    @Test
    void markAwaitedRtoTest_ValidationSuccess_OOSuccessResponse(){
        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setReferenceOrderCode("unicom_123");
        returnOrderRequest.setIncrementId(123);

        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setId(101);

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setUnicomOrderCode("unicom_123");
        uwOrderDTO.setUwItemId(151);
        uwOrderDTOList.add(uwOrderDTO);
        purchaseOrderDetailsDTO.setUwOrders(uwOrderDTOList);

        ResponseEntity<PurchaseOrderDetailsDTO> ordersDetails = ResponseEntity.ok().body(purchaseOrderDetailsDTO);

        ReturnDetailItem returnOrderItem = new ReturnDetailItem();
        returnOrderItem.setId(401);

        ReturnGroup returnGroup = new ReturnGroup();
        returnGroup.setId(1L);

        when(returnOrderRepository.findTop1ByIncrementIdAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(returnOrderRequest.getIncrementId(), returnOrderRequest.getReferenceOrderCode(), "awaited_rto"))
                .thenReturn(null);
        when(returnRequestRepository.save(any())).thenReturn(returnRequest);
        doNothing().when(returnEventService).createReturnEvent(anyInt(), anyInt(), anyString(), anyString());
        when(orderOpsFeignClient.getPurchaseOrderDetails(anyString(), anyString()))
                .thenReturn(ordersDetails);
        when(returnOrderItemService.findByUwItemId(anyInt())).thenReturn(new ReturnDetailItem());
        when(returnOrderActionService.createAwaitedRtoReturnOrder(any(), any(), anyString(), anyString(), any()))
                .thenReturn(null);
        when(returnGroupRepository.save(any())).thenReturn(returnGroup);
        Map<String, Object> awaitedRto = awaitedRtoService.markAwaitedRto(returnOrderRequest);
        assertNotNull(awaitedRto);
        assertNotNull(awaitedRto.get("success"));
        assertEquals(true, awaitedRto.get("success"));
    }

    @Test
    void markAwaitedRtoTest_ValidationSuccess_OOSuccessResponse_ReturnOrder_Success(){
        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setReferenceOrderCode("unicom_123");
        returnOrderRequest.setIncrementId(123);

        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setId(101);

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setUnicomOrderCode("unicom_123");
        uwOrderDTO.setUwItemId(151);
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setItemId(201);
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTO.setFacilityCode("DK02");
        uwOrderDTOList.add(uwOrderDTO);
        uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setUnicomOrderCode("unicom_123-1");
        uwOrderDTO.setUwItemId(152);
        uwOrderDTO.setIncrementId(123);
        uwOrderDTO.setItemId(202);
        uwOrderDTO.setFacilityCode("DKS02");
        uwOrderDTO.setProductDeliveryType("B2B");
        uwOrderDTOList.add(uwOrderDTO);

        List<OrdersDTO> ordersDTOList = new ArrayList<>();
        OrdersDTO ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(201);
        ordersDTOList.add(ordersDTO);
        ordersDTO = new OrdersDTO();
        ordersDTO.setIncrementId(123);
        ordersDTO.setItemId(202);
        ordersDTOList.add(ordersDTO);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setIncrementId(123);
        ordersHeaderDTO.setPaymentMode("phonepe");
        ordersHeaderDTO.setLkCountry("IN");

        purchaseOrderDetailsDTO.setUwOrders(uwOrderDTOList);
        purchaseOrderDetailsDTO.setOrders(ordersDTOList);
        purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);

        ResponseEntity<PurchaseOrderDetailsDTO> ordersDetails = ResponseEntity.ok().body(purchaseOrderDetailsDTO);

        ReturnDetailItem returnOrderItem = new ReturnDetailItem();
        returnOrderItem.setId(401);
        returnOrderItem.setUwItemId(151);

        CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse = new CreateUpdateReturnOrderResponseDTO();
        createUpdateReturnOrderResponse.setReturnId(161);

        when(returnOrderRepository.findTop1ByIncrementIdAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(returnOrderRequest.getIncrementId(), returnOrderRequest.getReferenceOrderCode(), "awaited_rto"))
                .thenReturn(null);
        when(returnRequestRepository.save(any())).thenReturn(returnRequest);
        doNothing().when(returnEventService).createReturnEvent(anyInt(), anyInt(), anyString(), anyString());
        when(orderOpsFeignClient.getPurchaseOrderDetails(anyString(), anyString()))
                .thenReturn(ordersDetails);
        when(returnOrderItemService.findByUwItemId(anyInt())).thenReturn(new ReturnDetailItem());
        when(returnOrderActionService.createAwaitedRtoReturnOrder(any(), any(), anyString(), anyString(), any()))
                .thenReturn(createUpdateReturnOrderResponse);
        when(returnOrderItemService.createReturnItemForAwaitedRto(anyString(), any(), anyInt(), anyString()))
                .thenReturn(returnOrderItem);
        List<String> physicalFacilities = List.of("DK02");
        when(returnUtil.getPhysicalFacilityCodes()).thenReturn(physicalFacilities);
        when(returnUtil.getPaymentMethod(any())).thenReturn("phonepe");
        ReturnGroup returnGroup = new ReturnGroup();
        returnGroup.setId(1L);
        when(returnGroupRepository.save(any())).thenReturn(returnGroup);
        when(restTemplate.exchange(
                anyString(),
                Mockito.eq(HttpMethod.POST),
                any(),
                Mockito.eq(RefundMethodResponse.class)
        )).thenThrow(RuntimeException.class);
        Map<String, Object> awaitedRto = awaitedRtoService.markAwaitedRto(returnOrderRequest);
        assertNotNull(awaitedRto);
        assertNotNull(awaitedRto.get("success"));
        assertEquals(true, awaitedRto.get("success"));
    }

}

