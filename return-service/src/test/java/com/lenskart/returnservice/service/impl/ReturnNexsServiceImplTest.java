package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.return_refund_rules.model.ReturnItemRequest;
import com.lenskart.return_refund_rules.model.ReturnOrderRequest;
import com.lenskart.returncommon.exception.ReturnRequestFailException;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.lenskart.returncommon.model.response.NexsReturnReponseData;
import com.lenskart.returncommon.model.response.NexsReturnResponse;
import com.lenskart.returncommon.model.response.NexsReturnResponseMeta;
import org.aspectj.lang.annotation.Before;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

@TestPropertySource(properties = "wms.base.url=https://mocked.url/api")
@ContextConfiguration(classes = {ReturnNexsServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ReturnNexsServiceImplTest {
    @Autowired
    private ReturnNexsServiceImpl returnNexsServiceImpl;
    @MockBean
    private RestTemplate restTemplate;
    @BeforeEach
    public void setUp() {
        returnNexsServiceImpl.restTemplate = restTemplate;
    }


    /**
     * Method under test:
     * {@link ReturnNexsServiceImpl#createReversePickupNexs(List, String, Integer, List)}
     */
    @Test
    void testCreateReversePickupNexs() throws ReturnRequestFailException {
        // Arrange
        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();

        // Act and Assert
        assertNull(
                returnNexsServiceImpl.createReversePickupNexs(returnItemRequestList, "Sale Order Code", 1, new ArrayList<>()));
    }

    /**
     * Method under test:
     * {@link ReturnNexsServiceImpl#createReversePickupNexs(List, String, Integer, List)}
     */
    @Test
    void testCreateReversePickupNexs2() throws ReturnRequestFailException {
        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("Qc Status");
        returnItemRequest.setReasonDetail("Just cause");

        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();
        returnItemRequestList.add(returnItemRequest);
        assertThrows(ReturnRequestFailException.class, () -> returnNexsServiceImpl
                .createReversePickupNexs(returnItemRequestList, "Sale Order Code", 1, new ArrayList<>()));
    }

    /**
     * Method under test:
     * {@link ReturnNexsServiceImpl#createReversePickupNexs(List, String, Integer, List)}
     */
    @Test
    void testCreateReversePickupNexs3() throws ReturnRequestFailException {
        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("Qc Status");
        returnItemRequest.setReasonDetail("Just cause");

        ReturnItemRequestDTO returnItemRequest2 = new ReturnItemRequestDTO();
        returnItemRequest2.setItemId(2);
        returnItemRequest2.setQcFailReason("RETURNED");
        returnItemRequest2.setQcStatus("[createReversePickupNexs] for list of itemsRequestLists : {}");
        returnItemRequest2.setReasonDetail("RETURNED");

        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();
        returnItemRequestList.add(returnItemRequest2);
        returnItemRequestList.add(returnItemRequest);
        assertThrows(ReturnRequestFailException.class, () -> returnNexsServiceImpl
                .createReversePickupNexs(returnItemRequestList, "Sale Order Code", 1, new ArrayList<>()));
    }

    /**
     * Method under test:
     * {@link ReturnNexsServiceImpl#createReversePickupNexs(List, String, Integer, List)}
     */
    @Test
    void testCreateReversePickupNexs4() throws ReturnRequestFailException {
        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("Pass");
        returnItemRequest.setReasonDetail("Just cause");

        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();
        returnItemRequestList.add(returnItemRequest);
        assertThrows(ReturnRequestFailException.class, () -> returnNexsServiceImpl
                .createReversePickupNexs(returnItemRequestList, "Sale Order Code", 1, new ArrayList<>()));
    }

    /**
     * Method under test:
     * {@link ReturnNexsServiceImpl#createReversePickupNexs(List, String, Integer, List)}
     */
    @Test
    void testCreateReversePickupNexs5() throws ReturnRequestFailException {
        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        ArrayList<UwOrderDTO> uwOrderDTOS = new ArrayList<>();
        uwOrderDTOS.add(uwOrderDTO);
        assertNull(returnNexsServiceImpl.createReversePickupNexs(returnItemRequestList, "Sale Order Code", 1, uwOrderDTOS));
    }

    /**
     * Method under test:
     * {@link ReturnNexsServiceImpl#createReversePickupNexs(List, String, Integer, List)}
     */
    @Test
    void testCreateReversePickupNexsWhenNexsReturnRequestPrepareFromOnlyReturnRequest() throws ReturnRequestFailException {
        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        UwOrderDTO uwOrderDTO2 = new UwOrderDTO();
        uwOrderDTO2.setB2bRefrenceItemId(2);
//        uwOrderDTO2.setBarcode("42");
//        uwOrderDTO2.setBifocalDivision("42");
        uwOrderDTO2.setBrand("42");
        uwOrderDTO2.setClassification("42");
        uwOrderDTO2.setClassificationName("42");
        uwOrderDTO2.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setEta("42");
//        uwOrderDTO2
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO2
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO2.setFacilityCode("42");
//        uwOrderDTO2.setFitterName("42");
        uwOrderDTO2.setFitting("42");
//        uwOrderDTO2.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO2.setIncrementId(2);
//        uwOrderDTO2.setInventoryCount(1);
//        uwOrderDTO2.setIsFulfillable("42");
        uwOrderDTO2.setIsLocalFittingRequired(false);
//        uwOrderDTO2.setIsOMAFileUploaded(0);
        uwOrderDTO2.setItemId(2);
//        uwOrderDTO2
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setJitFlag("42");
//        uwOrderDTO2.setJitPoStatus("42");
//        uwOrderDTO2.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setLastShipmentState("42");
//        uwOrderDTO2.setLastShipmentStatus("42");
        uwOrderDTO2.setLensPackage("Lens Package");
//        uwOrderDTO2.setLenskartDiscount(0.5d);
//        uwOrderDTO2.setLenskartPlusDiscount(0.5d);
        uwOrderDTO2.setNavChannel("42");
//        uwOrderDTO2.setNotStockOutReason(0);
//        uwOrderDTO2.setOrder_type("42");
        uwOrderDTO2.setParentUw(0);
//        uwOrderDTO2.setPickedByPicker(false);
//        uwOrderDTO2.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setPoId("Po Id");
//        uwOrderDTO2.setPolicyNo("42");
//        uwOrderDTO2.setPolicyStatus("42");
//        uwOrderDTO2.setPriority(0);
        uwOrderDTO2.setProductDeliveryType("42");
        uwOrderDTO2.setProductId(2);
        uwOrderDTO2.setProductSku("42");
        uwOrderDTO2.setProductValue("Product Value");
//        uwOrderDTO2.setQcFailCnt(0);
//        uwOrderDTO2.setQcFailReason("Qc Fail Reason");
//        uwOrderDTO2.setQcFailShelf("42");
//        uwOrderDTO2.setQcHold(false);
//        uwOrderDTO2.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setQccheck(0);
//        uwOrderDTO2.setReturn_(0);
        uwOrderDTO2.setShipToStoreRequired(false);
//        uwOrderDTO2.setShipment(0);
        uwOrderDTO2.setShipmentState("42");
        uwOrderDTO2.setShipmentStatus("42");
//        uwOrderDTO2.setShippingPackageId("java.text");
//        uwOrderDTO2.setSplOrderFlag("42");
//        uwOrderDTO2.setStatus("42");
//        uwOrderDTO2.setStockout(0);
//        uwOrderDTO2.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setStoreInventory(0);
//        uwOrderDTO2.setSwitchFacilityStatus(0);
        uwOrderDTO2.setTotalPrice(0.5d);
        uwOrderDTO2.setUnicomOrderCode("42");
//        uwOrderDTO2.setUnicomPriority(0);
//        uwOrderDTO2.setUnicomShipmentStatus("42");
//        uwOrderDTO2.setUnicomSynStatus("42");
        uwOrderDTO2.setUwItemId(2);
//        uwOrderDTO2.setVendor("42");
        uwOrderDTO2.setVsmStockout(0);
//        uwOrderDTO2.setWarehouseNotPresent(0);

        ArrayList<UwOrderDTO> uwOrderDTOS = new ArrayList<>();
        uwOrderDTOS.add(uwOrderDTO2);
        uwOrderDTOS.add(uwOrderDTO);
        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("success");
        returnItemRequest.setReasonDetail("Just cause");
        returnItemRequestList.add(returnItemRequest);
        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setGroupId(1L);
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setRaiseRPUatNexs(false);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setSource("Source");
        returnRequest.setItems(returnItemRequestList);
        NexsReturnResponse nexsReturnResponse = new NexsReturnResponse();
        NexsReturnResponseMeta nexsReturnResponseMeta = new NexsReturnResponseMeta();
        nexsReturnResponseMeta.setCode("ALREADY_EXISTS");
        NexsReturnReponseData nexsReturnReponseData = new NexsReturnReponseData();
        nexsReturnResponse.setMeta(nexsReturnResponseMeta);
        nexsReturnResponse.setData(nexsReturnReponseData);
        ResponseEntity<NexsReturnResponse> responseEntity = new ResponseEntity<>(nexsReturnResponse, HttpStatus.OK);
        when(restTemplate.exchange(any(RequestEntity.class), Mockito.eq(NexsReturnResponse.class)))
                .thenReturn(responseEntity);
        assertNotNull(returnNexsServiceImpl.createReversePickupNexs(returnItemRequestList, returnRequest.getReferenceOrderCode(), 1, uwOrderDTOS));
    }

    @Test
    void testCreateReversePickupNexs6WhenNexsReturnRequestPrepareFRomUwOrder() throws ReturnRequestFailException {
        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Reference Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        UwOrderDTO uwOrderDTO2 = new UwOrderDTO();
        uwOrderDTO2.setB2bRefrenceItemId(2);
//        uwOrderDTO2.setBarcode("42");
//        uwOrderDTO2.setBifocalDivision("42");
        uwOrderDTO2.setBrand("42");
        uwOrderDTO2.setClassification("42");
        uwOrderDTO2.setClassificationName("42");
        uwOrderDTO2.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setEta("42");
//        uwOrderDTO2
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO2
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO2.setFacilityCode("42");
//        uwOrderDTO2.setFitterName("42");
        uwOrderDTO2.setFitting("42");
//        uwOrderDTO2.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO2.setIncrementId(2);
//        uwOrderDTO2.setInventoryCount(1);
//        uwOrderDTO2.setIsFulfillable("42");
        uwOrderDTO2.setIsLocalFittingRequired(false);
//        uwOrderDTO2.setIsOMAFileUploaded(0);
        uwOrderDTO2.setItemId(2);
//        uwOrderDTO2
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setJitFlag("42");
//        uwOrderDTO2.setJitPoStatus("42");
//        uwOrderDTO2.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setLastShipmentState("42");
//        uwOrderDTO2.setLastShipmentStatus("42");
        uwOrderDTO2.setLensPackage("Lens Package");
//        uwOrderDTO2.setLenskartDiscount(0.5d);
//        uwOrderDTO2.setLenskartPlusDiscount(0.5d);
        uwOrderDTO2.setNavChannel("42");
//        uwOrderDTO2.setNotStockOutReason(0);
//        uwOrderDTO2.setOrder_type("42");
        uwOrderDTO2.setParentUw(0);
//        uwOrderDTO2.setPickedByPicker(false);
//        uwOrderDTO2.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setPoId("Po Id");
//        uwOrderDTO2.setPolicyNo("42");
//        uwOrderDTO2.setPolicyStatus("42");
//        uwOrderDTO2.setPriority(0);
        uwOrderDTO2.setProductDeliveryType("42");
        uwOrderDTO2.setProductId(2);
        uwOrderDTO2.setProductSku("42");
        uwOrderDTO2.setProductValue("Product Value");
//        uwOrderDTO2.setQcFailCnt(0);
//        uwOrderDTO2.setQcFailReason("Qc Fail Reason");
//        uwOrderDTO2.setQcFailShelf("42");
//        uwOrderDTO2.setQcHold(false);
//        uwOrderDTO2.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setQccheck(0);
//        uwOrderDTO2.setReturn_(0);
        uwOrderDTO2.setShipToStoreRequired(false);
//        uwOrderDTO2.setShipment(0);
        uwOrderDTO2.setShipmentState("42");
        uwOrderDTO2.setShipmentStatus("42");
//        uwOrderDTO2.setShippingPackageId("java.text");
//        uwOrderDTO2.setSplOrderFlag("42");
//        uwOrderDTO2.setStatus("42");
//        uwOrderDTO2.setStockout(0);
//        uwOrderDTO2.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setStoreInventory(0);
//        uwOrderDTO2.setSwitchFacilityStatus(0);
        uwOrderDTO2.setTotalPrice(0.5d);
        uwOrderDTO2.setUnicomOrderCode("42");
//        uwOrderDTO2.setUnicomPriority(0);
//        uwOrderDTO2.setUnicomShipmentStatus("42");
//        uwOrderDTO2.setUnicomSynStatus("42");
        uwOrderDTO2.setUwItemId(2);
//        uwOrderDTO2.setVendor("42");
        uwOrderDTO2.setVsmStockout(0);
//        uwOrderDTO2.setWarehouseNotPresent(0);

        ArrayList<UwOrderDTO> uwOrderDTOS = new ArrayList<>();
        uwOrderDTOS.add(uwOrderDTO2);
        uwOrderDTOS.add(uwOrderDTO);
        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("success");
        returnItemRequest.setReasonDetail("Just cause");
        returnItemRequestList.add(returnItemRequest);
        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setGroupId(1L);
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setRaiseRPUatNexs(false);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setSource("Source");
        returnRequest.setItems(returnItemRequestList);
        NexsReturnResponse nexsReturnResponse = new NexsReturnResponse();
        NexsReturnResponseMeta nexsReturnResponseMeta = new NexsReturnResponseMeta();
        nexsReturnResponseMeta.setCode("ALREADY_EXISTS");
        NexsReturnReponseData nexsReturnReponseData = new NexsReturnReponseData();
        nexsReturnResponse.setMeta(nexsReturnResponseMeta);
        nexsReturnResponse.setData(nexsReturnReponseData);
        ResponseEntity<NexsReturnResponse> responseEntity = new ResponseEntity<>(nexsReturnResponse, HttpStatus.OK);
        when(restTemplate.exchange(any(RequestEntity.class), Mockito.eq(NexsReturnResponse.class)))
                .thenReturn(responseEntity);
        assertNotNull(returnNexsServiceImpl.createReversePickupNexs(returnItemRequestList, returnRequest.getReferenceOrderCode(), 1, uwOrderDTOS));
    }
}
