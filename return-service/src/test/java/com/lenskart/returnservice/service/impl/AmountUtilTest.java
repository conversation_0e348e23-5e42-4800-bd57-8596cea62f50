package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {AmountUtil.class})
@ExtendWith(SpringExtension.class)
class AmountUtilTest {
    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private AmountUtil amountUtil;

    @Test
    void testGetAmountThrowsException() {
        PurchaseOrderDetailsDTO orderInfoResponseDTO = new PurchaseOrderDetailsDTO();
        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setUwItemId(123);
        uwOrderDTO.setIncrementId(101);
        uwOrderDTOList.add(uwOrderDTO);
        orderInfoResponseDTO.setUwOrders(uwOrderDTOList);
        when(orderOpsFeignClient.getItemAmount(any())).thenThrow(RuntimeException.class);
        ItemWiseAmountDTO itemAmount = amountUtil.getItemAmount(false, uwOrderDTO.getIncrementId(), List.of(123));
        assertNull(itemAmount);
    }

    @Test
    void testGetAmountWhenOrderInfoIsNull() {
        PurchaseOrderDetailsDTO orderInfoResponseDTO = null;
        when(orderOpsFeignClient.getItemAmount(any())).thenThrow(RuntimeException.class);
        ItemWiseAmountDTO itemAmount = amountUtil.getItemAmount(false, 123, List.of(123));
        assertNull(itemAmount);
    }

    @Test
    void testGetAmountWhenApiGivesSuccessResponse() {
        PurchaseOrderDetailsDTO orderInfoResponseDTO = new PurchaseOrderDetailsDTO();
        List<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setUwItemId(123);
        uwOrderDTO.setIncrementId(101);
        uwOrderDTOList.add(uwOrderDTO);
        orderInfoResponseDTO.setUwOrders(uwOrderDTOList);
        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        ResponseEntity<ItemWiseAmountDTO> itemWiseAmountDTOResponseEntity = ResponseEntity.ok().body(itemWiseAmountDTO);
        when(orderOpsFeignClient.getItemAmount(any())).thenReturn(itemWiseAmountDTOResponseEntity);
        ItemWiseAmountDTO itemAmount = amountUtil.getItemAmount(false, uwOrderDTO.getIncrementId(), List.of(123));
        assertNotNull(itemAmount);
    }
}

