package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.returncommon.model.dto.ReturnOrderItemDTO;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.response.KafkaProducerResponse;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.repository.ReturnDetailItemRepository;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {D365FinanceServiceImpl.class})
@ExtendWith(SpringExtension.class)
class D365FinanceServiceImplTest {
    @Autowired
    private D365FinanceServiceImpl d365FinanceServiceImpl;

    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private IReturnOrderItemService returnOrderItemService;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private ISystemPreferenceService systemPreferenceService;

    @MockBean
    private IReturnEventService returnEventService;

    @MockBean
    private ReturnDetailRepository returnOrderRepository;

    @MockBean
    private ReturnDetailItemRepository returnOrderItemRepository;

    @MockBean
    private IReturnOrderActionService returnOrderActionService;

    /**
     * Method under test: {@link D365FinanceServiceImpl#sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest)}
     */
    @Test
    void testSendDataToFinanceConsumerForAwbReturn() {

        // Arrange
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenThrow(new RuntimeException());

        ReturnOrderItemDTO returnOrderItemDTO = new ReturnOrderItemDTO();
        returnOrderItemDTO.setId(1);
        returnOrderItemDTO.setReturnId(1);
        returnOrderItemDTO.setUwItemId(1);

        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
        returnCreateRequest.setReturnOrderItem(returnOrderItemDTO);
        returnCreateRequest.setUwItemId(1);

        // Act
        d365FinanceServiceImpl.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
    }

    /**
     * Method under test: {@link D365FinanceServiceImpl#sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest)}
     */
    @Test
    void testSendDataToFinanceConsumerWhenKafkaResponseIsNotSuccess() {

        ResponseEntity<KafkaProducerResponse> kafkaResponse = ResponseEntity.ok().body(null);
        // Arrange
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenReturn(kafkaResponse);

        ReturnOrderItemDTO returnOrderItemDTO = new ReturnOrderItemDTO();
        returnOrderItemDTO.setId(1);
        returnOrderItemDTO.setReturnId(1);
        returnOrderItemDTO.setUwItemId(1);

        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
        returnCreateRequest.setReturnOrderItem(returnOrderItemDTO);
        returnCreateRequest.setUwItemId(1);

        // Act
        Boolean dataSent = d365FinanceServiceImpl.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
        assertFalse(dataSent);
    }

    /**
     * Method under test: {@link D365FinanceServiceImpl#sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest)}
     */
    @Test
    void testSendDataToFinanceConsumerWhenKafkaResponseIsSuccess() {

        KafkaProducerResponse kafkaProducerResponse = new KafkaProducerResponse();
        kafkaProducerResponse.setSuccess(true);
        ResponseEntity<KafkaProducerResponse> kafkaResponse = ResponseEntity.ok().body(kafkaProducerResponse);
        // Arrange
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenReturn(kafkaResponse);

        ReturnOrderItemDTO returnOrderItemDTO = new ReturnOrderItemDTO();
        returnOrderItemDTO.setId(1);
        returnOrderItemDTO.setReturnId(1);
        returnOrderItemDTO.setUwItemId(1);

        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
        returnCreateRequest.setReturnOrderItem(returnOrderItemDTO);
        returnCreateRequest.setUwItemId(1);

        // Act
        Boolean dataSent = d365FinanceServiceImpl.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
        assertTrue(dataSent);
    }

    /**
     * Method under test: {@link D365FinanceServiceImpl#sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest)}
     */
    @Test
    void testSendDataToFinanceConsumerWhenInputIsNull() {

        ReturnCreateRequest returnCreateRequest = null;

        // Act
        Boolean dataSent = d365FinanceServiceImpl.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
        assertFalse(dataSent);
    }

    /**
     * Method under test: {@link D365FinanceServiceImpl#sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest)}
     */
    @Test
    void testSendDataToFinanceConsumerForAwbReturn2() {
        // Arrange
        KafkaProducerResponse kafkaProducerResponse = new KafkaProducerResponse();
        kafkaProducerResponse.setError("An error occurred");
        kafkaProducerResponse.setSuccess(true);
        ResponseEntity<KafkaProducerResponse> responseEntity = (ResponseEntity<KafkaProducerResponse>) mock(
                ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(kafkaProducerResponse);
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenReturn(responseEntity);

        ReturnOrderItemDTO returnOrderItemDTO = new ReturnOrderItemDTO();
        returnOrderItemDTO.setId(1);
        returnOrderItemDTO.setItemId(1);
        returnOrderItemDTO.setProductId(1L);
        returnOrderItemDTO.setQcStatus("Qc Status");
        returnOrderItemDTO.setReturnCreateDatetime(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrderItemDTO.setReturnId(1);
        returnOrderItemDTO.setUwItemId(1);

        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
        returnCreateRequest.setReturnOrderItem(returnOrderItemDTO);
        returnCreateRequest.setUwItemId(1);

        // Act and Assert
        assertTrue(d365FinanceServiceImpl.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest));
        verify(iKafkaService).pushToKafka((String) any(), (String) any(), (Object) any());
        verify(responseEntity, atLeast(1)).getBody();
    }

    /**
     * Method under test: {@link D365FinanceServiceImpl#sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest)}
     */
    @Test
    void testSendDataToFinanceConsumerForAwbReturn3() {
        // Arrange
        KafkaProducerResponse kafkaProducerResponse = new KafkaProducerResponse();
        kafkaProducerResponse.setError("An error occurred");
        kafkaProducerResponse.setSuccess(true);
        ResponseEntity<KafkaProducerResponse> responseEntity = (ResponseEntity<KafkaProducerResponse>) mock(
                ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(kafkaProducerResponse);
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenReturn(responseEntity);

        ReturnOrderItemDTO returnOrderItemDTO = new ReturnOrderItemDTO();
        returnOrderItemDTO.setId(1);
        returnOrderItemDTO.setItemId(1);
        returnOrderItemDTO.setProductId(1L);
        returnOrderItemDTO.setQcStatus("Qc Status");
        returnOrderItemDTO.setReturnCreateDatetime(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrderItemDTO.setReturnId(1);
        returnOrderItemDTO.setUwItemId(1);

        ReturnOrderItemDTO returnOrderItemDTO1 = new ReturnOrderItemDTO();
        returnOrderItemDTO1.setId(1);
        returnOrderItemDTO1.setItemId(1);
        returnOrderItemDTO1.setProductId(1L);
        returnOrderItemDTO1.setQcStatus("Qc Status");
        returnOrderItemDTO1.setReturnCreateDatetime(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrderItemDTO1.setReturnId(1);
        returnOrderItemDTO1.setUwItemId(1);
        ReturnCreateRequest returnCreateRequest = mock(ReturnCreateRequest.class);
        when(returnCreateRequest.getReturnOrderItem()).thenReturn(returnOrderItemDTO1);
        when(returnCreateRequest.getUwItemId()).thenReturn(1);
        doNothing().when(returnCreateRequest).setReturnOrderItem((ReturnOrderItemDTO) any());
        doNothing().when(returnCreateRequest).setUwItemId((Integer) any());
        returnCreateRequest.setReturnOrderItem(returnOrderItemDTO);
        returnCreateRequest.setUwItemId(1);

        // Act and Assert
        assertTrue(d365FinanceServiceImpl.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest));
        verify(iKafkaService).pushToKafka((String) any(), (String) any(), (Object) any());
        verify(responseEntity, atLeast(1)).getBody();
//        verify(returnCreateRequest).getReturnOrderItem();
        verify(returnCreateRequest, atLeast(1)).getUwItemId();
        verify(returnCreateRequest).setReturnOrderItem((ReturnOrderItemDTO) any());
        verify(returnCreateRequest).setUwItemId((Integer) any());
    }

    /**
     * Method under test: {@link D365FinanceServiceImpl#createReturnEInvoice(Integer, Integer, String)}
     */
    @Test
    void testCreateReturnEInvoice() {
        // Arrange
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenReturn(null);

        ReturnDetail returnOrder = new ReturnDetail();
        returnOrder.setFacilityCode("Facility Code");
        returnOrder.setGroupId(1L);
        returnOrder.setId(1);
        returnOrder.setIncrementId(1);
        returnOrder.setIsInsurance(true);
        returnOrder.setIsQcAtDoorstep(1);
        returnOrder.setReceivingFlag("Receiving Flag");
        returnOrder.setRequestId(1);
        returnOrder.setReturnCreateDatetime(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrder.setReturnMethod("Return Method");
        returnOrder.setReturnType("Return Type");
        returnOrder.setSource("Source");
        returnOrder.setUnicomOrderCode("Unicom Order Code");
        Optional<ReturnDetail> ofResult = Optional.of(returnOrder);
        when(returnOrderRepository.findById((Integer) any())).thenReturn(ofResult);

        // Act and Assert
        assertFalse(d365FinanceServiceImpl.createReturnEInvoice(1, 1, null));
//        verify(iKafkaService).pushToKafka((String) any(), (String) any(), (Object) any());
//        verify(returnOrderRepository).findById((Integer) any());
    }

    /**
     * Method under test: {@link D365FinanceServiceImpl#createReturnEInvoice(Integer, Integer, String)}
     */
    @Test
    void testCreateReturnEInvoice_1() {
        // Arrange
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenReturn(null);

        ReturnDetail returnOrder = new ReturnDetail();
        returnOrder.setFacilityCode("Facility Code");
        returnOrder.setGroupId(1L);
        returnOrder.setId(1);
        returnOrder.setIncrementId(1);
        returnOrder.setIsInsurance(true);
        returnOrder.setIsQcAtDoorstep(1);
        returnOrder.setReceivingFlag("Receiving Flag");
        returnOrder.setRequestId(1);
        returnOrder.setReturnCreateDatetime(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnOrder.setReturnMethod("Return Method");
        returnOrder.setReturnType("Return Type");
        returnOrder.setSource("Source");
        returnOrder.setUnicomOrderCode("Unicom Order Code");

        ReturnDetailItem returnOrderItem = new ReturnDetailItem();
        returnOrderItem.setReturnId(1);
        returnOrderItem.setUwItemId(123);

        Optional<ReturnDetail> ofResult = Optional.of(returnOrder);
        when(returnOrderRepository.findById((Integer) any())).thenReturn(ofResult);
        when(returnOrderItemRepository.findTopByReturnId(anyInt())).thenReturn(returnOrderItem);

        // Act and Assert
        assertFalse(d365FinanceServiceImpl.createReturnEInvoice(1, 1, null));
//        verify(iKafkaService).pushToKafka((String) any(), (String) any(), (Object) any());

        ofResult = Optional.ofNullable(null);
        when(returnOrderRepository.findById((Integer) any())).thenReturn(ofResult);

        // Act and Assert
        assertFalse(d365FinanceServiceImpl.createReturnEInvoice(1, 1, null));

    }
}

