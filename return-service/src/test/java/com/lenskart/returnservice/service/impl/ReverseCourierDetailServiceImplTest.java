package com.lenskart.returnservice.service.impl;

import com.lenskart.ordermetadata.dto.request.PickupAddressDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.dto.PickupAddress;
import com.lenskart.returncommon.model.dto.ReturnOrderItemDTO;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.model.response.KafkaProducerResponse;
import com.lenskart.returnrepository.entity.ReverseCourierMapping;
import com.lenskart.returnrepository.entity.ReversePickupPincode;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IDoorStepQcService;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IOrderUtilsService;
import com.lenskart.returnservice.service.IReturnReasonService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {ReverseCourierDetailServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ReverseCourierDetailServiceImplTest {
    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;

    @MockBean
    private ReverseCourierMappingRepository reverseCourierMappingRepository;

    @MockBean
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @MockBean
    private ReturnRequestRepository returnRequestRepository;

    @MockBean
    private ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @MockBean
    private ReturnDetailRepository returnDetailRepository;

    @MockBean
    private PrimaryReturnReasonsRepository primaryReturnReasonsRepository;

    @MockBean
    private IReturnReasonService returnReasonService;

    @MockBean
    ReversePickupPincodeRepository reversePickupPincodeRepository;

    @MockBean
    private IOrderUtilsService orderUtilsService;

    @MockBean
    private DateUtil dateUtil;

    @MockBean
    private IDoorStepQcService doorStepQcService;

    @MockBean
    RedisTemplate<Object,Object> redisTemplate;

    @MockBean
    RedisTemplate<String,Object> redisTemplate1;

    @MockBean
    private QcAndWarrantyRulesRepository qcAndWarrantyRulesRepository;

    @MockBean
    private IKafkaService kafkaService;

    @Autowired
    private ReverseCourierDetailServiceImpl reverseCourierDetailService;

    /**
     * Method under test: {@link D365FinanceServiceImpl#sendDataToFinanceConsumerForAwbReturn(ReturnCreateRequest)}
     */
    @Test
    void testReassignCourier() {

        ReturnCreationRequestDTO returnCreateRequest = new ReturnCreationRequestDTO();
        PickupAddressDTO pickupAddress = new PickupAddressDTO();
        pickupAddress.setPincode(123);
        returnCreateRequest.setReversePickupAddress(pickupAddress);
        returnCreateRequest.setNewCourier("Bluedart");

        ReversePickupPincode reversePickupPincode = new ReversePickupPincode();
        reversePickupPincode.setIsQcAtDoorstep(false);
        reversePickupPincode.setTat(10);

        ReverseCourierDetail reverseCourierDetail = new ReverseCourierDetail();
        reverseCourierDetail.setCourierAssignmentType(QCType.QC_REQUIRED);

        ReverseCourierMapping reverseCourierMapping = new ReverseCourierMapping();
        reverseCourierMapping.setCpId(101);

        when(reversePickupPincodeRepository.findTopByPincodeAndStatusAndCourierOrderByPriorityAsc(any(), anyString()))
                .thenReturn(reversePickupPincode);
        when(reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAsc(any(), anyList(), anyByte(), anyByte()))
                .thenReturn(reversePickupPincode);
        when(reverseCourierMappingRepository.findByCourier(anyString()))
                .thenReturn(reverseCourierMapping);

        // Act
        ReverseCourierDetail reverseCourierDetail1 = reverseCourierDetailService.reassignCourier(returnCreateRequest, reverseCourierDetail);
        assertNotNull(reverseCourierDetail1);
        assertEquals( 101, reverseCourierDetail1.getCpId());
        assertEquals( "Bluedart", reverseCourierDetail1.getCourier());
    }
}

