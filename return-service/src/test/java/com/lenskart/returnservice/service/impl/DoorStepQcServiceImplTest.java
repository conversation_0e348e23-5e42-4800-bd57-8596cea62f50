package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.Reasons;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returnrepository.repository.DoorstepQcQuestionMappingRepository;
import com.lenskart.returnservice.service.IJunoService;
import com.lenskart.returnservice.service.INexsFacilityService;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import com.lenskart.reversemodel.request.kafka.Additional;
import com.lenskart.reversemodel.request.kafka.CourierAssignRetryMetaData;
import com.lenskart.reversemodel.request.kafka.DropInfo;
import com.lenskart.reversemodel.request.kafka.InitiateReverseRequest;
import com.lenskart.reversemodel.request.kafka.PickUpInfo;
import com.lenskart.reversemodel.request.kafka.ShipmentDetails;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {DoorStepQcServiceImpl.class})
@ExtendWith(SpringExtension.class)
class DoorStepQcServiceImplTest {
    @Autowired
    private DoorStepQcServiceImpl doorStepQcServiceImpl;

    @MockBean
    private DoorstepQcQuestionMappingRepository doorstepQcQuestionMappingRepository;

    @MockBean
    private IJunoService iJunoService;

    @MockBean
    private INexsFacilityService iNexsFacilityService;

    @MockBean
    private ISystemPreferenceService iSystemPreferenceService;

    @MockBean
    private RedisTemplate redisTemplate;

    /**
     * Method under test:
     * {@link DoorStepQcServiceImpl#addQcAtDoorstepDetails(ReverseCourierDetail, UwOrderDTO, InitiateReverseRequest, List, ItemWiseAmountDTO, ShippingStatusDetail)}
     */
    @Test
    void testAddQcAtDoorstepDetails() throws Exception {
        // Arrange
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        ReverseCourierDetail reverseCourierDetail = new ReverseCourierDetail();
        reverseCourierDetail.setAllowDispensingCourier(true);
        reverseCourierDetail.setCourier("Courier");
        reverseCourierDetail.setCourierAssignmentType(QCType.QC_REQUIRED);
        reverseCourierDetail.setCourierReassigned(true);
        reverseCourierDetail.setCpId(1);
        reverseCourierDetail.setCpName("Cp Name");
        reverseCourierDetail.setPincode(1);
        reverseCourierDetail.setQcAtDoorStepEligibleByCourierDetails(true);
        reverseCourierDetail.setStatus(true);
        reverseCourierDetail.setTat(1);

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setB2bRefrenceItemId(1);
//        uwOrder.setBarcode("Barcode");
//        uwOrder.setBifocalDivision("Bifocal Division");
        uwOrder.setBrand("Brand");
        uwOrder.setClassification("Classification");
        uwOrder.setClassificationName("Classification Name");
        uwOrder.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setEta("Eta");
//        uwOrder
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder.setFacilityCode("Facility Code");
//        uwOrder.setFitterName("Fitter Name");
        uwOrder.setFitting("Fitting");
//        uwOrder.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setFulfillable_time(mock(Timestamp.class));
        uwOrder.setIncrementId(1);
//        uwOrder.setInventoryCount(3);
//        uwOrder.setIsFulfillable("Is Fulfillable");
        uwOrder.setIsLocalFittingRequired(true);
//        uwOrder.setIsOMAFileUploaded(1);
        uwOrder.setItemId(1);
//        //uwOrder.setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        //uwOrder.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setJitFlag("Jit Flag");



        Additional additional = new Additional();
        additional.setAsync(true);
        additional.setDeliveryType("Delivery Type");
        additional.setLabel(true);
        additional.setOldCourier("Old Courier");
        additional.setOrderId("42");
        additional.setQcType("Qc Type");
        additional.setRvpReason("Just cause");
        additional.setSpecialInstructions("Special Instructions");

        CourierAssignRetryMetaData courierAssignRetryMetaData = new CourierAssignRetryMetaData();
        courierAssignRetryMetaData.setAllowDispensingCourier(true);
        courierAssignRetryMetaData.setAttemptedCouriers(new ArrayList<>());
        courierAssignRetryMetaData.setRetryCount(3);

        DropInfo dropInfo = new DropInfo();
        dropInfo.setDropAddress("42 Main St");
        dropInfo.setDropCity("Drop City");
        dropInfo.setDropCountry("GB");
        dropInfo.setDropEmail("<EMAIL>");
        dropInfo.setDropName("Drop Name");
        dropInfo.setDropPhone("6625550144");
        dropInfo.setDropPincode("Drop Pincode");
        dropInfo.setDropState("Drop State");

        PickUpInfo pickUpInfo = new PickUpInfo();
        pickUpInfo.setEmail("<EMAIL>");
        pickUpInfo.setPickupAddress("42 Main St");
        pickUpInfo.setPickupCity("Pickup City");
        pickUpInfo.setPickupCountry("GB");
        pickUpInfo.setPickupName("Pickup Name");
        pickUpInfo.setPickupPhone("6625550144");
        pickUpInfo.setPickupPincode("Pickup Pincode");
        pickUpInfo.setPickupState("Pickup State");
        pickUpInfo.setPickupTime("Pickup Time");

        ShipmentDetails shipmentDetails = new ShipmentDetails();
        shipmentDetails.setBreadth(1);
        shipmentDetails.setCodValue(10.0d);
        shipmentDetails.setCourierPartner(1);
        shipmentDetails.setHeight(1);
        shipmentDetails.setInvoiceDate("2020-03-01");
        shipmentDetails.setInvoiceNumber("42");
        shipmentDetails.setInvoiceValue(10.0d);
        shipmentDetails.setItems(new ArrayList<>());
        shipmentDetails.setLength(3);
        shipmentDetails.setOrderType("Order Type");
        shipmentDetails.setReferenceNumber("42");
        shipmentDetails.setWeight(3);

        InitiateReverseRequest initiateReverseRequest = new InitiateReverseRequest();
        initiateReverseRequest.setAdditional(additional);
        initiateReverseRequest.setCourierAssignRetryMetaData(courierAssignRetryMetaData);
        initiateReverseRequest.setDropInfo(dropInfo);
        initiateReverseRequest.setPickUpInfo(pickUpInfo);
        initiateReverseRequest.setQcAtDoorStepEligible(true);
        initiateReverseRequest.setShipmentDetails(shipmentDetails);
        ArrayList<com.lenskart.ordermetadata.dto.request.Reasons> reasons = new ArrayList<>();

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);


        // Act
        boolean actualAddQcAtDoorstepDetailsResult = doorStepQcServiceImpl.addQcAtDoorstepDetails(reverseCourierDetail,
                uwOrder, initiateReverseRequest, reasons, itemWiseAmountDTO, new ShippingStatusDetail());

        // Assert
        verify(iSystemPreferenceService).getValuesAsList(eq("qc_at_doorstep"), eq("valid_nav_channel"));
        assertFalse(actualAddQcAtDoorstepDetailsResult);
    }

    /**
     * Method under test:
     * {@link DoorStepQcServiceImpl#addQcAtDoorstepDetails(ReverseCourierDetail, UwOrderDTO, InitiateReverseRequest, List, ItemWiseAmountDTO, ShippingStatusDetail)}
     */
    @Test
    void testAddQcAtDoorstepDetails2() throws Exception {
        // Arrange
        ArrayList<String> stringList = new ArrayList<>();
        stringList.add("START checkQcAtDoorStepEligibility uw-item-id {}, reason-id:{}");
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any())).thenReturn(stringList);

        ReverseCourierDetail reverseCourierDetail = new ReverseCourierDetail();
        reverseCourierDetail.setAllowDispensingCourier(true);
        reverseCourierDetail.setCourier("Courier");
        reverseCourierDetail.setCourierAssignmentType(QCType.QC_REQUIRED);
        reverseCourierDetail.setCourierReassigned(true);
        reverseCourierDetail.setCpId(1);
        reverseCourierDetail.setCpName("Cp Name");
        reverseCourierDetail.setPincode(1);
        reverseCourierDetail.setQcAtDoorStepEligibleByCourierDetails(true);
        reverseCourierDetail.setStatus(true);
        reverseCourierDetail.setTat(1);

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setB2bRefrenceItemId(1);
//        uwOrder.setBarcode("Barcode");
//        uwOrder.setBifocalDivision("Bifocal Division");
        uwOrder.setBrand("Brand");
        uwOrder.setClassification("Classification");
        uwOrder.setClassificationName("Classification Name");
        uwOrder.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setEta("Eta");
//        uwOrder
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder.setFacilityCode("Facility Code");
//        uwOrder.setFitterName("Fitter Name");
        uwOrder.setFitting("Fitting");
//        uwOrder.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setFulfillable_time(mock(Timestamp.class));
        uwOrder.setIncrementId(1);
//        uwOrder.setInventoryCount(3);
//        uwOrder.setIsFulfillable("Is Fulfillable");
        uwOrder.setIsLocalFittingRequired(true);
//        uwOrder.setIsOMAFileUploaded(1);
        uwOrder.setItemId(1);
//        //uwOrder.setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        //uwOrder.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setJitFlag("Jit Flag");
//        uwOrder.setJitPoStatus("Jit Po Status");
//        uwOrder.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setLastShipmentState("Last Shipment State");
//        uwOrder.setLastShipmentStatus("Last Shipment Status");
        uwOrder.setLensPackage("java.text");
//        uwOrder.setLenskartDiscount(10.0d);
//        uwOrder.setLenskartPlusDiscount(10.0d);
        uwOrder.setNavChannel("Nav Channel");
//        uwOrder.setNotStockOutReason(1);
//        uwOrder.setOrder_type("Order type");
        uwOrder.setParentUw(1);
//        uwOrder.setPickedByPicker(true);
//        uwOrder.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setPoId("42");
//        uwOrder.setPolicyNo("Policy No");
//        uwOrder.setPolicyStatus("Policy Status");
//        uwOrder.setPriority(1);
        uwOrder.setProductDeliveryType("Product Delivery Type");
        uwOrder.setProductId(1);
        uwOrder.setProductSku("Product Sku");
        uwOrder.setProductValue("42");
//        uwOrder.setQcFailCnt(1);
//        uwOrder.setQcFailReason("Just cause");
//        uwOrder.setQcFailShelf("Qc Fail Shelf");
//        uwOrder.setQcHold(true);
//        uwOrder.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setQccheck(1);
//        uwOrder.setReturn_(1);
        uwOrder.setShipToStoreRequired(true);
//        uwOrder.setShipment(1);
        uwOrder.setShipmentState("Shipment State");
        uwOrder.setShipmentStatus("Shipment Status");
//        uwOrder.setShippingPackageId("42");
//        uwOrder.setSplOrderFlag("Spl Order Flag");
//        uwOrder.setStatus("Status");
//        uwOrder.setStockout(1);
//        uwOrder.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setStoreInventory(1);
//        uwOrder.setSwitchFacilityStatus(1);
        uwOrder.setTotalPrice(10.0d);
        uwOrder.setUnicomOrderCode("Unicom Order Code");
//        uwOrder.setUnicomPriority(1);
//        uwOrder.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrder.setUnicomSynStatus("Unicom Syn Status");
        uwOrder.setUwItemId(1);
//        uwOrder.setVendor("Vendor");
        uwOrder.setVsmStockout(1);
//        uwOrder.setWarehouseNotPresent(1);

        Additional additional = new Additional();
        additional.setAsync(true);
        additional.setDeliveryType("Delivery Type");
        additional.setLabel(true);
        additional.setOldCourier("Old Courier");
        additional.setOrderId("42");
        additional.setQcType("Qc Type");
        additional.setRvpReason("Just cause");
        additional.setSpecialInstructions("Special Instructions");

        CourierAssignRetryMetaData courierAssignRetryMetaData = new CourierAssignRetryMetaData();
        courierAssignRetryMetaData.setAllowDispensingCourier(true);
        courierAssignRetryMetaData.setAttemptedCouriers(new ArrayList<>());
        courierAssignRetryMetaData.setRetryCount(3);

        DropInfo dropInfo = new DropInfo();
        dropInfo.setDropAddress("42 Main St");
        dropInfo.setDropCity("Drop City");
        dropInfo.setDropCountry("GB");
        dropInfo.setDropEmail("<EMAIL>");
        dropInfo.setDropName("Drop Name");
        dropInfo.setDropPhone("6625550144");
        dropInfo.setDropPincode("Drop Pincode");
        dropInfo.setDropState("Drop State");

        PickUpInfo pickUpInfo = new PickUpInfo();
        pickUpInfo.setEmail("<EMAIL>");
        pickUpInfo.setPickupAddress("42 Main St");
        pickUpInfo.setPickupCity("Pickup City");
        pickUpInfo.setPickupCountry("GB");
        pickUpInfo.setPickupName("Pickup Name");
        pickUpInfo.setPickupPhone("6625550144");
        pickUpInfo.setPickupPincode("Pickup Pincode");
        pickUpInfo.setPickupState("Pickup State");
        pickUpInfo.setPickupTime("Pickup Time");

        ShipmentDetails shipmentDetails = new ShipmentDetails();
        shipmentDetails.setBreadth(1);
        shipmentDetails.setCodValue(10.0d);
        shipmentDetails.setCourierPartner(1);
        shipmentDetails.setHeight(1);
        shipmentDetails.setInvoiceDate("2020-03-01");
        shipmentDetails.setInvoiceNumber("42");
        shipmentDetails.setInvoiceValue(10.0d);
        shipmentDetails.setItems(new ArrayList<>());
        shipmentDetails.setLength(3);
        shipmentDetails.setOrderType("Order Type");
        shipmentDetails.setReferenceNumber("42");
        shipmentDetails.setWeight(3);

        InitiateReverseRequest initiateReverseRequest = new InitiateReverseRequest();
        initiateReverseRequest.setAdditional(additional);
        initiateReverseRequest.setCourierAssignRetryMetaData(courierAssignRetryMetaData);
        initiateReverseRequest.setDropInfo(dropInfo);
        initiateReverseRequest.setPickUpInfo(pickUpInfo);
        initiateReverseRequest.setQcAtDoorStepEligible(true);
        initiateReverseRequest.setShipmentDetails(shipmentDetails);
        ArrayList<com.lenskart.ordermetadata.dto.request.Reasons> reasons = new ArrayList<>();

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmountDTO.setLenskartPlusDiscount(10.0d);
        itemWiseAmountDTO.setPaymentNotCapture(true);
        itemWiseAmountDTO.setPrepaidWeb(10.0d);
        itemWiseAmountDTO.setStoreCredit(10.0d);
        itemWiseAmountDTO.setTPendingPGAmount(10.0d);
        itemWiseAmountDTO.setTotalCodPrice(10.0d);
        itemWiseAmountDTO.setTotalItemAmount(10.0d);
        itemWiseAmountDTO.setTotalOnlinePrice(10.0d);
        itemWiseAmountDTO.setTotalPrepaidAmt(10.0d);
        itemWiseAmountDTO.setTotalPriceOfOrder(10.0d);

        // Act
        boolean actualAddQcAtDoorstepDetailsResult = doorStepQcServiceImpl.addQcAtDoorstepDetails(reverseCourierDetail,
                uwOrder, initiateReverseRequest, reasons, itemWiseAmountDTO, new ShippingStatusDetail());

        // Assert
        verify(iSystemPreferenceService).getValuesAsList(eq("qc_at_doorstep"), eq("valid_nav_channel"));
        assertFalse(actualAddQcAtDoorstepDetailsResult);
    }

    /**
     * Method under test:
     * {@link DoorStepQcServiceImpl#addQcAtDoorstepDetails(ReverseCourierDetail, UwOrderDTO, InitiateReverseRequest, List, ItemWiseAmountDTO, ShippingStatusDetail)}
     */
    @Test
    void testAddQcAtDoorstepDetails3() throws Exception {
        // Arrange
        ArrayList<String> stringList = new ArrayList<>();
        stringList.add("qc_at_doorstep");
        stringList.add("START checkQcAtDoorStepEligibility uw-item-id {}, reason-id:{}");
        when(iSystemPreferenceService.getValuesAsList(Mockito.<String>any(), Mockito.<String>any())).thenReturn(stringList);

        ReverseCourierDetail reverseCourierDetail = new ReverseCourierDetail();
        reverseCourierDetail.setAllowDispensingCourier(true);
        reverseCourierDetail.setCourier("Courier");
        reverseCourierDetail.setCourierAssignmentType(QCType.QC_REQUIRED);
        reverseCourierDetail.setCourierReassigned(true);
        reverseCourierDetail.setCpId(1);
        reverseCourierDetail.setCpName("Cp Name");
        reverseCourierDetail.setPincode(1);
        reverseCourierDetail.setQcAtDoorStepEligibleByCourierDetails(true);
        reverseCourierDetail.setStatus(true);
        reverseCourierDetail.setTat(1);

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setB2bRefrenceItemId(1);
//        uwOrder.setBarcode("Barcode");
//        uwOrder.setBifocalDivision("Bifocal Division");
        uwOrder.setBrand("Brand");
        uwOrder.setClassification("Classification");
        uwOrder.setClassificationName("Classification Name");
        uwOrder.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setEta("Eta");
//        uwOrder
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder.setFacilityCode("Facility Code");
//        uwOrder.setFitterName("Fitter Name");
        uwOrder.setFitting("Fitting");
//        uwOrder.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setFulfillable_time(mock(Timestamp.class));
        uwOrder.setIncrementId(1);
//        uwOrder.setInventoryCount(3);
//        uwOrder.setIsFulfillable("Is Fulfillable");
        uwOrder.setIsLocalFittingRequired(true);
//        uwOrder.setIsOMAFileUploaded(1);
        uwOrder.setItemId(1);
//        //uwOrder.setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        //uwOrder.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setJitFlag("Jit Flag");
//        uwOrder.setJitPoStatus("Jit Po Status");
//        uwOrder.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setLastShipmentState("Last Shipment State");
//        uwOrder.setLastShipmentStatus("Last Shipment Status");
        uwOrder.setLensPackage("java.text");
//        uwOrder.setLenskartDiscount(10.0d);
//        uwOrder.setLenskartPlusDiscount(10.0d);
        uwOrder.setNavChannel("Nav Channel");
//        uwOrder.setNotStockOutReason(1);
//        uwOrder.setOrder_type("Order type");
        uwOrder.setParentUw(1);
//        uwOrder.setPickedByPicker(true);
//        uwOrder.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setPoId("42");
//        uwOrder.setPolicyNo("Policy No");
//        uwOrder.setPolicyStatus("Policy Status");
//        uwOrder.setPriority(1);
        uwOrder.setProductDeliveryType("Product Delivery Type");
        uwOrder.setProductId(1);
        uwOrder.setProductSku("Product Sku");
        uwOrder.setProductValue("42");
//        uwOrder.setQcFailCnt(1);
//        uwOrder.setQcFailReason("Just cause");
//        uwOrder.setQcFailShelf("Qc Fail Shelf");
//        uwOrder.setQcHold(true);
//        uwOrder.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setQccheck(1);
//        uwOrder.setReturn_(1);
        uwOrder.setShipToStoreRequired(true);
//        uwOrder.setShipment(1);
        uwOrder.setShipmentState("Shipment State");
        uwOrder.setShipmentStatus("Shipment Status");
//        uwOrder.setShippingPackageId("42");
//        uwOrder.setSplOrderFlag("Spl Order Flag");
//        uwOrder.setStatus("Status");
//        uwOrder.setStockout(1);
//        uwOrder.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setStoreInventory(1);
//        uwOrder.setSwitchFacilityStatus(1);
        uwOrder.setTotalPrice(10.0d);
        uwOrder.setUnicomOrderCode("Unicom Order Code");
//        uwOrder.setUnicomPriority(1);
//        uwOrder.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrder.setUnicomSynStatus("Unicom Syn Status");
        uwOrder.setUwItemId(1);
//        uwOrder.setVendor("Vendor");
        uwOrder.setVsmStockout(1);
//        uwOrder.setWarehouseNotPresent(1);

        Additional additional = new Additional();
        additional.setAsync(true);
        additional.setDeliveryType("Delivery Type");
        additional.setLabel(true);
        additional.setOldCourier("Old Courier");
        additional.setOrderId("42");
        additional.setQcType("Qc Type");
        additional.setRvpReason("Just cause");
        additional.setSpecialInstructions("Special Instructions");

        CourierAssignRetryMetaData courierAssignRetryMetaData = new CourierAssignRetryMetaData();
        courierAssignRetryMetaData.setAllowDispensingCourier(true);
        courierAssignRetryMetaData.setAttemptedCouriers(new ArrayList<>());
        courierAssignRetryMetaData.setRetryCount(3);

        DropInfo dropInfo = new DropInfo();
        dropInfo.setDropAddress("42 Main St");
        dropInfo.setDropCity("Drop City");
        dropInfo.setDropCountry("GB");
        dropInfo.setDropEmail("<EMAIL>");
        dropInfo.setDropName("Drop Name");
        dropInfo.setDropPhone("6625550144");
        dropInfo.setDropPincode("Drop Pincode");
        dropInfo.setDropState("Drop State");

        PickUpInfo pickUpInfo = new PickUpInfo();
        pickUpInfo.setEmail("<EMAIL>");
        pickUpInfo.setPickupAddress("42 Main St");
        pickUpInfo.setPickupCity("Pickup City");
        pickUpInfo.setPickupCountry("GB");
        pickUpInfo.setPickupName("Pickup Name");
        pickUpInfo.setPickupPhone("6625550144");
        pickUpInfo.setPickupPincode("Pickup Pincode");
        pickUpInfo.setPickupState("Pickup State");
        pickUpInfo.setPickupTime("Pickup Time");

        ShipmentDetails shipmentDetails = new ShipmentDetails();
        shipmentDetails.setBreadth(1);
        shipmentDetails.setCodValue(10.0d);
        shipmentDetails.setCourierPartner(1);
        shipmentDetails.setHeight(1);
        shipmentDetails.setInvoiceDate("2020-03-01");
        shipmentDetails.setInvoiceNumber("42");
        shipmentDetails.setInvoiceValue(10.0d);
        shipmentDetails.setItems(new ArrayList<>());
        shipmentDetails.setLength(3);
        shipmentDetails.setOrderType("Order Type");
        shipmentDetails.setReferenceNumber("42");
        shipmentDetails.setWeight(3);

        InitiateReverseRequest initiateReverseRequest = new InitiateReverseRequest();
        initiateReverseRequest.setAdditional(additional);
        initiateReverseRequest.setCourierAssignRetryMetaData(courierAssignRetryMetaData);
        initiateReverseRequest.setDropInfo(dropInfo);
        initiateReverseRequest.setPickUpInfo(pickUpInfo);
        initiateReverseRequest.setQcAtDoorStepEligible(true);
        initiateReverseRequest.setShipmentDetails(shipmentDetails);
        ArrayList<com.lenskart.ordermetadata.dto.request.Reasons> reasons = new ArrayList<>();

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmountDTO.setLenskartPlusDiscount(10.0d);
        itemWiseAmountDTO.setPaymentNotCapture(true);
        itemWiseAmountDTO.setPrepaidWeb(10.0d);
        itemWiseAmountDTO.setStoreCredit(10.0d);
        itemWiseAmountDTO.setTPendingPGAmount(10.0d);
        itemWiseAmountDTO.setTotalCodPrice(10.0d);
        itemWiseAmountDTO.setTotalItemAmount(10.0d);
        itemWiseAmountDTO.setTotalOnlinePrice(10.0d);
        itemWiseAmountDTO.setTotalPrepaidAmt(10.0d);
        itemWiseAmountDTO.setTotalPriceOfOrder(10.0d);

        // Act
        boolean actualAddQcAtDoorstepDetailsResult = doorStepQcServiceImpl.addQcAtDoorstepDetails(reverseCourierDetail,
                uwOrder, initiateReverseRequest, reasons, itemWiseAmountDTO, new ShippingStatusDetail());

        // Assert
        verify(iSystemPreferenceService).getValuesAsList(eq("qc_at_doorstep"), eq("valid_nav_channel"));
        assertFalse(actualAddQcAtDoorstepDetailsResult);
    }

    /**
     * Method under test:
     * {@link DoorStepQcServiceImpl#addQcAtDoorstepDetails(ReverseCourierDetail, UwOrderDTO, InitiateReverseRequest, List, ItemWiseAmountDTO, ShippingStatusDetail)}
     */
    @Test
    void testAddQcAtDoorstepDetails4() {
        // Arrange
        ReverseCourierDetail reverseCourierDetail = mock(ReverseCourierDetail.class);
        when(reverseCourierDetail.isQcAtDoorStepEligibleByCourierDetails()).thenReturn(false);
        doNothing().when(reverseCourierDetail).setAllowDispensingCourier(anyBoolean());
        doNothing().when(reverseCourierDetail).setCourier(Mockito.<String>any());
        doNothing().when(reverseCourierDetail).setCourierAssignmentType(Mockito.<QCType>any());
        doNothing().when(reverseCourierDetail).setCourierReassigned(anyBoolean());
        doNothing().when(reverseCourierDetail).setCpId(Mockito.<Integer>any());
        doNothing().when(reverseCourierDetail).setCpName(Mockito.<String>any());
        doNothing().when(reverseCourierDetail).setPincode(Mockito.<Integer>any());
        doNothing().when(reverseCourierDetail).setQcAtDoorStepEligibleByCourierDetails(anyBoolean());
        doNothing().when(reverseCourierDetail).setStatus(Mockito.<Boolean>any());
        doNothing().when(reverseCourierDetail).setTat(Mockito.<Integer>any());
        reverseCourierDetail.setAllowDispensingCourier(true);
        reverseCourierDetail.setCourier("Courier");
        reverseCourierDetail.setCourierAssignmentType(QCType.QC_REQUIRED);
        reverseCourierDetail.setCourierReassigned(true);
        reverseCourierDetail.setCpId(1);
        reverseCourierDetail.setCpName("Cp Name");
        reverseCourierDetail.setPincode(1);
        reverseCourierDetail.setQcAtDoorStepEligibleByCourierDetails(true);
        reverseCourierDetail.setStatus(true);
        reverseCourierDetail.setTat(1);

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setB2bRefrenceItemId(1);
//        uwOrder.setBarcode("Barcode");
//        uwOrder.setBifocalDivision("Bifocal Division");
        uwOrder.setBrand("Brand");
        uwOrder.setClassification("Classification");
        uwOrder.setClassificationName("Classification Name");
        uwOrder.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setEta("Eta");
//        uwOrder
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder.setFacilityCode("Facility Code");
//        uwOrder.setFitterName("Fitter Name");
        uwOrder.setFitting("Fitting");
//        uwOrder.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setFulfillable_time(mock(Timestamp.class));
        uwOrder.setIncrementId(1);
//        uwOrder.setInventoryCount(3);
//        uwOrder.setIsFulfillable("Is Fulfillable");
        uwOrder.setIsLocalFittingRequired(true);
//        uwOrder.setIsOMAFileUploaded(1);
        uwOrder.setItemId(1);
//        //uwOrder.setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        //uwOrder.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setJitFlag("Jit Flag");
//        uwOrder.setJitPoStatus("Jit Po Status");
//        uwOrder.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setLastShipmentState("Last Shipment State");
//        uwOrder.setLastShipmentStatus("Last Shipment Status");
        uwOrder.setLensPackage("java.text");
//        uwOrder.setLenskartDiscount(10.0d);
//        uwOrder.setLenskartPlusDiscount(10.0d);
        uwOrder.setNavChannel("Nav Channel");
//        uwOrder.setNotStockOutReason(1);
//        uwOrder.setOrder_type("Order type");
        uwOrder.setParentUw(1);
//        uwOrder.setPickedByPicker(true);
//        uwOrder.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setPoId("42");
//        uwOrder.setPolicyNo("Policy No");
//        uwOrder.setPolicyStatus("Policy Status");
//        uwOrder.setPriority(1);
        uwOrder.setProductDeliveryType("Product Delivery Type");
        uwOrder.setProductId(1);
        uwOrder.setProductSku("Product Sku");
        uwOrder.setProductValue("42");
//        uwOrder.setQcFailCnt(1);
//        uwOrder.setQcFailReason("Just cause");
//        uwOrder.setQcFailShelf("Qc Fail Shelf");
//        uwOrder.setQcHold(true);
//        uwOrder.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setQccheck(1);
//        uwOrder.setReturn_(1);
        uwOrder.setShipToStoreRequired(true);
//        uwOrder.setShipment(1);
        uwOrder.setShipmentState("Shipment State");
        uwOrder.setShipmentStatus("Shipment Status");
//        uwOrder.setShippingPackageId("42");
//        uwOrder.setSplOrderFlag("Spl Order Flag");
//        uwOrder.setStatus("Status");
//        uwOrder.setStockout(1);
//        uwOrder.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setStoreInventory(1);
//        uwOrder.setSwitchFacilityStatus(1);
        uwOrder.setTotalPrice(10.0d);
        uwOrder.setUnicomOrderCode("Unicom Order Code");
//        uwOrder.setUnicomPriority(1);
//        uwOrder.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrder.setUnicomSynStatus("Unicom Syn Status");
        uwOrder.setUwItemId(1);
//        uwOrder.setVendor("Vendor");
        uwOrder.setVsmStockout(1);
//        uwOrder.setWarehouseNotPresent(1);

        Additional additional = new Additional();
        additional.setAsync(true);
        additional.setDeliveryType("Delivery Type");
        additional.setLabel(true);
        additional.setOldCourier("Old Courier");
        additional.setOrderId("42");
        additional.setQcType("Qc Type");
        additional.setRvpReason("Just cause");
        additional.setSpecialInstructions("Special Instructions");

        CourierAssignRetryMetaData courierAssignRetryMetaData = new CourierAssignRetryMetaData();
        courierAssignRetryMetaData.setAllowDispensingCourier(true);
        courierAssignRetryMetaData.setAttemptedCouriers(new ArrayList<>());
        courierAssignRetryMetaData.setRetryCount(3);

        DropInfo dropInfo = new DropInfo();
        dropInfo.setDropAddress("42 Main St");
        dropInfo.setDropCity("Drop City");
        dropInfo.setDropCountry("GB");
        dropInfo.setDropEmail("<EMAIL>");
        dropInfo.setDropName("Drop Name");
        dropInfo.setDropPhone("6625550144");
        dropInfo.setDropPincode("Drop Pincode");
        dropInfo.setDropState("Drop State");

        PickUpInfo pickUpInfo = new PickUpInfo();
        pickUpInfo.setEmail("<EMAIL>");
        pickUpInfo.setPickupAddress("42 Main St");
        pickUpInfo.setPickupCity("Pickup City");
        pickUpInfo.setPickupCountry("GB");
        pickUpInfo.setPickupName("Pickup Name");
        pickUpInfo.setPickupPhone("6625550144");
        pickUpInfo.setPickupPincode("Pickup Pincode");
        pickUpInfo.setPickupState("Pickup State");
        pickUpInfo.setPickupTime("Pickup Time");

        ShipmentDetails shipmentDetails = new ShipmentDetails();
        shipmentDetails.setBreadth(1);
        shipmentDetails.setCodValue(10.0d);
        shipmentDetails.setCourierPartner(1);
        shipmentDetails.setHeight(1);
        shipmentDetails.setInvoiceDate("2020-03-01");
        shipmentDetails.setInvoiceNumber("42");
        shipmentDetails.setInvoiceValue(10.0d);
        shipmentDetails.setItems(new ArrayList<>());
        shipmentDetails.setLength(3);
        shipmentDetails.setOrderType("Order Type");
        shipmentDetails.setReferenceNumber("42");
        shipmentDetails.setWeight(3);

        InitiateReverseRequest initiateReverseRequest = new InitiateReverseRequest();
        initiateReverseRequest.setAdditional(additional);
        initiateReverseRequest.setCourierAssignRetryMetaData(courierAssignRetryMetaData);
        initiateReverseRequest.setDropInfo(dropInfo);
        initiateReverseRequest.setPickUpInfo(pickUpInfo);
        initiateReverseRequest.setQcAtDoorStepEligible(true);
        initiateReverseRequest.setShipmentDetails(shipmentDetails);
        ArrayList<Reasons> reasons = new ArrayList<>();

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmountDTO.setLenskartPlusDiscount(10.0d);
        itemWiseAmountDTO.setPaymentNotCapture(true);
        itemWiseAmountDTO.setPrepaidWeb(10.0d);
        itemWiseAmountDTO.setStoreCredit(10.0d);
        itemWiseAmountDTO.setTPendingPGAmount(10.0d);
        itemWiseAmountDTO.setTotalCodPrice(10.0d);
        itemWiseAmountDTO.setTotalItemAmount(10.0d);
        itemWiseAmountDTO.setTotalOnlinePrice(10.0d);
        itemWiseAmountDTO.setTotalPrepaidAmt(10.0d);
        itemWiseAmountDTO.setTotalPriceOfOrder(10.0d);

        // Act
        boolean actualAddQcAtDoorstepDetailsResult = doorStepQcServiceImpl.addQcAtDoorstepDetails(reverseCourierDetail,
                uwOrder, initiateReverseRequest, reasons, itemWiseAmountDTO, new ShippingStatusDetail());

        // Assert
        verify(reverseCourierDetail).isQcAtDoorStepEligibleByCourierDetails();
        verify(reverseCourierDetail).setAllowDispensingCourier(eq(true));
        verify(reverseCourierDetail).setCourier(eq("Courier"));
        verify(reverseCourierDetail).setCourierAssignmentType(eq(QCType.QC_REQUIRED));
        verify(reverseCourierDetail).setCourierReassigned(eq(true));
        verify(reverseCourierDetail).setCpId(eq(1));
        verify(reverseCourierDetail).setCpName(eq("Cp Name"));
        verify(reverseCourierDetail).setPincode(eq(1));
        verify(reverseCourierDetail).setQcAtDoorStepEligibleByCourierDetails(eq(true));
        verify(reverseCourierDetail).setStatus(eq(true));
        verify(reverseCourierDetail).setTat(eq(1));
        assertFalse(actualAddQcAtDoorstepDetailsResult);
    }
}
