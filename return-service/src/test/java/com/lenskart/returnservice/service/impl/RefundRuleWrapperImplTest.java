package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.returncommon.model.dto.DecisionTableRefundDTO;
import com.lenskart.returncommon.model.dto.MvcOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundResponseDTO;
import com.lenskart.returnrepository.entity.RefundRules;
import com.lenskart.returnrepository.repository.RefundRulesRepository;
import com.lenskart.returnservice.elasticsearch.service.RefundRulesCache;
import com.lenskart.returnservice.service.IMvcService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.DirtiesContext.ClassMode;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {RefundRuleWrapperImpl.class})
@ExtendWith(SpringExtension.class)
@DirtiesContext(classMode = ClassMode.AFTER_EACH_TEST_METHOD)
class RefundRuleWrapperImplTest {
    @MockBean
    private IMvcService iMvcService;

    @Autowired
    private RefundRuleWrapperImpl refundRuleWrapperImpl;

    @MockBean
    private RefundRulesRepository refundRulesRepository;

    @MockBean
    private RefundRulesCache refundRulesCache;

    /**
     * Test {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}.
     * <p>
     * Method under test: {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}
     */
    @Test
    @DisplayName("Test fetchRule(DecisionTableRefundDTO)")
    @Tag("MaintainedByDiffblue")
    void testFetchRule() {
        // Arrange
        when(iMvcService.getMvcOrdersResponse(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(new MvcOrderDTO(3, "[fetchRule] Entered into func. uwitemId {} decisionTableRefundDTO {}"));

        RefundRules refundRules = new RefundRules();
        refundRules.setAction("Action");
        refundRules.setAmountFrom(10L);
        refundRules.setAmountTill(10L);
        refundRules.setBlacklistedPhoneNumbers(true);
        refundRules.setBlacklistedPincodes(true);
        refundRules.setCategory("Category");
        refundRules.setCountryCode("GB");
        refundRules.setCustomerScoreFrom(3);
        refundRules.setCustomerScoreTo(3);
        refundRules.setDoRefund(true);
        refundRules.setDraftReturnMethod("Draft Return Method");
        refundRules.setExchangeAllowed(1);
        refundRules.setExchangeOrderDispatch("Exchange Order Dispatch");
        refundRules.setId(1);
        refundRules.setInsurancePolicy(true);
        refundRules.setIsAccessoryMissing(true);
        refundRules.setIsBranded(true);
        refundRules.setIsLastPiece(true);
        refundRules.setIsLensOnly(true);
        refundRules.setIsPsuedoGatepass(true);
        refundRules.setIsQcPass(true);
        refundRules.setIsReturnable(true);
        refundRules.setNavChannel("Nav Channel");
        refundRules.setOverrideWarrantyPeriod(true);
        refundRules.setPaymentMethod("Payment Method");
        refundRules.setRefundDispatch("Refund Dispatch");
        refundRules.setRefundMethod("Refund Method");
        refundRules.setReturnEligibilityPeriod(1);
        refundRules.setReturnInitiatedSource("Return Initiated Source");
        refundRules.setReturnReasons("Just cause");
        refundRules.setReverseType("Reverse Type");
        refundRules.setStoreScoreFrom(3);
        refundRules.setStoreScoreTo(3);
        refundRules.setTriggerPoint("Trigger Point");
        refundRules.setWarantyFrom(1);
        refundRules.setWarantyTo(1);
        when(refundRulesRepository.findRuleTriggered(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.anyBoolean(),
                Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.anyBoolean(), Mockito.anyInt(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.anyBoolean(), Mockito.<String>any(), Mockito.anyBoolean(), Mockito.<String>any(), Mockito.anyDouble(),
                Mockito.anyDouble())).thenReturn(refundRules);

        DecisionTableRefundDTO decisionTableRefundDTO = new DecisionTableRefundDTO();
        decisionTableRefundDTO.setAction("Action");
        decisionTableRefundDTO.setAmountValidity(10.0d);
        decisionTableRefundDTO.setBlacklistedPhoneNumbers(true);
        decisionTableRefundDTO.setBlacklistedPincodes(true);
        decisionTableRefundDTO.setBrand("Brand");
        decisionTableRefundDTO.setCategory("Category");
        decisionTableRefundDTO.setCountryCode("GB");
        decisionTableRefundDTO.setCustomerScore(3);
        decisionTableRefundDTO.setDoRefund(true);
        decisionTableRefundDTO.setDraftReturnMethod("Draft Return Method");
        decisionTableRefundDTO.setExchangeAllowed(1);
        decisionTableRefundDTO.setExchangeCount(3);
        decisionTableRefundDTO.setExchangeOrderDispatch("Exchange Order Dispatch");
        decisionTableRefundDTO.setInsurancePolicy(true);
        decisionTableRefundDTO.setIsAccessoryMissing(true);
        decisionTableRefundDTO.setIsBranded(true);
        decisionTableRefundDTO.setIsLastPiece(true);
        decisionTableRefundDTO.setIsLensOnly(true);
        decisionTableRefundDTO.setIsPseudoGatepass(true);
        decisionTableRefundDTO.setIsQcPass(true);
        decisionTableRefundDTO.setIsReturnable(true);
        decisionTableRefundDTO.setItemWarrantyPeriod(42);
        decisionTableRefundDTO.setNavChannel("Nav Channel");
        decisionTableRefundDTO.setPaymentMethod("Payment Method");
        decisionTableRefundDTO.setRefundCount(3);
        decisionTableRefundDTO.setRefundDispatch("Refund Dispatch");
        decisionTableRefundDTO.setRefundMethod("Refund Method");
        decisionTableRefundDTO.setReturnCount(3);
        decisionTableRefundDTO.setReturnEligibiityPeriod(1);
        decisionTableRefundDTO.setReturnId(1);
        decisionTableRefundDTO.setReturnInitiatedSource("Return Initiated Source");
        decisionTableRefundDTO.setReturnPeriod(1);
        decisionTableRefundDTO.setReturnReasons("Just cause");
        decisionTableRefundDTO.setReverseType("Reverse Type");
        decisionTableRefundDTO.setRuleCalledFrom("<EMAIL>");
        decisionTableRefundDTO.setStoreScore(3);
        decisionTableRefundDTO.setTriggerPoint("Trigger Point");
        decisionTableRefundDTO.setUwItemId(1);

        // Act
        ReturnRefundResponseDTO actualFetchRuleResult = refundRuleWrapperImpl.fetchRule(decisionTableRefundDTO);

        // Assert
        verify(refundRulesRepository).findRuleTriggered(eq("Reverse Type"), eq("Return Initiated Source"),
                eq("Trigger Point"), eq(true), eq(true), eq(true), eq(1), eq(true), eq(true), eq(true), eq(10), eq(true), eq(1),
                eq("Draft Return Method"), eq("Nav Channel"), eq("Just cause"), eq("Category"), eq(true), eq("GB"), eq(true),
                eq("Payment Method"), eq(3.0d), eq(3.0d));
        verify(iMvcService).getMvcOrdersResponse(eq("UW_ITEM_ID"), eq("1"));
        assertEquals("Action", actualFetchRuleResult.getAction());
        assertEquals("Exchange Order Dispatch", actualFetchRuleResult.getExchangeOrderDispatch());
        assertEquals("Refund Dispatch", actualFetchRuleResult.getRefundDispatch());
        assertEquals("Refund Method", actualFetchRuleResult.getRefundMethod());
        assertNull(actualFetchRuleResult.getErrorForNotExchangeable());
        assertNull(actualFetchRuleResult.getErrorForNotRefundable());
        assertNull(actualFetchRuleResult.getErrorForNotReturnable());
        assertNull(actualFetchRuleResult.getRefundMethodRequest());
        assertNull(actualFetchRuleResult.getReturnEligibleTillDate());
        assertNull(actualFetchRuleResult.getRefundIntentCreatedAt());
        assertNull(actualFetchRuleResult.getRefundMethods());
        assertNull(actualFetchRuleResult.getRulesList());
        assertEquals(0, actualFetchRuleResult.getExchangeCount());
        assertEquals(0, actualFetchRuleResult.getRefundCount());
        assertEquals(0, actualFetchRuleResult.getReturnCount());
        assertEquals(0.0d, actualFetchRuleResult.getAmountToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getItemPrice());
        assertEquals(0.0d, actualFetchRuleResult.getItemRefundedAmount());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashPlusToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashToRefund());
        assertEquals(1, actualFetchRuleResult.getId());
        assertEquals(1, actualFetchRuleResult.getReturnEligibilityPeriod());
        assertFalse(actualFetchRuleResult.isApprovalNeeded());
        assertFalse(actualFetchRuleResult.isDraftStatus());
        assertFalse(actualFetchRuleResult.isExchange());
        assertFalse(actualFetchRuleResult.isExchangeCreatedAndCancelled());
        assertFalse(actualFetchRuleResult.isExchangeExpired());
        assertFalse(actualFetchRuleResult.isExchangeOnlyCTA());
        assertFalse(actualFetchRuleResult.isFetchExistingReturnResponse());
        assertFalse(actualFetchRuleResult.isFraud());
        assertTrue(actualFetchRuleResult.getDoRefund());
        assertTrue(actualFetchRuleResult.getIsReturnable());
        assertTrue(actualFetchRuleResult.isWarrantyActive());
    }

    /**
     * Test {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}.
     * <ul>
     *   <li>Given {@link IMvcService} {@link IMvcService#getMvcOrdersResponse(String, String)} return {@link MvcOrderDTO#MvcOrderDTO()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}
     */
    @Test
    @DisplayName("Test fetchRule(DecisionTableRefundDTO); given IMvcService getMvcOrdersResponse(String, String) return MvcOrderDTO()")
    @Tag("MaintainedByDiffblue")
    void testFetchRule_givenIMvcServiceGetMvcOrdersResponseReturnMvcOrderDTO() {
        // Arrange
        when(iMvcService.getMvcOrdersResponse(Mockito.<String>any(), Mockito.<String>any())).thenReturn(new MvcOrderDTO());

        RefundRules refundRules = new RefundRules();
        refundRules.setAction("Action");
        refundRules.setAmountFrom(10L);
        refundRules.setAmountTill(10L);
        refundRules.setBlacklistedPhoneNumbers(true);
        refundRules.setBlacklistedPincodes(true);
        refundRules.setCategory("Category");
        refundRules.setCountryCode("GB");
        refundRules.setCustomerScoreFrom(3);
        refundRules.setCustomerScoreTo(3);
        refundRules.setDoRefund(true);
        refundRules.setDraftReturnMethod("Draft Return Method");
        refundRules.setExchangeAllowed(1);
        refundRules.setExchangeOrderDispatch("Exchange Order Dispatch");
        refundRules.setId(1);
        refundRules.setInsurancePolicy(true);
        refundRules.setIsAccessoryMissing(true);
        refundRules.setIsBranded(true);
        refundRules.setIsLastPiece(true);
        refundRules.setIsLensOnly(true);
        refundRules.setIsPsuedoGatepass(true);
        refundRules.setIsQcPass(true);
        refundRules.setIsReturnable(true);
        refundRules.setNavChannel("Nav Channel");
        refundRules.setOverrideWarrantyPeriod(true);
        refundRules.setPaymentMethod("Payment Method");
        refundRules.setRefundDispatch("Refund Dispatch");
        refundRules.setRefundMethod("Refund Method");
        refundRules.setReturnEligibilityPeriod(1);
        refundRules.setReturnInitiatedSource("Return Initiated Source");
        refundRules.setReturnReasons("Just cause");
        refundRules.setReverseType("Reverse Type");
        refundRules.setStoreScoreFrom(3);
        refundRules.setStoreScoreTo(3);
        refundRules.setTriggerPoint("Trigger Point");
        refundRules.setWarantyFrom(1);
        refundRules.setWarantyTo(1);
        when(refundRulesRepository.findRuleTriggered(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                anyBoolean(), anyBoolean(), anyBoolean(), anyInt(), anyBoolean(), anyBoolean(), anyBoolean(), anyInt(),
                anyBoolean(), anyInt(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), anyBoolean(), Mockito.<String>any(), anyBoolean(), Mockito.<String>any(), anyDouble(),
                anyDouble())).thenReturn(refundRules);

        DecisionTableRefundDTO decisionTableRefundDTO = new DecisionTableRefundDTO();
        decisionTableRefundDTO.setAction("Action");
        decisionTableRefundDTO.setAmountValidity(10.0d);
        decisionTableRefundDTO.setBlacklistedPhoneNumbers(true);
        decisionTableRefundDTO.setBlacklistedPincodes(true);
        decisionTableRefundDTO.setBrand("Brand");
        decisionTableRefundDTO.setCategory("Category");
        decisionTableRefundDTO.setCountryCode("GB");
        decisionTableRefundDTO.setCustomerScore(3);
        decisionTableRefundDTO.setDoRefund(true);
        decisionTableRefundDTO.setDraftReturnMethod("Draft Return Method");
        decisionTableRefundDTO.setExchangeAllowed(1);
        decisionTableRefundDTO.setExchangeCount(3);
        decisionTableRefundDTO.setExchangeOrderDispatch("Exchange Order Dispatch");
        decisionTableRefundDTO.setInsurancePolicy(true);
        decisionTableRefundDTO.setIsAccessoryMissing(true);
        decisionTableRefundDTO.setIsBranded(true);
        decisionTableRefundDTO.setIsLastPiece(true);
        decisionTableRefundDTO.setIsLensOnly(true);
        decisionTableRefundDTO.setIsPseudoGatepass(true);
        decisionTableRefundDTO.setIsQcPass(true);
        decisionTableRefundDTO.setIsReturnable(true);
        decisionTableRefundDTO.setItemWarrantyPeriod(42);
        decisionTableRefundDTO.setNavChannel("Nav Channel");
        decisionTableRefundDTO.setPaymentMethod("Payment Method");
        decisionTableRefundDTO.setRefundCount(3);
        decisionTableRefundDTO.setRefundDispatch("Refund Dispatch");
        decisionTableRefundDTO.setRefundMethod("Refund Method");
        decisionTableRefundDTO.setReturnCount(3);
        decisionTableRefundDTO.setReturnEligibiityPeriod(1);
        decisionTableRefundDTO.setReturnId(1);
        decisionTableRefundDTO.setReturnInitiatedSource("Return Initiated Source");
        decisionTableRefundDTO.setReturnPeriod(1);
        decisionTableRefundDTO.setReturnReasons("Just cause");
        decisionTableRefundDTO.setReverseType("Reverse Type");
        decisionTableRefundDTO.setRuleCalledFrom("<EMAIL>");
        decisionTableRefundDTO.setStoreScore(3);
        decisionTableRefundDTO.setTriggerPoint("Trigger Point");
        decisionTableRefundDTO.setUwItemId(1);

        // Act
        ReturnRefundResponseDTO actualFetchRuleResult = refundRuleWrapperImpl.fetchRule(decisionTableRefundDTO);

        // Assert
        verify(refundRulesRepository).findRuleTriggered(eq("Reverse Type"), eq("Return Initiated Source"),
                eq("Trigger Point"), eq(true), eq(true), eq(true), eq(1), eq(true), eq(true), eq(true), eq(10), eq(true), eq(1),
                eq("Draft Return Method"), eq("Nav Channel"), eq("Just cause"), eq("Category"), eq(true), eq("GB"), eq(true),
                eq("Payment Method"), eq(3.0d), eq(3.0d));
        verify(iMvcService).getMvcOrdersResponse(eq("UW_ITEM_ID"), eq("1"));
        assertEquals("Action", actualFetchRuleResult.getAction());
        assertEquals("Exchange Order Dispatch", actualFetchRuleResult.getExchangeOrderDispatch());
        assertEquals("Refund Dispatch", actualFetchRuleResult.getRefundDispatch());
        assertEquals("Refund Method", actualFetchRuleResult.getRefundMethod());
        assertNull(actualFetchRuleResult.getErrorForNotExchangeable());
        assertNull(actualFetchRuleResult.getErrorForNotRefundable());
        assertNull(actualFetchRuleResult.getErrorForNotReturnable());
        assertNull(actualFetchRuleResult.getRefundMethodRequest());
        assertNull(actualFetchRuleResult.getReturnEligibleTillDate());
        assertNull(actualFetchRuleResult.getRefundIntentCreatedAt());
        assertNull(actualFetchRuleResult.getRefundMethods());
        assertNull(actualFetchRuleResult.getRulesList());
        assertEquals(0, actualFetchRuleResult.getExchangeCount());
        assertEquals(0, actualFetchRuleResult.getRefundCount());
        assertEquals(0, actualFetchRuleResult.getReturnCount());
        assertEquals(0.0d, actualFetchRuleResult.getAmountToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getItemPrice());
        assertEquals(0.0d, actualFetchRuleResult.getItemRefundedAmount());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashPlusToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashToRefund());
        assertEquals(1, actualFetchRuleResult.getId());
        assertEquals(1, actualFetchRuleResult.getReturnEligibilityPeriod());
        assertFalse(actualFetchRuleResult.isApprovalNeeded());
        assertFalse(actualFetchRuleResult.isDraftStatus());
        assertFalse(actualFetchRuleResult.isExchange());
        assertFalse(actualFetchRuleResult.isExchangeCreatedAndCancelled());
        assertFalse(actualFetchRuleResult.isExchangeExpired());
        assertFalse(actualFetchRuleResult.isExchangeOnlyCTA());
        assertFalse(actualFetchRuleResult.isFetchExistingReturnResponse());
        assertFalse(actualFetchRuleResult.isFraud());
        assertTrue(actualFetchRuleResult.getDoRefund());
        assertTrue(actualFetchRuleResult.getIsReturnable());
        assertTrue(actualFetchRuleResult.isWarrantyActive());
    }

    /**
     * Test {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}.
     * <ul>
     *   <li>Given {@link IMvcService} {@link IMvcService#getMvcOrdersResponse(String, String)} return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}
     */
    @Test
    @DisplayName("Test fetchRule(DecisionTableRefundDTO); given IMvcService getMvcOrdersResponse(String, String) return 'null'")
    @Tag("MaintainedByDiffblue")
    void testFetchRule_givenIMvcServiceGetMvcOrdersResponseReturnNull() {
        // Arrange
        when(iMvcService.getMvcOrdersResponse(Mockito.<String>any(), Mockito.<String>any())).thenReturn(null);

        RefundRules refundRules = new RefundRules();
        refundRules.setAction("Action");
        refundRules.setAmountFrom(10L);
        refundRules.setAmountTill(10L);
        refundRules.setBlacklistedPhoneNumbers(true);
        refundRules.setBlacklistedPincodes(true);
        refundRules.setCategory("Category");
        refundRules.setCountryCode("GB");
        refundRules.setCustomerScoreFrom(3);
        refundRules.setCustomerScoreTo(3);
        refundRules.setDoRefund(true);
        refundRules.setDraftReturnMethod("Draft Return Method");
        refundRules.setExchangeAllowed(1);
        refundRules.setExchangeOrderDispatch("Exchange Order Dispatch");
        refundRules.setId(1);
        refundRules.setInsurancePolicy(true);
        refundRules.setIsAccessoryMissing(true);
        refundRules.setIsBranded(true);
        refundRules.setIsLastPiece(true);
        refundRules.setIsLensOnly(true);
        refundRules.setIsPsuedoGatepass(true);
        refundRules.setIsQcPass(true);
        refundRules.setIsReturnable(true);
        refundRules.setNavChannel("Nav Channel");
        refundRules.setOverrideWarrantyPeriod(true);
        refundRules.setPaymentMethod("Payment Method");
        refundRules.setRefundDispatch("Refund Dispatch");
        refundRules.setRefundMethod("Refund Method");
        refundRules.setReturnEligibilityPeriod(1);
        refundRules.setReturnInitiatedSource("Return Initiated Source");
        refundRules.setReturnReasons("Just cause");
        refundRules.setReverseType("Reverse Type");
        refundRules.setStoreScoreFrom(3);
        refundRules.setStoreScoreTo(3);
        refundRules.setTriggerPoint("Trigger Point");
        refundRules.setWarantyFrom(1);
        refundRules.setWarantyTo(1);
        when(refundRulesRepository.findRuleTriggered(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.anyBoolean(),
                Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.anyBoolean(), Mockito.anyInt(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.anyBoolean(), Mockito.<String>any(), Mockito.anyBoolean(), Mockito.<String>any(), Mockito.anyDouble(),
                Mockito.anyDouble())).thenReturn(refundRules);

        DecisionTableRefundDTO decisionTableRefundDTO = new DecisionTableRefundDTO();
        decisionTableRefundDTO.setAction("Action");
        decisionTableRefundDTO.setAmountValidity(10.0d);
        decisionTableRefundDTO.setBlacklistedPhoneNumbers(true);
        decisionTableRefundDTO.setBlacklistedPincodes(true);
        decisionTableRefundDTO.setBrand("Brand");
        decisionTableRefundDTO.setCategory("Category");
        decisionTableRefundDTO.setCountryCode("GB");
        decisionTableRefundDTO.setCustomerScore(3);
        decisionTableRefundDTO.setDoRefund(true);
        decisionTableRefundDTO.setDraftReturnMethod("Draft Return Method");
        decisionTableRefundDTO.setExchangeAllowed(1);
        decisionTableRefundDTO.setExchangeCount(3);
        decisionTableRefundDTO.setExchangeOrderDispatch("Exchange Order Dispatch");
        decisionTableRefundDTO.setInsurancePolicy(true);
        decisionTableRefundDTO.setIsAccessoryMissing(true);
        decisionTableRefundDTO.setIsBranded(true);
        decisionTableRefundDTO.setIsLastPiece(true);
        decisionTableRefundDTO.setIsLensOnly(true);
        decisionTableRefundDTO.setIsPseudoGatepass(true);
        decisionTableRefundDTO.setIsQcPass(true);
        decisionTableRefundDTO.setIsReturnable(true);
        decisionTableRefundDTO.setItemWarrantyPeriod(42);
        decisionTableRefundDTO.setNavChannel("Nav Channel");
        decisionTableRefundDTO.setPaymentMethod("Payment Method");
        decisionTableRefundDTO.setRefundCount(3);
        decisionTableRefundDTO.setRefundDispatch("Refund Dispatch");
        decisionTableRefundDTO.setRefundMethod("Refund Method");
        decisionTableRefundDTO.setReturnCount(3);
        decisionTableRefundDTO.setReturnEligibiityPeriod(1);
        decisionTableRefundDTO.setReturnId(1);
        decisionTableRefundDTO.setReturnInitiatedSource("Return Initiated Source");
        decisionTableRefundDTO.setReturnPeriod(1);
        decisionTableRefundDTO.setReturnReasons("Just cause");
        decisionTableRefundDTO.setReverseType("Reverse Type");
        decisionTableRefundDTO.setRuleCalledFrom("<EMAIL>");
        decisionTableRefundDTO.setStoreScore(3);
        decisionTableRefundDTO.setTriggerPoint("Trigger Point");
        decisionTableRefundDTO.setUwItemId(1);

        // Act
        ReturnRefundResponseDTO actualFetchRuleResult = refundRuleWrapperImpl.fetchRule(decisionTableRefundDTO);

        // Assert
        verify(refundRulesRepository).findRuleTriggered(eq("Reverse Type"), eq("Return Initiated Source"),
                eq("Trigger Point"), eq(true), eq(true), eq(true), eq(1), eq(true), eq(true), eq(true), eq(10), eq(true), eq(1),
                eq("Draft Return Method"), eq("Nav Channel"), eq("Just cause"), eq("Category"), eq(true), eq("GB"), eq(true),
                eq("Payment Method"), eq(3.0d), eq(3.0d));
        verify(iMvcService).getMvcOrdersResponse(eq("UW_ITEM_ID"), eq("1"));
        assertEquals("Action", actualFetchRuleResult.getAction());
        assertEquals("Exchange Order Dispatch", actualFetchRuleResult.getExchangeOrderDispatch());
        assertEquals("Refund Dispatch", actualFetchRuleResult.getRefundDispatch());
        assertEquals("Refund Method", actualFetchRuleResult.getRefundMethod());
        assertNull(actualFetchRuleResult.getErrorForNotExchangeable());
        assertNull(actualFetchRuleResult.getErrorForNotRefundable());
        assertNull(actualFetchRuleResult.getErrorForNotReturnable());
        assertNull(actualFetchRuleResult.getRefundMethodRequest());
        assertNull(actualFetchRuleResult.getReturnEligibleTillDate());
        assertNull(actualFetchRuleResult.getRefundIntentCreatedAt());
        assertNull(actualFetchRuleResult.getRefundMethods());
        assertNull(actualFetchRuleResult.getRulesList());
        assertEquals(0, actualFetchRuleResult.getExchangeCount());
        assertEquals(0, actualFetchRuleResult.getRefundCount());
        assertEquals(0, actualFetchRuleResult.getReturnCount());
        assertEquals(0.0d, actualFetchRuleResult.getAmountToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getItemPrice());
        assertEquals(0.0d, actualFetchRuleResult.getItemRefundedAmount());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashPlusToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashToRefund());
        assertEquals(1, actualFetchRuleResult.getId());
        assertEquals(1, actualFetchRuleResult.getReturnEligibilityPeriod());
        assertFalse(actualFetchRuleResult.isApprovalNeeded());
        assertFalse(actualFetchRuleResult.isDraftStatus());
        assertFalse(actualFetchRuleResult.isExchange());
        assertFalse(actualFetchRuleResult.isExchangeCreatedAndCancelled());
        assertFalse(actualFetchRuleResult.isExchangeExpired());
        assertFalse(actualFetchRuleResult.isExchangeOnlyCTA());
        assertFalse(actualFetchRuleResult.isFetchExistingReturnResponse());
        assertFalse(actualFetchRuleResult.isFraud());
        assertTrue(actualFetchRuleResult.getDoRefund());
        assertTrue(actualFetchRuleResult.getIsReturnable());
        assertTrue(actualFetchRuleResult.isWarrantyActive());
    }

    /**
     * Test {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}.
     * <ul>
     *   <li>Given {@link RefundRules} {@link RefundRules#getDoRefund()} return {@code true}.</li>
     *   <li>Then calls {@link RefundRules#getAction()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundRuleWrapperImpl#fetchRule(DecisionTableRefundDTO)}
     */
    @Test
    @DisplayName("Test fetchRule(DecisionTableRefundDTO); given RefundRules getDoRefund() return 'true'; then calls getAction()")
    @Tag("MaintainedByDiffblue")
    void testFetchRule_givenRefundRulesGetDoRefundReturnTrue_thenCallsGetAction() {
        // Arrange
        when(iMvcService.getMvcOrdersResponse(Mockito.<String>any(), Mockito.<String>any())).thenReturn(new MvcOrderDTO());
        RefundRules refundRules = mock(RefundRules.class);
        when(refundRules.getDoRefund()).thenReturn(true);
        when(refundRules.getIsReturnable()).thenReturn(true);
        when(refundRules.getOverrideWarrantyPeriod()).thenReturn(false);
        when(refundRules.getId()).thenReturn(1);
        when(refundRules.getReturnEligibilityPeriod()).thenReturn(1);
        when(refundRules.getAction()).thenReturn("Action");
        when(refundRules.getExchangeOrderDispatch()).thenReturn("Exchange Order Dispatch");
        when(refundRules.getRefundDispatch()).thenReturn("Refund Dispatch");
        when(refundRules.getRefundMethod()).thenReturn("Refund Method");
        doNothing().when(refundRules).setAction(Mockito.<String>any());
        doNothing().when(refundRules).setAmountFrom(Mockito.<Long>any());
        doNothing().when(refundRules).setAmountTill(Mockito.<Long>any());
        doNothing().when(refundRules).setBlacklistedPhoneNumbers(Mockito.<Boolean>any());
        doNothing().when(refundRules).setBlacklistedPincodes(Mockito.<Boolean>any());
        doNothing().when(refundRules).setCategory(Mockito.<String>any());
        doNothing().when(refundRules).setCountryCode(Mockito.<String>any());
        doNothing().when(refundRules).setCustomerScoreFrom(Mockito.<Integer>any());
        doNothing().when(refundRules).setCustomerScoreTo(Mockito.<Integer>any());
        doNothing().when(refundRules).setDoRefund(Mockito.<Boolean>any());
        doNothing().when(refundRules).setDraftReturnMethod(Mockito.<String>any());
        doNothing().when(refundRules).setExchangeAllowed(Mockito.<Integer>any());
        doNothing().when(refundRules).setExchangeOrderDispatch(Mockito.<String>any());
        doNothing().when(refundRules).setId(Mockito.<Integer>any());
        doNothing().when(refundRules).setInsurancePolicy(Mockito.<Boolean>any());
        doNothing().when(refundRules).setIsAccessoryMissing(Mockito.<Boolean>any());
        doNothing().when(refundRules).setIsBranded(Mockito.<Boolean>any());
        doNothing().when(refundRules).setIsLastPiece(Mockito.<Boolean>any());
        doNothing().when(refundRules).setIsLensOnly(Mockito.<Boolean>any());
        doNothing().when(refundRules).setIsPsuedoGatepass(Mockito.<Boolean>any());
        doNothing().when(refundRules).setIsQcPass(Mockito.<Boolean>any());
        doNothing().when(refundRules).setIsReturnable(Mockito.<Boolean>any());
        doNothing().when(refundRules).setNavChannel(Mockito.<String>any());
        doNothing().when(refundRules).setOverrideWarrantyPeriod(Mockito.<Boolean>any());
        doNothing().when(refundRules).setPaymentMethod(Mockito.<String>any());
        doNothing().when(refundRules).setRefundDispatch(Mockito.<String>any());
        doNothing().when(refundRules).setRefundMethod(Mockito.<String>any());
        doNothing().when(refundRules).setReturnEligibilityPeriod(Mockito.<Integer>any());
        doNothing().when(refundRules).setReturnInitiatedSource(Mockito.<String>any());
        doNothing().when(refundRules).setReturnReasons(Mockito.<String>any());
        doNothing().when(refundRules).setReverseType(Mockito.<String>any());
        doNothing().when(refundRules).setStoreScoreFrom(Mockito.<Integer>any());
        doNothing().when(refundRules).setStoreScoreTo(Mockito.<Integer>any());
        doNothing().when(refundRules).setTriggerPoint(Mockito.<String>any());
        doNothing().when(refundRules).setWarantyFrom(Mockito.<Integer>any());
        doNothing().when(refundRules).setWarantyTo(Mockito.<Integer>any());
        refundRules.setAction("Action");
        refundRules.setAmountFrom(10L);
        refundRules.setAmountTill(10L);
        refundRules.setBlacklistedPhoneNumbers(true);
        refundRules.setBlacklistedPincodes(true);
        refundRules.setCategory("Category");
        refundRules.setCountryCode("GB");
        refundRules.setCustomerScoreFrom(3);
        refundRules.setCustomerScoreTo(3);
        refundRules.setDoRefund(true);
        refundRules.setDraftReturnMethod("Draft Return Method");
        refundRules.setExchangeAllowed(1);
        refundRules.setExchangeOrderDispatch("Exchange Order Dispatch");
        refundRules.setId(1);
        refundRules.setInsurancePolicy(true);
        refundRules.setIsAccessoryMissing(true);
        refundRules.setIsBranded(true);
        refundRules.setIsLastPiece(true);
        refundRules.setIsLensOnly(true);
        refundRules.setIsPsuedoGatepass(true);
        refundRules.setIsQcPass(true);
        refundRules.setIsReturnable(true);
        refundRules.setNavChannel("Nav Channel");
        refundRules.setOverrideWarrantyPeriod(true);
        refundRules.setPaymentMethod("Payment Method");
        refundRules.setRefundDispatch("Refund Dispatch");
        refundRules.setRefundMethod("Refund Method");
        refundRules.setReturnEligibilityPeriod(1);
        refundRules.setReturnInitiatedSource("Return Initiated Source");
        refundRules.setReturnReasons("Just cause");
        refundRules.setReverseType("Reverse Type");
        refundRules.setStoreScoreFrom(3);
        refundRules.setStoreScoreTo(3);
        refundRules.setTriggerPoint("Trigger Point");
        refundRules.setWarantyFrom(1);
        refundRules.setWarantyTo(1);
        when(refundRulesRepository.findRuleTriggered(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.anyBoolean(),
                Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.anyBoolean(), Mockito.anyInt(),
                Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.anyBoolean(), Mockito.<String>any(), Mockito.anyBoolean(), Mockito.<String>any(), Mockito.anyDouble(),
                Mockito.anyDouble())).thenReturn(refundRules);

        DecisionTableRefundDTO decisionTableRefundDTO = new DecisionTableRefundDTO();
        decisionTableRefundDTO.setAction("Action");
        decisionTableRefundDTO.setAmountValidity(10.0d);
        decisionTableRefundDTO.setBlacklistedPhoneNumbers(true);
        decisionTableRefundDTO.setBlacklistedPincodes(true);
        decisionTableRefundDTO.setBrand("Brand");
        decisionTableRefundDTO.setCategory("Category");
        decisionTableRefundDTO.setCountryCode("GB");
        decisionTableRefundDTO.setCustomerScore(3);
        decisionTableRefundDTO.setDoRefund(true);
        decisionTableRefundDTO.setDraftReturnMethod("Draft Return Method");
        decisionTableRefundDTO.setExchangeAllowed(1);
        decisionTableRefundDTO.setExchangeCount(3);
        decisionTableRefundDTO.setExchangeOrderDispatch("Exchange Order Dispatch");
        decisionTableRefundDTO.setInsurancePolicy(true);
        decisionTableRefundDTO.setIsAccessoryMissing(true);
        decisionTableRefundDTO.setIsBranded(true);
        decisionTableRefundDTO.setIsLastPiece(true);
        decisionTableRefundDTO.setIsLensOnly(true);
        decisionTableRefundDTO.setIsPseudoGatepass(true);
        decisionTableRefundDTO.setIsQcPass(true);
        decisionTableRefundDTO.setIsReturnable(true);
        decisionTableRefundDTO.setItemWarrantyPeriod(42);
        decisionTableRefundDTO.setNavChannel("Nav Channel");
        decisionTableRefundDTO.setPaymentMethod("Payment Method");
        decisionTableRefundDTO.setRefundCount(3);
        decisionTableRefundDTO.setRefundDispatch("Refund Dispatch");
        decisionTableRefundDTO.setRefundMethod("Refund Method");
        decisionTableRefundDTO.setReturnCount(3);
        decisionTableRefundDTO.setReturnEligibiityPeriod(1);
        decisionTableRefundDTO.setReturnId(1);
        decisionTableRefundDTO.setReturnInitiatedSource("Return Initiated Source");
        decisionTableRefundDTO.setReturnPeriod(1);
        decisionTableRefundDTO.setReturnReasons("Just cause");
        decisionTableRefundDTO.setReverseType("Reverse Type");
        decisionTableRefundDTO.setRuleCalledFrom("<EMAIL>");
        decisionTableRefundDTO.setStoreScore(3);
        decisionTableRefundDTO.setTriggerPoint("Trigger Point");
        decisionTableRefundDTO.setUwItemId(1);

        // Act
        ReturnRefundResponseDTO actualFetchRuleResult = refundRuleWrapperImpl.fetchRule(decisionTableRefundDTO);

        // Assert
        verify(refundRules).getAction();
        verify(refundRules).getDoRefund();
        verify(refundRules).getExchangeOrderDispatch();
        verify(refundRules).getId();
        verify(refundRules).getIsReturnable();
        verify(refundRules, atLeast(1)).getOverrideWarrantyPeriod();
        verify(refundRules).getRefundDispatch();
        verify(refundRules).getRefundMethod();
        verify(refundRules).getReturnEligibilityPeriod();
        verify(refundRules).setAction(eq("Action"));
        verify(refundRules).setAmountFrom(eq(10L));
        verify(refundRules).setAmountTill(eq(10L));
        verify(refundRules).setBlacklistedPhoneNumbers(eq(true));
        verify(refundRules).setBlacklistedPincodes(eq(true));
        verify(refundRules).setCategory(eq("Category"));
        verify(refundRules).setCountryCode(eq("GB"));
        verify(refundRules).setCustomerScoreFrom(eq(3));
        verify(refundRules).setCustomerScoreTo(eq(3));
        verify(refundRules).setDoRefund(eq(true));
        verify(refundRules).setDraftReturnMethod(eq("Draft Return Method"));
        verify(refundRules).setExchangeAllowed(eq(1));
        verify(refundRules).setExchangeOrderDispatch(eq("Exchange Order Dispatch"));
        verify(refundRules).setId(eq(1));
        verify(refundRules).setInsurancePolicy(eq(true));
        verify(refundRules).setIsAccessoryMissing(eq(true));
        verify(refundRules).setIsBranded(eq(true));
        verify(refundRules).setIsLastPiece(eq(true));
        verify(refundRules).setIsLensOnly(eq(true));
        verify(refundRules).setIsPsuedoGatepass(eq(true));
        verify(refundRules).setIsQcPass(eq(true));
        verify(refundRules).setIsReturnable(eq(true));
        verify(refundRules).setNavChannel(eq("Nav Channel"));
        verify(refundRules).setOverrideWarrantyPeriod(eq(true));
        verify(refundRules).setPaymentMethod(eq("Payment Method"));
        verify(refundRules).setRefundDispatch(eq("Refund Dispatch"));
        verify(refundRules).setRefundMethod(eq("Refund Method"));
        verify(refundRules).setReturnEligibilityPeriod(eq(1));
        verify(refundRules).setReturnInitiatedSource(eq("Return Initiated Source"));
        verify(refundRules).setReturnReasons(eq("Just cause"));
        verify(refundRules).setReverseType(eq("Reverse Type"));
        verify(refundRules).setStoreScoreFrom(eq(3));
        verify(refundRules).setStoreScoreTo(eq(3));
        verify(refundRules).setTriggerPoint(eq("Trigger Point"));
        verify(refundRules).setWarantyFrom(eq(1));
        verify(refundRules).setWarantyTo(eq(1));
        verify(refundRulesRepository).findRuleTriggered(eq("Reverse Type"), eq("Return Initiated Source"),
                eq("Trigger Point"), eq(true), eq(true), eq(true), eq(1), eq(true), eq(true), eq(true), eq(10), eq(true), eq(1),
                eq("Draft Return Method"), eq("Nav Channel"), eq("Just cause"), eq("Category"), eq(true), eq("GB"), eq(true),
                eq("Payment Method"), eq(3.0d), eq(3.0d));
        verify(iMvcService).getMvcOrdersResponse(eq("UW_ITEM_ID"), eq("1"));
        assertEquals("Action", actualFetchRuleResult.getAction());
        assertEquals("Exchange Order Dispatch", actualFetchRuleResult.getExchangeOrderDispatch());
        assertEquals("Refund Dispatch", actualFetchRuleResult.getRefundDispatch());
        assertEquals("Refund Method", actualFetchRuleResult.getRefundMethod());
        assertNull(actualFetchRuleResult.getErrorForNotExchangeable());
        assertNull(actualFetchRuleResult.getErrorForNotRefundable());
        assertNull(actualFetchRuleResult.getErrorForNotReturnable());
        assertNull(actualFetchRuleResult.getRefundMethodRequest());
        assertNull(actualFetchRuleResult.getReturnEligibleTillDate());
        assertNull(actualFetchRuleResult.getRefundIntentCreatedAt());
        assertNull(actualFetchRuleResult.getRefundMethods());
        assertNull(actualFetchRuleResult.getRulesList());
        assertEquals(0, actualFetchRuleResult.getExchangeCount());
        assertEquals(0, actualFetchRuleResult.getRefundCount());
        assertEquals(0, actualFetchRuleResult.getReturnCount());
        assertEquals(0.0d, actualFetchRuleResult.getAmountToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getItemPrice());
        assertEquals(0.0d, actualFetchRuleResult.getItemRefundedAmount());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashPlusToRefund());
        assertEquals(0.0d, actualFetchRuleResult.getLkcashToRefund());
        assertEquals(1, actualFetchRuleResult.getId());
        assertEquals(1, actualFetchRuleResult.getReturnEligibilityPeriod());
        assertFalse(actualFetchRuleResult.isApprovalNeeded());
        assertFalse(actualFetchRuleResult.isDraftStatus());
        assertFalse(actualFetchRuleResult.isExchange());
        assertFalse(actualFetchRuleResult.isExchangeCreatedAndCancelled());
        assertFalse(actualFetchRuleResult.isExchangeExpired());
        assertFalse(actualFetchRuleResult.isExchangeOnlyCTA());
        assertFalse(actualFetchRuleResult.isFetchExistingReturnResponse());
        assertFalse(actualFetchRuleResult.isFraud());
        assertTrue(actualFetchRuleResult.getDoRefund());
        assertTrue(actualFetchRuleResult.getIsReturnable());
        assertTrue(actualFetchRuleResult.isWarrantyActive());
    }

    /**
     * Test {@link RefundRuleWrapperImpl#onCacheLoadComplete()}.
     * <p>
     * Method under test: {@link RefundRuleWrapperImpl#onCacheLoadComplete()}
     */
    @Test
    @DisplayName("Test onCacheLoadComplete()")
    @Tag("MaintainedByDiffblue")
    void testOnCacheLoadComplete() {
        // Arrange and Act
        refundRuleWrapperImpl.onCacheLoadComplete();

        // Assert
        assertTrue(refundRuleWrapperImpl.rulesLoaded);
    }
}
