package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto;
import com.lenskart.returncommon.model.dto.RuleContextDTO;
import com.lenskart.returncommon.model.enums.ActionType;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.enums.RuleConditionType;
import com.lenskart.returncommon.model.response.CancelAndConvertEventRuleResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

class EventDecisionAlgorithmImplTest {

    private EventDecisionAlgorithmImpl service;

    @BeforeEach
    void setUp() {
        service = new EventDecisionAlgorithmImpl();
    }

    private Map<PaymentMode, List<CancelAndConvertRuleDto>> buildEventRules() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = new HashMap<>();

        eventRules.put(PaymentMode.EXCHANGE, Arrays.asList(
                new CancelAndConvertRuleDto(1, PaymentMode.EXCHANGE,
                        "{\"RTO\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(2, PaymentMode.EXCHANGE,
                        "{\"CANCELLATION\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(3, PaymentMode.EXCHANGE,
                        "{\"CANCELLATION\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(4, PaymentMode.EXCHANGE,
                        "{\"RTO\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(5, PaymentMode.EXCHANGE,
                        "{\"RTO\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(6, PaymentMode.EXCHANGE,
                        "{\"CANCELLATION\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(7, PaymentMode.EXCHANGE,
                        "{\"CANCELLATION\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(8, PaymentMode.EXCHANGE,
                        "{\"RTO\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(9, PaymentMode.EXCHANGE,
                        "{\"RTO\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(10, PaymentMode.EXCHANGE,
                        "{\"CANCELLATION\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(11, PaymentMode.EXCHANGE,
                        "{\"RETURN\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(12, PaymentMode.EXCHANGE,
                        "{\"RETURN\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(13, PaymentMode.EXCHANGE,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(14, PaymentMode.EXCHANGE,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(15, PaymentMode.EXCHANGE,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(16, PaymentMode.EXCHANGE,
                        "{\"FAST_REFUND\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(17, PaymentMode.EXCHANGE,
                        "{\"FAST_REFUND\":true,\"IS_CASH_PAYMENT_INVOLVED\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(18, PaymentMode.EXCHANGE,
                        "{\"FAST_REFUND\":true,\"SC_REFUNDED_IS_EQUAL_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(19, PaymentMode.EXCHANGE,
                        "{\"FAST_REFUND\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(20, PaymentMode.EXCHANGE,
                        "{\"FAST_REFUND\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(21, PaymentMode.EXCHANGE,
                        "{\"FAST_REFUND\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"), // when extra is paid through store credit
                new CancelAndConvertRuleDto(22, PaymentMode.EXCHANGE,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"), // when extra is paid through store credit
                new CancelAndConvertRuleDto(23, PaymentMode.EXCHANGE,
                        "{\"CANCELLATION\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"), // when extra is paid through store credit
                new CancelAndConvertRuleDto(25, PaymentMode.EXCHANGE,
                        "{\"RTO\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}") // when extra is paid through store credit


        ));

        eventRules.put(PaymentMode.POSTPAID, Arrays.asList(
                new CancelAndConvertRuleDto(11, PaymentMode.POSTPAID,
                        "{\"RTO\":true}",
                        "{\"EXCHANGE\": false,\"CNC\": false}"),
                new CancelAndConvertRuleDto(12, PaymentMode.POSTPAID,
                        "{\"CANCELLATION\":true}",
                        "{\"EXCHANGE\": false,\"CNC\": false}"),
                new CancelAndConvertRuleDto(13, PaymentMode.POSTPAID,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}")
        ));

        eventRules.put(PaymentMode.PREPAID, Arrays.asList(
                new CancelAndConvertRuleDto(14, PaymentMode.PREPAID,
                        "{\"RTO\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(15, PaymentMode.PREPAID,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(16, PaymentMode.PREPAID,
                        "{\"CANCELLATION\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(17, PaymentMode.PREPAID,
                        "{\"CANCELLATION\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"CASHPAYMENT\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(18, PaymentMode.PREPAID,
                        "{\"RTO\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"CASHPAYMENT\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(19, PaymentMode.PREPAID,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"CASHPAYMENT\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(20, PaymentMode.PREPAID,
                        "{\"RTO\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(21, PaymentMode.PREPAID,
                        "{\"RETURN\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(22, PaymentMode.PREPAID,
                        "{\"CANCELLATION\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(23, PaymentMode.PREPAID,
                        "{\"FAST_REFUND\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true,\"IS_DATE_WITHIN_PG_ALLOWANCE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": true}"),
                new CancelAndConvertRuleDto(24, PaymentMode.PREPAID,
                        "{\"FAST_REFUND\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"CASHPAYMENT\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}"),
                new CancelAndConvertRuleDto(25, PaymentMode.PREPAID,
                        "{\"FAST_REFUND\":true,\"SC_REFUNDED_IS_GREATER_THAN_SC_PAID\":true,\"IS_PAYMENT_DONE\":true}",
                        "{\"EXCHANGE\": true,\"CNC\": false}")
        ));

        return eventRules;
    }

    @Test
    void testGetActionsResult1() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RTO, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true,
                RuleConditionType.IS_PAYMENT_DONE, true,
                RuleConditionType.IS_DATE_WITHIN_PG_ALLOWANCE, true
        ));

        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.PREPAID, context, eventRules);

        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be true");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be true");
    }

    @Test
    void testGetActionsResult2() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RETURN, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true,
                RuleConditionType.IS_PAYMENT_DONE, true,
                RuleConditionType.IS_DATE_WITHIN_PG_ALLOWANCE, true
        ));

        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.PREPAID, context, eventRules);

        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be true");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be true");
    }

    @Test
    void testGetActionsResult3() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RETURN, true,
                RuleConditionType.SC_REFUNDED_IS_EQUAL_SC_PAID, true,
                RuleConditionType.IS_PAYMENT_DONE, true,
                RuleConditionType.IS_DATE_WITHIN_PG_ALLOWANCE, true
        ));

        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.PREPAID, context, eventRules);
        assertNull(response.getActionRuleContextDTO(), "Should return null when no matching rule found");
    }

    @Test
    void testGetActionsResult4() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RTO, true
        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.POSTPAID, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult5() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.CANCELLATION, true
        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.POSTPAID, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult6() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RTO, true,
                RuleConditionType.CASHPAYMENT, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.PREPAID, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult7() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RETURN, true,
                RuleConditionType.CASHPAYMENT, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.PREPAID, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult8() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RETURN, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.POSTPAID, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult9() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RETURN, true,
                RuleConditionType.CASHPAYMENT, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.PREPAID, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult10() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RTO, true,
                RuleConditionType.IS_CASH_PAYMENT_INVOLVED, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.EXCHANGE, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult11() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.CANCELLATION, true,
                RuleConditionType.IS_CASH_PAYMENT_INVOLVED, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.EXCHANGE, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult12() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RTO, true,
                RuleConditionType.IS_CASH_PAYMENT_INVOLVED, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.EXCHANGE, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult13() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RTO, true,
                RuleConditionType.IS_CASH_PAYMENT_INVOLVED, true,
                RuleConditionType.SC_REFUNDED_IS_EQUAL_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.EXCHANGE, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be true");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }

    @Test
    void testGetActionsResult14() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.RTO, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true,
                RuleConditionType.IS_PAYMENT_DONE, true,
                RuleConditionType.IS_DATE_WITHIN_PG_ALLOWANCE, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.EXCHANGE, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be true");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be true");
    }

    //when extra amount is paid through store credit, in that case there will no IS_PAYMENT_DONE flag.
    @Test
    void testGetActionsResult15() {
        Map<PaymentMode, List<CancelAndConvertRuleDto>> eventRules = buildEventRules();

        RuleContextDTO context = new RuleContextDTO(Map.of(
                RuleConditionType.CANCELLATION, true,
                RuleConditionType.SC_REFUNDED_IS_GREATER_THAN_SC_PAID, true

        ));
        CancelAndConvertEventRuleResponse response = service.getActionsResult(PaymentMode.EXCHANGE, context, eventRules);
        assertNotNull(response.getActionRuleContextDTO(), "ActionRuleContextDTO should not be null");
        assertTrue(response.getActionRuleContextDTO().getConditions().get(ActionType.EXCHANGE), "Exchange action should be false");
        assertFalse(response.getActionRuleContextDTO().getConditions().get(ActionType.CNC), "CNC action should be false");
    }
}
