package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.*;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.exception.ReturnRequestException;
import com.lenskart.returncommon.model.dto.ReturnDetailsDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundResponseDTO;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnRequest;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.RefundFeignClient;
import com.lenskart.returnservice.service.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {ReturnOrderActionServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ReturnOrderActionServiceImplTest {
    @MockBean
    private ID365FinanceService iD365FinanceService;

    @MockBean
    private InsuranceService insuranceService;

    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private INexsFacilityService iNexsFacilityService;

    @MockBean
    private IOrderSyncService iOrderSyncService;

    @MockBean
    private IOrderUtilsService iOrderUtilsService;

    @MockBean
    private IRefundRequestService iRefundRequestService;

    @MockBean
    private IReturnEventService iReturnEventService;

    @MockBean
    private IReturnOrderAddressUpdateService iReturnOrderAddressUpdateService;

    @MockBean
    private IReturnOrderItemService iReturnOrderItemService;

    @MockBean
    private IReturnOrderSaleDetailsRepository iReturnOrderSaleDetailsRepository;

    @MockBean
    private IReturnReasonService iReturnReasonService;

    @MockBean
    private IReturnRefundRuleService iReturnRefundRuleService;

    @MockBean
    private IReverseCourierDetailService iReverseCourierDetailService;

    @MockBean
    private ISystemPreferenceService iSystemPreferenceService;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private RefundFeignClient refundFeignClient;

    @MockBean
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @MockBean
    private ReturnEventRepository returnEventRepository;

    @MockBean
    private ReturnGroupRepository returnGroupRepository;

    @MockBean
    private ReturnOrderActionServiceImpl returnOrderActionServiceImpl;

    @MockBean
    private ReturnDetailItemRepository returnOrderItemRepository;

    @MockBean
    private ReturnDetailRepository returnOrderRepository;

    @MockBean
    private ReturnReasonRepository returnReasonRepository;

    @MockBean
    private ReturnUtil returnUtil;

    @MockBean
    private ReverseTrackingEventRepository reverseTrackingEventRepository;


    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#checkIsReturnOrderCancelled(ReturnDetail)}
     */
    @Test
    void testCheckIsReturnOrderCancelled() {

        // Arrange, Act and Assert
        assertFalse((new ReturnOrderActionServiceImpl()).checkIsReturnOrderCancelled(null));
    }


    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#isExisitingReturnNotRejectedCancelledReshiped(ReturnDetail)}
     */
    @Test
    void testIsExisitingReturnNotRejectedCancelledReshiped() {


        // Arrange, Act and Assert
        assertTrue((new ReturnOrderActionServiceImpl()).isExisitingReturnNotRejectedCancelledReshiped(null));
    }


    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getReturnOrderStatus(ReturnDetail)}
     */
    @Test
    void testGetReturnOrderStatus() {


        // Arrange, Act and Assert
        assertNull((new ReturnOrderActionServiceImpl()).getReturnOrderStatus((ReturnDetail) null));
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#setDraftStatusAndRefundMethodCaptured(UwOrderDTO, ReturnRefundResponseDTO)}
     */
    @Test
    void testSetDraftStatusAndRefundMethodCaptured() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();
        UwOrderDTO uwOrder = mock(UwOrderDTO.class);
        when(uwOrder.getUwItemId()).thenThrow(new RuntimeException("foo"));

        // Act and Assert
        assertThrows(RuntimeException.class, () -> returnOrderActionServiceImpl
                .setDraftStatusAndRefundMethodCaptured(uwOrder, mock(ReturnRefundResponseDTO.class)));
        verify(uwOrder).getUwItemId();
    }
    @Test
    void testCreateReturn() throws Exception {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersDTO ordersDTO = new OrdersDTO();
//        ordersDTO.setB2bRefrenceItemId("42");
//        ordersDTO.setBaseTotalInvoiced(new BigDecimal("2.3"));
        ordersDTO.setChannel("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setComments((byte) 'A');
//        ordersDTO.setCouponCode("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setCouponRuleName("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setCustomerEmail("<EMAIL>");
//        ordersDTO.setCustomerId(1L);
//        ordersDTO.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setDiscount(3);
//        ordersDTO.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO.setExtCustomerId("42");
        ordersDTO.setFacilityCode("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setGrandTotal(new BigDecimal("2.3"));
//        ordersDTO.setHubCountry("GB");
//        ordersDTO.setHubFacility("[ReturnOrderActionServiceImpl][create] request : {}");
        ordersDTO.setIncrementId(1);
//        ordersDTO.setIsLocalFittingRequired(true);
        ordersDTO.setItemId(1);
        ordersDTO.setMethod("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setOffer3orfree("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setOrderDiscountAmount(new BigDecimal("2.3"));
//        ordersDTO.setOrderGrandTotal(10.0d);
        ordersDTO.setOrderId(1);
//        ordersDTO.setOrderType("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setParentId(1);
//        ordersDTO.setPayementCapture(1);
//        ordersDTO.setPriority(1);
        ordersDTO.setProductDeliveryType("[ReturnOrderActionServiceImpl][create] request : {}");
        ordersDTO.setProductId(1);
//        ordersDTO.setProductOptions("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setQtyOrdered((short) 1);
//        ordersDTO.setRewardPoints((short) 1);
//        ordersDTO.setSaleSource("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setShipToStoreRequired(true);
//        ordersDTO.setShippingAmount(new BigDecimal("2.3"));
//        ordersDTO.setState("MD");
//        ordersDTO.setStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setStockOutStatus((byte) 'A');
        ordersDTO.setStoreId((byte) 'A');
        ordersDTO.setSubProductId(1);
//        ordersDTO.setSubtotal(new BigDecimal("2.3"));
//        ordersDTO.setTaxCollected(new BigDecimal("2.3"));
//        ordersDTO.setUnicomOrderStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setUnicomOrdercode("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setUnicomSynStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        ordersDTO.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setVirtualFacilityCode("[ReturnOrderActionServiceImpl][create] request : {}");

        ArrayList<OrdersDTO> ordersDTOList = new ArrayList<>();
        ordersDTOList.add(ordersDTO);

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setBifocalDivision("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setBrand("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setClassification("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setClassificationName("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setFitterName("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setFitting("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setJitPoStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setLastShipmentStatus("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setPolicyStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setShipmentStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("[ReturnOrderActionServiceImpl][create] request : {}");
//        uwOrderDTO.setUnicomSynStatus("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("[ReturnOrderActionServiceImpl][create] request : {}");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        ArrayList<UwOrderDTO> uwOrderDTOList = new ArrayList<>();
        uwOrderDTOList.add(uwOrderDTO);
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = mock(PurchaseOrderDetailsDTO.class);
        when(purchaseOrderDetailsDTO.getUwOrders()).thenReturn(uwOrderDTOList);
        when(purchaseOrderDetailsDTO.getOrders()).thenReturn(ordersDTOList);

        ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        returnItemDTO.setClaimInsurance(true);
        returnItemDTO.setDoRefund(true);
        returnItemDTO.setNeedApproval(true);
        returnItemDTO.setQcStatus("[ReturnOrderActionServiceImpl][create] request : {}");
        returnItemDTO.setReasons(new ArrayList<>());
        returnItemDTO.setRefundMethod("[ReturnOrderActionServiceImpl][create] request : {}");
        returnItemDTO.setRefundMethodRequest("[ReturnOrderActionServiceImpl][create] request : {}");
        returnItemDTO.setUwItemId(1);

        ArrayList<ReturnItemDTO> returnItemDTOList = new ArrayList<>();
        returnItemDTOList.add(returnItemDTO);
        ReturnCreationRequestDTO returnCreationRequest = mock(ReturnCreationRequestDTO.class);
        when(returnCreationRequest.getReturnMethod())
                .thenThrow(new RuntimeException("[ReturnOrderActionServiceImpl][create] request : {}"));
        when(returnCreationRequest.getReturnSource()).thenReturn(ReturnSourcesDTO.WEB);
        when(returnCreationRequest.getItems()).thenReturn(returnItemDTOList);

//        // Act and Assert
//        assertThrows(RuntimeException.class, () -> returnOrderActionServiceImpl.createReturn(purchaseOrderDetailsDTO,
//                returnCreationRequest, mock(ReverseCourierDetail.class), "<EMAIL>", mock(ReturnRequest.class)));
//        verify(returnCreationRequest).getItems();
//        verify(returnCreationRequest).getReturnMethod();
//        verify(returnCreationRequest).getReturnSource();
//        verify(purchaseOrderDetailsDTO).getOrders();
//        verify(purchaseOrderDetailsDTO).getUwOrders();
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createAwaitedRtoReturnOrder(ReturnRequest,  ReturnOrderRequestDTO, String, String, UwOrderDTO)}
     */
    @Test
    void testCreateAwaitedRtoReturnOrder() {


        // Arrange and Act
        CreateUpdateReturnOrderResponseDTO actualCreateAwaitedRtoReturnOrderResult = (new ReturnOrderActionServiceImpl())
                .createAwaitedRtoReturnOrder(mock(ReturnRequest.class),
                        mock(ReturnOrderRequestDTO.class), "Status", "Return Type", mock(UwOrderDTO.class));

        // Assert
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnId());
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createAwaitedRtoReturnOrder(ReturnRequest,  ReturnOrderRequestDTO, String, String, UwOrderDTO)}
     */
    @Test
    void testCreateAwaitedRtoReturnOrder2() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setAgentId("42");
        returnRequest.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRequest.setId(1);
        returnRequest.setIdentifierType("Identifier Type");
        returnRequest.setIdentifierValue("42");
        returnRequest.setIncrementId(1);
        returnRequest.setReturnIntention("Return Intention");
        returnRequest.setReturnReason("Just cause");
        returnRequest.setSource(null);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setDoRefund(true);
        returnOrderRequest.setFacility("Facility");
        returnOrderRequest.setGroupId(1L);
        returnOrderRequest.setIncrementId(1);
        returnOrderRequest.setIsDualCo(true);
        returnOrderRequest.setItems(new ArrayList<>());
        returnOrderRequest.setNewFlowFlag(1);
        returnOrderRequest.setOrderDTOs(new ArrayList<>());
        returnOrderRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnOrderRequest.setRaiseRPUatNexs(true);
        returnOrderRequest.setRaiseRPUatUnicom(true);
        returnOrderRequest.setReasonDetail("Just cause");
        returnOrderRequest.setReferenceOrderCode("Reference Order Code");
        returnOrderRequest.setRefundMethod("Refund Method");
        returnOrderRequest.setShippingPackageId("42");
        returnOrderRequest.setSource("Source");
        returnOrderRequest.setUwOrderDTOs(new ArrayList<>());

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateAwaitedRtoReturnOrderResult = returnOrderActionServiceImpl
                .createAwaitedRtoReturnOrder(returnRequest,  returnOrderRequest, "Status", "Return Type", uwOrderDTO);

        // Assert
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnId());
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createAwaitedRtoReturnOrder(ReturnRequest,  ReturnOrderRequestDTO, String, String, UwOrderDTO)}
     */
    @Test
    void testCreateAwaitedRtoReturnOrder3() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setAgentId("42");
        returnRequest.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRequest.setId(1);
        returnRequest.setIdentifierType("Identifier Type");
        returnRequest.setIdentifierValue("42");
        returnRequest.setIncrementId(1);
        returnRequest.setReturnIntention("Return Intention");
        returnRequest.setReturnReason("Just cause");
        returnRequest.setSource(null);

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("Qc Status");
        returnItemRequest.setReasonDetail("Just cause");
        returnItemRequest.setUwOrderDTO(uwOrderDTO);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setDoRefund(true);
        returnOrderRequest.setFacility("Facility");
        returnOrderRequest.setGroupId(1L);
        returnOrderRequest.setIncrementId(1);
        returnOrderRequest.setIsDualCo(true);
        returnOrderRequest.setItems(new ArrayList<>());
        returnOrderRequest.setNewFlowFlag(1);
        returnOrderRequest.setOrderDTOs(new ArrayList<>());
        returnOrderRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnOrderRequest.setRaiseRPUatNexs(true);
        returnOrderRequest.setRaiseRPUatUnicom(true);
        returnOrderRequest.setReasonDetail("Just cause");
        returnOrderRequest.setReferenceOrderCode("Reference Order Code");
        returnOrderRequest.setRefundMethod("Refund Method");
        returnOrderRequest.setShippingPackageId("42");
        returnOrderRequest.setSource("Source");
        returnOrderRequest.setUwOrderDTOs(new ArrayList<>());

        UwOrderDTO uwOrderDTO2 = new UwOrderDTO();
        uwOrderDTO2.setB2bRefrenceItemId(1);
//        uwOrderDTO2.setBarcode("Barcode");
//        uwOrderDTO2.setBifocalDivision("Bifocal Division");
        uwOrderDTO2.setBrand("Brand");
        uwOrderDTO2.setClassification("Classification");
        uwOrderDTO2.setClassificationName("Classification Name");
        uwOrderDTO2.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setEta("Eta");
//        uwOrderDTO2
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO2
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO2.setFacilityCode("Facility Code");
//        uwOrderDTO2.setFitterName("Fitter Name");
        uwOrderDTO2.setFitting("Fitting");
//        uwOrderDTO2.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO2.setIncrementId(1);
//        uwOrderDTO2.setInventoryCount(3);
//        uwOrderDTO2.setIsFulfillable("Is Fulfillable");
        uwOrderDTO2.setIsLocalFittingRequired(true);
//        uwOrderDTO2.setIsOMAFileUploaded(1);
        uwOrderDTO2.setItemId(1);
//        uwOrderDTO2
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setJitFlag("Jit Flag");
//        uwOrderDTO2.setJitPoStatus("Jit Po Status");
//        uwOrderDTO2.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setLastShipmentState("Last Shipment State");
//        uwOrderDTO2.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO2.setLensPackage("java.text");
//        uwOrderDTO2.setLenskartDiscount(10.0d);
//        uwOrderDTO2.setLenskartPlusDiscount(10.0d);
        uwOrderDTO2.setNavChannel("Nav Channel");
//        uwOrderDTO2.setNotStockOutReason(1);
//        uwOrderDTO2.setOrder_type("Order type");
        uwOrderDTO2.setParentUw(1);
//        uwOrderDTO2.setPickedByPicker(true);
//        uwOrderDTO2.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setPoId("42");
//        uwOrderDTO2.setPolicyNo("Policy No");
//        uwOrderDTO2.setPolicyStatus("Policy Status");
//        uwOrderDTO2.setPriority(1);
        uwOrderDTO2.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO2.setProductId(1);
        uwOrderDTO2.setProductSku("Product Sku");
        uwOrderDTO2.setProductValue("42");
//        uwOrderDTO2.setQcFailCnt(1);
//        uwOrderDTO2.setQcFailReason("Just cause");
//        uwOrderDTO2.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO2.setQcHold(true);
//        uwOrderDTO2.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setQccheck(1);
//        uwOrderDTO2.setReturn_(1);
        uwOrderDTO2.setShipToStoreRequired(true);
//        uwOrderDTO2.setShipment(1);
        uwOrderDTO2.setShipmentState("Shipment State");
        uwOrderDTO2.setShipmentStatus("Shipment Status");
//        uwOrderDTO2.setShippingPackageId("42");
//        uwOrderDTO2.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO2.setStatus("Status");
//        uwOrderDTO2.setStockout(1);
//        uwOrderDTO2.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO2.setStoreInventory(1);
//        uwOrderDTO2.setSwitchFacilityStatus(1);
        uwOrderDTO2.setTotalPrice(10.0d);
        uwOrderDTO2.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO2.setUnicomPriority(1);
//        uwOrderDTO2.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO2.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO2.setUwItemId(1);
//        uwOrderDTO2.setVendor("Vendor");
        uwOrderDTO2.setVsmStockout(1);
//        uwOrderDTO2.setWarehouseNotPresent(1);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateAwaitedRtoReturnOrderResult = returnOrderActionServiceImpl
                .createAwaitedRtoReturnOrder(returnRequest,  returnOrderRequest, "Status", "Return Type",
                        uwOrderDTO2);

        // Assert
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnId());
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createAwaitedRtoReturnOrder(ReturnRequest, ReturnOrderRequestDTO, String, String, UwOrderDTO)}
     */
    @Test
    void testCreateAwaitedRtoReturnOrder4() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setAgentId("42");
        returnRequest.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnRequest.setId(1);
        returnRequest.setIdentifierType("Identifier Type");
        returnRequest.setIdentifierValue("42");
        returnRequest.setIncrementId(1);
        returnRequest.setReturnIntention("Return Intention");
        returnRequest.setReturnReason("Just cause");
        returnRequest.setSource("Return Request");

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnOrderRequest = new ReturnOrderRequestDTO();
        returnOrderRequest.setDoRefund(true);
        returnOrderRequest.setFacility("Facility");
        returnOrderRequest.setGroupId(1L);
        returnOrderRequest.setIncrementId(1);
        returnOrderRequest.setIsDualCo(true);
        returnOrderRequest.setItems(new ArrayList<>());
        returnOrderRequest.setNewFlowFlag(1);
        returnOrderRequest.setOrderDTOs(new ArrayList<>());
        returnOrderRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnOrderRequest.setRaiseRPUatNexs(true);
        returnOrderRequest.setRaiseRPUatUnicom(true);
        returnOrderRequest.setReasonDetail("Just cause");
        returnOrderRequest.setReferenceOrderCode("Reference Order Code");
        returnOrderRequest.setRefundMethod("Refund Method");
        returnOrderRequest.setShippingPackageId("42");
        returnOrderRequest.setSource("Source");
        returnOrderRequest.setUwOrderDTOs(new ArrayList<>());

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateAwaitedRtoReturnOrderResult = returnOrderActionServiceImpl
                .createAwaitedRtoReturnOrder(returnRequest, returnOrderRequest, "Status", "Return Type", uwOrderDTO);

        // Assert
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnId());
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createAwaitedRtoReturnOrder(ReturnRequest, ReturnOrderRequestDTO, String, String, UwOrderDTO)}
     */
    @Test
    void testCreateAwaitedRtoReturnOrder5() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();
        ReturnRequest returnRequest = mock(ReturnRequest.class);
        when(returnRequest.getIncrementId()).thenThrow(new RuntimeException("foo"));

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateAwaitedRtoReturnOrderResult = returnOrderActionServiceImpl
                .createAwaitedRtoReturnOrder(returnRequest,  mock(ReturnOrderRequestDTO.class), "Status", "Return Type",
                        mock(UwOrderDTO.class));

        // Assert
        verify(returnRequest).getIncrementId();
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnId());
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createAwaitedRtoReturnOrder(ReturnRequest, ReturnOrderRequestDTO, String, String, UwOrderDTO)}
     */
    @Test
    void testCreateAwaitedRtoReturnOrder6() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();
        ReturnRequest returnRequest = mock(ReturnRequest.class);
        when(returnRequest.getIncrementId()).thenReturn(1);
        when(returnRequest.getSource()).thenReturn("Source");
        ReturnOrderRequestDTO returnOrderRequest = mock(ReturnOrderRequestDTO.class);
        when(returnOrderRequest.getGroupId()).thenThrow(new RuntimeException("foo"));

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateAwaitedRtoReturnOrderResult = returnOrderActionServiceImpl
                .createAwaitedRtoReturnOrder(returnRequest, returnOrderRequest, "Status", "Return Type",
                        mock(UwOrderDTO.class));

        // Assert
        verify(returnOrderRequest).getGroupId();
        verify(returnRequest).getIncrementId();
        verify(returnRequest, atLeast(1)).getSource();
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnId());
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createAwaitedRtoReturnOrder(ReturnRequest, ReturnOrderRequestDTO, String, String, UwOrderDTO)}
     */
    @Test
    void testCreateAwaitedRtoReturnOrder7() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();
        ReturnRequest returnRequest = mock(ReturnRequest.class);
        when(returnRequest.getIncrementId()).thenReturn(1);
        when(returnRequest.getSource()).thenReturn("");
        ReturnOrderRequestDTO returnOrderRequest = mock(ReturnOrderRequestDTO.class);
        when(returnOrderRequest.getGroupId()).thenThrow(new RuntimeException("foo"));

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateAwaitedRtoReturnOrderResult = returnOrderActionServiceImpl
                .createAwaitedRtoReturnOrder(returnRequest, returnOrderRequest, "Status", "Return Type",
                        mock(UwOrderDTO.class));

        // Assert
        verify(returnOrderRequest).getGroupId();
        verify(returnRequest).getIncrementId();
        verify(returnRequest).getSource();
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnId());
        assertNull(actualCreateAwaitedRtoReturnOrderResult.getReturnStatus());
    }


    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, Integer, UwOrderDTO, String)}
     */
    @Test
    void testCreateUpdateReturnOrder() throws ReturnRequestException {


        // Arrange, Act and Assert
        assertThrows(ReturnRequestException.class,
                () -> (new ReturnOrderActionServiceImpl()).createUpdateReturnOrder(mock(ReturnOrderRequestDTO.class), 1,
                        "Status", "Return Type", 1, mock(UwOrderDTO.class), "Receiving flag"));
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, Integer, UwOrderDTO, String)}
     */
    @Test
    void testCreateUpdateReturnOrder2() throws ReturnRequestException {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setGroupId(1L);
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(new ArrayList<>());
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setSource(null);

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        // Act and Assert
        assertThrows(ReturnRequestException.class, () -> returnOrderActionServiceImpl.createUpdateReturnOrder(returnRequest,
                null, "Status", "Return Type", 1, uwOrderDTO, "Receiving flag"));
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder4() {


        // Arrange and Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = (new ReturnOrderActionServiceImpl())
                .createUpdateReturnOrder(mock(ReturnOrderRequestDTO.class), 1, "Status", "Return Type",
                        "Return Order Original Status",1 );

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder5() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(new ArrayList<>());
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(null);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, null, "Status", "Return Type", "Return Order Original Status",1 );

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder6() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(new ArrayList<>());
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(null);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, 1, "Status", "Return Type", "Return Order Original Status", 1);

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder7() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(new ArrayList<>());
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(0L);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, null, "Status", "Return Type", "Return Order Original Status",1 );

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder8() {


        // Arrange and Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = (new ReturnOrderActionServiceImpl())
                .createUpdateReturnOrder(null, null, "Status", "Return Type", "Return Order Original Status",1 );

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder9() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setBifocalDivision("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setBrand("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setClassification("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setClassificationName("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setFitterName("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setFitting("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setJitPoStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setLastShipmentStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setPolicyStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setShipmentStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setUnicomSynStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        ReturnItemRequestDTO returnItemRequestDTO = new ReturnItemRequestDTO();
        returnItemRequestDTO.setItemId(1);
        returnItemRequestDTO.setQcFailReason("Just cause");
        returnItemRequestDTO.setQcStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        returnItemRequestDTO.setReasonDetail("Just cause");
        returnItemRequestDTO.setUwOrderDTO(uwOrderDTO);

        ArrayList<ReturnItemRequestDTO> items = new ArrayList<>();
        items.add(returnItemRequestDTO);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(items);
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(new ArrayList<>());
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(null);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, null, "Status", "Return Type", "Return Order Original Status",1 );

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder10() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setBifocalDivision("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setBrand("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setClassification("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setClassificationName("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setFitterName("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setFitting("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setJitPoStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setLastShipmentStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setPolicyStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setShipmentStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        uwOrderDTO.setUnicomSynStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        ReturnItemRequestDTO returnItemRequestDTO = new ReturnItemRequestDTO();
        returnItemRequestDTO.setItemId(1);
        returnItemRequestDTO.setQcFailReason("");
        returnItemRequestDTO.setQcStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        returnItemRequestDTO.setReasonDetail("Just cause");
        returnItemRequestDTO.setUwOrderDTO(uwOrderDTO);

        ArrayList<ReturnItemRequestDTO> items = new ArrayList<>();
        items.add(returnItemRequestDTO);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(items);
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(new ArrayList<>());
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(null);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, null, "Status", "Return Type", "Return Order Original Status",1 );

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder11() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersDTO ordersDTO = new OrdersDTO();
//        ordersDTO.setB2bRefrenceItemId("42");
//        ordersDTO.setBaseTotalInvoiced(new BigDecimal("2.3"));
        ordersDTO.setChannel("Channel");
//        ordersDTO.setComments((byte) 'A');
//        ordersDTO.setCouponCode("Coupon Code");
//        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        ordersDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setCustomerEmail("<EMAIL>");
//        ordersDTO.setCustomerId(1L);
//        ordersDTO.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setDiscount(3);
//        ordersDTO.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO.setExtCustomerId("42");
        ordersDTO.setFacilityCode("Facility Code");
//        ordersDTO.setGrandTotal(new BigDecimal("2.3"));
//        ordersDTO.setHubCountry("GB");
//        ordersDTO.setHubFacility("Hub Facility");
        ordersDTO.setIncrementId(1);
//        ordersDTO.setIsLocalFittingRequired(true);
        ordersDTO.setItemId(1);
        ordersDTO.setMethod("Method");
//        ordersDTO.setOffer3orfree("Offer3orfree");
//        ordersDTO.setOrderDiscountAmount(new BigDecimal("2.3"));
//        ordersDTO.setOrderGrandTotal(10.0d);
        ordersDTO.setOrderId(1);
//        ordersDTO.setOrderType("Order Type");
//        ordersDTO.setParentId(1);
//        ordersDTO.setPayementCapture(1);
//        ordersDTO.setPriority(1);
        ordersDTO.setProductDeliveryType("Product Delivery Type");
        ordersDTO.setProductId(1);
//        ordersDTO.setProductOptions("Product Options");
//        ordersDTO.setQtyOrdered((short) 1);
//        ordersDTO.setRewardPoints((short) 1);
//        ordersDTO.setSaleSource("Sale Source");
//        ordersDTO.setShipToStoreRequired(true);
//        ordersDTO.setShippingAmount(new BigDecimal("2.3"));
//        ordersDTO.setState("MD");
//        ordersDTO.setStatus("Status");
//        ordersDTO.setStockOutStatus((byte) 'A');
        ordersDTO.setStoreId((byte) 'A');
        ordersDTO.setSubProductId(1);
//        ordersDTO.setSubtotal(new BigDecimal("2.3"));
//        ordersDTO.setTaxCollected(new BigDecimal("2.3"));
//        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
//        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
//        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        ordersDTO.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");

        ArrayList<OrdersDTO> orderDTOs = new ArrayList<>();
        orderDTOs.add(ordersDTO);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(orderDTOs);
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(0L);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, 1, "Status", "Return Type", "Return Order Original Status", 1);

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder12() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersDTO ordersDTO = new OrdersDTO();
//        ordersDTO.setB2bRefrenceItemId("42");
//        ordersDTO.setBaseTotalInvoiced(new BigDecimal("2.3"));
        ordersDTO.setChannel("Channel");
//        ordersDTO.setComments((byte) 'A');
//        ordersDTO.setCouponCode("Coupon Code");
//        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        ordersDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setCustomerEmail("<EMAIL>");
//        ordersDTO.setCustomerId(1L);
//        ordersDTO.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setDiscount(3);
//        ordersDTO.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO.setExtCustomerId("42");
        ordersDTO.setFacilityCode("Facility Code");
//        ordersDTO.setGrandTotal(new BigDecimal("2.3"));
//        ordersDTO.setHubCountry("GB");
//        ordersDTO.setHubFacility("Hub Facility");
        ordersDTO.setIncrementId(1);
//        ordersDTO.setIsLocalFittingRequired(true);
        ordersDTO.setItemId(1);
        ordersDTO.setMethod("Method");
//        ordersDTO.setOffer3orfree("Offer3orfree");
//        ordersDTO.setOrderDiscountAmount(new BigDecimal("2.3"));
//        ordersDTO.setOrderGrandTotal(10.0d);
        ordersDTO.setOrderId(1);
//        ordersDTO.setOrderType("Order Type");
//        ordersDTO.setParentId(1);
//        ordersDTO.setPayementCapture(1);
//        ordersDTO.setPriority(1);
        ordersDTO.setProductDeliveryType("Product Delivery Type");
        ordersDTO.setProductId(1);
//        ordersDTO.setProductOptions("Product Options");
//        ordersDTO.setQtyOrdered((short) 1);
//        ordersDTO.setRewardPoints((short) 1);
//        ordersDTO.setSaleSource("Sale Source");
//        ordersDTO.setShipToStoreRequired(true);
//        ordersDTO.setShippingAmount(new BigDecimal("2.3"));
//        ordersDTO.setState("MD");
//        ordersDTO.setStatus("Status");
//        ordersDTO.setStockOutStatus((byte) 'A');
        ordersDTO.setStoreId((byte) 'A');
        ordersDTO.setSubProductId(1);
//        ordersDTO.setSubtotal(new BigDecimal("2.3"));
//        ordersDTO.setTaxCollected(new BigDecimal("2.3"));
//        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
//        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
//        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        ordersDTO.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");

        OrdersDTO ordersDTO2 = new OrdersDTO();
//        ordersDTO2.setB2bRefrenceItemId("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        ordersDTO2.setBaseTotalInvoiced(new BigDecimal("2.3"));
        ordersDTO2.setChannel("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setComments((byte) 'X');
//        ordersDTO2.setCouponCode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setCouponRuleName("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO2.setCustomerEmail("<EMAIL>");
//        ordersDTO2.setCustomerId(2L);
//        ordersDTO2.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO2.setDiscount(1);
//        ordersDTO2.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO2.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO2.setExtCustomerId("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
        ordersDTO2.setFacilityCode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setGrandTotal(new BigDecimal("2.3"));
//        ordersDTO2.setHubCountry("GBR");
//        ordersDTO2.setHubFacility("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
        ordersDTO2.setIncrementId(2);
//        ordersDTO2.setIsLocalFittingRequired(false);
        ordersDTO2.setItemId(2);
        ordersDTO2.setMethod("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setOffer3orfree("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setOrderDiscountAmount(new BigDecimal("2.3"));
//        ordersDTO2.setOrderGrandTotal(0.5d);
        ordersDTO2.setOrderId(2);
//        ordersDTO2.setOrderType("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setParentId(2);
//        ordersDTO2.setPayementCapture(0);
//        ordersDTO2.setPriority(0);
        ordersDTO2.setProductDeliveryType("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
        ordersDTO2.setProductId(2);
//        ordersDTO2.setProductOptions("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setQtyOrdered((short) 0);
//        ordersDTO2.setRewardPoints((short) 0);
//        ordersDTO2.setSaleSource("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setShipToStoreRequired(false);
//        ordersDTO2.setShippingAmount(new BigDecimal("2.3"));
//        ordersDTO2.setState("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: ReturnOrderRequest is: {}");
//        ordersDTO2.setStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setStockOutStatus((byte) 'X');
        ordersDTO2.setStoreId((byte) 'X');
        ordersDTO2.setSubProductId(2);
//        ordersDTO2.setSubtotal(new BigDecimal("2.3"));
//        ordersDTO2.setTaxCollected(new BigDecimal("2.3"));
//        ordersDTO2.setUnicomOrderStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setUnicomOrdercode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setUnicomSynStatus("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");
//        ordersDTO2.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO2.setVirtualFacilityCode("[ReturnOrderServiceImpl][createUpdateReturnOrder]:: returnId is: {}");

        ArrayList<OrdersDTO> orderDTOs = new ArrayList<>();
        orderDTOs.add(ordersDTO2);
        orderDTOs.add(ordersDTO);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(orderDTOs);
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(0L);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, 1, "Status", "Return Type", "Return Order Original Status", 1);

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createUpdateReturnOrder(ReturnOrderRequestDTO, Integer, String, String, String, Integer)}
     */
    @Test
    void testCreateUpdateReturnOrder13() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        OrdersDTO ordersDTO = new OrdersDTO();
//        ordersDTO.setB2bRefrenceItemId("42");
//        ordersDTO.setBaseTotalInvoiced(null);
        ordersDTO.setChannel("Channel");
//        ordersDTO.setComments((byte) 'A');
//        ordersDTO.setCouponCode("Coupon Code");
//        ordersDTO.setCouponRuleName("Coupon Rule Name");
//        ordersDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setCustomerEmail("<EMAIL>");
//        ordersDTO.setCustomerId(1L);
//        ordersDTO.setDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setDiscount(3);
//        ordersDTO.setDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setEmiCharge(new BigDecimal("2.3"));
//        ordersDTO.setExtCustomerId("42");
        ordersDTO.setFacilityCode("Facility Code");
//        ordersDTO.setGrandTotal(new BigDecimal("2.3"));
//        ordersDTO.setHubCountry("GB");
//        ordersDTO.setHubFacility("Hub Facility");
        ordersDTO.setIncrementId(1);
//        ordersDTO.setIsLocalFittingRequired(true);
        ordersDTO.setItemId(1);
        ordersDTO.setMethod("Method");
//        ordersDTO.setOffer3orfree("Offer3orfree");
//        ordersDTO.setOrderDiscountAmount(new BigDecimal("2.3"));
//        ordersDTO.setOrderGrandTotal(10.0d);
        ordersDTO.setOrderId(1);
//        ordersDTO.setOrderType("Order Type");
//        ordersDTO.setParentId(1);
//        ordersDTO.setPayementCapture(1);
//        ordersDTO.setPriority(1);
        ordersDTO.setProductDeliveryType("Product Delivery Type");
        ordersDTO.setProductId(1);
//        ordersDTO.setProductOptions("Product Options");
//        ordersDTO.setQtyOrdered((short) 1);
//        ordersDTO.setRewardPoints((short) 1);
//        ordersDTO.setSaleSource("Sale Source");
//        ordersDTO.setShipToStoreRequired(true);
//        ordersDTO.setShippingAmount(new BigDecimal("2.3"));
//        ordersDTO.setState("MD");
//        ordersDTO.setStatus("Status");
//        ordersDTO.setStockOutStatus((byte) 'A');
        ordersDTO.setStoreId((byte) 'A');
        ordersDTO.setSubProductId(1);
//        ordersDTO.setSubtotal(new BigDecimal("2.3"));
//        ordersDTO.setTaxCollected(new BigDecimal("2.3"));
//        ordersDTO.setUnicomOrderStatus("Unicom Order Status");
//        ordersDTO.setUnicomOrdercode("Unicom Ordercode");
//        ordersDTO.setUnicomSynStatus("Unicom Syn Status");
//        ordersDTO.setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        ordersDTO.setVirtualFacilityCode("Virtual Facility Code");

        ArrayList<OrdersDTO> orderDTOs = new ArrayList<>();
        orderDTOs.add(ordersDTO);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
//        ordersHeaderDTO.setPaymentCaptureReason("Just cause");
        ordersHeaderDTO.setPaymentMode("Payment Mode");
//        ordersHeaderDTO.setPayuId("42");
//        ordersHeaderDTO.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setPrepaidweb(10.0d);
//        ordersHeaderDTO.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeaderDTO.setPrescriptionMethod("Prescription Method");
//        ordersHeaderDTO.setPrescriptionType("Prescription Type");
//        ordersHeaderDTO.setQuoteId(1);
//        ordersHeaderDTO.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setStoreCredit(10.0d);
//        ordersHeaderDTO.setStoreType("Store Type");
//        ordersHeaderDTO.setTransactionId("42");
//        ordersHeaderDTO.setUrgentDelivery(true);
//        ordersHeaderDTO.setWhatsappStatus("Whatsapp Status");

        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setOrderDTOs(orderDTOs);
        returnRequest.setOrdersHeaderDTO(ordersHeaderDTO);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setUwOrderDTOs(new ArrayList<>());
        returnRequest.setGroupId(0L);
        returnRequest.setSource(null);

        // Act
        CreateUpdateReturnOrderResponseDTO actualCreateUpdateReturnOrderResult = returnOrderActionServiceImpl
                .createUpdateReturnOrder(returnRequest, 1, "Status", "Return Type", "Return Order Original Status", 1);

        // Assert
        assertNull(actualCreateUpdateReturnOrderResult.getReturnId());
        assertNull(actualCreateUpdateReturnOrderResult.getReturnStatus());
    }


    /**
     * Method under test:
     * {@link IReturnOrderActionService#getReturnDetailsByIdentifier(String, List, boolean)}
     */
    @Test
    void testGetReturnDetailsByIdentifier() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        // Act
        ReturnDetailsDTO actualReturnDetailsByIdentifier = returnOrderActionServiceImpl
                .getReturnDetailsByIdentifier("Identifier Type", new ArrayList<>(), true);

        // Assert
        assertNull(actualReturnDetailsByIdentifier.getReturnOrders());
        assertNull(actualReturnDetailsByIdentifier.getReturnOrderItems());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#getReturnDetailsByIdentifier(String, List, boolean)}
     */
    @Test
    void testGetReturnDetailsByIdentifier2() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        // Act
        ReturnDetailsDTO actualReturnDetailsByIdentifier = returnOrderActionServiceImpl.getReturnDetailsByIdentifier(null,
                new ArrayList<>(), true);

        // Assert
        assertNull(actualReturnDetailsByIdentifier.getReturnOrders());
        assertNull(actualReturnDetailsByIdentifier.getReturnOrderItems());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#getReturnDetailsByIdentifier(String, List, boolean)}
     */
    @Test
    void testGetReturnDetailsByIdentifier3() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        // Act
        ReturnDetailsDTO actualReturnDetailsByIdentifier = returnOrderActionServiceImpl.getReturnDetailsByIdentifier("",
                new ArrayList<>(), true);

        // Assert
        assertNull(actualReturnDetailsByIdentifier.getReturnOrders());
        assertNull(actualReturnDetailsByIdentifier.getReturnOrderItems());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#getReturnDetailsByIdentifier(String, List, boolean)}
     */
    @Test
    void testGetReturnDetailsByIdentifier4() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        // Act
        ReturnDetailsDTO actualReturnDetailsByIdentifier = returnOrderActionServiceImpl.getReturnDetailsByIdentifier("42",
                new ArrayList<>(), true);

        // Assert
        assertNull(actualReturnDetailsByIdentifier.getReturnOrders());
        assertNull(actualReturnDetailsByIdentifier.getReturnOrderItems());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#getReturnDetailsByIdentifier(String, List, boolean)}
     */
    @Test
    void testGetReturnDetailsByIdentifier5() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifierValues = new ArrayList<>();
        identifierValues.add("foo");

        // Act
        ReturnDetailsDTO actualReturnDetailsByIdentifier = returnOrderActionServiceImpl
                .getReturnDetailsByIdentifier("Identifier Type", identifierValues, true);

        // Assert
        assertNull(actualReturnDetailsByIdentifier.getReturnOrders());
        assertNull(actualReturnDetailsByIdentifier.getReturnOrderItems());
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#getReturnDetailsByIdentifier(String, List, boolean)}
     */
    @Test
    void testGetReturnDetailsByIdentifier6() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifierValues = new ArrayList<>();
        identifierValues.add("42");
        identifierValues.add("foo");

        // Act
        ReturnDetailsDTO actualReturnDetailsByIdentifier = returnOrderActionServiceImpl
                .getReturnDetailsByIdentifier("Identifier Type", identifierValues, true);

        // Assert
        assertNull(actualReturnDetailsByIdentifier.getReturnOrders());
        assertNull(actualReturnDetailsByIdentifier.getReturnOrderItems());
    }

    /**
//     * Method under test:
//     * {@link ReturnOrderActionServiceImpl#getIsReturnFromNewFlow(String, String)}
//     */
//    @Test
//    void testGetIsReturnFromNewFlow() {
//
//
//        // Arrange and Act
//        Map<String, Boolean> actualIsReturnFromNewFlow = (new ReturnOrderActionServiceImpl())
//                .getIsReturnFromNewFlow("Identifier Type", "42");
//
//        // Assert
//        assertEquals(1, actualIsReturnFromNewFlow.size());
//        assertFalse(actualIsReturnFromNewFlow.get("newReturnFlow"));
//    }

    /**
     * Method under test:
//     * {@link ReturnOrderActionServiceImpl#getIsReturnFromNewFlow(String, String)}
//     */
//    @Test
//    void testGetIsReturnFromNewFlow2() {
//
//
//        // Arrange and Act
//        Map<String, Boolean> actualIsReturnFromNewFlow = (new ReturnOrderActionServiceImpl())
//                .getIsReturnFromNewFlow("ORDER_ID", "42");
//
//        // Assert
//        assertEquals(1, actualIsReturnFromNewFlow.size());
//        assertFalse(actualIsReturnFromNewFlow.get("newReturnFlow"));
//    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIsReturnFromNewFlow(String, String)}
     */
//    @Test
//    void testGetIsReturnFromNewFlow3() {
//
//
//        // Arrange and Act
//        Map<String, Boolean> actualIsReturnFromNewFlow = (new ReturnOrderActionServiceImpl())
//                .getIsReturnFromNewFlow("RETURN_ID", "42");
//
//        // Assert
//        assertEquals(1, actualIsReturnFromNewFlow.size());
//        assertFalse(actualIsReturnFromNewFlow.get("newReturnFlow"));
//    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIsReturnFromNewFlow(String, String)}
     */
//    @Test
//    void testGetIsReturnFromNewFlow4() {
//
//
//        // Arrange and Act
//        Map<String, Boolean> actualIsReturnFromNewFlow = (new ReturnOrderActionServiceImpl())
//                .getIsReturnFromNewFlow("UW_ITEM_ID", "42");
//
//        // Assert
//        assertEquals(1, actualIsReturnFromNewFlow.size());
//        assertFalse(actualIsReturnFromNewFlow.get("newReturnFlow"));
//    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIsReturnFromNewFlow(String, String)}
     */
//    @Test
//    void testGetIsReturnFromNewFlow5() {
//
//
//        // Arrange and Act
//        Map<String, Boolean> actualIsReturnFromNewFlow = (new ReturnOrderActionServiceImpl())
//                .getIsReturnFromNewFlow("ORDER_ID", "newReturnFlow");
//
//        // Assert
//        assertEquals(1, actualIsReturnFromNewFlow.size());
//        assertFalse(actualIsReturnFromNewFlow.get("newReturnFlow"));
//    }

//    /**
//     * Method under test:
//     * {@link ReturnOrderActionServiceImpl#getIsReturnFromNewFlow(String, String)}
//     */
//    @Test
//    void testGetIsReturnFromNewFlow6() {
//
//
//        // Arrange and Act
//        Map<String, Boolean> actualIsReturnFromNewFlow = (new ReturnOrderActionServiceImpl())
//                .getIsReturnFromNewFlow("RETURN_ID", "newReturnFlow");
//
//        // Assert
//        assertEquals(1, actualIsReturnFromNewFlow.size());
//        assertFalse(actualIsReturnFromNewFlow.get("newReturnFlow"));
//    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIsReturnFromNewFlow(String, String)}
     */
//    @Test
//    void testGetIsReturnFromNewFlow7() {
//
//
//        // Arrange and Act
//        Map<String, Boolean> actualIsReturnFromNewFlow = (new ReturnOrderActionServiceImpl())
//                .getIsReturnFromNewFlow("UW_ITEM_ID", "newReturnFlow");
//
//        // Assert
//        assertEquals(1, actualIsReturnFromNewFlow.size());
//        assertFalse(actualIsReturnFromNewFlow.get("newReturnFlow"));
//    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIdentifierValuesMappingWithNewFlow(String, List)}
     */
    @Test
    void testGetIdentifierValuesMappingWithNewFlow() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        // Act and Assert
        assertTrue(returnOrderActionServiceImpl.getIdentifierValuesMappingWithNewFlow("Identifier Type", new ArrayList<>())
                .isEmpty());
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIdentifierValuesMappingWithNewFlow(String, List)}
     */
    @Test
    void testGetIdentifierValuesMappingWithNewFlow2() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifier = new ArrayList<>();
        identifier.add("foo");

        // Act
        Map<String, Boolean> actualIdentifierValuesMappingWithNewFlow = returnOrderActionServiceImpl
                .getIdentifierValuesMappingWithNewFlow("Identifier Type", identifier);

        // Assert
        assertEquals(1, actualIdentifierValuesMappingWithNewFlow.size());
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("foo"));
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIdentifierValuesMappingWithNewFlow(String, List)}
     */
    @Test
    void testGetIdentifierValuesMappingWithNewFlow3() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifier = new ArrayList<>();
        identifier.add("foo");
        identifier.add("foo");

        // Act
        Map<String, Boolean> actualIdentifierValuesMappingWithNewFlow = returnOrderActionServiceImpl
                .getIdentifierValuesMappingWithNewFlow("Identifier Type", identifier);

        // Assert
        assertEquals(1, actualIdentifierValuesMappingWithNewFlow.size());
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("foo"));
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIdentifierValuesMappingWithNewFlow(String, List)}
     */
    @Test
    void testGetIdentifierValuesMappingWithNewFlow4() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifier = new ArrayList<>();
        identifier.add("foo");

        // Act
        Map<String, Boolean> actualIdentifierValuesMappingWithNewFlow = returnOrderActionServiceImpl
                .getIdentifierValuesMappingWithNewFlow("ORDER_ID", identifier);

        // Assert
        assertEquals(1, actualIdentifierValuesMappingWithNewFlow.size());
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("foo"));
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIdentifierValuesMappingWithNewFlow(String, List)}
     */
    @Test
    void testGetIdentifierValuesMappingWithNewFlow5() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifier = new ArrayList<>();
        identifier.add("42");
        identifier.add("foo");

        // Act
        Map<String, Boolean> actualIdentifierValuesMappingWithNewFlow = returnOrderActionServiceImpl
                .getIdentifierValuesMappingWithNewFlow("ORDER_ID", identifier);

        // Assert
        assertEquals(2, actualIdentifierValuesMappingWithNewFlow.size());
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("42"));
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("foo"));
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIdentifierValuesMappingWithNewFlow(String, List)}
     */
    @Test
    void testGetIdentifierValuesMappingWithNewFlow6() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifier = new ArrayList<>();
        identifier.add("foo");

        // Act
        Map<String, Boolean> actualIdentifierValuesMappingWithNewFlow = returnOrderActionServiceImpl
                .getIdentifierValuesMappingWithNewFlow("RETURN_ID", identifier);

        // Assert
        assertEquals(1, actualIdentifierValuesMappingWithNewFlow.size());
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("foo"));
    }

    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getIdentifierValuesMappingWithNewFlow(String, List)}
     */
    @Test
    void testGetIdentifierValuesMappingWithNewFlow7() {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        ArrayList<String> identifier = new ArrayList<>();
        identifier.add("42");
        identifier.add("foo");

        // Act
        Map<String, Boolean> actualIdentifierValuesMappingWithNewFlow = returnOrderActionServiceImpl
                .getIdentifierValuesMappingWithNewFlow("RETURN_ID", identifier);

        // Assert
        assertEquals(2, actualIdentifierValuesMappingWithNewFlow.size());
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("42"));
        assertFalse(actualIdentifierValuesMappingWithNewFlow.get("foo"));
    }


    /**
     * Method under test:
     * {@link ReturnOrderActionServiceImpl#getReturnOrderDetails(String, String)}
     */
//    @Test
//    void testGetReturnOrderDetails() {
//
//
//        // Arrange, Act and Assert
//        assertNull((new ReturnOrderActionServiceImpl()).getReturnOrderDetails("Identifier Type", "42"));
////        assertNull((new ReturnOrderActionServiceImpl()).getReturnOrderDetails("RETURN_ID", "42").getReturnOrderDTO());
////        assertNull((new ReturnOrderActionServiceImpl()).getReturnOrderDetails("INCREMENT_ID", "42").getReturnOrderDTO());
////        assertNull((new ReturnOrderActionServiceImpl()).getReturnOrderDetails("UW_ITEM_ID", "42").getReturnOrderDTO());
//        assertNull(
//                (new ReturnOrderActionServiceImpl())
//                        .getReturnOrderDetails("RETURN_ID",
//                                "[RefundUtilServiceImpl][getReturnOrderDetails] getReturnOrderDetails for identifier-type:{},"
//                                        + " identifier:{}")
//                        .getReturnOrderDTO());
//        assertNull(
//                (new ReturnOrderActionServiceImpl())
//                        .getReturnOrderDetails("INCREMENT_ID",
//                                "[RefundUtilServiceImpl][getReturnOrderDetails] getReturnOrderDetails for identifier-type:{},"
//                                        + " identifier:{}")
//                        .getReturnOrderDTO());
//        assertNull(
//                (new ReturnOrderActionServiceImpl())
//                        .getReturnOrderDetails("UW_ITEM_ID",
//                                "[RefundUtilServiceImpl][getReturnOrderDetails] getReturnOrderDetails for identifier-type:{},"
//                                        + " identifier:{}")
//                        .getReturnOrderDTO());
//    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createReturnItem(ReturnItemRequestDTO, UwOrderDTO, Integer, String, Integer)}
     */
    @Test
    void testCreateReturnItem() throws ReturnRequestException {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();

        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setB2bRefrenceItemId(1);
//        uwOrderDTO.setBarcode("Barcode");
//        uwOrderDTO.setBifocalDivision("Bifocal Division");
        uwOrderDTO.setBrand("Brand");
        uwOrderDTO.setClassification("Classification");
        uwOrderDTO.setClassificationName("Classification Name");
        uwOrderDTO.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setEta("Eta");
//        uwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrderDTO.setFacilityCode("Facility Code");
//        uwOrderDTO.setFitterName("Fitter Name");
        uwOrderDTO.setFitting("Fitting");
//        uwOrderDTO.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        uwOrderDTO.setIncrementId(1);
//        uwOrderDTO.setInventoryCount(3);
//        uwOrderDTO.setIsFulfillable("Is Fulfillable");
        uwOrderDTO.setIsLocalFittingRequired(true);
//        uwOrderDTO.setIsOMAFileUploaded(1);
        uwOrderDTO.setItemId(1);
//        uwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setJitFlag("Jit Flag");
//        uwOrderDTO.setJitPoStatus("Jit Po Status");
//        uwOrderDTO.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setLastShipmentState("Last Shipment State");
//        uwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        uwOrderDTO.setLensPackage("java.text");
//        uwOrderDTO.setLenskartDiscount(10.0d);
//        uwOrderDTO.setLenskartPlusDiscount(10.0d);
        uwOrderDTO.setNavChannel("Nav Channel");
//        uwOrderDTO.setNotStockOutReason(1);
//        uwOrderDTO.setOrder_type("Order type");
        uwOrderDTO.setParentUw(1);
//        uwOrderDTO.setPickedByPicker(true);
//        uwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setPoId("42");
//        uwOrderDTO.setPolicyNo("Policy No");
//        uwOrderDTO.setPolicyStatus("Policy Status");
//        uwOrderDTO.setPriority(1);
        uwOrderDTO.setProductDeliveryType("Product Delivery Type");
        uwOrderDTO.setProductId(1);
        uwOrderDTO.setProductSku("Product Sku");
        uwOrderDTO.setProductValue("42");
//        uwOrderDTO.setQcFailCnt(1);
//        uwOrderDTO.setQcFailReason("Just cause");
//        uwOrderDTO.setQcFailShelf("Qc Fail Shelf");
//        uwOrderDTO.setQcHold(true);
//        uwOrderDTO.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setQccheck(1);
//        uwOrderDTO.setReturn_(1);
        uwOrderDTO.setShipToStoreRequired(true);
//        uwOrderDTO.setShipment(1);
        uwOrderDTO.setShipmentState("Shipment State");
        uwOrderDTO.setShipmentStatus("Shipment Status");
//        uwOrderDTO.setShippingPackageId("42");
//        uwOrderDTO.setSplOrderFlag("Spl Order Flag");
//        uwOrderDTO.setStatus("Status");
//        uwOrderDTO.setStockout(1);
//        uwOrderDTO.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrderDTO.setStoreInventory(1);
//        uwOrderDTO.setSwitchFacilityStatus(1);
        uwOrderDTO.setTotalPrice(10.0d);
        uwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        uwOrderDTO.setUnicomPriority(1);
//        uwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrderDTO.setUnicomSynStatus("Unicom Syn Status");
        uwOrderDTO.setUwItemId(1);
//        uwOrderDTO.setVendor("Vendor");
        uwOrderDTO.setVsmStockout(1);
//        uwOrderDTO.setWarehouseNotPresent(1);

        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setReasonDetail("Just cause");
        returnItemRequest.setUwOrderDTO(uwOrderDTO);
        returnItemRequest.setQcStatus(null);

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setB2bRefrenceItemId(1);
//        uwOrder.setBarcode("Barcode");
//        uwOrder.setBifocalDivision("Bifocal Division");
        uwOrder.setBrand("Brand");
        uwOrder.setClassification("Classification");
        uwOrder.setClassificationName("Classification Name");
        uwOrder.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setEta("Eta");
//        uwOrder
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder.setFacilityCode("Facility Code");
//        uwOrder.setFitterName("Fitter Name");
        uwOrder.setFitting("Fitting");
//        uwOrder.setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setFulfillable_time(mock(Timestamp.class));
        uwOrder.setIncrementId(1);
//        uwOrder.setInventoryCount(3);
//        uwOrder.setIsFulfillable("Is Fulfillable");
        uwOrder.setIsLocalFittingRequired(true);
//        uwOrder.setIsOMAFileUploaded(1);
        uwOrder.setItemId(1);
//        //uwOrder.setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        //uwOrder.setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setJitFlag("Jit Flag");
//        uwOrder.setJitPoStatus("Jit Po Status");
//        uwOrder.setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setLastShipmentState("Last Shipment State");
//        uwOrder.setLastShipmentStatus("Last Shipment Status");
        uwOrder.setLensPackage("java.text");
//        uwOrder.setLenskartDiscount(10.0d);
//        uwOrder.setLenskartPlusDiscount(10.0d);
        uwOrder.setNavChannel("Nav Channel");
//        uwOrder.setNotStockOutReason(1);
//        uwOrder.setOrder_type("Order type");
        uwOrder.setParentUw(1);
//        uwOrder.setPickedByPicker(true);
//        uwOrder.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setPoId("42");
//        uwOrder.setPolicyNo("Policy No");
//        uwOrder.setPolicyStatus("Policy Status");
//        uwOrder.setPriority(1);
        uwOrder.setProductDeliveryType("Product Delivery Type");
        uwOrder.setProductId(1);
        uwOrder.setProductSku("Product Sku");
        uwOrder.setProductValue("42");
//        uwOrder.setQcFailCnt(1);
//        uwOrder.setQcFailReason("Just cause");
//        uwOrder.setQcFailShelf("Qc Fail Shelf");
//        uwOrder.setQcHold(true);
//        uwOrder.setQcTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setQccheck(1);
//        uwOrder.setReturn_(1);
        uwOrder.setShipToStoreRequired(true);
//        uwOrder.setShipment(1);
        uwOrder.setShipmentState("Shipment State");
        uwOrder.setShipmentStatus("Shipment Status");
//        uwOrder.setShippingPackageId("42");
//        uwOrder.setSplOrderFlag("Spl Order Flag");
//        uwOrder.setStatus("Status");
//        uwOrder.setStockout(1);
//        uwOrder.setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        uwOrder.setStoreInventory(1);
//        uwOrder.setSwitchFacilityStatus(1);
        uwOrder.setTotalPrice(10.0d);
        uwOrder.setUnicomOrderCode("Unicom Order Code");
//        uwOrder.setUnicomPriority(1);
//        uwOrder.setUnicomShipmentStatus("Unicom Shipment Status");
//        uwOrder.setUnicomSynStatus("Unicom Syn Status");
        uwOrder.setUwItemId(1);
//        uwOrder.setVendor("Vendor");
        uwOrder.setVsmStockout(1);
//        uwOrder.setWarehouseNotPresent(1);

        // Act and Assert
        assertThrows(ReturnRequestException.class,
                () -> returnOrderActionServiceImpl.createReturnItem(returnItemRequest, uwOrder, 1, "Return Type", 1));
    }

    /**
     * Method under test:
     * {@link IReturnOrderActionService#createReturnItem(ReturnItemRequestDTO, UwOrderDTO, Integer, String, Integer)}
     */
    @Test
    void testCreateReturnItem2() throws ReturnRequestException {


        // Arrange
        ReturnOrderActionServiceImpl returnOrderActionServiceImpl = new ReturnOrderActionServiceImpl();
        ReturnItemRequestDTO returnItemRequest = mock(ReturnItemRequestDTO.class);
        UwOrderDTO uwOrder = mock(UwOrderDTO.class);
        when(uwOrder.getUwItemId()).thenThrow(new RuntimeException("Unable to Save data in ReturnOrderItem"));

        // Act and Assert
        assertThrows(ReturnRequestException.class,
                () -> returnOrderActionServiceImpl.createReturnItem(returnItemRequest, uwOrder, 1, "Return Type", 1));
        verify(uwOrder).getUwItemId();
    }
}
