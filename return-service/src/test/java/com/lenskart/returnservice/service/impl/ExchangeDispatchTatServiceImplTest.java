package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;

import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.response.ExchangeDetails;
import com.lenskart.returncommon.model.dto.PickUpAddressDetails;
import com.lenskart.returncommon.model.dto.ReturnStatusHeadingDetail;
import com.lenskart.returncommon.model.response.ReturnDetailsResponse;
import com.lenskart.returnservice.service.IReversePickupTatService;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ExchangeDispatchTatServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ExchangeDispatchTatServiceImplTest {
    @MockBean
    private IReversePickupTatService iReversePickupTatService;

    @Autowired
    private ExchangeDispatchTatServiceImpl exchangeDispatchTatServiceImpl;

    /**
     * Method under test:
     * {@link ExchangeDispatchTatServiceImpl#getExchangeIncrementId(ReturnDetailsResponse)}
     */
    @Test
    void testGetExchangeIncrementId() {
        // Arrange
        ExchangeDetails exchangeDetails = new ExchangeDetails();
        exchangeDetails.setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetails.setExchangeChildId("42");
        exchangeDetails.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetails.setExchangeParentId("42");

        UwOrderDTO exchangeUwOrderDTO = new UwOrderDTO();
        exchangeUwOrderDTO.setB2bRefrenceItemId(1);
//        exchangeUwOrderDTO.setBarcode("Barcode");
//        exchangeUwOrderDTO.setBifocalDivision("Bifocal Division");
        exchangeUwOrderDTO.setBrand("Brand");
        exchangeUwOrderDTO.setClassification("Classification");
        exchangeUwOrderDTO.setClassificationName("Classification Name");
        exchangeUwOrderDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setEta("Eta");
//        exchangeUwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO.setFacilityCode("Facility Code");
//        exchangeUwOrderDTO.setFitterName("Fitter Name");
        exchangeUwOrderDTO.setFitting("Fitting");
//        exchangeUwOrderDTO
//                .setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        exchangeUwOrderDTO.setIncrementId(1);
//        exchangeUwOrderDTO.setInventoryCount(3);
//        exchangeUwOrderDTO.setIsFulfillable("Is Fulfillable");
        exchangeUwOrderDTO.setIsLocalFittingRequired(true);
//        exchangeUwOrderDTO.setIsOMAFileUploaded(1);
        exchangeUwOrderDTO.setItemId(1);
//        exchangeUwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO
//                .setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setJitFlag("Jit Flag");
//        exchangeUwOrderDTO.setJitPoStatus("Jit Po Status");
//        exchangeUwOrderDTO
//                .setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setLastShipmentState("Last Shipment State");
//        exchangeUwOrderDTO.setLastShipmentStatus("Last Shipment Status");
        exchangeUwOrderDTO.setLensPackage("java.text");
//        exchangeUwOrderDTO.setLenskartDiscount(10.0d);
//        exchangeUwOrderDTO.setLenskartPlusDiscount(10.0d);
        exchangeUwOrderDTO.setNavChannel("Nav Channel");
//        exchangeUwOrderDTO.setNotStockOutReason(1);
//        exchangeUwOrderDTO.setOrder_type("Order type");
        exchangeUwOrderDTO.setParentUw(1);
//        exchangeUwOrderDTO.setPickedByPicker(true);
//        exchangeUwOrderDTO.setPoDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setPoId("42");

//        exchangeUwOrderDTO
//                .setStockoutTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setStoreInventory(1);
//        exchangeUwOrderDTO.setSwitchFacilityStatus(1);
        exchangeUwOrderDTO.setTotalPrice(10.0d);
        exchangeUwOrderDTO.setUnicomOrderCode("Unicom Order Code");
//        exchangeUwOrderDTO.setUnicomPriority(1);
//        exchangeUwOrderDTO.setUnicomShipmentStatus("Unicom Shipment Status");

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);

        PickUpAddressDetails pickUpAddressDetails = new PickUpAddressDetails();
        pickUpAddressDetails.setCity("Oxford");
        pickUpAddressDetails.setCountry("GB");
        pickUpAddressDetails.setCountryCode("GB");


        ReturnStatusHeadingDetail returnStatusHeading = new ReturnStatusHeadingDetail();
        returnStatusHeading.setEstimatedDate(1L);
        returnStatusHeading.setHeading("Heading");
        returnStatusHeading.setSubHeading(new ArrayList<>());

        ReturnDetailsResponse returnDetailsResponse = new ReturnDetailsResponse();
        returnDetailsResponse.setAmountToRefund(10.0d);
        returnDetailsResponse.setCourierName("Courier Name");
        returnDetailsResponse.setExchangeDetails(exchangeDetails);
        returnDetailsResponse.setExchangeDispatchPoint("Exchange Dispatch Point");
        returnDetailsResponse.setExchangeOrdersDTO(new ExchangeOrdersDTO());
        returnDetailsResponse.setExchangeStatus("Exchange Status");
        returnDetailsResponse.setExchangeTimelines(new ArrayList<>());
        returnDetailsResponse.setExchangeUwOrderDTO(exchangeUwOrderDTO);
        returnDetailsResponse.setFacilityCode("Facility Code");
        returnDetailsResponse.setGroupId(1L);
        returnDetailsResponse
                .setInitiationDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnDetailsResponse.setInsuranceApplied(true);
        returnDetailsResponse.setIsSelfDispatch(true);
        returnDetailsResponse.setItemId(1);
        returnDetailsResponse.setOrderAddressUpdates(new ArrayList<>());
        returnDetailsResponse.setOrderRefundedAmount(10.0d);
        returnDetailsResponse.setOrdersHeaderDTO(ordersHeaderDTO);
        returnDetailsResponse.setPickUpAddressDetails(pickUpAddressDetails);
        returnDetailsResponse.setPickupAttemptHistory(new ArrayList<>());
        returnDetailsResponse
                .setPickupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnDetailsResponse.setPickupStatus("Pickup Status");
        returnDetailsResponse.setReceivedAtWarehouse("true");
        returnDetailsResponse.setRefundAmount(10.0d);
        returnDetailsResponse.setRefundArn("Refund Arn");
        returnDetailsResponse.setRefundDetails(new ArrayList<>());
        returnDetailsResponse.setRefundDispatchPoint("Refund Dispatch Point");


        // Act and Assert
        assertNull(exchangeDispatchTatServiceImpl.getExchangeIncrementId(returnDetailsResponse));
    }

    /**
     * Method under test:
     * {@link ExchangeDispatchTatServiceImpl#getWareHouseProcessingTat(ReturnDetailsResponse)}
     */
    @Test
    void testGetWareHouseProcessingTat() {
        // Arrange
        ExchangeDetails exchangeDetails = new ExchangeDetails();
        exchangeDetails.setDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeDetails.setExchangeChildId("42");
        exchangeDetails.setExchangeDetailsHistories(new ArrayList<>());
        exchangeDetails.setExchangeParentId("42");

        UwOrderDTO exchangeUwOrderDTO = new UwOrderDTO();
        exchangeUwOrderDTO.setB2bRefrenceItemId(1);
//        exchangeUwOrderDTO.setBarcode("Barcode");
//        exchangeUwOrderDTO.setBifocalDivision("Bifocal Division");
        exchangeUwOrderDTO.setBrand("Brand");
        exchangeUwOrderDTO.setClassification("Classification");
        exchangeUwOrderDTO.setClassificationName("Classification Name");
        exchangeUwOrderDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setEta("Eta");
//        exchangeUwOrderDTO
//                .setExpectedDeliveryDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        exchangeUwOrderDTO.setFacilityCode("Facility Code");
//        exchangeUwOrderDTO.setFitterName("Fitter Name");
        exchangeUwOrderDTO.setFitting("Fitting");
//        exchangeUwOrderDTO
//                .setFittingTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setFulfillable_time(mock(Timestamp.class));
        exchangeUwOrderDTO.setIncrementId(1);
//        exchangeUwOrderDTO.setInventoryCount(3);
//        exchangeUwOrderDTO.setIsFulfillable("Is Fulfillable");
        exchangeUwOrderDTO.setIsLocalFittingRequired(true);
//        exchangeUwOrderDTO.setIsOMAFileUploaded(1);
        exchangeUwOrderDTO.setItemId(1);
//        exchangeUwOrderDTO
//                .setJitDispatchedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO
//                .setJitEtaUpdate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setJitFlag("Jit Flag");
//        exchangeUwOrderDTO.setJitPoStatus("Jit Po Status");
//        exchangeUwOrderDTO
//                .setJitUpdateTime(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
//        exchangeUwOrderDTO.setLastShipmentState("Last Shipment State");
//
//        exchangeUwOrderDTO.setWarehouseNotPresent(1);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
//        ordersHeaderDTO.setCurrency("GBP");
//        ordersHeaderDTO.setCustomerComments("Customer Comments");
//        ordersHeaderDTO.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeaderDTO.setEmailStatus("<EMAIL>");
//        ordersHeaderDTO.setExcludePower(1);
//        ordersHeaderDTO.setFacilityCode("Facility Code");
//        ordersHeaderDTO.setFreebieStockoutStatus(true);
//        ordersHeaderDTO.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
//        ordersHeaderDTO.setIsDualCompanyEnabled(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
//        ordersHeaderDTO.setLegalOwnerCountry("GB");
//        ordersHeaderDTO.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeaderDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeaderDTO.setLkCountry("GB");
//        ordersHeaderDTO.setMall("Mall");
//        ordersHeaderDTO.setMerchantId("42");

        PickUpAddressDetails pickUpAddressDetails = new PickUpAddressDetails();
        pickUpAddressDetails.setCity("Oxford");
        pickUpAddressDetails.setCountry("GB");
        pickUpAddressDetails.setCountryCode("GB");
        pickUpAddressDetails.setEmail("<EMAIL>");
        pickUpAddressDetails.setFirstName("Jane");

        ReturnStatusHeadingDetail returnStatusHeading = new ReturnStatusHeadingDetail();
        returnStatusHeading.setEstimatedDate(1L);
        returnStatusHeading.setHeading("Heading");
        returnStatusHeading.setSubHeading(new ArrayList<>());

        ReturnDetailsResponse returnDetailsResponse = new ReturnDetailsResponse();
        returnDetailsResponse.setAmountToRefund(10.0d);
        returnDetailsResponse.setCourierName("Courier Name");
        returnDetailsResponse.setExchangeDetails(exchangeDetails);
        returnDetailsResponse.setExchangeDispatchPoint("Exchange Dispatch Point");
        returnDetailsResponse.setExchangeOrdersDTO(new ExchangeOrdersDTO());
        returnDetailsResponse.setExchangeStatus("Exchange Status");
        returnDetailsResponse.setExchangeTimelines(new ArrayList<>());
        returnDetailsResponse.setExchangeUwOrderDTO(exchangeUwOrderDTO);
        returnDetailsResponse.setFacilityCode("Facility Code");
        returnDetailsResponse.setGroupId(1L);
        returnDetailsResponse
                .setInitiationDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnDetailsResponse.setInsuranceApplied(true);
        returnDetailsResponse.setIsSelfDispatch(true);
        returnDetailsResponse.setItemId(1);
        returnDetailsResponse.setOrderAddressUpdates(new ArrayList<>());
        returnDetailsResponse.setOrderRefundedAmount(10.0d);
        returnDetailsResponse.setOrdersHeaderDTO(ordersHeaderDTO);
        returnDetailsResponse.setPickUpAddressDetails(pickUpAddressDetails);
        returnDetailsResponse.setPickupAttemptHistory(new ArrayList<>());
        returnDetailsResponse
                .setPickupDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnDetailsResponse.setPickupStatus("Pickup Status");
        returnDetailsResponse.setReceivedAtWarehouse("true");
        returnDetailsResponse.setRefundAmount(10.0d);
        returnDetailsResponse.setRefundArn("Refund Arn");
        returnDetailsResponse.setRefundDetails(new ArrayList<>());
        returnDetailsResponse.setRefundDispatchPoint("Refund Dispatch Point");
        returnDetailsResponse.setRefundMethodRequest("Refund Method Request");


        // Act and Assert
        assertEquals(0, exchangeDispatchTatServiceImpl.getWareHouseProcessingTat(returnDetailsResponse).intValue());
    }

}
