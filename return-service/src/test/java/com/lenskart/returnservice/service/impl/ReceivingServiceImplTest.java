package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.ordermetadata.dto.*;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.exception.ReturnRequestFailException;
import com.lenskart.returncommon.model.response.NexsReturnReponseData;
import com.lenskart.returncommon.model.response.NexsReturnResponse;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.response.NexsReturnResponseMeta;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.repository.ReturnDetailItemRepository;
import com.lenskart.returnrepository.repository.ReturnGroupRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ReceivingServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ReceivingServiceImplTest {
    @MockBean
    private IReturnNexsService iReturnNexsService;

    @MockBean
    private ReturnGroupRepository returnGroupRepository;

    @MockBean
    private ReturnUnicomServiceImpl iReturnUnicomService;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReceivingServiceImpl receivingServiceImpl;

    @MockBean
    private ReturnDetailItemRepository returnOrderItemRepository;
    @MockBean
    IOrderUtilsService orderUtilsService;
    @MockBean
    IReturnOrderItemService returnOrderItemService;
    @MockBean
    private IReturnEventService returnEventService;
    @MockBean
    private IReturnRefundRuleService returnRefundRuleService;

    @MockBean
    private IReturnReasonService returnReasonService;
    @MockBean
    private IRefundUtilsService refundUtilsService;

    @MockBean
    IReturnOrderActionService returnOrderActionService;

    @MockBean
    private IKafkaService kafkaService;

    @MockBean
    private ID365FinanceService id365FinanceService;

    @MockBean
    private ReturnRequestRepository returnRequestRepository;

    /**
     * Method under test:
     * {@link ReceivingServiceImpl#returnItems(ReturnOrderRequestDTO)}
     */
    @Test
    void testReturnItems() throws Exception {
        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setGroupId(1L);
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setSource("Source");
        assertThrows(ReturnRequestFailException.class, () -> receivingServiceImpl.returnItems(returnRequest));
    }

    /**
     * Method under test:
     * {@link ReceivingServiceImpl#returnItems(ReturnOrderRequestDTO)}
     */
    @Test
    void testReturnItems2() throws Exception {
        ReturnOrderRequestDTO returnRequest = mock(ReturnOrderRequestDTO.class);
        when(returnRequest.getItems()).thenReturn(new ArrayList<>());
        when(returnRequest.getIncrementId()).thenReturn(1);
        when(returnRequest.getReferenceOrderCode()).thenReturn("Reference Order Code");
        doNothing().when(returnRequest).setDoRefund(Mockito.<Boolean>any());
        doNothing().when(returnRequest).setFacility(Mockito.<String>any());
        doNothing().when(returnRequest).setGroupId(Mockito.<Long>any());
        doNothing().when(returnRequest).setIncrementId(Mockito.<Integer>any());
        doNothing().when(returnRequest).setIsDualCo(Mockito.<Boolean>any());
        doNothing().when(returnRequest).setItems(Mockito.<List<ReturnItemRequestDTO>>any());
        doNothing().when(returnRequest).setNewFlowFlag(Mockito.<Integer>any());
        doNothing().when(returnRequest).setRaiseRPUatNexs(Mockito.<Boolean>any());
        doNothing().when(returnRequest).setRaiseRPUatUnicom(Mockito.<Boolean>any());
        doNothing().when(returnRequest).setReasonDetail(Mockito.<String>any());
        doNothing().when(returnRequest).setReferenceOrderCode(Mockito.<String>any());
        doNothing().when(returnRequest).setRefundMethod(Mockito.<String>any());
        doNothing().when(returnRequest).setShippingPackageId(Mockito.<String>any());
        doNothing().when(returnRequest).setSource(Mockito.<String>any());
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setGroupId(1L);
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setSource("Source");
        assertThrows(ReturnRequestFailException.class, () -> receivingServiceImpl.returnItems(returnRequest));
        verify(returnRequest, atLeast(1)).getIncrementId();
        verify(returnRequest).getItems();
        verify(returnRequest, atLeast(1)).getReferenceOrderCode();
        verify(returnRequest).setDoRefund(eq(true));
        verify(returnRequest).setFacility(eq("Facility"));
        verify(returnRequest).setGroupId(eq(1L));
        verify(returnRequest).setIncrementId(eq(1));
        verify(returnRequest).setIsDualCo(eq(true));
        verify(returnRequest).setItems(isA(List.class));
        verify(returnRequest).setNewFlowFlag(eq(1));
        verify(returnRequest).setRaiseRPUatNexs(eq(true));
        verify(returnRequest).setRaiseRPUatUnicom(eq(true));
        verify(returnRequest).setReasonDetail(eq("Just cause"));
        verify(returnRequest).setReferenceOrderCode(eq("Reference Order Code"));
        verify(returnRequest).setRefundMethod(eq("Refund Method"));
        verify(returnRequest).setShippingPackageId(eq("42"));
        verify(returnRequest).setSource(eq("Source"));
    }

    /**
     * Method under test:
     * {@link ReceivingServiceImpl#returnItems(ReturnOrderRequestDTO)}
     */
    @Test
    @Disabled
    void testReturnItemsWhenRPUFlagFromNexsIsTrue() throws Exception {
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        List<UwOrderDTO> uwOrderDTOS = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setUwItemId(1);
        uwOrderDTO.setUnicomOrderCode("Reference Order Code");
        uwOrderDTO.setClassification("123422");
        uwOrderDTOS.add(uwOrderDTO);
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(uwOrderDTOS).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(buildResult);
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("success");
        returnItemRequest.setReasonDetail("Just cause");

        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();
        returnItemRequestList.add(returnItemRequest);
        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setGroupId(1L);
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setRaiseRPUatNexs(true);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setSource("Source");
        returnRequest.setItems(returnItemRequestList);
        NexsReturnResponse nexsReturnResponse = new NexsReturnResponse();
        NexsReturnResponseMeta nexsReturnResponseMeta = new NexsReturnResponseMeta();
        nexsReturnResponseMeta.setCode("ALREADY_EXISTS");
        NexsReturnReponseData nexsReturnReponseData = new NexsReturnReponseData();
        nexsReturnResponse.setMeta(nexsReturnResponseMeta);
        nexsReturnResponse.setData(nexsReturnReponseData);
        when(iReturnNexsService.createReversePickupNexs(anyList(),anyString(),anyInt(),anyList())).thenReturn(nexsReturnResponse);

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        purchaseOrderDetailsDTO.setBranded(true);
        purchaseOrderDetailsDTO.setGetIsBlacklisted(true);
        purchaseOrderDetailsDTO.setIdentifierType("Identifier Type");
        purchaseOrderDetailsDTO.setIdentifierValue("42");
        //purchaseOrderDetailsDTO.setItemWiseAmountDTO(itemWiseAmountDTO);
        purchaseOrderDetailsDTO.setItemWisePrices(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrderAddressUpdateDTOs(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrders(new ArrayList<>());
        //purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        purchaseOrderDetailsDTO.setUwOrders(uwOrderDTOS);

        when(orderUtilsService.getPurchaseOrderDetails(anyString(), anyString())).thenReturn(purchaseOrderDetailsDTO);

        List<ReturnDetail> returnOrders = new ArrayList<>();
        ReturnDetail returnOrder = new ReturnDetail();
        returnOrder.setId(1234);
        returnOrders.add(returnOrder);
        when(returnOrderActionService.getReturnOrderStatus((ReturnDetail) any())).thenReturn("new_reverse_pickup");
        when(returnOrderActionService.findAllByIncrementId(anyInt())).thenReturn(returnOrders);

        ReturnDetailItem returnOrderItem = new ReturnDetailItem();
        returnOrderItem.setReturnId(123);
        when(returnOrderItemService.findByUwItemId(anyInt())).thenReturn(returnOrderItem);
        when(returnOrderActionService.findReturnOrderById(any())).thenReturn(Optional.of(returnOrder));
        Map<String,Object> returnItemsResponse = receivingServiceImpl.returnItems(returnRequest);
        assertFalse(returnItemsResponse.isEmpty());
        assertEquals(Boolean.TRUE, returnItemsResponse.get("alreadyExists"));
        assertEquals(Boolean.FALSE, returnItemsResponse.get("success"));
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        verify(responseEntity).getBody();

    }


    @Test
    @Disabled
    void testReturnItemsWhenRPUFlagFromUnicomIsTrue() throws Exception {
        FraudCustomerDTO fraudCustomerDTO = new FraudCustomerDTO();
        fraudCustomerDTO.setExchangeCount(3);
        fraudCustomerDTO.setFraud(true);
        fraudCustomerDTO.setRefundCount(3);
        fraudCustomerDTO.setReturnCount(3);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder builderResult = OrderInfoResponseDTO.builder();
        CustomerAccountInfoDTO.CustomerAccountInfoDTOBuilder rewardPointsResult = CustomerAccountInfoDTO.builder()
                .blacklistFlag(true)
                .customerId(1L)
                .email("<EMAIL>")
                .fax("Fax")
                .firstname("Jane")
                .lastname("Doe")
                .rewardPoints(1);
        CustomerAccountInfoDTO customerAccountInfoDTO = rewardPointsResult.storeCredit(new BigDecimal("2.3"))
                .telephone("**********")
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder customerAccountInfoDTOResult = builderResult
                .customerAccountInfoDTO(customerAccountInfoDTO);
        ExchangeOrdersDTO.ExchangeOrdersDTOBuilder countExchangeItemsResult = ExchangeOrdersDTO.builder()
                .countExchangeItems(3);
        ExchangeOrdersDTO exchangeOrdersDTO = countExchangeItemsResult
                .createdAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()))
                .createdBy(1)
                .exchangeIncrementId(1)
                .incrementId(1)
                .newPackageName("java.text")
                .newProductId(1)
                .packageName("java.text")
                .productId(1)
                .returnId(1)
                .updatedBy(1)
                .uwItemId(1)
                .build();
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder fraudCustomerDTOResult = customerAccountInfoDTOResult
                .exchangeOrdersDTO(exchangeOrdersDTO)
                .fraudCustomerDTO(fraudCustomerDTO);

        ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO = new ItemWiseFastRefunResponseDTO();
        itemWiseFastRefunResponseDTO.setDebitAdditionalAmount(10.0d);
        itemWiseFastRefunResponseDTO.setItemIds(new ArrayList<>());
        itemWiseFastRefunResponseDTO.setLenskartAmount(10.0d);
        itemWiseFastRefunResponseDTO.setLenskartPlusAmount(10.0d);
        itemWiseFastRefunResponseDTO.setOrderGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setRefundGrandTotal(10.0d);
        itemWiseFastRefunResponseDTO.setResponseStatus("Response Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWiseFastRefunResponseDTOResult = fraudCustomerDTOResult
                .itemWiseFastRefunResponseDTO(itemWiseFastRefunResponseDTO);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder itemWisePricesResult = itemWiseFastRefunResponseDTOResult
                .itemWisePrices(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder masterExchangeOrderPaymentMethodsResult = itemWisePricesResult
                .masterExchangeOrderPaymentMethods(new HashSet<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder orderAddressUpdatesResult = masterExchangeOrderPaymentMethodsResult
                .orderAddressUpdates(new ArrayList<>());
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersResult = orderAddressUpdatesResult.orders(new ArrayList<>());

        OrdersHeaderDTO ordersHeader = new OrdersHeaderDTO();
        ordersHeader.setAssignedMgr(true);
//        ordersHeader.setCurrency("GBP");
//        ordersHeader.setCustomerComments("Customer Comments");
//        ordersHeader.setDonationCharge(new BigDecimal("2.3"));
//        ordersHeader.setEmailStatus("<EMAIL>");
//        ordersHeader.setExcludePower(1);
//        ordersHeader.setFacilityCode("Facility Code");
//        ordersHeader.setFreebieStockoutStatus(true);
//        ordersHeader.setGiftvoucherDiscount(new BigDecimal("2.3"));
        ordersHeader.setIncrementId(1);
        ordersHeader.setIsBulkOrder(true);
//        ordersHeader.setIsDualCompanyEnabled(true);
        ordersHeader.setIsExchangeOrder(true);
//        ordersHeader.setLegalOwnerCountry("GB");
//        ordersHeader.setLenskartDiscount(new BigDecimal("2.3"));
//        ordersHeader.setLenskartPlusDiscount(new BigDecimal("2.3"));
        ordersHeader.setLkCountry("GB");
//        ordersHeader.setMall("Mall");
//        ordersHeader.setMerchantId("42");
        ordersHeader.setOrderId(1);
        ordersHeader.setPaymentCaptureFlag(1);
//        ordersHeader.setPaymentCaptureReason("Just cause");
        ordersHeader.setPaymentMode("Payment Mode");
//        ordersHeader.setPayuId("42");
//        ordersHeader.setPrepaiddiscount(new BigDecimal("2.3"));
//        ordersHeader.setPrepaidweb(10.0d);
//        ordersHeader.setPrepaidwebPg("Prepaidweb Pg");
//        ordersHeader.setPrescriptionMethod("Prescription Method");
//        ordersHeader.setPrescriptionType("Prescription Type");
//        ordersHeader.setQuoteId(1);
//        ordersHeader.setServiceCharge(new BigDecimal("2.3"));
//        ordersHeader.setStoreCredit(10.0d);
//        ordersHeader.setStoreType("Store Type");
//        ordersHeader.setTransactionId("42");
//        ordersHeader.setUrgentDelivery(true);
//        ordersHeader.setWhatsappStatus("Whatsapp Status");
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder ordersHeaderResult = ordersResult.ordersHeader(ordersHeader);
        OrderInfoResponseDTO.OrderInfoResponseDTOBuilder uwOrderAttributesResult = ordersHeaderResult
                .uwOrderAttributes(new ArrayList<>());
        List<UwOrderDTO> uwOrderDTOS = new ArrayList<>();
        UwOrderDTO uwOrderDTO = new UwOrderDTO();
        uwOrderDTO.setUwItemId(1);
        uwOrderDTO.setUnicomOrderCode("Reference Order Code");
        uwOrderDTO.setClassification("123422");
        uwOrderDTOS.add(uwOrderDTO);
        OrderInfoResponseDTO buildResult = uwOrderAttributesResult.uwOrders(uwOrderDTOS).build();
        ResponseEntity<OrderInfoResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(buildResult);
        when(orderOpsFeignClient.getOrderDetails(Mockito.<Integer>any())).thenReturn(responseEntity);

        ReturnItemRequestDTO returnItemRequest = new ReturnItemRequestDTO();
        returnItemRequest.setItemId(1);
        returnItemRequest.setQcFailReason("Just cause");
        returnItemRequest.setQcStatus("success");
        returnItemRequest.setReasonDetail("Just cause");

        ArrayList<ReturnItemRequestDTO> returnItemRequestList = new ArrayList<>();
        returnItemRequestList.add(returnItemRequest);
        ReturnOrderRequestDTO returnRequest = new ReturnOrderRequestDTO();
        returnRequest.setDoRefund(true);
        returnRequest.setFacility("Facility");
        returnRequest.setGroupId(1L);
        returnRequest.setIncrementId(1);
        returnRequest.setIsDualCo(true);
        returnRequest.setItems(new ArrayList<>());
        returnRequest.setNewFlowFlag(1);
        returnRequest.setRaiseRPUatNexs(false);
        returnRequest.setRaiseRPUatUnicom(true);
        returnRequest.setReasonDetail("Just cause");
        returnRequest.setReferenceOrderCode("Reference Order Code");
        returnRequest.setRefundMethod("Refund Method");
        returnRequest.setShippingPackageId("42");
        returnRequest.setSource("Source");
        returnRequest.setItems(returnItemRequestList);
        returnRequest.setRtoItem(true);
        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        purchaseOrderDetailsDTO.setBranded(true);
        purchaseOrderDetailsDTO.setGetIsBlacklisted(true);
        purchaseOrderDetailsDTO.setIdentifierType("Identifier Type");
        purchaseOrderDetailsDTO.setIdentifierValue("42");
        //purchaseOrderDetailsDTO.setItemWiseAmountDTO(itemWiseAmountDTO);
        purchaseOrderDetailsDTO.setItemWisePrices(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrderAddressUpdateDTOs(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrders(new ArrayList<>());
        //purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        purchaseOrderDetailsDTO.setUwOrders(uwOrderDTOS);

        when(orderUtilsService.getPurchaseOrderDetails(anyString(), anyString())).thenReturn(purchaseOrderDetailsDTO);

        List<ReturnDetail> returnOrders = new ArrayList<>();
        ReturnDetail returnOrder = new ReturnDetail();
        returnOrder.setId(1234);
        returnOrders.add(returnOrder);
        when(returnOrderActionService.getReturnOrderStatus((ReturnDetail) any())).thenReturn("new_reverse_pickup");
        when(returnOrderActionService.findAllByIncrementId(anyInt())).thenReturn(returnOrders);

        ReturnDetailItem returnOrderItem = new ReturnDetailItem();
        returnOrderItem.setReturnId(123);
        when(returnOrderItemService.findByUwItemId(anyInt())).thenReturn(returnOrderItem);
        when(returnOrderActionService.findReturnOrderById(any())).thenReturn(Optional.of(returnOrder));
        String jsonString = "{\"successful\":true,\"reversePickupCode\":\"newReturn\"}";
        ResponseEntity<String> responseEntity1 = new ResponseEntity<>(jsonString, HttpStatus.OK);
        when(iReturnUnicomService.createReversePickup(returnItemRequestList,null,returnRequest.getReferenceOrderCode(), returnRequest.getFacility(),returnRequest.getRtoItem())).thenReturn(responseEntity1);
        Map<String,Object> returnItemsResponse = receivingServiceImpl.returnItems(returnRequest);
        assertFalse(returnItemsResponse.isEmpty());
        assertEquals(Boolean.FALSE, returnItemsResponse.get("alreadyExists"));
        assertEquals(Boolean.TRUE, returnItemsResponse.get("success"));
        verify(orderOpsFeignClient).getOrderDetails(eq(1));
        verify(responseEntity).getBody();
    }
}
