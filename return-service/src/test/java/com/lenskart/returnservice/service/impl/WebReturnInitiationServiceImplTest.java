package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.ccautils.util.CosmosEventPushUtil;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returnrepository.entity.ReverseCourierMapping;
import com.lenskart.returnrepository.entity.ReversePickupPincode;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.factory.cosmospayloadfactory.CosmosEventPayloadFactory;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {WebReturnInitiationServiceImpl.class})
@ExtendWith(SpringExtension.class)
class WebReturnInitiationServiceImplTest {
    @MockBean
    private ICommunicationService iCommunicationService;

    @MockBean
    private CosmosEventPushUtil cosmosEventPushUtil;

    @MockBean
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @MockBean
    private QCWarrantyRuleEngineRepository qcWarrantyRuleEngineRepository;

    @MockBean
    private CosmosEventPayloadFactory cosmosEventPayloadFactory;

    @MockBean
    private IExchangeOrderService iExchangeOrderService;

    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private INexsFacilityService iNexsFacilityService;

    @MockBean
    private IRefundUtilsService iRefundUtilsService;

    @MockBean
    private IReturnEventService iReturnEventService;

    @MockBean
    private IReturnOrderActionService iReturnOrderActionService;

    @MockBean
    private IReturnRefundRuleService iReturnRefundRuleService;

    @MockBean
    private IReverseCourierDetailService iReverseCourierDetailService;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private PrimaryReturnReasonsRepository primaryReturnReasonsRepository;

    @MockBean
    private RedisTemplate redisTemplate;

    @MockBean
    private ReturnDetailRepository returnOrderRepository;

    @MockBean
    private ReturnRequestRepository returnRequestRepository;

    @MockBean
    private ReturnUtil returnUtil;

    @MockBean
    private ReverseCourierMappingRepository reverseCourierMappingRepository;

    @MockBean
    private ReversePickupPincodeRepository reversePickupPincodeRepository;

    @MockBean
    private SecondaryReturnReasonRepository secondaryReturnReasonRepository;

    @Autowired
    private WebReturnInitiationServiceImpl webReturnInitiationServiceImpl;

    @MockBean
    private IReturnCreationService returnCreationService;

    /**
     * Method under test:
     * {@link WebReturnInitiationServiceImpl#assignReverseCourier(Integer, Integer, Boolean, Integer, int, QCType, boolean, OrderInfoResponseDTO)}
     */
    @Test
    void testAssignReverseCourier() throws Exception {
        ReverseCourierMapping reverseCourierMapping = new ReverseCourierMapping();
        reverseCourierMapping.setCourier("Courier");
        reverseCourierMapping.setCpId(1);
        reverseCourierMapping.setCpName("Cp Name");
        reverseCourierMapping.setId(1);
        reverseCourierMapping
                .setUpdatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        when(reverseCourierMappingRepository.findByCourier(Mockito.<String>any())).thenReturn(reverseCourierMapping);

        ReversePickupPincode reversePickupPincode = new ReversePickupPincode();
        reversePickupPincode.setCourier("Courier");
        reversePickupPincode.setCpId(1);
        reversePickupPincode.setCpName("Cp Name");
        reversePickupPincode.setIsQcAtDoorstep(true);
        reversePickupPincode.setPincode(1);
        reversePickupPincode.setPriority(1);
        reversePickupPincode.setStatus("Status");
        reversePickupPincode.setTat(1);
        when(reversePickupPincodeRepository.findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAsc(
                Mockito.<Integer>any(), Mockito.<List<String>>any(), anyInt(), anyInt())).thenReturn(reversePickupPincode);
        ReverseCourierDetail actualAssignReverseCourierResult = webReturnInitiationServiceImpl.assignReverseCourier(1, 1,
                true, 1, 2, QCType.QC_REQUIRED, true, new OrderInfoResponseDTO());
        verify(reverseCourierMappingRepository).findByCourier(Mockito.<String>any());
        verify(reversePickupPincodeRepository).findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAsc(
                Mockito.<Integer>any(), Mockito.<List<String>>any(), anyInt(), anyInt());
        assertEquals("Courier", actualAssignReverseCourierResult.getCourier());
        assertEquals(1, actualAssignReverseCourierResult.getCpId().intValue());
        assertEquals(1, actualAssignReverseCourierResult.getPincode().intValue());
        assertEquals(1, actualAssignReverseCourierResult.getTat().intValue());
        assertEquals(QCType.QC_REQUIRED, actualAssignReverseCourierResult.getCourierAssignmentType());
        assertTrue(actualAssignReverseCourierResult.getStatus());
        assertTrue(actualAssignReverseCourierResult.isQcAtDoorStepEligibleByCourierDetails());
    }
}
