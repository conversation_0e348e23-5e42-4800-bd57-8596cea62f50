package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;

import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {DateUtil.class})
@ExtendWith(SpringExtension.class)
class DateUtilTest {
    @Autowired
    private DateUtil dateUtil;

    /**
     * Method under test: {@link DateUtil#convertDateToISO8601Format(Date)}
     */
    @Test
    void testConvertDateToISO8601Format() {
        // Arrange, Act and Assert
        assertEquals("1970-01-01T00:00:00.000Z", dateUtil.convertDateToISO8601Format(
                Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant())));
    }

    /**
     * Method under test: {@link DateUtil#convertDateToISO8601Format(java.util.Date)}
     */
    @Test
    void testConvertDateToISO8601Format2() {
        // Arrange
        java.sql.Date date = mock(java.sql.Date.class);
        when(date.getTime()).thenReturn(10L);

        // Act and Assert
        assertEquals("1970-01-01T00:00:00.010Z", dateUtil.convertDateToISO8601Format(date));
        verify(date).getTime();
    }

    /**
     * Method under test: {@link DateUtil#convertDateToISO8601Format(Date)}
     */
    @Test
    void testConvertDateToISO8601Format3() {

        // Arrange and Act
        dateUtil.convertDateToISO8601Format(null);
    }

    /**
     * Method under test: {@link DateUtil#getFormattedDate(java.util.Date, Integer)}
     */
    @Test
    void testGetFormattedDate2() {
        // Arrange
        java.sql.Date date = mock(java.sql.Date.class);
        when(date.getTime()).thenReturn(10L);

        // Act
        dateUtil.getFormattedDate(date, 1);

        // Assert
        verify(date).getTime();
    }
}

