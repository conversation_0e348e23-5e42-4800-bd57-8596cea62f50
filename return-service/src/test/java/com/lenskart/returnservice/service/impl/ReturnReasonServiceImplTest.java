package com.lenskart.returnservice.service.impl;

import com.lenskart.returncommon.model.dto.ReturnReasonDTO;
import com.lenskart.returncommon.model.response.ReturnReasonsResponse;
import com.lenskart.returnrepository.entity.ApprovalReasons;
import com.lenskart.returnrepository.entity.PrimaryReturnReasons;
import com.lenskart.returnrepository.entity.ReturnReasonsMapping;
import com.lenskart.returnrepository.entity.SecondaryReturnReason;
import com.lenskart.returnrepository.repository.ExchangeApprovalReasonRepository;
import com.lenskart.returnrepository.repository.IExchangeReturnReasonRepository;
import com.lenskart.returnservice.service.ICacheDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ReturnReasonServiceImplTest {

    @InjectMocks
    ReturnReasonServiceImpl returnReasonService;
    @Mock
    RedisTemplate redisTemplate;

    @Mock
    IExchangeReturnReasonRepository exchangeReturnReasonRepository;
    @Mock
    ExchangeApprovalReasonRepository exchangeApprovalReasonRepository;

    @Mock
    ICacheDataService cacheDataService;
    private static final String PREFIX_REASON = "REASON:";
    String platform = "";
    String categoryId = "";
    String cacheKey = "";

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getReturnReasonsWhenEmptyReturnReasonMappingForPlateformAndCategoryIdTest() {
        platform = "return";
        categoryId = "1234";
        cacheKey = PREFIX_REASON + platform + categoryId;
        List<ReturnReasonsMapping> returnReasonsMappings = new ArrayList<>();
        when(redisTemplate.hasKey(cacheKey)).thenReturn(false);
        when(exchangeReturnReasonRepository.getReturnReasonsMappingsByPlatformAndClassification(platform, Integer.valueOf(categoryId))).thenReturn(returnReasonsMappings);
        ReturnReasonsResponse response = returnReasonService.getReturnReasons(platform, categoryId);
        assertTrue(Objects.nonNull(response.getError_response()));
    }

    @Test
    public void getReturnReasonsWhenEmptyPlatformTest() {
        platform = "return";
        cacheKey = PREFIX_REASON + platform + categoryId;
        List<ReturnReasonsMapping> returnReasonsMappings = new ArrayList<>();
        ReturnReasonsResponse response = returnReasonService.getReturnReasons(platform, categoryId);
        assertEquals(response.getError_response().getError_msg(), "Invalid Category.");
    }

    @Test
    public void getReturnReasonsWhenEmptyCategoryIdTest() {
        categoryId = "1234";
        cacheKey = PREFIX_REASON + platform + categoryId;
        List<ReturnReasonsMapping> returnReasonsMappings = new ArrayList<>();
        ReturnReasonsResponse response = returnReasonService.getReturnReasons(platform, categoryId);
        assertEquals(response.getError_response().getError_msg(), "Invalid Platform.");
    }

    @Test
    public void getReturnReasonsTest() {
        platform = "return";
        categoryId = "1234";
        cacheKey = PREFIX_REASON + platform + categoryId;
        when(redisTemplate.hasKey(cacheKey)).thenReturn(false);
        List<ReturnReasonsMapping> returnReasonsMappings = new ArrayList<>();
        ReturnReasonsMapping returnReasonsMapping = new ReturnReasonsMapping();
        PrimaryReturnReasons primaryReturnReasons = new PrimaryReturnReasons();
        primaryReturnReasons.setPrimaryReasonId(2);
        primaryReturnReasons.setReason("frame broke");
        primaryReturnReasons.setType("reverse");
        primaryReturnReasons.setIsInsuranceReason(true);
        primaryReturnReasons.setUpdatedAt(new Timestamp(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime()));
        primaryReturnReasons.setCreatedAt(new Timestamp(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime()));
        SecondaryReturnReason secondaryReturnReason = new SecondaryReturnReason();
        secondaryReturnReason.setSecondaryReasonId(3);
        secondaryReturnReason.setType("reverse2");
        secondaryReturnReason.setReason("lens crack");
        secondaryReturnReason.setCreatedAt(new Timestamp(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime()));
        secondaryReturnReason.setUpdatedAt(new Timestamp(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime()));
        returnReasonsMapping.setPrimaryReturnReasons(primaryReturnReasons);
        returnReasonsMapping.setId(1);
        returnReasonsMapping.setClassificationId(1234);
        returnReasonsMapping.setPlatform("POS");
        returnReasonsMapping.setSecondaryReturnReasons(secondaryReturnReason);
        returnReasonsMapping.setIsInsuranceReason(true);
        returnReasonsMapping.setCreatedAt(new Timestamp(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime()));
        returnReasonsMappings.add(returnReasonsMapping);
        when(exchangeReturnReasonRepository.getReturnReasonsMappingsByPlatformAndClassification(platform, Integer.valueOf(categoryId))).thenReturn(returnReasonsMappings);
        ReturnReasonsResponse response = returnReasonService.getReturnReasons(platform, categoryId);
        assertFalse(CollectionUtils.isEmpty(response.return_reasons));
    }

    @Test
    @Disabled
    public void getApprovalReasonsTest(){
        List<ApprovalReasons> approvalReasons = new ArrayList<>();
        cacheKey = "REASON:";
        ApprovalReasons approvalReasons1 = new ApprovalReasons();
        approvalReasons1.setReason("test123");
        approvalReasons1.setId(1);
        approvalReasons1.setCreatedTime(new Date());
        approvalReasons1.setUpdateTime(new Date());
        approvalReasons.add(approvalReasons1);
        List<ReturnReasonDTO> returnReasonDTOS = null;
        ReturnReasonsResponse returnReasonsResponse = new ReturnReasonsResponse();
        when(exchangeApprovalReasonRepository.findAll()).thenReturn(approvalReasons);
        when(redisTemplate.hasKey(cacheKey)).thenReturn(false);
        returnReasonsResponse = returnReasonService.getApprovalReasons();
        assertEquals(returnReasonsResponse.getReturn_reasons().get(0).getId(),1);
    }
}
