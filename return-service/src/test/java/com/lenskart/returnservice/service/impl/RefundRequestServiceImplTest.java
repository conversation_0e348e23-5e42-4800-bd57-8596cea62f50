package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import com.lenskart.ordermetadata.dto.ReverseCourierMetaData;
import com.lenskart.ordermetadata.dto.request.ExchangeAddressDTO;
import com.lenskart.ordermetadata.dto.request.PickupAddressDTO;
import com.lenskart.ordermetadata.dto.request.RefundRequestDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.request.ReturnSourcesDTO;
import com.lenskart.ordermetadata.dto.response.DispensingDTO;
import com.lenskart.ordermetadata.dto.response.RefundRequestResponseDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.RefundRequestInputDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.IReturnOrderActionService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {RefundRequestServiceImpl.class})
@ExtendWith(SpringExtension.class)
class RefundRequestServiceImplTest {

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private ReturnRequestRepository returnRequestRepository;

    @Autowired
    private RefundRequestServiceImpl refundRequestServiceImpl;

    @MockBean
    private IReturnOrderActionService iReturnOrderActionService;

    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private IReturnEventService iReturnEventService;

    /**
     * Test {@link RefundRequestServiceImpl#initiateRefund(Integer, ReturnCreationRequestDTO, ReturnItemDTO, UwOrderDTO, Integer, boolean, String)}.
     * <p>
     * Method under test: {@link RefundRequestServiceImpl#initiateRefund(Integer, ReturnCreationRequestDTO, ReturnItemDTO, UwOrderDTO, Integer, boolean, String)}
     */
    @Test
    @DisplayName("Test initiateRefundProcess(Integer, ReturnCreationRequestDTO, ReturnItemDTO, UwOrderDTO, Integer, boolean, String)")
    @Tag("MaintainedByDiffblue")
    void testInitiateRefundProcess() {
        // Arrange
        when(iKafkaService.pushToKafka(Mockito.<String>any(), Mockito.<String>any(), Mockito.<Object>any()))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        ExchangeAddressDTO exchangeAddress = new ExchangeAddressDTO();
        exchangeAddress.setAddressType("42 Main St");
        exchangeAddress.setAddressline1("42 Main St");
        exchangeAddress.setCity("Oxford");
        exchangeAddress.setCountry("GB");
        exchangeAddress.setEmail("<EMAIL>");
        exchangeAddress.setFirstName("Jane");
        exchangeAddress.setGender("Gender");
        exchangeAddress.setLandmark("Landmark");
        exchangeAddress.setLastName("Doe");
        exchangeAddress.setLocality("Locality");
        exchangeAddress.setPhone("6625550144");
        exchangeAddress.setPhoneCode("6625550144");
        exchangeAddress.setPostcode("OX1 1PT");
        exchangeAddress.setState("MD");

        ItemWiseAmountDTO itemWiseAmountDTO = new ItemWiseAmountDTO();
        itemWiseAmountDTO.setCurrency("GBP");
        itemWiseAmountDTO.setExchangePgAmount(10.0d);
        itemWiseAmountDTO.setGiftCardDiscount(10.0d);
        itemWiseAmountDTO.setGiftVoucher(10.0d);
        itemWiseAmountDTO.setInsuranceBenefitDiscount(10.0d);
        itemWiseAmountDTO.setIsExchangeItemReceived(true);
        itemWiseAmountDTO.setItemWiseRefundAmount(10.0d);
        itemWiseAmountDTO.setLenskartDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangeDiscount(10.0d);
        itemWiseAmountDTO.setLenskartExchangePlusDiscount(10.0d);
        itemWiseAmountDTO.setLenskartPlusDiscount(10.0d);
        itemWiseAmountDTO.setPaymentNotCapture(true);
        itemWiseAmountDTO.setPrepaidWeb(10.0d);
        itemWiseAmountDTO.setStoreCredit(10.0d);
        itemWiseAmountDTO.setTPendingPGAmount(10.0d);
        itemWiseAmountDTO.setTotalCodPrice(10.0d);
        itemWiseAmountDTO.setTotalItemAmount(10.0d);
        itemWiseAmountDTO.setTotalOnlinePrice(10.0d);
        itemWiseAmountDTO.setTotalPrepaidAmt(10.0d);
        itemWiseAmountDTO.setTotalPriceOfOrder(10.0d);

        OrdersHeaderDTO ordersHeaderDTO = new OrdersHeaderDTO();
        ordersHeaderDTO.setAssignedMgr(true);
        ordersHeaderDTO.setIncrementId(1);
        ordersHeaderDTO.setIsBulkOrder(true);
        ordersHeaderDTO.setIsExchangeOrder(true);
        ordersHeaderDTO.setLkCountry("GB");
        ordersHeaderDTO.setOrderId(1);
        ordersHeaderDTO.setPaymentCaptureFlag(1);
        ordersHeaderDTO.setPaymentMode("Payment Mode");

        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = new PurchaseOrderDetailsDTO();
        purchaseOrderDetailsDTO.setBranded(true);
        purchaseOrderDetailsDTO.setGetIsBlacklisted(true);
        purchaseOrderDetailsDTO.setIdentifierType("Identifier Type");
        purchaseOrderDetailsDTO.setIdentifierValue("42");
        purchaseOrderDetailsDTO.setItemWiseAmountDTO(itemWiseAmountDTO);
        purchaseOrderDetailsDTO.setItemWisePrices(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrderAddressUpdateDTOs(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrders(new ArrayList<>());
        purchaseOrderDetailsDTO.setOrdersHeaderDTO(ordersHeaderDTO);
        purchaseOrderDetailsDTO.setShippingStatusDetail(new ShippingStatusDetail());
        purchaseOrderDetailsDTO.setUwOrders(new ArrayList<>());

        PickupAddressDTO ReversePickupAddress = new PickupAddressDTO();
        ReversePickupAddress.setCity("Oxford");
        ReversePickupAddress.setCountry("GB");
        ReversePickupAddress.setEmail("<EMAIL>");
        ReversePickupAddress.setFirstName("Jane");
        ReversePickupAddress.setLastName("Doe");
        ReversePickupAddress.setPincode(1);
        ReversePickupAddress.setState("MD");
        ReversePickupAddress.setStreet1("Street1");
        ReversePickupAddress.setStreet2("Street2");
        ReversePickupAddress.setTelephone("6625550144");

        ReturnCreationRequestDTO returnCreationRequest = new ReturnCreationRequestDTO();
        returnCreationRequest.setCallbackRequiredToSalesman(true);
        returnCreationRequest.setDispensingDTO(new DispensingDTO());
        returnCreationRequest.setEnforceRefundAtStore(true);
        returnCreationRequest.setExchangeAddress(exchangeAddress);
        returnCreationRequest.setFacilityCode("Facility Code");
        returnCreationRequest.setIncrementId(1);
        returnCreationRequest.setInitiatedBy(1);
        returnCreationRequest.setIsCourierReassigned(true);
        returnCreationRequest.setItems(new ArrayList<>());
        returnCreationRequest.setNewCourier("New Courier");
        returnCreationRequest.setOldCourier("Old Courier");
        returnCreationRequest.setProductIdsMap(new HashMap<>());
        returnCreationRequest.setPurchaseOrderDetailsDTO(purchaseOrderDetailsDTO);
        returnCreationRequest.setReturnMethod("Return Method");
        returnCreationRequest.setReturnSource(ReturnSourcesDTO.WEB);
        returnCreationRequest.setReverseCourierMetaData(new ReverseCourierMetaData());
        returnCreationRequest.setReversePickupAddress(ReversePickupAddress);
        returnCreationRequest.setSalesmanName("Salesman Name");
        returnCreationRequest.setSalesmanNumber("42");
        returnCreationRequest.setStoreEmail("<EMAIL>");
        returnCreationRequest.setStoreFacilityCode("Store Facility Code");

        ReturnItemDTO item = new ReturnItemDTO();
        item.setClaimInsurance(true);
        item.setDoRefund(true);
        item.setNeedApproval(true);
        item.setQcStatus("Qc Status");
        item.setReasons(new ArrayList<>());
        item.setRefundMethod("Refund Method");
        item.setRefundMethodRequest("Refund Method Request");
        item.setUwItemId(1);

        UwOrderDTO uwOrder = new UwOrderDTO();
        uwOrder.setB2bRefrenceItemId(1);
        uwOrder.setBarcode("Barcode");
        uwOrder.setBrand("Brand");
        uwOrder.setChannel("Channel");
        uwOrder.setClassification("Classification");
        uwOrder.setClassificationName("Classification Name");
        uwOrder.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder
                .setExpectedDispatchDate(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        uwOrder.setFacilityCode("Facility Code");
        uwOrder.setFitting("Fitting");
        uwOrder.setIncrementId(1);
        uwOrder.setIsFranchise("Is Franchise");
        uwOrder.setIsLocalFittingRequired(true);
        uwOrder.setItemId(1);
        uwOrder.setLensPackage("java.text");
        uwOrder.setMethod("Method");
        uwOrder.setNavChannel("Nav Channel");
        uwOrder.setParentUw(1);
        uwOrder.setProductDeliveryType("Product Delivery Type");
        uwOrder.setProductId(1);
        uwOrder.setProductSku("Product Sku");
        uwOrder.setProductValue("42");
        uwOrder.setShipToStoreRequired(true);
        uwOrder.setShipmentState("Shipment State");
        uwOrder.setShipmentStatus("Shipment Status");
        uwOrder.setShippingPackageId("42");
        uwOrder.setTotalPrice(10.0d);
        uwOrder.setUnicomOrderCode("Unicom Order Code");
        uwOrder.setUwItemId(1);
        uwOrder.setVsmStockout(1);

        // Act
        RefundRequestResponseDTO actualInitiateRefundProcessResult = refundRequestServiceImpl.initiateRefund(1,
                returnCreationRequest, item, uwOrder, 1, true, "Initiate Refund Process", 1);

        // Assert
        assertNull(actualInitiateRefundProcessResult);
    }

    /**
     * Test {@link RefundRequestServiceImpl#createRefundRequest(RefundRequestInputDTO)}.
     * <ul>
     *   <li>Then return {@code null}.</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundRequestServiceImpl#createRefundRequest(RefundRequestInputDTO)}
     */
    @Test
    @DisplayName("Test createRefundRequest(RefundRequestInputDTO); then return 'null'")
    @Tag("MaintainedByDiffblue")
    void testCreateRefundRequest_thenReturnNull() {
        // Arrange
        when(orderOpsFeignClient.initiateRefundProcess(Mockito.<RefundRequestDTO>any()))
                .thenReturn(new ResponseEntity<>(HttpStatusCode.valueOf(200)));

        // Act
        RefundRequestResponseDTO actualCreateRefundRequestResult = refundRequestServiceImpl
                .createRefundRequest(new RefundRequestInputDTO());

        // Assert
        verify(orderOpsFeignClient).initiateRefundProcess(isNull());
        assertNull(actualCreateRefundRequestResult);
    }

    /**
     * Test {@link RefundRequestServiceImpl#createRefundRequest(RefundRequestInputDTO)}.
     * <ul>
     *   <li>Then return {@link RefundRequestResponseDTO} (default constructor).</li>
     * </ul>
     * <p>
     * Method under test: {@link RefundRequestServiceImpl#createRefundRequest(RefundRequestInputDTO)}
     */
    @Test
    @DisplayName("Test createRefundRequest(RefundRequestInputDTO); then return RefundRequestResponseDTO (default constructor)")
    @Tag("MaintainedByDiffblue")
    void testCreateRefundRequest_thenReturnRefundRequestResponseDTO() {
        // Arrange
        RefundRequestResponseDTO refundRequestResponseDTO = new RefundRequestResponseDTO();
        refundRequestResponseDTO.setAmount(10.0d);
        refundRequestResponseDTO
                .setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        refundRequestResponseDTO.setCustomerId(1L);
        refundRequestResponseDTO.setId(1L);
        refundRequestResponseDTO.setIncrementId(1L);
        refundRequestResponseDTO.setInitiateViaNewFlow(true);
        refundRequestResponseDTO.setLenskartDiscount(new BigDecimal("2.3"));
        refundRequestResponseDTO.setLenskartPlusDiscount(new BigDecimal("2.3"));
        refundRequestResponseDTO.setNeftDetailId(1L);
        refundRequestResponseDTO.setNeftToSc(1);
        refundRequestResponseDTO.setOrderStatus("Order Status");
        refundRequestResponseDTO.setRefundMethod("Refund Method");
        refundRequestResponseDTO.setRefundType("Refund Type");
        refundRequestResponseDTO.setRequestedBy("Requested By");
        refundRequestResponseDTO.setReturnId(1);
        refundRequestResponseDTO.setSource("Source");
        refundRequestResponseDTO.setStatus("Status");
        refundRequestResponseDTO.setStatusDetail("Status Detail");
        refundRequestResponseDTO.setType("Type");
        ResponseEntity<RefundRequestResponseDTO> responseEntity = mock(ResponseEntity.class);
        when(responseEntity.getBody()).thenReturn(refundRequestResponseDTO);
        when(responseEntity.getStatusCode()).thenReturn(HttpStatusCode.valueOf(200));
        when(orderOpsFeignClient.initiateRefundProcess(Mockito.<RefundRequestDTO>any())).thenReturn(responseEntity);
        when(iReturnOrderActionService.getReturnOrderStatusById(Mockito.<Integer>any())).thenReturn("42");

        // Act
        RefundRequestResponseDTO actualCreateRefundRequestResult = refundRequestServiceImpl
                .createRefundRequest(new RefundRequestInputDTO(new RefundRequestDTO(), 1));

        // Assert
        verify(orderOpsFeignClient).initiateRefundProcess(isA(RefundRequestDTO.class));
        verify(iReturnOrderActionService).getReturnOrderStatusById(isNull());
        verify(responseEntity).getBody();
        verify(responseEntity).getStatusCode();
        assertSame(refundRequestResponseDTO, actualCreateRefundRequestResult);
    }
}
