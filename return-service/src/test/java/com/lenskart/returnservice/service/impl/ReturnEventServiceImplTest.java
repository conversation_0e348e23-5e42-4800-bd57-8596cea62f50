package com.lenskart.returnservice.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnrepository.repository.ReverseTrackingEventRepository;
import com.lenskart.returnservice.service.IKafkaService;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ReturnEventServiceImpl.class})
@ExtendWith(SpringExtension.class)
class ReturnEventServiceImplTest {
    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private ReturnDetailRepository returnDetailRepository;

    @MockBean
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private ReturnEventServiceImpl returnEventServiceImpl;

    @MockBean
    private ReverseTrackingEventRepository reverseTrackingEventRepository;

    /**
     * Method under test: {@link ReturnEventServiceImpl#persistEvent(Integer, Integer, String, String)}
     */
    @Test
    void testPersistEvent() {
        // Arrange
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent.setEvent("Event");
        returnEvent.setId(1);
        returnEvent.setRemarks("Remarks");
        returnEvent.setReturnId(1);
        returnEvent.setReturnRequestId(1);
        when(returnEventRepository.save((ReturnEvent) any())).thenReturn(returnEvent);

        // Act
        returnEventServiceImpl.persistEvent(1, 1, "Event", "Remarks");

        // Assert
        verify(returnEventRepository).save((ReturnEvent) any());
    }

    /**
     * Method under test: {@link ReturnEventServiceImpl#createReturnEvent(Integer, Integer, String, String)}
     */
    @Test
    void testCreateReturnEvent() {
        // Arrange
        when(iKafkaService.pushToKafka((String) any(), (String) any(), (Object) any())).thenReturn(null);

        // Act
        returnEventServiceImpl.createReturnEvent(1, 1, "Event", "Remarks");

        // Assert
        verify(iKafkaService).pushToKafka((String) any(), (String) any(), (Object) any());
    }

    /**
     * Method under test: {@link ReturnEventServiceImpl#getReturnEvent(Integer)}
     */
    @Test
    void testGetReturnEvent() {
        // Arrange
        ArrayList<ReturnEvent> returnEventList = new ArrayList<>();
        when(returnEventRepository.findByReturnId((Integer) any())).thenReturn(returnEventList);

        // Act
        List<ReturnEvent> actualReturnEvent = returnEventServiceImpl.getReturnEvent(1);

        // Assert
        assertSame(returnEventList, actualReturnEvent);
        assertTrue(actualReturnEvent.isEmpty());
        verify(returnEventRepository).findByReturnId((Integer) any());
    }

    /**
     * Method under test: {@link ReturnEventServiceImpl#createTrackingEvent(ReturnEvent)}
     */
    @Test
    void testCreateTrackingEvent2() {
        // Arrange
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent.setEvent("Event");
        returnEvent.setId(1);
        returnEvent.setRemarks("Remarks");
        returnEvent.setReturnId(1);
        returnEvent.setReturnRequestId(1);

        ArrayList<ReturnEvent> returnEventList = new ArrayList<>();
        returnEventList.add(returnEvent);
        when(returnEventRepository.findByReturnId((Integer) any())).thenReturn(returnEventList);

        ReturnEvent returnEvent1 = new ReturnEvent();
        Date fromResult = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        returnEvent1.setCreatedAt(fromResult);
        returnEvent1.setEvent("Event");
        returnEvent1.setId(1);
        returnEvent1.setRemarks("Remarks");
        returnEvent1.setReturnId(1);
        returnEvent1.setReturnRequestId(1);

        // Act
        returnEventServiceImpl.createTrackingEvent(returnEvent1);

        // Assert that nothing has changed
        verify(returnEventRepository).findByReturnId((Integer) any());
        assertSame(fromResult, returnEvent1.getCreatedAt());
        assertEquals(1, returnEvent1.getReturnRequestId().intValue());
        assertEquals(1, returnEvent1.getReturnId().intValue());
        assertEquals("Remarks", returnEvent1.getRemarks());
        assertEquals(1, returnEvent1.getId().intValue());
        assertEquals("Event", returnEvent1.getEvent());
    }

    /**
     * Method under test: {@link ReturnEventServiceImpl#createTrackingEvent(ReturnEvent)}
     */
    @Test
    void testCreateTrackingEvent3() {
        // Arrange
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent.setEvent("Event");
        returnEvent.setId(1);
        returnEvent.setRemarks("Remarks");
        returnEvent.setReturnId(1);
        returnEvent.setReturnRequestId(1);

        ReturnEvent returnEvent1 = new ReturnEvent();
        returnEvent1.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent1.setEvent("com.lenskart.returnrepository.entity.ReturnEvent");
        returnEvent1.setId(2);
        returnEvent1.setRemarks("com.lenskart.returnrepository.entity.ReturnEvent");
        returnEvent1.setReturnId(2);
        returnEvent1.setReturnRequestId(2);

        ArrayList<ReturnEvent> returnEventList = new ArrayList<>();
        returnEventList.add(returnEvent1);
        returnEventList.add(returnEvent);
        when(returnEventRepository.findByReturnId((Integer) any())).thenReturn(returnEventList);

        ReturnEvent returnEvent2 = new ReturnEvent();
        Date fromResult = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        returnEvent2.setCreatedAt(fromResult);
        returnEvent2.setEvent("Event");
        returnEvent2.setId(1);
        returnEvent2.setRemarks("Remarks");
        returnEvent2.setReturnId(1);
        returnEvent2.setReturnRequestId(1);

        // Act
        returnEventServiceImpl.createTrackingEvent(returnEvent2);

        // Assert that nothing has changed
        verify(returnEventRepository).findByReturnId((Integer) any());
        assertSame(fromResult, returnEvent2.getCreatedAt());
        assertEquals(1, returnEvent2.getReturnRequestId().intValue());
        assertEquals(1, returnEvent2.getReturnId().intValue());
        assertEquals("Remarks", returnEvent2.getRemarks());
        assertEquals(1, returnEvent2.getId().intValue());
        assertEquals("Event", returnEvent2.getEvent());
    }

    /**
     * Method under test: {@link ReturnEventServiceImpl#createTrackingEvent(ReturnEvent)}
     */
    @Test
    void testCreateTrackingEvent4() {
        // Arrange
        when(returnEventRepository.findByReturnId((Integer) any())).thenReturn(new ArrayList<>());

        ReturnEvent returnEvent = new ReturnEvent();
        Date fromResult = Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        returnEvent.setCreatedAt(fromResult);
        returnEvent.setEvent("Event");
        returnEvent.setId(1);
        returnEvent.setRemarks("Remarks");
        returnEvent.setReturnId(null);
        returnEvent.setReturnRequestId(1);

        // Act
        returnEventServiceImpl.createTrackingEvent(returnEvent);

        // Assert that nothing has changed
        assertSame(fromResult, returnEvent.getCreatedAt());
        assertEquals(1, returnEvent.getReturnRequestId().intValue());
        assertEquals("Remarks", returnEvent.getRemarks());
        assertEquals(1, returnEvent.getId().intValue());
        assertEquals("Event", returnEvent.getEvent());
    }

    /**
     * Method under test: {@link ReturnEventServiceImpl#createTrackingEvent(ReturnEvent)}
     */
    @Test
    void testCreateTrackingEvent5() {
        // Arrange
        ReturnEvent returnEvent = mock(ReturnEvent.class);
        when(returnEvent.getReturnRequestId()).thenReturn(1);
        when(returnEvent.getEvent()).thenReturn("foo");
        doNothing().when(returnEvent).setCreatedAt((Date) any());
        doNothing().when(returnEvent).setEvent((String) any());
        doNothing().when(returnEvent).setId((Integer) any());
        doNothing().when(returnEvent).setRemarks((String) any());
        doNothing().when(returnEvent).setReturnId((Integer) any());
        doNothing().when(returnEvent).setReturnRequestId((Integer) any());
        returnEvent.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent.setEvent("Event");
        returnEvent.setId(1);
        returnEvent.setRemarks("Remarks");
        returnEvent.setReturnId(1);
        returnEvent.setReturnRequestId(1);

        ArrayList<ReturnEvent> returnEventList = new ArrayList<>();
        returnEventList.add(returnEvent);

        ReturnEvent returnEvent1 = new ReturnEvent();
        returnEvent1.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent1.setEvent("Event");
        returnEvent1.setId(1);
        returnEvent1.setRemarks("Remarks");
        returnEvent1.setReturnId(1);
        returnEvent1.setReturnRequestId(1);
        when(returnEventRepository.save((ReturnEvent) any())).thenReturn(returnEvent1);
        when(returnEventRepository.findByReturnId((Integer) any())).thenReturn(returnEventList);

        ReturnEvent returnEvent2 = new ReturnEvent();
        returnEvent2.setCreatedAt(Date.from(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant()));
        returnEvent2.setEvent("Event");
        returnEvent2.setId(1);
        returnEvent2.setRemarks("Remarks");
        returnEvent2.setReturnId(1);
        returnEvent2.setReturnRequestId(1);

        // Act
        returnEventServiceImpl.createTrackingEvent(returnEvent2);

        // Assert
        verify(returnEventRepository).save((ReturnEvent) any());
        verify(returnEventRepository).findByReturnId((Integer) any());
        verify(returnEvent).getReturnRequestId();
        verify(returnEvent).getEvent();
        verify(returnEvent).setCreatedAt((Date) any());
        verify(returnEvent).setEvent((String) any());
        verify(returnEvent).setId((Integer) any());
        verify(returnEvent).setRemarks((String) any());
        verify(returnEvent).setReturnId((Integer) any());
        verify(returnEvent).setReturnRequestId((Integer) any());
        assertEquals(1, returnEvent2.getReturnRequestId().intValue());
    }
}

