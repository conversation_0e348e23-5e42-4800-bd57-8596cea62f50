<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lenskart</groupId>
        <artifactId>return</artifactId>
        <version>0.1.86</version>
    </parent>
    <artifactId>return-common</artifactId>
    <version>${return.common.version}</version>
    <name>return-common</name>
    <description>return common project</description>
    <packaging>jar</packaging>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <repositories>
        <repository>
            <id>internal</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal/</url>
        </repository>
    </repositories>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.7.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>order-metadata</artifactId>
            <version>0.0.47</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>refund-client-commons</artifactId>
            <version>0.0.30</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-core</artifactId>
            <version>1.13</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>reverse-model</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>return-refund-rules-model</artifactId>
            <version>5.1.8-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>order-ops-model</artifactId>
            <version>8.10.11</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-envers</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-rest</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>antlr</groupId>
            <artifactId>antlr</artifactId>
            <version>2.7.7</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart.platform.fl</groupId>
            <artifactId>fl-utils</artifactId>
            <version>5.28_Spring_Boot_3.1.5.RELEASE_17_JDK</version>
            <classifier>plain</classifier>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.jsondoc</groupId>
                <artifactId>spring-boot-starter-jsondoc</artifactId>
                <version>1.2.17</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.common</groupId>
                <artifactId>hibernate-commons-annotations</artifactId>
                <version>6.0.6.Final</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>enforce-no-circular-dependencies</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <dependencyConvergence/>
                                <bannedDependencies>
                                    <excludes>
                                        <exclude>com.lenskart:return-api</exclude>
                                        <exclude>com.lenskart:return-consumer</exclude>
                                        <exclude>com.lenskart:return-job</exclude>
                                        <exclude>com.lenskart:return-repository</exclude>
                                        <exclude>com.lenskart:return-service</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
