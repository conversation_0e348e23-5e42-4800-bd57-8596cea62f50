package com.lenskart.returncommon.aop;

import com.lenskart.returncommon.annotations.HandleException;
import com.lenskart.returncommon.annotations.LogException;
import com.lenskart.returncommon.model.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class ExceptionInterceptor {

    @Around("execution(@com.lenskart.returncommon.annotations.HandleException * *(..))")
    public Object handleException(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = signature.getName();
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            HandleException handleException = signature.getMethod().getAnnotation(HandleException.class);
            String message = null;
            if (handleException != null) {
                message = handleException.message();
            }
            if (StringUtils.isEmpty(message)) {
                message = "Exception occurred while processing the request";
            }
            log.error("HandleExceptionInterceptor: [{}#{}] {}", className, methodName, message, e);
            ApiResponse apiResponse = new ApiResponse();
            apiResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            apiResponse.setMessage(e.getMessage());
            apiResponse.setError(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase());
            apiResponse.setSuccess(false);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(apiResponse);
        }
    }

    @Around("execution(@com.lenskart.returncommon.annotations.LogException * *(..))")
    public Object logException(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = signature.getName();
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            LogException handleException = signature.getMethod().getAnnotation(LogException.class);
            String message = null;
            if (handleException != null) {
                message = handleException.message();
            }
            if (StringUtils.isEmpty(message)) {
                message = "Exception occurred while processing the request";
            }
            log.error("LogExceptionInterceptor: [{}#{}] {}", className, methodName, message, e);
            throw e;
        }
    }
}
