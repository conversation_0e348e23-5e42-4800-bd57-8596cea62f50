package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.enums.RuleConditionType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Map;

@Data
@ToString
@NoArgsConstructor
public class RuleContextDTO {
    private Map<RuleConditionType, Boolean> conditions;

    public RuleContextDTO(Map<RuleConditionType, Boolean> conditions) {
        this.conditions = conditions;
    }

    public Boolean getCondition(RuleConditionType conditionType) {
        if (!CollectionUtils.isEmpty(conditions)) {
            return conditions.getOrDefault(conditionType, null);
        }
        return null;
    }
}
