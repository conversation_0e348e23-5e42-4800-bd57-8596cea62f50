package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BackSyncStatusDTO {
    private Integer uwItemId;
    private String trackingStatus;
    private Integer incrementId;
    private List<UwOrderDTO> uwOrderDTOS;
}
