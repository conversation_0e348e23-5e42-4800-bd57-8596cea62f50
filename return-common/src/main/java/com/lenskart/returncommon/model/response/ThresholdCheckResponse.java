package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ThresholdCheckResponse {

    @JsonProperty("isThresholdReached")
    private boolean result;

    @JsonProperty("status")
    private String status;

    @JsonProperty("hasViolatedThreshold")
    private boolean hasViolatedThreshold;
}
