package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
@ToString
public class InsuranceEligibilityResponse {
    @ApiObjectField(name = "increment_id")
    private Integer incrementId;

    @ApiObjectField(name = "magento_item_list")
    private List<ItemsInsuranceDetail> itemsInsuranceDetailList;

    @ApiObjectField(name = "error_message")
    private String errorMessage;
}
