package com.lenskart.returncommon.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

public enum ReturnSources {

    WEB("web"),
    POS("pos"),
    VSM("vsm"),
    DIRECT("direct"),
    WAREHOUSE("warehouse");



    private ReturnSources(String value){
        this.source = value;
    }

    private final String source;

    private static Map<String, ReturnSources> sourceEnumMap = new HashMap<String, ReturnSources>(5);

    static {
        sourceEnumMap.put("web", ReturnSources.WEB);
        sourceEnumMap.put("pos", ReturnSources.POS);
        sourceEnumMap.put("vsm", ReturnSources.VSM);
        sourceEnumMap.put("direct", ReturnSources.DIRECT);
        sourceEnumMap.put("warehouse", ReturnSources.WAREHOUSE);
    }

    @JsonCreator
    public static ReturnSources forValue(String value) {
        return sourceEnumMap.get(value.toLowerCase());
    }

    @JsonValue public String getSource(){
        return source;
    }
}
