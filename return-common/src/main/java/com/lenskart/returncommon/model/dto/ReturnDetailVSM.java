package com.lenskart.returncommon.model.dto;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
public class ReturnDetailVSM {
    private Integer returnId;
    private Integer incrementId;
    private String storeName;
    private Date statusChangeDate;
    private Date returnDate;
    private String returnType;
    private String returnStatus;
    private String qcStatus;
    private String agentId;
    private String category;
}
