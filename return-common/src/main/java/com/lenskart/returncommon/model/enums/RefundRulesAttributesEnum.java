package com.lenskart.returncommon.model.enums;

public enum RefundRulesAttributesEnum {
    ID("id"),
    REVERSE_TYPE("reverseType"),
    NAV_CHANNEL("navChannel"),
    RETURN_INITIATE_SOURCE("returnInitiatedSource"),
    TRIGGER_POINT("triggerPoint"),
    IS_PSEUDO_GATEPASS("isPsuedoGatepass"),
    IS_BRANDED("isBranded"),
    IS_QC_PASS("isQcPass"),
    CATEGORY("category"),
    WARRANTY_FROM("warantyFrom"),
    WARRANTY_TO("warantyTo"),
    IS_ACCESSORY_MISSING("isAccessoryMissing"),
    IS_LAST_PIECE("isLastPiece"),
    IS_LENS_ONLY("isLensOnly"),
    COUNTRY_CODE("countryCode"),
    INSURANCE_POLICY("insurancePolicy"),
    AMOUNT_FROM("amountFrom"),
    AMOUNT_TILL("amountTill"),
    BLACKLISTED_PHONE_NUMBERS("blacklistedPhoneNumbers"),
    BLACKLISTED_PINCODES("blacklistedPincodes"),
    IS_RETURNABLE("isReturnable"),
    ACTION("action"),
    DO_REFUND("doRefund"),
    REFUND_METHOD("refundMethod"),
    DRAFT_RETURN_METHOD("draftReturnMethod"),
    EXCHANGE_ALLOWED("exchangeAllowed"),
    EXCHANGE_DISPATCH_POINT("exchangeOrderDispatch"),
    RETURN_REASONS("returnReasons"),
    PAYMENT_METHOD("paymentMethod"),
    RETURN_ELIGIBILITY_PERIOD("returnEligibiityPeriod"),
    REFUND_DISPATCH_POINT("refundDispatch"),
    OVERRIDE_WARRANTY_PERIOD("overrideWarrantyPeriod"),
    CUSTOMER_SCORE_FROM("customerScoreFrom"),
    CUSTOMER_SCORE_TO("customerScoreTo"),
    STORE_SCORE_FROM("storeScoreFrom"),
    STORE_SCORE_TO("storeScoreTo");

    String attribteName;

    RefundRulesAttributesEnum(String attribute) {
        this.attribteName = attribute;
    }

    public String getAttributeName() {
        return this.attribteName;
    }
}
