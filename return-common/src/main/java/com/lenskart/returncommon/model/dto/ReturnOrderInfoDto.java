package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ReturnOrderInfoDto {
    @JsonProperty("return_id")
    private Integer returnId;
    @JsonProperty("order_no")
    private Long orderNo;
    @JsonProperty("override_comment")
    private String overrideComment;
    @JsonProperty("group_id")
    private Integer groupId;
    @JsonProperty("return_type")
    private String returnType;
    @JsonProperty("reverse_awb")
    private String reverseAwb;
    @JsonProperty("reverse_courier")
    private String reverseCourier;
    @JsonProperty("status")
    private String status;
    @JsonProperty("source")
    private String source;
    @JsonProperty("return_create_datetime")
    private Date returnCreateDatetime;
    @JsonProperty("last_update_datetime")
    private boolean reversePickupFlag;
    @JsonProperty("last_update_datetime")
    private Date lastUpdateDatetime;
    @JsonProperty("return_method")
    private String returnMethod;
    @JsonProperty("is_qc_at_doorstep")
    private Integer isQcAtDoorstep;
    @JsonProperty("uw_item_id")
    private Long uwItemId;
    @JsonProperty("bulk_type")
    private String bulkType;
}