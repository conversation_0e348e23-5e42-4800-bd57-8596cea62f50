package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

public @Data class PickupAddress {

    @ApiObjectField(name = "first_name")
    @JsonProperty("first_name")
    private String firstName;

    @ApiObjectField(name = "last_name")
    @JsonProperty("last_name")
    private String lastName;

    @ApiObjectField(name = "street_1")
    @JsonProperty("street_1")
    private String street1;

    @ApiObjectField(name = "street_2")
    @JsonProperty("street_2")
    private String street2;

    @ApiObjectField(name = "city")
    @JsonProperty("city")
    private String city;

    @ApiObjectField(name = "state")
    @JsonProperty("state")
    private String state;

    @ApiObjectField(name = "pincode")
    @JsonProperty("pincode")
    private Integer pincode;

    @ApiObjectField(name = "country")
    @JsonProperty("country")
    private String country;

    @ApiObjectField(name = "email")
    @JsonProperty("email")
    private String email;

    @ApiObjectField(name = "telephone")
    @JsonProperty("telephone")
    private String telephone;

}
