package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.ReturnOrderInfoDto;
import com.lenskart.returncommon.model.dto.ReturnTrackerOrderItemDto;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ReturnTrackerResponse {
    @JsonProperty("returnArr")
    List<ReturnOrderInfoDto> returnOrderResponse;
    @JsonProperty("returnTrackerOrderItemDtoMap")
    Map<Integer,List<ReturnTrackerOrderItemDto>> returnTrackerOrderItemDtoMap;
    //TODO - to add following param
    //PickUpAddressDetails
}