package com.lenskart.returncommon.model.dto;

import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import lombok.Data;

import java.util.Date;

@Data
public class PostReturnCreationActivityDTO{
    private Integer returnId;
    private Integer returnRequestId;
    private String returnType;
    private ReturnStatus returnStatus;
    private Long groupId;
    private Integer incrementId;
    private Integer orderId;
    private String source;
    private Date returnCreationDate;
    private boolean isReturnCreatedNow;
    private String returnInitiatedFrom;
    private UwOrderDTO uwOrder;
    private ReturnItemDTO item;
    private ReverseCourierDetail reverseCourierDetail;
    private PurchaseOrderDetailsDTO purchaseOrderDetailsDTO;
    private ReturnCreationRequestDTO returnCreationRequest;
}
