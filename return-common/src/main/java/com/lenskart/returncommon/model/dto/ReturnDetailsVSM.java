package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.SqlResultSetMapping;
import lombok.Data;

import java.util.Date;

@Data
public class ReturnDetailsVSM {
    @JsonProperty("order_no")
    private Integer orderNo;

    @JsonProperty("return_id")
    private Integer returnId;

    @JsonProperty("primary_reason")
    private String primaryReason;

    @JsonProperty("secondary_reason")
    private String secondaryReason;

    @JsonProperty("time_diff")
    private Long timeDiff;

    @JsonProperty("receiving_flag")
    private String receivingFlag;

    @JsonProperty("roi_status")
    private String roiStatus;

    @JsonProperty("status_change_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statusChangeDate;

    @JsonProperty("return_type")
    private String returnType;

    @JsonProperty("agent_email")
    private String agentEmail;

    @JsonProperty("reverse_awb")
    private String reverseAwb;

    @JsonProperty("status")
    private String status;

    @JsonProperty("qc_status")
    private String qcStatus;

    @JsonProperty("return_create_datetime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnCreateDatetime;

    @JsonProperty("uw_item_id")
    private Integer uwItemId;

    @JsonProperty("classification")
    private Integer classification;

    public ReturnDetailsVSM() {
    }

    // Constructor for the query projection
    public ReturnDetailsVSM(
            Integer orderNo,
            Integer returnId,
            String primaryReason,
            String secondaryReason,
            int timeDiff,
            String receivingFlag,
            String roiStatus,
            Date statusChangeDate,
            String returnType,
            String agentEmail,
            String reverseAwb,
            String status,
            String qcStatus,
            Date returnCreateDatetime,
            Integer uwItemId
    ) {
        this.orderNo = orderNo;
        this.returnId = returnId;
        this.primaryReason = primaryReason;
        this.secondaryReason = secondaryReason;
        this.timeDiff = (long) timeDiff;
        this.receivingFlag = receivingFlag;
        this.roiStatus = roiStatus;
        this.statusChangeDate = statusChangeDate;
        this.returnType = returnType;
        this.agentEmail = agentEmail;
        this.reverseAwb = reverseAwb;
        this.status = status;
        this.qcStatus = qcStatus;
        this.returnCreateDatetime = returnCreateDatetime;
        this.uwItemId = uwItemId;
    }

    public ReturnDetailsVSM(
            Integer orderNo,
            Integer returnId,
            String primaryReason,
            String secondaryReason,
            Long timeDiff,
            String receivingFlag,
            String roiStatus,
            Date statusChangeDate,
            String returnType,
            String agentEmail,
            String reverseAwb,
            String status,
            String qcStatus,
            Date returnCreateDatetime,
            Integer uwItemId,
            Integer classification
    ) {
        this.orderNo = orderNo;
        this.returnId = returnId;
        this.primaryReason = primaryReason;
        this.secondaryReason = secondaryReason;
        this.timeDiff = timeDiff;
        this.receivingFlag = receivingFlag;
        this.roiStatus = roiStatus;
        this.statusChangeDate = statusChangeDate;
        this.returnType = returnType;
        this.agentEmail = agentEmail;
        this.reverseAwb = reverseAwb;
        this.status = status;
        this.qcStatus = qcStatus;
        this.returnCreateDatetime = returnCreateDatetime;
        this.uwItemId = uwItemId;
        this.classification = classification;
    }
}
