package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

@ToString
@Data
public class PrescriptionDetails {

    @JsonProperty("leftEye")
    @ApiObjectField(name = "leftEye")
    private LensDetails leftEye;

    @JsonProperty("prescriptionURL")
    @ApiObjectField(name = "prescriptionURL")
    private String prescriptionURL;

    @JsonProperty("rightEye")
    @ApiObjectField(name = "rightEye")
    private LensDetails rightEye;
}

