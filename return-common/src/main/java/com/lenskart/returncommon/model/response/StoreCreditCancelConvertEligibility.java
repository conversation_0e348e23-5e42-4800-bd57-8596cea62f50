package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StoreCreditCancelConvertEligibility {
    @JsonProperty(value = "is_sc_cancel_convertible")
    private boolean isScCancelConvertible;
    @JsonProperty(value = "sc_refund_breakup")
    private List<RefundBreakupResponse> scRefundBreakup;
}
