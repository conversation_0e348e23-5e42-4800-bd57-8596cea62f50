package com.lenskart.returncommon.model.response;

import com.lenskart.returncommon.model.dto.Pagination;
import com.lenskart.returncommon.model.dto.ReportDetail;
import com.lenskart.returncommon.model.dto.ReturnDetailVSM;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class GetReturnReportResponse {
    private List<ReturnDetailVSM> returnDetails;
    private List<ReportDetail> reportDetails;
    private Pagination pagination;
    private String error;
}
