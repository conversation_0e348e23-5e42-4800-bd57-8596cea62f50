package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import lombok.Data;
import lombok.ToString;
import org.springframework.stereotype.Component;

@Data
@ToString
@Component
public class DoorstepQcEligibility {
    private boolean isEligible;
    private int classification;
    private String period;
    private int incrementId;
    private int reasonId;
    private UwOrderDTO uwOrder;
    private String specialInstruction;
    private String rvpReason;
    private String navChannel;
    private Double price;
}
