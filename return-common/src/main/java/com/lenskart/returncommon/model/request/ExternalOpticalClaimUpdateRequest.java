package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ExternalOpticalClaimUpdateRequest {
    @JsonProperty("invoiceNumber")
    String invoiceNumber;
    @JsonProperty("claimStatus")
    String claimStatus;
    @JsonProperty("approvedClaimAmount")
    Double approvedClaimAmount;
    @JsonProperty("breakageDetails")
    String breakageDetails;
    @JsonProperty("reason")
    String reason;
    @JsonProperty("exchangeOrderInvoiceUrl")
    List<String> exchangeOrderInvoiceUrl = null;
}
