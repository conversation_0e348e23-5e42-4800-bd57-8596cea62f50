package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShippingStatusDto {
    private Integer id;
    private Integer orderNo;
    private String shipped;
    private Date complete_time;
    private String shipping_time;
    private String trackingNo;
    private String name;
    private String email;
    private String shippingMode;
    private String codAmount;
    private String paymentReceived;
    private String phone;
    private String carrierCode;
    private String linkToOrderNo;
    private String updatedCrm;
    private String updatedMagento;
    private String packed;
    private String deleted;
    private String leadId;
    private String shipmentStatus;
    private Date DeliveredDate;
    private String receivedBy;
    private Date pickDate;
    private String pickedBy;
    private String warehouseLocation;
    private String routingCode;
    private Date awbAssignedAt;
    private String updatedAt;
    private String region;
    private Integer statusId;
    private Date followedupDate;
    private Date lastFollowedupDate;
    private Integer followedupCount;
    private String execEmailId;
    private Integer ndrSmsSent;
    private Integer referralApiSent;
    private String manifestNumber;
    private String manifestDate;
    private String docketNumber;
    private String invoicedDate;
    private String manifestTime;
    private String invoicedTime;
    private String unicomOrderCode;
    private String shippingPackageId;
    private String ccUploadDate;
    private Integer ccFlag;
    private Integer isNpsProcessPicked;
    private String npsReason;
}
