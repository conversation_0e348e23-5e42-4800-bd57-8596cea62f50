package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.io.Serializable;
import java.util.List;

@Data
public class BackSyncRequest implements Serializable {
    @ApiObjectField(
            name = "checkPoint",
            description = "checkPoint"
    )
    @JsonProperty("checkPoint")
    String checkPoint;
    @ApiObjectField(
            name = "uwItemIdList",
            description = "uw item list"
    )
    @JsonProperty("uwItemIdList")
    List<Integer> uwItemIdList;
    @ApiObjectField(
            name = "invoiceUrl",
            description = "Invoice Url"
    )
    @JsonProperty("invoiceUrl")
    String invoiceUrl;
    @ApiObjectField(
            name = "invoiceDate",
            description = "Invoice Date, This is used while syncing POS data to JUNO"
    )
    @JsonProperty("invoiceDate")
    String invoiceDate;
    @ApiObjectField(
            name = "utr",
            description = "Bank Arn Number, required by Juno to show at front end"
    )
    @JsonProperty(
            value = "utr",
            required = false
    )
    String bankReferenceNumber;
}
