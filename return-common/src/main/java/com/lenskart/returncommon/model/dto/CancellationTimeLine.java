package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class CancellationTimeLine {
    @JsonProperty("cancellation_status")
    @ApiObjectField(name = "cancellation_status", description = "cancellation_status")
    private String cancellationStatus;

    @JsonProperty("cancellation_sub_status")
    @ApiObjectField(name = "cancellation_sub_status", description = "cancellation_sub_status")
    private String cancellationSubStatus;

    @JsonProperty("cancellation_estimated_date")
    @ApiObjectField(name = "cancellation_estimated_date", description = "cancellation_estimated_date")
    private Long cancellationDispatchDate;
}
