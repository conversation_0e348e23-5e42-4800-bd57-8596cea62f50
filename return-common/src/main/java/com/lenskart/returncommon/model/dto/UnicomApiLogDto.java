package com.lenskart.returncommon.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class UnicomApiLogDto {

    private Long id;
    private String incrementId;
    private String type;
    private String api;
    private String status;
    private String message;
    private Boolean exception;
    private String eMsg;
    private Date createDatetime;
    private String unicomOrderCode;

    public interface API {
        String MARK_ORDER_RETURN = "MarkOrderReturn";
        String MARK_ORDER_RTO = "MarkOrderRto";
    }

}
