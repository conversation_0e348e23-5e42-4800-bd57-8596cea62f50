package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class Returns {

    @ApiObjectField(name = "magento_item_id", description = "Magento Item Id of the item for which the return is created")
    @JsonProperty("magento_item_id")
    private Long magentoItemId;
    @ApiObjectField(name = "return_id", description = "Return Id of the item for which the return is created")
    @JsonProperty("return_id")
    private Integer returnId;
    @ApiObjectField(name = "uw_item_id", description = "Unitwise Item Id of the item for which the return is created")
    @JsonProperty("uw_item_id")
    private Integer uwItemId;
    @JsonIgnore
    private String eventName;
}
