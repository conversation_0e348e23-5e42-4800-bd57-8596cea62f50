package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.enums.ActionType;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.enums.RuleConditionType;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Slf4j
@ToString
@Data
public class CancelAndConvertRuleDto implements Serializable {
    private Integer id;
    private PaymentMode paymentMode;
    private Map<RuleConditionType, Boolean> conditionsMap;
    private String conditions;
    private Map<ActionType, Boolean> actionMap;
    private String action;

    public CancelAndConvertRuleDto(Integer id, PaymentMode paymentMode, String conditions, String action) {
        this.id = id;
        this.paymentMode = paymentMode;
        this.conditions = conditions;
        this.action = action;
    }

    public boolean conditionsMet(RuleContextDTO context) {
        Map<RuleConditionType, Boolean> conditionsMap = getConditionsMap();

        if (context == null || conditionsMap == null || conditionsMap.isEmpty()) {
            return false;
        }

        Map<RuleConditionType, Boolean> contextConditions = context.getConditions();
        if (contextConditions == null || contextConditions.isEmpty()) {
            return false;
        }

        // First check: both maps must have exactly same keys
        if (!conditionsMap.keySet().equals(contextConditions.keySet())) {
            return false;
        }

        // Second check: values must match for each key
        for (Map.Entry<RuleConditionType, Boolean> entry : conditionsMap.entrySet()) {
            if (!Objects.equals(entry.getValue(), contextConditions.get(entry.getKey()))) {
                return false;
            }
        }

        return true;
    }

    public Map<RuleConditionType, Boolean> getConditionsMap() {
        if (conditions == null || conditions.isEmpty()) {
            return Collections.emptyMap();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(conditions, new TypeReference<Map<RuleConditionType, Boolean>>() {
            });
        } catch (IOException e) {
            log.error("Exception occurred while getting conditionsMap, error:{}", e.getMessage());
            return null;
        }
    }

    public Map<ActionType, Boolean> getActionMap() {
        if (action == null || action.isEmpty()) {
            return Collections.emptyMap();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(action, new TypeReference<Map<ActionType, Boolean>>() {
            });
        } catch (IOException e) {
            log.error("Exception occurred while getting getActionsMap, error:{}", e.getMessage());
            return null;
        }
    }

}
