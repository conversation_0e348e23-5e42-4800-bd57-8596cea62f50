package com.lenskart.returncommon.model.enums;

public enum EventStatus {
    CREATED,
    CANCELLED;

    /**
     * Convert a String to EventStatus safely.
     *
     * @param value the input string
     * @return corresponding EventStatus
     * @throws IllegalArgumentException if no matching status is found
     */
    public static EventStatus fromString(String value) {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException("Status cannot be null or empty");
        }
        for (EventStatus status : EventStatus.values()) {
            if (status.name().equalsIgnoreCase(value.trim())) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown status: " + value);
    }
}
