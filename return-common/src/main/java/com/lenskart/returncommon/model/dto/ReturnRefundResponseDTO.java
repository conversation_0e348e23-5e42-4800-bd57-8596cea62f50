package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.response.RtoExchangeEligibilityResponse;
import com.lenskart.returncommon.model.response.StoreCreditCancelConvertEligibility;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Data
public class ReturnRefundResponseDTO {
    private int id = 0;
    private Boolean isReturnable;
    private Boolean doRefund;
    private String action;
    private String refundMethod;
    private String exchangeOrderDispatch;
    private boolean isFraud;
    private int returnCount = 0;
    private int exchangeCount = 0;
    private int refundCount = 0;
    private int returnEligibilityPeriod = 30;
    private String refundDispatch = "CourierPickup";
    private boolean isWarrantyActive;
    private List<String> rulesList;
    private boolean fetchExistingReturnResponse = false;
    private boolean isScCancelConvertible;
    private RtoExchangeEligibilityResponse rtoExchangeEligibilityResponse;
    private StoreCreditCancelConvertEligibility storeCreditCancelConvertEligibility;

    @JsonIgnore
    private boolean exchange;
    @JsonIgnore
    private boolean exchangeOnlyCTA;
    @JsonIgnore
    private List<String> refundMethods;
    @JsonIgnore
    private double amountToRefund;
    @JsonIgnore
    private boolean draftStatus;

    private String returnEligibleTillDate;
    @JsonIgnore
    private boolean approvalNeeded;
    @JsonIgnore
    private double lkcashToRefund;
    @JsonIgnore
    private double lkcashPlusToRefund;
    @JsonIgnore
    private double itemPrice;
    @JsonIgnore
    private double itemRefundedAmount;
    @JsonIgnore
    private boolean isExchangeCreatedAndCancelled;
    @JsonIgnore
    private String refundMethodRequest;
    @JsonIgnore
    private boolean isExchangeExpired;
    @JsonIgnore
    private Date refundIntentCreatedAt;
    @JsonIgnore
    private String errorForNotRefundable;
    @JsonIgnore
    private String errorForNotExchangeable;
    @JsonIgnore
    private String errorForNotReturnable;

    @JsonIgnore
    private int extensionInReturnEligibilityPeriod;
    @JsonIgnore
    private String needApprovalMessage;
    @JsonIgnore
    private Integer returnId;
    @JsonIgnore
    private String returnSource;

    public static List<String> getRefundMethodList(String refundMethod) {
        return null != refundMethod ? Arrays.asList(refundMethod.split(",")) : Collections.emptyList();
    }

    public interface ACTION {
        String PUTAWAY_YES_BAD_INVENTORY = "PUTAWAY_YES_BAD_INVENTORY";
        String PUTAWAY_YES_GOOD_INVENTORY = "PUTAWAY_YES_GOOD_INVENTORY";
        String RESHIP = "RESHIP";
        String DELIGHT = "DELIGHT";
    }

    public interface RefundMethod {
        String STORECREDIT = "storecredit";
        String NEFT = "neft";
        String ONLINE = "online";
        String WALLET = "wallet";
        String EXCHANGE = "Exchange";
        String SOURCE = "Source";
    }
}
