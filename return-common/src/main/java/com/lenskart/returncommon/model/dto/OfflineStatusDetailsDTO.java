package com.lenskart.returncommon.model.dto;

import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;

@Data
@ToString
public class OfflineStatusDetailsDTO implements Serializable {

    private Long id;
    private String orderId;
    private String uwItemId;
    private String status;
    private String subStatus;
    private String vendorId;
    private Date updatedAt;
    private Date createdAt;
    private Boolean syncStatus;
    private String invoiceUrl;
    private String qcStatus;
}