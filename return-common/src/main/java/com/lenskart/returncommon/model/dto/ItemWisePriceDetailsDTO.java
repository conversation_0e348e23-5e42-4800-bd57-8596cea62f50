package com.lenskart.returncommon.model.dto;

import lombok.Data;

@Data
public class ItemWisePriceDetailsDTO {

    private Integer id;
    private Integer itemId;
    private Integer uwItemId;
    private Integer incrementId;
    private Double shippingCharges;
    private Double storeCreditDiscount;
    private Double giftVoucherDiscount;
    private Double prepaidDiscount;
    private Double implicitDiscount;
    private Double lenskartDiscount;
    private Double lenskartPlusDiscount;
    private Double couponDiscount;
    private Double itemTotalAfterDiscount;
    private Double exchangeDiscount;
    private Double taxCollected;
    private Double fcDiscount;
    private Double giftCardDiscount;
}
