package com.lenskart.returncommon.model.request;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnItem;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import lombok.Data;

import java.util.List;

@Data
public class ReturnCommunicationRequest {
    private ReturnCreationRequest returnCreationRequest;
    private ReverseCourierDetail reverseCourierDetail;
    private List<UwOrderDTO> uwOrderDTOs;
    private List<OrdersDTO> orderDTOs;
    private Long groupId;
    private List<ReturnItem> exchangeItemList;
    private Integer requestId;
    private Integer returnId;
}
