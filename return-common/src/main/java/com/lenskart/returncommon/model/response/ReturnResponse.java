package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ReturnResponse {
    @JsonProperty(value = "return_id")
    private Integer returnId;
    @JsonProperty(value = "status")
    private String status;
    @JsonProperty(value = "return_create_datetime")
    private Date returnCreationDate;
    @JsonProperty(value = "delight_action_response")
    private DelightActionResponse delightActionResponse;
}
