package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.ordermetadata.dto.ReverseCourierMetaData;
import com.lenskart.ordermetadata.dto.request.ExchangeAddressDTO;
import com.lenskart.ordermetadata.dto.request.PickupAddressDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.request.ReturnSourcesDTO;
import com.lenskart.ordermetadata.dto.response.DispensingDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ReturnCreationRequestDTO {
    @JsonProperty("items")
    private List<ReturnItemDTO> items;
    @JsonProperty("pickup_address")
    private PickupAddressDTO ReversePickupAddress;
    @JsonProperty("exchange_address")
    private ExchangeAddressDTO exchangeAddress;
    @JsonProperty("facility_code")
    private String facilityCode;
    @JsonProperty("return_source")
    private ReturnSourcesDTO returnSource;
    @JsonProperty("initiated_by")
    private Integer initiatedBy;
    @JsonProperty("return_method")
    private String returnMethod;
    @JsonIgnore
    private Map<ReturnItemDTO, Integer> productIdsMap;
    @JsonIgnore
    private Integer incrementId;
    @JsonProperty("store_email")
    private String storeEmail;
    @JsonProperty("is_courier_reassigned")
    private Boolean isCourierReassigned;
    @JsonProperty("new_courier")
    private String newCourier;
    @JsonProperty("old_courier")
    private String oldCourier;
    @JsonProperty("salesman_name")
    private String salesmanName;
    @JsonProperty("salesman_number")
    private String salesmanNumber;
    @JsonProperty("callback_required_to_salesman")
    private boolean callbackRequiredToSalesman;
    @JsonProperty("store_facility_code")
    private String storeFacilityCode;
    @JsonProperty("enforce_refund_at_store")
    private boolean enforceRefundAtStore;
    @JsonProperty("purchase_order_details")
    private PurchaseOrderDetailsDTO purchaseOrderDetailsDTO;
    @JsonProperty("reverse_courier_meta_data")
    private ReverseCourierMetaData reverseCourierMetaData;
    @JsonProperty("dispensing_dto")
    private DispensingDTO dispensingDTO;
    @JsonProperty("request_approver")
    private String requestApprover;
}
