package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class OrderRefundDetails {
    @ApiObjectField(name = "refund_method", description = "refund method")
    @JsonProperty("refund_method")
    private String refundMethod;

    @ApiObjectField(name = "total_refund_amount", description = "refund amount")
    @JsonProperty("total_refund_amount")
    private double refundAmount;
}
