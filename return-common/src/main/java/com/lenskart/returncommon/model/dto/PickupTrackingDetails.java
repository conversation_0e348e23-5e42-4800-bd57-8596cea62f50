package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class PickupTrackingDetails {
    @JsonProperty("courier")
    @ApiObjectField(name = "courier", description = "courier")
    private String courier;
    @JsonProperty("tracking_id")
    @ApiObjectField(name = "tracking_id", description = "tracking_id")
    private String trackingId;
    @JsonProperty("tracking_link")
    @ApiObjectField(name = "tracking_link", description = "tracking_link")
    private String trackingLink;
}
