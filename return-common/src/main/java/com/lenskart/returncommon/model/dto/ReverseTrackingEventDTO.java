package com.lenskart.returncommon.model.dto;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
public class ReverseTrackingEventDTO {
    private Integer returnId;
    private Integer orderId;
    private String reverseAwb;
    private String trackingRemark;
    private Integer trackingStatusCode;
    private String pickupAttempted;
    private String nonPickupReason;
    private String reverseMappedStatus;
    private Date eventDateTime;
    private Date pickupDateTime;
    private Date createDatetime;
    private Integer notificationEventId;
}
