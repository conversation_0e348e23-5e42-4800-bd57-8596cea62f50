package com.lenskart.returncommon.model.enums;

public enum LKCountry {
    IN("IN"),
    SG("SG"),
    VN("VN"),
    AE("AE");

    private final String value;

    private LKCountry(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }

    public static LKCountry byValue(String value) {
        LKCountry result = IN;
        LKCountry[] var2 = values();
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            LKCountry lk = var2[var4];
            if (lk.value.equalsIgnoreCase(value)) {
                result = lk;
                break;
            }
        }

        return result;
    }
}
