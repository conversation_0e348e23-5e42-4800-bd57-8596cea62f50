package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.Gson;
import com.lenskart.ordermetadata.dto.ItemWiseAmountDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DualRefundRequest {
    private static final Gson gson = new Gson();
    @JsonProperty("id")
    private Long id;

    @JsonProperty("customer_id")
    private Long customerId;

    @JsonProperty("increment_id")
    private Long incrementId;

    @JsonProperty("refund_method")
    private String refundMethod;

    @JsonProperty("neft_detail_id")
    private Long neftDetailId;

    @JsonProperty("refund_type")
    private String refundType;

    @JsonProperty("type")
    private String type;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("order_status")
    private String orderStatus;

    @JsonProperty("requested_by")
    private String requestedBy;

    @JsonProperty("source")
    private String source;

    @JsonProperty("status")
    private String status;

    @JsonProperty("status_detail")
    private String statusDetail;

    @JsonProperty("return_id")
    private Long returnId;

    @JsonIgnore
    @JsonProperty("refund_id")
    private Long refundId;

    @JsonIgnore
    @JsonProperty("storeCode")
    private String storeCode;

    @JsonProperty("neft_to_sc")
    private Integer neftToSc;

    @JsonProperty("lenskart_discount")
    private BigDecimal lenskartDiscount;

    @JsonProperty("lenskart_plus_discount")
    private BigDecimal lenskartPlusDiscount;

    @JsonProperty("facility_id")
    private String facilityId;

    @JsonProperty("transaction_type")
    private String transactionType;

    @JsonProperty("category")
    private String category;

    @JsonProperty("item_ids")
    private List<Integer> itemIds;

    @JsonProperty("refund_amount")
    private java.lang.Double refundAmount;

    @JsonProperty("isAdditionalRefund")
    private boolean isAdditionalRefund;

    @JsonProperty("additional_amount")
    private Double additionalAmount;

    @JsonProperty("item_wise_amount")
    private ItemWiseAmountDTO itemWiseAmount;

    @JsonProperty("is_customer_refund_eligible")
    private String isCustomerRefundEligible;

    @JsonProperty("is_wallet_adjustment_eligible")
    private String isWalletAdjustmentEligible;

    @JsonProperty("do_customer_wallet_processing")
    private boolean doCustomerWalletProcessing;

    @JsonProperty("is_conversion_sc_to_neft")
    private boolean isConversionScToNeft;

    @JsonProperty("is_fofo_old_order")
    private boolean isFofoOldOrder;

    @JsonProperty("is_pos_grn")
    private boolean isPosGrn;

    @JsonIgnore
    private boolean isFastOrExceptionRefund;

    @JsonIgnore
    private boolean isExchangeCreated;

    @JsonIgnore
    private boolean isReceived;

    @JsonProperty("order_refund_reason")
    private String orderRefundReason;

    @JsonProperty("return_status")
    private String returnStatus;

    @JsonIgnore
    private boolean isEasyRefund;
    @JsonIgnore
    private String payoutLink;

    @JsonIgnore
    private String additionalComment;

    @JsonIgnore
    private String gatewayName;

    public String toString() {
        return gson.toJson(this);
    }

}

