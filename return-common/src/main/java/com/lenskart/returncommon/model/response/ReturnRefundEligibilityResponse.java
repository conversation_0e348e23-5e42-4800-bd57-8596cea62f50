package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.MVCDetailsDTO;
import lombok.Data;
import lombok.ToString;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ToString
public class ReturnRefundEligibilityResponse {
    private static final String COURIER_PICKUP_TEXT = "picked up";
    private static final String RETURN_AT_STORE = "received and inspected by lenskart";
    private static final String RETURN_AT_WAREHOUSE = "received and inspected by lenskart";
    @JsonProperty("items")
    private List<ItemResponse> itemsResponseList;
    @JsonProperty("order_refunded_amount")
    private double orderRefundedAmount;
    @JsonProperty("is_fraud")
    private boolean isFraud;
    @JsonProperty("exchange_dispatch_point_mapping")
    private Map<String, String> exchangeDispatchPointMapping;
    {
        exchangeDispatchPointMapping = new HashMap<>();
        exchangeDispatchPointMapping.put("courier_pickup", COURIER_PICKUP_TEXT);
        exchangeDispatchPointMapping.put("return_at_store", RETURN_AT_STORE);
        exchangeDispatchPointMapping.put("return_at_wh", RETURN_AT_WAREHOUSE);
    }
    @JsonProperty("is_order_cancellable")
    private boolean isOrderCancellable;
    @JsonProperty("mvc_details")
    private MVCDetailsDTO mvcDetails;

}
