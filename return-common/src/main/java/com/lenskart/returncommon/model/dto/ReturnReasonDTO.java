package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReturnReasonDTO {
    private String reason;
    private Integer id;
    private String reason_tag;
    private List<SecondaryReasonDTO> secondary_reasons;
    private Integer uwItemId;
    private Integer returnId;
    private Integer orderId;
    private String secondaryReason;
    private String primaryReason;
}
