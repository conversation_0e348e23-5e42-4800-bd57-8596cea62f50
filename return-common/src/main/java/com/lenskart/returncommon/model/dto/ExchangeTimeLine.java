package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class ExchangeTimeLine {
    @JsonProperty("exchange_status")
    @ApiObjectField(name = "exchange_status", description = "exchange_status")
    private String exchangeStatus;

    @JsonProperty("exchange_sub_status")
    @ApiObjectField(name = "exchange_sub_status", description = "exchange_sub_status")
    private String exchangeSubStatus;

    @JsonProperty("exchange_dispatch_date")
    @ApiObjectField(name = "exchange_dispatch_date", description = "exchange_dispatch_date")
    private Long exchangeDispatchDate;

}

