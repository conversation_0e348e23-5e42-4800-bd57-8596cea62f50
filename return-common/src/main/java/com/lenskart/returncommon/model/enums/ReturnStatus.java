package com.lenskart.returncommon.model.enums;

public enum ReturnStatus {

    RETURN_RECEIVED("return_received","","",1),
    RETURN_UNDER_FOLLOWUP("return_under_followup","","",2),
    RETURN_FOLLOWED_UP("return_followed_up","","",3),
    RETURN_OVERRIDE("return_override","","",4),
    RETURN_PENDING_APPROVAL("return_pending_approval","","",5),
    RETURN_REFUNDED("return_refunded","","",6),
    RETURN_RESHIP("return_reship","","",7),
    NEW_REVERSE_PICKUP("new_reverse_pickup","","",8),
    REFERENCE_ID_ISSUED("reference_id_issued","","",9),
    AWB_ASSIGNED("awb_assigned","","",10),
    CANCELLED("cancelled","","",11),
    RETURN_CLOSED("return_closed","","",12),
    SC_REFUNDED_RECEIVING_PENDING("sc_refunded_receiving_pending","","",13),
    RTO_COD_STOCKIN("rto_cod_stockin","","",14),
    VENDOR_SYNC_PENDING("vendor_sync_pending","","",15),
    RETURN_REFUND_REJECTED("return_refund_rejected","","",16),
    AUTO_SYNC_FAILED("auto_sync_failed","","",17),
    CUSTOMER_CANCELLED("customer_cancelled","","",18),
    RETURN_EXCHANGE("return_exchange","","",19),
    SELFDISPATCHED("selfdispatched","","",20),
    RETURN_CLOSED_TBYB("return_closed_tbyb","","",21),
    EASY_REFUND_GIVEN_PICKUP_CANCELLED("easy_refund_given_pickup_cancelled","","",22),
    PARTIAL_EASY_REFUND_GIVEN_PICKUP_CANCELLED("partial_easy_refund_given_pickup_cancelled","","",23),
    RETURN_REFUNDED_RECEIVING_PENDING("return_refunded_receiving_pending","","",24),
    INITIATED_REVERSE("initiated_reverse","","",25),
    RETURN_EXPECTED_POS("return_expected_pos","","",26),
    RETURN_EXPECTED_WH("return_expected_wh","","",27),
    RETURN_ACCEPTED("return_accepted","","",28),
    RETURN_REJECTED("return_rejected","","",29),
    RETURN_NEED_APPROVAL("return_need_approval","","",30),
    RETURN_NEED_APPROVAL_FROM_WAREHOUSE("return_need_approval_from_whse","","",30),
    RETURN_REJECTED_HANDOVER_PENDING("return_rejected_handover_pending","","",31),
    RETURN_REJECTED_HANDOVER_DONE("return_rejected_handover_done","","",31),
    RETURN_RECEIVED_ACTION_PENDING("return_received_action_pending","","",1),

    RETURN_APPROVAL_STATUS_UPDATED("approval_status_updated","","",32),
    RETURN_RECEIVED_AT_WAREHOUSE("return_received","","",1),
    RETURN_RECEIVED_AT_STORE("return_received","","",1),
    RTO_RECEIVED_AT_WAREHOUSE("return_received","","",1);


    private String status;
    private String returnMethod;
    private String returnSource;
    private int priority;


    ReturnStatus(String status,String returnMethod,String returnSource,int priority){
        this.status = status;
        this.returnMethod = returnMethod;
        this.returnSource = returnSource;
        this.priority = priority;
    }

    public String getStatus(){
        return this.status;
    }

    public String getName() {
        return this.name();
    }

    public String getReturnMethod(){
        return this.returnMethod;
    }

    public String getReturnSource(){
        return this.returnSource;
    }

    public int getPriority(){
        return this.priority;
    }

    public static ReturnStatus getEnum(String returnMethod, String returnSource) {
        for(ReturnStatus v : values()){
            if(v.getReturnMethod().equalsIgnoreCase(returnMethod) && v.getReturnSource().equalsIgnoreCase(returnSource)){
                return v;
            }
        }
        return null;
    }

    public static String getEnumNameByStatus(String returnStatus) {
        for(ReturnStatus v : values()){
            if(v.getStatus().equalsIgnoreCase(returnStatus)){
                return v.toString();
            }
        }
        return null;
    }
}
