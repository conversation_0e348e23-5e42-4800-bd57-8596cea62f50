package com.lenskart.returncommon.model.dto;

import lombok.Data;

@Data
public class DecisionTableRefundDTO {

    private Integer returnId;
    private Integer uwItemId;
    private String reverseType;
    private String navChannel;
    private String returnInitiatedSource;
    private String triggerPoint;
    private Boolean isPseudoGatepass = false;
    private Boolean isBranded = false;
    private Boolean isQcPass;
    private String category;
    private Integer returnPeriod = 0;
    private Boolean isAccessoryMissing = false;
    private Boolean isLastPiece = false;
    private Boolean isLensOnly = false;
    private String countryCode;
    private Boolean insurancePolicy;
    private Double amountValidity = null;
    private Boolean blacklistedPhoneNumbers;
    private Boolean blacklistedPincodes;
    private Integer returnCount;
    private Integer exchangeCount;
    private Integer refundCount;
    private String brand;
    public Boolean isReturnable;
    private String action;
    private Boolean doRefund;
    private String refundMethod;
    private String ruleCalledFrom;
    private String draftReturnMethod;
    private Integer exchangeAllowed;
    private String exchangeOrderDispatch;
    private String returnReasons;
    private String paymentMethod;
    private int returnEligibiityPeriod = 30;
    private String refundDispatch = "CourierPickup";
    private Integer itemWarrantyPeriod;
    private Integer customerScore = 1;
    private Integer storeScore = 1;

    public interface RETURN_SOURCE {
        String POS = "pos";
        String ONLINE = "online";
        String ONLINE_WEB = "web";
        String ONLINE_VSM = "vsm";
        String DIRECT = "direct";
    }
}
