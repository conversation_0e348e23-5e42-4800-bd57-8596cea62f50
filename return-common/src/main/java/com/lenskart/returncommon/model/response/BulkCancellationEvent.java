package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BulkCancellationEvent {
    @JsonProperty("return_id")
    private Integer returnId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    @JsonProperty("message")
    private String message;
    @JsonProperty("source")
    private String source;
}
