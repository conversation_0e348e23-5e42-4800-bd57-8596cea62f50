package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Data
public class Product {

    @JsonProperty("productId")
    @ApiObjectField(name = "productId")
    private String productId;

    @JsonProperty("variant")
    @ApiObjectField(name = "variant")
    private String variant;

    @JsonProperty("sumInsured")
    @ApiObjectField(name = "sumInsured")
    private Double sumInsured;

    @JsonProperty("maximumClaimableAmount")
    @ApiObjectField(name = "maximumClaimableAmount")
    private Double maximumClaimableAmount;

    @JsonProperty("category")
    @ApiObjectField(name = "category")
    private String category;

    @JsonProperty("model")
    @ApiObjectField(name = "model")
    private String model;

    @JsonProperty("powerType")
    @ApiObjectField(name = "powerType")
    private String powerType;

    @JsonProperty("prescriptionDetails")
    @ApiObjectField(name = "prescriptionDetails")
    private PrescriptionDetails prescriptionDetails;

    @JsonProperty("price")
    @ApiObjectField(name = "price")
    private Double price;

    @JsonProperty("productDescription")
    @ApiObjectField(name = "productDescription")
    private String productDescription;

    @JsonProperty("productUrl")
    @ApiObjectField(name = "productUrl")
    private List<String> productUrl = null;

    @JsonProperty("copayAmount")
    @ApiObjectField(name = "copayAmount")
    private Double copayAmount;
}
