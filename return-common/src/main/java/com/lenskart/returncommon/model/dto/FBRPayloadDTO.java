package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class FBRPayloadDTO extends BasePayloadDTO {
    @JsonProperty("gv_code")
    private String gvCode;
    @JsonProperty("expiry_date")
    private String expiryDate;
    @JsonProperty("name")
    private String name;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("currency")
    private String currency;

    public FBRPayloadDTO(BasePayloadDTO basePayloadDTO) {
        super(basePayloadDTO);
    }
}
