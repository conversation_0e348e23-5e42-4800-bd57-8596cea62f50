package com.lenskart.returncommon.model.dto;

import com.lenskart.ordermetadata.dto.request.ExchangeAddressDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExchangeOrderCreationRequest implements Serializable{

    private List<ExchangeItem> items;

    private ExchangeAddressDTO shippingAddress;

    private String paymentMethod;

    private Integer returnId;
}
