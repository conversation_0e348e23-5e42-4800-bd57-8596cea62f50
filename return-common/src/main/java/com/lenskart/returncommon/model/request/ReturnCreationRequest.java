package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.ExchangeAddress;
import com.lenskart.returncommon.model.dto.PickupAddress;
import com.lenskart.returncommon.model.dto.ReturnItem;
import com.lenskart.returncommon.model.enums.ReturnSources;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;
import java.util.Map;

@Data
@ToString
public class ReturnCreationRequest {

    @ApiObjectField(name = "items", description = "items to be returned", required=true)
    @JsonProperty("items")
    private List<ReturnItem> items;

   /* @ApiObjectField(name = "refund_method", description = "Refund method for return", required=true)
    @JsonProperty("refund_method")
    private String refundMethod;*/

    @ApiObjectField(name = "pickup_address", description = "Address for revrsePickup -- Optional")
    @JsonProperty("pickup_address")
    private PickupAddress ReversePickupAddress;

    @ApiObjectField(name = "exchange_address", description = "Address for exchange creation -- Optional")
    @JsonProperty("exchange_address")
    private ExchangeAddress exchangeAddress;

   // @JsonIgnore
    @ApiObjectField(name = "facility_code", description = "Facility code for return", required=true)
    @JsonProperty("facility_code")
    private String facilityCode;

    @ApiObjectField(name = "return_source", description = "Source ", required=true)
    @JsonProperty("return_source")
    private ReturnSources returnSource;

    @JsonIgnore
    @ApiObjectField(name = "initiated_by", description = "initiated by -- optional")
    @JsonProperty("initiated_by")
    private Integer initiatedBy;

    @ApiObjectField(name = "return_method", description = "return_method ", required=true)
    @JsonProperty("return_method")
    private String returnMethod;
    
    @JsonIgnore
    private  Map<ReturnItem,Integer> productIdsMap;
    
    @JsonIgnore
    private Integer incrementId;

    @ApiObjectField(name = "store_email", description = "store_email ", required=true)
    @JsonProperty("store_email")
    private String storeEmail;

    @ApiObjectField(name = "is_courier_reassigned", description = "is_courier_reassigned ", required=false)
    @JsonProperty("is_courier_reassigned")
    private Boolean isCourierReassigned;

    @ApiObjectField(name = "new_courier", description = "new_courier ", required=false)
    @JsonProperty("new_courier")
    private String newCourier;

    @ApiObjectField(name = "old_courier", description = "old_courier ", required=false)
    @JsonProperty("old_courier")
    private String oldCourier;

    @ApiObjectField(name = "salesman_name", description = "salesman_name ", required=false)
    @JsonProperty("salesman_name")
    private String salesmanName;

    @ApiObjectField(name = "salesman_number", description = "salesman_number ", required=false)
    @JsonProperty("salesman_number")
    private String salesmanNumber;

    @ApiObjectField(name = "callback_required_to_salesman", description = "callback_required_to_salesman ", required=false)
    @JsonProperty("callback_required_to_salesman")
    private boolean callbackRequiredToSalesman;

    @ApiObjectField(name = "store_facility_code", description = "store_facility_code", required=false)
    @JsonProperty("store_facility_code")
    private String storeFacilityCode;

}
