package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SprinklrNeedApprovalRequest {
    @JsonProperty("returnId")
    Integer returnId;
    @JsonProperty("incrementID")
    Integer incrementId;
    @JsonProperty("lkStoreCode")
    String lkStoreCode;
    @JsonProperty("uw_item_id")
    Integer uwItemId;
    @JsonProperty("magento_item_id")
    Long magentoItemId;
    @JsonProperty("returnStatus")
    String returnStatus;
}
