package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class ReturnTimeLine {

    @JsonProperty("return_status")
    @ApiObjectField(name = "return_status", description = "refund timelines")
    private String returnStatus;

    @JsonProperty("return_sub_status")
    @ApiObjectField(name = "return_sub_status", description = "refund timelines")
    private String returnSubStatus;

    @JsonProperty("return_estimated_date")
    @ApiObjectField(name = "return_estimated_date", description = "refund timelines")
    private Long returnEstimatedDate;
}

