package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lenskart.returncommon.model.enums.LKCountry;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class RefundCreateRequest {
    private static Gson gson = new GsonBuilder().setPrettyPrinting().create();

    @ApiObjectField(name = "magento_item_id", description = "Magento Item Id")
    @JsonProperty("magento_item_id")
    private Integer magentoItemId;
    @ApiObjectField(name = "refund_method", description = "refund method selected by customer")
    @JsonProperty("refund_method")
    private String refundMethod;
    @JsonIgnore
    private LKCountry lkCountry;
}
