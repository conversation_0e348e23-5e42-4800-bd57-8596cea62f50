package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import lombok.Data;

import java.util.List;

@Data
public class ReturnCommunicationRequestDTO {
    private List<OrdersDTO> returnOrders;
    private List<UwOrderDTO> uwOrders;
    private ReturnCreationRequest returnCreationRequest;
    private ReverseCourierDetail reverseCourierDetail;
    private Long groupId;
}
