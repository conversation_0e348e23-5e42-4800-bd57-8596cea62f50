package com.lenskart.returncommon.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class UwOrderDTO {
    private Integer uwItemId;
    private int incrementId;
    private int itemId;
    private int productId;
    private String barcode;
    private int parentUw;
    private int vsmStockout;
    private String fitting;
    private Date createdAt;
    private Date expectedDispatchDate;
    private Double totalPrice;
    private String facilityCode;
    private String shipmentStatus;
    private String shipmentState;
    private String unicomOrderCode;
    private String productDeliveryType;
    private Boolean shipToStoreRequired = false;
    private Boolean isLocalFittingRequired = false;
    private String navChannel;
    private Integer b2bRefrenceItemId;
    private String classification;
    private String brand;
    private String lensPackage;
    private String productValue;
    private String productSku;
    private String classificationName;
    private String shippingPackageId;
    private String method;
    private String isFranchise;
    private String channel;
    private boolean isLensOnly = false;
}
