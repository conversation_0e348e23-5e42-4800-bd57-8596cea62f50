package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

@ToString
@Data
public class HubDetails {

    @JsonProperty("facilityCode")
    @ApiObjectField(name = "facilityCode")
    String facilityCode;

    @JsonProperty("pincode")
    @ApiObjectField(name = "pincode")
    String pincode;

    @JsonProperty("name")
    @ApiObjectField(name = "name")
    String name;

    @JsonProperty("address1")
    @ApiObjectField(name = "address1")
    String address1;

    @JsonProperty("address2")
    @ApiObjectField(name = "address2")
    String address2;

    @JsonProperty("city")
    @ApiObjectField(name = "city")
    String city;

    @JsonProperty("state")
    @ApiObjectField(name = "state")
    String state;

    @JsonProperty("country")
    @ApiObjectField(name = "country")
    String country;

    @JsonProperty("gstin")
    @ApiObjectField(name = "gstin")
    String gstin;

    @JsonProperty("customizations")
    @ApiObjectField(name = "customizations")
    HubExclusion hubExclusion;

}
