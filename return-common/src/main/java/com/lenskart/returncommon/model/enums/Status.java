package com.lenskart.returncommon.model.enums;

public enum Status {
    NEW("new", "ORDER_NOT_CONFIRMED"),
    PROCESSING("processing", "ORDER_PLACED"),
    COMPLETE("complete", "PACKED"),
    CLOSED("closed", "CANCELLED"),
    CLOSE("close", "CANC<PERSON>LED"),
    <PERSON>NC<PERSON>ED("canceled", "CANCELLED"),
    PENDING("pending", "PAYMENT_NOT_INITIATED"),
    PENDING_FOLLOWEDUP("pending_followedup", "PAYMENT_NOT_INITIATED"),
    PROCESSING_NEW("processing_new", "ORDER_NOT_CONFIRMED"),
    MONEY_ACCEPTED_OFFLINE_NO("money_accepted_offline_no", "ORDER_NOT_CONFIRMED"),
    PENDING_PAYMENT_CHECK("pending_payment_check", "PAYMENT_NOT_INITIATED"),
    MONEY_ACCEPTED_OFFLINE_DELIVERED("money_accepted_offline_delivered", "ORDER_NOT_CONFIRMED"),
    PROCESSING_STORECREDIT("processing_storecredit", "ORDER_NOT_CONFIRMED"),
    PENDING_PARTIALPAYMENT("pending_partialpayment", "PAYMENT_NOT_INITIATED"),
    PENDING_PAYMENT("pending_payment", "PAYMENT_NOT_INITIATED"),
    PAYMENT_REVIEW("payment_review", (String)null),
    PROCESSING_CONFIRMATION_PENDING("processing_confirmation_pending", "ORDER_PLACED"),
    PROCESSING_POWER_FOLLOWUP("processing_power_followup", "ORDER_PLACED"),
    PROCESSING_POWER_FOLLOWUP_HTO("processing_power_followup_hto", "ORDER_PLACED"),
    PROCESSING_RED_FLAG("processing_red_flag", "PROCESSING_RED_FLAG"),
    PROCESSING_PACKED("processing_packed", (String)null),
    PROCESSING_BLACKLIST("processing_blacklist", (String)null),
    PROCESSING_CHARGEBACK("processing_chargeback", "ORDER_PLACED"),
    PROCESSING_POWER_FOLLOWUP_VERIFY("processing_power_followup_verify", "ORDER_PLACED"),
    PROCESSING_POWERFOLLOWUP_CONFIRM("processing_powerfollowup_confirm", "ORDER_PLACED"),
    PROCESSING_POWER_FOLLOWUP_CONFIRM("processing_power_followup_confirm", "ORDER_PLACED"),
    COMPLETE_SHIPPED("complete_shipped", "DISPATCHED"),
    COMPLETE_CHARGEBACK("complete_chargeback", "PACKED"),
    COMPLETE_REDFLAG("complete_redflag", "PACKED"),
    COMPLETE_DELIVERED_IN_1_DAY("complete_delivered_in_1_day", (String)null),
    COMPLETE_DELIVERED_IN_2_DAYS("complete_delivered_in_2_days", (String)null),
    COMPLETE_DELIVERED_IN_3_DAYS("complete_delivered_in_3_days", (String)null),
    COMPLETE_DELIVERED_IN_5_DAYS("complete_delivered_in_5_days", (String)null),
    COMPLETE_DELIVERED_IN_7_PLUS_DAY("complete_delivered_in_7_plus_day", (String)null),
    COMPLETE_INCORRECT_ADDRESS("complete_incorrect_address", (String)null),
    COMPLETE_DELAY_SHIPMENT("complete_delay_shipment", "PACKED"),
    COMPLETE_DELIVERED_TAMPERED("complete_delivered_tampered", (String)null),
    COMPLETE_LOST_BY_COURIER("complete_lost_by_courier", "DISPATCHED"),
    COMPLETE_NO_SERVICE("complete_no_service", (String)null),
    COMPLETE_ON_HOLD("complete_on_hold", (String)null),
    DELIVERED("delivered", "DELIVERED"),
    AWAITED_RTO("awaited_rto", "RETURNED"),
    INTERNAL_RTO("internal_rto", "RETURNED"),
    COMPLETE_ARRANGED_RTO("complete_arranged_rto", "RETURNED"),
    COMPLETE_RECEIVED_RTO("complete_received_rto", "RETURNED"),
    PREDELIVERY_PRODUCTCHANGE("predelivery_productchange", (String)null),
    RETURN_CHANGED("return_changed", (String)null),
    CLOSED_CHARGEBACK("closed_chargeback", (String)null),
    CLOSED_LOST_BY_COURIER("closed_lost_by_courier", (String)null),
    RETURN_REFUNDED("return_refunded", "RETURN"),
    RETURN_REFUNDED_PART("return_refunded_part", "RETURN"),
    RETURN_REFUNDED_ONLINE("return_refunded_online", "RETURN"),
    RETURN_REFUNDED_CHECK("return_refunded_check", (String)null),
    LOST_REFUNDED_ONLINE("lost_refunded_online", (String)null),
    LOST_REFUNDED_CHECK("lost_refunded_check", (String)null),
    PREDELIVERY_REFUND("predelivery_refund", "CANCELLED"),
    PRE_DELIVERY_STORECREDIT("pre_delivery_storecredit", "CANCELLED"),
    RTO("rto", "RETURNED"),
    CLOSED_AWAITED_RTO("closed_awaited_rto", "RETURNED"),
    TESTORDER("testorder", (String)null),
    ORDER_NOT_CONFIRMED("order_not_confirmed", "ORDER_NOT_CONFIRMED"),
    RETURN_RESHIP("return_reship", "RETURNED"),
    RETURN_REFUNDED_SC_DELAY("return_refunded_sc_delay", (String)null),
    RETURN_REFUNDED_STORE_CREDIT("return_refunded_store_credit", "RETURN"),
    RETURN_REFUNDED_STORE_CREDIT_RECEIVING_PENDING("return_refunded_sc_recv_pending", (String)null),
    SC_REFUNDED_RECEIVING_PENDING("sc_refunded_receiving_pending", (String)null),
    FC_REFUNDED_RECEIVING_PENDING("fc_refunded_receiving_pending", (String)null),
    LOST_REFUNDED_STORE_CREDIT("lost_refunded_store_credit", (String)null),
    RETURN_REFUNDED_FRANCHISE_CREDIT("return_refunded_franchise_credit", "RETURN"),
    RETURN_REFUNDED_FRANCHISE_CREDIT_RECEIVING_PENDING("return_refunded_fc_recv_pending", (String)null),
    RETURN_RESHIP_DELIVERED("return_reship_delivered", "DELIVERED"),
    RETURN_REFUNDED_NEFT("return_refunded_neft", "RETURN"),
    RETURN_REFUNDED_OFFLINE("return_refunded_online", "RETURN"),
    RETURN_REFUNDED_ONLINE_RECEIVING_PENDING("return_refunded_online_recv_pending", (String)null),
    RETURN_CHANGE_REFUNDED("return_change_refunded", (String)null),
    INITIATED_REVERSE("initiated_reverse", "RETURN"),
    CANCELED_PAYMENT_CHECK("canceled_payment_check", "CANCELLED"),
    PREDELIVERY_CANCELLATION("predelivery_cancellation", "CANCELLED"),
    CANCELED_CUSTOMER_REQUEST("canceled_customer_request", "CANCELLED"),
    CANCELED_DUPLIACTE("canceled_dupliacte", "CANCELLED"),
    CANCELED_FAKE("canceled_fake", "CANCELLED"),
    CANCELED_NRE("canceled_nre", "CANCELLED"),
    HOLDED("holded", "HOLDED"),
    HOLD_COD_CONFIRMATION("hold_cod_confirmation", "HOLDED"),
    HOLD_POSTPONE_DELIVERY("hold_postpone_delivery", "HOLDED"),
    HOLD_ADDRESS_CONFIRMATION("hold_address_confirmation", "HOLDED"),
    HOLD_POWER_CONFIRMATION("hold_power_confirmation", "HOLDED"),
    HOLD_PAYMENT_CONFIRMATION("hold_payment_confirmation", "HOLDED"),
    HOLD_CANCELLATION("hold_cancellation", "HOLDED"),
    HOLD_DUPLICATE("hold_duplicate", "HOLDED"),
    MONEY_ACCEPTED_OFFLINE_NOT_DELIVERED("money_accepted_offline_not_delivered", "ORDER_NOT_CONFIRMED"),
    PROCESSING_NOT_IN_STOCK("processing_not_in_stock", (String)null),
    PROCESSING_LENS_ONLY_PENDING("processing_lens_only_pending", (String)null),
    COMPLETE_DELIVERED_IN_2_DAY("complete_delivered_in_2_day", (String)null),
    COMPLETE_DELIVERED_IN_3_DAY("complete_delivered_in_3_day", (String)null),
    COMPLETE_DELIVERED_IN_5_DAY("complete_delivered_in_5_day", (String)null),
    COMPLETE_DELIVERED_IN_7_DAY("complete_delivered_in_7_day", (String)null),
    COMPLETE_RESHIP_NEW_ADDRESS("complete_reship_new_address", "DISPATCHED"),
    HOLD_LANGUAGE_BARRIER("hold_language_barrier", "HOLDED"),
    HOLD_POWERFUP("hold_powerfup", "HOLDED"),
    FRAUD("fraud", (String)null),
    DELETE_STORECREDIT("delete_storecredit", (String)null),
    PROCESSING_IVR_CONFIRM("processing_ivr_confirm", (String)null);

    private String value;
    private String trackingStatus;

    private Status(String value, String trackingStatus) {
        this.value = value;
        this.trackingStatus = trackingStatus;
    }

    public String getValue() {
        return this.value;
    }

    public String getTrackingStatus() {
        return this.trackingStatus;
    }
}
