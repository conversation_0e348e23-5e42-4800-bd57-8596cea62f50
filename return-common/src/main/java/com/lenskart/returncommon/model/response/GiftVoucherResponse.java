package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class GiftVoucherResponse {
    @JsonProperty("result")
    private ResultDTO result;
    @JsonProperty("status")
    private Integer status;
    @JsonProperty("traceId")
    private String traceId;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JsonProperty("id")
        private Long id;
        @JsonProperty("code")
        private String code;
        @JsonProperty("balance")
        private Double balance;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("status")
        private String status;
        @JsonProperty("expiredAt")
        private String expiredAt;
        @JsonProperty("phoneCode")
        private String phoneCode;
        @JsonProperty("customerPhone")
        private String customerPhone;
        @JsonProperty("customerId")
        private Integer customerId;
        @JsonProperty("customerName")
        private String customerName;
        @JsonProperty("customerEmail")
        private String customerEmail;
        @JsonProperty("recipientName")
        private String recipientName;
        @JsonProperty("recipientEmail")
        private String recipientEmail;
        @JsonProperty("message")
        private String message;
        @JsonProperty("createdBy")
        private String createdBy;
        @JsonProperty("updatedBy")
        private String updatedBy;
        @JsonProperty("comments")
        private String comments;
        @JsonProperty("genericGv")
        private Boolean genericGv;
    }
}
