package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.response.ExchangeDetails;
import com.lenskart.returncommon.model.dto.*;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Date;
import java.util.List;

@Data
public class ReturnDetailsResponse {
    @ApiObjectField(name = "magento_item_id", description = "magento item id")
    @JsonProperty("magento_item_id")
    private Long magentoItemId;

    @ApiObjectField(name = "return_id", description = "return id")
    @JsonProperty("return_id")
    private int returnId;

    @ApiObjectField(name = "group_id", description = "group id")
    @JsonProperty("group_id")
    private Long groupId;

    @ApiObjectField(name = "return_label_generated", description = "return label generated")
    @JsonProperty("return_label_generated")
    private boolean returnLabelGenerated;

    @ApiObjectField(name = "pickup_address", description = "pickup address details")
    @JsonProperty("pickup_address")
    private PickUpAddressDetails pickUpAddressDetails;

    @ApiObjectField(name = "is_self_dispatch", description = "magento item id")
    @JsonProperty("is_self_dispatch")
    private Boolean isSelfDispatch;

    @ApiObjectField(name = "itemId", description = "item id")
    @JsonProperty("itemId")
    private int itemId;

    @ApiObjectField(name = "uwItemId", description = "uw item id")
    @JsonProperty("uwItemId")
    private int uwItemId;

    @ApiObjectField(name = "exchangeDetails", description = "Exchange Details")
    @JsonProperty("exchangeDetails")
    private ExchangeDetails exchangeDetails;

    @ApiObjectField(name = "refund_details", description = "refund details")
    @JsonProperty("refund_details")
    private List<RefundDetails> refundDetails;

    @ApiObjectField(name = "order_refunded_amount", description = "refunded amount at order level")
    @JsonProperty("order_refunded_amount")
    private double orderRefundedAmount;

    @ApiObjectField(name ="total_refund_amount",description = "refunded amount on item level")
    @JsonProperty("total_refund_amount")
    private double totalRefundAmount;

    @ApiObjectField(name ="return_cancel_refunded_amount",description = "refunded amount at cancel/return level")
    @JsonProperty("return_cancel_refunded_amount")
    private double returnCancelRefundedAmount;

    @ApiObjectField(name ="total_refund_complete_date",description = "total_refund_complete_date")
    @JsonProperty("total_refund_complete_date")
    private Long totalRefundCompleteDate;


    @ApiObjectField(name = "return_method", description = "return method")
    @JsonProperty("return_method")
    private String returnMethod;

    @ApiObjectField(name = "return_source", description = "return source")
    @JsonProperty("return_source")
    private String returnSource;

    @ApiObjectField(name = "facility_code", description = "facility code")
    @JsonProperty("facility_code")
    private String facilityCode;

    @ApiObjectField(name = "return_status_history", description = "return status history")
    @JsonProperty("return_status_history")
    private List<ReturnStatusHistory> returnStatusHistoryList;

    @ApiObjectField(name = "return_status", description = "return status")
    @JsonProperty("return_status")
    private String returnStatus;

    @ApiObjectField(name = "return_type", description = "return type")
    @JsonProperty("return_type")
    private String returnType;

    @ApiObjectField(name = "is_return_cancellable", description = "is return cancellable")
    @JsonProperty("is_return_cancellable")
    private boolean isReturnCancellable;

    @ApiObjectField(name = "refund_total_type_wise", description = "refunded amount total by type")
    @JsonProperty("refund_total_type_wise")
    private List<OrderRefundDetails> refundTotalTypeWise;

    @JsonProperty("is_insurance_applied")
    @ApiObjectField(name = "is_insurance_applied", description = "True if insurance is applied or false ")
    private boolean isInsuranceApplied;

    @JsonProperty("refund_method_request")
    @ApiObjectField(name = "refund_method_request", description = "refund method intent of customer")
    private String refundMethodRequest;

    @JsonProperty("exchange_dispatch_point")
    @ApiObjectField(name = "exchange_dispatch_point", description = "dispatch point of exchange order created against return")
    private String exchangeDispatchPoint;

    @JsonProperty("refund_dispatch_point")
    @ApiObjectField(name = "refund_dispatch_point", description = "refund dispatch point against a return")
    private String refundDispatchPoint;

    @JsonProperty("return_time_line")
    @ApiObjectField(name = "return_time_line", description = "return timeslines")
    private List<ReturnTimeLine> returnTimeLines;

    @JsonProperty("refund_time_line")
    @ApiObjectField(name = "refund_time_line", description = "refund timelines")
    private List<RefundTimeLine> refundTimeLines;

    @JsonProperty("amount_to_refund")
    @ApiObjectField(name = "amount_to_refund", description = "amount_to_refund")
    private Double amountToRefund;

    @JsonProperty("exchange_time_line")
    @ApiObjectField(name = "exchange_time_line", description = "exchange_time_line")
    private List<ExchangeTimeLine> exchangeTimelines;

    @ApiObjectField(name = "cancellation_time_line",description = "cancellation_time_line")
    @JsonProperty("cancellation_time_line")
    private List<CancellationTimeLine> cancellationTimeLines;

    @JsonProperty("return_status_heading")
    @ApiObjectField(name = "return_status_heading", description = "return_status_heading")
    private ReturnStatusHeadingDetail returnStatusHeading;

    @JsonProperty("pickup_attempt_history")
    @ApiObjectField(name = "pickup_attempt_history", description = "pickup_attempt_history")
    private List<PickupAttemptHistory> pickupAttemptHistory;

    @JsonProperty("initiation_date")
    @ApiObjectField(name = "initiation_date", description = "initiation_date")
    private Date initiationDate;

    @JsonProperty("courier_name")
    @ApiObjectField(name = "courier_name", description = "courier_name")
    private String courierName;

    @JsonProperty("tracking_number")
    @ApiObjectField(name = "tracking_number", description = "tracking_number")
    private String trackingNumber;

    @JsonProperty("pickup_date")
    @ApiObjectField(name = "pickup_date", description = "pickup_date")
    private Date pickupDate;

    @JsonProperty("pickup_status")
    @ApiObjectField(name = "pickup_status", description = "pickup_status")
    private String pickupStatus;

    @JsonProperty("received_at_warehouse")
    @ApiObjectField(name = "received_at_warehouse", description = "received_at_warehouse")
    private String receivedAtWarehouse;

    @JsonProperty("refund_amount")
    @ApiObjectField(name = "refund_amount", description = "refund_amount")
    private Double refundAmount;

    @JsonProperty("refund_arn")
    @ApiObjectField(name = "refund_arn", description = "refund_arn")
    private String refundArn;


    @JsonProperty("refund_status")
    @ApiObjectField(name = "refund_status", description = "refund_status")
    private String refundStatus;

    @JsonProperty("refund_source")
    @ApiObjectField(name = "refund_source", description = "refund_source")
    private String refundSource;

    @JsonProperty("exchange_status")
    @ApiObjectField(name = "exchange_status", description = "exchange_status")
    private String exchangeStatus;

    @JsonIgnore
    private OrdersHeaderDTO ordersHeaderDTO;
    @JsonIgnore
    private List<OrderAddressUpdateDTO> orderAddressUpdates;
    @JsonIgnore
    private ExchangeOrdersDTO exchangeOrdersDTO;
    @JsonIgnore
    private UwOrderDTO exchangeUwOrderDTO;
    @ApiObjectField(name = "cancellation_status_history", description = "cancellation_status_history")
    @JsonProperty("cancellation_status_history")
    private List<CancellationStatusHistory> cancellationStatusHistories;

    @JsonProperty("refund_intent_created_at")
    @ApiObjectField(name = "refund_intent_created_at", description = "intended refund method by customer captured date")
    private Date refundIntentCreatedAt;
}
