package com.lenskart.returncommon.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ReturnOrderDTO {
    private Integer id;
    private Integer requestId;
    private Integer incrementId;
    private String status;
    private String unicomOrderCode;
    private Integer groupId;
    private Date returnCreateDatetime;
    private String returnType;
    private String returnMethod;
    private Boolean isInsurance;
    private Integer isQcAtDoorstep;
    private Boolean isAutoCancelEnable;
    private String receivingFlag;
    private String source;
    private String facilityCode;
    private String bulkType;
    private Integer reversePuFollowupCnt;
    private Date lastFollowupDatetime;
    private String agentEmail = "Unassigned";
    private String reverseCourier;
    private String overrideComment;
    private Date lastUpdateDatetime;
    private String reversePickupReferenceId;
    private String reverseAwb;

}
