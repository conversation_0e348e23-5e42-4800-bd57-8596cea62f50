package com.lenskart.returncommon.model.enums;

public enum PayloadName {
    MASTER("master"),FRAME_BROKEN_APPROVAL("frame_broken_approval");

    private final String name;

    PayloadName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static PayloadName getPayloadName(String name) {
        for (PayloadName payloadName : PayloadName.values()) {
            if (payloadName.getName().equals(name)) {
                return payloadName;
            }
        }
        throw new IllegalArgumentException(String.format("No enum constant with name %s found", name));
    }
}
