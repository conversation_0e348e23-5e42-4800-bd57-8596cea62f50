package com.lenskart.returncommon.model.response;

import lombok.Data;

import java.util.Date;

@Data
public class D365ReturnTrackingDTO {
    private Integer id;
    private Integer returnId;
    private Integer incrementId;
    private String oldStatus;
    private String newStatus;
    private String source;
    private Date createdAt;
    private Date updatedAt;
    private Date pslipRetryUpdatedAt;
    private Date returnRetryUpdatedAt;
    private Integer d365Flag;
    private Integer retryCount;
    private Integer pslipCreated;
    private Integer pSlipRetryCount;
    private Integer tjournalCreated;
    private String returnSyncMessage;
    private String pslipSyncMessage;
    private Integer movJournalFlag;
}
