package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ProductDto {
    private int productId;
    private int classification;
    private String groupProductIds;
    private String value;
    private int qty;
    private int isInStock;
    private String sku;
    private String productImage;
    private String productImageAlt;
    private int enable;
    private int sellingPrice;
    private int costPrice;
    private int costAverage;
    private int price;
    private String size;
    private int startingSoh;
    private int currentSoh;
    private int qsi;
    private int wfi;
    private int stockOut;
    private int stockMissingSupplier;
    private int stockOutSupplier;
    private int stockInSupplier;
    private int stockFoundSupplier;
    private int stockDefectiveSupplier;
    private int stockReturnSupplier;
    private int webStockOut;
    private int stockIn;
    private int stockDefective;
    private int stockMissing;
    private int stockFound;
    private int stockReturn;
    private int returnVendor;
    private Date inventoryStartDate;
    private Date createdAt;
    private int closedStockOut;
    private int processingNotStockout;
    private int nqsi;
    private int repeatRate;
    private int repeatMailerRate;
    private String catIds;
    private String brand;
    private int stockOffline;
    private String productUrl;
    private String frameType;
    private int msl;
    private int isPowerFollowup;
    private String unicomSynStatus;
    private int unicommerceInventory;
    private int blockedUnicomInventory;
    private int completeClosedCount;
    private List<String> productImages;
    private String hsnCode;
    private String hsnCodeWithoutPower;
    private String hsnClassification;
    private String transportationFlag;
    private String frameMaterial;
    private int alwaysGoForTracing;
    private String frameBaseCureveId;
    private String meiFrameType;
}
