package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class RefundTimeLine {

    @JsonProperty("refund_status")
    @ApiObjectField(name = "refund_status", description = "refund refund_status")
    private String refundStatus;

    @JsonProperty("refund_sub_status")
    @ApiObjectField(name = "refund_estimated_date", description = "refund refund_estimated_date")
    private String refundSubStatus;

    @JsonProperty("refund_estimated_date")
    @ApiObjectField(name = "refund_estimated_date", description = "refund refund_estimated_date")
    private Long refundEstimatedDate;
}
