package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ReturnStatusResponse {
    @JsonProperty(value = "return_id")
    private Integer returnId;
    @JsonProperty(value = "status")
    private String status;
    @JsonProperty(value = "return_create_datetime")
    private Date returnCreationDate;
    @JsonProperty(value = "is_return_active")
    private boolean isReturnActive = false;


}
