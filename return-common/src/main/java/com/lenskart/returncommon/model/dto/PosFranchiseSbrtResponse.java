package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PosFranchiseSbrtResponse {
    @JsonProperty("facilityCode")
    private String facilityCode;

    @JsonProperty("franchiseId")
    private int franchiseId;

    @JsonProperty("franchiseStatus")
    private String franchiseStatus;

    @JsonProperty("isSbrtStore")
    private boolean isSbrtStore;

    @JsonProperty("sbrtEnabledDate")
    private String sbrtEnabledDate;
}