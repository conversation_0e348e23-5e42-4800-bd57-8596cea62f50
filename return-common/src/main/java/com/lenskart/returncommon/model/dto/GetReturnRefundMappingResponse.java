package com.lenskart.returncommon.model.dto;

import com.lenskart.refund.client.model.dto.Refund;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
public class GetReturnRefundMappingResponse {
    @ApiObjectField(name = "return_refund_mapping", description = "It contains return refund mapping")
    private List<ReturnRefundMapping> returnRefundMapping;

    @ApiObjectField(name = "additional_refunds", description = "It contains fast refund")
    private List<Refund> additionalRefunds;

    @ApiObjectField(name = "refunds", description = "It contains refunds")
    private List<Refund> refunds;

    @ApiObjectField(name = "total_refund", description = "Total refund of the order")
    private Double total_refund;
}
