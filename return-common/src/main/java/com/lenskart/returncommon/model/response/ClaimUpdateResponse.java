package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ClaimUpdateResponse {
    @JsonProperty("status")
    private Integer status;
    @JsonProperty("claimStatus")
    private String claimStatus;
    @JsonProperty("success")
    private Boolean success;
    @JsonProperty("error")
    private String error;
    @JsonProperty("errors")
    private List<Error> errors = null;
    @JsonProperty("message")
    private String message;
    @JsonProperty("operation")
    private String operation;
    @JsonProperty("timestamp")
    private String timestamp;
}
