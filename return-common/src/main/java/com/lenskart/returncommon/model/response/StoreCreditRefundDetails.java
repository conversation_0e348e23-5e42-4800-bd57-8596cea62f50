package com.lenskart.returncommon.model.response;

import com.lenskart.refund.client.model.response.StoreCreditResponse;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class StoreCreditRefundDetails {
    double refundedScAmount;
    boolean rtoFlag;
    boolean returnFlag;
    boolean partialCancellationFlag;
    boolean fastRefundFlag;
    Integer orderId;
    Long refundId;
    StoreCreditResponse storeCreditResponse;
}
