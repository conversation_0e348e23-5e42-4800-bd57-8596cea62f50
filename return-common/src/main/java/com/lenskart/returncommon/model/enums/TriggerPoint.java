package com.lenskart.returncommon.model.enums;

public enum TriggerPoint {

    WHReceiving("WHReceiving",3),
    POSReceiving("POSReceiving",2),
    ReturnInitiation("ReturnInitiation",0),
    CourierPickup("CourierPickup",1);

    private final String triggerPoint;
    private final int priority;
    TriggerPoint (String triggerPoint, int priority){
        this.triggerPoint = triggerPoint;
        this.priority = priority;
    }

    public static boolean contains(String triggerPointStr){
        return TriggerPoint.getTriggerPointEnum(triggerPointStr)!=null;
    }

    public static TriggerPoint getTriggerPointEnum(String triggerPointstr){
        TriggerPoint triggerPointEnum = null;
        for (TriggerPoint candidate : TriggerPoint.values()) {
            if(candidate.getName().equalsIgnoreCase(triggerPointstr)){
                triggerPointEnum = candidate;
            }
        }
        return triggerPointEnum;
    }

    public String getName() {
        return this.triggerPoint;
    }
    public int getPriority() { return this.priority;}

    @Override
    public String toString() {
        return this.getName();
    }
}
