package com.lenskart.returncommon.model.request;

import com.lenskart.core.model.UwOrder;
import com.lenskart.orderops.model.ReturnOrder;
import com.lenskart.orderops.model.ReturnOrderItem;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class D365ReturnRequest {
    private Integer uwItemId;
    private ReturnOrder returnOrder;
    private Integer returnId;
    private UwOrder uwOrder;
    private ReturnOrderItem returnOrderItem;
    Integer rowId;
    private boolean retryFlag;
}
