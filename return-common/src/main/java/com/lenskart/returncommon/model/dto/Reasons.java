package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class Reasons {
    @JsonProperty("type")
    private String type;

    @JsonProperty("primary_reason_id")
    private Integer primaryReasonId;

    @JsonProperty("secondary_reason_id")
    private Integer secondaryReasonId;

    @JsonProperty("additional_comments")
    private String additionalComments;
}
