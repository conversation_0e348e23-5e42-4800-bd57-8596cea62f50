package com.lenskart.returncommon.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ExchangeAddress implements Serializable{

	private String firstName;

	private String lastName;

	private String phone;

	private String phoneCode;

	private String email;
	
	private String addressType;

	private String addressline1;

	private String city;
	
	private String landmark;

	private String state;

	private String postcode;

	private String country;

	private String locality;

	private String gender;
}
