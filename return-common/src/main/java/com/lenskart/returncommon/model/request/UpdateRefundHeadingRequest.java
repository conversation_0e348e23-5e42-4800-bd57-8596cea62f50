package com.lenskart.returncommon.model.request;

import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.RefundDetails;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UpdateRefundHeadingRequest {
    private List<OrderAddressUpdateDTO> orderAddressUpdateDTOs;
    private OrdersHeaderDTO ordersHeaderDTO;
    private String refundMethodRequest;
    private String source;
    private String status;
    private Date cancelledCreatedAt;
    private Integer cancelledIncrementId;
    private List<RefundDetails> refundDetails;
}
