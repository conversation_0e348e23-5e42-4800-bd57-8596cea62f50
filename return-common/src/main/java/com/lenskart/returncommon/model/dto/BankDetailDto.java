package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObject;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
@ApiObject(name = "bank_detail")
@JsonIgnoreProperties(ignoreUnknown = true)
public class BankDetailDto {
    @ApiObjectField(name = "bank_name", description = "Bank name ")
    @JsonProperty("bank_name")
    private String bankName;
    @JsonProperty("ifsc_code")
    @ApiObjectField(name = "ifsc_code", description = "IFSC Code of bank")
    private String ifscCode;
    @ApiObjectField(name = "account_number", description = "Account number of customer")
    @JsonProperty("account_number")
    private String accountNumber;
    @ApiObjectField(name = "beneficiary_name", description = "Beneficiary Name of the account holder")
    @JsonProperty("beneficiary_name")
    private String beneficiaryName;
    @ApiObjectField(name = "branch_name", description = "Branch name ")
    @JsonProperty("branch_name")
    private String branchName;
}
