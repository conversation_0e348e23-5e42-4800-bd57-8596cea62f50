package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

@ToString
@Data
public class LensDetails {

    @JsonProperty("axis")
    @ApiObjectField(name = "axis")
    private String axis;

    @JsonProperty("cylindrical")
    @ApiObjectField(name = "cylindrical")
    private String cylindrical;

    @JsonProperty("spherical")
    @ApiObjectField(name = "spherical")
    private String spherical;
}
