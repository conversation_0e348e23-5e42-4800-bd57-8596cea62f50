package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.enums.UserActionEnum;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class NonPickupCustomerFeedback {
    @JsonProperty("action")
    UserActionEnum action;
    @JsonProperty("returnId")
    Integer returnId;
    @JsonProperty("uniqueId")
    String uniqueId;
    @JsonProperty("source")
    String source;
}
