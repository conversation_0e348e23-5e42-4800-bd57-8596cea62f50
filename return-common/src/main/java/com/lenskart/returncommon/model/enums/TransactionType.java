package com.lenskart.returncommon.model.enums;

public enum TransactionType {
    REFUND_ON_CANCELLATION("refund_on_cancellation"),
    REFUND_ON_RECEIVING("refund_on_receiving"),
    REFUND_ON_RETURN_INITIATE("refund_on_return_initiate"),
    FAST_REFUND("fast_refund");

    String transactionType;

    TransactionType(String transactionType) {
        this.transactionType=transactionType;

    }

    public static TransactionType getTransactionType(String transactionType){
        for ( TransactionType transaction : TransactionType.values()
        ) {
            if(transaction.transactionType.equals(transactionType))
                return transaction;
        }
        return null;
    }

    public String getValue(){
        return this.transactionType;
    }
}
