package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.Reasons;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class ItemRequest {
    @JsonProperty("magento_item_id")
    private Integer magentoItemId;
    @JsonProperty("qc_status")
    private String qcStatus;
    @JsonProperty("reasons")
    private List<Reasons> reasonsList;
}
