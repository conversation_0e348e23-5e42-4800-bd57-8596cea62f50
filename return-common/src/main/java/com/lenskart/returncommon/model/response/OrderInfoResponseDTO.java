package com.lenskart.returncommon.model.response;


import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderInfoResponseDTO {
    private OrdersHeaderDTO ordersHeader;
    private List<UwOrderDTO> uwOrders;
    private List<OrdersDTO> orders;
    private List<ItemWisePriceDetailsDTO> itemWisePrices;
    private List<OrderAddressUpdateDTO> orderAddressUpdates;
    private Set<String> masterExchangeOrderPaymentMethods;
    private ExchangeOrdersDTO exchangeOrdersDTO;
    private FraudCustomerDTO fraudCustomerDTO;
    private CustomerAccountInfoDTO customerAccountInfoDTO;
    private List<UwOrderAttributeDTO> uwOrderAttributes;
    private ItemWiseFastRefunResponseDTO itemWiseFastRefunResponseDTO;
    private OrderExchangeCancellationDetails orderExchangeCancellationDetails;
    private PaymentDTO paymentDTO;
    private Map<Integer, ExchangePricingDTO> exchangePricingDTOMap;
}
