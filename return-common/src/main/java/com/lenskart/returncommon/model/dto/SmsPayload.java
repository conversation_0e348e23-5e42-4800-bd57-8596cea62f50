package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@Data
@Builder
@ToString
public class SmsPayload {
    @Builder.Default
    private String desc = "important";
    private String mobile;
    @Builder.Default
    private String mask = "LENSKT";
    private Map<String, Object> params;
    @Builder.Default
    @JsonProperty("communication_type")
    private String communicationType = "Transactional";
    private boolean rulesToBeApplied;
    @JsonProperty("order_id")
    private String orderId;
}
