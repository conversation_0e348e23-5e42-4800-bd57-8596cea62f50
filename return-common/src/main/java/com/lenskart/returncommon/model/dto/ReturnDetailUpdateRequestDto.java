package com.lenskart.returncommon.model.dto;

import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class ReturnDetailUpdateRequestDto {
    private int returnId;
    private @NotBlank String status;
    private String comments;
    private String username;
    private OrderInfoResponseDTO orderInfoResponseDTO;
    ExchangeOrdersDTO exchangeOrdersDTO;
    private UwOrderDTO uwOrderDTO;

    @Override
    public String toString() {
        return "ReturnDetailUpdateRequestDto{" +
                "returnId=" + returnId +
                ", status='" + status + '\'' +
                ", comments='" + comments + '\'' +
                ", username='" + username + '\'' +
                '}';
    }
}
