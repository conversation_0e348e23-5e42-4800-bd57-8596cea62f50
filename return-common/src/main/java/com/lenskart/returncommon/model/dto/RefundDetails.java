package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Date;
import java.util.List;

@Data
public class RefundDetails {
    @ApiObjectField(name = "refundId", description = "refundId")
    @JsonProperty("refundId")
    private String refundId;

    @ApiObjectField(name = "refund_method", description = "refund method")
    @JsonProperty("refund_method")
    private String refundMethod;

    @ApiObjectField(name = "refund_method_label", description = "refund_method_label")
    @JsonProperty("refund_method_label")
    private String refundMethodLabel;

    @ApiObjectField(name = "refund_method_sub_label",description = "refund_method_sub_label")
    @JsonProperty(value = "refund_method_sub_label")
    private String refundMethodSubLabel;

    @ApiObjectField(name = "refund_amount", description = "refund amount")
    @JsonProperty("refund_amount")
    private double refundAmount;

    @ApiObjectField(name = "lk_cash_refunded", description = "LK cash refunded amount")
    @JsonProperty("lk_cash_refunded")
    private Double lkCashRefunded;

    @ApiObjectField(name = "lk_cash_plus_refunded", description = "LK cash plus refunded amount")
    @JsonProperty("lk_cash_plus_refunded")
    private Double lkCashPlusRefunded;

    @ApiObjectField(name = "date", description = "date")
    @JsonProperty("date")
    private Date date;

    @ApiObjectField(name = "status", description = "status")
    @JsonProperty("status")
    private String status;

    @ApiObjectField(name = "store_credit_code", description = "store_credit_code")
    @JsonProperty("store_credit_code")
    private String storeCreditCode;
    @ApiObjectField(name = "cash_free_link", description = "cash_free_linkr")
    @JsonProperty("cash_free_link")
    private String cashFreeLink;

    @ApiObjectField(name = "bank_reference_number", description = "bank_reference_number")
    @JsonProperty("bank_reference_number")
    private String bankReferenceNumber;

    @ApiObjectField(name = "lk_cash_reference_number", description = "lk_cash_reference_number")
    @JsonProperty("lk_cash_reference_number")
    private String lkCashReferenceNumber;

    @ApiObjectField(name = "lk_cash_plus_reference_number", description = "lk_cash_plus_reference_number")
    @JsonProperty("lk_cash_plus_reference_number")
    private String lkCashPlusReferenceNumber;

    @ApiObjectField(name = "refund_status_history", description = "return status history")
    @JsonProperty("refund_status_history")
    private List<RefundStatusHistory> refundStatusHistoryList; //todo : relevent chagez for refund history
}
