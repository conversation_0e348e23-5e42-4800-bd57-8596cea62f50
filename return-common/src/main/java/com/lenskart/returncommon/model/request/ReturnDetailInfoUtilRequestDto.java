package com.lenskart.returncommon.model.request;

import com.lenskart.ordermetadata.dto.response.OrderExchangeCancellationDetails;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import lombok.Data;

@Data
public class ReturnDetailInfoUtilRequestDto {
    Long magentoItemId;
    OrderInfoResponseDTO orderInfoResponseDTO;
    OrderExchangeCancellationDetails orderExchangeCancellationDetails;
    PurchaseOrderDetailsDTO purchaseOrderDetailsDTO;
}
