package com.lenskart.returncommon.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class CustomOptionsResponseDTO {
    private Integer itemId;
    private Integer orderId;
    private Integer productId;
    private Integer shellId;
    private String rightLens;
    private String axis;
    private String lensHeight;
    private String lensWidth;
    private String cyl;
    private String ap;
    private String relatedProduct;
    private String webPackage;
    private double packagePrice;
    private String pd;
    private String sph;
    private String lensPackage;
    private Date patientDOB;
    private String patientName;
    private String patientDRName;
    private String gender;
    private String patientComments;
    private String bottomDistance;
    private String edgeDistance;
    private String effectiveDia;
    private String nearPD;
    private String topDistance;
    private Date updatedAt;
    private String customerAxis;
    private String customerCYL;
    private String customerSPH;
    private String lensPackageType;
    private String coatingOid;
    private String coatingName;
    private String lensName;
    private Double lensIndex;
    private String lensBrand;
    private String coating;
}