package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.enums.ActionType;
import com.lenskart.returncommon.model.enums.RuleConditionType;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.util.Map;

@Data
@ToString
@Builder
public class ActionRuleContextDTO {
    private Map<ActionType, Boolean> conditions;

    public ActionRuleContextDTO(){}
    public ActionRuleContextDTO(Map<ActionType, Boolean> conditions) {
        this.conditions = conditions;
    }

    public Boolean getCondition(ActionType conditionType) {
        if (!CollectionUtils.isEmpty(conditions)) {
            return conditions.getOrDefault(conditionType, null);
        }
        return null;
    }
}
