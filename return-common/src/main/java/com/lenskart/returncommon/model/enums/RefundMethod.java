package com.lenskart.returncommon.model.enums;

public enum RefundMethod {
    STORECREDIT("storecredit"),
    NEFT("neft"),
    SOURCE("source"),
    EXCHANGE("exchange"),
    CUSTOMER_WALLET("customer_wallet"),
    JUSPAY("juspay"),
    CASHFREE("cashfree"),
    PAYU("payu"),
    LKCASH("lkcash"),
    LKCASHPLUS("lkcashplus"),
    OFFLINE("offline"),
    ONLINE("online"),
    PHONEPE("phonepe"),
    PAYTM("paytm"),
    EZETAPCARD("ezetapcard"),
    OTHERS("others"),
    FRANCHISE("franchise");

    private String refundMethod;

    private RefundMethod(String refundMethod) {
        this.refundMethod = refundMethod;
    }

    public String getName() {
        return this.refundMethod;
    }

    public static RefundMethod getName(String refundMethodStr) {
        RefundMethod refundMethodEnum = null;
        RefundMethod[] var2 = values();
        int var3 = var2.length;

        for (int var4 = 0; var4 < var3; ++var4) {
            RefundMethod candidate = var2[var4];
            if (candidate.getName().equalsIgnoreCase(refundMethodStr)) {
                refundMethodEnum = candidate;
            }
        }

        return refundMethodEnum;
    }
}
