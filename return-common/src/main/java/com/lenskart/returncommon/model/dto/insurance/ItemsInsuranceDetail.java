package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

@ToString
@Data
public class ItemsInsuranceDetail {
    @JsonProperty("magento_item_id")
    @ApiObjectField(name = "magento_item_id")
    private Integer magentoItemId;

    @JsonProperty("is_insurance_eligible")
    @ApiObjectField(name = "is_insurance_eligible")
    private Boolean isInsuranceEligible;

    @JsonProperty("sum_insured")
    @ApiObjectField(name = "sum_insured")
    private Double sumInsured;

    @JsonProperty("maximum_claimable_amount")
    @ApiObjectField(name = "maximum_claimable_amount")
    private Double maximumClaimableAmount;

    @JsonProperty("copay_amount")
    @ApiObjectField(name = "copay_amount")
    private Double copayAmount;


}
