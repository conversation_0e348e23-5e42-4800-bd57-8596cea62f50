package com.lenskart.returncommon.model.dto;

import com.lenskart.ordermetadata.dto.ReturnOrderItemDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AwaitedRtoRefundRequestDTO {
    int returnId;
    UwOrderDTO uwOrder;
    ReturnOrderItemDTO returnOrderItem;
    int customerReturnId;
    int customerUwItemId;
}
