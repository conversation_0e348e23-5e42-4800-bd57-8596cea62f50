package com.lenskart.returncommon.model.request;


import com.lenskart.platform.fl.utils.dto.returnPayloads.salesOrder.SalesOrderHeader;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PackageSlipCreationRequest {

    private ReturnCreateRequest returnCreateRequest;
    private SalesOrderHeader salesOrderHeader;
    
}
