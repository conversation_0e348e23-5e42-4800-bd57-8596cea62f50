package com.lenskart.returncommon.model.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class UpdateReturnReasonRequest {
    private Integer returnItemId;
    private String returnReason;
    @NotNull
    private String primaryReason;
    @NotNull
    private String secondaryReason;
    @NotNull
    private Integer uwItemId;
    @NotNull
    private Integer orderId;
    @NotNull
    private String source;
    @NotNull
    private String user;
}
