package com.lenskart.returncommon.model.response;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreditNoteResponse {
    
    private CreditNoteResponseData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreditNoteResponseData {
        private Integer totalCount;
        private List<DocumentContent> content;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentContent {
        private Long id;
        private String createdBy;
        private String createdAt;
        private String updatedBy;
        private String updatedAt;
        private String documentNo;
        private String documentDate;
        private String documentType;
        private String documentSource;
        private String documentSourceRefId;
        private String documentProvider;
        private String documentLink;
        private String sourceCode;
        private String sourceGstin;
        private String destinationCode;
        private String destinationGstin;
        private String currency;
        private String sourceCountry;
        private String destinationCountry;
    }
}

