package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;
import org.jsondoc.core.annotation.ApiObjectField;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApprovalStatusRequest {
    private static final Gson gson = new Gson();
    @ApiObjectField(name="magentoItemId", description = "Id")
    @JsonProperty("magentoItemId")
    private long magentoItemId;

    @ApiObjectField(name="returnStatus", description = "Id")
    @JsonProperty("returnStatus")
    private String returnStatus;

    @ApiObjectField(name="selectedRefundAction", description = "Id")
    @JsonProperty("selectedRefundAction")
    private String selectedRefundAction;

    @ApiObjectField(name="retryCount", description = "retryCount")
    @JsonProperty("retryCount")
    private int retryCount;
    @Override
    public String toString() {
        return gson.toJson(this);
    }
}

