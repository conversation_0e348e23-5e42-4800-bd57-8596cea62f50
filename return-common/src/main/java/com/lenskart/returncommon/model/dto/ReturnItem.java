package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObject;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

//@JsonIgnoreProperties(ignoreUnknown = true)
@ApiObject
@Data
public class ReturnItem {

    @JsonProperty("magento_item_id")
    @ApiObjectField(name = "magento_item_id", description = "This is magento item id", required=true)
    public Long magentoId;

    //@JsonIgnore
    @JsonProperty("uw_item_id")
    @ApiObjectField(name = "uw_item_id", description = "This is unitwise item id", required=true)
    public Integer uwItemId;

    @JsonProperty("qc_status")
    @ApiObjectField(name = "qc_status" , description = "This is qc_status", required=true)
    public String qcStatus="Pass";

    @ApiObjectField(name = "need_approval", description = "To create a return id only for the approval from Delight team regarding the same", required=true)
    @JsonProperty("need_approval")
    private Boolean needApproval;

    @JsonProperty("reasons")
    @ApiObjectField(name = "reasons", required=true)
    public List<Reasons> reasons;

    @ApiObjectField(name = "do_refund", description = "Flag asserting refundabiliity of return", required=true)
    @JsonProperty("do_refund")
    private Boolean doRefund;

    //@JsonIgnore
    @ApiObjectField(name = "refund_method", description = "Refund method for return", required=true)
    @JsonProperty("refund_method")
    private String refundMethod;

    @ApiObjectField(name = "claim_insurance", description = "If true then raise claim", required=true)
    @JsonProperty("claim_insurance")
    private Boolean claimInsurance;

    @ApiObjectField(name = "refund_method_request", description = "refund_method_request ", required=false)
    @JsonProperty("refund_method_request")
    private String refundMethodRequest;

}
