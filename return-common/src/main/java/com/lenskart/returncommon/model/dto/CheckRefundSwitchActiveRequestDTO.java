package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import lombok.Data;

@Data
public class CheckRefundSwitchActiveRequestDTO {
    private CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse;
    private Integer incrementId;
    private Integer returnId;
    private Integer requestId;
    private boolean createNewReturn;
    private String returnType;
    private UwOrderDTO uwOrder;
    private ReturnOrderRequestDTO returnRequest;
}
