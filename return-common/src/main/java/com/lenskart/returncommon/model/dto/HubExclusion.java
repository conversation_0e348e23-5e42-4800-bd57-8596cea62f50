package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Set;

@ToString
@Data
public class HubExclusion {

    @JsonProperty("parent_pids")
    @ApiObjectField(name = "parent_pids")
    private Set<String> parentPids;

    @JsonProperty("frame_types")
    @ApiObjectField(name = "frame_types")
    private Set<String> frameTypes;
}
