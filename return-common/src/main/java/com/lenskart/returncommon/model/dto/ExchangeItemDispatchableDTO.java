package com.lenskart.returncommon.model.dto;

import com.lenskart.ordermetadata.dto.ExchangeItemDTO;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import lombok.Data;

import java.util.List;

@Data
public class ExchangeItemDispatchableDTO {
    private ExchangeItemDTO exchangeItemDTO;
    private ExchangeOrdersDTO exchangeOrdersDTO;
    private List<UwOrderDTO> uwOrderDTOs;
    private OrdersHeaderDTO ordersHeaderDTO;
}
