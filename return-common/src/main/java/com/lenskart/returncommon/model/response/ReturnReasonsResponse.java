package com.lenskart.returncommon.model.response;

import com.lenskart.returncommon.model.dto.ReturnReasonDTO;
import lombok.Data;
import org.springframework.http.HttpStatus;

import java.util.List;

@Data
public class ReturnReasonsResponse {
    private String platform;
    private String category;
    public List<ReturnReasonDTO> return_reasons;
    private ReturnReasonErrorResponse error_response;
    private HttpStatus response_status;
}
