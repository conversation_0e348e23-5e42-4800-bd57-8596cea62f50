package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.kafka.common.protocol.types.Field;

@Data
public class ReturnTrackerOrderItemDto {
    @JsonProperty("reason_for_return")
    private String returnForReason;
    @JsonProperty("qc_status")
    private String qcStatus;
    @JsonProperty("qc_fail_reason")
    private String qcFailReason;
    @JsonProperty("qc_comment")
    private String qcComment;
    @JsonProperty("product_id")
    private Long productId;
    @JsonProperty("classification")
    private Long classification;
    @JsonProperty("sku")
    private String sku;
    @JsonProperty("product_url")
    private String productUrl;
    @JsonProperty("product_image")
    private String productImage;
    @JsonProperty("value")
    private String value;
    @JsonProperty("website")
    private String website;
}