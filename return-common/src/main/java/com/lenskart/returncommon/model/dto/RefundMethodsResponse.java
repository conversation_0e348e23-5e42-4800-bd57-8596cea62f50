package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
public class RefundMethodsResponse {

    @ApiObjectField(name = "magento_item_id")
    @JsonProperty("magento_item_id")
    public Integer magentoItemId;

    @ApiObjectField(name = "exchange")
    @JsonProperty("exchange")
    public Boolean isExchange;

    @ApiObjectField(name = "refund_methods")
    @JsonProperty("refund_methods")
    public List<String> refundMethods;

}
