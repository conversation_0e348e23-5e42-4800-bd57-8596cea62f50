package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NeedApprovalRequest {
    @JsonProperty("returnId")
    private int returnId;
    @JsonProperty("status")
    @NotBlank
    private String status;
    @JsonProperty("comments")
    private String comments;
    @JsonProperty("username")
    private String username;
    @JsonProperty("delightAction")
    private String delightAction;
    @JsonProperty("delightMethod")
    private String delightMethod;
    @JsonProperty("delightComment")
    private String delightComment;
    @JsonProperty("approvalStatusRequest")
    private ApprovalStatusRequest approvalStatusRequest;
    @JsonProperty("agentAutoApproval")
    private Boolean agentAutoApproval = false;
}
