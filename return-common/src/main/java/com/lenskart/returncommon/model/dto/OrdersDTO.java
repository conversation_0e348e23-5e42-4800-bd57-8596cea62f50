package com.lenskart.returncommon.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrdersDTO {
    private int itemId;
    private int orderId;
    private Integer parentId;
    private Integer incrementId;
    private BigDecimal grandTotal;
    private int productId;
    private Integer subProductId;
    private byte storeId;
    private String method;
    private int magentoItemId;
    private String channel;
    private String productDeliveryType;
    private String facilityCode;
    private Long customerId;
    private Boolean shipToStoreRequired = false;
    private String saleSource;

}
