package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lenskart.returncommon.model.enums.LKCountry;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

public class DualReturnReceived {
    private static Gson gson = new GsonBuilder().setPrettyPrinting().create();

    @ApiObjectField(name="return_id", description = "return_id")
    @JsonProperty("return_id")
    private Integer returnId;

    @ApiObjectField(name="increment_id", description = "incrementId")
    @JsonProperty("increment_id")
    private Integer incrementId;

    @ApiObjectField(name="category", description = "category")
    @JsonProperty("category")
    private String category;

    @ApiObjectField(name="refund_method", description = "refund_method")
    @JsonProperty("refund_method")
    private String refundMethod;

    @ApiObjectField(name="amount", description = "amount")
    @JsonProperty("amount")
    private String amount;

    @ApiObjectField(name="source", description = "source")
    @JsonProperty("source")
    private String source;

    @ApiObjectField(name="transactionType", description = "transactionType")
    @JsonProperty("transaction_type")
    private String transactionType;

    @ApiObjectField(name="additionalAmount", description = "additionalAmount")
    @JsonProperty("additional_amount")
    private String additionalAmount;

    @ApiObjectField(name="isCustomerRefundEligible", description = "isCustomerRefundEligible")
    @JsonProperty("is_customer_refund_eligible")
    private String isCustomerRefundEligible;

    @ApiObjectField(name="isWalletAdjustmentEligible", description = "isWalletAdjustmentEligible")
    @JsonProperty("is_wallet_adjustment_eligible")
    private String isWalletAdjustmentEligible;

    @ApiObjectField(name="item_ids", description = "item_ids")
    @JsonProperty("item_ids")
    private List<Integer> itemIdList;

    @ApiObjectField(name="isConversionScToNeft", description = "isConversionScToNeft")
    @JsonProperty("is_conversion_sc_to_neft")
    private boolean isConversionScToNeft;

    @ApiObjectField(name="isExceptionalRefund", description = "isExceptionalRefund")
    @JsonProperty("is_exceptional_refund")
    private boolean isExceptionalRefund;

    @ApiObjectField(name="isFofoOldOrder", description = "isFofoOldOrder")
    @JsonProperty("is_fofo_old_order")
    private boolean isFofoOldOrder;

    @ApiObjectField(name="skipItemWisePriceCalculation", description = "skipItemWisePriceCalculation")
    @JsonProperty("skip_item_wise_price_calculation")
    private boolean skipItemWisePriceCalculation;

    @ApiObjectField(name="transaction_subcategory", description = "transaction_subcategory")
    @JsonProperty("transaction_subcategory")
    private String transactionSubcategory;

    @JsonIgnore
    private LKCountry lkCountry;
    @JsonIgnore
    private String paymentMethod;

    @JsonIgnore
    @ApiObjectField(name="return_id", description = "return_id")
    @JsonProperty("refund_id")
    private Integer refundId;

    @JsonIgnore
    @ApiObjectField(name = "store_credit_code", description = "store_credit_code")
    @JsonProperty("storeCode")
    private String storeCode;
    @JsonIgnore
    private String payoutLink;

    @JsonIgnore
    private String additionalComment;

    @JsonIgnore
    private String gatewayName;
}
