package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderAddressUpdateDto {

    private Integer id;
    private Integer orderId;
    private String region;
    private String postcode;
    private String lastName;
    private String street;
    private String city;
    private String email;
    private String telephone;
    private String fax;
    private String firstName;
    private String addressType;
    private String countryId;

}
