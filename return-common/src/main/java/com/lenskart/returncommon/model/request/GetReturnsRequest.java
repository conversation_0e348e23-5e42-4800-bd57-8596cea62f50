package com.lenskart.returncommon.model.request;

import lombok.Data;
import lombok.ToString;

import java.util.Calendar;
import java.util.Date;

@Data
@ToString
public class GetReturnsRequest {
    private Integer orderId;
    private String returnType;
    private String returnStatus;
    private String qcStatus;
    private String orderType;
    private String agentEmail;
    private String followedUp;
    private String paymentMethod;
    private String optDate;
    private String reportFilterFld1;
    private String reportFilterFld2;
    private String reportFilterFld3;
    private String storeType;
    private String returnReasonsSecondary;
    private String returnReasonsPrimary;
    private String insuranceOrderType;
    private String category;
    private String country;
    private Date fromSearch;
    private Date toSearch;
    private int pageNumber = 0;
    private int pageSize = 20;

    public Date getFromSearch() {
        if (fromSearch == null) return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(fromSearch);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public Date getToSearch() {
        if (toSearch == null) return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(toSearch);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime();
    }
}
