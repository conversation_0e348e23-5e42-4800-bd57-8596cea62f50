package com.lenskart.returncommon.model.response;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.ReturnOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import lombok.Data;

import java.util.List;

@Data
public class ReversePickUpInfoUpdateResponse {

    private Boolean successful;
    private String message;
    private List<ReturnOrderDTO> returnOrderList;
    private List<ReturnCreateRequest> returnCreateRequestList;
    private Integer noOfItems;
    private String returnStatus;
    private OrdersDTO order;

}