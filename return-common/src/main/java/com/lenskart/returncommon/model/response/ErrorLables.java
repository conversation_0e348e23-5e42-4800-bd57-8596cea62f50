package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
@ToString
public class ErrorLables {

    @JsonProperty("error_for_not_refundable")
    @ApiObjectField(name = "error_for_not_refundable", description = "error for not refundable")
    private String errorForNotRefundable;
    @JsonProperty("error_for_not_exchangeable")
    @ApiObjectField(name = "error_for_not_exchangeable", description = "error for not exchangeable")
    private String errorForNotExchangeable;
    @JsonProperty("error_for_not_returnable")
    @ApiObjectField(name = "error_for_not_returnable", description = "error for not returnable")
    private String errorForNotReturnable;

}