package com.lenskart.returncommon.model.enums;

public enum QCType {
    QC_REQUIRED(1, "doorstep"),
    NON_QC(0, "non_qc"),
    QC_OVERRIDDEN(2, "qc_overridden");

    int value;
    String code;

    private QCType(int value, String code) {
        this.value = value;
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }

    public int getValue() {
        return this.value;
    }

    public static QCType getQcType(String code) {
        QCType[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            QCType values = var1[var3];
            if (values.getCode().equalsIgnoreCase(code)) {
                return values;
            }
        }

        throw new IllegalArgumentException("No enum constant with value: " + code);
    }
}

