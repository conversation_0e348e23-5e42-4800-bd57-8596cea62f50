package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BarcodePriceRequestDto {

    @JsonProperty("barcode_list")
    private List<String> barcodeList;

    @JsonProperty("facility_code")
    private String facilityCode;

}
