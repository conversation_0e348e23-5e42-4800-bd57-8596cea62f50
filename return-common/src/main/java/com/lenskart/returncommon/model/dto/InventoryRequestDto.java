package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InventoryRequestDto {
    
    @Builder.Default
    private String identifierType = "INCREMENT_ID";
    private String identifierValue;
    private Integer uwItemId;
    private Integer b2bRefrenceItemId;
    private Integer itemId;
    private Integer parentUwId;
    private Integer orderId;
    private String unicomOrderCode;
    private Integer incrementId;
    private Integer productId;
    private String shippingPackageId;
    private Integer classificationId;
}
