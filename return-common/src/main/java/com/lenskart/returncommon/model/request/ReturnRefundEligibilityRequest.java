package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class ReturnRefundEligibilityRequest {
    @NotNull
    @JsonProperty("order_id")
    private Integer orderId;
    @JsonProperty("check_cancellable")
    private boolean checkCancellable;
    @NotEmpty
    @JsonProperty("item_ids")
    private List<ItemRequest> itemList;
    @NotNull
    @JsonProperty("source")
    private String source;
    private OrderInfoResponseDTO orderInfoDTO;
    private List<ShippingStatusDetail> shippingStatusDetails;
    private Boolean isOrderCancellable;
}
