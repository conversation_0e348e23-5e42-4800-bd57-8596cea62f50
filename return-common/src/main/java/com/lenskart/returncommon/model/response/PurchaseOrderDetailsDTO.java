package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lenskart.ordermetadata.dto.*;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.ItemWisePriceDetailsDTO;
import lombok.Data;

import java.util.List;

@Data
public class PurchaseOrderDetailsDTO {
    private List<UwOrderDTO> uwOrders;
    private List<OrdersDTO> orders;
    private OrdersHeaderDTO ordersHeaderDTO;
    private List<OrderAddressUpdateDTO> orderAddressUpdateDTOs;
    private List<ItemWisePriceDetailsDTO> itemWisePrices;
    private ItemWiseAmountDTO itemWiseAmountDTO;
    private ShippingStatusDetail shippingStatusDetail;
    private List<ShippingStatusDetail> shippingStatusDetailList;
    private boolean getIsBlacklisted;
    private boolean isBranded;
    @JsonIgnore
    private String identifierType;
    @JsonIgnore
    private String identifierValue;
}
