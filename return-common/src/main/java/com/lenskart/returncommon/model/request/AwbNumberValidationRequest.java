package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AwbNumberValidationRequest {

    @NotBlank(message = "tracking number cannot be blank")
    @JsonProperty("tracking_number")
    String trackingNumber;


    @NotBlank(message = "return type cannot be blank")
    @JsonProperty("return_type")
    String returnType;

    @NotBlank(message = "courier name cannot be blank")
    @JsonProperty("courier_name")
    String courierName;

}
