package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.HubDetails;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@ToString
@Data
public class HubDetailsResponse {

    @JsonProperty("total_record_count")
    @ApiObjectField(name = "total_record_count")
    private Integer totalRecordCount;

    @JsonProperty("hub_details")
    @ApiObjectField(name = "hub_details")
    private List<HubDetails> hubDetails;

    @JsonProperty("result")
    @ApiObjectField(name = "result")
    private boolean result;

    @JsonProperty("error_message")
    @ApiObjectField(name = "error_message")
    private String errorMessage;

    @JsonProperty("status_code")
    @ApiObjectField(name = "status_code")
    private Integer statusCode;
}
