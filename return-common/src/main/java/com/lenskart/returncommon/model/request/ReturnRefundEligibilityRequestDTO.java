package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.ordermetadata.dto.request.ItemRequestDTO;
import com.lenskart.returncommon.model.dto.MVCResponseDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
public class ReturnRefundEligibilityRequestDTO {
    @JsonProperty("order_id")
    private @NotNull Integer orderId;
    @JsonProperty("check_cancellable")
    private boolean checkCancellable;
    @JsonProperty("item_ids")
    private @NotEmpty List<ItemRequestDTO> itemList;
    @JsonProperty("source")
    private @NotNull String source;
    @JsonProperty("order_info")
    private OrderInfoResponseDTO orderInfoDTO;
    @JsonProperty("shipping_status_details")
    private List<ShippingStatusDetail> shippingStatusDetails;
    @JsonProperty("is_order_cancellable")
    private Boolean isOrderCancellable;
    @JsonProperty("store_facility")
    @ApiObjectField(name = "store_facility", description = "This is store_id , provided by backend services.")
    private String storeId;
    @JsonIgnore
    private Integer customerScore;
    @JsonIgnore
    private Integer storeScore;
    @JsonProperty("mvc_response")
    @ApiObjectField(name = "mvc_response", description = "mvc response of customer")
    private MVCResponseDTO mvcResponseDTO;
}
