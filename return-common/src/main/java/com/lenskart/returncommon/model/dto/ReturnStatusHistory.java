package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Date;

@Data
public class ReturnStatusHistory {
    @ApiObjectField(name = "date", description = "date")
    @JsonProperty("date")
    private Date date;

    @ApiObjectField(name = "return_status", description = "return status")
    @JsonProperty("return_status")
    private String returnStatus;


    @ApiObjectField(name = "comment", description = "comment")
    @JsonProperty("comment")
    private String comment;
}