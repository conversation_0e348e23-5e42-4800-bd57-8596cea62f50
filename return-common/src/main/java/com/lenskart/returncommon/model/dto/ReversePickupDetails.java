package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class ReversePickupDetails {

    @ApiObjectField(name = "incrementId")
    @JsonProperty("incrementId")
    private Integer incrementId;

    @ApiObjectField(name = "reverseCreatedAt")
    @JsonProperty("reverseCreatedAt")
    private String reverseCreatedAt;

    @ApiObjectField(name = "courier")
    @JsonProperty("courier")
    private String courier;

    @ApiObjectField(name = "awbNumber")
    @JsonProperty("awbNumber")
    private String awbNumber;
}
