package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class D365ReturnTrackingRequestDTO {
    private Integer returnId;
    private String status;
    private Integer d365Flag;
    private Integer pslipCreated;
    private String returnSyncMessage;
    private String pslipSyncMessage;
    private Date updatedAt;
    private Date pslipRetryUpdatedAt;
    private Date returnRetryUpdatedAt;
    private String newStatus;
    private String oldStatus;
    private String source;

    public D365ReturnTrackingRequestDTO(Integer returnId, String status) {
        this.returnId = returnId;
        this.status = status;
        this.newStatus = status;
    }
}