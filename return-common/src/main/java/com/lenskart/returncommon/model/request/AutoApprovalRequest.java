package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class AutoApprovalRequest {
    @JsonProperty("returnCreationRequest")
    ReturnCreationRequestDTO returnCreationRequest;

    @JsonProperty("refundRequestResponse")
    ReturnCreationResponse refundRequestResponse;

    @JsonProperty("retryCount")
    private int retryCount;
}
