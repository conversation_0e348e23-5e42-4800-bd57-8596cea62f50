package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class ReturnItemRequestDTO {
    @JsonProperty("item_id")
    private Integer itemId;
    @JsonProperty("qc_status")
    private String qcStatus = "Pass";
    @JsonIgnore
    @JsonProperty("qc_fail_reason")
    private String qcFailReason;
    @JsonIgnore
    @JsonProperty("reason_detail")
    private String reasonDetail;
    @JsonProperty("uw_order")
    private UwOrderDTO uwOrderDTO;
    @JsonIgnore
    @JsonProperty("dispatchFacility")
    private String dispatchFacility;
    @JsonIgnore
    @JsonProperty("barcode")
    private String barcode;
    @JsonIgnore
    @JsonProperty("productId")
    private String productId;
}
