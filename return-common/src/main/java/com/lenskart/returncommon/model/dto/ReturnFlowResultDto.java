package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReturnFlowResultDto {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("returnable")
    private Boolean returnable;

    @JsonProperty("items")
    private List<ItemDto> itemDtos;

    @JsonProperty("isFraud")
    private Boolean isFraud;
}
