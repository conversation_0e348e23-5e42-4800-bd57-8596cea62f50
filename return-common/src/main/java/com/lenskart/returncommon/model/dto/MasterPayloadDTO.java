package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class MasterPayloadDTO extends BasePayloadDTO {

    @JsonProperty("my_account_return_url")
    private String my_account_return_url;

    @JsonProperty("pickup_courier")
    private String returnPickupCourierName;

    @JsonProperty("return_pickup_awb")
    private String returnPickupAwb;

    @JsonProperty("return_pickup_date")
    private String returnPickupDate;

    @JsonProperty("return_creation_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "IST")
    private Date returnCreationDate;

    @JsonProperty("brand")
    private String brand;

    @JsonProperty("category")
    private String category;

    @JsonProperty("handover_date")
    private String handoverDate;

    @JsonProperty("handover_time")
    private String handoverTime;

    @JsonProperty("no_of_items")
    private String noOfItems;

    @JsonProperty("refund_method_chosen")
    private String returnMethodChosen;

    @JsonProperty("pickup_edd")
    private String orderPickupEdd;

    @JsonProperty("country_id")
    private String countryId;

    @JsonProperty("exchange_order_placement_link")
    private String exchangeOrderPlacementLink;

    @JsonProperty("store_name")
    private String storeName;

    @JsonProperty("store_phone")
    private String storePhone;

    @JsonProperty("store_locator_link")
    private String storeLocatorLink;

    @JsonProperty("store_opening_time")
    private String storeOpeningTime;

    @JsonProperty("store_closing_time")
    private String storeClosingTime;

    @JsonProperty("company_name")
    private String companyName;

    @JsonProperty("customer_support_no")
    private String customerSupportNo;

    @JsonProperty("short_url_returns_detail_page")
    private String short_url_returns_detail_page;

    @JsonProperty("myaccount_order_tracking_shorturl")
    private String myaccount_order_tracking_shorturl;

    @JsonProperty("order_id")
    private Integer order_id;

    public MasterPayloadDTO(BasePayloadDTO basePayloadDTO) {
        super(basePayloadDTO);
    }
}
