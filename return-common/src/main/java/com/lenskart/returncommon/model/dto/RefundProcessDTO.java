package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundProcessDTO {
    private ReturnCreationResponse returnCreationResponse;
    private List<UwOrderDTO> uwOrderDTOList;
    private List<OrdersDTO> ordersDTOList;
    private OrdersHeaderDTO ordersHeaderDTO;
    private Map<Long, ReturnItemDTO> magentoItemToReturnItemMap;
    private Integer incrementId;
    private Integer returnId;
    private Integer requestId;
}
