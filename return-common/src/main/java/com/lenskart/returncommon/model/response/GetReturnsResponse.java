package com.lenskart.returncommon.model.response;

import com.lenskart.returncommon.model.dto.ReturnDetailsVSM;
import lombok.Data;

import java.util.List;

@Data
public class GetReturnsResponse {
    private List<ReturnDetailsVSM> returnDetails;

    public GetReturnsResponse() {
    }

    public GetReturnsResponse(List<ReturnDetailsVSM> returnDetails) {
        this.returnDetails = returnDetails;
    }
}
