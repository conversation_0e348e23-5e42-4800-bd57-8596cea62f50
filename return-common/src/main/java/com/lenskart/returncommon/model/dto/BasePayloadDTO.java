package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BasePayloadDTO implements BridgePayloadDTO {
    private String orderId;
    private Integer returnId;
    @JsonProperty("customer_name")
    private String customerName;
    @JsonProperty("email")
    private String customerEmail;
    @JsonProperty("mobile")
    private String customerMobile;
    private String desc = "important";
    private String mask = "LENSKT";
    @JsonProperty("communication_type")
    private String communicationType = "Transactional";
    private boolean rulesToBeApplied;

    public BasePayloadDTO(BasePayloadDTO basePayloadDTO) {
        this.setRulesToBeApplied(basePayloadDTO.isRulesToBeApplied());
        this.setOrderId(basePayloadDTO.getOrderId());
        this.setCustomerName(basePayloadDTO.getCustomerName());
        this.setCustomerMobile(basePayloadDTO.getCustomerMobile());
        this.setDesc(basePayloadDTO.getDesc());
        this.setMask(basePayloadDTO.getMask());
        this.setCommunicationType(basePayloadDTO.getCommunicationType());
    }
}
