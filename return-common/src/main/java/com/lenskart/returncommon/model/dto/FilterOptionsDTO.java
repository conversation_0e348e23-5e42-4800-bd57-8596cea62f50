package com.lenskart.returncommon.model.dto;

import lombok.ToString;

import java.util.List;

@ToString
public class FilterOptionsDTO {

    private List<FilterOption> returnType;
    private List<FilterOption> orderType;
    private List<FilterOption> returnStatus;
    private List<FilterOption> agentEmail;
    private List<FilterOption> returnReasonsPrimary;
    private List<FilterOption> returnReasonsSecondary;
    private List<FilterOption> storeType;
    private List<FilterOption> insuranceOrderType;
    private List<FilterOption> country;
    private List<FilterOption> paymentMethod;
    private List<FilterOption> qcStatus;
    private List<FilterOption> category;

    // Getters and Setters
    public List<FilterOption> getReturnType() {
        return returnType;
    }

    public void setReturnType(List<FilterOption> returnType) {
        this.returnType = returnType;
    }

    public List<FilterOption> getOrderType() {
        return orderType;
    }

    public void setOrderType(List<FilterOption> orderType) {
        this.orderType = orderType;
    }

    public List<FilterOption> getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(List<FilterOption> returnStatus) {
        this.returnStatus = returnStatus;
    }

    public List<FilterOption> getAgentEmail() {
        return agentEmail;
    }

    public void setAgentEmail(List<FilterOption> agentEmail) {
        this.agentEmail = agentEmail;
    }

    public List<FilterOption> getReturnReasonsPrimary() {
        return returnReasonsPrimary;
    }

    public void setReturnReasonsPrimary(List<FilterOption> returnReasonsPrimary) {
        this.returnReasonsPrimary = returnReasonsPrimary;
    }

    public List<FilterOption> getReturnReasonsSecondary() {
        return returnReasonsSecondary;
    }

    public void setReturnReasonsSecondary(List<FilterOption> returnReasonsSecondary) {
        this.returnReasonsSecondary = returnReasonsSecondary;
    }

    public List<FilterOption> getStoreType() {
        return storeType;
    }

    public void setStoreType(List<FilterOption> storeType) {
        this.storeType = storeType;
    }

    public List<FilterOption> getInsuranceOrderType() {
        return insuranceOrderType;
    }

    public void setInsuranceOrderType(List<FilterOption> insuranceOrderType) {
        this.insuranceOrderType = insuranceOrderType;
    }

    public List<FilterOption> getCountry() {
        return country;
    }

    public void setCountry(List<FilterOption> country) {
        this.country = country;
    }

    public List<FilterOption> getQcStatus() {
        return qcStatus;
    }

    public void setQcStatus(List<FilterOption> qcStatus) {
        this.qcStatus = qcStatus;
    }

    public List<FilterOption> getCategory() {
        return category;
    }

    public void setCategory(List<FilterOption> category) {
        this.category = category;
    }

    public List<FilterOption> getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(List<FilterOption> paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    @ToString
    // Inner Class for Filter Options
    public static class FilterOption {
        private String key;
        private String value;

        public FilterOption(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public FilterOption() {
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
