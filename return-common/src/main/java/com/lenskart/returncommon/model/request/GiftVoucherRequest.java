package com.lenskart.returncommon.model.request;

import lombok.Data;

@Data
public class GiftVoucherRequest {

    private Payload payload;

    public GiftVoucherRequest() {
    }

    public GiftVoucherRequest(Payload payload) {
        this.payload = payload;
    }

    public Payload getPayload() {
        return payload;
    }

    public void setPayload(Payload payload) {
        this.payload = payload;
    }

    @Data
    public static class Payload {
        private String code;
        private String balance;
        private String currency;
        private String status;
        private String customerPhone;
        private String phoneCode;
        private String expiredAt;
        private String countryCode;
        private Long ticketId;
        private String comments;
    }
}
