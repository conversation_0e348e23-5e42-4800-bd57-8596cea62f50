package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class ItemWiseGetAmount {
    @JsonProperty("uwItemId")
    @ApiObjectField(name = "uwItemId", description = "This is unitwise item id, provided by backend services.")
    private Integer uwItemId;
    @JsonProperty("actualAmount")
    @ApiObjectField(name = "actualAmount", description = "This is  actual amount of unitwise item id, provided by backend services.")
    private Double actualAmount;
    @JsonProperty("refundAmount")
    @ApiObjectField(name = "refundAmount", description = "This is  refunded amount of unitwise item id, provided by backend services.")
    private Double refundAmount;
}
