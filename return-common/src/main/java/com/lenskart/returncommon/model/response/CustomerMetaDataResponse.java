package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerMetaDataResponse {

    private CustomerCategoryResponse customerCategoryResponse;
    private CustomerStoreProfileResponse customerStoreProfileResponse;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CustomerCategoryResponse {
        private String customerCategory;
    }
}