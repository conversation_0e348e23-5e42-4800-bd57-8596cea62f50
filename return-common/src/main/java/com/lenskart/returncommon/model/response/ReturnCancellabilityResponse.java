package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class ReturnCancellabilityResponse {

    @ApiObjectField(name = "is_cancellable", description = "Field representing whether a return exists against this item an the same is cancellable")
    @JsonProperty("is_cancellable")
    private Boolean isCancellable;

    @ApiObjectField(name = "error_type", description = "Field stating the reason type as to why this item's retrun cannot be cancelled")
    @JsonProperty("error_type")
    private String errorType;

    @ApiObjectField(name = "error_message", description = "Field stating the reason as to why this item's retrun cannot be cancelled")
    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("return_id")
    private Integer returnId;
}
