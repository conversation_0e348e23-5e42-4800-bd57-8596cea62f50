package com.lenskart.returncommon.model.enums;

public enum ReverseType {
    REVERSE("reverse"),RTO("rto");

    private String reverseType;
    ReverseType(String reverseType) {
        this.reverseType = reverseType;
    }

    public String getName() {
        return reverseType;
    }

    public static ReverseType getName(String revTypeStr){
        ReverseType revTypeEnum = null;
        for (ReverseType candidate : ReverseType.values()) {
            if(candidate.getName().equalsIgnoreCase(revTypeStr)){
                revTypeEnum = candidate;
            }
        }
        return revTypeEnum;
    }

    @Override
    public String toString() {
        return this.getName();
    }
}
