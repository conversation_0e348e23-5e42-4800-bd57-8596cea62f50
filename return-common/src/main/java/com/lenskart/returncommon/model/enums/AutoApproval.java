package com.lenskart.returncommon.model.enums;

public enum AutoApproval {
    AGENT, APPROVE, REJECT;

    public static AutoApproval fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Customer category cannot be null or empty.");
        }
        try {
            return AutoApproval.valueOf(value.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid customer category: " + value);
        }
    }
}
