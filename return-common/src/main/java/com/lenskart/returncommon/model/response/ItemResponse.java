package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Date;
import java.util.List;

@Data
@ToString
public class ItemResponse {
    @JsonProperty("is_returnable")
    private boolean isReturnable;
    @JsonProperty("draftStatus")
    private boolean draftStatus;
    @JsonProperty("do_refund")
    private boolean doRefund;
    @JsonProperty("exchange")
    private boolean exchange;
    @JsonProperty("exchange_only_cta")
    private boolean exchangeOnlyCTA;
    @JsonProperty("is_approval_needed")
    private boolean isApprovalNeeded;
    @JsonProperty("refund_methods")
    private List<String> refundMethods;
    @JsonProperty("item_price")
    private double itemPrice;
    @JsonProperty("item_refunded_amount")
    private double itemRefundedAmount;
    @JsonProperty("magento_item_id")
    private Integer magentoItemId;
    @JsonProperty("amount_to_refund")
    private double amountToRefund;
    @JsonProperty("lkcash_to_refund")
    private double lkcashToRefund;
    @JsonProperty("lkcash_plus_to_refund")
    private double lkcashPlusToRefund;
    @JsonProperty("return_eligible_till_date")
    private String returnEligibleTillDate;
    @JsonProperty("refund_method_request")
    private String refundMethodRequest;
    @JsonProperty("error_for_not_refundable")
    private String errorForNotRefundable;
    @JsonProperty("error_for_not_exchangeable")
    private String errorForNotExchangeable;
    @JsonProperty("error_for_not_returnable")
    private String errorForNotReturnable;
    @JsonProperty("error_labels")
    @ApiObjectField(name = "error_labels", description = "It has error reasons")
    private ErrorLables errorLables;
    @JsonProperty("is_exchange_expired")
    private boolean isExchangeExpired;
    @JsonProperty("refund_intent_created_at")
    private Date refundIntentCreatedAt;
    @JsonProperty("return_eligibiity_period")
    private Integer returnEligibilityPeriod = 30;
    @JsonProperty("need_approval_message")
    private String needApprovalMessage;
    @JsonProperty("return_id")
    private Integer returnId;
    @JsonProperty("return_source")
    private String returnSource;
    @JsonProperty("rto_exchange_eligibility")
    private RtoExchangeEligibilityResponse rtoExchangeEligibilityResponse;
    @JsonProperty("sc_cancel_convertible_eligibility")
    private StoreCreditCancelConvertEligibility storeCreditCancelConvertEligibility;
}
