package com.lenskart.returncommon.model.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

@Data
public class CommRequestDTO {
    @NotNull
    private String eventType;
    @NotNull
    private Map<String, String> conditionsMap;
    @NotNull
    private Integer returnRequestId;
    @NotNull
    private Integer orderId;
    @NotNull
    private Integer returnId;
    private Integer uwItemId;
    private Map<String, Object> params;
    private String templateName;
    private boolean skipValidation;
}
