package com.lenskart.returncommon.model.dto;

import lombok.Data;

@Data
public class OrdersHeaderDTO {
    private int orderId;
    private int incrementId;
    private int paymentCaptureFlag;
    private Boolean isBulkOrder;
    private String lkCountry;
    private Boolean isExchangeOrder;
    private Boolean assignedMgr;
    private String paymentMode;
    private boolean isFrameBrokenGvFlow;
    private String legalOwnerCountry;
    private String facilityCode;
    private String storeType;
}
