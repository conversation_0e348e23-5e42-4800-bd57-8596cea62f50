package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderItemGSTDetailDto {

    private Integer orderId;
    private Integer uwItemId;
    private Integer pid;
    private Integer classificationId;
    private String hsn;
    private String prodDesc;
    private Double costPerItem;
    private Double cgstPc;
    private Double cgstAmount;
    private Double sgstPc;
    private Double sgstAmount;
    private Double igstPc;
    private Double igstAmount;
    private Double ugstPc;
    private Double ugstAmount;
    private Double totalAmount;
    private Date createdAt;

}
