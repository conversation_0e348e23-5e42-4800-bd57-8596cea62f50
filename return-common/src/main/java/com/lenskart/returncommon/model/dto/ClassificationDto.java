package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ClassificationDto {

    private Integer id;

    private String classificationName;

    private String website;

    private String unicom_syn_status;

    private String displayName;

    private String UStaxCode;

    private String UStaxCodeWithoutPower;
}
