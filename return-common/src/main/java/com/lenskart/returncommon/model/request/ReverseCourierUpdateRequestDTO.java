package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import lombok.Data;

@Data
public class ReverseCourierUpdateRequestDTO {
    @JsonProperty("pickupId")
    private Integer pickupId;
    @JsonProperty("courier")
    private String courier;
    @JsonProperty("referenceId")
    private String referenceId;
    @JsonProperty("awb")
    private String awb;
    @JsonProperty("userId")
    private Integer userId;
    @JsonProperty("userName")
    private String userName;
    @JsonProperty("order")
    private OrdersDTO ordersDTO;
}
