package com.lenskart.returncommon.model.enums;

public enum ReturnSubHeaderTimelineDatesEnum {
    PIKCUP_DATE("pickup_date"),
    EXCHANGE_DISPATCH_DATE("exchange_dispatch_date"),
    REFUND_DATE("refund_date"),
    REFUND_MODE("refund_mode"),
    WORKING_DAYS("working_days"),
    COURIER_NAME("courier_name"),
    TRACKING_ID("tracking_id"),
    TRACKING_URL("tracking_url");

    private String val;
    ReturnSubHeaderTimelineDatesEnum(String value) {
        val=value;
    }
    public static ReturnSubHeaderTimelineDatesEnum getReturnSubHeaderTimelineDatesEnum(String value){
        ReturnSubHeaderTimelineDatesEnum returnSubHeaderTimelineDatesEnum = null;
        for (ReturnSubHeaderTimelineDatesEnum candidate : ReturnSubHeaderTimelineDatesEnum.values()) {
            if(candidate.getName().equalsIgnoreCase(value)){
                returnSubHeaderTimelineDatesEnum = candidate;
            }
        }
        return returnSubHeaderTimelineDatesEnum;
    }

    public String getName() {
        return this.val;
    }
}