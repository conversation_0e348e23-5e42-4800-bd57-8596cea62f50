package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import lombok.Data;

import java.util.Date;

@Data
public class ReturnDetailsForSmsDTO {
    private String returnPickupCourierName;

    private String returnPickupAwb;

    private String returnPickupDate;

    private Date returnCreationDate;

    private ReturnCreationRequestDTO returnCreationRequest;
}
