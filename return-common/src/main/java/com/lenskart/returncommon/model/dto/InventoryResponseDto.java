package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InventoryResponseDto {

    private UwOrderDTO uwOrder;
    // get this order from itemId in uworder
    private OrdersDTO order;
    // get this from orderId of order
    private OrderAddressUpdateDto orderAddressUpdateDto;
    // get this from unicomOrderCode of uw order
    private ShippingStatusDto shippingStatusDto;
    // get this from order's incrementId
    private OrdersHeaderDTO ordersHeaderDTO;
    //get this by uwProductId
    private ProductDto productDto;
    List<String> hubMasterFacilityCodes;
    //find by uwOrders b2bRefrenceItemId
    private UwOrderDTO b2bCounterPart;
    //find by shippingPackageId from b2bCounterPart
    private InvoiceDetailsDto invoiceDetailsDto;

}
