package com.lenskart.returncommon.model.request;

import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.ItemRequestDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.refund.client.model.response.GetRefundAmountResponse;
import lombok.Data;

import java.util.List;

@Data
public class RRRServiceRequest { //ReturnRefundRuleServiceRequest
    private OrderInfoResponseDTO orderInfoResponse;
    private UwOrderDTO uwOrder;
    private UwOrderDTO uwOrderWH;
    private ItemRequestDTO itemRequest;
    private ReturnRefundEligibilityRequestDTO rreRequest;
    private List<ShippingStatusDetail> shippingStatuses;
    private GetRefundAmountResponse refundAmountResponse;
}
