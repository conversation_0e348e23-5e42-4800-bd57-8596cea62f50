package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
public class Result {


    @ApiObjectField(name = "success", description = "return creation success flag")
    @JsonProperty("success")
    private Boolean success;

    @ApiObjectField(name = "returns", description = "itemwise details of the return")
    @JsonProperty("returns")
    private List<Returns> returns;

    @ApiObjectField(name = "group_id", description = "itemwise details of the return")
    @JsonProperty("group_id")
    private Long groupId;

    @ApiObjectField(name = "dispensingFlag", description = "Is dspensing team assigned?")
    @JsonProperty("dispensingFlag")
    private Boolean dispensingFlag;

    @ApiObjectField(name = "returnLabelCreated", description = "returnLabelCreated")
    @JsonProperty("returnLabelCreated")
    private Boolean returnLabelCreated = false;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public List<Returns> getReturns() {
        return returns;
    }

    public void setReturns(List<Returns> returns) {
        this.returns = returns;
    }
}
