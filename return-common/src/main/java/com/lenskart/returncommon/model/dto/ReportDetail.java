package com.lenskart.returncommon.model.dto;

import lombok.ToString;

@ToString
public class ReportDetail {
    private String name;
    private int assign;
    private int normal;
    private int time;

    public ReportDetail(String name) {
        this.name = name;
    }

    public void incrementAssign() { assign++; }
    public void incrementNormal() { normal++; }
    public void incrementTime() { time++; }

    public String getName() { return name; }
    public int getAssign() { return assign; }
    public int getNormal() { return normal; }
    public int getTime() { return time; }
}
