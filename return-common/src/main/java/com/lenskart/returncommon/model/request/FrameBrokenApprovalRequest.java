package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public record FrameBrokenApprovalRequest(
        @JsonProperty("repairId") String repairId,
        @JsonProperty("orderId") String orderId,
        @JsonProperty("itemId") String itemId,
        @JsonProperty("ticketId") String ticketId,
        @JsonProperty("gvAmountRequested") String gvAmountRequested,
        @JsonProperty("status") String status,
        @JsonProperty("notes") String notes,
        @JsonProperty("mobile") String mobile,
        @JsonProperty("reason") String reason
) {}

