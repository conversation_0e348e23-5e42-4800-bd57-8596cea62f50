package com.lenskart.returncommon.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ReturnOrderItemDTO {
    private Integer id;
    private Integer returnId;
    private Integer uwItemId;
    private Integer itemId;
    private Long productId;
    private String status;
    private String qcStatus;
    private String qcComment;
    private Date returnCreateDatetime;
    private String isFranchise;
    private Integer classification;
    private String productDeliveryType;
    private String method;
    private String channel;
    private String reasonForReturn;
    private String qcFailReason;
    private String tbybPrescription;
    private int itemSelectedFlag;
    private Integer csohUpdatedFlag = 0;
    private Date updatedAt;
    private String wfi;
    private String barcodeId;
}
