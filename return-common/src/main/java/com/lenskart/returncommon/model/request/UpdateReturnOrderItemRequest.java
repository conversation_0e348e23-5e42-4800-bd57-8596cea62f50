package com.lenskart.returncommon.model.request;

import lombok.Data;

import java.util.List;

@Data
public class UpdateReturnOrderItemRequest {
    private String status;
    private Integer csohUpdatedFlag;
    private UpdateReturnItemConditions condition;

    @Data
    public static class UpdateReturnItemConditions {
        private Integer id;
        private String status;
        private List<String> statusNotIn;
    }
}
