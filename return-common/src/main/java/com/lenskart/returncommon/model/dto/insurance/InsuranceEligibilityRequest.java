package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;


import java.util.List;

@Data
@ToString
public class InsuranceEligibilityRequest {
    @JsonProperty("magento_item_ids")
    @ApiObjectField(name = "magento_item_ids")
    private List<Integer> magentoItemIds;
    private List<OrdersDTO> ordersDTOList;
}