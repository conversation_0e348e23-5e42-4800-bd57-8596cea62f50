package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Data
public class LenskartClaimDetailStatusResponse {

    @JsonProperty("isClaimAllowed")
    @ApiObjectField(name = "isClaimAllowed")
    private Boolean isClaimAllowed;

    @JsonProperty("msg")
    @ApiObjectField(name = "msg")
    private String msg;

    @JsonProperty("claimStatus")
    @ApiObjectField(name = "claimStatus")
    private String claimStatus;

}
