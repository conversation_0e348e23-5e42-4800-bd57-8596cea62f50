package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Date;

@Data
public class RefundStatusHistory {
    @ApiObjectField(name = "date", description = "date")
    @JsonProperty("date")
    private Date date;

    @ApiObjectField(name = "refund_status", description = "return status")
    @JsonProperty("refund_status")
    private String refundStatus;


    @ApiObjectField(name = "comment", description = "comment")
    @JsonProperty("comment")
    private String comment;
}
