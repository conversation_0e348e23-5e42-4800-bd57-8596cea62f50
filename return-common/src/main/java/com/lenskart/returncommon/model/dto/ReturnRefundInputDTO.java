package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lenskart.returncommon.model.request.RRRServiceRequest;
import lombok.Data;

@Data
public class ReturnRefundInputDTO {
    private boolean isPseudoGatepass = false;
    private boolean isBranded = false;
    private boolean isQcPass = true;
    private String returnInitiatedSource;
    private boolean isAccessoryMissing = false;
    private Integer returnPeriod = 0;
    private Integer uwItemId;
    private Integer itemId;
    private boolean isLastPiece = false;
    private boolean isLensOnly = false;
    private String countryCode;
    private String reverseType;
    private String navChannel;
    private String triggerPoint;
    private Double amountValidity = null;
    private boolean blacklistedPhoneNumbers;
    private boolean blacklistedPincodes;
    private boolean callRefundRule = false;
    private String ruleCalledFrom;
    private int returnCount = 0;
    private int exchangeCount = 0;
    private int refundCount = 0;
    private boolean isFraud;
    private String returnReason;
    private String draftReturnMethod;
    private Integer returnId;
    private boolean isBulkOrder;
    private Integer customerScore = 1;
    private Integer storeScore = 1;
    @JsonIgnore
    private RRRServiceRequest rrrServiceRequest;
    @JsonIgnore
    private DecisionTableRefundDTO decisionTableRefundDTO;

}
