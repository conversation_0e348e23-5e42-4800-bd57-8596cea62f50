package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.ReturnFlowResultDto;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ReturnFlowResponse {
    @ApiObjectField(name = "result")
    @JsonProperty("result")
    private ReturnFlowResultDto result;

    @ApiObjectField(name = "status")
    @JsonProperty("status")
    private String status;

    @ApiObjectField(name = "traceId")
    @JsonProperty("traceId")
    private String traceId;
}
