package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.Result;
import com.lenskart.returncommon.model.dto.ReversePickupDetails;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Map;

public @Data
class ReturnCreationResponse extends Response {


    private int status;
    private Object entity;
//    private MultivaluedMap<String, Object> metadata;

    @ApiObjectField(name = "result", description = "return creation success flag")
    @JsonProperty("result")
    private Result result;

    @ApiObjectField(name = "reversePickUpDetails", description = "reversePickUpDetails")
    @JsonProperty("reversePickUpDetails")
    private ReversePickupDetails reversePickupDetails;
    
    @JsonIgnore
    private Map<Integer, Double> itemRefundMap;
    @JsonIgnore
    private Integer incrementId;
    @JsonIgnore
    private List<UwOrderDTO> uwOrders;
    @JsonIgnore
    private Integer returnId;
    @JsonIgnore
    private PurchaseOrderDetailsDTO purchaseOrderDetailsDTO;
    @JsonIgnore
    private UwOrderDTO uwOrder;
    @JsonIgnore
    private UwOrderDTO uwOrderWH;

    @Override
    public Object getEntity() {
        return this.entity;
    }

    @Override
    public int getStatus() {
        return this.status;
    }

    @Override
    public MultivaluedMap<String, Object> getMetadata() {
        return null;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setEntity(Object entity) {
        this.entity = entity;
    }

//    public void setMetadata(MultivaluedMap<String, Object> metadata) {
//        this.metadata = metadata;
//    }


}
