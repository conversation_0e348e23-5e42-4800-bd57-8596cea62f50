package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.ItemWiseGetAmount;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
public class ItemWiseFastRefunResponse {
    @ApiObjectField(name = "items", description = "items")
    @JsonProperty("items")
    private List<ItemWiseGetAmount> itemIds;

    @ApiObjectField(name = "ItemWiseFastRefunResponserefundGrandTotal", description = "refundGrandTotal")
    @JsonProperty("refundGrandTotal")
    private Double refundGrandTotal;

    @ApiObjectField(name = "orderGrandTotal", description = "orderGrandTotal")
    @JsonProperty("orderGrandTotal")
    private Double orderGrandTotal;

    @ApiObjectField(name = "debitAdditionalAmount", description = "debitAdditionalAmount")
    @JsonProperty("debitAdditionalAmount")
    private Double debitAdditionalAmount;

    @ApiObjectField(name = "lenskartPlusAmount", description = "lenskartPlusAmount")
    @JsonProperty("lenskartPlusAmount")
    private Double lenskartPlusAmount;

    @ApiObjectField(name = "lenskartAmount", description = "lenskartAmount")
    @JsonProperty("lenskartAmount")
    private Double lenskartAmount;

    @ApiObjectField(name = "status", description = "status")
    @JsonProperty("responseStatus")
    private String responseStatus;
}
