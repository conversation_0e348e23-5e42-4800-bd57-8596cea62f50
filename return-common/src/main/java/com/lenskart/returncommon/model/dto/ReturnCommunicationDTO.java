package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import lombok.Data;

import java.util.List;

@Data
public class ReturnCommunicationDTO {
    private Long groupId;
    private ReturnCreationRequestDTO returnCreationRequest;
    private List<UwOrderDTO> uwOrderDTOs;
    private OrdersDTO ordersDTO;
    private List<ReturnItem> exchangeItemList;
    private String communicationMethod;
    private ReturnOrderDTO returnOrderDTO;
    private String flow;
    private Integer uwItemId;
    private Integer incrementId;
    private String refundMethod;
}
