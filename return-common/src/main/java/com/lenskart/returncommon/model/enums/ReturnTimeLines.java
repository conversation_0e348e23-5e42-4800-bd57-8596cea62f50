package com.lenskart.returncommon.model.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum ReturnTimeLines {
    RPU(Arrays.asList(ReturnStatus.NEW_REVERSE_PICKUP.getStatus(), ReturnStatus.REFERENCE_ID_ISSUED.getStatus(), ReturnStatus.AWB_ASSIGNED.getStatus(),
            ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    returntoNearbyStore(Arrays.asList(ReturnStatus.RETURN_EXPECTED_POS.getStatus(),ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    StoreReceiving(Arrays.asList(ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    DirectReceiving(Arrays.asList(ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    ShiptoLenskrt(Arrays.asList(ReturnStatus.RETURN_EXPECTED_WH.getStatus(),ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    RPU_RETURN_NEED_APPROVAL(Arrays.asList(ReturnStatus.NEW_REVERSE_PICKUP.getStatus(),ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_ACCEPTED.getStatus()
            ,ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    returntoNearbyStore_RETURN_NEED_APPROVAL(Arrays.asList(ReturnStatus.RETURN_EXPECTED_POS.getStatus(),ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_ACCEPTED.getStatus()
            ,ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    StoreReceiving_RETURN_NEED_APPROVAL(Arrays.asList(ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_ACCEPTED.getStatus(),ReturnStatus.RETURN_RECEIVED.getStatus()
            ,ReturnStatus.RETURN_REFUNDED.getStatus())),
    ShiptoLenskrt_RETURN_NEED_APPROVAL(Arrays.asList(ReturnStatus.RETURN_EXPECTED_WH.getStatus(),ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_ACCEPTED.getStatus()
            ,ReturnStatus.RETURN_RECEIVED.getStatus(), ReturnStatus.RETURN_REFUNDED.getStatus())),
    RPU_RETURN_REJECTED_HANDOVER_PENDING(Arrays.asList(ReturnStatus.NEW_REVERSE_PICKUP.getStatus(),ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus()
            ,ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus())),
    returntoNearbyStore_RETURN_REJECTED_HANDOVER_PENDING(Arrays.asList(ReturnStatus.RETURN_EXPECTED_POS.getStatus(),ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus()
            ,ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus())),
    StoreReceiving_RETURN_REJECTED_HANDOVER_PENDING(Arrays.asList(ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus()
            ,ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus())),
    ShiptoLenskrt_RETURN_REJECTED_HANDOVER_PENDING(Arrays.asList(ReturnStatus.RETURN_EXPECTED_WH.getStatus(),ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus()
            ,ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus()));

    final List<String> statusList;
    ReturnTimeLines(List<String> asList) {
        statusList = new ArrayList<>(asList);
    }

    public static ReturnTimeLines getReturnMethod(String returnMethod) {
        for(ReturnTimeLines returnTimeLines: ReturnTimeLines.values()){
            if(returnTimeLines.toString().equalsIgnoreCase(returnMethod)){
                return returnTimeLines;
            }
        }
        return null;
    }

    public List<String> getStatusList(){
        return this.statusList;
    }
}
