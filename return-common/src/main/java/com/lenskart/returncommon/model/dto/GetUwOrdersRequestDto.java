package com.lenskart.returncommon.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GetUwOrdersRequestDto {
    private List<Integer> b2bUwReferenceItemIds;
    private List<Integer> parentUwItemIds;
    private String barcode;
    private Integer uwItemId;
}
