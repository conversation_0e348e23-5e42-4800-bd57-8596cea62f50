package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnItemRequestDTO;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonIgnoreProperties(
        ignoreUnknown = true
)
@ToString
public class ReturnOrderRequestDTO {
    @JsonProperty("increment_id")
    private Integer incrementId;
    @JsonProperty("items")
    private List<ReturnItemRequestDTO> items = new ArrayList();
    @JsonProperty("source")
    private String source;
    @JsonProperty("reference_order_code")
    private String referenceOrderCode;
    @JsonProperty("do_refund")
    private Boolean doRefund;
    @JsonProperty("is_dualco")
    private Boolean isDualCo;
    @JsonProperty("facility")
    private String facility;
    @JsonProperty("raiseRPUatUnicom")
    private Boolean raiseRPUatUnicom;
    @JsonProperty("rtoItem")
    private Boolean rtoItem;
    @JsonProperty("awaitedRtoItem")
    private Boolean awaitedRtoItem = false;
    @JsonProperty("NewFlowFlag")
    private Integer newFlowFlag;
    @JsonProperty("groupId")
    private Long groupId;
    @JsonProperty("raiseRPUatNexs")
    private Boolean raiseRPUatNexs;
    @JsonProperty("reasonDetail")
    private String reasonDetail;
    @JsonProperty("refundMethod")
    private String refundMethod;
    @JsonProperty("shipping_package_id")
    private String shippingPackageId;
    @JsonProperty("uw_orders_list")
    private List<UwOrderDTO> uwOrderDTOs;
    @JsonProperty("orders_list")
    private List<OrdersDTO> orderDTOs;
    @JsonProperty("orders_header")
    private OrdersHeaderDTO ordersHeaderDTO;
    @JsonProperty("user_id")
    private String userId;
}
