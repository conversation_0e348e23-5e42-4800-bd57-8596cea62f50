package com.lenskart.returncommon.model.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.Date;

@Data
public class CancellationStatusHistory {

    @ApiObjectField(name ="cancel_id",description = "cancel_id")
    @JsonProperty("cancel_id")
    private Integer cancelId;

    @ApiObjectField(name ="cancellation_status",description = "cancellation_state")
    @JsonProperty("cancellation_status")
    private String cancellationStatus;

    @ApiObjectField(name ="cancelled_by",description = "cancelled_by")
    @JsonProperty("cancelled_by")
    private String cancelledBy;

    @ApiObjectField(name ="cancellation_reason",description = "cancellation_reason")
    @JsonProperty("cancellation_reason")
    private String cancellationReason;

    @ApiObjectField(name ="cancellation_type",description = "cancellation_type")
    @JsonProperty("cancellation_type")
    private String cancellationType;

    @ApiObjectField(name ="date",description = "date")
    @JsonProperty("date")
    private Date date;
}
