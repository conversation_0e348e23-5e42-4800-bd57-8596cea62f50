package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.enums.QCType;
import lombok.Data;

@Data
public class ReverseCourierDetail {

    private Integer pincode;
    private Boolean status;
    private Integer tat;
    private String courier;
    private Integer cpId;
    private String cpName;
    private QCType courierAssignmentType;
    private boolean isQcAtDoorStepEligibleByCourierDetails;
    private boolean isCourierReassigned;
    private boolean allowDispensingCourier;
}
