package com.lenskart.returncommon.model.dto.insurance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Data
public class ExternalOpticalInsuranceClaimDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("status")
    @ApiObjectField(name = "status")
    private Integer status;

    @JsonProperty("claim")
    @ApiObjectField(name = "claim")
    private LenskartClaimDetailStatusResponse claim;

    @JsonProperty("invoiceNumber")
    @ApiObjectField(name = "invoiceNumber")
    private String invoiceNumber;

    @JsonProperty("products")
    @ApiObjectField(name = "products")
    private List<Product> products;

    @JsonProperty("error")
    @ApiObjectField(name = "error")
    private String error;

    @JsonProperty("message")
    @ApiObjectField(name = "message")
    private String message;

    @JsonProperty("operation")
    @ApiObjectField(name = "operation")
    private String operation;

    @JsonProperty("timestamp")
    @ApiObjectField(name = "timestamp")
    private String timestamp;

}
