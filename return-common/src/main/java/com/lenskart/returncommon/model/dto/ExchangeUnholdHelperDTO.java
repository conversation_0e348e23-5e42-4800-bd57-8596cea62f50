package com.lenskart.returncommon.model.dto;

import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExchangeUnholdHelperDTO {
    private Integer returnId;
    private Integer uwItemId;
    private Integer requestId;
    private Integer incrementId;
}
