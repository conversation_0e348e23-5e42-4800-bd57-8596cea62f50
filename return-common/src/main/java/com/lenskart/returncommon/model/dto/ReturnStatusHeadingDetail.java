package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
@ToString
public class ReturnStatusHeadingDetail {
    @JsonProperty("heading")
    @ApiObjectField(name = "heading", description = "heading")
    private String heading;
    @JsonProperty("sub_headings")
    @ApiObjectField(name = "sub_headings", description = "sub_headings")
    private List<String> subHeading;
    @JsonProperty("estimated_date")
    @ApiObjectField(name = "estimated_date", description = "estimated_date")
    private Long estimatedDate;
}