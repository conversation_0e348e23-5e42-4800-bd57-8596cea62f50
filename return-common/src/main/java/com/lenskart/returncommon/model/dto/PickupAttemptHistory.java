package com.lenskart.returncommon.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

@Data
public class PickupAttemptHistory {
    @JsonProperty("pickup_status")
    @ApiObjectField(name = "pickup_status", description = "pickup_status")
    private String pickupStatus;
    @JsonProperty("reason")
    @ApiObjectField(name = "reason", description = "reason")
    private String reason;
    @JsonProperty("date")
    @ApiObjectField(name = "date", description = "date")
    private Long date;
    @JsonProperty("pickup_tracking_details")
    @ApiObjectField(name = "pickup_tracking_details", description = "pickup_tracking_details")
    private PickupTrackingDetails pikupTrackingDetails;
}
