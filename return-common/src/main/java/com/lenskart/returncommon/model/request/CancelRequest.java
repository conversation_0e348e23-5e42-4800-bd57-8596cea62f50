package com.lenskart.returncommon.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.returncommon.model.dto.BankDetailDto;
import com.lenskart.returncommon.model.enums.LKCountry;
import lombok.Data;
import org.jsondoc.core.annotation.ApiObjectField;

import java.util.List;

@Data
@JsonIgnoreProperties(
        ignoreUnknown = true,
        value = {"amount"}
)
public class CancelRequest {
    @JsonProperty("payment_method")
    private String paymentMethod;
    @JsonProperty("reason_id")
    private Integer reasonId;
    @JsonProperty("reason_detail")
    private String reasonDetail;
    @JsonProperty("bank_detail")
    private BankDetailDto bankDetail;
    @JsonProperty("source")
    private String source;
    @JsonProperty("initiated_by")
    private String initiatedBy;
    @JsonProperty("unicom_code")
    private String unicomCode;
    @JsonProperty("cancellation_type")
    private String cancellationType;
    @JsonProperty("List Items")
    private List<Integer> items;
    @JsonProperty("IsWalletRefundEligible")
    private boolean IsWalletRefundEligible;
    private boolean cancelRefund;
    @JsonIgnore
    private LKCountry lkCountry;
    private String csOrderId;
    @JsonProperty(value = "magento_item_id", required = false)
    private Integer magentoItemId;
    @JsonProperty(value = "cancellationSubType", required = false)
    private String cancellationSubType;
    @JsonProperty(value = "exchangeOnCancellation", required = false)
    private boolean exchangeOnCancellation;
}
