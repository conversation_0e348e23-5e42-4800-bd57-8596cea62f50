package com.lenskart.returncommon.model.dto;

import com.lenskart.orderops.model.ItemsStateStatusDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class OrderStatusUpdateDTO {
    private Integer incrementId;
    private String unicomOrderCode;
    private String currentStatus;
    private Integer returnId;
    private List<ItemsStateStatusDto> magentoItems;

    public OrderStatusUpdateDTO(Integer incrementId, String unicomOrderCode, String currentStatus, Integer returnId) {
        this.incrementId = incrementId;
        this.unicomOrderCode = unicomOrderCode;
        this.currentStatus = currentStatus;
        this.returnId = returnId;
    }

    public OrderStatusUpdateDTO(Integer incrementId, String unicomOrderCode, String currentStatus, Integer returnId, List<ItemsStateStatusDto> magentoItems) {
        this.incrementId = incrementId;
        this.unicomOrderCode = unicomOrderCode;
        this.currentStatus = currentStatus;
        this.returnId = returnId;
        this.magentoItems = magentoItems;
    }
}
