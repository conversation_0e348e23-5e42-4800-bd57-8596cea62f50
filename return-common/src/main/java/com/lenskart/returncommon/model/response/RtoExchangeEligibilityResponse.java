package com.lenskart.returncommon.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RtoExchangeEligibilityResponse {
    @JsonProperty(value = "is_rto_exchangeable")
    private boolean isRtoExchangeable;
    @JsonProperty(value = "eligible_sc_exchange_amount")
    private double eligibleScExchangeAmount;
}
