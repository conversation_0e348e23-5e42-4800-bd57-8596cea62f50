package com.lenskart.returncommon.cache;

import java.util.ArrayList;
import java.util.List;

public class CacheLoadNotifier {
    private List<CacheLoadObserver> observers = new ArrayList<>();

    // Method to register observers
    public void registerObserver(CacheLoadObserver observer) {
        observers.add(observer);
    }

    // Method to notify observers when tasks are done
    public void notifyObservers() {
        for (CacheLoadObserver observer : observers) {
            observer.onCacheLoadComplete();
        }
    }
}
