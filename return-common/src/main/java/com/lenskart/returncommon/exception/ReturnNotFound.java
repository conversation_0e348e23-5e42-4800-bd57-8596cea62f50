package com.lenskart.returncommon.exception;

public class ReturnNotFound extends BaseOrderException {

    private static final long serialVersionUID = 1L;

    public ReturnNotFound() {
        super("RETURN_NOT_FOUND", "Unable to find return order.");
    }

    public ReturnNotFound(String exception, String message) {
        super(exception, message);
    }

    public ReturnNotFound(String reason) {
        super(reason);
    }
}
