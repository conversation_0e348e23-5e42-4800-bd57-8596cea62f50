package com.lenskart.returncommon.exception;

public abstract class BaseOrderException extends Exception {

    private static final long serialVersionUID = 1L;
    private String code;
    private String message;

    public BaseOrderException(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public BaseOrderException(String code, String message, Throwable cause) {
        super(cause);
        this.code = code;
        this.message = message;
    }

    public BaseOrderException(String message) {
        super(message);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}
