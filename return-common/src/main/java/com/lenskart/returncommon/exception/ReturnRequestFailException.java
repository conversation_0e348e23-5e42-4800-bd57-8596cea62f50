package com.lenskart.returncommon.exception;

public class ReturnRequestFailException extends Exception {
    String code;
    String message;

    public ReturnRequestFailException(Throwable cause) {
        super(cause);
    }

    public ReturnRequestFailException(String message) {
        super(message);
    }

    public ReturnRequestFailException(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
