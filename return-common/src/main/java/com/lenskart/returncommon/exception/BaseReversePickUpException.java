package com.lenskart.returncommon.exception;

public abstract class BaseReversePickUpException extends Exception {

    private static final long serialVersionUID = 1L;
    private String code;
    private String message;

    public BaseReversePickUpException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BaseReversePickUpException(String message) {
        this.message=message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}

