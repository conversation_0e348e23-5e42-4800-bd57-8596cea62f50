package com.lenskart.returncommon.exception;

import java.io.Serial;

public class OrderNotFound  extends BaseOrderException{
    @Serial
    private static final long serialVersionUID = 1L;

    public OrderNotFound() {
        super("ORDER_NOT_FOUND", "Unable to find order.");
    }

    public OrderNotFound(String exception, String message) {
        super(exception, message);
    }

    public OrderNotFound(String reason) {
        super(reason);
    }
}
