package com.lenskart.returncommon.config;

import io.lettuce.core.ClientOptions;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.LoggingCacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Configuration
@EnableCaching
@ConditionalOnProperty(prefix = "spring.redis.sentinel", value = "master")
@ConfigurationProperties(prefix = "spring.redis.sentinel")
public class RedisConfig implements CachingConfigurer {

    private String master;
    private List<String> nodes;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration().master(master);
        if (!CollectionUtils.isEmpty(nodes)) {
            nodes.forEach(node -> sentinelConfig.addSentinel(parseRedisNode(node)));
        }
        ClientOptions clientOptions = ClientOptions.builder()
                .autoReconnect(false)
                .disconnectedBehavior(ClientOptions.DisconnectedBehavior.REJECT_COMMANDS)
                .build();
//        ClientResources clientResources = DefaultClientResources.builder()
//                .reconnectDelay(Delay.constant(Duration.ofSeconds(30)))
//                .build();
        return new LettuceConnectionFactory(sentinelConfig, LettucePoolingClientConfiguration.builder()
                .poolConfig(createLettucePoolConfig())
//                .clientResources(clientResources)
                .clientOptions(clientOptions)
                .build());
    }

    @ConfigurationProperties(prefix = "spring.redis.lettuce.pool")
    private GenericObjectPoolConfig<?> createLettucePoolConfig() {
        log.info("creating LettucePoolConfig");
        return new GenericObjectPoolConfig<>();
    }

    private RedisNode parseRedisNode(String node) {
        String[] parts = node.split(":");
        String host = parts[0];
        int port = Integer.parseInt(parts[1]);
        return new RedisNode(host, port);
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        log.info("initializing redisTemplate");
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory());
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        return redisTemplate;
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        log.info("initializing cacheManager");
        RedisCacheConfiguration cacheConfiguration = RedisCacheConfiguration.defaultCacheConfig();

        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
//        cacheConfigurations.put(Constant.EVENT_RULE_CACHE_NAME, cacheConfiguration);
//        cacheConfigurations.put(Constant.COMM_TEMPLATE_CACHE_NAME, cacheConfiguration);
//        cacheConfigurations.put(Constants.RETRY_RULE_TEMPLATE_CACHE_NAME, cacheConfiguration);
//        cacheConfigurations.put(Constants.REFUND_SERVICE_CONFIGURATION_CACHE, cacheConfiguration);
//        cacheConfigurations.put(Constants.REFUND_STATUS_MAPPING_CACHE, cacheConfiguration);

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(cacheConfiguration)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    @Override
    public CacheErrorHandler errorHandler() {
        return new LoggingCacheErrorHandler();
    }

}
