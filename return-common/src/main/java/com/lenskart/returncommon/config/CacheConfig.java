package com.lenskart.returncommon.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.stream.Collectors;

@Slf4j
@Configuration
@EnableCaching
public class CacheConfig {
//    @Bean
//    public CacheManager cacheManager() {
//        return new ConcurrentMapCacheManager("return_status_heading");
//    }

    @Bean("return_status_heading_key_gen")
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            String key = Arrays.stream(params)
                    .map(param -> param != null ? param.toString() : "null")
                    .collect(Collectors.joining("-"));
            log.info("Generated Cache Key for return status heading: " + key);
            return key;
        };
    }
}
