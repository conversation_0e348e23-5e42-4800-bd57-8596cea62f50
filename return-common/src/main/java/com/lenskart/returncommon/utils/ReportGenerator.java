package com.lenskart.returncommon.utils;

import com.lenskart.returncommon.model.dto.ReportDetail;
import com.lenskart.returncommon.model.dto.ReturnDetailsVSM;

import java.util.*;

public class ReportGenerator {
    public static List<ReportDetail> processReturns(List<ReturnDetailsVSM> returnDetails, int timeDiffFlag, long offset, long lastCount, int pageSize) {
        Map<String, ReportDetail> reportDetails = new HashMap<>();
        int i = 0;

        for (ReturnDetailsVSM row : returnDetails) {
            if (shouldProcessRow(i, offset, lastCount, pageSize)) {
                processRow(reportDetails, row, timeDiffFlag);
            }
            i++;
        }
        return new ArrayList<>(reportDetails.values());
    }

    private static boolean shouldProcessRow(int index, long offset, long lastCount, int pageSize) {
        return (index >= offset && index <= lastCount) || pageSize == 0;
    }

    private static void processRow(Map<String, ReportDetail> reportDetails, ReturnDetailsVSM row, int timeDiffFlag) {
        String agent = row.getAgentEmail().trim();
        long timeDiff = row.getTimeDiff();
        boolean isAgentAssigned = !agent.isEmpty() && !agent.equalsIgnoreCase("unassigned");

        evaluateConditions(reportDetails, "POS_revalidation", isStatus(row, "return_need_approval", "return_need_approval_from_whse"), isAgentAssigned, timeDiff, timeDiffFlag);
        evaluateConditions(reportDetails, "warehouse_received_-_reverse", isStatusAndType(row, "return_received", "reverse"), isAgentAssigned, timeDiff, timeDiffFlag);
        evaluateConditions(reportDetails, "warehouse_received_-_RTO", isStatusTypeAndFlag(row, "return_received", "rto", "yes"), isAgentAssigned, timeDiff, timeDiffFlag);
        evaluateConditions(reportDetails, "warehouse_received_-_RTO_(Putaway_pending)", isFullCondition(row, "return_received", "rto", "yes", "initiated_stockin"), isAgentAssigned, timeDiff, timeDiffFlag);
        evaluateConditions(reportDetails, "warehouse_received_-_reverse_(Putaway_pending)", isFullCondition(row, "return_received", "reverse", "yes", "initiated_stockin"), isAgentAssigned, timeDiff, timeDiffFlag);
        evaluateConditions(reportDetails, "Return_Follow_Up", isStatus(row, "return_under_followup"), isAgentAssigned, timeDiff, timeDiffFlag);
        evaluateConditions(reportDetails, "QC_Pending", isStatus(row, "qc_pending"), isAgentAssigned, timeDiff, timeDiffFlag);
    }

    private static boolean isStatus(ReturnDetailsVSM row, String... statuses) {
        return Arrays.asList(statuses).contains(row.getStatus());
    }

    private static boolean isStatusAndType(ReturnDetailsVSM row, String status, String type) {
        return row.getStatus().equals(status) && row.getReturnType().equals(type);
    }

    private static boolean isStatusTypeAndFlag(ReturnDetailsVSM row, String status, String type, String flag) {
        return row.getStatus().equals(status) && row.getReturnType().equals(type) && row.getReceivingFlag().equals(flag);
    }

    private static boolean isFullCondition(ReturnDetailsVSM row, String status, String type, String flag, String roiStatus) {
        return isStatusTypeAndFlag(row, status, type, flag) && row.getRoiStatus().equals(roiStatus);
    }

    private static void evaluateConditions(Map<String, ReportDetail> reportDetails, String key, boolean condition, boolean isAgentAssigned, long timeDiff, long timeDiffFlag) {
        if (condition) {
            reportDetails.putIfAbsent(key, new ReportDetail(key));
            ReportDetail detail = reportDetails.get(key);
            if (isAgentAssigned) detail.incrementAssign();
            if (timeDiff <= timeDiffFlag) detail.incrementTime();
            detail.incrementNormal();
        }
    }
}