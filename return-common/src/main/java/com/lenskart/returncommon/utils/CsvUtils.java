package com.lenskart.returncommon.utils;

import com.opencsv.CSVWriter;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

public class CsvUtils {
    public static <T> void exportCsv(String fileName, HttpServletResponse response, List<T> respList, Class<T> reqClass) throws CsvRequiredFieldEmptyException, CsvDataTypeMismatchException, IOException {
        response.setContentType("text/csv");
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + fileName + "\"");
        try {
            com.opencsv.bean.StatefulBeanToCsv<T> writer = new StatefulBeanToCsvBuilder<T>(response.getWriter())
                    .withQuotechar(CSVWriter.DEFAULT_QUOTE_CHARACTER)
                    .withSeparator(CSVWriter.DEFAULT_SEPARATOR)
                    .withOrderedResults(false)
                    .build();

            writer.write(respList);
        }catch (Exception e){
            throw new RuntimeException("Exception while Exporting csv file");
        }
    }
}
