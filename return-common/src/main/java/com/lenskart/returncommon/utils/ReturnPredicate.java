package com.lenskart.returncommon.utils;

import com.lenskart.returncommon.model.enums.ReturnStatus;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;

public class ReturnPredicate {
    private static final List<String> DRAFTED_REJECTED_RETURN_STATUS = new ArrayList<String>(Arrays.asList(ReturnStatus.RETURN_ACCEPTED.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus(),
            ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus(), ReturnStatus.RETURN_EXPECTED_POS.getStatus(),
            ReturnStatus.RETURN_EXPECTED_WH.getStatus()));
    private static final List<String> RPU_CYCLE_STATUS = new ArrayList<String>(Arrays.asList(ReturnStatus.NEW_REVERSE_PICKUP.getStatus(), ReturnStatus.AWB_ASSIGNED.getStatus(),
            ReturnStatus.REFERENCE_ID_ISSUED.getStatus()));
    private static final List<String> REJECTED_OR_CANCELLED_OR_RESHIP_RETURN_STATUS = new ArrayList<String>(Arrays.asList(ReturnStatus.CANCELLED.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus(),
            ReturnStatus.RETURN_REJECTED.getStatus(), ReturnStatus.CUSTOMER_CANCELLED.getStatus(), ReturnStatus.RETURN_RESHIP.getStatus(), ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus()));
    public static final Predicate<String> IS_RETURN_IN_DRAFTED_REJECTED_STATE = d-> StringUtils.isNotBlank(d) && DRAFTED_REJECTED_RETURN_STATUS.contains(d);
    public static final Predicate<String> IS_RETURN_REJECTED_OR_CANCELLED_OR_RESHIP = d-> StringUtils.isNotBlank(d) && REJECTED_OR_CANCELLED_OR_RESHIP_RETURN_STATUS.contains(d);
    public static final Predicate<String> IS_RETURN_NOT_REJECTED_AND_CANCELLED_AND_RESHIP = IS_RETURN_REJECTED_OR_CANCELLED_OR_RESHIP.negate();
    private static final Predicate<String> IS_RETURN_IN_RPU_CYCLE = d-> StringUtils.isNotBlank(d) && RPU_CYCLE_STATUS.contains(d);
    public static final Predicate<String> IS_RETURN_NOT_IN_RPU_CYCLE = IS_RETURN_IN_RPU_CYCLE.negate();
}
