package com.lenskart.returncommon.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;

@Slf4j
public class DateUtil {

    public static String dayName(String inputDate, String format){
        Date date = null;
        try {
            date = new SimpleDateFormat(format).parse(inputDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return new SimpleDateFormat("EEEE", Locale.ENGLISH).format(date);
    }

    //in 08:40 am format
    public static String getCurrentTime(){
        String formattedTime = null;
        try{
            ZoneId zoneId = ZoneId.of("Asia/Kolkata");
            LocalTime localTime=LocalTime.now(zoneId);
            System.out.println(localTime);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("hh:mm a");
            formattedTime=localTime.format(formatter);
            log.info("[getCurrentTime] current time : "+formattedTime);
        }catch (Exception e){
            log.error("[getCurrentTime] Exception occurred : ",e);
        }
        return formattedTime;
    }

    public static String getCurrentDateTime(String pattern){
        if (pattern == null || pattern.isEmpty()) {
            throw new IllegalArgumentException("Pattern must not be null or empty");
        }

        // Get the current date and time
        LocalDateTime now = LocalDateTime.now();

        // Format the current date and time using the provided pattern
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return now.format(formatter);
    }

    //in 25 Oct 2018 format
    public static String getCurrentDate(){
        String formattedDate = null;
        try {
            ZoneId zoneId = ZoneId.of("Asia/Kolkata");
            LocalDate localDate= LocalDate.now(zoneId);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM yyyy");
            formattedDate = localDate.format(formatter);
            log.info("[getCurrentDate] Current day : " + formattedDate);
        }catch (Exception e){
            log.error("[getCurrentDate] Exception occurred :",e);
        }
        return formattedDate;
    }
}
