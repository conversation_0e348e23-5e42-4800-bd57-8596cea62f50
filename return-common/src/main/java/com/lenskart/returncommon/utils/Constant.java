package com.lenskart.returncommon.utils;

import com.lenskart.returncommon.model.enums.RefundMethod;
import com.lenskart.returncommon.model.enums.ReturnStatus;

import java.util.*;

public interface Constant {
    String BLANK = "";
    String NULL = null;
    String DIRECT_RECEIVING = "WAREHOUSE";
    String TEMPLATE_ID = "template_id";
    String COMM_TEMPLATE_CACHE_NAME = "commTemplateCache";
    String SYSTEM_PREFERENCE_CACHE_NAME = "systemPreferenceCacheName";
    String OLD_RETURN = "Old_Return";
    String D365_FINANCE_CONSUMER_RETURN_TOPIC = "D365-ReturnOrder";
    String RETURN_CREATE_COURIER_SERVICE_QUEUE = "return_create_courier_service_queue_new";
    String FAILURE = "Failure";
    String INVALID_RETURN_ID = "Invalid return id.";
    String REQUEST_IS_NULL = "request is null";
    String SUCCESS = "success";
    String MESSAGE = "message";
    String DEFAULT_CLICKPOST_RESPONSE = "Default clickpost response";
    String RETURN_DETAILS_UPDATED_SUCCESSFULLY = "Return details updated successfully.";
    String IND_PHONE_CODE = "+91";
    String INSURANCE = "insurance";
    String DELIGHT_ACTION_REJECT = "REJECT";
    String DELIGHT_ACTION_FAILED = "delight_action_failed";
    String DELIGHT_ACTION_REQUEST_VALIDATION_FAILED="validation_failed";
    String DELIGHT_ACTION_PUSHED_QUEUE_EXCEPTION = "delight_action_pushed_queue_exception";
    String DELIGHT_ACTION_RETURN_STATUS_UPDATE_FAILED_EXCEPTION = "delight_action_return_status_failed_exception";
    String L_STORE ="LStore";
    String BACKWARD_FLOW = "BACKWARD_FLOW";
    String EXCHANGE_ONHOLD_TO_PROCESSING = "Order is moved from exchange_on_hold to processing";
    List<String> RETURN_ITEM_STATUS = new ArrayList<String>(Arrays.asList(STATUS.UNICOM_STOCKIN, STATUS.RETURN_RECEIVED));
    String REVERSE_PICKUP_ALREADY_EXISTS = "ALREADY_EXISTS";
    String ALREADY_EXIST = "alreadyExists";
    String REVERSE_PICKUP_CODE ="reversePickupCode";
    String RETURN_ID = "returnId";
    List<String> eventStatusList = new ArrayList<>(Arrays.asList(ReturnStatus.NEW_REVERSE_PICKUP.getStatus(),ReturnStatus.REFERENCE_ID_ISSUED.getStatus(),ReturnStatus.AWB_ASSIGNED.getStatus(),ReturnStatus.RETURN_RECEIVED.getStatus(),ReturnStatus.CANCELLED.getStatus(),ReturnStatus.CUSTOMER_CANCELLED.getStatus(),ReturnStatus.RETURN_FOLLOWED_UP.getStatus(),ReturnStatus.RETURN_UNDER_FOLLOWUP.getStatus(),ReturnStatus.RETURN_NEED_APPROVAL.getStatus()));
    String NEW_RETURN_FLOW = "isReturnFromNewFlow";
    String PREFIX_CASHFREE = "Ref #";
    String PREFIX_LKCASH_OR_LKCASHPLUS = "Ref #";
    String PREFIX_ARN ="Bank Ref #";
    String PREFIX_SC = "Code ";
    String RETURN_COMMUNICATION_TOPIC = "return-communication";
    String ORDER_TYPE = "Returned order";
    String BULK = "BULK";
    String INTRANSIT = "_Intransit";
    String LK = "LK";
    String DK = "DK";
    String FOFOB2B = "FOFOB2B";
    String LEGAL_ENTITY_LENSKART = "LKIN";
    String LEGAL_ENTITY_DEALSKART = "DKIN";
    String GENDER = "Male";
    String GROUP = "B2C Domestic";
    String MODE_OF_DELIVERY = "Air";
    String DATE_FORMAT = "yyyy-MM-dd";
    String COCOBulk = "COCOBulk";
    String WEB_PREFIX = "Web";
    String DIRECT_DELIVERY = "Direct Delivery";
    String STOCK = "Stock";
    String SOURCING_VENDOR = "LKIN";
    String RETURN_ORDER = "return_order";
    String NA = "NA";
    String ITEM_QTY = "1";
    String FOFO_IDENTIFIER = "fofo";
    String NEW_ARCHITECTURE_FLAG = "d365.new-architecture.enabled";
    String CES = "CES";
    String RETURN_PACKING_SLIP = "RETURN_PACKING_SLIP";
    String RETURN_SALE_ORDER = "RETURN_SALE_ORDER";
    String RETURN_GIFT_CARD = "RETURN_GIFT_CARD";
    String UW_ITEM_ID = "UW_ITEM_ID";
    String RETURN = "RETURN";
    String TYPE = "type";
    String CONTRACT = "contract";
    String RETURN_PACKING_SLIP_TYPE = "returnPackingSlip";
    String RETURN_SALE_ORDER_HEADER = "salesOrderHeader";
    String RETURN_SALE_ORDER_TYPE = "returnSalesOrder";
    String ORDER_NOT_FOUND_EXCEPTION_MESSAGE = "ORDER_NOT_FOUND";

    interface UwOrderAttribute_AttributeName {
        String WARRANTY_PERIOD = "warrantyPeriod";
    }

    interface Country {
        String IN = "IN";
        String US = "US";
        String SG = "SG";
        String AE = "AE";
    }

    interface RETURN_STATUS {
        String NEW_REVERSE_PICKUP = "new_reverse_pickup";
        String SC_REF_REC_PEND = "sc_refunded_receiving_pending";
        String RETURN_REFUNDED_RECEIVING_PENDING = "return_refunded_receiving_pending";
        String RETURN_REFUNDED = "return_refunded";
        String REFERENCE_ID_ISSUED = "reference_id_issued";
        String AWB_ASSIGNED = "awb_assigned";
        String AUTO_SYNC_FAILED = "auto_sync_failed";
        String RETURN_RECEIVED_ACTION_PENDING = "return_received_action_pending";
        String RETURN_REJECTED = "return_rejected";
        String RETURN_CANCELLED = "cancelled";
        String PICKUP_REGISTRATION_FAILED = "pickup_regd_failed";
    }

    interface REFUND_INTENT {
        String EXCHANGE = "Exchange";
        String REFUND = "Refund";
    }

    public interface RETURN_ORDER_STATUS {
        String RETURN_RECEIVED = "return_received";
        String RETURN_UNDER_FOLLOWUP = "return_under_followup";
        String RETURN_FOLLOWED_UP = "return_followed_up";
        String RETURN_OVERRIDE = "return_override";
        String RETURN_PENDING_APPROVAL = "return_pending_approval";
        String RETURN_REFUNDED_RECEIVING_PENDING = "return_refunded_receiving_pending";
        String RETURN_REFUNDED = "return_refunded";
        String RETURN_RESHIP = "return_reship";
        String NEW_REVERSE_PICKUP = "new_reverse_pickup";
        String REFERENCE_ID_ISSUED = "reference_id_issued";
        String AWB_ASSIGNED = "awb_assigned";
        String CANCELLED = "cancelled";
        String RETURN_CLOSED = "return_closed";
        String SC_REFUNDED_RECEIVING_PENDING = "sc_refunded_receiving_pending";
        String RTO_COD_STOCKIN = "rto_cod_stockin";
        String VENDOR_SYNC_PENDING = "vendor_sync_pending";
        String RETURN_REFUND_REJECTED = "return_refund_rejected";
        String AUTO_SYNC_FAILED = "auto_sync_failed";
        String CUSTOMER_CANCELLED = "customer_cancelled";
        String RETURN_EXCHANGE = "return_exchange";
        String SELFDISPATCHED = "selfdispatched";
        String RETURN_CLOSED_TBYB = "return_closed_tbyb";
        String EASY_REFUND_GIVEN_PICKUP_CANCELLED = "easy_refund_given_pickup_cancelled";
        String PARTIAL_EASY_REFUND_GIVEN_PICKUP_CANCELLED = "partial_easy_refund_given_pickup_cancelled";
        String RETURN_ACCEPTED = "return_accepted";
    }



    interface PRODUCT_DELIVERY_TYPE {
        String OTC = "OTC";
        String B2B = "B2B";
        String DTC = "DTC";
    }

    interface RETURN_METHOD {
        String RPU = "RPU";
        String SHIP_TO_LENSKRT = "ShiptoLenskrt";
        String RETURN_TO_STORE = "returntoNearbyStore";
        String ENFORCED_RETURN_AT_STORE = "enforceRefundAtStore";
    }

    interface SYSTEM_PREFERENCE_GROUPS {
        String EASY_REFUND_ELIGIBILITY = "easy_refund_eligibility";
        String FACILITY_CODE_VALUE = "facility_code_value";
        String MODULE_BASE_VALUE = "module_base_value";
        String DELIVERY_DISPATCH_INFORMATION = "deliverydispatch_information";
        String NON_BRANDS = "non_brands";
        String NEW_ORDERS_FLOW_DISTRIBUTOR = "new_orders_flow_distributor";
        String BYPASS_UNICOM_VALIDATION = "By_Passunicom_Validation";
        String NEXS_ORDER_CRITERIA = "nexs_order_criteria";
        String NEXS_FACILITIES = "nexs_facilities";
        String FLAT_TAX_RATE_GROUP = "flat_tax_rate_group";
        String RETURN_REFUND_ELIGIBILITY = "return_refund_eligibility";

        String REFUND = "refund";
        String ALLOWED_REFUND_METHODS = "ALLOWED_REFUND_METHODS_FOR_PAYMENT_METHODS";
        String SMS = "sms";
        String PAYMENT_MODE_REFUND_COMPLETE_DAYS = "payment_mode_refund_complete_days";
        String NEED_APPROVAL = "Need_Approval";
        String RETURNS = "returns";
        String COMMUNICATION_MIGRATION = "communication_migration";
        String Lk_Cash_Service_Group = "lk_3orfree_service";
        String D365_RETURNS = "d365_return";
        String BRAND_CATEGORY = "brand_category";
        String LLM_MODEL_PROMPTS = "llm_model_prompts";
        String D365 = "D365";
        String D365_RETURN = "d365_return";
        String E_INVOICE = "e_invoice";
        String MANESAR_FACILITIES = "manesar_facilities";
        String SBRT = "SBRT";
        String D365_Country_List = "d365_country_list";
    }

    interface SYSTEM_PREFERENCE_TYPES {
        String LIST = "LIST";
        String INTEGER = "Integer";
        String BOOLEAN = "BOOLEAN";
    }

    interface SYSTEM_PREFERENCE_KEYS {
        String EASY_REFUND_PAYMENT_METHODS = "payment_methods";
        String EASY_REFUND_PRICE_LIMIT = "price_limit";
        String EASY_REFUND_BLOCKED_REVERSE_PINCODES = "blocked_reverse_pincodes";
        String EASY_REFUND_BRANDS = "brands";
        String DEALSKART_FACILITY_CODE = "dealskart_facility";
        String MODULE_QUOTIENT = "module_quotient";
        String MODULE_DIVISOR = "module_divisor";
        String LENSKART_FACILITY_CODE = "lenskart_facility";
        String PRIVATE_BRANDS = "private_brands";
        String TESTEMAIL_VALIDATE_FLAG = "TESTEMAIL_VALIDATE_FLAG";
        String LK_COUNTRY = "lk_Country";
        String NEXS_FAC_PRODUCT_ID = "nexs_fac_product_id";
        String NEXS_FAC_EMAIL_ID = "nexs_fac_email_id";
        String NEXS_FAC_PIN_CODE = "nexs_fac_pin_code";
        String NEXS_FAC_PAY_METHOD = "nexs_fac_pay_method";
        String NEXS_FAC_SHIP_COUNTRY = "nexs_fac_ship_country";
        String NEXS_FACILITIES = "nexs_facilities";
        String MANESAR_FACILITIES = "manesar_facilities";
        String D365_Country_List = "d365_country_list";
        String IS_SBRT_WEB_ENABLED = "IS_SBRT_WEB_ENABLED";
        String RETURN_ID_AWB_KEY = "return_id_awb_key";
        String RETURN_ID_REF_ID_ENABLED_KEY = "return_id_ref_id_enabled_key";
        String RETURN_STATUS_AWB_ASSIGNED_GROUP = "return_status_awb_assigned_group";
        String RETURN_STATUS_REF_ENABLED_GROUP = "return_status_ref_id_enabled_group";
        String PRODUCT_ID_INSURANCE = "product_id_insurance";
        String PRODUCT_INSURANCE = "product_insurance";
        String NEXS_MAX_ORDER = "nexs_order_limit";
        String FLAT_TAX_RATE_KEY = "flat_tax_rate_key";
        String REFUND_ON_APPROVAL = "disable_web_refund_for_needApproval";
        String OFFLINE_PAYMENT_METHODS = "offline_payment_methods";
        String REFUND_CHANGES_APPLICABLE_DATE = "refund_changes_applicable_date";
        String CANCELLATION_REFUND_TEMPLATE = "cancellation_refund_confirmation_sms_template";
        String ENABLE_BRIDGE_V4_FLAG = "enable_bridge_v4_flag";
        String V4_ENABLED_TEMPLATES = "v4_enabled_templates";
        String CANCELLATION_REFUND_MASTER_TEMPLATE = "cancellation_refund_confirmation_master_template";
        String WAREHOUSE_APPROVAL_THRESHOLD_DAYS = "warehouse_approval_required_days";
        String BRANDED_PRODUCT = "branded_product";
        String NON_BRANDED = "non_branded";
        String IS_NEED_APPROVAL_AUTO_WARRANTY_ENABLE="IS_NEED_APPROVAL_AUTO_WARRANTY_ENABLE";
        String ORDER_RETURN_SMS = "OrderReturnSms";
        String RETURN_FILTER_CONFIG = "RETURN_FILTER_CONFIG";
        String STORE_APPOINTMENT_DAYS = "store_appointment_days";
        String STORE_APPOINTMENT = "store_appointment";
        String CHECK_BOGOGO = "CHECK_BOGOGO";
        String MAX_DISCOUNT_PERCENTAGE = "max_discount_percentage";
        String Lk_Cash_Duration_Key = "lk_cash_duration_month";
        String Lk_Cash_Exchange_Days = "lk_cash_exchange_days";
        String IS_PROCESS_D365_OLD_RECORDS = "IS_PROCESS_D365_OLD_RECORDS";
        String D365RetryOldData_Retry = "d365_retry_flag";
        String D365RetryOldData_Pslip_Retry = "d365_pslip_retry_job_flag";
        String D365_RECORDS_PER_EXECUTION = "d365_records_per_execution";
        String D365_DATE_FOR_OLD_DATA = "d365_date_for_old_data";
        String D365_FINANCIAL_YEAR = "d365_financial_year";
        String D365_BLOCKED_RETURN_STATUSES = "d365_blocked_return_statuses";
        String D365_Flags = "d365_flags";
    }

    interface Lk_3OrFree {
        String MAX_REFUNDED_AMOUNT = "max_refunded_amount";
        String TIMESTAMP = "23:59:59";
        String START_TIMESTAMP = "00:00:00";
    }

    interface PAYMENT_METHOD {
        String STORECREDIT = "storecredit";
        String PURCHASEORDER = "purchaseorder";
        String CASHONDELIVERY = "cashondelivery";
        String COD = "cod";
        String COD_MODE = "COD";
        String LKWALLET = "lenskartwallet";
        String FRANCHISECREDIT = "franchisecredit";
        String PAYU = "payu_shared";
        String CITRUS = "citrus";
        String SEAMLESS = "seamless";
        String CCAVENUE = "ccavenue";
        String MOBIKWIK = "mobikwik";
        String SIMPL = "simpl";
        String NEFT = "neft";
        String CASHFREE = "cashfree";
        String EXCHANGE = "exchange";
        String SOURCE = "source";
        String LK_CASH = "LKCash";
        String LK_CASH_PLUS = "LKCashPlus";
        String giftvoucher = "giftvoucher";
    }

    interface B2B_NAV_CHANNEL {
        final Set<String> B2B_NAV_CHANNELS = new HashSet<>(Arrays.asList("FOFOB2B", "WebB2B", "HECB2B", "COCOB2B", "JJOnlineB2B", "B2B"));
    }

    public interface REFUND_STATUS {
        String PENDING = "pending";
        String PROCESSING = "processing";
        String AWAITING = "awaiting";
        String PROCESSED = "processed";
        String REJECTED = "rejected";
        String AWAITING_NEFT_INFO = "awaiting_neft_info";
        String REFUND_COMPLETE = "refund_complete";
        String REFUND_PENDING = "refund_pending";
        String REFUND_ARN_RECEIVED = "refund_arn_received";
    }

    public interface RETURN_SOURCE {
        String POS = "pos";
        String ONLINE = "online";
        String ONLINE_WEB = "web";
        String ONLINE_VSM = "vsm";
        String DIRECT = "direct";
        String VSM = "vsm";
        String WEB = "web";
        String WAREHOUSE = "warehouse";
    }

    interface ORDER_STATE_OR_STATUS {
        String HOLDED = "holded";
        String CANCELED = "canceled";
        String CLOSED = "closed";
        String COMPLETE = "complete";
        String COMPLETE_SHIPPED = "complete_shipped";
        String RTO = "rto";
        String NEW = "new";
        String PENDING = "pending";
        String PROCESSING_BLACKLIST = "processing_blacklist";
        String PROCESSING_NEW = "processing_new";
        String PROCESSING = "processing";
        String PROCESSING_RED_FLAG = "processing_red_flag";
        String PROCESSING_POWERFOLLOWUP_CONFIRM = "processing_powerfollowup_confirm";
        String PROCESSING_POWER_FOLLOWUP = "processing_power_followup";
        String PFU_WRONG_POWER_PACKAGE = "pfu_wrong_power_package";
        String PFU_INCOMPATIBLE_POWERS = "pfu_incompatible_powers";
        String PROCESSING_POWER_FOLLOWUP_HTO = "processing_power_followup_hto";
        String PROCESSING_POWER_FOLLOWUP_VERIFY = "processing_power_followup_verify";
        String PROCESSING_LENS_ONLY_PENDING = "processing_lens_only_pending";
        String ORDER_UNHOLD = "Order Unhold";
        String ORDER_NOT_CONFIRMED = "order_not_confirmed";
        String PREDELIVERY_REFUND = "predelivery_refund";
        String PROCESSING_NOT_IN_STOCK = "processing_not_in_stock";
        String INITIATED_REVERESE = "initiated_reverse";
        String DELIVERED = "delivered";
        String PREDELIVERY_CANCEL = "predelivery_cancellation";
        String ORDER_CANCELED = "ORDER_CANCELED";
        String EXCHANGE_ON_HOLD = "exchange_on_hold";
        String EXCHANGE_CANCELLED_SO_CANCELLING_RETURN = "exchange cancelled so cancelling return as well";
        String RPU_CANCELLED_FROM_VSM = "RPU CANCELLED FROM VSM";
        String MARK_ORDER_RETURN = "MarkOrderReturn";
        String MARK_ORDER_RTO = "MarkOrderRto";
        String CANCELING_RPU_FOR_REASSIGNING_COURIER ="cancelling RPU for reassigning courier";
    }

     interface REFUND_METHOD {
        String STORECREDIT = "storecredit";
        String OFFLINE = "offline";
        String ONLINE = "online";
        String NEFT = "neft";
        String SOURCE = "source";
        String CASH = "cash";
        String LKWALLET = "lkwallet";
        String CASHFREE = "cashfree";
        String JUSPAY = "juspay";
        String CUSTOMER_WALLET = "customer_wallet";
        String OFFLINECARD = "offlinecard";
        String OFFLINECASH = "offlinecash";
        String CARD = "card";
        String HOOLAH = "hoolah";
        String FAVEPAY = "favepay";
        String ATOME = "atome";
        String GRABPAY = "grabpay";
        String MEDNEFITS = "mednefits";
        String NTUCLINKPLUS = "ntuclinkplus";
        String ADYEN = "adyen";

        String EXCHANGE = "EXCHANGE";

        static List<String> getOnlineRefundMethods() {
            return Arrays.asList(RefundMethod.JUSPAY.getName(), RefundMethod.PAYU.getName(), RefundMethod.EZETAPCARD.getName(), RefundMethod.PHONEPE.getName(), RefundMethod.PAYTM.getName());
        }


    }

    interface STORE_ID {
        Byte JJONLINE_STOREID = 4;
        Integer JOHNJACOBS_STOREID = 6;
        Integer LKSINGAPORE_STOREID = 8;
        Integer ODONLINE_STOREID = 2;
    }

    interface CLASSIFICATION {
        Integer SUNGLASS = 11357;
    }

    interface FIELDEZ_STATUS{
        List<String> DISPENSING_DONE_STATUSES= Arrays.asList("Dispensed_without_Replacement","Dispensed_with_Replacement");
    }

    interface ADDRESS_TYPE {

        String SHIPPING = "shipping";
        String BILLING = "billing";
        String RETURN = "return";
    }

    interface COURIER {

        String LKART = "LKart";
        String SELF_COURIER = "Self courier";
        List<Integer> CLASSIFICATIONS_FOR_DISPENSING = new ArrayList<Integer>(Arrays.asList(11355, 11356));
        List<Integer> REASON_IDS_FOR_DISPENSING = new ArrayList<Integer>(Arrays.asList());        //TODO: add values once tushar corrects the reasons list
        List<Integer> REASON_IDS_TO_DISABLE_DISPENSING = new ArrayList<Integer>(Arrays.asList());        //TODO: add values once tushar corrects the reasons list
        String BLUEDART = "bluedart";
        String XPRESSBEES = "xpressbees";
        String CARRIER_CODE = "carrier_code";
        String TRACKING_NO = "tracking_no";

    }

    interface EVENT {
        String COURIER_ALLOCATED = "COURIER_ALLOCATED";
        String COURIER_REALLOCATED = "COURIER_REALLOCATED";
        String COURIER_ASSIGNED = "COURIER_ASSIGNED";
        String COURIER_REASSIGNED = "COURIER_REASSIGNED";
        String RETURN_INITIATED = "RETURN_INITIATED";
        String RETURN_CANCELLED = "RETURN_CANCELLED";
        String RETURN_RECEIVED = "RETURN_RECEIVED";
        String RETURN_REFUNDED = "RETURN_REFUNDED";
        String RETURN_ACCEPTED = "RETURN_ACCEPTED";
        String RETURN_REQUEST_CREATED = "RETURN_REQUEST_CREATED";
        String RTO_TRACKING_NO_RECEIVED_AT_WAREHOUSE = "RTO_TRACKING_NO_RECEIVED_AT_WAREHOUSE";
        String RETURN_TRACKING_NO_RECEIVED_AT_WAREHOUSE = "RETURN_TRACKING_NO_RECEIVED_AT_WAREHOUSE";
        String RETURN_RECEIVED_AT_WAREHOUSE = "RETURN_RECEIVED_AT_WAREHOUSE";
        String D365_RETURN_ORDER_SYNCED = "D365_RETURN_ORDER_SYNC_TRIGGERED";
        String RETURN_INITIATION_SMS = "RETURN_INITIATED_SMS_SENT";
        String RETURN_INITIATION_TO_STORE_SMS = "RETURN_INITIATION_TO_STORE_SMS";
        String EXCHANGE_CREATION = "EXCHANGE_CREATION";
        String HOME_PICKUP_EMAIL = "HOME_PICKUP_EMAIL_SENT";
        String RULE_ENGINE_RESPONSE_EMAIL = "RULE_ENGINE_RESPONSE_EMAIL_SENT";
        String RULE_ENGINE_RESPONSE_SMS = "RULE_ENGINE_RESPONSE_SMS_SENT";
        String DISPENSING_TEAM_ASSIGNED_EMAIL = "DISPENSING_TEAM_ASSIGNED_EMAIL_SENT";
        String DISPENSING_TEAM_ASSIGNED_SMS = "DISPENSING_TEAM_ASSIGNED_SMS_SENT";
        String SELF_COURIER_WAREHOUSE_RETURN_SMS = "SELF_COURIER_WAREHOUSE_RETURN_SMS_SENT";
        String REFUND_TRIGGERED = "REFUND_TRIGGERED";
        String RETURN_ORDER_ITEM_NOT_CANCELLABLE ="RETURN_ORDER_ITEM_NOT_CANCELLABLE";

        String CANCELLATION_FAILED_AT_CLICKPOST = "CANCELLATION_FAILED_AT_CLICKPOST";
        String CLICKPOST_CANCELLED_STATUS = "350";
        String CANCEL_EXCHANGE_ON_RETURN_CANCEL = "CANCEL_EXCHANGE_ON_RETURN_CANCEL";
        String REFUND_REJECT_ON_EXCHANGE_CANCELLATION = "REFUND_REJECT_ON_EXCHANGE_CANCELLATION";
        String PUSHED_TO_INSURANCE_UPDATE_CLAIM_QUEUE = "PUSHED_TO_INSURANCE_CLAIM_QUEUE";
        String PUSHED_TO_INSURANCE_QUEUE = "PUSHED_TO_INSURANCE_QUEUE";
        String PUSHED_TO_BACKSYNC_QUEUE = "PUSHED_TO_BACKSYNC_QUEUE";

        String EXCHANGE_ORDER_CREATED = "EXCHANGE_ORDER_CREATED";
        String EXCHANGE_ORDER_REFUND = "EXCHANGE_ORDER_REFUND";
        String EXCHANGE_ORDER_UNHOLDED = "EXCHANGE_ORDER_UNHOLDED";
        String PUSH_TO_CHECK_REFUND_DISPATCH_POINT = "PUSH_TO_CHECK_REFUND_DISPATCH_POINT";
        String PUSHED_UW_ORDERS_STATUS_UPDATE_QUEUE = "PUSHED_UW_ORDERS_STATUS_UPDATE_QUEUE";
        String RETURN_NEED_APPROVAL = "RETURN_NEED_APPROVAL";

        String DELIGHT_NEED_APPROVAL = "DELIGHT_NEED_APPROVAL";
        String RETURN_NEED_APPROVAL_EMAIL ="RETURN_NEED_APPROVAL_EMAIL";
        String RETURN_STORE_RECEIVING = "RETURN_STORE_RECEIVING";
        String RETURN_SUCCESS_MESSAGE_POS = "RETURN_SUCCESS_MESSAGE_POS";

        String AWAITED_RTO_REQUEST_CREATED = "AWAITED_RTO_REQUEST_CREATED";
        String RETURN_EINVOICE_SYNC_TRIGGERED = "RETURN_EINVOICE_SYNC_TRIGGERED";
        String ENFORCED_RETURN_AT_STORE = "ENFORCED_RETURN_AT_STORE";
        String RETURN_QUALITY_CHECK_DONE = "RETURN_QUALITY_CHECK_DONE";
        String REFUND_REQUEST_CREATED = "REFUND_REQUEST_CREATED";
        String FRAME_BROKEN_APPROVAL = "FRAME_BROKEN_APPROVAL";
        String LOCAL_FIT_FBR_APPROVAL = "LOCAL_FIT_FBR_APPROVAL";

    }

    interface QC_STATUS {
        String QC_PASS = "Pass";
        String QC_FAIL = "Fail";
        String qcStatusGood="GOOD";
        String qcStatusBad="BAD";
    }

    interface RETURN_TOPICS {
        String RETURN_EVENT_TOPIC = "return_event_queue";
        String D365_Return_Sync = "D365_Return_Sync";
        String D365_Return_Invoice_Sync = "D365_Return_Invoice_Sync";
        String RETURN_REFUND = "return_refund_queue";
        String EXCHANGE_ORDER_CREATION_TOPIC = "Exchange_Order_Creation_Queue";
        String RETURN_COMMUNICATION = "Return_Communication";
        String Insurance_Update_Claim_Request_Queue = "insurance-update-claim-request-queue";
        String D365_FINANCE_CONSUMER_RETURN_TOPIC = "D365-ReturnOrder";
        String UW_ORDERS_STATUS_UPDATE = "uw_orders_status_update";
        String STATUS_UPDATE = "status_update";
        String RETURN_TRACKING_EVENT_QUEUE = "Return_tracking_event_queue";
        String EXCHANGE_ORDER_CREATION_QUEUE = "exchange_order_queue";
        String EXCHANGE_ORDER_UNHOLD_QUEUE = "exchange_order_unhold_queue";
        String ORDER_STATUS_HISTORY_QUEUE = "order_status_history_queue";
        String SYNC_UNICOM_STATUS_QUEUE = "Sync_unicom_status_queue";
        String D365_FINANCE_CONSUMER_RETURN_EINVOICE_TOPIC = "return-einvoicing";
        String ORDER_SYNC_QUEUE = "order_sync_queue";
        String BACK_SYNC_STATUS_QUEUE = "back_sync_status_queue";
        String MARK_DISPENSING_QUEUE = "mark_dispensing_queue";
        String ORDER_COMMENT_QUEUE = "order_comments_queue";
        String IVR_RESPONSE_QUEUE = "ivr-customer-feedback";
        String REFUND_INITIATION_ON_RECEIVING_QUEUE = "refund_initiate_on_receiving_queue";
        String CREATE_REFUND_REQUEST_QUEUE = "create-refund-request-queue";
        String COSMOS_EVENT_QUEUE = "cosmos-event-queue";
        String INVOICE_CREATE_REQUEST = "invoice-create-request";
        String FL_SALE_ORDER_RETURN = "fl_sale_order_return";
        String FL_PACKAGE_SLIP = "fl_package_slip";
        String FL_GIFT_CARD = "fl_gift_card_reversal";
    }
    interface RETURN_TYPE {
        String RTO = "rto";
        String STORE = "store";
        String REVERSE = "reverse";
        String AWAITED_RTO = "awaited_rto";
    }

    interface REASONS{
        public static final String NOT_AVAILABLE = "NA";
    }

    interface STATUS {
        String RETURN_RECEIVED = "return_received";
        String RETURN_UNDER_FOLLOWUP = "return_under_followup";
        String INITIATED_RETURN_RESHIP = "initiated_return_reship";
        String INITIATED_STOCKIN = "initiated_stockin";
        String UNICOM_STOCKIN = "unicom_stockin";
        String CANCELLED = "cancelled";
        String RETURN_FOLLOWED_UP = "return_followed_up";
        String RETURN_OVERRIDE = "return_override";
        String RETURN_PENDING_APPROVAL = "return_pending_approval";
        String RETURN_REFUNDED = "return_refunded";
        String RETURN_RESHIP = "return_reship";
        String NEW_REVERSE_PICKUP = "new_reverse_pickup";
        String REFERENCE_ID_ISSUED = "reference_id_issued";
        String AWB_ASSIGNED = "awb_assigned";
        String RETURN_CLOSED = "return_closed";
        String SC_REFUNDED_RECEIVING_PENDING = "sc_refunded_receiving_pending";
        String RTO_COD_STOCKIN = "rto_cod_stockin";
        String VENDOR_SYNC_PENDING = "vendor_sync_pending";
        String RETURN_REFUND_REJECTED = "return_refund_rejected";
        String AUTO_SYNC_FAILED = "auto_sync_failed";
        String CUSTOMER_CANCELLED = "customer_cancelled";
        String RETURN_EXCHANGE = "return_exchange";
        String SELFDISPATCHED = "selfdispatched";
        String RETURN_CLOSED_TBYB = "return_closed_tbyb";
        String EASY_REFUND_GIVEN_PICKUP_CANCELLED = "easy_refund_given_pickup_cancelled";
        String PARTIAL_EASY_REFUND_GIVEN_PICKUP_CANCELLED = "partial_easy_refund_given_pickup_cancelled";
    }

    interface HUB_DETAILS{
        String HUB_SEARCH_BY_PINCODE = "pincode";
        String HUB_SEARCH_BY_HUB_ID = "hub";
    }

    interface RETURN_DAYS{
        Integer RETURN_CREATE_DATE_CUT_OFF_DAYS = -30;
        Integer RETURN_CREATE_DATE_CUT_OFF_DAYS_FALLBACK = -20;
    }

    interface JUNO_PRODUCT {
        String ID = "productId";
        String NAME = "productName";
        String CLASSIFICATION = "classification";
        String IMAGE = "productImage";
        String URL = "productUrl";
        String COLOR_MODEL_NAME = "modelName";
        String TOP_COLOR = "topColor";
        String BOTTOM_COLOR = "bottomColor";
        String RIGHT_COLOR = "rightColor";
        String LEFT_COLOR = "leftColor";
        String COLOR_OPTIONS = "colorOptions";
        String COLOR_NAME = "colorName";
        String SPECIAL_ORDER_FLAG = "splOrderFlag";
        String FULFILLABLE = "fulfillable";
        String IMAGE_URLS = "imageUrls";
        String MODEL_NAME = "modelName";
        String FRAME_MATERIAL ="frameMateriaL";
        String FRAME_TYPE = "frameType";
        String PRODUCT_BRAND = "productBrand";
        String ITEM_CATEGORY = "itemCategory";
        String RESULT = "result";
        String STATUS = "status";
        String IMAGE_SHORT_URLS = "imageShortUrls";
        String HTTP_SHORT_IMAGE_URL = "httpShortImageUrl";
        String HTTPS_SHORT_IMAGE_URL = "httpsShortImageUrl";
        String HTTP_LONG_IMAGE_URL = "httpLongImageUrl";
        String HTTPS_LONG_IMAGE_URL = "httpsLongImageUrl";

        String refund_initiation_on_master_greater_than_exchange = "refund_initiation_on_master_greater_than_exchange";

    }

    interface VSM_COMMENTS{
        String BLACKLIST_CUSTOMER_FLAG_TRUE="Blacklist customer flag was true";
        String BLACKLIST_CUSTOMER_FLAG_RULE="Blacklist_customer_flag rule matched";
        String BRANDED_PRODUCT="It is a branded product";
        String BRANDED_PRODUCT_RULE="Branded_product flag rule matched";
        String REFUND_DISPATCH_REACHED="Refund dispatch point reached";
        String REFUND_INTENT_MATCHED="Refund intent matched as per qc rule engine";
        String CUSTOMER_PROFILING_SCORE_HIGH="Customer profiling score is more than the permissible value";
        String CUSTOMER_PROFILING_SCORE_LOW="Customer profiling score is less than the permissible value";
        String CUSTOMER_RETURN_PERCENTAGE_HIGH="Customer return percentage is high";
        String CUSTOMER_RETURN_COUNT_HIGH="Customer return count is high";
        String ITEM_PRICE_HIGH="Item value is more than the permissible value";
        String ITEM_PRICE_LOW="Item value is less than the permissible value";
        String DELIVERY_DATE_GAP_HIGH="Delivered and current date gap is more than the permissible value";
        String DELIVERY_DATE_GAP_LOW="Delivered and current date gap is less than the permissible value";
    }

    interface RULE_TYPE{
        String SMART_QC="smart_qc";
        String WARRANTY_RULE="warranty_rule";
    }


    interface Dispensing {
        public interface LEAD_EXT_STATUS {
            String OPEN = "OPEN";
            String CLOSED_FIELDEZ = "CLOSED_FIELDEZ";
            String CLOSED_BOTH = "CLOSED_BOTH";
        }
        public interface MARK_ATTENDED{
            String YES="YES";
            String NO = "NO";
        }
        public interface FIELDEZ_STATUS{
            List<String> DISPENSING_DONE_STATUSES= Arrays.asList("Dispensed_without_Replacement","Dispensed_with_Replacement");
        }
    }

    interface SYNC_STATUS{
        String CLOSED = "closed";
        String BLANK = "";
    }

    interface TRACKING_STATUS {
        String FITTING_DONE = "FITTING_DONE";
        String PACKED = "PACKED";
        String PICKED = "PICKED";
        String IN_WAREHOUSE = "IN_WAREHOUSE";
        String COATING_TC = "COATING_TC";
        String SURFACING = "SURFACING";
        String FITTING = "FITTING";
        String DISPATCHED = "DISPATCHED";
        String DELIVERED = "DELIVERED";
        String CANCELLED = "CANCELLED";
        String RETURN_REQUESTED = "RETURN_REQUESTED";
        String RETURN_TO_ORIGIN = "RETURN_TO_ORIGIN";
        String EXCHANGE_CREATED = "EXCHANGE_CREATED";
        String SENT_TO_LOCAL_LAB = "SENT_TO_LOCAL_LAB";
        String DISPATCHED_TO_STORE = "DISPATCHED_TO_STORE";
        String RECEIVED_AT_STORE = "RECEIVED_AT_STORE";
        String DELIVERED_AT_STORE = "DELIVERED_AT_STORE";
        String HANDED_OVER_TO_CUSTOMER = "HANDED_OVER_TO_CUSTOMER";
        String RETURN_RESHIPPED = "RETURN_RESHIPPED";
        String RETURNED = "RETURNED";
        String RETURN_RECEIVED = "RETURN_RECEIVED";
        String RETURN_CANCELLED = "RETURN_CANCELLED";
        String RETURN_REFUNDED = "RETURN_REFUNDED";
        String RETURN_NEED_APPROVAL_ACCEPTED = "RETURN_NEED_APPROVAL_ACCEPTED";
        String REFUND_COMPLETED="REFUND_COMPLETED";

        String AWAITED_RTO = "awaited_rto";
        String REFUND_INITIATED="REFUND_INITIATED";
        String ARN_RECEIVED="ARN_RECEIVED";

        String OUT_FOR_DELIVERY = "OUT_FOR_DELIVERY";

        String DELIVERY_ATTEMPT_FAILED = "DELIVERY_MISSED";
        String IN_TRANSIT = "IN_TRANSIT";
        String PICKUP_DONE = "PICKUP_DONE";
        String RETURN_NEED_APPROVAL_PENDING = "RETURN_NEED_APPROVAL_PENDING";

    }

    interface IDENTIFIER_TYPE {
        String ORDER_ID = "ORDER_ID";
        String RETURN_ID = "RETURN_ID";
        String UWITEM_ID = "UW_ITEM_ID";
        String SHIPMENT_ID = "SHIPMENT_ID";
        String MAGENTO_ITEM_ID = "MAGENTO_ITEM_ID";
        String INCREMENT_ID = "INCREMENT_ID";
        String GROUP_ID = "GROUP_ID";

    }

    interface REFUND_QUEUE_CONSTANTS {
        String REFUND_PROCESS_TOPIC = "refund-process-queue";
        String PROCESS_REFUND_ON_RETURN_INITIATION = "refund_process_on_return_initiation_queue";
        String REFUND_CREATE_STRATEGY = "REFUND_CREATE_STRATEGY";
        String DUAL_RETURN_RECEIVED_STRATEGY = "DUAL_RETURN_RECEIVED_STRATEGY";
    }

    interface JSON_CONSTANT {
        String SUCCESSFUL = "successful";
        String TRUE = "true";
        String RESPONSE = "response";
    }

    interface CHANNEL {
        String B2B = "B2B";
        String CUSTOM = "CUSTOM";
        String JJONLINE = "JJONLINE";
        String FRANCHISEBULK = "FranchiseBulk";
        String AQUALENS = "AQUA";
        String MARKETPLACE = "MPDTC";
    }

    public interface RECEIVING_FLAG {
        String YES = "yes";
        String NO = "no";
    }

    interface RefundEligibilityError {
        String THE_ORDER_IS_NOT_YET_MARKED_DELIVERED = "The order is not yet marked delivered.";
        String THE_ORDER_IS_COD = "The order is COD";
        String THIS_ORDER_HAS_ALREADY_BEEN_CANCELLED = "This order has already been cancelled";
        String FRAME_BROKEN_ORDER_NOT_ALLOWED = "Frame broken item approved for GV is not allowed";
        String FRAME_BROKEN_GV_ORDER_NOT_ALLOWED = "Frame broken GV order is not allowed";
        String THIS_ORDER_IS_NOT_YET_CONFIRMED = "This order is not yet confirmed. Please check the invoice";
        String THIS_ITEM_HAS_ALREADY_BEEN_CANCELLED_REFUNDED = "This item has already been cancelLed and refunded.";
        String THIS_ITEM_HAS_ALREADY_BEEN_CANCELLED = "This item has already been canceled.";
        String ACTIVE_RETURN_AGAINST_ITEM = "There’s an active return against this item.";
        String ACTIVE_RETURN_ALREADY_APPROVED_BY_CS = "There’s an active return against this item, which has already been approved by the CS team.";
        String ACTIVE_RETURN_WAITING_FOR_APPROVAL = "There’s an active return against this item, currently waiting for CS team approval.";
        String ITEM_IS_NOT_RETURNABLE_FOR_FRAUD_CUSTOMER = "Item is not returnable as the customer is flagged as fraudulent.";
        String ITEM_IS_NOT_REFUNDABLE_FOR_FRAUD_CUSTOMER = "Item is not refundable as the customer is flagged as fraudulent.";
        String ITEM_IS_NOT_RETURNABLE_AS_PER_SYSTEM_POLICY = "Item is not returnable as per the system's return policy.";


        String ORDER_NOT_COMPLETE_STATE = "The order is not marked completed in the system. Please check the dispact status and invoicing";
        String ORDER_NOT_CLOSED_STATE = "The order is not in closed state";
        String ORDER_NOT_DELIVERED = "The order is not yet marked delivered";
        String ITEM_NOT_EXCHANGEABLE_REFUND_AMOUNT_GREATER_THAN_ZERO = "Exchangeable not allowed because refund already processed through fast refund.";
        String ITEM_NOT_EXCHANGEABLE_REFUND_LESS_THAN_ZERO = "Exchangeable not allowed because refund already processed through fast refund.";
        String ITEM_NOT_REFUNDABLE_REFUND_LESS_THAN_ZERO = "Refund not allowed because refund already processed through fast refund.";
        String ITEM_NOT_REFUNDABLE_RETURN_CYCLE_CLOSED = "Refund cannot be initiated as return cycle is closed with return status ";
        String ITEM_NOT_REFUNDABLE_AS_NO_VALID_REFUND_METHOD_FOUND = "Refund cannot be initiated there is no valid refund method ";
        String ITEM_NOT_EXCHANGEABLE_RETURN_CYCLE_CLOSED = "Exchange cannot be initiated as return cycle is closed with return status ";
        String ITEM_NOT_REFUNDABLE_GIFT_VOUCHER = "Item not refundable as payment through gift voucher.";
        public static final String ITEM_NOT_EXCHANGEABLE_ON_RTO_SHIPMENTS= "Exchange is not allowed on RTO shipments.";
        String ORDER_NOT_IN_DELIVERED_CLOSED_COMPLETE_STATE = "The order is not in delivered, closed or complete state as its current state is ";
        String ITEM_NOT_EXCHANGEABLE_EXCHANGE_ALREADY_CREATED = "Item not exchangeable as exchange already created.";
        String ITEM_NOT_REFUNDABLE_EXCHANGE_ALREADY_CREATED = "Item not refundable as exchange already created.";
        String NOT_EXCHANGEABLE_PARENT_ALREADY_REFUNDED = "Order not exchangeable as parent order already refunded.";
        String ITEM_NOT_EXCHANGEABLE_AS_REFUND_METHOD_NOT_GIVEN = "Item not exchangeable as refund method not given as exchange in return rules";
        String ITEM_NOT_REFUNDABLE_AS_REFUND_METHOD_NOT_GIVEN = "Item not refundable as refund method not mentioned in return rules.";
        String ITEM_NOT_REFUNDABLE_AS_ORDER_IS_BOGOGO = "Item is not refundable as the order falls under the BOGOGO promotion.";
        String DISABLED_WEB_REFUND_FOR_NEED_APPROVAL = "Refund is disabled for web return.";
        String DISABLED_WEB_RETURN_FOR_NEED_APPROVAL = "Exchange is disabled for web return.";


    }

    interface RULE_ENGINE_STATUS{
        String COURIER_PICKUP="CourierPickup";
        String AWB_ASSIGNED="awb_assigned";
        String RETURN_RECEIVED="return_received";
        String POS_RECEIVING="POSReceiving";
        String WH_RECEIVING="WHReceiving";
        String  RETURN_RECEIVED_ACTION_PENDING="return_received_action_pending";
        String ANY_RECEIVING_POINT ="AnyReceivingPoint";
    }

    public interface SYNC_TO_UNICOM {
        String YES = "yes";
        String NO = "no";
    }

    interface CANCEL_ORDER_TYPE{
        String PARTIAL_CANCEL = "partial_cancel";
        String CANCEL = "cancel";
    }

    public interface IVRCustomerFeedbackConstant {

        String FAILED = "FAILED";
        String SUCCESS = "SUCCESS";
        String PREFERRED_DATE = "preferred_date";
        String COMMENT = "comment";
        String PHONE_NUMBER = "phone_number";
        String ISSUE_ID = "issue_id";
        String PICK_UP_DATE = "pickup_date";
        String KEY = "key";
        String AWB = "awb";
        String USERNAME = "username";
        String PICKUP_DATE_FORMAT = "yyyy-MM-dd";
        String CANCELLED = "cancelled";

    }

    public interface REFUND_RULES_CONSTANT{
        String WEBDTC = "WebDTC";
        String RETURN_INITIATION = "ReturnInitiation";
        String WEB = "web";
        String EYE_FRAME = "eyeframe";
        String ANY_REASON = "anyReason";
        String JUS_PAY = "juspay";
        String NONE = "NA";
        double DEFAULT_AMOUNT = 3000d;
        Integer ONE = 1;

    }

    HashMap<Integer, String> revrseMappedStatusMapping = new HashMap<Integer, String>() {
        private static final long serialVersionUID = 1L;
        {
            put(1	, "Out For Pickup");
            put(2	, "Pickup Done");
            put(3	, "Out For Delivery");
            put(4	, "Failed Delivery");
            put(5	, "Return Delivered to Warehouse");
            put(6	, "RTO");
            put(9	, "Pickup Scheduled");
            put(10	, "Order Cancelled");
            put(11	, "Return Request placed");
            put(12	, "RTO-Delivered");
            put(14	, "Exchange Pickup");
            put(15	, "Exchange Delivered");
            put(16	, "Pickup Failed");
            put(17	, "Shipment Stuck");
            put(18	, "SLA breached");
            put(19	, "Lost");
            put(20	, "Damaged");
        }
    };

}
