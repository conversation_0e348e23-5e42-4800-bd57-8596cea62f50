package com.lenskart.returncommon.utils;

import com.lenskart.returncommon.model.dto.insurance.ExternalOpticalInsuranceClaimDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
public class SymboUtil {

    @Value("${insurance.hub.service.base.url}")
    private String insuranceHubServiceBaseUrl;

    @Autowired
    RestTemplate restTemplate;

    public ExternalOpticalInsuranceClaimDetailResponse getInsuranceClaimDetails(Integer incrementId){
        ExternalOpticalInsuranceClaimDetailResponse externalOpticalInsuranceClaimDetailResponse=null;
        log.info("[SymboUtil][getInsuranceClaimDetails]getInsurance claim details for{} :: ",incrementId);
        String insuranceDetailsUrl = insuranceHubServiceBaseUrl + "v1/insurance/"+incrementId+"/claim";
        log.info("insuranceDetailsUrl :: "+insuranceDetailsUrl);

        try {
            externalOpticalInsuranceClaimDetailResponse = restTemplate.getForObject(insuranceDetailsUrl, ExternalOpticalInsuranceClaimDetailResponse.class);
            log.info("[SymboUtil][getInsuranceClaimDetails] insurance details api response: "+externalOpticalInsuranceClaimDetailResponse);
        }
        catch(Exception e){
            log.error("[SymboUtil][getInsuranceClaimDetails] Exception while calling insurance hub service get insurance details",e);
        }
        return externalOpticalInsuranceClaimDetailResponse;
    }
}
