package com.lenskart.returncommon.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.Date;

public class UnixTimestampToDateDeserializer extends JsonDeserializer<Date> {
    private static final long IST_OFFSET_MS = 5 * 60 * 60 * 1000 + 30 * 60 * 1000;

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext context) throws IOException {
        // Adjust timestamp by subtracting the IST offset
        if (jsonParser.getCurrentToken().isNumeric()) {
            long timestampMillis = jsonParser.getLongValue(); // Handle Unix timestamp
            return new Date(timestampMillis - IST_OFFSET_MS);
        } else if (jsonParser.getCurrentToken() == JsonToken.VALUE_STRING) {
            String dateStr = jsonParser.getText().trim();
            try {
                // Attempt to parse it as an ISO 8601 formatted date string
                return Date.from(Instant.parse(dateStr));
            } catch (DateTimeParseException e) {
                // Handle the exception or return null if it's not a valid date string
                return null;
            }
        }
        return null; // Default return if neither numeric nor string
    }
}
