package com.lenskart.returncommon.utils;

import com.lenskart.returncommon.model.dto.insurance.ExternalOpticalInsuranceClaimDetailResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class SymboUtilTest {

    @InjectMocks
    private SymboUtil symboUtil;

    @Mock
    private RestTemplate restTemplate;

    @Value("${insurance.hub.service.base.url}")
    private String insuranceHubServiceBaseUrl;

    @Test
    public void testGetInsuranceClaimDetails_Success() {
        Integer incrementId = 12345;
        ExternalOpticalInsuranceClaimDetailResponse mockResponse = new ExternalOpticalInsuranceClaimDetailResponse();

        when(restTemplate.getForObject(anyString(), eq(ExternalOpticalInsuranceClaimDetailResponse.class))).thenReturn(mockResponse);

        ExternalOpticalInsuranceClaimDetailResponse response = symboUtil.getInsuranceClaimDetails(incrementId);

        assertNotNull(response);
        verify(restTemplate, times(1)).getForObject(anyString(), eq(ExternalOpticalInsuranceClaimDetailResponse.class));
    }

    @Test
    public void testGetInsuranceClaimDetails_Exception() {
        Integer incrementId = 12345;

        when(restTemplate.getForObject(anyString(), eq(ExternalOpticalInsuranceClaimDetailResponse.class))).thenThrow(new RuntimeException("Exception occurred"));

        ExternalOpticalInsuranceClaimDetailResponse response = symboUtil.getInsuranceClaimDetails(incrementId);

        assertNull(response);
        verify(restTemplate, times(1)).getForObject(anyString(), eq(ExternalOpticalInsuranceClaimDetailResponse.class));
    }
}