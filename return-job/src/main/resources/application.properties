#============================================================================
# Configure Main Scheduler Properties
#============================================================================

quartz.org.quartz.scheduler.instanceName = MyClusteredScheduler
quartz.org.quartz.scheduler.instanceId = AUTO

#============================================================================
# Configure ThreadPool
#============================================================================

quartz.org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
quartz.org.quartz.threadPool.threadCount = 25
quartz.org.quartz.threadPool.threadPriority = 5

#============================================================================
# Configure JobStore
#============================================================================

quartz.properties.org.quartz.jobStore.misfireThreshold = 60000

quartz.properties.org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
quartz.properties.org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
quartz.properties.org.quartz.jobStore.useProperties = false
quartz.properties.org.quartz.jobStore.dataSource = quartzDataSource
quartz.properties.org.quartz.jobStore.tablePrefix = HEADLESS_RETURNS_QRTZ_

quartz.properties.org.quartz.jobStore.isClustered = true
quartz.properties.org.quartz.jobStore.clusterCheckinInterval = 20000

#============================================================================
# Configure Datasources
#============================================================================

quartz.properties.org.quartz.dataSource.quartzDataSource.driver = com.mysql.cj.jdbc.Driver
quartz.properties.org.quartz.dataSource.quartzDataSource.URL = ******************************************************************************************
quartz.properties.org.quartz.dataSource.quartzDataSource.user = root
quartz.properties.org.quartz.dataSource.quartzDataSource.password = Root@123456
quartz.properties.org.quartz.dataSource.quartzDataSource.maxConnections = 5
quartz.properties.org.quartz.dataSource.quartzDataSource.validationQuery = select 0 from dual
quartz.properties.org.quartz.dataSource.quartzDataSource.provider=hikaricp
quartz.properties.org.quartz.scheduler.instanceId=AUTO
#logging.level=DEBUG
#logging.level.org.hibernate=DEBUG

org.quartz.dataSource.quartzDataSource.driver = com.mysql.cj.jdbc.Driver
org.quartz.dataSource.quartzDataSource.URL = ******************************************************************************************
org.quartz.dataSource.quartzDataSource.user = root
org.quartz.dataSource.quartzDataSource.password = Root@123456
org.quartz.dataSource.quartzDataSource.maxConnections = 5
org.quartz.dataSource.quartzDataSource.validationQuery = select 0 from dual
org.quartz.dataSource.quartzDataSource.provider=hikaricp

org.quartz.jobStore.isClustered = true