CREATE TABLE `fl_return_tracking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `return_id` int(11) NOT NULL,
  `uw_item_id` int(11) NOT NULL,
  `identifier_type` VARCHAR(50) DEFAULT NULL,
  `status` VARCHAR(50) DEFAULT NULL,
  `status_remarks` varchar(500) DEFAULT NULL,
  `retry_count` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `return_id` (`return_id`),
  KEY `uw_item_id` (`uw_item_id`),
  KEY `created_at` (`created_at`),
  KEY `identifier_type` (`identifier_type`),
  KEY `idx_return_id_status_retry` (`return_id`,`status`,`retry_count`),
  KEY `idx_cmp1` (`status`,`retry_count`,`created_at`)
);