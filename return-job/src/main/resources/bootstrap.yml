spring:
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
    consul:
      host: ${consul-server:localhost}
      port: ${consul-port:8500}
      config:
        enabled: true
        prefix: ${CONFIG_PREFIX:scm-config}
        name: return-job,preprod-k8s
        format: YAML
        data-key: v1
        acl-token: ${acl-token:dummyToken}
        profileSeparator: ','
        defaultContext: ${spring.application.name:return-job},${profile:prod-k8s}
        watch:
          enabled: false
        metadata:
          appName: return-job
          serverPort: ${server.port}
      discovery:
        enabled: false
      service-registry:
        enabled: false
    vault:
      enabled: ${VAULT_ENABLED:false}
      uri: ${VAULT_URI:http://vault.infra.svc.cluster.local:8200}
      authentication: ${VAULT_AUTHENTICATION:KUBERNETES}
      kubernetes:
        role: ${VAULT_ROLE:vault-ces-config-return-job-auth-role}
        kubernetes-path: ${VAULT_KUBERNETES-PATH:kubernetes}
        service-account-token-file: ${VAULT_SERVICE-ACCOUNT-TOKEN-FILE:/var/run/secrets/kubernetes.io/serviceaccount/token}
      kv:
        backend: ${VAULT_BACKEND:lenskart/ces/ces-config}
        application-name: ${VAULT_DEFAULT_CONTEXT:return-job}
        default-context: ${VAULT_DEFAULT_CONTEXT:return-job}
        enabled: true
        profiles: ''
management:
  health:
    vault:
      enabled: false