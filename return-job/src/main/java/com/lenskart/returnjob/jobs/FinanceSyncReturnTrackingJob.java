package com.lenskart.returnjob.jobs;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.lenskart.ccautils.dto.enums.EventStatus;
import com.lenskart.returncommon.model.enums.FinanceReturnEvent;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returnrepository.entity.FinanceSyncReturnTrackingEvent;
import com.lenskart.returnrepository.repository.FinanceSyncReturnTrackingRepository;
import com.lenskart.returnservice.service.IKafkaService;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class FinanceSyncReturnTrackingJob {

    private final FinanceSyncReturnTrackingRepository financeSyncReturnTrackingEvent;
    
    @Value("${fl.retry.count:5}")
    private Integer maxRetryCount;

    @Value("${fl.retry.limit:1000}")
    private Integer recordsLimit;

    @Value("${fl.retry.timeInterval:10}")
    private Integer timeInterval;

    @Value("${fl.retry.updateBatchLimit:200}")
    private int updateBatchLimit;

    @Value("${fl.saleOrderReturn.topic:fl_sale_order_return}")
    private String saleOrderReturnTopic;

    @Value("${fl.packageSlip.topic:fl_package_slip}")
    private String pSlipTopic;

    @Value("${fl.giftCardReversal.topic:fl_gift_card_reversal}")
    private String giftCardReversalTopic;

    @Autowired
    private IKafkaService kafkaService;

    private Map<String, String> topicMap = new HashMap<>();

    @PostConstruct
    void init() {
        topicMap = Map.of(
            FinanceReturnEvent.RETURN_SALE_ORDER.name(), saleOrderReturnTopic,
            FinanceReturnEvent.RETURN_PACKING_SLIP.name(), pSlipTopic,
            FinanceReturnEvent.RETURN_GIFT_CARD.name(), giftCardReversalTopic
        );
    }

    @Scheduled(initialDelayString = "${fl.retry.initial.delay:10000}", fixedDelayString = "${fl.retry.fixed.delay:600000}")
    public void execute() {
    
        List<FinanceSyncReturnTrackingEvent> updateStatusBatch = Collections.synchronizedList(new ArrayList<>());
        List<ReturnCreateRequest> retryBatch = Collections.synchronizedList(new ArrayList<>());
    
        List<FinanceSyncReturnTrackingEvent> failedEvents = financeSyncReturnTrackingEvent.findFinanceSyncReturnTrackingEventsByStatusFailedAndRetryCountLessThan(maxRetryCount, timeInterval, recordsLimit);
    
        failedEvents.parallelStream().forEach(eventLog -> {
            try {
                processEventLog(eventLog, updateStatusBatch, retryBatch);
            } catch (Exception e) {
                log.error("Failed to process record log id: {} error: {}",eventLog.getId(), e.getMessage());
            }
        });
    
        if (!updateStatusBatch.isEmpty() && !retryBatch.isEmpty()) {
            pushToQueueAndUpdateStatus(updateStatusBatch, retryBatch);
        }
    
        log.info("[FinanceSyncReturnTrackingEvent] retryJob executed. Total failed events count: {}", failedEvents.size());
    }
    
    private void processEventLog(FinanceSyncReturnTrackingEvent eventLog, List<FinanceSyncReturnTrackingEvent> updateStatusBatch, List<ReturnCreateRequest> retryBatch) throws Exception {
    
        String queue = topicMap.get(eventLog.getIdentifierType().toUpperCase());
        
        ReturnCreateRequest retryRequest = new ReturnCreateRequest();
        retryRequest.setPackageSlip(eventLog.getIdentifierType().equalsIgnoreCase(FinanceReturnEvent.RETURN_PACKING_SLIP.name()) ? true : false);
        retryRequest.setUwItemId(eventLog.getUwItemId());
        retryRequest.setRetryCount(eventLog.getRetryCount()+1);
        retryRequest.setQueue(queue);
    
        eventLog.setStatus(EventStatus.RETRIED.toString());
    
        synchronized (updateStatusBatch) {
            updateStatusBatch.add(eventLog);
            if (updateStatusBatch.size() >= updateBatchLimit) {
                pushToQueueAndUpdateStatus(updateStatusBatch, retryBatch);
            }
        }
    
        synchronized (retryBatch) {
            retryBatch.add(retryRequest);
        }
    }
    
    private void pushToQueueAndUpdateStatus(List<FinanceSyncReturnTrackingEvent> updateStatusBatch, List<ReturnCreateRequest> retryBatch) {

        retryBatch.forEach(data->{
            kafkaService.pushToKafka(data.getQueue(), String.valueOf(data.getUwItemId()), data);
        });
        financeSyncReturnTrackingEvent.saveAll(updateStatusBatch);
        updateStatusBatch.clear();
        retryBatch.clear();
    }

}
