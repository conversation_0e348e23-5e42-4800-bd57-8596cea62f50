package com.lenskart.returnjob.jobs;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.D365ReturnRequest;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.D365ReturnTracking;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.ID365ReturnTrackingService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.ISystemPreferenceService;
import com.lenskart.returnservice.service.impl.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class D365RetryOldDataJob {
    private final ISystemPreferenceService systemPreferenceService;
    private final DateUtil dateUtil;
    private final ID365ReturnTrackingService d365ReturnTrackingService;
    private final IReturnOrderActionService returnOrderActionService;
    private final OrderOpsFeignClient orderOpsFeignClient;
    @Value("${d365.return.kafka.topic:D365-ReturnOrder}")
    private String d365ReturnTopic;
    @Value("${d365.return.pslip.kafka.topic:D365-Return-Pslip-Retry}")
    private String d365ReturnPslipTopic;
    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Scheduled(cron = "${d365.retry.scheduler.timestamp.new:0 0/20 * * * *}")
    public void execute() throws Exception {
        log.info("D365RetryOldData : Attempting to execute job");
        SystemPreference systemPreference = systemPreferenceService.findOneByGroupAndKey(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.IS_PROCESS_D365_OLD_RECORDS);
        SystemPreference d365FlagsValues = systemPreferenceService.findOneByGroupAndKey(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.D365_Flags);
        List<Integer> d365Flags = Arrays.asList(0,2);
        if(Objects.nonNull(d365FlagsValues)){
            d365Flags = Stream.of(d365FlagsValues.getValue().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        log.info("[D365RetryOldData] d365Flag list is "+d365Flags);
        List<String> blockD365SyncStatus = systemPreferenceService.getValuesAsList(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.D365_BLOCKED_RETURN_STATUSES);
        String newFromDate = systemPreferenceService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.D365_FINANCIAL_YEAR,30, TimeUnit.MINUTES);
        String newToDate = systemPreferenceService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.D365_DATE_FOR_OLD_DATA, 30, TimeUnit.MINUTES);//"2023-06-30 00:00:00";
        String retryFlag = systemPreferenceService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.D365RetryOldData_Retry, 30, TimeUnit.MINUTES);
        String pSlipRetryFlag = systemPreferenceService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.D365RetryOldData_Pslip_Retry, 30, TimeUnit.MINUTES);
        int recordsPerExecution = 150;
        String sysPrefRecordsNum = systemPreferenceService.getSystemPreferenceValues(Constant.SYSTEM_PREFERENCE_GROUPS.D365_RETURNS, Constant.SYSTEM_PREFERENCE_KEYS.D365_RECORDS_PER_EXECUTION, 30, TimeUnit.MINUTES);
        if (sysPrefRecordsNum != null) {
            recordsPerExecution = Integer.parseInt(sysPrefRecordsNum);
        }

        log.info("D365RetryOldData : pSlipRetryFlag : {}, newFromDate: {}, newToDate: {}, retryFlag: {}, record: {} blockD365SyncStatus: {}",pSlipRetryFlag, newFromDate, newToDate, retryFlag, sysPrefRecordsNum, blockD365SyncStatus);
        Date startDate = null;
        Date endDate = null;
        if (retryFlag.equalsIgnoreCase("1")) {
            try {
                log.info("D365RetryOldData : Processing retry for d365 return orders");
                startDate = sdf1.parse(dateUtil.getCurrentDateTimeForLog());
                List<D365ReturnTracking> d365ReturnTrackingList = d365ReturnTrackingService.findDataForReturnRetryForOldData(newFromDate, newToDate, recordsPerExecution, d365Flags);
                log.info("D365RetryOldData : d365ReturnTrackingList size in return retry : {}",d365ReturnTrackingList.size());
                if (!CollectionUtils.isEmpty(d365ReturnTrackingList)) {
                    for (D365ReturnTracking d365ReturnTracking:d365ReturnTrackingList) {
                        try {
                            if (d365ReturnTracking.getRetryCount() < 4 ) {
                                String returnStatus = returnOrderActionService.getReturnOrderStatusById(d365ReturnTracking.getReturnId());
                                if(blockD365SyncStatus.stream().anyMatch(status -> status.equals(returnStatus))){
                                    log.info("D365RetryOldData blocked for this return status {}", returnStatus);
                                }
                                else {
                                    ReturnDetailItem returnDetailItem = returnOrderActionService.findTopByNewReturnId(d365ReturnTracking.getReturnId());
                                    D365ReturnRequest returnRequestMessage = new D365ReturnRequest();
                                    returnRequestMessage.setRowId(d365ReturnTracking.getId());
                                    returnRequestMessage.setUwItemId(returnDetailItem.getUwItemId());
                                    returnRequestMessage.setReturnId(d365ReturnTracking.getReturnId());
                                    returnRequestMessage.setRetryFlag(true);
                                    try {
                                        log.info("D365RetryOldData : pushing data to kafka for returnId for d365 return : {} topic {}", d365ReturnTracking.getReturnId(), d365ReturnTopic);
                                        orderOpsFeignClient.pushData(objectMapper.convertValue(returnRequestMessage, Map.class), d365ReturnTopic);
                                    } catch (Exception e) {
                                        log.error("D365RetryOldData : exception found to push data into kafka for return retry : ", e);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("D365RetryOldData : exception found to process data for return retry : ",e);
                        }
                    }
                }
                endDate = sdf1.parse(dateUtil.getCurrentDateTimeForLog());
                int totalTimeTakenByForReturn = (int) ((endDate.getTime() - startDate.getTime()) / (1000 * 60));
                log.info("D365RetryOldData : Total time taken for d365 return in minutes : {}", totalTimeTakenByForReturn);

            } catch (Exception e) {
                log.error("D365RetryOldData : exception found to get retry data for d365 returns : ",e);
            }

            // PackingSlip retry
            if (pSlipRetryFlag.equalsIgnoreCase("1")) {
                try {
                    log.info("D365PslipRetryOldData : Processing pslip retry for d365 return packingSlip");
                    List<D365ReturnTracking> d365ReturnTrackingList = d365ReturnTrackingService.findDataForPSlipRetryForOldData(newFromDate, newToDate, recordsPerExecution,d365Flags);
                    log.info("D365PslipRetryOldData : d365ReturnTrackingList size for pslipRetry : {}",d365ReturnTrackingList.size());
                    if (!CollectionUtils.isEmpty(d365ReturnTrackingList)) {
                        startDate = sdf1.parse(dateUtil.getCurrentDateTimeForLog());
                        for (D365ReturnTracking d365ReturnTracking : d365ReturnTrackingList) {
                            log.info("processForPSlipRetryNew : Processing for returnId for pslipRetry : {}", d365ReturnTracking.getReturnId());
                            try {
                                if (d365ReturnTracking.getPSlipRetryCount() < 4) {
                                    String returnStatus = returnOrderActionService.getReturnOrderStatusById(d365ReturnTracking.getReturnId());
                                    if(blockD365SyncStatus.stream().anyMatch(status -> status.equals(returnStatus))){
                                        log.info("D365RetryOldData PackingSlip retry blocked for this return status {}", returnStatus);
                                    }
                                    else{
                                        ReturnDetailItem returnDetailItem = returnOrderActionService.findTopByNewReturnId(d365ReturnTracking.getReturnId());
                                        D365ReturnRequest returnPSlipRequest = new D365ReturnRequest();
                                        returnPSlipRequest.setUwItemId(returnDetailItem.getUwItemId());
                                        returnPSlipRequest.setReturnId(d365ReturnTracking.getReturnId());
                                        returnPSlipRequest.setRowId(d365ReturnTracking.getId());
                                        returnPSlipRequest.setRetryFlag(true);
                                        log.info("D365PslipRetryOldData : returnPSlipRetryRequest prepared : {}", returnPSlipRequest);
                                        try {
                                            log.info("D365PslipRetryOldData : pushing data to kafka for returnId for PSlipRetry : {} topic {}", d365ReturnTracking.getReturnId(), d365ReturnPslipTopic);
                                            orderOpsFeignClient.pushData(objectMapper.convertValue(returnPSlipRequest, Map.class), d365ReturnPslipTopic);
                                            log.info("D365PslipRetryOldData : PSlip retry message pushed successfully to kafka");
                                        } catch (Exception e) {
                                            log.error("D365PslipRetryOldData : exception found to push data into kafka for pslipRetry: ", e);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.error("D365PslipRetryOldData : unable to process data for pslipRetry: ", e);
                            }
                        }
                    }
                    endDate = sdf1.parse(dateUtil.getCurrentDateTimeForLog());
                    int totalTimeTakenByForPSlip = (int) ((endDate.getTime() - startDate.getTime()) / (1000 * 60));
                    log.info("D365PslipRetryOldData : Total time taken for d365 PSlip in minutes : {}", totalTimeTakenByForPSlip);
                } catch (Exception e) {
                    log.error("D365PslipRetryOldData : exception found to get retry data for d365 returns pslip : ",e);
                }
            }
        } else {
            log.info("D365RetryOldData : Scheduler run value is false as of now !!");
        }
    }
}