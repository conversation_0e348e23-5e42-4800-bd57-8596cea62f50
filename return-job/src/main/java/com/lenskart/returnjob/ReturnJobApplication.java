package com.lenskart.returnjob;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication( exclude = { RedisAutoConfiguration.class } )
@ComponentScan(basePackages = "com.lenskart")
@EnableScheduling
@EnableFeignClients(basePackages = "com.lenskart")
@Slf4j
public class ReturnJobApplication {

	@Value("${APPLICATION_NAME:default}")
	String applicationName;

	public static void main(String[] args) {
		SpringApplication.run(ReturnJobApplication.class, args);
	}

	@PostConstruct
	private void postConstruct() {
		log.info("Vault::APPLICATION_NAME: " + applicationName);
	}
}
