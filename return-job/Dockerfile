# build
FROM public.ecr.aws/z5y1f1y8/maven:3.8.7-amazoncorretto-17 as builder

ARG TARGETPLATFORM
ARG BUILDPLATFORM
RUN echo "Building on $BUILDPLATFORM, for $TARGETPLATFORM"

COPY ./pom.xml /return-job/
WORKDIR /return-job/
COPY ./.mvn /return-job/.mvn
RUN aws s3 cp s3://deployment.lenskartprod.internal/settings.xml /root/.m2/

RUN aws s3 cp s3://deployment.lenskartprod.internal/newrelic-java-latest.zip .
RUN unzip newrelic-java-latest.zip

COPY . .
RUN --mount=type=cache,target=/root/.m2 mvn clean install -DskipTests --settings .mvn/custom-settings.xml

# run
FROM public.ecr.aws/z5y1f1y8/maven:3.8.7-amazoncorretto-17

RUN mkdir /return-job

COPY --from=builder /return-job/target/*.jar /return-job/
COPY --from=builder /return-job/newrelic/ /return-job/newrelic/
RUN chown -R 1000 /return-job && chmod 777 return-job

EXPOSE 8080
WORKDIR /return-job/
ENTRYPOINT ["sh", "-c", "java -Djava.security.egd=file:/dev/./urandom -javaagent:/return-job/newrelic/newrelic.jar -Dserver.port=8080 -Dspring.profiles.active=$PROFILE $JVM_ARGS -Duser.timezone=Asia/Kolkata -jar /return-job/*.jar"]
