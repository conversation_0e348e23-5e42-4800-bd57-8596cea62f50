<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <servers>
        <server>
            <id>nexs-releases</id>
            <username>kalyan</username>
            <password>lenskart@12</password>
        </server>
        <server>
            <id>internal</id>
            <username>kalyan</username>
            <password>lenskart@12</password>
        </server>
        <server>
            <id>snapshots</id>
            <username>kalyan</username>
            <password>lenskart@12</password>
        </server>
    </servers>

    <mirrors>
        <mirror>
            <id>archiva.default</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal/</url>
            <mirrorOf>*,!nexs-releases</mirrorOf>
        </mirror>
        <mirror>
            <id>nexs-releases-mirror</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
            <mirrorOf>nexs-releases</mirrorOf>
        </mirror>
    </mirrors>

    <profiles>
        <profile>
            <id>archiva-repositories</id>
            <repositories>
                <repository>
                    <id>nexs-releases</id>
                    <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>internal</id>
                    <url>http://archiva-new.prod.internal:8080/repository/internal/</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <activeProfiles>
        <activeProfile>archiva-repositories</activeProfile>
    </activeProfiles>
</settings>