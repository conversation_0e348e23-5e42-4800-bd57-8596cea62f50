package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returnservice.feignclient.POSFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.support.Acknowledgment;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class ReturnApprovalStatusMessageConsumerTest {
    @Mock
    private POSFeignClient posFeignClient;

    @Mock
    private IKafkaService kafkaService;

    @Mock
    private Acknowledgment acknowledgment;

    @InjectMocks
    private ReturnApprovalStatusMessageConsumer consumer;

    private ObjectMapper mapper = new ObjectMapper();

    private final String validMessage = "{ \"orderId\": \"123\", \"status\": \"APPROVED\", \"retryCount\": 0 }";
    private ApprovalStatusRequest approvalStatusRequest;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        approvalStatusRequest = mapper.readValue(validMessage, ApprovalStatusRequest.class);
    }


    @Test
    void testConsume_InvalidMessage_NotAcknowledged() throws Exception {
        String invalidMessage = "";
        consumer.consume(invalidMessage, acknowledgment);

        verify(posFeignClient, never()).updateReturnApprovalStatus(any(), anyString(), anyString(), anyString());
        verify(acknowledgment, never()).acknowledge();
    }

    @Test
    void testProcessApprovalStatusRequest_Retry() throws Exception {
        when(posFeignClient.updateReturnApprovalStatus(any(), anyString(), anyString(), anyString()))
                .thenReturn(new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR));

        consumer.processApprovalStatusRequest(approvalStatusRequest);

        verify(kafkaService, times(1)).pushToKafka(anyString(), anyString(), anyString());
        assertEquals(1, approvalStatusRequest.getRetryCount());
    }

    @Test
    void testRetryApprovalStatusUpdate_MaxRetriesReached() throws Exception {
        approvalStatusRequest.setRetryCount(3);

        consumer.retryApprovalStatusUpdate(approvalStatusRequest);

        verify(kafkaService, never()).pushToKafka(anyString(), anyString(), anyString());
    }

}