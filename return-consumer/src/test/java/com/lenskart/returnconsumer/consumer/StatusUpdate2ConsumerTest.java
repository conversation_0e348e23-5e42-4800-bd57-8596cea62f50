package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class StatusUpdate2ConsumerTest {
    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private Acknowledgment acknowledgment;

    @InjectMocks
    private StatusUpdate2Consumer statusUpdate2Consumer;

    private ObjectMapper mapper = new ObjectMapper();

    private final String validMessage = "{ \"increment_id\": \"123\", \"status\": \"DELIVERED\" }";
    private OrderStatusUpdateDetails orderStatusUpdateDetails;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        orderStatusUpdateDetails = mapper.readValue(validMessage, OrderStatusUpdateDetails.class);
    }

    @Test
    void testConsume_ValidMessage_Acknowledged() throws Exception {

        statusUpdate2Consumer.consume(validMessage, acknowledgment);

//        verify(orderOpsFeignClient, times(1)).statusUpdate(any(OrderStatusUpdateDetails.class));
//        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    void testProcessMessage_ValidMessage_Processed() throws Exception {
//        doNothing().when(orderOpsFeignClient).statusUpdate(any(OrderStatusUpdateDetails.class));

        statusUpdate2Consumer.processMessage(validMessage);

        verify(orderOpsFeignClient, times(1)).statusUpdate(any(OrderStatusUpdateDetails.class));
    }

    @Test
    void testProcessMessage_InvalidMessage_NotProcessed() throws Exception {
        String invalidMessage = "";

        statusUpdate2Consumer.processMessage(invalidMessage);

        verify(orderOpsFeignClient, never()).statusUpdate(any(OrderStatusUpdateDetails.class));
    }

    @Test
    void testProcessMessage_ExceptionThrown_NotProcessed() throws Exception {
        String faultyMessage = "";

        statusUpdate2Consumer.processMessage(faultyMessage);

        verify(orderOpsFeignClient, never()).statusUpdate(any(OrderStatusUpdateDetails.class));
    }

}