package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.BackSyncStatusDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class BackSyncStatusConsumerTest {

    @InjectMocks
    private BackSyncStatusConsumer backSyncStatusConsumer;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private Acknowledgment acknowledgment;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void consume_ValidMessage_Acknowledged() throws Exception {
        String validMessage = "{\"uwItemId\":\"123\",\"trackingStatus\":\"DELIVERED\"}";
        BackSyncStatusDTO backSyncStatusDTO = new BackSyncStatusDTO();
        backSyncStatusDTO.setUwItemId(123);
        backSyncStatusDTO.setTrackingStatus("DELIVERED");

        when(objectMapper.readValue(anyString(), eq(BackSyncStatusDTO.class))).thenReturn(backSyncStatusDTO);

        backSyncStatusConsumer.consume(validMessage, acknowledgment);

        verify(orderOpsFeignClient, times(1)).callBackSyncApi(anyList(), anyString());
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    void consume_InvalidMessage_NoAcknowledgment() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(anyString(), eq(BackSyncStatusDTO.class))).thenThrow(new JsonProcessingException("Invalid JSON") {});

        backSyncStatusConsumer.consume(invalidMessage, acknowledgment);

        verify(orderOpsFeignClient, never()).callBackSyncApi(anyList(), anyString());
        verify(acknowledgment, never()).acknowledge();
    }

    @Test
    void processMessage_ValidMessage_CallsFeignClient() throws Exception {
        String validMessage = "{\"uwItemId\":\"123\",\"trackingStatus\":\"DELIVERED\"}";
        BackSyncStatusDTO backSyncStatusDTO = new BackSyncStatusDTO();
        backSyncStatusDTO.setUwItemId(123);
        backSyncStatusDTO.setTrackingStatus("DELIVERED");

        when(objectMapper.readValue(validMessage, BackSyncStatusDTO.class)).thenReturn(backSyncStatusDTO);

        backSyncStatusConsumer.processMessage(validMessage);

        verify(orderOpsFeignClient, times(1)).callBackSyncApi(Collections.singletonList(123), "DELIVERED");
    }

    @Test
    void processMessage_EmptyMessage_LogsError() throws JsonProcessingException {
        String emptyMessage = "";

        backSyncStatusConsumer.processMessage(emptyMessage);

        verify(orderOpsFeignClient, never()).callBackSyncApi(anyList(), anyString());
        verify(objectMapper, never()).readValue(anyString(), eq(BackSyncStatusDTO.class));
    }

}