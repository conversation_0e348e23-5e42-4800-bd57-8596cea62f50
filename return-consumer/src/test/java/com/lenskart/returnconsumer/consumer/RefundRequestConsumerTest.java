package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.RefundRequestDTO;
import com.lenskart.returncommon.model.dto.RefundRequestInputDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IRefundRequestService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class RefundRequestConsumerTest {
    @InjectMocks
    private RefundRequestConsumer refundRequestConsumer;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private IRefundRequestService refundRequestService;

    @Mock
    private Acknowledgment acknowledgment;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void consume_ValidMessage_Acknowledged() throws Exception {
        String validMessage = "{\"orderId\":\"ORD123\",\"refundAmount\":100.0}";
        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        RefundRequestInputDTO refundRequestInputDTO = new RefundRequestInputDTO(refundRequestDTO,1);

        when(objectMapper.readValue(anyString(), eq(RefundRequestInputDTO.class))).thenReturn(refundRequestInputDTO);

        refundRequestConsumer.consume(validMessage, acknowledgment);

        verify(refundRequestService, times(1)).createRefundRequest(refundRequestInputDTO);
        verify(acknowledgment, times(1)).acknowledge();
    }
    


    @Test
    void processMessage_ValidMessage_CallsRefundRequestService() throws Exception {
        String validMessage = "{\"orderId\":\"ORD123\",\"refundAmount\":100.0}";
        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        RefundRequestInputDTO refundRequestInputDTO = new RefundRequestInputDTO(refundRequestDTO,1);

        when(objectMapper.readValue(validMessage, RefundRequestInputDTO.class)).thenReturn(refundRequestInputDTO);

        refundRequestConsumer.processMessage(validMessage);

        verify(refundRequestService, times(1)).createRefundRequest(refundRequestInputDTO);
    }

    @Test
    void processMessage_EmptyMessage_LogsError() throws Exception {
        String emptyMessage = "";

        refundRequestConsumer.processMessage(emptyMessage);

        verify(refundRequestService, never()).createRefundRequest(any(RefundRequestInputDTO.class));
        verify(objectMapper, never()).readValue(anyString(), eq(RefundRequestInputDTO.class));
    }

}