package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.CommRequestDTO;
import com.lenskart.returncommon.model.dto.ReturnCommunicationDTO;
import com.lenskart.returnservice.service.ICommunicationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class ReturnCommunicationConsumerTest {

    @Mock
    private ICommunicationService communicationService;

    @Mock
    private Acknowledgment acknowledgment;

    @InjectMocks
    private ReturnCommunicationConsumer returnCommunicationConsumer;

    private ObjectMapper mapper = new ObjectMapper();

    private final String validMessage = "{ \"incrementId\": \"123\", \"refundMethod\": \"Test method\" }";
    private ReturnCommunicationDTO returnCommunicationDTO;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        returnCommunicationDTO = mapper.readValue(validMessage, ReturnCommunicationDTO.class);
    }

//    @Test
//    void testConsume_ValidMessage_Acknowledged() throws Exception {
//        doNothing().when(communicationService).sendCommunication(any(CommRequestDTO.class));
//
//        returnCommunicationConsumer.consume(validMessage, acknowledgment);
//
//        verify(communicationService, times(1)).sendCommunication(eq(returnCommunicationDTO));
//        verify(acknowledgment, times(1)).acknowledge();
//    }

//    @Test
//    void testProcessMessage_ValidMessage_Processed() throws Exception {
//        doNothing().when(communicationService).sendCommunication(any(ReturnCommunicationDTO.class));
//
//        returnCommunicationConsumer.processMessage(validMessage);
//
//        verify(communicationService, times(1)).sendCommunication(eq(returnCommunicationDTO));
//    }

//    @Test
//    void testProcessMessage_InvalidMessage_NotProcessed() throws Exception {
//        String invalidMessage = "";
//
//        returnCommunicationConsumer.processMessage(invalidMessage);
//
//        verify(communicationService, never()).sendCommunication(any(ReturnCommunicationDTO.class));
//    }


}