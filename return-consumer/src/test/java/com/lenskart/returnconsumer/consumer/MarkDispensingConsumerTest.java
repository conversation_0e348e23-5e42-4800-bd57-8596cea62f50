package com.lenskart.returnconsumer.consumer;

import static org.junit.jupiter.api.Assertions.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.MarkDispensingDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class MarkDispensingConsumerTest {

    @InjectMocks
    private MarkDispensingConsumer markDispensingConsumer;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private Acknowledgment acknowledgment;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void consume_ValidMessage_Acknowledged() throws Exception {
        String validMessage = "{\"incrementId\":\"123\",\"groupId\":\"group1\"}";
        MarkDispensingDTO markDispensingDTO = new MarkDispensingDTO();
        markDispensingDTO.setIncrementId(123);
        markDispensingDTO.setGroupId(1L);

        when(objectMapper.readValue(anyString(), eq(MarkDispensingDTO.class))).thenReturn(markDispensingDTO);

        markDispensingConsumer.consume(validMessage, acknowledgment);

//        verify(orderOpsFeignClient, times(1)).markOrderDispensing(123, 1L);
//        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    void consume_InvalidMessage_NoAcknowledgment() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(anyString(), eq(MarkDispensingDTO.class))).thenThrow(new JsonProcessingException("Invalid JSON") {});

        markDispensingConsumer.consume(invalidMessage, acknowledgment);

        verify(orderOpsFeignClient, never()).markOrderDispensing(anyInt(), anyLong());
        verify(acknowledgment, never()).acknowledge();
    }

    @Test
    void processMessage_ValidMessage_CallsFeignClient() throws Exception {
        String validMessage = "{\"incrementId\":\"123\",\"groupId\":\"group1\"}";
        MarkDispensingDTO markDispensingDTO = new MarkDispensingDTO();
        markDispensingDTO.setIncrementId(123);
        markDispensingDTO.setGroupId(1L);

        when(objectMapper.readValue(validMessage, MarkDispensingDTO.class)).thenReturn(markDispensingDTO);

        markDispensingConsumer.processMessage(validMessage);

        verify(orderOpsFeignClient, times(1)).markOrderDispensing(123, 1L);
    }

    @Test
    void processMessage_EmptyMessage_LogsError() throws JsonProcessingException {
        String emptyMessage = "";

        markDispensingConsumer.processMessage(emptyMessage);

        verify(orderOpsFeignClient, never()).markOrderDispensing(anyInt(), anyLong());
        verify(objectMapper, never()).readValue(anyString(), eq(MarkDispensingDTO.class));
    }

    @Test
    void processMessage_InvalidJson_ThrowsException() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(invalidMessage, MarkDispensingDTO.class)).thenThrow(new JsonProcessingException("Invalid JSON") {});

        assertThrows(JsonProcessingException.class, () -> markDispensingConsumer.processMessage(invalidMessage));

        verify(orderOpsFeignClient, never()).markOrderDispensing(anyInt(), anyLong());
    }

}