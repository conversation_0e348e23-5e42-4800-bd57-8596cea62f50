package com.lenskart.returnconsumer.consumer;

import static org.junit.jupiter.api.Assertions.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.CommentDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class OrderCommentConsumerTest {
    @InjectMocks
    private OrderCommentConsumer orderCommentConsumer;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private Acknowledgment acknowledgment;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void consume_ValidMessage_Acknowledged() throws Exception {
        String validMessage = "{\"comment\":\"Good service\",\"orderId\":\"ORD123\"}";
        CommentDTO commentDTO = new CommentDTO();
        HashMap<String,String> comment = new HashMap<>();
        comment.put("comment", "comment");
        commentDTO.setComment(comment);
        commentDTO.setOrderId(123);

        when(objectMapper.readValue(anyString(), eq(CommentDTO.class))).thenReturn(commentDTO);

        orderCommentConsumer.consume(validMessage, acknowledgment);

        verify(orderOpsFeignClient, times(1)).insertOrderComment(comment, 123);
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    void consume_InvalidMessage_NoAcknowledgment() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(anyString(), eq(CommentDTO.class))).thenThrow(new JsonProcessingException("Invalid JSON") {});

        orderCommentConsumer.consume(invalidMessage, acknowledgment);

        verify(orderOpsFeignClient, never()).insertOrderComment(anyMap(), anyInt());
        verify(acknowledgment, never()).acknowledge();
    }

    @Test
    void processMessage_ValidMessage_CallsFeignClient() throws Exception {
        String validMessage = "{\"comment\":\"Good service\",\"orderId\":\"ORD123\"}";
        CommentDTO commentDTO = new CommentDTO();
        HashMap<String,String> comment = new HashMap<>();
        comment.put("comment", "comment");
        commentDTO.setComment(comment);
        commentDTO.setOrderId(123);

        when(objectMapper.readValue(validMessage, CommentDTO.class)).thenReturn(commentDTO);

        orderCommentConsumer.processMessage(validMessage);

        verify(orderOpsFeignClient, times(1)).insertOrderComment(comment, 123);
    }

    @Test
    void processMessage_EmptyMessage_LogsError() throws JsonProcessingException {
        String emptyMessage = "";

        orderCommentConsumer.processMessage(emptyMessage);

        verify(orderOpsFeignClient, never()).insertOrderComment(anyMap(), anyInt());
        verify(objectMapper, never()).readValue(anyString(), eq(CommentDTO.class));
    }

    @Test
    void processMessage_InvalidJson_ThrowsException() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(invalidMessage, CommentDTO.class)).thenThrow(new JsonProcessingException("Invalid JSON") {});

        assertThrows(JsonProcessingException.class, () -> orderCommentConsumer.processMessage(invalidMessage));

        verify(orderOpsFeignClient, never()).insertOrderComment(anyMap(), anyInt());
    }

}