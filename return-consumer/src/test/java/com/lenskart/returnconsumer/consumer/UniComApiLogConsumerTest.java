package com.lenskart.returnconsumer.consumer;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {UniComApiLogConsumer.class})
@ExtendWith(SpringExtension.class)
class UniComApiLogConsumerTest {
    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private UniComApiLogConsumer uniComApiLogConsumer;

    /**
     * Method under test:
     * {@link UniComApiLogConsumer#consume(String, Acknowledgment)}
     */
    @Test
    void testConsume() {
        // Arrange
        Acknowledgment ack = mock(Acknowledgment.class);
        doNothing().when(ack).acknowledge();

        // Act
        uniComApiLogConsumer.consume("", ack);

        // Assert that nothing has changed
        verify(ack).acknowledge();
    }
}
