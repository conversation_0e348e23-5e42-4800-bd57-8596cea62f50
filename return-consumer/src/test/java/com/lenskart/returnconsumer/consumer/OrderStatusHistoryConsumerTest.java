package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class OrderStatusHistoryConsumerTest {
    @InjectMocks
    private OrderStatusHistoryConsumer orderStatusHistoryConsumer;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private Acknowledgment acknowledgment;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void consume_ValidMessage_Acknowledged() throws Exception {
        String validMessage = "{\"comment\":\"Order delivered\",\"orderId\":\"ORD456\"}";
        OrderCommentReq orderCommentReq = new OrderCommentReq();
        orderCommentReq.setComment("Order delivered");
        orderCommentReq.setIncrementId(123);

        when(objectMapper.readValue(anyString(), eq(OrderCommentReq.class))).thenReturn(orderCommentReq);

        orderStatusHistoryConsumer.consume(validMessage, acknowledgment);

//        verify(orderOpsFeignClient, times(1)).addOrderComment(orderCommentReq);
//        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    void consume_InvalidMessage_NoAcknowledgment() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(anyString(), eq(OrderCommentReq.class))).thenThrow(new JsonProcessingException("Invalid JSON") {});

        orderStatusHistoryConsumer.consume(invalidMessage, acknowledgment);

        verify(orderOpsFeignClient, never()).addOrderComment(any(OrderCommentReq.class));
        verify(acknowledgment, never()).acknowledge();
    }

    @Test
    void processMessage_ValidMessage_CallsFeignClient() throws Exception {
        String validMessage = "{\"comment\":\"Order delivered\",\"orderId\":\"ORD456\"}";
        OrderCommentReq orderCommentReq = new OrderCommentReq();
        orderCommentReq.setComment("Order delivered");
        orderCommentReq.setIncrementId(123);

        when(objectMapper.readValue(validMessage, OrderCommentReq.class)).thenReturn(orderCommentReq);

        orderStatusHistoryConsumer.processMessage(validMessage);

        verify(orderOpsFeignClient, times(1)).addOrderComment(orderCommentReq);
    }

    @Test
    void processMessage_EmptyMessage_LogsError() throws JsonProcessingException {
        String emptyMessage = "";

        orderStatusHistoryConsumer.processMessage(emptyMessage);

        verify(orderOpsFeignClient, never()).addOrderComment(any(OrderCommentReq.class));
        verify(objectMapper, never()).readValue(anyString(), eq(OrderCommentReq.class));
    }

    @Test
    void processMessage_InvalidJson_ThrowsException() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(invalidMessage, OrderCommentReq.class)).thenThrow(new JsonProcessingException("Invalid JSON") {});

        assertThrows(JsonProcessingException.class, () -> orderStatusHistoryConsumer.processMessage(invalidMessage));

        verify(orderOpsFeignClient, never()).addOrderComment(any(OrderCommentReq.class));
    }
}