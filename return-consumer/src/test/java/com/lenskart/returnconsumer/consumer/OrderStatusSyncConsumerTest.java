package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrderStatusSyncDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
class OrderStatusSyncConsumerTest {

    @InjectMocks
    private OrderStatusSyncConsumer orderStatusSyncConsumer;

    @Mock
    private OrderOpsFeignClient orderOpsFeignClient;

    @Mock
    private Acknowledgment acknowledgment;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void consume_ValidMessage_Acknowledged() throws Exception {
        String validMessage = "{\"incrementId\":\"ORD123\",\"status\":\"DELIVERED\",\"trackingNo\":\"TRACK123\"}";
        OrderStatusSyncDTO orderStatusSyncDTO = new OrderStatusSyncDTO();
        orderStatusSyncDTO.setIncrementId(123);
        orderStatusSyncDTO.setStatus("DELIVERED");
        orderStatusSyncDTO.setTrackingNo("TRACK123");

        when(objectMapper.readValue(anyString(), eq(OrderStatusSyncDTO.class))).thenReturn(orderStatusSyncDTO);

        orderStatusSyncConsumer.consume(validMessage, acknowledgment);

        verify(orderOpsFeignClient, times(1)).saveOrderSyncDetails(123, "DELIVERED", "TRACK123");
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    void consume_InvalidMessage_NoAcknowledgment() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(anyString(), eq(OrderStatusSyncDTO.class))).thenThrow(new JsonProcessingException("Invalid JSON") {});

        orderStatusSyncConsumer.consume(invalidMessage, acknowledgment);

        verify(orderOpsFeignClient, never()).saveOrderSyncDetails(anyInt(), anyString(), anyString());
        verify(acknowledgment, never()).acknowledge();
    }


    @Test
    void processMessage_ValidMessage_CallsFeignClient() throws Exception {
        String validMessage = "{\"incrementId\":\"ORD123\",\"status\":\"DELIVERED\",\"trackingNo\":\"TRACK123\"}";
        OrderStatusSyncDTO orderStatusSyncDTO = new OrderStatusSyncDTO();
        orderStatusSyncDTO.setIncrementId(123);
        orderStatusSyncDTO.setStatus("DELIVERED");
        orderStatusSyncDTO.setTrackingNo("TRACK123");

        when(objectMapper.readValue(validMessage, OrderStatusSyncDTO.class)).thenReturn(orderStatusSyncDTO);

        orderStatusSyncConsumer.processMessage(validMessage);

        verify(orderOpsFeignClient, times(1)).saveOrderSyncDetails(123, "DELIVERED", "TRACK123");
    }

    @Test
    void processMessage_EmptyMessage_LogsError() throws JsonProcessingException {
        String emptyMessage = "";

        orderStatusSyncConsumer.processMessage(emptyMessage);

        verify(orderOpsFeignClient, never()).saveOrderSyncDetails(anyInt(), anyString(), anyString());
        verify(objectMapper, never()).readValue(anyString(), eq(OrderStatusSyncDTO.class));
    }

    @Test
    void processMessage_InvalidJson_ThrowsException() throws Exception {
        String invalidMessage = "{\"invalidJson\":\"value\"}";

        when(objectMapper.readValue(invalidMessage, OrderStatusSyncDTO.class)).thenThrow(new JsonProcessingException("Invalid JSON") {});

        assertThrows(JsonProcessingException.class, () -> orderStatusSyncConsumer.processMessage(invalidMessage));

        verify(orderOpsFeignClient, never()).saveOrderSyncDetails(anyInt(), anyString(), anyString());
    }

}