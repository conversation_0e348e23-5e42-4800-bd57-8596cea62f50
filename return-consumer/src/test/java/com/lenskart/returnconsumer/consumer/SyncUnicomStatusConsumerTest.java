package com.lenskart.returnconsumer.consumer;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.lenskart.returnservice.service.ISyncUnicomStatusService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {SyncUnicomStatusConsumer.class})
@ExtendWith(SpringExtension.class)
class SyncUnicomStatusConsumerTest {
    @MockBean
    private ISyncUnicomStatusService iSyncUnicomStatusService;

    @Autowired
    private SyncUnicomStatusConsumer syncUnicomStatusConsumer;

    /**
     * Method under test: {@link SyncUnicomStatusConsumer#consume(String, Acknowledgment)}
     */
    @Test
    void testConsume() {
        // Arrange
        Acknowledgment acknowledgment = mock(Acknowledgment.class);
        doNothing().when(acknowledgment).acknowledge();

        // Act
        syncUnicomStatusConsumer.consume("Not all who wander are lost", acknowledgment);

        // Assert
//        verify(acknowledgment).acknowledge();
    }

    /**
     * Method under test: {@link SyncUnicomStatusConsumer#consume(String, Acknowledgment)}
     */
    @Test
    void testConsume2() {
        // Arrange
        Acknowledgment acknowledgment = mock(Acknowledgment.class);
        doNothing().when(acknowledgment).acknowledge();

        // Act
        syncUnicomStatusConsumer.consume("42", acknowledgment);

        // Assert
        verify(acknowledgment).acknowledge();
    }

    /**
     * Method under test: {@link SyncUnicomStatusConsumer#consume(String, Acknowledgment)}
     */
    @Test
    void testConsume3() {
        // Arrange
        Acknowledgment acknowledgment = mock(Acknowledgment.class);
        doNothing().when(acknowledgment).acknowledge();

        // Act
        syncUnicomStatusConsumer.consume("", acknowledgment);

        // Assert that nothing has changed
//        verify(acknowledgment).acknowledge();
    }
}

