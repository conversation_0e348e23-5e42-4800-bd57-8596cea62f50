package com.lenskart.returnconsumer.consumer;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.lenskart.returnrepository.repository.ReturnCourierDetailRepository;
import com.lenskart.returnrepository.repository.ReturnRequestRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.ReverseLogisticsConsumerFeignClient;
import com.lenskart.returnservice.service.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ReturnCreateCourierConsumer.class})
@ExtendWith(SpringExtension.class)
class ReturnCreateCourierConsumerTest {
    @MockBean
    private IKafkaService iKafkaService;

    @MockBean
    private ICommunicationService iCommunicationService;

    @MockBean
    private ReturnRequestRepository returnRequestRepository;

    @MockBean
    private IReturnEventService iReturnEventService;

    @MockBean
    private IReturnMethodResolver iReturnMethodResolver;

    @MockBean
    private IReturnOrderActionService iReturnOrderActionService;

    @MockBean
    private ISystemPreferenceService iSystemPreferenceService;

    @MockBean
    private OrderOpsFeignClient orderOpsFeignClient;

    @MockBean
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @Autowired
    private ReturnCreateCourierConsumer returnCreateCourierConsumer;

    @MockBean
    private ReverseLogisticsConsumerFeignClient reverseLogisticsConsumerFeignClient;

    /**
     * Method under test:
     * {@link ReturnCreateCourierConsumer#consume(String, Acknowledgment)}
     */
    @Test
    void testConsume() {
        Acknowledgment ack = mock(Acknowledgment.class);
        doNothing().when(ack).acknowledge();
        returnCreateCourierConsumer.consume("", ack);
        verify(ack).acknowledge();
    }

    /**
     * Method under test:
     * {@link ReturnCreateCourierConsumer#consume(String, Acknowledgment)}
     */
    @Test
    void testConsume2() {
        Acknowledgment ack = mock(Acknowledgment.class);
        doThrow(new RuntimeException("[processMessage] Processing message : {}")).when(ack).acknowledge();
        returnCreateCourierConsumer.consume("", ack);
        verify(ack).acknowledge();
    }
}
