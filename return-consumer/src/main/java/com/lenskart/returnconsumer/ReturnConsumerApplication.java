package com.lenskart.returnconsumer;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication( exclude = { RedisAutoConfiguration.class } )
@EnableFeignClients(basePackages = "com.lenskart")
@ComponentScan(basePackages = {"com.lenskart","com.lenskart.platform.fl.utils"})
@EnableAsync
@Slf4j
public class ReturnConsumerApplication {
	@Value("${APPLICATION_NAME:default}")
	String applicationName;
	public static void main(String[] args) {
		SpringApplication.run(ReturnConsumerApplication.class, args);
	}

	@PostConstruct
	private void postConstruct() {
		log.info("Vault::APPLICATION_NAME: " + applicationName);
	}
}
