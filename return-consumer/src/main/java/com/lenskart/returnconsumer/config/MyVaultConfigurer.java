package com.lenskart.returnconsumer.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.vault.config.SecretBackendConfigurer;
import org.springframework.cloud.vault.config.VaultConfigurer;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class MyVaultConfigurer implements VaultConfigurer {

    @Value("${VAULT_BACKEND:lenskart/ces/ces-config}")
    String vault_backend;

    @Value("${VAULT_DEFAULT_CONTEXT:return-consumer}")
    String appName;

    @Override
    public void addSecretBackends(SecretBackendConfigurer configurer) {
        configurer.add(vault_backend + "/" + appName);
        configurer.registerDefaultDiscoveredSecretBackends(false);
    }
}