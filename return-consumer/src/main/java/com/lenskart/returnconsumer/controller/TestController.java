package com.lenskart.returnconsumer.controller;

import com.lenskart.platform.fl.utils.dto.giftCard.GiftCardD365Request;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returnservice.service.ID365FinanceService;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.impl.GiftCardReversal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("")
public class TestController {

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ID365FinanceService d365FinanceService;

    @Autowired
    private GiftCardReversal giftCardService;

    @GetMapping("")
    public String serviceUp() {
        return "Hello from Headless Return Consumer!";
    }

    @RequestMapping(value = "/push-data/{queue}", method = RequestMethod.POST)
    public String pushTestData(@RequestBody Map<String, Object> data, @PathVariable("queue") String queue) {
        kafkaService.pushToKafka(queue, "abc", data);
        return "success";
    }

    @RequestMapping(value = "/return-event", method = RequestMethod.POST)
    public String testGeneratePayloadForReturn(@RequestBody ReturnCreateRequest payload) throws Exception {
        d365FinanceService.generatePayloadForReturn(payload);
        return "success";
    }

    @PostMapping("/gift-card-payload")
    public ResponseEntity<GiftCardD365Request> getPayload(@RequestBody List<Integer> uwItemIds) {
        GiftCardD365Request payload = giftCardService.getPayloadViaApi(uwItemIds);
        if(Objects.isNull(payload)) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        } else {
            return new ResponseEntity<>(payload,HttpStatus.OK);
        }
    }
}
