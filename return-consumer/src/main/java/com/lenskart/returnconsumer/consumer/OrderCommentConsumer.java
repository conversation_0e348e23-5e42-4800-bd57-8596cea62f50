package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.CommentDTO;
import com.lenskart.returncommon.model.dto.MarkDispensingDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class OrderCommentConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "order-comment-2", topics = "${order.comment.topic:order_comments_queue}", groupId = "${order.comment.topic.group:order_comments_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[OrderCommentConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            //message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[OrderCommentConsumer][consume] exception found in consume : " + exception);
        }
    }

    protected void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[OrderCommentConsumer][processMessage] empty message received");
        } else {
            CommentDTO commentDTO = mapper.readValue(message, CommentDTO.class);
            if (commentDTO != null) {
                orderOpsFeignClient.insertOrderComment(commentDTO.getComment(), commentDTO.getOrderId());
            }
        }
    }

}
