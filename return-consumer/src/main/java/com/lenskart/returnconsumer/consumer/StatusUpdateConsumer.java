package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrderStatusUpdateDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IOrderStatusUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class StatusUpdateConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IOrderStatusUpdateService orderStatusUpdateService;

    @KafkaListener(id = "status-update-1", topics = "${uworders.status.update.topic:uw_orders_status_update}", groupId = "${uworders.status.update.topic.group:uw_orders_status_update_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[StatusUpdateConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[StatusUpdateConsumer][consume] exception found in listenConsumer1 : " + exception);
        }
    }

    protected void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[StatusUpdateConsumer][processMessage] empty message received");
        } else {
            List<Integer> uwItemds = mapper.readValue(message, List.class);
            if (!CollectionUtils.isEmpty(uwItemds)) {
                log.info("[StatusUpdateConsumer][processMessage] calling status update api : "+uwItemds);
                orderOpsFeignClient.statusUpdate(uwItemds);
            }
        }
    }

    @KafkaListener(id = "status-update-3", topics = "${return.order.status.update.topic:return_order_status_update}", groupId = "${return.order.status.update.topic.group:return_order_status_update_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeStatusUpdateMsg(String message, Acknowledgment ack) {
        try {
            log.info("[StatusUpdateConsumer][consumeStatusUpdateMsg] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            updateStatus(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[StatusUpdateConsumer][consumeStatusUpdateMsg] exception found in listenConsumer1 : " + exception);
        }
    }

    protected void updateStatus(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[StatusUpdateConsumer][updateStatus] empty message received");
        } else {
            List<Map<String, Object>> orderStatusUpdateDTOS = mapper.readValue(message, List.class);
            if (!CollectionUtils.isEmpty(orderStatusUpdateDTOS)) {
                orderStatusUpdateService.updateOrderStatus(orderStatusUpdateDTOS);
            }
        }
    }

}
