package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.OrderAddressUpdateDTO;
import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.returncommon.config.IgnoreMetadataMixin;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.request.*;
import com.lenskart.returncommon.model.response.CustomerMetaDataResponse;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.*;
import com.lenskart.returnrepository.repository.*;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.RMSFeignClient;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.lenskart.returncommon.utils.Constant.ADDRESS_TYPE.BILLING;

@Slf4j
@Service
public class PostingDataToNuggetConsumer {
//    @Value("${nugget.api.auth.key}")
//    private String nuggetSecretKey;

    @Value("${nugget.aggregator.baseurl:https://ivr-core-system.scm.lenskart.com}")
    private String baseUrlNugget;

    @Value("${url.product.image:https://fs.scm.lenskart.com/product/info/}")
    private String urlProductImage;

    @Value("${url.customer.image:https://fus.lenskart.com/file/image/pos-return-approval/}")
    private String urlCustomerImage;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    private BotAutoApprovalRepository botAutoApprovalRepository;

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private IJunoService junoService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private RMSFeignClient rmsFeignClient;

    @Autowired
    private ClaimDecisionRuleRepository claimDecisionRuleRepository;

    @Autowired
    ISystemPreferenceService systemPreferenceService;

    @Autowired
    private ModelPromptRepository modelPromptRepository;

    @Autowired
    RedisTemplate redisTemplate;



    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY))
            .addMixIn(ReturnCreationResponse.class, IgnoreMetadataMixin.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
//    private static final String SIGNATURE_KEY = "signature";
    private final static int MAX_RETRY = 2;



    @KafkaListener(id = "posting-data-to-nugget-queue-1", topics = "${posting.data.to.nugget.queue:posting_data_to_nugget_queue}", groupId = "${posting.data.to.nugget.queue.group:posting_data_to_nugget_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[PostingDataToNuggetConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[PostingDataToNuggetConsumer][consume] exception found in consume : "+exception);
        }
    }

    private void processMessage(String message) {
        boolean retry = false;
        AutoApprovalRequest autoApprovalRequest = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ExchangeOrderConsumer][processMessage] empty message received");
            }else{
                autoApprovalRequest = mapper.readValue(message, AutoApprovalRequest.class);
                if(autoApprovalRequest != null){
                    retry = postingDataToNuggetConsumer(autoApprovalRequest);
                    checkResponseAndRetry(autoApprovalRequest, retry);
                }
            }
        }catch (Exception exception){
            log.error("[ExchangeOrderConsumer][processMessage] exception found : {}", String.valueOf(exception));
        }
    }

    private void checkResponseAndRetry(AutoApprovalRequest autoApprovalRequest,
                                       boolean response) throws Exception {
        if (autoApprovalRequest.getRetryCount() <= MAX_RETRY && response) {
            autoApprovalRequest.setRetryCount((autoApprovalRequest.getRetryCount() + 1));
            log.info("[checkResponseAndRetry][nugget] call to return auto approval return cycle {}", autoApprovalRequest);
            kafkaService.pushToKafka("posting_data_to_nugget_queue", String.valueOf(autoApprovalRequest.getRefundRequestResponse().getReturnId()), autoApprovalRequest);
        } else if (autoApprovalRequest.getRetryCount() > MAX_RETRY) {
            log.info("[checkResponseAndRetry][nugget] return auto approval reached max retry count for {}", autoApprovalRequest);
        }
    }

    private boolean postingDataToNuggetConsumer(AutoApprovalRequest autoApprovalRequest) {
        boolean isSuccessfullyPosted = false;
        try {
            log.info("[postingDataToNuggetConsumer] order:{}", autoApprovalRequest.getReturnCreationRequest().getPurchaseOrderDetailsDTO().getUwOrders().get(0).getIncrementId());
            List<ModelPrompt> modelPrompts= findAllModelPrompts(Constant.SYSTEM_PREFERENCE_GROUPS.LLM_MODEL_PROMPTS, 3, TimeUnit.HOURS);
            ImagesToTextRequest imagesToTextRequest = createAutoApprovalBotRequest(autoApprovalRequest);

            for(ModelPrompt modelPrompt:modelPrompts) {
                imagesToTextRequest.setModel(modelPrompt.getModelName());
                imagesToTextRequest.setMessage(modelPrompt.getPrompt());
                log.info("[postingDataToNuggetConsumer] imagesToTextRequest:{}", imagesToTextRequest.toString());
                isSuccessfullyPosted = postNeedApprovalDataAutoApprovalBot(imagesToTextRequest);
            }
        } catch (Exception exception) {
            log.error("[postingDataToNuggetConsumer] exception : {}", exception.getMessage());
        }
        return !isSuccessfullyPosted;
    }

    private Map<String,String> createMetaData(AutoApprovalRequest autoApprovalRequest,UwOrderDTO uwOrder) {
        Map<String, String> metadata = new HashMap<>();
        try {
            Map<String, Object> productDetailsMap = junoService.getProductDetails(uwOrder.getProductId());

            ReturnCreationRequestDTO returnCreationRequest = autoApprovalRequest.getReturnCreationRequest();
            ReturnCreationResponse refundRequestResponse = autoApprovalRequest.getRefundRequestResponse();


            if (productDetailsMap != null) {
                if (productDetailsMap.get(Constant.JUNO_PRODUCT.ITEM_CATEGORY) != null) {
                    metadata.put("productCategory", (String) productDetailsMap.get(Constant.JUNO_PRODUCT.ITEM_CATEGORY));
                }
                if (productDetailsMap.get(Constant.JUNO_PRODUCT.PRODUCT_BRAND) != null) {
                    metadata.put("productBrand", (String) productDetailsMap.get(Constant.JUNO_PRODUCT.PRODUCT_BRAND));
                    metadata.put("brandCategory", getBrandCategory(metadata.get("productBrand")));
                }
                if (productDetailsMap.get(Constant.JUNO_PRODUCT.FRAME_MATERIAL) != null) {
                    metadata.put("frameMaterial", (String) productDetailsMap.get(Constant.JUNO_PRODUCT.FRAME_MATERIAL));
                }
                if (productDetailsMap.get(Constant.JUNO_PRODUCT.FRAME_TYPE) != null) {
                    metadata.put("frameType", (String) productDetailsMap.get(Constant.JUNO_PRODUCT.FRAME_TYPE));
                }
            }


            ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(uwOrder.getUwItemId());

            metadata.put("returnId", String.valueOf(refundRequestResponse.getResult().getReturns().get(0).getReturnId()));
            metadata.put("itemId", String.valueOf(uwOrder.getItemId()));
            metadata.put("orderId", String.valueOf(uwOrder.getIncrementId()));
            metadata.put("magentoId", String.valueOf(returnCreationRequest.getItems().get(0).getMagentoId()));
            metadata.put("productId", String.valueOf(uwOrder.getProductId()));
            metadata.put("warrantyClaimReason", returnDetailItem.getReasonForReturn());
            metadata.put("storeStaffComments", returnDetailItem.getQcComment() != null ? returnDetailItem.getQcComment() : "NA");
            metadata.put("masterOrderCreationDate", uwOrder.getCreatedAt().toInstant().toString());
            metadata.put("deliveryDate", getDeliveryDate(returnCreationRequest, uwOrder));
            metadata.put("issueCommonToProduct", "No");//default
//        metadata.put("customerConfidence","Low");//default
            metadata.put("claimDate", returnDetailItem.getReturnCreateDatetime().toInstant().toString());
            metadata.put("claimType", returnCreationRequest.getItems().get(0).getClaimInsurance() != null && returnCreationRequest.getItems().get(0).getClaimInsurance() ? "Insurance" : "Warranty");
//        metadata.put("storeCallbackRequested", String.valueOf(returnCreationRequest.isCallbackRequiredToSalesman()));
            metadata.put("storeCallbackRequested", "false");
            metadata.put("periodAllowed", getWarrantyDays(metadata.get("brandCategory"), returnCreationRequest.getItems().get(0).getReasons().get(0).getSecondaryReasonId()));

            setPowerTypeAndRange(metadata, returnCreationRequest.getItems().get(0).getMagentoId());
            setCustomerCategory(returnCreationRequest, uwOrder, metadata);
            setCustomerName(returnCreationRequest,metadata);

            return metadata;
        }catch (Exception e){
            log.error("[AiBotConsumer][createMetaData] exception in creating metadata:{}",e.getMessage());
            return metadata;
        }
    }

    private void setCustomerName(ReturnCreationRequestDTO returnCreationRequest,Map<String,String> metadata){

        try{
            Integer orderId =returnCreationRequest.getPurchaseOrderDetailsDTO().getOrders().get(0).getOrderId();
            ResponseEntity<OrderAddressUpdateDTO> orderAddressUpdateResponseEntity = orderOpsFeignClient.getOrderAddressUpdate(orderId,BILLING);
            if(orderAddressUpdateResponseEntity.getStatusCode().is2xxSuccessful()){
                OrderAddressUpdateDTO addressUpdateDTO = orderAddressUpdateResponseEntity.getBody();

                if(addressUpdateDTO != null){
                    StringBuilder fullName = getCompleteName(addressUpdateDTO);
                    metadata.put("customerName",fullName.toString());
                }
            }else{
                log.info("[PostingDataToNuggetConsumer][setCustomerName] api failure : {}", orderAddressUpdateResponseEntity);
            }
        }catch (Exception e){
            log.error("[PostingDataToNuggetConsumer][setCustomerName] error occurred : {}", e.getMessage());
        }
    }

    private  StringBuilder getCompleteName(OrderAddressUpdateDTO addressUpdateDTO) {
        String firstName= addressUpdateDTO.getFirstName();
        String lastName= addressUpdateDTO.getLastName();
        StringBuilder fullName = new StringBuilder();

        if (firstName != null && !firstName.trim().isEmpty()) {
            fullName.append(firstName.trim());
        }

        if (lastName != null && !lastName.trim().isEmpty()) {
            if (!fullName.isEmpty()) {
                fullName.append(" ");
            }
            fullName.append(lastName.trim());
        }
        return fullName;
    }

    private void setCustomerCategory(ReturnCreationRequestDTO returnCreationRequest,UwOrderDTO uwOrder,Map<String,String> metadata) {
        try {
            OrdersDTO ordersDTO = returnCreationRequest.getPurchaseOrderDetailsDTO().getOrders().stream().filter(o -> Objects.equals(o.getItemId(), uwOrder.getItemId()))
                    .findFirst()
                    .orElse(null);

            if (ordersDTO != null) {
                CustomerStoreProfileRequest customerStoreProfileRequest = new CustomerStoreProfileRequest();
                customerStoreProfileRequest.setCustomerId(ordersDTO.getCustomerId());
                ResponseEntity<CustomerMetaDataResponse> response = rmsFeignClient.fetchCustomerMetaData(customerStoreProfileRequest);
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null && response.getBody().getCustomerCategoryResponse().getCustomerCategory()!=null) {
                    metadata.put("customerCategory", response.getBody().getCustomerCategoryResponse().getCustomerCategory());
                }else{
                    metadata.put("customerCategory","NA");
                }
            }
        }catch (Exception e){
            log.error("[AiBotConsumer][setCustomerCategory] exception:{}",e.getMessage());
        }
    }

    private String getBrandCategory(String productBrand) {
        String category = "CAT4";
        if (productBrand == null || productBrand.trim().isEmpty()) {
            return category;
        }

        List<SystemPreference> systemPreferenceList = systemPreferenceService
                .findAllByGroup(Constant.SYSTEM_PREFERENCE_GROUPS.BRAND_CATEGORY, 1, TimeUnit.DAYS);

        if (!CollectionUtils.isEmpty(systemPreferenceList)) {
            for (SystemPreference systemPreference : systemPreferenceList) {
                String value = systemPreference.getValue();
                if (value != null) {
                    List<String> brands = Arrays.stream(value.split(","))
                            .map(String::trim)
                            .toList();

                    boolean matchFound = brands.stream()
                            .anyMatch(b -> b.toLowerCase().contains(productBrand.toLowerCase()) || productBrand.toLowerCase().contains(b.toLowerCase()));

                    if (matchFound) {
                        category = systemPreference.getKey(); // Return the CAT code
                        break;
                    }
                }
            }
        }
        return category;
    }

    public String getWarrantyDays(String category, Integer reasonId) {
        String warrantPeriod=claimDecisionRuleRepository.findWarrantyPeriodByCategoryAndReason(category, String.valueOf(reasonId));
        return warrantPeriod!=null?warrantPeriod:"30";
    }

    private void setPowerTypeAndRange(Map<String,String> metadata,Long magentoId){
        try {
            ResponseEntity<List<CustomOptionsResponseDTO>> customOptionsResponseDTO = orderOpsFeignClient.getLensPresciption(magentoId);
            log.info("[AiBotConsumer] customOptionsResponseDTO : {}", customOptionsResponseDTO);

            if (customOptionsResponseDTO.getStatusCode().is2xxSuccessful() && !CollectionUtils.isEmpty(customOptionsResponseDTO.getBody())) {
                List<CustomOptionsResponseDTO> customOptions = customOptionsResponseDTO.getBody();

                CustomOptionsResponseDTO rightPrescriptionLens = customOptions.get(0);
                CustomOptionsResponseDTO leftPrescriptionLens = customOptions.get(1);

                double avgPower = calculateAveragePower(rightPrescriptionLens, leftPrescriptionLens);
                metadata.put("powerRange", categorizePower(avgPower));
                metadata.put("powerType", rightPrescriptionLens.getLensPackageType() != null ? rightPrescriptionLens.getLensPackageType() : "NA");//which one to choose right or left
            } else {
                metadata.put("powerRange", "NA");
                metadata.put("powerType", "NA");
            }
        }catch (Exception e){
            log.error("[AiBotConsumer][setPowerTypeAndRange] exception:{}",e.getMessage());
        }
    }

    private static String categorizePower(double averagePower) {
        if (averagePower < 3.0) {
            return "Low";
        } else if (averagePower < 6.0) {
            return "Medium";
        } else {
            return "High";
        }
    }

    private double calculateAveragePower(CustomOptionsResponseDTO rightPrescriptionLens,CustomOptionsResponseDTO leftPrescriptionLens) {
        double sphLeft=StringUtils.isEmpty(leftPrescriptionLens.getSph())?0.0:Double.parseDouble(leftPrescriptionLens.getSph());
        double cylLeft=StringUtils.isEmpty(leftPrescriptionLens.getCyl())?0.0:Double.parseDouble(leftPrescriptionLens.getCyl());
        double sphRight=StringUtils.isEmpty(rightPrescriptionLens.getSph())?0.0:Double.parseDouble(rightPrescriptionLens.getSph());
        double cylRight=StringUtils.isEmpty(rightPrescriptionLens.getCyl())?0.0:Double.parseDouble(rightPrescriptionLens.getCyl());

        double mseLeft = sphLeft + (cylLeft / 2.0);  //mean spherical equivalent
        double mseRight = sphRight + (cylRight / 2.0);

        double absLeft = Math.abs(mseLeft);
        double absRight = Math.abs(mseRight);

        if(absLeft>absRight){
            return absLeft;
        }else{
            return absRight;
        }

//        return (absLeft + absRight) / 2.0;
    }

    private String getDeliveryDate(ReturnCreationRequestDTO returnCreationRequest, UwOrderDTO uwOrder) {
        try {
            Date time = null;
            Date zeroTime = new Date(0, 0, 0, 0, 0, 0); // Deprecated, but keeping for legacy compatibility
            boolean shipToStoreOrder = uwOrder.getShipToStoreRequired(); // S2S if true, S2C if false

            ShippingStatusDetail shippingStatusDetail = null;
            if (returnCreationRequest.getPurchaseOrderDetailsDTO().getShippingStatusDetailList() != null) {
                shippingStatusDetail = returnCreationRequest.getPurchaseOrderDetailsDTO().getShippingStatusDetailList().stream()
                        .filter(ss -> Objects.equals(uwOrder.getUnicomOrderCode(), ss.getUnicomOrderCode())
                                && ss.getOrderNo().equals(uwOrder.getIncrementId()))
                        .findFirst()
                        .orElse(null);
            }


            if (!shipToStoreOrder) { // S2C
                // 1. Check for Delivered Date first
                if (shippingStatusDetail != null && shippingStatusDetail.getDeliveredDate() != null
                        && !shippingStatusDetail.getDeliveredDate().equals(zeroTime)) {
                    time = shippingStatusDetail.getDeliveredDate();
                } else if (shippingStatusDetail != null && shippingStatusDetail.getComplete_time() != null
                        && !shippingStatusDetail.getComplete_time().equals(zeroTime)) {
                    // Dispatch date + 7 days
                    time = addDays(shippingStatusDetail.getComplete_time(), 3);
                } else {
                    // Order date + 14 days
                    time = addDays(uwOrder.getCreatedAt(), 7);
                }
            } else { // S2S
                OfflineStatusDetailsDTO handedOverToCustomerDate = new OfflineStatusDetailsDTO();
                OfflineStatusDetailsDTO receivedAtStoreDate = new OfflineStatusDetailsDTO();
                ResponseEntity<List<OfflineStatusDetailsDTO>> offlineOrderStatusResponse = orderOpsFeignClient.getOfflineOrderStatus(uwOrder.getUwItemId());
                log.info("[AiBotConsumer] offlineOrderStatusResponse : {}", offlineOrderStatusResponse);

                if (offlineOrderStatusResponse.getStatusCode().is2xxSuccessful() && !CollectionUtils.isEmpty(offlineOrderStatusResponse.getBody())) {
                    List<OfflineStatusDetailsDTO> offlineOrderStatus = offlineOrderStatusResponse.getBody();
                    handedOverToCustomerDate = offlineOrderStatus.stream().filter(oos -> Objects.equals("handedOverToCustomer", oos.getStatus())).findFirst().orElse(null);
                    receivedAtStoreDate = offlineOrderStatus.stream().filter(oos -> Objects.equals("receivedAtStore", oos.getStatus())).findFirst().orElse(null);
                }

                if (handedOverToCustomerDate != null && handedOverToCustomerDate.getStatus() != null && handedOverToCustomerDate.getCreatedAt() != null && !handedOverToCustomerDate.getCreatedAt().equals(zeroTime)) {
                    //hand over to customer date
                    time = handedOverToCustomerDate.getCreatedAt();
                } else if (receivedAtStoreDate != null && receivedAtStoreDate.getStatus() != null && receivedAtStoreDate.getCreatedAt() != null && !receivedAtStoreDate.getCreatedAt().equals(zeroTime)) {
                    // Store delivery date + 3 days
                    time = addDays(receivedAtStoreDate.getCreatedAt(), 3);
                } else if (shippingStatusDetail != null && shippingStatusDetail.getComplete_time() != null
                        && !shippingStatusDetail.getComplete_time().equals(zeroTime)) {
                    // Dispatch date + 3 days
                    time = addDays(shippingStatusDetail.getComplete_time(), 3);
                } else {
                    // Order date + 7 days
                    time = addDays(uwOrder.getCreatedAt(), 7);
                }
            }


            // Return as ISO-8601 UTC String
            return toIsoUtcString(time);
        }catch (Exception e){
            log.error("[AiBotConsumer][getDeliveryDate] exception in finding deliveryDate:{}",e.getMessage());
            return "NA";
        }
    }

    // Utility: Add days to a Date
    private Date addDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        return cal.getTime();
    }

    // Utility: Convert Date to ISO-8601 UTC string
    private String toIsoUtcString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(date);
    }

    private ImagesToTextRequest createAutoApprovalBotRequest(AutoApprovalRequest autoApprovalRequest) {
        ImagesToTextRequest imagesToTextRequest= new ImagesToTextRequest();

        ReturnCreationRequestDTO returnCreationRequest = autoApprovalRequest.getReturnCreationRequest();
        ReturnCreationResponse refundRequestResponse = autoApprovalRequest.getRefundRequestResponse();


        if (returnCreationRequest != null && refundRequestResponse != null) {
            PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = returnCreationRequest.getPurchaseOrderDetailsDTO();
            UwOrderDTO uwOrder = purchaseOrderDetailsDTO.getUwOrders().stream()
                    .filter(uw -> Objects.equals(uw.getUwItemId(), returnCreationRequest.getItems().get(0).getUwItemId()))
                    .findFirst()
                    .orElse(null);

            imagesToTextRequest.setActualImageUrls(fetchActualImageUrls(uwOrder.getProductId()));
            imagesToTextRequest.setCompareImageUrls(fetchCompareImageUrls(returnCreationRequest.getItems().get(0).getMagentoId()));
            imagesToTextRequest.setMetaData(createMetaData(autoApprovalRequest,uwOrder));
        } else {
            log.error("[createAutoApprovalBotRequest] returnCreationRequest or refundRequestResponse is null");
        }


        return imagesToTextRequest;
    }

    public boolean postNeedApprovalDataAutoApprovalBot(ImagesToTextRequest imagesToTextRequest) {
        Date createdAt=new Date();
        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Application");

//            headers = addRefundAuthorizationHeaders(nuggetRequest, headers);

            HttpEntity<ImagesToTextRequest> httpEntity = new HttpEntity<>(imagesToTextRequest, headers);
            String url=baseUrlNugget+"/gemini/auto-approval-bot";
            log.info("[PostingDataToNuggetConsumer][postNeedApprovalDataToNugget] Calling nugget api for auto-approval creation, url:{}",url);
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            log.info("[PostingDataToNuggetConsumer][postNeedApprovalDataToNugget] Response from nugget api :{}", responseEntity.getBody());
            if(responseEntity.getStatusCode().is2xxSuccessful()){
                return processAutoApprovalBotResponse(responseEntity.getBody(),imagesToTextRequest,createdAt);
            }else{
                setBotAutoApproval(responseEntity.getBody(),imagesToTextRequest,imagesToTextRequest.getMessage(),"Technical Issue At Service End",createdAt);
            }
        } catch (Exception e){
            setBotAutoApproval(e.getMessage(),imagesToTextRequest,imagesToTextRequest.getMessage(),"Technical Issue while api call",createdAt);
            log.error("[PostingDataToNuggetConsumer][postNeedApprovalDataToNugget] Exception:{}", String.valueOf(e));
        }
        return false;
    }

    public boolean processAutoApprovalBotResponse(String response,ImagesToTextRequest imagesToTextRequest,Date createdAt){
        try {
            Map<String, String> resultMap = objectMapper.readValue(response, Map.class);
            String aiGeminiResponse = resultMap.get("aiResponse");
            String prompt = resultMap.get("finalPrompt");

            // Step 1: Remove ```json and ```
            String cleanedJson = aiGeminiResponse.replaceAll("(?s)```json|```", "").trim();

            // Step 2: Parse JSON
            ObjectMapper mapper = new ObjectMapper();
            JsonNode node = mapper.readTree(cleanedJson);

            // Step 3: Extract decision field
            String decision = node.get("decision").asText();
            log.info("Decision:{},id:{},timeTaken:{}", decision, imagesToTextRequest.getMetaData().get("magentoId"),resultMap.get("timeTaken"));

            return setBotAutoApproval(aiGeminiResponse,imagesToTextRequest,prompt,decision,createdAt);
        }catch (Exception e){
            log.error("error while mapping bot response:{}",e.getMessage());
        }
        return false;
    }

    private boolean setBotAutoApproval(String aiGeminiResponse,ImagesToTextRequest imagesToTextRequest,String prompt,String decision,Date createdAt){
        BotAutoApprovalResponse record = new BotAutoApprovalResponse();
        Map<String, String> metaData=imagesToTextRequest.getMetaData();

        record.setMetaData(metaData.toString());
        record.setMagentoId(metaData.get("magentoId"));
        record.setItemId(metaData.get("itemId"));
        record.setModelResult(aiGeminiResponse);
        record.setModelDecision(decision);
        record.setPrompt(prompt);
        record.setActualImage(imagesToTextRequest.getActualImageUrls().toString());
        record.setCustomerImage(imagesToTextRequest.getCompareImageUrls().toString());
        record.setModelName(imagesToTextRequest.getModel());
        record.setReasonForReturn(metaData.get("warrantyClaimReason"));
        record.setProductId(metaData.get("productId"));
        record.setUserComment(metaData.get("storeStaffComments"));
        record.setCreatedAt(createdAt);
        record.setUpdatedAt(new Date());
        botAutoApprovalRepository.save(record);
        return true;
    }

    public List<String> fetchActualImageUrls(int productId) {
        String url = urlProductImage + productId;
        try {
            ResponseEntity<JsonNode> response = restTemplate.getForEntity(url, JsonNode.class);
            JsonNode productImage = Objects.requireNonNull(response.getBody()).get("productImage");
            if (productImage != null && productImage.isArray()) {
                List<String> actualImages= StreamSupport.stream(productImage.spliterator(), false)
                        .map(JsonNode::asText)
                        .toList();
                List<String> filterImages=new ArrayList<>();
                filterImages.add(actualImages.get(0));
                filterImages.add(actualImages.get(1));
                filterImages.add(actualImages.get(2));
                return filterImages;
            }
        } catch (Exception e) {
            log.error("Failed for productId={} → {}", productId, e.getMessage());
        }
        return Collections.emptyList();
    }

    public List<String> fetchCompareImageUrls(Long magentoId) {
        //String url ="https://file-uploader-service.scm.preprod.lenskart.com/file/image/pos-return-approval/"+ magentoId; //preprod url
        String url = urlCustomerImage + magentoId; //prod url
        try {
            ResponseEntity<JsonNode> response = restTemplate.getForEntity(url, JsonNode.class);
            JsonNode array = response.getBody();
            if (array != null && array.isArray()) {
                return StreamSupport.stream(array.spliterator(), false)
                        .map(obj -> obj.get("fileLocation").asText())
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("Failed for magentoId={} → {}", magentoId, e.getMessage());
        }
        return Collections.emptyList();
    }

    public List<ModelPrompt> findAllModelPrompts(String group, int ttl, TimeUnit timeUnit) {

        List<ModelPrompt>  modelPrompts = null;
        String searchKey = "";
        if(!StringUtils.isEmpty(group)) {
            searchKey = group;
        }else {
            return null;
        }

        try {
            if(Boolean.TRUE.equals(redisTemplate.hasKey(searchKey))){
                log.info("[getSystemPreferenceValues] Fetching from cache searchKey : {}", searchKey);
                modelPrompts = (List<ModelPrompt>) redisTemplate.opsForValue().get(searchKey);
            }else{
                log.info("[getSystemPreferenceValues] Fetching from DB searchKey : "+searchKey);
                modelPrompts= modelPromptRepository.findAllActive();
                if(!org.springframework.util.CollectionUtils.isEmpty(modelPrompts)){
                    log.info("[getSystemPreferenceValues] Setting searchKey : {} and value : ", searchKey);
                    redisTemplate.opsForValue().set(searchKey,modelPrompts,ttl,timeUnit);
                }
            }
        }catch (Exception e){
            log.error("[getSystemPreferenceValues] Exception in config set {}",e.getMessage());
            modelPrompts= modelPromptRepository.findAllActive();
        }
        return modelPrompts;
    }

//    public List<String> fetchCompareImageUrls(Long magentoId) {
//        List<String> customerUploadedImages=new ArrayList<>();
//        String url = "https://static-fus-images-preprod.lenskart.com/" + magentoId;
//        customerUploadedImages.add(url+"_Left_View");
//        customerUploadedImages.add(url+"_Right_View");
//        customerUploadedImages.add(url+"_Temple_View");
//        return customerUploadedImages;
//    }

//    public <T> HttpHeaders addRefundAuthorizationHeaders(T request, HttpHeaders headers) {
//        String generatedRequestHashKey = null;
//        try {
//            generatedRequestHashKey = IRefundAuthorizationServiceImpl.createChecksum(nuggetSecretKey, objectMapper.writeValueAsString(request));
//            if (headers == null) {
//                headers = new HttpHeaders();
//            }
//
//            headers.set(SIGNATURE_KEY, generatedRequestHashKey);
//        } catch (JsonProcessingException e) {
//            log.error("Could not parse request body : {}", request);
//        }
//        return headers;
//    }

}
