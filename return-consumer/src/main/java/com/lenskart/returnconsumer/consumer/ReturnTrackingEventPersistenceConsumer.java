package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.dto.ReverseTrackingEventDTO;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ReturnTrackingEventPersistenceConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReverseTrackingEventService reverseTrackingEventService;

    @Autowired
    private IReverseCourierDetailService reverseCourierDetailService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private IReturnUpdateService returnUpdateService;

    @KafkaListener(id = "return-tracking-event-queue-2", topics = "${return.tracking.event.queue:reverse_tracking_event_returns}", groupId = "${return.tracking.event.queue.group:reverse_tracking_event_returns_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ReturnTrackingEventPersistenceConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[ReturnTrackingEventPersistenceConsumer][consume] exception found in listenConsumer1 : "+exception);
        }
    }

    private void processMessage(String message) {
        ReverseTrackingEventDTO reverseTrackingEventDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ReturnTrackingEventPersistenceConsumer][processMessage] empty message received");
            }else{
                reverseTrackingEventDTO = mapper.readValue(message, ReverseTrackingEventDTO.class);
                if(reverseTrackingEventDTO != null){
                    reverseTrackingEventService.persistTrackingEvent(reverseTrackingEventDTO);
                    if(reverseTrackingEventDTO.getTrackingStatusCode() == 10){
                        log.info("[ReturnTrackingEventPersistenceConsumer][processMessage] cancelling this return : {}", reverseTrackingEventDTO.getReturnId());
                        ReturnDetailUpdateRequestDto request = new ReturnDetailUpdateRequestDto();
                        request.setReturnId(reverseTrackingEventDTO.getReturnId());
                        request.setStatus(Constant.STATUS.CANCELLED);
                        request.setComments("got Order Has Been Cancelled remark from clickpost");
                        request.setUsername("clickpost");
                        returnUpdateService.updateReturnStatus(request);
                    }
                    kafkaService.pushToKafka("back_sync_rev_tracking_queue",String.valueOf(reverseTrackingEventDTO.getOrderId()),reverseTrackingEventDTO);
                    if(reverseTrackingEventDTO.getReverseAwb() != null){
                        reverseCourierDetailService.updateReverseCourierDetail(reverseTrackingEventDTO.getReturnId(), reverseTrackingEventDTO.getReverseAwb());
                    }
                }
            }
        }catch (Exception exception){
            log.error("[ReturnTrackingEventPersistenceConsumer][processMessage] exception found : "+exception);
        }
    }
}
