package com.lenskart.returnconsumer.consumer;

import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returnservice.service.ID365FinanceService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class SaleOrderReturnConsumer {

    private final ID365FinanceService d365FinanceService;

    private final ObjectMapper mapper;

    @KafkaListener(id = "fl-sale-order-return", topics = "${fl.saleOrderReturn.topic:fl_sale_order_return}", groupId = "${fl.saleOrderReturn.topic.group:fl_sale_order_return_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[SaleOrderReturnConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[SaleOrderReturnConsumer][consume] exception found in consume : " + exception);
        } finally {
            ack.acknowledge();
        }
    }

    protected void processMessage(String message) throws Exception {
        if (StringUtils.isEmpty(message)) {
            log.error("[SaleOrderReturnConsumer][processMessage] empty message received");
        } else {
            ReturnCreateRequest returnCreateRequest = mapper.readValue(message, ReturnCreateRequest.class);
            if (returnCreateRequest != null) {
                d365FinanceService.generatePayloadForReturn(returnCreateRequest);
            }
        }
    }
    
}
