package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.dto.OrdersHeaderDTO;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.ordermetadata.dto.request.OldRefundRequestCreateDTO;
import com.lenskart.returncommon.model.request.ReturnOrderRequestDTO;
import com.lenskart.ordermetadata.dto.response.CheckRefundSwitchActiveDTO;
import com.lenskart.ordermetadata.dto.response.CreateUpdateReturnOrderResponseDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.orderops.model.Amount;
import com.lenskart.refund.client.model.dto.RefundAmount;
import com.lenskart.refund.client.model.enums.*;
import com.lenskart.refund.client.model.request.CheckRefundInitiatedRequest;
import com.lenskart.refund.client.model.request.CreateRefundRequest;
import com.lenskart.refund.client.model.response.CheckRefundInitiatedResponse;
import com.lenskart.refund.client.model.response.RefundMethodResponse;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returncommon.predicate.RefundPredicate;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import com.lenskart.returnservice.service.impl.ReturnUtil;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.ITEM_WISE_PRICES_DTO;
import static com.lenskart.refund.client.model.enums.IdentifierType.ORDER_ID;
import static com.lenskart.returncommon.utils.Constant.PRODUCT_DELIVERY_TYPE.B2B;
import static com.lenskart.returncommon.utils.Constant.RETURN_ORDER_STATUS.*;
import static com.lenskart.returncommon.utils.Constant.RETURN_STATUS.RETURN_REFUNDED;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.CREATE_REFUND_REQUEST_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.REFUND_INITIATION_ON_RECEIVING_QUEUE;
import static com.lenskart.returncommon.utils.Constant.RETURN_TYPE.REVERSE;

@Slf4j
@Component
public class CheckRefundSwitchActiveConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    IReturnOrderItemService returnOrderItemService;

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @Autowired
    IOrderUtilsService orderUtilsService;

    @Autowired
    private ID365FinanceService id365FinanceService;

    @Autowired
    private IReturnUpdateService returnUpdateService;

    @Autowired
    private ReturnUtil returnUtil;

    public final static List<String> pending_approval_excluded_methods = List.of("marketplace","cod","cashondelivery");

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));


    @KafkaListener(id = "check-refund-active-consumer-1", topics = "${check.refund.active.queue:check_refund_active_queue}", groupId = "${check.refund.active.queue.group:check_refund_active_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[CheckRefundSwitchActiveConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[CheckRefundSwitchActiveConsumer][consume] exception found in listenConsumer1 : " + exception);
        }finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message) {
        log.info("[CheckRefundSwitchActiveConsumer][processMessage] Processing message : {}", message);

        if (StringUtils.isBlank(message)) {
            log.error("Message is blank");
            return;
        }

        try {
            CheckRefundSwitchActiveRequestDTO checkRefundSwitchActiveRequestDTO = mapper.readValue(message, CheckRefundSwitchActiveRequestDTO.class);
            processRequest(checkRefundSwitchActiveRequestDTO);
        } catch (JsonProcessingException | InterruptedException | ExecutionException e) {
            log.error("[CheckRefundSwitchActiveConsumer][processMessage] error occurred : "+e);
        }

    }

    private void processRequest(CheckRefundSwitchActiveRequestDTO checkRefundSwitchActiveRequestDTO) throws ExecutionException, InterruptedException {
        log.info("[CheckRefundSwitchActiveConsumer][processRequest] return : {} , checkRefundSwitchActiveRequestDTO : {}", checkRefundSwitchActiveRequestDTO.getReturnId(), checkRefundSwitchActiveRequestDTO);
        ResponseEntity<CheckRefundSwitchActiveDTO> checkRefundSwitchActiveDTOResponseEntity = orderOpsFeignClient.checkRefundSwitchActive(ORDER_ID.name(), String.valueOf(checkRefundSwitchActiveRequestDTO.getIncrementId()));
        log.info("[CheckRefundSwitchActiveConsumer][processRequest] checkRefundSwitchActiveDTOResponseEntity : {}", checkRefundSwitchActiveDTOResponseEntity);
        if(null!=checkRefundSwitchActiveDTOResponseEntity && checkRefundSwitchActiveDTOResponseEntity.getStatusCode().is2xxSuccessful()){
            boolean refundServiceSwitchFlag = checkRefundSwitchActiveDTOResponseEntity.getBody() != null && checkRefundSwitchActiveDTOResponseEntity.getBody().isNewRefundFlow();
            log.info("[CheckRefundSwitchActiveConsumer][processRequest] createNewReturn : {} , refundServiceSwitchFlag : {}", checkRefundSwitchActiveRequestDTO.isCreateNewReturn(), refundServiceSwitchFlag);
            if(refundServiceSwitchFlag && checkRefundSwitchActiveRequestDTO.isCreateNewReturn()){
                Integer customerReturnId = checkRefundSwitchActiveRequestDTO.getReturnId();
                log.info("[CheckRefundSwitchActiveConsumer][processRequest] getProductDeliveryType : {}", checkRefundSwitchActiveRequestDTO.getUwOrder().getProductDeliveryType());
                if(B2B.equalsIgnoreCase(checkRefundSwitchActiveRequestDTO.getUwOrder().getProductDeliveryType())){
                    customerReturnId = returnOrderItemService.getReturnId(checkRefundSwitchActiveRequestDTO.getUwOrder().getB2bRefrenceItemId());
                }
                log.info("[CheckRefundSwitchActiveConsumer][processRequest] return : {} , customerReturnId {} , source : {}", checkRefundSwitchActiveRequestDTO.getReturnId(), customerReturnId, checkRefundSwitchActiveRequestDTO.getReturnRequest().getSource());
                createAndInitiateRefundRequestOnReturn(customerReturnId, checkRefundSwitchActiveRequestDTO.getReturnRequest(), checkRefundSwitchActiveRequestDTO.getUwOrder());
            }
            if(refundServiceSwitchFlag){
                returnRefundOperationsV2(checkRefundSwitchActiveRequestDTO.getReturnId());
            }else{
                returnRefundOperationsV1(checkRefundSwitchActiveRequestDTO.getReturnId(), checkRefundSwitchActiveRequestDTO.getReturnRequest(), checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse());
            }

            if(null != checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse()){
                if(RETURN_REFUNDED.equalsIgnoreCase(checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getReturnStatus())){
                    log.info("[CheckRefundSwitchActiveConsumer] calling backsync api for "+checkRefundSwitchActiveRequestDTO.getIncrementId()+" status is "+RETURN_REFUNDED);
                    backSync(checkRefundSwitchActiveRequestDTO.getIncrementId(), Collections.singletonList(checkRefundSwitchActiveRequestDTO.getUwOrder().getUwItemId()), RETURN_REFUNDED);
                    if(REVERSE.equalsIgnoreCase(checkRefundSwitchActiveRequestDTO.getReturnType())){
                        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
                        returnCreateRequest.setUwItemId(checkRefundSwitchActiveRequestDTO.getUwOrder().getUwItemId());
                        id365FinanceService.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
                        id365FinanceService.createReturnEInvoice(checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getReturnId(), checkRefundSwitchActiveRequestDTO.getUwOrder().getUwItemId(), checkRefundSwitchActiveRequestDTO.getUwOrder().getFacilityCode());
                        pushReturnEInvoiceForVirtualReturn(checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getVirtualReturnId(), checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getVirtualUWItemId(),
                                checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getFacilityCode());
                    }
                }
                if(RETURN_RECEIVED.equalsIgnoreCase(checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getReturnStatus())){
                    log.info("[CheckRefundSwitchActiveConsumer] calling backsync api for "+checkRefundSwitchActiveRequestDTO.getReturnRequest().getIncrementId()+" status is "+RETURN_RECEIVED);
                    backSync(checkRefundSwitchActiveRequestDTO.getIncrementId(), Collections.singletonList(checkRefundSwitchActiveRequestDTO.getUwOrder().getUwItemId()), RETURN_RECEIVED);
                    if(REVERSE.equalsIgnoreCase(checkRefundSwitchActiveRequestDTO.getReturnType())){
                        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
                        returnCreateRequest.setUwItemId(checkRefundSwitchActiveRequestDTO.getUwOrder().getUwItemId());
                        id365FinanceService.sendDataToFinanceConsumerForAwbReturn(returnCreateRequest);
                        id365FinanceService.createReturnEInvoice(checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getReturnId(), checkRefundSwitchActiveRequestDTO.getUwOrder().getUwItemId(), checkRefundSwitchActiveRequestDTO.getUwOrder().getFacilityCode());
                        pushReturnEInvoiceForVirtualReturn(checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getVirtualReturnId(), checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getVirtualUWItemId(),
                                checkRefundSwitchActiveRequestDTO.getCreateUpdateReturnOrderResponse().getFacilityCode());
                    }
                }
            }
        }
    }

    private void pushReturnEInvoiceForVirtualReturn(Integer returnId, Integer uwItemId, String facilityCode) {
        log.info("[pushReturnEinvoiceForVirtualReturn] pushing return invoice for virtual returnId: {} ", returnId);
        id365FinanceService.createReturnEInvoice(returnId, uwItemId, facilityCode);
    }

    private void createAndInitiateRefundRequestOnReturn(Integer returnId, ReturnOrderRequestDTO returnRequest, UwOrderDTO uwOrderDTO) throws ExecutionException, InterruptedException {
        String method;
        if (CollectionUtils.isNotEmpty(returnRequest.getOrderDTOs())) {
            method = returnRequest.getOrderDTOs().get(0).getMethod();
        } else {
            method = null;
        }
        log.info("[createAndInitiateRefundRequestOnReturn] returnId : {} method : {}", returnId, method);
        OrdersHeaderDTO ordersHeaderDTO = returnRequest.getOrdersHeaderDTO();

        CompletableFuture<Amount> amountCompletableFuture = CompletableFuture.supplyAsync(() -> getRefundAmount(uwOrderDTO.getIncrementId(), returnId));
        CompletableFuture<String> refundMethodCompletableFuture = CompletableFuture.supplyAsync(() -> getRefundMethod(uwOrderDTO, returnRequest, ordersHeaderDTO));
        CompletableFuture<Boolean> isFranchiseRefundRequiredCompletableFuture = CompletableFuture.supplyAsync(() -> validateForFranchiseRefund(returnId, method, uwOrderDTO.getItemId(), ordersHeaderDTO.getIsBulkOrder(), uwOrderDTO.getIncrementId(), uwOrderDTO.getUwItemId()));

        CompletableFuture.allOf(amountCompletableFuture, refundMethodCompletableFuture, isFranchiseRefundRequiredCompletableFuture).join();

        Amount amount = amountCompletableFuture.get();
        String refundMethod = refundMethodCompletableFuture.get();
        Boolean isFranchiseRefundRequired = isFranchiseRefundRequiredCompletableFuture.get();

        log.info("[createAndInitiateRefundRequestOnReturn] returnId : {} refundMethod : {} isFranchiseRefundRequired : {}", returnId, refundMethod, isFranchiseRefundRequired);

        if (refundMethod != null || isFranchiseRefundRequired) {
            CreateRefundRequest createRefundRequest = new CreateRefundRequest();
            createRefundRequest.setIdentifierType(com.lenskart.refund.client.model.enums.IdentifierType.RETURN_ID);
            createRefundRequest.setIdentifierValue(String.valueOf(returnId));
            createRefundRequest.setRefundReason(RefundReason.DIRECT_RECEIVING);
            createRefundRequest.setOrderId((long) ordersHeaderDTO.getIncrementId());
            if (refundMethod != null) {
                createRefundRequest.setRefundAmount(new RefundAmount(BigDecimal.valueOf(amount.getPrice()), amount.getCurrency()));
                createRefundRequest.setRefundTarget(RefundTarget.getByValue(refundMethod));
            } else {
                createRefundRequest.setRefundTarget(RefundTarget.FRANCHISE);
                createRefundRequest.setRefundAmount(RefundAmount.rupees(BigDecimal.ZERO));
            }
            createRefundRequest.setType(RefundType.CREDIT);
            createRefundRequest.setRefundTriggerPoint(RefundTriggerPoint.WH_RECEIVING.getName());
            createRefundRequest.setFranchiseRefundRequired(isFranchiseRefundRequired);
            createRefundRequest.setClient(returnRequest.getSource());
            log.info("[createAndInitiateRefundRequestOnReturn] pushing to REFUND_INITIATE_QUEUE source : {} createRefundRequest : {}", returnRequest.getSource(), createRefundRequest);

            kafkaService.pushToKafka(REFUND_INITIATION_ON_RECEIVING_QUEUE, String.valueOf(returnId), createRefundRequest);
        }

        if(!"marketplace".equals(method) && refundMethod == null){
            log.info("[createAndInitiateRefundRequestOnReturn] updating RETURN_PENDING_APPROVAL for returnId : {}", returnId);
            ReturnDetailUpdateRequestDto request = new ReturnDetailUpdateRequestDto();
            request.setReturnId(returnId);
            request.setStatus(RETURN_PENDING_APPROVAL);
            ReturnDetailsUpdateResponse returnDetailsUpdateResponse = returnUpdateService.updateReturnStatus(request);
            log.info("[createAndInitiateRefundRequestOnReturn] returnDetailsUpdateResponse : {} returnId : {}", returnDetailsUpdateResponse, returnId);
        }
    }

    private boolean validateForFranchiseRefund(Integer returnId, String method, Integer itemId, boolean isBulk_Order, Integer incrementId, Integer uwItemId) {

        //check if Bulk order
        log.info("[validateForFranchiseRefund] isBulk_Order: {}, uwItemId :{} ",isBulk_Order,uwItemId);
        if(isBulk_Order) return false;


        //check if to be refund amount is 0.
        log.info("[validateForFranchiseRefund] Checking refund amount for uwItemId: {}", uwItemId);
        Double refundAmount = orderOpsFeignClient.getRefundAmount(incrementId, List.of(uwItemId)).getBody();
        if (Double.compare(refundAmount, 0.0) == 0) {
            log.info("[validateForFranchiseRefund] Refund amount is zero for uwItemId: {}", uwItemId);
            return false;
        }

        boolean isCodWithSC = false;
        if ("cashondelivery".equalsIgnoreCase(method) || "cod".equalsIgnoreCase(method)) {
            boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
            ResponseEntity<OrderInfoResponseDTO> orderDetailsEntity = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(ITEM_WISE_PRICES_DTO))
                    : orderOpsFeignClient.getOrderDetails(incrementId);
            if (orderDetailsEntity.hasBody()) {
                OrderInfoResponseDTO orderInfoResponseDTO = orderDetailsEntity.getBody();
                if (orderInfoResponseDTO != null && CollectionUtils.isNotEmpty(orderInfoResponseDTO.getItemWisePrices())) {
                    isCodWithSC = orderInfoResponseDTO.getItemWisePrices().stream().anyMatch(i -> itemId.equals(i.getItemId()) && i.getStoreCreditDiscount().compareTo(0.0) > 0);
                    log.info("[validateForFranchiseRefund] isCodWithSC: {}, uwItemId: {}",isCodWithSC,uwItemId);
                    if (isCodWithSC) {
                        return true;
                    }
                }
            }
        }

        // Check if the method is valid
        boolean validMethod = !pending_approval_excluded_methods.contains(method);
        log.info("[validateForFranchiseRefund] validMethod: {}, uwItemId: {} ",validMethod,uwItemId);

        if (!validMethod) {
            return false;
        }

        String refundStatus = refundUtilsService.getRefundRequestStatus(uwItemId);

        RefundIntentDTO refundIntentDTO = refundUtilsService.getRefundMethodIntentByCustomer(returnId);
        // Check if both refund initiated responses are null
        boolean isCustomerIntentEmpty = refundStatus == null && refundIntentDTO == null;
        log.info("[validateForFranchiseRefund] isCustomerIntentEmpty: {}, method: {}",isCustomerIntentEmpty, method);

        return isCustomerIntentEmpty;
    }

    private Amount getRefundAmount(Integer incrementId, Integer returnId){
        try {
            return orderOpsFeignClient.getRefundAmount(incrementId, returnId);
        } catch (FeignException e) {
            log.error("Error fetching refund amount: {}", e.getMessage());
            return new Amount();
        }
    }

    private String getRefundMethod(UwOrderDTO uwOrderDTO, ReturnOrderRequestDTO returnOrderRequest, OrdersHeaderDTO ordersHeaderDTO) {
        OrdersDTO ordersDTO = returnOrderRequest.getOrderDTOs()
                .stream()
                .filter(ord -> Objects.equals(ord.getItemId(), uwOrderDTO.getItemId()))
                .findFirst()
                .orElse(null);
        String paymentMethod = getPaymentMethod(uwOrderDTO, returnOrderRequest, ordersDTO != null ? ordersDTO.getMethod() : null);

        String refundInitiatedAt = RefundInitiatedAt.DIRECT_RECEIVING.getValue();
        if(ordersHeaderDTO.getIsExchangeOrder()!=null && ordersHeaderDTO.getIsExchangeOrder()){
            refundInitiatedAt = RefundInitiatedAt.EXCHANGE_DIRECT_RECEIVING.getValue();
        }

        //call get refund methods api
        RefundMethodResponse refundMethodResponse = refundUtilsService.getRefundMethod(refundInitiatedAt, ordersHeaderDTO.getPaymentMode(),
                paymentMethod, ordersHeaderDTO.getLkCountry(), ordersHeaderDTO.getIncrementId());

        return refundMethodResponse != null ? refundMethodResponse.getAutoRefundMethod() : null;
    }

    private boolean getIsFranchiseRefundRequired(Integer incrementId, String source){
        return refundUtilsService.getIsFranchiseRefundRequired(incrementId, RefundTriggerPoint.WH_RECEIVING, source, RefundReason.DIRECT_RECEIVING);
    }

    private String getPaymentMethod(UwOrderDTO uwOrderDTO, ReturnOrderRequestDTO returnOrderRequest, String orderMethod) {
        return orderUtilsService.getPaymentMethod(uwOrderDTO, returnOrderRequest.getUwOrderDTOs(), returnOrderRequest.getOrderDTOs(), orderMethod);
    }

    private void returnRefundOperationsV2(Integer returnId) {
        log.info("[returnRefundOperationsV2] returnId {} ", returnId);
        try {
            returnOrderItemService.updateReturnOrderItem(returnId, "initiated_stockin");

            CheckRefundInitiatedRequest checkRefundInitiatedRequest=new CheckRefundInitiatedRequest();
            checkRefundInitiatedRequest.setIdentifierType(com.lenskart.refund.client.model.enums.IdentifierType.RETURN_ID);
            checkRefundInitiatedRequest.setIdentifierValue(String.valueOf(returnId));
            CheckRefundInitiatedResponse checkRefundInitiatedResponse= refundUtilsService.isRefundInitiated(checkRefundInitiatedRequest);
            if(checkRefundInitiatedResponse==null || checkRefundInitiatedResponse.getRefundRequestDTO()==null || checkRefundInitiatedResponse.getRefundRequestDTO().getId()==null){
                log.error("returnRefundOperationsV2 checkRefundInitiatedResponse={}",checkRefundInitiatedResponse);
                return;
            }

            RefundRequestStatus refundStatus = checkRefundInitiatedResponse.getRefundRequestDTO().getStatus();
            RefundTarget refundTarget = checkRefundInitiatedResponse.getRefundRequestDTO().getRefundTarget();
            if (RefundRequestStatus.PROCESSED.equals(refundStatus) || RefundTarget.EXCHANGE.equals(refundTarget)) {
                Optional<ReturnDetail> returnOrder = returnOrderActionService.findReturnOrderById(returnId);
                String returnOrderStatus = null;
                if(returnOrder.isPresent()){
                    returnOrderStatus = returnOrderActionService.getReturnOrderStatus(returnOrder.get());
                }
                if (returnOrder.isPresent() && !RefundPredicate.IS_RETURN_IN_DRAFTED_REJECTED_STATE.test(returnOrderStatus)
                        && RefundPredicate.IS_RETURN_NOT_IN_RPU_CYCLE.test(returnOrderStatus)) {
                    String status = RefundTarget.EXCHANGE.equals(refundTarget) ? Constant.RETURN_ORDER_STATUS.RETURN_EXCHANGE : Constant.RETURN_ORDER_STATUS.RETURN_REFUNDED;
                    log.info("[returnRefundOperationsV2] status to update is " + refundStatus);
                    Integer requestId = returnEventService.getRequestId(returnId);
                    String returnStatus = returnOrderActionService.getReturnOrderStatusById(returnId);
                    if(!status.equalsIgnoreCase(returnStatus)){
                        returnEventService.createReturnEvent(requestId, returnId, status, "");
                    }
                    log.info("[returnRefundOperationsV2] return order status is ===---========-----===== " + status);
                }
            }
        }
        catch (Exception e) {
            log.error("[returnRefundOperationsV2] Exception {}", e.getMessage());
        }
    }

    private void returnRefundOperationsV1(Integer returnId, ReturnOrderRequestDTO returnRequest, CreateUpdateReturnOrderResponseDTO createUpdateReturnOrderResponse) {
        log.info("[returnRefundOperationsV1] returnId : {}, incrementId : {}", returnId, returnRequest.getIncrementId());
        OldRefundRequestCreateDTO oldRefundRequestCreateDTO = new OldRefundRequestCreateDTO();
        oldRefundRequestCreateDTO.setReturnId(returnId);
        oldRefundRequestCreateDTO.setDoRefund(returnRequest.getDoRefund());
        oldRefundRequestCreateDTO.setSource(returnRequest.getSource());
        oldRefundRequestCreateDTO.setRefundMethod(returnRequest.getRefundMethod());
        oldRefundRequestCreateDTO.setIncrementId(returnRequest.getIncrementId());
        oldRefundRequestCreateDTO.setAwaitedRtoItem(false);
        oldRefundRequestCreateDTO.setReturnStatus(createUpdateReturnOrderResponse.getReturnStatus());
        try{
            log.info("[returnRefundOperationsV1] request dto : {}", oldRefundRequestCreateDTO);
            kafkaService.pushToKafka(CREATE_REFUND_REQUEST_QUEUE,String.valueOf(returnId),oldRefundRequestCreateDTO);
        }catch (Exception exception){
            log.error("[returnRefundOperationsV1] exception occurred : "+exception);
        }
    }

    private void backSync(Integer incrementId, List<Integer> uwItemIds, String status){
        log.info("[backSync] order : {}, status : {}, uwItemIds : {}", incrementId, status, uwItemIds);
        try{
            CompletableFuture<Void> backSyncCompletableFuture = CompletableFuture.runAsync(() -> orderOpsFeignClient.callBackSyncApi(uwItemIds, status));
            backSyncCompletableFuture
                    .thenAccept(body -> {
                        log.info("[backSync] Response received: " + body);
                    })
                    .exceptionally(ex -> {
                        ex.printStackTrace();
                        return null;
                    });
        }catch (Exception exception){
            log.error("[backSync] exception : "+exception);
        }
    }

}
