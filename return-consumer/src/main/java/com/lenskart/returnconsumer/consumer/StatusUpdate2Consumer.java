package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.orderops.model.OrderStatusUpdateDetails;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class StatusUpdate2Consumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "status-update-2", topics = "${status.update.topic:status_update}", groupId = "${uworders.status.update.topic.group:status_update_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[StatusUpdateConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[StatusUpdateConsumer][consume] exception found in listenConsumer1 : " + exception);
        }
    }

    protected void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[StatusUpdateConsumer][processMessage] empty message received");
        } else {
            OrderStatusUpdateDetails orderStatusUpdateDetails = mapper.readValue(message, OrderStatusUpdateDetails.class);
            if (orderStatusUpdateDetails != null) {
                orderOpsFeignClient.statusUpdate(orderStatusUpdateDetails);
            }
        }
    }

}
