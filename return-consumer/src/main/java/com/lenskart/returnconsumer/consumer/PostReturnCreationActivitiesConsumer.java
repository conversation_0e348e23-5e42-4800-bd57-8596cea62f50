package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.PostReturnCreationActivityDTO;
import com.lenskart.returnservice.service.IReturnCreationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PostReturnCreationActivitiesConsumer {

    @Autowired
    private IReturnCreationService returnCreationService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "post-return-creation-activities", topics = "${post.return.creation.activities.topic:post_return_creation_activities_queue}", groupId = "${post.return.creation.activities.topic.group:post_return_creation_activities_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[PostReturnCreationActivitiesConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            log.info("[OrderStatusHistoryConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            message = mapper.readValue(message, String.class);
            processMessage(message);
        } catch (Exception exception) {
            log.error("[PostReturnCreationActivitiesConsumer][consume] exception found in consume : " + exception);
        }finally {
            ack.acknowledge();
        }
    }

    protected void processMessage(String message) throws Exception {
        if (StringUtils.isEmpty(message)) {
            log.error("[PostReturnCreationActivitiesConsumer][processMessage] empty message received");
        } else {
            PostReturnCreationActivityDTO postReturnCreationActivityDTO = mapper.readValue(message, PostReturnCreationActivityDTO.class);
            if (postReturnCreationActivityDTO != null) {
                returnCreationService.performPostReturnCreationActivities(postReturnCreationActivityDTO);
            }
        }
    }

}
