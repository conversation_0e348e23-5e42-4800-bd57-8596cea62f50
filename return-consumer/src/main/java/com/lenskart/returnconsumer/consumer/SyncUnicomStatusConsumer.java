package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ExchangeOrderUnholdRequest;
import com.lenskart.returncommon.model.dto.SyncUnicomStatusDTO;
import com.lenskart.returnservice.service.IExchangeOrderService;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.ISyncUnicomStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import static com.lenskart.returncommon.utils.Constant.EVENT.EXCHANGE_ORDER_UNHOLDED;

@Slf4j
@Service
public class SyncUnicomStatusConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private ISyncUnicomStatusService syncUnicomStatusService;

    @KafkaListener(id = "syn-unicom-status-queue-1", topics = "${sync.unicom.status.queue:Sync_unicom_status_queue}", groupId = "${sync.unicom.status.queue.group:Sync_unicom_status_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[SyncUnicomStatusConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[SyncUnicomStatusConsumer][consume] exception found in listenConsumer1 : "+exception);
        }
    }

    private void processMessage(String message) {
        SyncUnicomStatusDTO syncUnicomStatusDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[SyncUnicomStatusConsumer][processMessage] empty message received");
            }else{
                syncUnicomStatusDTO = mapper.readValue(message, SyncUnicomStatusDTO.class);
                syncUnicomStatusService.syncUnicomStatus(syncUnicomStatusDTO);
            }
        }catch (Exception exception){
            log.error("[SyncUnicomStatusConsumer][processMessage] exception found : "+exception);
        }
    }
}
