package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.refund.client.model.request.CreateRefundRequest;
import com.lenskart.returnservice.service.IRefundUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RefundInitiateConsumer {

    @Autowired
    private IRefundUtilsService refundUtilsService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "refund-initiate-queue", topics = "${refund.initiate.on.receiving.topic:refund_initiate_on_receiving_queue}", groupId = "${refund.initiate.on.receiving.topic.group:refund_initiate_on_receiving_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[RefundInitiateConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[RefundInitiateConsumer][consume] exception found in RefundInitiateConsumer : " + exception);
        }
    }

    private void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[RefundInitiateConsumer][processMessage] empty message received");
        } else {
            log.info("[RefundInitiateConsumer][processMessage] before createRefundRequest");
            CreateRefundRequest createRefundRequest = mapper.readValue(message, CreateRefundRequest.class);
            log.info("[RefundInitiateConsumer][processMessage] createRefundRequest :{}", createRefundRequest);
            if (createRefundRequest != null) {
                refundUtilsService.initiateRefundRequest(createRefundRequest);
            }
        }
    }
}
