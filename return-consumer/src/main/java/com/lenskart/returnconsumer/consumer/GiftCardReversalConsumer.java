package com.lenskart.returnconsumer.consumer;

import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.request.ReturnCreateRequest;
import com.lenskart.returnservice.service.impl.GiftCardReversal;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class GiftCardReversalConsumer {

    private final GiftCardReversal giftCardReversal;

    private ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "fl-gift-card-reversal", topics = "${fl.giftCardReversal.topic:fl_gift_card_reversal}", groupId = "${fl.giftCardReversal.topic.group:fl_gift_card_reversal_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[GiftCardReversalConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[GiftCardReversalConsumer][consume] exception found in consume : " + exception);
        } finally {
            ack.acknowledge();
        }
    }

    protected void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[GiftCardReversalConsumer][processMessage] empty message received");
        } else {
            ReturnCreateRequest giftCardEligibilityRequestDto = mapper.readValue(message, ReturnCreateRequest.class);
            if (giftCardEligibilityRequestDto != null) {
                giftCardReversal.checkIfGiftCardApplicableForUwItems(giftCardEligibilityRequestDto);
            }
        }
    }
    
}
