package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.lenskart.returncommon.model.request.NonPickupCustomerFeedback;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.service.IPickupReAttemptService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
public class CustomerFeedbackMessageProcessor {
    @Autowired
    IPickupReAttemptService pickupReAttemptService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = Constant.RETURN_TOPICS.IVR_RESPONSE_QUEUE, topics = "${kafka.topic.ivrResponse.name.queue:ivr-customer-feedback}", groupId = "${kafka.topic.ivrResponse.name.group:ivr-customer-feedback}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void processMessage(String message) {

        MDC.put("traceId", UUID.randomUUID().toString());
        log.info("[CustomerFeedbackMessageProcessor] with message:{}", message);

        if (Strings.isNullOrEmpty(message)) {
            log.error("consumed message is empty or null");
        }
        NonPickupCustomerFeedback nonPickupCustomerFeedback = null;
        try {
            message = mapper.readValue(message, String.class);
            nonPickupCustomerFeedback = mapper.readValue(message, NonPickupCustomerFeedback.class);
            pickupReAttemptService.processCustomerFeedback(nonPickupCustomerFeedback);
        } catch (Exception e) {
            log.info("[CustomerFeedbackMessageProcessor] exception while processing nonPickupCustomerFeedback : "+e);
            //throw new RuntimeException(e);
        } finally {
            MDC.clear();
        }

    }
}
