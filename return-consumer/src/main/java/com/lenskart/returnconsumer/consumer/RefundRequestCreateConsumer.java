package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.OldRefundRequestCreateDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RefundRequestCreateConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "create-refund-request-queue", topics = "${create.refund.request.topic:create-refund-request-queue}", groupId = "${create.refund.request.topic.group:create_refund_request_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[RefundRequestCreateConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
//            message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[RefundRequestCreateConsumer][consume] exception found in RefundRequestCreateConsumer : " + exception);
        }
    }

    private void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[RefundRequestCreateConsumer][processMessage] empty message received");
        } else {
            OldRefundRequestCreateDTO oldRefundRequestCreateDTO = mapper.readValue(message, OldRefundRequestCreateDTO.class);
            if (oldRefundRequestCreateDTO != null) {
                orderOpsFeignClient.createRefundRequest(oldRefundRequestCreateDTO);
            }
        }
    }

}
