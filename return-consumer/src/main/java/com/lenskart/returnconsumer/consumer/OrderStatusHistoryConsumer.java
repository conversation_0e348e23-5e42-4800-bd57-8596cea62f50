package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class OrderStatusHistoryConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "order-status-history-1", topics = "${order.status.history.topic:order_status_history_queue}", groupId = "${orders.status.history.topic.group:order_status_history_queue}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[OrderStatusHistoryConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[OrderStatusHistoryConsumer][consume] exception found in listenConsumer1 : " + exception);
        }
    }

    protected void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[OrderStatusHistoryConsumer][processMessage] empty message received");
        } else {
            OrderCommentReq orderCommentReq = mapper.readValue(message, OrderCommentReq.class);
            if (orderCommentReq != null) {
                orderOpsFeignClient.addOrderComment(orderCommentReq);
            }
        }
    }

}
