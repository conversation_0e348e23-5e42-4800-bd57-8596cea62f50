package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ReturnTrackingEventDTO;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AthenaTrackingEventConsumer {

    @Autowired
    private IReturnEventService returnEventService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));


    @KafkaListener(id = "athena-tracking-event-queue", topics = "${athena.tracking.event.queue:athena-tracking-event-queue}", groupId = "${athena-tracking-event-queue.group:athena-tracking-event-queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[AthenaTrackingEventConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[AthenaTrackingEventConsumer][consume] exception found : "+exception);
        }
    }

    private void processMessage(String message) {
        ReturnTrackingEventDTO returnTrackingEventDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[AthenaTrackingEventConsumer][processMessage] empty message received");
            }else{
                returnTrackingEventDTO = mapper.readValue(message, ReturnTrackingEventDTO.class);
                if(returnTrackingEventDTO != null){
                    returnEventService.createAthenaTrackingEvent(returnTrackingEventDTO);
                }
            }
        }catch (Exception exception){
            log.error("[AthenaTrackingEventConsumer][processMessage] exception found : "+exception);
        }
    }
}
