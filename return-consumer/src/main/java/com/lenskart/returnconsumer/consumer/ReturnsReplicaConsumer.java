package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returnservice.service.IReturnsReplicaHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ReturnsReplicaConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnsReplicaHandlerService returnsReplicaHandlerService;

    @KafkaListener(id = "returns-replica-1", topics = "${returns.replica.queue:returns_replica}", groupId = "${returns.replica.queue.group:returns_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReturnsReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReturnsReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReturnsReplica] exception found in consumeReturnsReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message) {
        try{
            JSONObject payload = new JSONObject(message);
            returnsReplicaHandlerService.handleReturnsReplica(payload);
        }catch (Exception exception){
            log.error("[ReturnsReplicaConsumer][processMessage] error : ", exception);
        }
    }

    @KafkaListener(id = "returns-replica-2", topics = "${return.order.replica.queue:return_order_replica_queue}", groupId = "${return.order.replica.queue.group:return_order_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReturnOrderReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReturnOrderReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            returnsReplicaHandlerService.handleReturnOrderReplica(new JSONObject(message));
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReturnOrderReplica] exception found in consumeReturnOrderReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }

    @KafkaListener(id = "returns-replica-3", topics = "${return.order.item.replica.queue:return_order_item_replica_queue}", groupId = "${return.order.item.replica.queue.group:return_order_item_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReturnOrderItemReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReturnOrderItemReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            returnsReplicaHandlerService.handleReturnOrderItemReplica(new JSONObject(message));
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReturnOrderItemReplica] exception found in consumeReturnOrderItemReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }

    @KafkaListener(id = "returns-replica-4", topics = "${return.order.address.update.replica.queue:return_order_address_update_replica_queue}", groupId = "${return.order.address.update.replica.queue.group:return_order_address_update_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReturnOrderAddressUpdateReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReturnOrderAddressUpdateReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            returnsReplicaHandlerService.handleReturnOrderAddressUpdateReplica(new JSONObject(message));
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReturnOrderAddressUpdateReplica] exception found in consumeReturnOrderAddressUpdateReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }

    @KafkaListener(id = "returns-replica-5", topics = "${return.history.replica.queue:return_history_replica_queue}", groupId = "${return.history.replica.queue.group:return_history_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReturnHistoryReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReturnHistoryReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            returnsReplicaHandlerService.handleReturnHistoryReplica(new JSONObject(message));
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReturnHistoryReplica] exception found in consumeReturnHistoryReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }

    @KafkaListener(id = "returns-replica-6", topics = "${return.reason.replica.queue:return_reason_replica_queue}", groupId = "${return.reason.replica.queue.group:return_reason_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReturnReasonReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReturnReasonReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            returnsReplicaHandlerService.handleReturnReasonReplica(new JSONObject(message));
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReturnReasonReplica] exception found in consumeReturnReasonReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }

//    @KafkaListener(id = "returns-replica-7", topics = "${refund.rules.replica.queue:refund_rules_replica_queue}", groupId = "${refund.rules.replica.queue.group:refund_rules_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
//    public void consumeRefundRulesReplica(String message, Acknowledgment ack){
//        try {
//            log.info("[ReturnsReplicaConsumer][consumeRefundRulesReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
//            returnsReplicaHandlerService.handleRefundRulesReplica(new JSONObject(message));
//        } catch (Exception exception) {
//            log.error("[ReturnsReplicaConsumer][consumeRefundRulesReplica] exception found in consumeRefundRulesReplica : ", exception);
//        } finally {
//            ack.acknowledge();
//        }
//    }

    @KafkaListener(id = "returns-replica-8", topics = "${reverse.pickup.pincode.replica.queue:reverse_pickup_pincode_replica_queue}", groupId = "${reverse.pickup.pincode.replica.queue.group:reverse_pickup_pincode_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReversePickupPincodeReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReversePickupPincodeReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            returnsReplicaHandlerService.handleReversePickupPincodeReplica(new JSONObject(message));
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReversePickupPincodeReplica] exception found in consumeReversePickupPincodeReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }

    @KafkaListener(id = "returns-replica-9", topics = "${reverse.courier.mapping.replica.queue:reverse_courier_mapping_replica_queue}", groupId = "${reverse.courier.mapping.replica.queue.group:reverse_courier_mapping_replica_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReverseCourierMappingReplica(String message, Acknowledgment ack){
        try {
            log.info("[ReturnsReplicaConsumer][consumeReverseCourierMappingReplica] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            returnsReplicaHandlerService.handleReverseCourierMappingReplica(new JSONObject(message));
        } catch (Exception exception) {
            log.error("[ReturnsReplicaConsumer][consumeReverseCourierMappingReplica] exception found in consumeReverseCourierMappingReplica : ", exception);
        } finally {
            ack.acknowledge();
        }
    }
}