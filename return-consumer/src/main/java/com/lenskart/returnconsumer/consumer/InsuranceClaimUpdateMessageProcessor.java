package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.request.InsuranceClaimUpdateKafkaRequest;
import com.lenskart.returncommon.model.response.ClaimUpdateResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.service.IEventDeduplicationService;
import com.lenskart.returnservice.service.InsuranceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class InsuranceClaimUpdateMessageProcessor {
    @Autowired
    InsuranceService insuranceService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    IEventDeduplicationService eventDeduplicationService;

    @KafkaListener(id = Constant.RETURN_TOPICS.Insurance_Update_Claim_Request_Queue, topics = "${insurance-update-claim-request.queue:insurance-update-claim-request-queue}", groupId = "${insurance-update-claim-request.queue.group:insurance-update-claim-request-queue-group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment acknowledgment) {
        try {
            log.info("[InsuranceClaimUpdateMessageProcessor] Processing message for insurance update request : " + message + " , by : " + Thread.currentThread().getName());
            message = mapper.readValue(message, String.class);
            processMessage(message);
            acknowledgment.acknowledge();
        } catch (Exception exception) {
            log.error("[OrderCommentConsumer][consume] exception found in consume : " + exception);
        }
    }

    private void processMessage(String message) throws Exception {
        if (StringUtils.isEmpty(message)) {
            log.error("[InsuranceClaimUpdateMessageProcessor] empty message received");
            return;
        }
        ClaimUpdateResponse claimUpdateResponse = null;
        InsuranceClaimUpdateKafkaRequest insuranceClaimUpdateKafkaRequest = null;
        insuranceClaimUpdateKafkaRequest = mapper.readValue(message, InsuranceClaimUpdateKafkaRequest.class);
        log.info("[InsuranceClaimUpdateMessageProcessor] updateInsuranceClaimUpdateRequest is " + insuranceClaimUpdateKafkaRequest);
        boolean shouldInsuranceBeClaimed = eventDeduplicationService.pushEvent("insurance" + "_" + insuranceClaimUpdateKafkaRequest.getReturnId());
        if(shouldInsuranceBeClaimed){
            claimUpdateResponse = insuranceService.updateInsuranceClaim(insuranceClaimUpdateKafkaRequest);
        }
        log.info("[InsuranceClaimUpdateMessageProcessor] updateInsuranceClaimUpdateResponse is " + claimUpdateResponse);
    }
}
