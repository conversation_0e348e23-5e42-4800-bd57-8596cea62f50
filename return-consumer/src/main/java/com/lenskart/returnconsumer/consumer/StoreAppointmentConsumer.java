package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.ItemRequestDTO;
import com.lenskart.returncommon.model.dto.ReturnRefundResponseDTO;
import com.lenskart.returncommon.model.request.RRRServiceRequest;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequestDTO;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.request.ReturnRefundEligibilityRequest;
import com.lenskart.returncommon.model.response.ItemResponse;
import com.lenskart.returncommon.model.response.ReturnRefundEligibilityResponse;
import com.lenskart.returnrepository.entity.StoreAppointmentOrders;
import com.lenskart.returnrepository.repository.StoreAppointmentOrdersRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IReturnRefundEligibilityService;
import com.lenskart.returnservice.service.impl.ReturnRefundEligibilityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class StoreAppointmentConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));


    @Autowired
    private StoreAppointmentOrdersRepository storeAppointmentOrdersRepository;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private IReturnRefundEligibilityService returnRefundEligibilityService;

    @Autowired
    private ReturnRefundEligibilityServiceImpl returnRefundEligibilityServiceImpl;

    @KafkaListener(id = "store-appointment-queue-queue-1", topics = "${store.appointment.queue:store_appointment_queue}", groupId = "${store.appointment.queue.group:store_appointment_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[StoreAppointmentConsumer][consume] request consumed : {} , by : {}", message, Thread.currentThread().getName());
            processMessage(message, ack);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[StoreAppointmentConsumer][consume] exception found in listenConsumer1 : {}", String.valueOf(exception));
        }
    }

    private void processMessage(String message, Acknowledgment acknowledgment) {
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[StoreAppointmentConsumer][processMessage] empty message received");
            }else{

                ReturnRefundEligibilityRequest request = mapper.readValue(message, ReturnRefundEligibilityRequest.class);
                request.setCheckCancellable(true);
                ReturnRefundEligibilityRequestDTO returnRefundEligibilityRequest = orderOpsFeignClient.eligibilityRequestBodyForHeadless(request);

                List<String> sources=new ArrayList<>();
                sources.add(ReturnSources.POS.getSource());
                sources.add(ReturnSources.VSM.getSource());
                sources.add(ReturnSources.WEB.getSource());

                for(String source:sources){
                    request.setSource(source);

                    Map<Integer, ReturnRefundResponseDTO> returnRefundEligibilityResponse= returnRefundEligibilityService.processEligibilityRequestForStoreAppointment(returnRefundEligibilityRequest);

                    for(Integer uwItemId:returnRefundEligibilityResponse.keySet()) {
                        ReturnRefundResponseDTO returnRefundResponseDTO=returnRefundEligibilityResponse.get(uwItemId);

                        StoreAppointmentOrders storeAppointmentOrders = new StoreAppointmentOrders();
                        storeAppointmentOrders.setCreatedAt(new Date());
                        storeAppointmentOrders.setOrderId(returnRefundEligibilityRequest.getOrderId());
                        storeAppointmentOrders.setIdentifierType("UW_ITEM_ID");
                        storeAppointmentOrders.setIdentifierValue(uwItemId);
                        storeAppointmentOrders.setSource(source);

                        if(!returnRefundResponseDTO.isFetchExistingReturnResponse()){
                            storeAppointmentOrders.setReturnRefundRuleResponse(mapper.writeValueAsString(returnRefundResponseDTO));
                        }

                        storeAppointmentOrdersRepository.save(storeAppointmentOrders);
                    }
                }

            }
        }catch (Exception exception){
            log.error("[StoreAppointmentConsumer][processMessage] exception found : {}", String.valueOf(exception));
        }finally {
            acknowledgment.acknowledge();
        }
    }
}
