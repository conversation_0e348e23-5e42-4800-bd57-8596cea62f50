package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.entity.ReturnOrder;
import com.lenskart.returnrepository.repository.ReturnOrderRepository;
import com.lenskart.returnservice.service.IEventDeduplicationService;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import static com.lenskart.returncommon.utils.Constant.EVENT.RETURN_REQUEST_CREATED;

@Slf4j
@Service
public class ReturnEventConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IEventDeduplicationService eventDeduplicationService;

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    @KafkaListener(id = "return-event-queue-1", topics = "${return.event.queue:return_event_queue}", groupId = "${return.event.queue.group:return_event_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ReturnEventConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[ReturnEventConsumer][consume] exception found in listenConsumer1 : "+exception);
        }finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message) {
        ReturnEvent returnEvent = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ReturnEventConsumer][processMessage] empty message received");
            }else{
                returnEvent = mapper.readValue(message, ReturnEvent.class);
                Integer requestId;
                if(returnEvent != null){
                    if(returnEvent.getReturnId() != null){
                        ReturnOrder returnOrder = returnOrderRepository.findByReturnId(returnEvent.getReturnId());
                        if(returnOrder != null){
                            log.error("[ReturnEventConsumer][processMessage] returnId : {} is from inventory", returnEvent.getReturnId());
                            return;
                        }
                    }
                    if(returnEvent.getReturnRequestId() == null){
                        requestId = returnEventService.getRequestId(returnEvent.getReturnId());
                        returnEvent.setReturnRequestId(requestId);
                    }else{
                        requestId = returnEvent.getReturnRequestId();
                    }
                    if(RETURN_REQUEST_CREATED.equalsIgnoreCase(returnEvent.getEvent())){
                        returnEventService.persistEvent(returnEvent.getReturnRequestId(), returnEvent.getReturnId(), returnEvent.getEvent().toUpperCase(), returnEvent.getRemarks());
                    }else{
                        boolean pushReturnEvent = eventDeduplicationService.pushEvent("RETURN_" + returnEvent.getEvent() + "_" + returnEvent.getReturnId());
                        if(pushReturnEvent && requestId != null){
                            returnEventService.persistEvent(returnEvent.getReturnRequestId(), returnEvent.getReturnId(), returnEvent.getEvent().toUpperCase(), returnEvent.getRemarks());
                        }
                    }
                }
            }
        }catch (Exception exception){
            log.error("[ReturnEventConsumer][processMessage] exception found : "+exception);
        }
    }
}
