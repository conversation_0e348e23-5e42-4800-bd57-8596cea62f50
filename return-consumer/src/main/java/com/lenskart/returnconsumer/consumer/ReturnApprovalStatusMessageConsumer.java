package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ApprovalStatusRequest;
import com.lenskart.returnservice.feignclient.POSFeignClient;
import com.lenskart.returnservice.service.IKafkaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.io.IOException;


@Slf4j
@Service
public class ReturnApprovalStatusMessageConsumer {

    @Autowired
    private POSFeignClient posFeignClient;

    @Autowired
    private IKafkaService kafkaService;

    private final static int MAX_RETRY = 3;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    //TODO - to be removed not needed
    //@KafkaListener(id = "return-approval-status", topics = "${return.approval.topic:Return-Approval-Status}", groupId = "${return.approval.topic.group:Return-Approval-Status-group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        log.info("[ReturnApprovalStatusMessageConsumer][consume] Request consumed: {} , by: {}", message, Thread.currentThread().getName());
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ReturnApprovalStatusMessageConsumer][consume] Empty message received");
                return;
            }
            ApprovalStatusRequest approvalStatusRequest = parseMessage(message);
            if (approvalStatusRequest == null) {
                log.error("[ReturnApprovalStatusMessageConsumer][consume] Failed to parse message: {}", message);
                return;
            }
            processApprovalStatusRequest(approvalStatusRequest);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[ReturnApprovalStatusMessageConsumer][consume] Exception encountered: ", exception);
        }
    }

    private ApprovalStatusRequest parseMessage(String message) {
        try {
            return mapper.readValue(message, ApprovalStatusRequest.class);
        } catch (IOException e) {
            log.error("[ReturnApprovalStatusMessageConsumer][parseMessage] IOException encountered: ", e);
            return null;
        }
    }

    protected void processApprovalStatusRequest(ApprovalStatusRequest approvalStatusRequest) {
        try {
            ResponseEntity response = posFeignClient.updateReturnApprovalStatus(
                    approvalStatusRequest,
                    MediaType.APPLICATION_JSON_VALUE,
                    "connect",
                    "valyoo123"
            );
            handleResponse(approvalStatusRequest, response);
        } catch (Exception e) {
            log.error("[ReturnApprovalStatusMessageConsumer][processApprovalStatusRequest] Exception encountered: ", e);
        }
    }

    private void handleResponse(ApprovalStatusRequest approvalStatusRequest, ResponseEntity response) throws JsonProcessingException {
        if (response.getStatusCode() != HttpStatus.OK) {
            log.info("[ReturnApprovalStatusMessageConsumer][handleResponse] Call to POS failed for status update: {}", approvalStatusRequest);
            retryApprovalStatusUpdate(approvalStatusRequest);
        } else {
            log.info("[ReturnApprovalStatusMessageConsumer][handleResponse] Successfully updated approval status: {}", approvalStatusRequest);
        }
    }

    protected void retryApprovalStatusUpdate(ApprovalStatusRequest approvalStatusRequest) throws JsonProcessingException {
        if (approvalStatusRequest.getRetryCount() < MAX_RETRY) {
            approvalStatusRequest.setRetryCount((approvalStatusRequest.getRetryCount() + 1));
            kafkaService.pushToKafka("Return-Approval-Status", String.valueOf(approvalStatusRequest.getMagentoItemId()), mapper.writeValueAsString(approvalStatusRequest));
            log.info("[ReturnApprovalStatusMessageConsumer][retryApprovalStatusUpdate] Retrying approval status update for: {}", approvalStatusRequest);
        } else {
            log.info("[ReturnApprovalStatusMessageConsumer][retryApprovalStatusUpdate] Max retry count reached for: {}", approvalStatusRequest);
        }
    }
}
