package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.DualRefundRequest;
import com.lenskart.returnservice.service.IReturnApprovalStatusUpdateService;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class NotifyCustomerRefundConsumer {

    private final ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IReturnApprovalStatusUpdateService returnApprovalStatusUpdateService;

    @KafkaListener(id = "notify-customer-refund", topics = "${notify.customer.refund.queue:notify_refund_customer_queue}", groupId = "${notify.customer.refund.queue.group:notify_refund_customer_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[NotifyCustomerRefundConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[NotifyCustomerRefundConsumer][consume] exception found in listenConsumer1 : "+exception);
        }
    }

    private void processMessage(String message) {
        DualRefundRequest refundRequest = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[NotifyCustomerRefundConsumer][processMessage] empty message received");
            }else{
                refundRequest = mapper.readValue(message, DualRefundRequest.class);
                if(refundRequest != null){
                    returnApprovalStatusUpdateService.notifyCustomerRefund(refundRequest);
                }
            }
        }catch (Exception exception){
            log.error("[NotifyCustomerRefundConsumer][processMessage] exception found : "+exception);
        }
    }
}
