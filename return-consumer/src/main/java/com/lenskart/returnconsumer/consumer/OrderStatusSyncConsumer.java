package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.request.OrderCommentReq;
import com.lenskart.returncommon.model.dto.OrderStatusSyncDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderStatusSyncConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "order-status-sync-1", topics = "${order.status.sync.topic:order_sync_queue}", groupId = "${order.status.sync.topic.group:order_sync_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[OrderStatusSyncConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            //message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[OrderStatusSyncConsumer][consume] exception found in listenConsumer1 : " + exception);
        }
    }

    protected void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[OrderStatusSyncConsumer][processMessage] empty message received");
        } else {
            OrderStatusSyncDTO orderStatusSync = mapper.readValue(message, OrderStatusSyncDTO.class);
            if (orderStatusSync != null) {
                orderOpsFeignClient.saveOrderSyncDetails(orderStatusSync.getIncrementId(), orderStatusSync.getStatus(), orderStatusSync.getTrackingNo());
            }
        }
    }

}
