package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ReturnTrackingEventConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnEventService returnEventService;

    private final List<String> duplicateEvents = List.of("AWB_ASSIGNED");

    @KafkaListener(id = "return-tracking-event-queue-1", topics = "${return.tracking.event.queue:Return_tracking_event_queue}", groupId = "${return.tracking.event.queue.group:Return_tracking_event_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ReturnTrackingEventConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[ReturnTrackingEventConsumer][consume] exception found in listenConsumer1 : "+exception);
        }
    }

    private void processMessage(String message) {
        ReturnEvent returnEvent = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ReturnTrackingEventConsumer][processMessage] empty message received");
            }else{
                returnEvent = mapper.readValue(message, ReturnEvent.class);
                if(returnEvent != null){
                    String event = returnEvent.getEvent();
                    if(!duplicateEvents.contains(event.toUpperCase())){
                        returnEventService.createTrackingEvent(returnEvent);
                    }
                }
            }
        }catch (Exception exception){
            log.error("[ReturnTrackingEventConsumer][processMessage] exception found : "+exception);
        }
    }
}
