package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.refund.client.model.response.StoreCreditResponse;
import com.lenskart.returncommon.model.dto.RefundRequestInputDTO;
import com.lenskart.returncommon.model.dto.ReturnEventDTO;
import com.lenskart.returncommon.model.dto.RtoExchangeMetaDataDTO;
import com.lenskart.returncommon.model.enums.EventStatus;
import com.lenskart.returncommon.model.enums.EventType;
import com.lenskart.returncommon.model.response.StoreCreditRefundDetails;
import com.lenskart.returnrepository.entity.ExchangeEventsTracker;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.ExchangeEventTrackerRepository;
import com.lenskart.returnservice.service.ICancelAndConvertService;
import com.lenskart.returnservice.service.IReturnEventService;
import com.lenskart.returnservice.service.impl.ReturnUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@Service
public class RtoExchangeHandlerConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private ICancelAndConvertService cancelAndConvertService;

    @Autowired
    private ExchangeEventTrackerRepository exchangeEventTrackerRepository;

    @Autowired
    private ReturnUtil returnUtil;



    @KafkaListener(id = "rto-exchange-handler-queue-1", topics = "${rto.exchange.handler.queue:rto_exchange_handler_queue}", groupId = "${rto.exchange.handler.queue.group:rto_exchange_handler_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[RtoExchangeHandlerConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[RtoExchangeHandlerConsumer][consume] exception found in listenConsumer : "+exception);
        }finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message) {
        RtoExchangeMetaDataDTO rtoExchangeMetaDataDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[RefundRequestConsumer][processMessage] empty message received");
            }else{
                rtoExchangeMetaDataDTO = mapper.readValue(message, RtoExchangeMetaDataDTO.class);
                if (rtoExchangeMetaDataDTO != null) {
                    StoreCreditRefundDetails scRefundDetails = cancelAndConvertService.getScRefundDetails(rtoExchangeMetaDataDTO.getUwItemId(), rtoExchangeMetaDataDTO.getUnicomOrderCode());
                    if (scRefundDetails != null) {
                        ExchangeEventsTracker exchangeEventsTracker = new ExchangeEventsTracker();
                        EventType eventType = returnUtil.getEventType(scRefundDetails);
                        exchangeEventsTracker.setType(eventType);
                        exchangeEventsTracker.setCreatedAt(LocalDate.now());
                        exchangeEventsTracker.setStatus(EventStatus.valueOf(rtoExchangeMetaDataDTO.getStatus()));
                        exchangeEventsTracker.setMasterMagentoItemId(rtoExchangeMetaDataDTO.getMagentoId());
                        exchangeEventsTracker.setMasterUwItemId(rtoExchangeMetaDataDTO.getUwItemId());
                        exchangeEventsTracker.setMasterUnicomOrderCode(rtoExchangeMetaDataDTO.getUnicomOrderCode());
                        exchangeEventsTracker.setExchangeOrderId(Long.valueOf(rtoExchangeMetaDataDTO.getExchangeIncrementId()));
                        exchangeEventTrackerRepository.save(exchangeEventsTracker);

                        if (eventType == EventType.RTO || eventType == EventType.RETURN) {
                            returnEventService.createReturnEvent(null, rtoExchangeMetaDataDTO.getReturnId(), "EXCHANGE_CREATED", "exchange order : " + rtoExchangeMetaDataDTO.getExchangeIncrementId());
                        }
                    }
                }
            }
        }catch (Exception exception){
            log.error("[RefundRequestConsumer][processMessage] exception found : "+exception);
        }
    }
}
