package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.CommRequestDTO;
import com.lenskart.returncommon.model.dto.ReturnCommunicationDTO;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnservice.service.ICommunicationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReturnCommunicationConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private ICommunicationService communicationService;

    @KafkaListener(id = Constant.RETURN_COMMUNICATION_TOPIC, topics = "${return.communication.topic:return-communication}", groupId = "${return.communication.topic.group:return-communication-group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ReturnCommunicationConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            //message = mapper.readValue(message, String.class);
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[ReturnCommunicationConsumer][consume] exception found in listenConsumer1 : "+exception);
        }
    }

    private void processMessage(String message) {
        CommRequestDTO commRequestDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ReturnCommunicationConsumer][processMessage] empty message received");
            }else{
                commRequestDTO = mapper.readValue(message, CommRequestDTO.class);
                if(commRequestDTO != null){
                    communicationService.sendCommunication(commRequestDTO);
                }
            }
        }catch (Exception exception){
            log.error("[ReturnCommunicationConsumer][processMessage] exception found : "+exception);
        }
    }
}
