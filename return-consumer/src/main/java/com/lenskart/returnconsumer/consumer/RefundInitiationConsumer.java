package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.RefundProcessDTO;
import com.lenskart.returnservice.service.IRefundUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RefundInitiationConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IRefundUtilsService refundUtilsService;

    @KafkaListener(id = "refund-initiation-queue-1", topics = "${refund.initiation.queue:refund_process_on_return_initiation_queue}", groupId = "${return.event.queue.group:refund_process_on_return_initiation_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[RefundInitiationConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[RefundInitiationConsumer][consume] exception found in listenConsumer1 : "+exception);
        }
    }

    private void processMessage(String message) {
        RefundProcessDTO refundProcessDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[RefundInitiationConsumer][processMessage] empty message received");
            }else{
                refundProcessDTO = mapper.readValue(message, RefundProcessDTO.class);
                if(refundProcessDTO != null){
                    refundUtilsService.initiateRefund(refundProcessDTO);
                }
            }
        }catch (Exception exception){
            log.error("[RefundInitiationConsumer][processMessage] exception found : "+exception);
        }
    }
}
