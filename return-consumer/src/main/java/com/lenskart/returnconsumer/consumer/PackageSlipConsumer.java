package com.lenskart.returnconsumer.consumer;

import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.request.PackageSlipCreationRequest;
import com.lenskart.returnservice.service.ID365FinanceService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PackageSlipConsumer {

    private final ID365FinanceService d365FinanceService;

    private final ObjectMapper mapper;

    @KafkaListener(id = "fl-package-slip", topics = "${fl.packageSlip.topic:fl_package_slip}", groupId = "${fl.packageSlip.topic.group:fl_package_slip_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[PackageSlipConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[PackageSlipConsumer][consume] exception found in consume : " + exception);
        } finally {
            ack.acknowledge();
        }
    }

    protected void processMessage(String message) throws Exception {
        if (StringUtils.isEmpty(message)) {
            log.error("[PackageSlipConsumer][processMessage] empty message received");
        } else {
            PackageSlipCreationRequest packageSlipCreationRequest = mapper.readValue(message, PackageSlipCreationRequest.class);
            if (packageSlipCreationRequest != null) {
                d365FinanceService.generatePayloadForPackingSlip(packageSlipCreationRequest);
            }
        }
    }
    
}
