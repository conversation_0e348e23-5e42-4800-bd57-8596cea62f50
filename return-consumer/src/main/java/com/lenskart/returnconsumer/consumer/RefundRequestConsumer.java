package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.response.RefundRequestResponseDTO;
import com.lenskart.returncommon.model.dto.RefundRequestInputDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IRefundRequestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RefundRequestConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;
    @Autowired
    private IRefundRequestService refundRequestService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "refund-request-1", topics = "${refund.request.topic:return_refund_queue}", groupId = "${refund.request.topic.group:return_refund_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[RefundRequestConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[RefundRequestConsumer][consume] exception found in listenConsumer1 : "+exception);
        }
    }

    protected void processMessage(String message) {
        RefundRequestInputDTO refundRequestInputDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[RefundRequestConsumer][processMessage] empty message received");
            }else{
                refundRequestInputDTO = mapper.readValue(message, RefundRequestInputDTO.class);
                if(refundRequestInputDTO != null){
                    refundRequestService.createRefundRequest(refundRequestInputDTO);
                }
            }
        }catch (Exception exception){
            log.error("[RefundRequestConsumer][processMessage] exception found : "+exception);
        }
    }
}
