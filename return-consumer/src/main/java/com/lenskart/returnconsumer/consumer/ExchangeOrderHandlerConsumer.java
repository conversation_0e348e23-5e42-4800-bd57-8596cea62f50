package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.RefundRequestInputDTO;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IRefundRequestService;
import com.lenskart.returnservice.service.IReversePickUpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExchangeOrderHandlerConsumer {

    @Autowired
    private IReversePickUpService reversePickUpService;
    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "exchange-order-handler-1", topics = "${exchange.order.handler.topic:exchange_order_handler_queue}", groupId = "${exchange.order.handler.topic.group:exchange_order_handler_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ExchangeOrderHandlerConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[ExchangeOrderHandlerConsumer][consume] exception found in listenConsumer1 : "+exception);
        }finally {
            ack.acknowledge();
        }
    }

    protected void processMessage(String message) {
        ReturnDetail returnOrder = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ExchangeOrderHandlerConsumer][processMessage] empty message received");
            }else{
                returnOrder = mapper.readValue(message, ReturnDetail.class);
                if(returnOrder != null){
                    reversePickUpService.handleExchangeOrder(returnOrder);
                }
            }
        }catch (Exception exception){
            log.error("[ExchangeOrderHandlerConsumer][processMessage] exception found : "+exception);
        }
    }
}
