package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.BackSyncStatusDTO;
import com.lenskart.returncommon.model.dto.OrderStatusSyncDTO;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IOrderUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;

@Slf4j
@Service
public class BackSyncStatusConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private IOrderUtilsService orderUtilsService;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @KafkaListener(id = "back-sync-status-sync-1", topics = "${back-sync-status.topic:back_sync_status_queue}", groupId = "${order.status.sync.topic.group:back_sync_status_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[BackSyncStatusConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[BackSyncStatusConsumer][consume] exception found in listenConsumer1 : " + exception);
        }
    }

    protected void processMessage(String message) throws JsonProcessingException {
        if (StringUtils.isEmpty(message)) {
            log.error("[BackSyncStatusConsumer][processMessage] empty message received");
        } else {
            BackSyncStatusDTO backSyncStatus = mapper.readValue(message, BackSyncStatusDTO.class);
            if (backSyncStatus != null) {
                if (!CollectionUtils.isEmpty(backSyncStatus.getUwOrderDTOS()) && backSyncStatus.getIncrementId() !=null) {
                    orderUtilsService.callingBackSyncApi(backSyncStatus.getIncrementId(), backSyncStatus.getUwOrderDTOS(), backSyncStatus.getTrackingStatus());
                } else {
                    orderOpsFeignClient.callBackSyncApi(Collections.singletonList(backSyncStatus.getUwItemId()), backSyncStatus.getTrackingStatus());
                }
            }
        }
    }

}
