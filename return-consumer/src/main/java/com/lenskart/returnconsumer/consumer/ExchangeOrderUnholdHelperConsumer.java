package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ordermetadata.dto.ExchangeOrdersDTO;
import com.lenskart.returncommon.model.dto.ExchangeUnholdHelperDTO;
import com.lenskart.returncommon.model.dto.Returns;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.repository.ReturnDetailRepository;
import com.lenskart.returnservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class ExchangeOrderUnholdHelperConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IExchangeOrderService exchangeOrderService;

    @Autowired
    private IReturnRefundRuleService returnRefundRuleService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ReturnDetailRepository returnDetailRepository;

    @Autowired
    private IReversePickUpService reversePickUpService;

    @KafkaListener(id = "exchange-order-unhold-helper-queue-1", topics = "${exchange.order.queue:exchange_order_unhold_helper_queue}", groupId = "${exchange.order.queue.group:exchange_order_unhold_helper_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ExchangeOrderUnholdHelperConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message, ack);
        } catch (Exception exception) {
            log.error("[ExchangeOrderUnholdHelperConsumer][consume] exception found in listenConsumer1 : "+exception);
        }finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message, Acknowledgment acknowledgment) {
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ExchangeOrderUnholdHelperConsumer][processMessage] empty message received");
            }else{
                processMessageWithDelay(message);
            }
        }catch (Exception exception){
            log.error("[ExchangeOrderUnholdHelperConsumer][processMessage] exception found : "+exception);
        }finally {
            acknowledgment.acknowledge();
        }
    }

    private void processMessageWithDelay(String message) {
            try {
                ExchangeUnholdHelperDTO exchangeUnholdHelperDTO = mapper.readValue(message, ExchangeUnholdHelperDTO.class);
               log.info("[processMessageWithDelay] uwItemId : {}, returnId : {}", exchangeUnholdHelperDTO.getUwItemId(), exchangeUnholdHelperDTO.getReturnId());
                ExchangeOrdersDTO exchangeOrdersDTO = exchangeOrderService.getExchangeOrder(exchangeUnholdHelperDTO.getUwItemId(), exchangeUnholdHelperDTO.getReturnId());
                if (exchangeOrdersDTO != null) {
                    handleExchangeOrder(exchangeUnholdHelperDTO.getReturnId());
                }else{
                    ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

                    AtomicInteger runCount = new AtomicInteger(0);
                    int maxRuns = 3;

                    Runnable task = () -> {
                        int currentRun = runCount.incrementAndGet();
                        log.info("[processMessageWithDelay] Running task, returnId : "+exchangeUnholdHelperDTO.getReturnId() +", execution #" + currentRun + " at " + java.time.LocalTime.now());
                        ExchangeOrdersDTO exchangeOrdersDTOTemp = exchangeOrderService.getExchangeOrder(exchangeUnholdHelperDTO.getUwItemId(), exchangeUnholdHelperDTO.getReturnId());
                        if (currentRun >= maxRuns || exchangeOrdersDTOTemp != null) {
                            log.info("[processMessageWithDelay] returnId : " + exchangeUnholdHelperDTO.getReturnId() +", currentRun : "+ currentRun);
                            if(exchangeOrdersDTOTemp != null){
                                log.info("[processMessageWithDelay] returnId : " + exchangeUnholdHelperDTO.getReturnId() +", calling handleExchangeOrder : ");
                                handleExchangeOrder(exchangeUnholdHelperDTO.getReturnId());
                            }else{
                                log.info("[processMessageWithDelay] returnId : " + exchangeUnholdHelperDTO.getReturnId() +", please manually unhold exchange order ");
                            }
                            scheduler.shutdown();
                        }
                    };

                    scheduler.scheduleWithFixedDelay(task, 0, 5, TimeUnit.MINUTES);
                }
            } catch (Exception exception){
                log.error("[processMessageWithDelay] exception : {}",exception.getMessage());
            }
    }

    private void handleExchangeOrder(Integer returnId){
        log.info("[processMessageWithDelay] returnId : {}", returnId);
        Optional<ReturnDetail> returnDetailOptional = returnDetailRepository.findById(returnId);
        returnDetailOptional.ifPresent(returnDetail -> reversePickUpService.handleExchangeOrder(returnDetail));
    }
}
