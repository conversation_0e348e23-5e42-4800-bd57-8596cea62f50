package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ccautils.constants.CosmosConstants;
import com.lenskart.ccautils.util.CosmosEventPushUtil;
import com.lenskart.ordermetadata.dto.ExchangeOrderUnholdRequest;
import com.lenskart.returnservice.service.IExchangeOrderService;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;

import static com.lenskart.returncommon.utils.Constant.EVENT.EXCHANGE_ORDER_UNHOLDED;

@Slf4j
@Service
public class ExchangeOrderUnholdConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IExchangeOrderService exchangeOrderService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private CosmosEventPushUtil cosmosEventPushUtil;

    @KafkaListener(id = "exchange-order-unhold-queue-1", topics = "${exchange.order.queue:exchange_order_unhold_queue}", groupId = "${exchange.order.queue.group:exchange_order_queue_unhold_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ExchangeOrderUnholdConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            message = mapper.readValue(message, String.class);
            processMessage(message);
        } catch (Exception exception) {
            log.error("[ExchangeOrderUnholdConsumer][consume] exception found in listenConsumer1 : "+exception);
        }finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message) {
        ExchangeOrderUnholdRequest exchangeOrderUnholdRequest = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ExchangeOrderUnholdConsumer][processMessage] empty message received");
            }else{
                exchangeOrderUnholdRequest = mapper.readValue(message, ExchangeOrderUnholdRequest.class);
                if(exchangeOrderUnholdRequest != null){
                    boolean exchangeOrderUnholded = exchangeOrderService.unholdExchangeOrder(exchangeOrderUnholdRequest.getIncrementId());
                    if(exchangeOrderUnholded){
                        Integer requestId = returnEventService.getRequestId(exchangeOrderUnholdRequest.getReturnId());
                        returnEventService.createReturnEvent(requestId, exchangeOrderUnholdRequest.getReturnId(), EXCHANGE_ORDER_UNHOLDED, "exchange order for return : "+exchangeOrderUnholdRequest.getReturnId());
                        cosmosEventPushUtil.pushEventToCosmosUsingReturnIds(Map.of("checkpoint", "headless_return_exchange_order_unhold"), CosmosConstants.Events.PROCESSING.eventName, exchangeOrderUnholdRequest.getIncrementId(), null, null);
                    }
                }
            }
        }catch (Exception exception){
            log.error("[ExchangeOrderUnholdConsumer][processMessage] exception found : "+exception);
        }
    }
}
