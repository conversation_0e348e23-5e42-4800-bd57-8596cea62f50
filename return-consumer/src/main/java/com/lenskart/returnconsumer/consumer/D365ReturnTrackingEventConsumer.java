package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.D365ReturnTrackingRequestDTO;
import com.lenskart.returnservice.service.ID365ReturnTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class D365ReturnTrackingEventConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private ID365ReturnTrackingService d365ReturnTrackingService;


    @KafkaListener(id = "d365-return-tracking-event-1", topics = "${d365.return.tracking.event.queue:d365_return_tracking_event}", groupId = "${d365.return.tracking.event.queue.group:d365_return_tracking_event_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consumeReturnTrackingEvent(String message, Acknowledgment ack){
        try {
            log.info("[D365ReturnTrackingEventConsumer][consumeReturnTrackingEvent] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[D365ReturnTrackingEventConsumer][consumeReturnTrackingEvent] exception found in consumeReturnsReplica : "+exception);
        }
    }

    private void processMessage(String message) {
        try{
            D365ReturnTrackingRequestDTO d365ReturnTrackingRequestDTO = null;
            if (StringUtils.isEmpty(message)) {
                log.error("[ExchangeOrderConsumer][processMessage] empty message received");
            }else{
                d365ReturnTrackingRequestDTO = mapper.readValue(message, D365ReturnTrackingRequestDTO.class);
                d365ReturnTrackingService.saveReturnTrackingEvent(d365ReturnTrackingRequestDTO);
            }
        }catch (Exception exception){
            log.error("[D365ReturnTrackingEventConsumer][processMessage] error : "+exception);
        }
    }

}