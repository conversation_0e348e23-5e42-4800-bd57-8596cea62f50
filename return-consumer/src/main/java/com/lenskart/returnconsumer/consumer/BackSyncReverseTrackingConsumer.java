package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.ReverseTrackingEventDTO;
import com.lenskart.returnservice.service.IKafkaService;
import com.lenskart.returnservice.service.IReverseCourierDetailService;
import com.lenskart.returnservice.service.IReverseTrackingBackSyncService;
import com.lenskart.returnservice.service.IReverseTrackingEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BackSyncReverseTrackingConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReverseTrackingEventService reverseTrackingEventService;

    @Autowired
    private IReverseCourierDetailService reverseCourierDetailService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private IReverseTrackingBackSyncService reverseTrackingBackSyncService;

    @KafkaListener(id = "back-sync-rev-tracking-id", topics = "${back.sync.reverse.tracking.event.queue:back_sync_rev_tracking_queue}", groupId = "${back.sync.reverse.tracking.event.queue.group:back_sync_rev_tracking_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[BackSyncReverseTrackingConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[BackSyncReverseTrackingConsumer][consume] exception found in consume() : "+exception);
        }
    }

    private void processMessage(String message) {
        ReverseTrackingEventDTO reverseTrackingEventDTO = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[BackSyncReverseTrackingConsumer][processMessage] empty message received");
            }else{
                reverseTrackingEventDTO = mapper.readValue(message, ReverseTrackingEventDTO.class);
                reverseTrackingBackSyncService.backSync(reverseTrackingEventDTO);
            }
        }catch (Exception exception){
            log.error("[BackSyncReverseTrackingConsumer][processMessage] exception found : "+exception);
        }
    }
}
