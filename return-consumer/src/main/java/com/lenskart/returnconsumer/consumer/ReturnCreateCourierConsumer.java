package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.OrdersDTO;
import com.lenskart.returncommon.model.response.OrderInfoResponseDTO;
import com.lenskart.returncommon.model.dto.ClickPostResponseMeta;
import com.lenskart.returncommon.model.dto.CommRequestDTO;
import com.lenskart.returncommon.model.dto.CommentDTO;
import com.lenskart.returncommon.model.dto.ReverseCourierDetail;
import com.lenskart.returncommon.model.enums.QCType;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnservice.feignclient.OrderAdaptorFeignClient;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.feignclient.ReverseLogisticsConsumerFeignClient;
import com.lenskart.returnservice.service.*;
import com.lenskart.returnservice.service.impl.ReturnUtil;
import com.lenskart.reversemodel.request.*;
import com.lenskart.reversemodel.request.kafka.InitiateReverseRequest;
import com.lenskart.reversemodel.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.ORDER_ADDRESS_UPDATE_DTO;
import static com.lenskart.ordermetadata.dto.OrderInfoDtoType.UW_ORDERS_DTO;
import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.ORDER_COMMENT_QUEUE;

@Slf4j
@Component
public class ReturnCreateCourierConsumer {

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private OrderAdaptorFeignClient orderAdaptorFeignClient;

    @Autowired
    private ReverseLogisticsConsumerFeignClient reverseLogisticsConsumerFeignClient;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnMethodResolver returnMethodResolver;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private ICommunicationService communicationService;

    @Autowired
    private ReturnUtil returnUtil;

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));
    private static final String SYSTEM_CACHE = "SYSTEM_CACHE";
    private static final String COURIER_ASSIGN_RETRY_LIMIT = "COURIER_ASSIGN_RETRY_LIMIT";
    private static final String COURIER_REASSIGN_EXCEPTION = "COURIER_REASSIGN_EXCEPTION";
    private static final String COURIER_REASSIGN_NOT_FOUND_EXCEPTION = "NO MORE COURIERS FOUND FOR REASSIGN";


    @KafkaListener(id = "return_create_courier_service_queue-1", topics = "${return.create.courier.queue:return_create_courier_service_queue_new}", groupId = "${return.create.courier.queue.group:return_create_courier_service_queue__new_consumer}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[ReturnCreateCourierConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[ReturnCreateCourierConsumer][consume] exception found in listenConsumer1 : " + exception);
        }
    }

    private void processMessage(String message) {
        log.info("[processMessage] Processing message : {}", message);

        if (StringUtils.isBlank(message)) {
            log.error("Message is blank");
            return;
        }

        String error = null;
        ReverseCourierDetail reassignedCourier = null;
        InitiateReverseRequest initiateReverseRequest = null;
        OrdersDTO order = null;
        try {
            initiateReverseRequest = mapper.readValue(message, InitiateReverseRequest.class);

            OrderInfoResponseDTO orderInfoResponseDTO = fetchOrder(initiateReverseRequest);

            if (orderInfoResponseDTO == null || CollectionUtils.isEmpty(orderInfoResponseDTO.getOrders())) {
                log.error("Order not found with incrementId :{}", initiateReverseRequest.getAdditional().getOrderId());
                return;
            }

            order = orderInfoResponseDTO.getOrders().get(0);

            com.lenskart.reversemodel.request.kafka.ShipmentDetails shipmentDetails = initiateReverseRequest.getShipmentDetails();
            if(!StringUtils.isNotEmpty(shipmentDetails.getReferenceNumber())){
                String[] arr = shipmentDetails.getReferenceNumber().split("-");
                long groupId = 0L;
                if(arr.length == 1){
                    groupId = Long.parseLong(arr[0]);
                }else{
                    groupId = Long.parseLong(arr[1]);
                }
                List<ReturnDetail> returnDetails = returnOrderActionService.findByGroupId(groupId);
                if(CollectionUtils.isEmpty(returnDetails)){
                    log.info("[processMessage] return not present in headless returns for groupId : {}", groupId);
                    return;
                }
            }

            if (shouldReassignCourier(initiateReverseRequest)) {
                reassignedCourier = updateNextPriorityCourier(initiateReverseRequest, order, orderInfoResponseDTO);
            }

            Response response = initiateCourierAssignation(initiateReverseRequest);
            error = handleResponse(response, initiateReverseRequest, order, reassignedCourier);
        } catch (IOException e) {
            error = handleException(e, "IOException found");
        } catch (Throwable e) {
            error = handleException(e, "Throwable found");
        } finally {
            finalizeProcessing(error, initiateReverseRequest, order);
        }
    }

    private ReverseCourierDetail updateNextPriorityCourier(InitiateReverseRequest initiateReverseRequest, OrdersDTO order, OrderInfoResponseDTO orderInfoResponseDTO) {
        IReturnInitiationService returnInitiationService = returnMethodResolver.getReturnMethod(Constant.RETURN_SOURCE.WEB);
        QCType qcType = QCType.NON_QC;
        try {
            if (Objects.nonNull(initiateReverseRequest.getAdditional()) && Objects.nonNull(initiateReverseRequest.getAdditional().getQcType())) {
                try {
                    qcType = QCType.getQcType(initiateReverseRequest.getAdditional().getQcType());
                } catch (Exception e) {
                    log.info("[updateNextPriorityCourier] Exception:{}", e.getMessage());
                }
            }
            boolean newQcFlow = returnInitiationService.getQcRuleCount() > 0;
            ReverseCourierDetail reverseCourierDetail = returnInitiationService.assignReverseCourier(order.getOrderId(), Integer.parseInt(initiateReverseRequest.getAdditional().getOrderId()), true, Integer.parseInt(initiateReverseRequest.getPickUpInfo().getPickupPincode()), initiateReverseRequest.getCourierAssignRetryMetaData().getRetryCount(), qcType, newQcFlow, orderInfoResponseDTO);
            if (reverseCourierDetail != null) {
                log.info("Courier reassigned: {}", reverseCourierDetail);
                if (reverseCourierDetail.getCpId() == null || initiateReverseRequest.getCourierAssignRetryMetaData().getAttemptedCouriers().contains(reverseCourierDetail.getCpId())) {
                    throw new RuntimeException(COURIER_REASSIGN_NOT_FOUND_EXCEPTION);
                }
                initiateReverseRequest.getShipmentDetails().setCourierPartner(reverseCourierDetail.getCpId());
                return reverseCourierDetail;
            } else {
                log.error("Error in courier reassign, reverseCourierDetail is null {}, {}", reverseCourierDetail, initiateReverseRequest.getShipmentDetails().getReferenceNumber());
                throw new RuntimeException(COURIER_REASSIGN_EXCEPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error in courier reassign {}, {}", e.getMessage(), initiateReverseRequest.getShipmentDetails().getReferenceNumber());
            throw new RuntimeException(COURIER_REASSIGN_EXCEPTION);
        }
    }

    private OrderInfoResponseDTO fetchOrder(InitiateReverseRequest request) {
        Integer incrementId = Integer.valueOf(request.getAdditional().getOrderId());
        boolean isFeatureEligible = returnUtil.isFeatureEligibleByRolloutPlan(incrementId, "order-info.rollout.percentage");
        ResponseEntity<OrderInfoResponseDTO> response = isFeatureEligible ? orderAdaptorFeignClient.getOrderDetailsV2(incrementId, List.of(UW_ORDERS_DTO, ORDER_ADDRESS_UPDATE_DTO))
                : orderOpsFeignClient.getOrderDetails(incrementId);
        return response.getBody();
    }

    private boolean shouldReassignCourier(InitiateReverseRequest request) {
        return request.getCourierAssignRetryMetaData().getRetryCount() != 0;
    }

    private Response initiateCourierAssignation(InitiateReverseRequest request) throws IOException {
        com.lenskart.reversemodel.request.InitiateReverseRequest restRequest = convertToRestRequest(request);
        log.info("[initiateCourierAssignation] initiateReverseRequest1 : {}", restRequest);
        Response response = reverseLogisticsConsumerFeignClient.processReverseInitiateCourierAssignation(restRequest).getBody();
        log.info("[initiateCourierAssignation] response content : {}, response status : {}", response != null ? response.getResponseContent() : response, response != null ? response.getResponseCode() : response);
        return response;
    }

    private com.lenskart.reversemodel.request.InitiateReverseRequest convertToRestRequest(InitiateReverseRequest initiateReverseRequest) {
        com.lenskart.reversemodel.request.InitiateReverseRequest initiateReverseRequest1 = new com.lenskart.reversemodel.request.InitiateReverseRequest();
        PickUpInfo pickUpInfo = new PickUpInfo();
        DropInfo dropInfo = new DropInfo();
        ShipmentDetails shipmentDetails = new ShipmentDetails();
        Additional additional = new Additional();
        if (null != initiateReverseRequest.getPickUpInfo()) {
            pickUpInfo.setPickup_phone(initiateReverseRequest.getPickUpInfo().getPickupPhone());
            pickUpInfo.setPickup_pincode(initiateReverseRequest.getPickUpInfo().getPickupPincode());
            pickUpInfo.setPickup_name(initiateReverseRequest.getPickUpInfo().getPickupName());
            pickUpInfo.setPickup_country(initiateReverseRequest.getPickUpInfo().getPickupCountry());
            pickUpInfo.setPickup_state(initiateReverseRequest.getPickUpInfo().getPickupState());
            pickUpInfo.setPickup_city(initiateReverseRequest.getPickUpInfo().getPickupCity());
            pickUpInfo.setPickup_address(initiateReverseRequest.getPickUpInfo().getPickupAddress());
            pickUpInfo.setEmail(initiateReverseRequest.getPickUpInfo().getEmail());
            pickUpInfo.setPickup_time(initiateReverseRequest.getPickUpInfo().getPickupTime());
        }
        if (null != initiateReverseRequest.getDropInfo()) {
            dropInfo.setDrop_state(initiateReverseRequest.getDropInfo().getDropState());
            dropInfo.setDrop_pincode(initiateReverseRequest.getDropInfo().getDropPincode());
            dropInfo.setDrop_email(initiateReverseRequest.getDropInfo().getDropEmail());
            dropInfo.setDrop_name(initiateReverseRequest.getDropInfo().getDropName());
            dropInfo.setDrop_city(initiateReverseRequest.getDropInfo().getDropCity());
            dropInfo.setDrop_address(initiateReverseRequest.getDropInfo().getDropAddress());
            dropInfo.setDrop_country(initiateReverseRequest.getDropInfo().getDropCountry());
            dropInfo.setDrop_phone(initiateReverseRequest.getDropInfo().getDropPhone());
            log.info("[ReturnCreateCourierMessageProcessor][convertToRestResquest] Drop Info : {}", dropInfo);
        }
        if (null != initiateReverseRequest.getShipmentDetails()) {
            shipmentDetails.setInvoice_value(initiateReverseRequest.getShipmentDetails().getInvoiceValue());
            shipmentDetails.setCourier_partner(initiateReverseRequest.getShipmentDetails().getCourierPartner());
            List<ReturnItems> returnItemsList = new ArrayList<>();
            for (com.lenskart.reversemodel.request.kafka.ReturnItems returnItems : initiateReverseRequest.getShipmentDetails().getItems()) {
                ReturnItems returnItems1 = new ReturnItems();
                returnItems1.setProduct_url(returnItems.getProductUrl());
                returnItems1.setSku(returnItems.getSku());
                returnItems1.setQuantity(returnItems.getQuantity());
                returnItems1.setDescription(returnItems.getDescription());
                returnItems1.setPrice(returnItems.getPrice());
                returnItems1.setAdditional(returnItems.getAdditional());
                returnItems1.setSerial_no(returnItems.getSerial_no());
                returnItemsList.add(returnItems1);
            }
            shipmentDetails.setItems(returnItemsList);
            shipmentDetails.setReference_number(initiateReverseRequest.getShipmentDetails().getReferenceNumber());
            shipmentDetails.setInvoice_number(initiateReverseRequest.getShipmentDetails().getInvoiceNumber());
            shipmentDetails.setInvoice_date(initiateReverseRequest.getShipmentDetails().getInvoiceDate());
            shipmentDetails.setOrder_type(initiateReverseRequest.getShipmentDetails().getOrderType());
            shipmentDetails.setWeight(initiateReverseRequest.getShipmentDetails().getWeight());
            shipmentDetails.setHeight(initiateReverseRequest.getShipmentDetails().getHeight());
            shipmentDetails.setCod_value(initiateReverseRequest.getShipmentDetails().getCodValue());
            shipmentDetails.setLength(initiateReverseRequest.getShipmentDetails().getLength());
            shipmentDetails.setBreadth(initiateReverseRequest.getShipmentDetails().getBreadth());
        }
        if (null != initiateReverseRequest.getAdditional()) {
            additional.setDelivery_type(initiateReverseRequest.getAdditional().getDeliveryType());
            additional.setQc_type(initiateReverseRequest.getAdditional().getQcType());
            additional.setOrder_id(initiateReverseRequest.getAdditional().getOrderId());
            additional.setLabel(initiateReverseRequest.getAdditional().getLabel());
            additional.setAsync(initiateReverseRequest.getAdditional().getAsync());
            additional.setRvp_reason(initiateReverseRequest.getAdditional().getRvpReason());
            additional.setSpecial_instruction(initiateReverseRequest.getAdditional().getSpecialInstructions());
        }
        initiateReverseRequest1.setShipment_details(shipmentDetails);
        initiateReverseRequest1.setPickup_info(pickUpInfo);
        initiateReverseRequest1.setDrop_info(dropInfo);
        initiateReverseRequest1.setAdditional(additional);
        log.info("initiateReverseRequest1 value is ============ : " + initiateReverseRequest1.toString());
        return initiateReverseRequest1;
    }

    private String handleResponse(Response response, InitiateReverseRequest request, OrdersDTO order, ReverseCourierDetail reassignedCourier) throws IOException {
        String error = null;
        if (response == null) {
            log.error("No response from click post");
            error = "No response from click post";
            return error;
        }

        String responseString = response.getResponseContent();
        log.info("[handleResponse] Response from click post is : {}", responseString);

        if (StringUtils.isBlank(responseString)) {
            log.error("No response content from click post");
            error = "No response content from click post";
            return error;
        }

        JsonNode jsonNode = mapper.readTree(responseString);

        if (jsonNode == null) {
            log.error("Blank response from click post");
            error = "Blank response from click post";
            return error;
        }
        log.info("[handleResponse] jsonNode tree structure is : {}", jsonNode);

        String failedResponse = receivedSuccessFromClickPost(jsonNode.at("/meta"));
        if (failedResponse != null) {
            log.error("Failed response from click post: {}", failedResponse);
            error = "Failed response from click post: "+failedResponse;
            return error;
        }

        handleSuccessfulResponse(jsonNode, request, order, reassignedCourier);
        return  error;
    }

    private String receivedSuccessFromClickPost(JsonNode clickPostResponseMetadata) {
        try {
            if (clickPostResponseMetadata != null) {
                log.info("[receivedSuccessFromClickPost]: {}", clickPostResponseMetadata);
                ClickPostResponseMeta clickPostResponseMeta = mapper.readValue(clickPostResponseMetadata.toString(), ClickPostResponseMeta.class);
                if (clickPostResponseMeta.getSuccess()) {
                    return null;
                } else {
                    return String.format("%d-%s", clickPostResponseMeta.getStatus(), clickPostResponseMeta.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error in [receivedSuccessFromClickPost]: {}", e.getMessage());
        }
        return null;     //TODO check this return true or false ?
    }

    private void handleSuccessfulResponse(JsonNode jsonNode, InitiateReverseRequest request, OrdersDTO order, ReverseCourierDetail reassignedCourier) {
        JsonNode waybillNode = jsonNode.at("/result/waybill");
        JsonNode referenceNode = jsonNode.at("/result/reference_number");

        if (waybillNode == null || referenceNode == null) {
            log.error("Either waybillNode or referenceNode is empty");
            return;
        }

        String waybill = waybillNode.asText();
        String referenceNumber = referenceNode.asText();
        String requestReferenceNumber = request.getShipmentDetails().getReferenceNumber();
        log.info("[processMessage] cpReferenceNumber : {}, waybill : {}, requestReferenceNumber : {}", referenceNumber, waybill, requestReferenceNumber);

        if (!referenceNumber.equalsIgnoreCase(requestReferenceNumber)) {
            log.error("Either reference number is null or not equal to requested group id : {}", referenceNumber);
            return;
        }

        long groupId = extractGroupId(requestReferenceNumber);
        QCType qcType = determineQCType(request);
        updateReturnOrders(groupId, waybill, request, qcType, reassignedCourier, order);
    }

    private long extractGroupId(String referenceNumber) {
        String[] arr = referenceNumber.split("-");
        return (arr.length == 1) ? Long.parseLong(arr[0]) : Long.parseLong(arr[1]);
    }

    private QCType determineQCType(com.lenskart.reversemodel.request.kafka.InitiateReverseRequest request) {
        QCType qcType = QCType.NON_QC;
        if (Objects.nonNull(request.getAdditional()) && Objects.nonNull(request.getAdditional().getQcType())) {
            try {
                qcType = QCType.getQcType(request.getAdditional().getQcType());
            } catch (Exception e) {
                log.info("[processMessage] Exception: {}", e.getMessage());
            }
        }
        return qcType;
    }

    private void updateReturnOrders(long groupId, String waybill, InitiateReverseRequest request, QCType qcType, ReverseCourierDetail reassignedCourier, OrdersDTO order) {
        returnOrderActionService.updateReturnCourierDetailByIncrementIdAndGroupId(ReturnStatus.REFERENCE_ID_ISSUED.getStatus(), waybill, groupId, Integer.valueOf(request.getAdditional().getOrderId()), qcType.getValue(), reassignedCourier);
        List<ReturnDetail> returnOrderList = returnOrderActionService.findAllByGroupIdAndIncrementId(groupId, Integer.valueOf(request.getAdditional().getOrderId()));

        if (CollectionUtils.isNotEmpty(returnOrderList)) {
            for (ReturnDetail returnOrder : returnOrderList) {
                ReturnCourierDetail returnCourierDetail = returnOrderActionService.findTopByReturnIdOrderByIdDesc(returnOrder.getId());
                if(returnCourierDetail != null){
                    createReturnEvent(returnOrder, request, returnCourierDetail.getReverseCourier());
                    saveOrderComment(returnOrder, order, returnCourierDetail.getReverseCourier());
                }
            }
            sendRPUInitiationSms(returnOrderList, request);
        }
    }

    private void createReturnEvent(ReturnDetail returnOrder, InitiateReverseRequest request, String courierName) {
        String remarks = "On clickpost response";
        if (request.isQcAtDoorStepEligible()) {
            remarks = "QcAtDoorStepEligible qcType=" + request.getAdditional().getQcType();
        }
        remarks = remarks + ", Courier Name: " + courierName;
        returnEventService.createReturnEvent(returnOrder.getRequestId(), returnOrder.getId(), ReturnStatus.REFERENCE_ID_ISSUED.getName().toUpperCase(), remarks);
    }

    private void saveOrderComment(ReturnDetail returnOrder, OrdersDTO order, String courierName) {
        ReturnDetailItem roi = returnOrderActionService.findTopByReturnId(returnOrder.getId());
        String orderComment = "Courier pickup arranged for product id " + (roi.getProductId() != null ? String.valueOf(roi.getProductId()) : "null") + " via " + courierName;
        CommentDTO commentDTO = new CommentDTO();
        commentDTO.setComment(Map.of("comment", orderComment, "comment_type", "cust"));
        commentDTO.setOrderId(order.getOrderId());
        kafkaService.pushToKafka("order_comments_queue", String.valueOf(order.getIncrementId()), commentDTO);
    }

    private void sendRPUInitiationSms(List<ReturnDetail> returnOrderList, InitiateReverseRequest request) {
        log.info("[processMessage] cpId : {}", request.getShipmentDetails().getCourierPartner());
        CommRequestDTO commRequestDTO = new CommRequestDTO();
        commRequestDTO.setEventType(ReturnStatus.REFERENCE_ID_ISSUED.getStatus());
        commRequestDTO.setReturnRequestId(returnOrderList.get(0).getRequestId());
        commRequestDTO.setReturnId(returnOrderList.get(0).getId());
        commRequestDTO.setOrderId(returnOrderList.get(0).getIncrementId());
        communicationService.pushToCommunicationTopic(commRequestDTO);
    }

    private String handleException(Throwable e, String logMessage) {
        log.error(logMessage, e);
        return e.getMessage();
    }

    private void finalizeProcessing(String error, InitiateReverseRequest request, OrdersDTO order) {
        log.info("Error in [ReturnCreateCourierMessageProcessor]: {}, {}", error, request);
        if (error != null && request != null) {
            saveInReturnHistoryAndOrderStatusHistoryForCourierError(request, error, order);
            if (!error.equals(COURIER_REASSIGN_EXCEPTION)) {
                checkResponseAndRetry(request, error);
            }
        }
    }

    private void saveInReturnHistoryAndOrderStatusHistoryForCourierError(InitiateReverseRequest initiateReverseRequest, String error, OrdersDTO order) {
        try {
            long groupId = getGroupId(initiateReverseRequest.getShipmentDetails().getReferenceNumber());
            log.info("[saveInReturnHistoryAndOrderStatusHistoryForCourierError]: {}, error: {}", groupId, error);
            List<ReturnDetail> returnOrderList = returnOrderActionService.findAllByGroupIdAndIncrementId(groupId, order.getIncrementId());
            if (CollectionUtils.isNotEmpty(returnOrderList)) {
                for (ReturnDetail returnOrder : returnOrderList) {
                    returnEventService.persistEvent(returnOrder.getRequestId(), returnOrder.getId(), Constant.RETURN_STATUS.PICKUP_REGISTRATION_FAILED.toUpperCase(), String.format("%s", error.substring(0, Math.min(error.length(), 1000))));
                    kafkaService.pushToKafka(ORDER_COMMENT_QUEUE, String.valueOf(returnOrder.getIncrementId()), Map.of("comment", String.format("Pickup registration failed for cpId %s via clickpost for return %d", initiateReverseRequest.getShipmentDetails().getCourierPartner(), returnOrder.getId()), "comment_type", "cust"));
                    log.info("[saveInReturnHistoryAndOrderStatusHistoryForCourierError]: {}", returnOrder.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error in [saveInReturnHistoryAndOrderStatusHistoryForCourierError]: {}", e.getMessage());
        }
    }

    private Long getGroupId(String referenceNumber) {
        String[] arr = referenceNumber.split("-");
        long groupId = 0;
        if (arr.length == 1) {
            groupId = Long.parseLong(arr[0]);
        } else {
            groupId = Long.parseLong(arr[1]);
        }
        return groupId;
    }

    private void checkResponseAndRetry(InitiateReverseRequest initiateReverseRequest, String error) {
        int maxRetry = Integer.parseInt(systemPreferenceService.getSystemPreferenceValues(COURIER_ASSIGN_RETRY_LIMIT, SYSTEM_CACHE, 30, TimeUnit.MINUTES));

        if (initiateReverseRequest.getCourierAssignRetryMetaData().getRetryCount() < maxRetry) {
            initiateReverseRequest.getCourierAssignRetryMetaData().setRetryCount(initiateReverseRequest.getCourierAssignRetryMetaData().getRetryCount() + 1);
            if (initiateReverseRequest.getCourierAssignRetryMetaData().getAttemptedCouriers() == null) {
                initiateReverseRequest.getCourierAssignRetryMetaData().setAttemptedCouriers(new ArrayList<>());
            }
            initiateReverseRequest.getCourierAssignRetryMetaData().getAttemptedCouriers().add(initiateReverseRequest.getShipmentDetails().getCourierPartner());
            log.info("Error in courier assigning, sending for retry: {}", initiateReverseRequest.getShipmentDetails().getReferenceNumber());
            try {
                kafkaService.pushToKafka(Constant.RETURN_CREATE_COURIER_SERVICE_QUEUE, initiateReverseRequest.getShipmentDetails().getReferenceNumber(), initiateReverseRequest);
            } catch (Exception e) {
                log.error("Error in [checkResponseAndRetry]:{}", e.getMessage());
            }
        } else {
            log.info("Max courier assign retry exhausted for: {}", initiateReverseRequest.getShipmentDetails().getReferenceNumber());
        }
    }
}
