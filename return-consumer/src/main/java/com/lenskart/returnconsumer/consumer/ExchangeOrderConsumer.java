package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.CommRequestDTO;
import com.lenskart.returncommon.model.dto.ExchangeOrderCreationRequest;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnservice.service.ICommunicationService;
import com.lenskart.returnservice.service.IExchangeOrderService;
import com.lenskart.returnservice.service.IReturnEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.lenskart.returncommon.utils.Constant.EVENT.*;

@Slf4j
@Service
public class ExchangeOrderConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IExchangeOrderService exchangeOrderService;

    @Autowired
    private IReturnEventService returnEventService;

    @Autowired
    ICommunicationService communicationService;

    @KafkaListener(id = "exchange-order-queue-1", topics = "${exchange.order.queue:exchange_order_queue}", groupId = "${exchange.order.queue.group:Exchange_order_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ExchangeOrderConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[ExchangeOrderConsumer][consume] exception found in listenConsumer1 : "+exception);
        }finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message) {
        ExchangeOrderCreationRequest exchangeOrderCreationRequest = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ExchangeOrderConsumer][processMessage] empty message received");
            }else{
                exchangeOrderCreationRequest = mapper.readValue(message, ExchangeOrderCreationRequest.class);
                if(exchangeOrderCreationRequest != null){
                    Boolean exchangeOrderCreated = exchangeOrderService.createExchangeOrder(exchangeOrderCreationRequest);
                    Integer requestId = returnEventService.getRequestId(exchangeOrderCreationRequest.getReturnId());
                    CommRequestDTO commRequestDTO = new CommRequestDTO();
                    commRequestDTO.setEventType(EXCHANGE_CREATION);
                    commRequestDTO.setReturnRequestId(requestId);
                    commRequestDTO.setReturnId(exchangeOrderCreationRequest.getReturnId());
                    commRequestDTO.setOrderId(exchangeOrderCreationRequest.getItems().get(0).getOrderId());
                    Map<String, String> conditionsMap = new HashMap<>();

                    if(exchangeOrderCreated){
                        returnEventService.createReturnEvent(requestId, exchangeOrderCreationRequest.getReturnId(), EXCHANGE_ORDER_CREATED, "exchange order for return : "+exchangeOrderCreationRequest.getReturnId());

                        conditionsMap.put("isExchangeCreated", "true");
                    } else {
                        conditionsMap.put("isExchangeCreated", "false");
                    }
                    communicationService.pushToCommunicationTopic(commRequestDTO);
                }
            }
        }catch (Exception exception){
            log.error("[ExchangeOrderConsumer][processMessage] exception found : "+exception);
        }
    }
}
