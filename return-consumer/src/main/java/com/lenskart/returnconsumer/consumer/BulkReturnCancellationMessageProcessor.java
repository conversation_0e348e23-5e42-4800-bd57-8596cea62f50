package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.dto.UwOrderDTO;
import com.lenskart.returncommon.model.dto.ReturnDetailUpdateRequestDto;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.enums.IdentifierType;
import com.lenskart.returncommon.model.response.ReturnCancellabilityResponse;
import com.lenskart.returncommon.model.response.ReturnDetailsUpdateResponse;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.IReturnDetailsService;
import com.lenskart.returnservice.service.IReturnOrderActionService;
import com.lenskart.returnservice.service.IReturnUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class BulkReturnCancellationMessageProcessor {

    @Autowired
    private IReturnDetailsService returnDetailsService;

    @Autowired
    private IReturnOrderActionService returnOrderActionService;

    @Autowired
    private IReturnUpdateService returnUpdateService;

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @KafkaListener(id = "return-bulk-cancellation", topics = "${return.bulk.cancellation.topic:return-bulk-cancellation}", groupId = "${return.bulk.cancellation.topic.group:return-bulk-cancellation-consumer-1}" , containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[BulkReturnCancellationMessageProcessor][consume] request consumed : {} , by : {}", message, Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[BulkReturnCancellationMessageProcessor][consume] exception found in consume : ", exception);
        }
    }

    private void processMessage(String message) {
        try{
            log.info("[BulkReturnOrderCancellation][processMessage] Trying to consume message {}", message);
            String filteredMessage = message.replace("\\", "");
            if(filteredMessage.indexOf("\"")==0 && filteredMessage.lastIndexOf("\"")==filteredMessage.length()-1) {
                filteredMessage = filteredMessage.substring(1, filteredMessage.length() - 1);
            }

            String[] returnIds = filteredMessage.split(",");
            log.info("[BulkReturnOrderCancellation][processMessage] List ReturnIDs to cancel: {}", Arrays.toString(returnIds));
            for(String returnIdstr:returnIds){
                returnIdstr = returnIdstr.trim();
                Integer returnId = Integer.parseInt(returnIdstr);
                try {
                    log.info("[BulkReturnOrderCancellation][processMessage] Querying on return_order_item table, return-id: {}", returnId);
                    List<ReturnDetailItem> returnOrderItems = returnOrderActionService.findAllByReturnId(returnId);
                    if(CollectionUtils.isEmpty(returnOrderItems)){
                        return;
                    }

                    for(ReturnDetailItem returnOrderItem : returnOrderItems){
                        if(returnOrderItem==null ||   returnOrderItem.getUwItemId()==null){
                            log.info("[BulkReturnOrderCancellation][processMessage] returnOrderItem or uw_item_id is null");
                            continue;
                        }

                        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = orderOpsFeignClient.getPurchaseOrderDetails(IdentifierType.UW_ITEM_ID.name(), String.valueOf(returnOrderItem.getUwItemId())).getBody();
                        if (purchaseOrderDetailsDTO != null && CollectionUtils.isNotEmpty(purchaseOrderDetailsDTO.getUwOrders())) {
                            UwOrderDTO order = purchaseOrderDetailsDTO.getUwOrders().get(0);
                            if (order != null && order.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                                log.info("[BulkReturnOrderCancellation][processMessage] it's a b2b order, return-id: {}", returnId);
                                if (order.getB2bRefrenceItemId() != null) {
                                    log.info("[BulkReturnOrderCancellation][processMessage] it's a b2b order, reference-item-id: {}", order.getB2bRefrenceItemId());
                                    List<ReturnDetailItem> returnOrderItemList = returnOrderActionService.findReturnOrderItemByUwItemId(order.getB2bRefrenceItemId());
                                    for (ReturnDetailItem orderItem : returnOrderItemList) {
                                        ReturnCancellabilityResponse returnCancellabilityResponseForB2BReferenceItem = returnDetailsService.getReturnCancellabilityForItem(orderItem.getUwItemId());
                                        ReturnCancellabilityResponse returnCancellabilityResponseForB2BItem = returnDetailsService.getReturnCancellabilityForItem(returnOrderItem.getUwItemId());
                                        log.info("[BulkReturnOrderCancellation][processMessage] returnCancellabilityResponse for b2breferenceitem: {} for b2bitem: {}", returnCancellabilityResponseForB2BReferenceItem, returnCancellabilityResponseForB2BItem);
                                        if (returnCancellabilityResponseForB2BReferenceItem.getIsCancellable() && returnCancellabilityResponseForB2BItem.getIsCancellable()) {
                                            log.info("[BulkReturnOrderCancellation][processMessage] Calling return cancellation service for return-id: {}", orderItem.getReturnId());
                                            saveReturnEvent(returnUpdateService.updateReturnStatus(getReturnDetailsUpdateRequest(orderItem.getReturnId(), "cancelled", "cancelled by return cancel bulk panel", "")), returnId);
                                            log.info("[BulkReturnOrderCancellation][processMessage] Calling return cancellation service for return-id: {}", returnId);
                                            saveReturnEvent(returnUpdateService.updateReturnStatus(getReturnDetailsUpdateRequest(returnId, "cancelled", "cancelled by return cancel bulk panel", "")), returnId);
                                            break;
                                        }
                                    }
                                }
                                log.info("[BulkReturnOrderCancellation][processMessage] Skip as it is a b2b order");
                                continue;
                            }
                        }

                        log.info("[BulkReturnOrderCancellation][processMessage] Calling return service to check eligibility, uw_item_id: {}", returnOrderItem.getUwItemId());
                        ReturnCancellabilityResponse returnCancellabilityResponse = returnDetailsService.getReturnCancellabilityForItem(returnOrderItem.getUwItemId());
                        log.info("[BulkReturnOrderCancellation][processMessage] Calling return service to check eligibility, return-id: {} returnCancellabilityResponse : {}", returnId, returnCancellabilityResponse);
                        if(returnCancellabilityResponse.getIsCancellable()){
                            log.info("[BulkReturnOrderCancellation][processMessage] Calling return cancellation service for return-id: {}", returnId);
                            saveReturnEvent(returnUpdateService.updateReturnStatus(getReturnDetailsUpdateRequest(returnId,"cancelled","cancelled by return cancel bulk panel","")), returnId);
                            break;
                        }
                    }
                } catch (Exception e){
                    log.error("[BulkReturnOrderCancellation][processMessage] Error occurred while cancelling order, return-id: {} , error: ", returnId, e);
                }
            }

        } catch (Exception e){
            log.info("[BulkReturnOrderCancellation][processMessage] Error occurred while consuming message, error ", e);
        }
    }

    private ReturnDetailUpdateRequestDto getReturnDetailsUpdateRequest(Integer returnId, String status, String comments, String username) {
        ReturnDetailUpdateRequestDto request = new ReturnDetailUpdateRequestDto();
        request.setReturnId(returnId);
        request.setStatus(status);
        request.setComments(comments);
        request.setUsername(username);
        return request;
    }

    private void saveReturnEvent(ReturnDetailsUpdateResponse response, Integer returnId) throws JsonProcessingException {
        ReturnEvent returnEvent = new ReturnEvent();
        returnEvent.setReturnId(returnId);
        if (response.getStatus().equalsIgnoreCase("success")) {
            returnEvent.setEvent("BULK_CANCELLATION_SUCCESS");
        } else {
            returnEvent.setEvent("BULK_CANCELLATION_FAILURE");
        }
        returnEvent.setRemarks(response.getMsg());
        returnEvent.setCreatedAt(new Date());
        returnEvent.setSource("vsm_panel");
        returnEventRepository.save(returnEvent);
    }
}
