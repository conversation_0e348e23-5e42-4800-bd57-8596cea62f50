package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.Product;
import com.lenskart.ordermetadata.dto.request.ReturnItemDTO;
import com.lenskart.ordermetadata.dto.response.ShippingStatusDetail;
import com.lenskart.refund.client.model.response.RefundMethodResponse;
import com.lenskart.returncommon.config.IgnoreMetadataMixin;
import com.lenskart.returncommon.model.dto.*;
import com.lenskart.returncommon.model.enums.AutoApproval;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returncommon.model.request.AutoApprovalRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequest;
import com.lenskart.returncommon.model.request.ReturnCreationRequestDTO;
import com.lenskart.returncommon.model.request.SprinklrNeedApprovalRequest;
import com.lenskart.returncommon.model.response.CustomerStoreProfileResponse;
import com.lenskart.returncommon.model.response.PurchaseOrderDetailsDTO;
import com.lenskart.returncommon.model.response.ReturnAutoApprovalRules;
import com.lenskart.returncommon.model.response.ReturnCreationResponse;
import com.lenskart.returncommon.utils.Constant;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.SystemPreference;
import com.lenskart.returnrepository.repository.ReturnDetailItemRepository;
import com.lenskart.returnservice.feignclient.OrderOpsFeignClient;
import com.lenskart.returnservice.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class ReturnAutoApprovalMessageConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY))
            .addMixIn(ReturnCreationResponse.class, IgnoreMetadataMixin.class);

    @Autowired
    private OrderOpsFeignClient orderOpsFeignClient;

    @Autowired
    private ISystemPreferenceService systemPreferenceService;

    @Autowired
    private IMvcService mvcService;

    @Autowired
    private ReturnDetailItemRepository returnDetailItemRepository;

    protected List<String> LK_FACILITY_CODE_LIST = new ArrayList<>();

    private final static int MAX_RETRY = 2;

    @Autowired
    private IKafkaService kafkaService;

    @Autowired
    private IOrderUtilsService orderUtilsService;

    @Autowired
    private IReturnRefundEligibilityService returnRefundEligibilityService;

    @Autowired
    private INexsFacilityService nexsFacilityService;

    @Value("${dealskart.manesar.facility:LKH03}")
    private String dealskartfacility;

    @Autowired
    private INeedApprovalProcessorService needApprovalProcessorService;

    @Autowired
    private IJunoService junoService;

    @Autowired
    private IRefundUtilsService refundUtilService;

    @PostConstruct
    public void init(){
        SystemPreference facility = systemPreferenceService.findOneByGroupAndKey("revised_edd", "facility");
        if (facility != null && facility.getValue() != null) {
            LK_FACILITY_CODE_LIST = Arrays.asList(facility.getValue().split("\\s*,\\s*"));
        }
    }


    @KafkaListener(id = "return-auto-approval-queue-1", topics = "${return.auto.approval.queue:return_auto_approval_queue}", groupId = "${return.auto.approval.queue.group:return_auto_approval_queue_group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack){
        try {
            log.info("[ReturnAutoApprovalMessageConsumer][consume] request consumed : " + message + " , by : "+Thread.currentThread().getName());
            processMessage(message);
            ack.acknowledge();
        } catch (Exception exception) {
            log.error("[ReturnAutoApprovalMessageConsumer][consume] exception found in consume : "+exception);
        }
    }

    private void processMessage(String message) {
        boolean retry = false;
        AutoApprovalRequest autoApprovalRequest = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ExchangeOrderConsumer][processMessage] empty message received");
            }else{
                autoApprovalRequest = mapper.readValue(message, AutoApprovalRequest.class);
                if(autoApprovalRequest != null){
                    retry = mvcAutoApproveReturnExchange(autoApprovalRequest.getReturnCreationRequest(), autoApprovalRequest.getRefundRequestResponse(), autoApprovalRequest.getRetryCount());
                    checkResponseAndRetry(autoApprovalRequest, retry);
                }
            }
        }catch (Exception exception){
            log.error("[ExchangeOrderConsumer][processMessage] exception found : "+exception);
        }
    }

    private void checkResponseAndRetry(AutoApprovalRequest autoApprovalRequest,
                                       boolean response) throws Exception {
        if (autoApprovalRequest.getRetryCount() <= MAX_RETRY && response) {
            autoApprovalRequest.setRetryCount((autoApprovalRequest.getRetryCount() + 1));
            log.info("[checkResponseAndRetry] call to return auto approval return cycle {}", autoApprovalRequest);
            kafkaService.pushToKafka("return_auto_approval_queue", String.valueOf(autoApprovalRequest.getReturnCreationRequest().getIncrementId()), autoApprovalRequest);
        } else if (autoApprovalRequest.getRetryCount() > MAX_RETRY) {
            log.info("[checkResponseAndRetry] return auto approval reached max retry count for {}", autoApprovalRequest);
        }
    }

    private boolean mvcAutoApproveReturnExchange(ReturnCreationRequestDTO returnCreationRequest, ReturnCreationResponse refundRequestResponse, int retryCount)
    {
        boolean retry=false;
        try {
            log.info("[mvcAutoApproveReturnExchange] order:{}", refundRequestResponse.getIncrementId());

            HashMap<String, Boolean> autoApprovalMap = new HashMap<>();
            autoApprovalMap.put("isActionTaken", false);
            autoApprovalMap.put("isRuleFound", false);

            boolean isMvc = false;

            for(ReturnItemDTO returnItem:returnCreationRequest.getItems()) {
                boolean isNeedApproval = returnItem.getNeedApproval();

                if (returnItem.getUwItemId() == null) {
                    log.info("[mvcAutoApproveReturnExchange] uwItemId is null in request of auto approval case");
                    break;
                }

                if(isNeedApproval){
                    ResponseEntity<PurchaseOrderDetailsDTO> purchaseOrderDetailsResponseEntity = orderOpsFeignClient.getPurchaseOrderDetails("UW_ITEM_ID", String.valueOf(returnItem.getUwItemId()));
                    if(purchaseOrderDetailsResponseEntity.getStatusCode().is2xxSuccessful() && purchaseOrderDetailsResponseEntity.getBody() != null && !CollectionUtils.isEmpty(purchaseOrderDetailsResponseEntity.getBody().getUwOrders())){
                        PurchaseOrderDetailsDTO purchaseOrderDetailsDTO = purchaseOrderDetailsResponseEntity.getBody();
                        UwOrderDTO uwOrder = purchaseOrderDetailsDTO.getUwOrders().get(0);

                        if(uwOrder!=null){
                            log.info("[mvcAutoApproveReturnExchange] uwOrder uwItemId:{}, incrementId:{}",uwOrder.getUwItemId(), uwOrder.getIncrementId());
                            returnCreationRequest.setIncrementId(uwOrder.getIncrementId());
                            refundRequestResponse.setIncrementId(uwOrder.getIncrementId());
                        }else{
                            log.info("[mvcAutoApproveReturnExchange] uwOrder is null for mvc auto approval case");
                            break;
                        }

                        isMvc = checkMvcCustomer(returnCreationRequest.getIncrementId(), purchaseOrderDetailsDTO.getOrders().get(0));
                        boolean isMvcAndEyeGlass = isMvc && isProductEyeGlass(uwOrder.getClassification());

                        log.info("[mvcAutoApproveReturnExchange] isMvcAndEyeGlass:{} and isNeedApproval:{}",isMvcAndEyeGlass,isNeedApproval);

                        UwOrderDTO uwOrderPhysical=null;
                        UwOrderDTO uwOrderVirtual=null;

                        List<String > whFacilityList = LK_FACILITY_CODE_LIST;

                        if("B2B".equalsIgnoreCase(uwOrder.getProductDeliveryType())){
                            if(!whFacilityList.contains(uwOrder.getFacilityCode())){
                                uwOrderVirtual = uwOrder;
                                ResponseEntity<UwOrderDTO> opsFeignClientUwOrderInfo = orderOpsFeignClient.getUwOrderInfo("UW_ITEM_ID", String.valueOf(uwOrder.getB2bRefrenceItemId()));
                                if(opsFeignClientUwOrderInfo.getStatusCode().is2xxSuccessful()){
                                    uwOrderPhysical = opsFeignClientUwOrderInfo.getBody();;
                                }
                            }else{
                                uwOrderPhysical=uwOrder;
                                ResponseEntity<UwOrderDTO> opsFeignClientUwOrderInfo = orderOpsFeignClient.getUwOrderInfo("UW_ITEM_ID", String.valueOf(uwOrder.getB2bRefrenceItemId()));
                                if(opsFeignClientUwOrderInfo.getStatusCode().is2xxSuccessful()){
                                    uwOrderVirtual = opsFeignClientUwOrderInfo.getBody();;
                                }
                            }
                        }else{
                            uwOrderPhysical=uwOrder;
                            uwOrderVirtual=uwOrder;
                        }

                        ReturnDetailItem returnDetailItem = returnDetailItemRepository.findTop1ByUwItemIdOrderByIdDesc(uwOrderVirtual.getUwItemId());
                        if(returnDetailItem == null){
                            log.info("[mvcAutoApproveReturnExchange] returnOrderItem is null for returnId:{}",refundRequestResponse.getResult().getReturns().get(0).getReturnId());
                            break;
                        }

                        if(isMvcAndEyeGlass) {
                            autoApprovalMap=returnApprovalForMvc(returnCreationRequest,refundRequestResponse,uwOrderPhysical,uwOrderVirtual,returnItem,returnDetailItem, purchaseOrderDetailsDTO);
                        }else{
                            autoApprovalMap=returnApprovalRejectionByRules(returnCreationRequest,refundRequestResponse,uwOrderPhysical,uwOrderVirtual,returnItem.getMagentoId(),returnDetailItem, purchaseOrderDetailsDTO.getOrders().get(0), purchaseOrderDetailsDTO);
                        }

                        if(!autoApprovalMap.get("isRuleFound") || (retryCount==MAX_RETRY && !autoApprovalMap.get("isActionTaken"))){
                            sendDataToSprinkler(returnDetailItem.getReturnId(),returnItem.getMagentoId(),uwOrderVirtual.getUwItemId(),returnCreationRequest.getIncrementId(),returnCreationRequest.getStoreFacilityCode());
                        }
                    }
                }
            }

            //considering request is coming for only one magentoId
            if(autoApprovalMap.get("isRuleFound") && !autoApprovalMap.get("isActionTaken")){
                retry=true;
            }
        }catch (Exception exception){
            log.error("[mvcAutoApproveReturnExchange] exception : {}",exception.getMessage());
        }

        log.info("[mvcAutoApproveReturnExchange] order : {}, retry : {}", refundRequestResponse.getIncrementId(), retry);
        return retry;
    }

    public HashMap<String,Boolean> returnApprovalForMvc(ReturnCreationRequestDTO returnCreationRequest, ReturnCreationResponse refundRequestResponse, UwOrderDTO uwOrderPhysical, UwOrderDTO uwOrderVirtual, ReturnItemDTO returnItem, ReturnDetailItem returnDetailItemVirtual, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO){
        HashMap<String,Boolean> actionTakenAndRuleFound=new HashMap<>();
        boolean isRuleFound =true;
        boolean isApprovedActionTaken =false;
        try {
            log.info("Inside mvcAutoApproveReturnExchange:{}", refundRequestResponse.getIncrementId());

            int returnEligibilityPeriod = 30;
            int exchangeEligibilityPeriod = 365;

            populateUwOrderAndUwOrderWH(refundRequestResponse);
            UwOrderDTO uwOrder = refundRequestResponse.getUwOrder();

            String returnPeriod = systemPreferenceService.getSystemPreferenceValues("ReturnPeriod", "mvc_return_period");
            log.info("System preference value for returnPeriod is {}", returnPeriod);
            if (Objects.nonNull(returnPeriod) && !org.springframework.util.StringUtils.isEmpty(returnPeriod)) {
                returnEligibilityPeriod = Integer.parseInt(returnPeriod);
            }

            String exchangePeriod = systemPreferenceService.getSystemPreferenceValues("ExchangePeriod", "mvc_return_period");
            log.info("System preference value for exchangePeriod is {}", exchangePeriod);
            if (Objects.nonNull(exchangePeriod) && !org.springframework.util.StringUtils.isEmpty(exchangePeriod)) {
                exchangeEligibilityPeriod = Integer.parseInt(exchangePeriod);
            }

            Calendar c = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            ResponseEntity<List<ShippingStatusDetail>> shippingResponse = orderOpsFeignClient.getShippingDetails(refundRequestResponse.getIncrementId(), "");
            String returnEligibileTillDate = returnRefundEligibilityService.fetchReturnEligibleTilDate(refundRequestResponse.getUwOrder(), refundRequestResponse.getUwOrderWH(), returnEligibilityPeriod, shippingResponse.getBody(), null, null).getReturnEligiblePeriodDate();
            log.info("returnEligibileTillDate:{}", returnEligibileTillDate);
            Date returnEligibilityDate = sdf.parse(returnEligibileTillDate);
            c.setTime(returnEligibilityDate);
            c.add(Calendar.DATE, exchangeEligibilityPeriod);
            c.add(Calendar.DATE, -returnEligibilityPeriod);

            String exchangeEligibleTillDate = sdf.format(c.getTime());
            log.info("exchangeEligibleTillDate:{}", exchangeEligibleTillDate);

            Date currentDate = new Date();
            String currentDateString = sdf.format(currentDate);

            boolean isReturnAllowed = returnEligibileTillDate.compareTo(currentDateString) > 0;
            boolean isExchangedAllowed = exchangeEligibleTillDate.compareTo(currentDateString) > 0;

            log.info("currentDateString:{}", currentDateString);
            NeedApprovalRequest needApprovalRequest = createNeedApprovalRequest(refundRequestResponse);
            log.info("needApprovalRequest:{}", needApprovalRequest.toString());
            log.info("isReturnAllowed:{}", isReturnAllowed);
            log.info("isExchangedAllowed:{}", isExchangedAllowed);
            if (isReturnAllowed) {
                //allow exchange and refund methods
                List<String> refundMethods = refundMethodsFromRefundService(uwOrder, refundRequestResponse.getPurchaseOrderDetailsDTO());
                if (!refundMethods.contains("exchange")) {
                    needApprovalRequest.setDelightMethod(String.join(", ", refundMethods) + ", exchange");
                } else {
                    needApprovalRequest.setDelightMethod(String.join(", ", refundMethods));
                }

                isApprovedActionTaken = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
                log.info("auto approved by delight through system for refund and exchange:{},incrementId:{}", isApprovedActionTaken, refundRequestResponse.getIncrementId());
            } else if (isExchangedAllowed) {
                //allow exchange only
                needApprovalRequest.setDelightMethod("exchange");

                isApprovedActionTaken = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
                log.info("auto approved by delight through system for exchange only:{} ,incrementId:{}", isApprovedActionTaken, refundRequestResponse.getIncrementId());
            }else{
                return returnApprovalRejectionByRules(returnCreationRequest,refundRequestResponse,uwOrderPhysical,uwOrderVirtual,returnItem.getMagentoId(),returnDetailItemVirtual, purchaseOrderDetailsDTO.getOrders().get(0), purchaseOrderDetailsDTO);
            }

        }catch (Exception exception){
            log.error("[returnApprovalForMvc] exception : {}",exception.getMessage());
        }

        actionTakenAndRuleFound.put("isActionTaken",isApprovedActionTaken);
        actionTakenAndRuleFound.put("isRuleFound",isRuleFound);
        return actionTakenAndRuleFound;
    }

    private NeedApprovalRequest createNeedApprovalRequest(ReturnCreationResponse refundRequestResponse) {
        NeedApprovalRequest needApprovalRequest = new NeedApprovalRequest();
        needApprovalRequest.setReturnId(refundRequestResponse.getResult().getReturns().get(0).getReturnId());
        needApprovalRequest.setStatus("return_accepted");
        needApprovalRequest.setDelightAction("APPROVE");
        needApprovalRequest.setComments("Pending approval return_accepted auto approved by system : <EMAIL> with reason - mvc customer");
        needApprovalRequest.setDelightComment("AutoApproved");
        needApprovalRequest.setUsername("system");

        ApprovalStatusRequest approvalStatusRequest = new ApprovalStatusRequest();
        approvalStatusRequest.setReturnStatus("return_accepted");
        approvalStatusRequest.setMagentoItemId(refundRequestResponse.getResult().getReturns().get(0).getMagentoItemId());
        approvalStatusRequest.setRetryCount(0);
        approvalStatusRequest.setSelectedRefundAction("refund");//confirm with Jitender sir if it should have value "exchange/refund/'' "

        needApprovalRequest.setApprovalStatusRequest(approvalStatusRequest);
        return needApprovalRequest;
    }

    private List<String> refundMethodsFromRefundService(UwOrderDTO uwOrder, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO) {
        List<String> eligibleRefundMethods = null;
        if (purchaseOrderDetailsDTO != null) {
            OrdersHeaderDTO ordersHeader = purchaseOrderDetailsDTO.getOrdersHeaderDTO();
            OrdersDTO order = purchaseOrderDetailsDTO.getOrders().stream().filter(o -> uwOrder.getItemId() == o.getItemId()).findFirst().orElse(null);
            if (order != null) {
                String paymentMethod = orderUtilsService.getPaymentMethod(uwOrder, purchaseOrderDetailsDTO.getUwOrders(), purchaseOrderDetailsDTO.getOrders(), order.getMethod());
                RefundMethodResponse refundMethodResponse = refundUtilService.getRefundMethod("auto_approval_at_pos", ordersHeader.getPaymentMode(), paymentMethod, ordersHeader.getLkCountry(), uwOrder.getIncrementId());
                eligibleRefundMethods = refundMethodResponse != null ? refundMethodResponse.getEligibleRefundMethod() : null;
            }
        }

        if (!CollectionUtils.isEmpty(eligibleRefundMethods)) {
            return eligibleRefundMethods;
        } else {
            return new ArrayList<>();
        }

    }

    public HashMap<String,Boolean> returnApprovalRejectionByRules(ReturnCreationRequestDTO returnCreationRequest,ReturnCreationResponse refundRequestResponse,UwOrderDTO uwOrderPhysical,UwOrderDTO uwOrderVirtual,Long magentoId,ReturnDetailItem returnDetailItemVirtual, OrdersDTO ordersDTO, PurchaseOrderDetailsDTO purchaseOrderDetailsDTO){
        HashMap<String,Boolean> autoApprovalMap=new HashMap<>();
        boolean isRuleFound =true;
        boolean isApprovedActionTaken=false;
        try {
            log.info("inside returnApprovalRejectionByRules");
            Long customerId = ordersDTO.getCustomerId();
            String classificationName = uwOrderVirtual.getClassificationName();

            Map<String, Object> productDetailsMap = junoService.getProductDetails(uwOrderVirtual.getProductId());
            String modelName = "";
            if(productDetailsMap!=null) {
                modelName = (productDetailsMap.get(Constant.JUNO_PRODUCT.MODEL_NAME) != null ? (String) productDetailsMap.get(Constant.JUNO_PRODUCT.MODEL_NAME) : null);
            }

            log.info("returnApprovalRejectionByRules customerId:{} ,classificationName:{},modelName:{},secondaryReturnReason:{}", customerId, classificationName,modelName,returnDetailItemVirtual.getReasonForReturn());

            ReturnAutoApprovalRules approvalRules = mvcService.getApprovalStatusAndRefundMethod(customerId, returnCreationRequest.getIncrementId(), uwOrderPhysical.getUwItemId(), modelName, classificationName,returnDetailItemVirtual.getReasonForReturn(),returnDetailItemVirtual.getReturnId());
            log.info("approvalRules from rms:{}", approvalRules);
            if(approvalRules==null){
                log.info("approvalRules from rms is empty");
                autoApprovalMap.put("isActionTaken",false);
                autoApprovalMap.put("isRuleFound",false);
                return autoApprovalMap;
            }

            AutoApproval status = approvalRules.getAutoApproval();

            if (AutoApproval.APPROVE.equals(status)) {

                if(org.springframework.util.StringUtils.isEmpty(approvalRules.getRefundMethod())){
                    log.info("approvalRules refund methof from rms is empty:{}",uwOrderPhysical.getUwItemId());
                    autoApprovalMap.put("isActionTaken",false);
                    autoApprovalMap.put("isRuleFound",false);
                    return autoApprovalMap;
                }

                List<String> refundMethods = Arrays.asList(approvalRules.getRefundMethod().split(","));
                if (refundMethods.contains("refund")) {
                    NeedApprovalRequest needApprovalRequest;

                    List<String> refunds = refundMethodsFromRefundService(uwOrderVirtual, purchaseOrderDetailsDTO);

                    if (refundMethods.contains("exchange")) {
                        needApprovalRequest = createNeedApprovalRequest(returnDetailItemVirtual.getReturnId(),magentoId, true, true, "by Rules");

                        if (refunds.isEmpty()) {
                            needApprovalRequest.setDelightMethod("exchange");
                        }else{
                            needApprovalRequest.setDelightMethod(String.join(",", refunds) + ",exchange");
                        }
                    } else {
                        if (refunds.isEmpty()) {
                            log.info("approval rules found for refund but refund methods could not found,incrementId:{}",returnCreationRequest.getIncrementId());
                            autoApprovalMap.put("isActionTaken",false);
                            autoApprovalMap.put("isRuleFound",false);
                            return autoApprovalMap;
                        }else{
                            needApprovalRequest = createNeedApprovalRequest(returnDetailItemVirtual.getReturnId(),magentoId,false, true, "by Rules");
                            needApprovalRequest.setDelightMethod(String.join(",", refunds));
                        }

                    }

                    isApprovedActionTaken = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
                    log.info("auto approved by rules through system for refund and exchange only:{} ,returnId:{}", isApprovedActionTaken, needApprovalRequest.getReturnId());
                } else if (refundMethods.contains("exchange")) {
                    NeedApprovalRequest needApprovalRequest = createNeedApprovalRequest(returnDetailItemVirtual.getReturnId(), magentoId,false, false, "by Rules");
                    needApprovalRequest.setDelightMethod("exchange");

                    isApprovedActionTaken = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
                    log.info("auto approved by rules through system for exchange only:{} ,returnId:{}", isApprovedActionTaken, needApprovalRequest.getReturnId());
                }else{
                    isRuleFound =false;
                }

            } else if (AutoApproval.REJECT.equals(status)) {
                NeedApprovalRequest needApprovalRequest = createRejectionsRequest(returnDetailItemVirtual.getReturnId(),magentoId);
                isApprovedActionTaken = needApprovalProcessorService.isSaveDelightActionProcessed(needApprovalRequest);
                log.info("auto rejected by rules through system :{} ,returnId:{}", isApprovedActionTaken, needApprovalRequest.getReturnId());
            }else{
                isRuleFound =false;
            }
        }catch (Exception e){
            log.error("[returnApprovalRejectionByRules] function, Exception message while approving return in  MvcAutoApproveReturnThreadPool = {} incrementId: {}", "scm-return-orders", refundRequestResponse.getIncrementId(), e);
        }

        autoApprovalMap.put("isActionTaken",isApprovedActionTaken);
        autoApprovalMap.put("isRuleFound",isRuleFound);

        return autoApprovalMap;
    }

    private NeedApprovalRequest createRejectionsRequest(Integer returnId,Long magentoId) {
        NeedApprovalRequest needApprovalRequest = new NeedApprovalRequest();
        needApprovalRequest.setReturnId(returnId);
        needApprovalRequest.setStatus("return_rejected_handover_pending");
        needApprovalRequest.setDelightAction("REJECT");
        needApprovalRequest.setDelightComment("rejected");
        needApprovalRequest.setUsername("system");
        needApprovalRequest.setComments("Pending approval return_rejected by rules from the system : with reason rejected");

        ApprovalStatusRequest approvalStatusRequest = new ApprovalStatusRequest();
        approvalStatusRequest.setReturnStatus("return_rejected");
        approvalStatusRequest.setMagentoItemId(magentoId);
        approvalStatusRequest.setRetryCount(0);
        approvalStatusRequest.setSelectedRefundAction("");

        needApprovalRequest.setApprovalStatusRequest(approvalStatusRequest);
        return needApprovalRequest;
    }

    private NeedApprovalRequest createNeedApprovalRequest(Integer returnId,Long magentoId,boolean isReturnAllowed,boolean isRefundAllowed,String comment) {
        NeedApprovalRequest needApprovalRequest = new NeedApprovalRequest();
        needApprovalRequest.setReturnId(returnId);
        needApprovalRequest.setStatus("return_accepted");
        needApprovalRequest.setDelightAction("APPROVE");
        needApprovalRequest.setDelightComment("AutoApproved");
        needApprovalRequest.setUsername("system");

        ApprovalStatusRequest approvalStatusRequest = new ApprovalStatusRequest();
        approvalStatusRequest.setReturnStatus("return_accepted");
        approvalStatusRequest.setMagentoItemId(magentoId);
        approvalStatusRequest.setRetryCount(0);
        if(isReturnAllowed){
            approvalStatusRequest.setSelectedRefundAction("exchange/refund");
            needApprovalRequest.setComments("Pending approval return_accepted auto approved, exchange and refund by system : with reason - "+comment);
        }else if(isRefundAllowed){
            approvalStatusRequest.setSelectedRefundAction("refund");
            needApprovalRequest.setComments("Pending approval return_accepted auto approved, refund by system : with reason - "+comment);
        }else{
            approvalStatusRequest.setSelectedRefundAction("exchange");
            needApprovalRequest.setComments("Pending approval return_accepted auto approved, exchange by system : with reason - "+comment);
        }

        needApprovalRequest.setApprovalStatusRequest(approvalStatusRequest);
        return needApprovalRequest;
    }

    public void sendDataToSprinkler(Integer returnId,Long magentoId,Integer uwItemId,Integer incrementId,String storeFacilityCode){
        try {
            SprinklrNeedApprovalRequest kafkaNeedApprovalRequest = new SprinklrNeedApprovalRequest();
            kafkaNeedApprovalRequest.setIncrementId(incrementId);
            kafkaNeedApprovalRequest.setReturnId(returnId);
            kafkaNeedApprovalRequest.setLkStoreCode(storeFacilityCode);
            kafkaNeedApprovalRequest.setUwItemId(uwItemId);
            kafkaNeedApprovalRequest.setMagentoItemId(magentoId);
            kafkaNeedApprovalRequest.setReturnStatus(ReturnStatus.RETURN_NEED_APPROVAL.getStatus());
            log.info("Pushing order status sync request to queue: scm-return-orders  :  {}", kafkaNeedApprovalRequest);
            orderOpsFeignClient.pushData(mapper.convertValue(kafkaNeedApprovalRequest, Map.class),"scm-return-orders");
        }catch (Exception e){
            log.error("[AutoApproveReturnMessageProcessor][sendDataToSprinkler] Exception message while pushing in kakfa topic = {} incrementId: {}", "scm-return-orders", incrementId,e);
        }
    }

    private boolean checkMvcCustomer(Integer incrementId, OrdersDTO ordersDTO) {
        try {
            int mvcMinimumValue = 9;

            String mvcMinValue = systemPreferenceService.getSystemPreferenceValues("mvcMinValue", "mvc_customer");
            log.info("System preference value for mvcMinValue is {}", mvcMinValue);

            if (Objects.nonNull(mvcMinValue) && !org.springframework.util.StringUtils.isEmpty(mvcMinValue)) {
                mvcMinimumValue = Integer.parseInt(mvcMinValue);
            }

            Long customerId = ordersDTO.getCustomerId();
            log.info("[ReturnAutoApproval] customerId in checkMvcCustomer:{}", customerId);
            CustomerStoreProfileResponse mvcResponseDTO = mvcService.getMvcMvsStats(customerId, null);
            MvcOrderDTO mvcOrderResponseOld = mvcService.getMvcOrdersResponse("ORDER_ID", String.valueOf(incrementId));

            if (mvcResponseDTO == null) {
                mvcResponseDTO = new CustomerStoreProfileResponse();
            }

            if (mvcOrderResponseOld != null) {
                mvcResponseDTO.setCustomerScore(mvcOrderResponseOld.getCustomerScore());
            }

            log.info("customer score in checkMvcCustomer:{}", mvcResponseDTO.getCustomerScore());
            return mvcResponseDTO.getCustomerScore() != null && mvcResponseDTO.getCustomerScore() > mvcMinimumValue;
        }catch (Exception e){
            log.error("Exception in checkMvcCustomer:{}",incrementId);
            return false;
        }
    }

    private boolean isProductEyeGlass(String classification) {
        return "11355".equals(classification);
    }

    private void populateUwOrderAndUwOrderWH(ReturnCreationResponse returnCreationResponse) {
        UwOrderDTO uwOrder = returnCreationResponse.getUwOrder();
        PurchaseOrderDetailsDTO orderResponse = returnCreationResponse.getPurchaseOrderDetailsDTO();
        if (uwOrder != null && "B2B".equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            boolean isNEXSFacility = nexsFacilityService.getNexsFacilities().contains(uwOrder.getFacilityCode());
            boolean isDealsKartFacility = dealskartfacility.equalsIgnoreCase(uwOrder.getFacilityCode());

            if (isDealsKartFacility || isNEXSFacility) {
                returnCreationResponse.setUwOrderWH(uwOrder); //uwOrder is Warehouse version

                //set other version then warehouse version
                returnCreationResponse.setUwOrder(orderResponse.getUwOrders().stream().filter(uw -> uwOrder.getUwItemId().equals(uw.getB2bRefrenceItemId())).findFirst().orElse(null));
            } else {
                //set warehouse version
                returnCreationResponse.setUwOrderWH(orderResponse.getUwOrders().stream().filter(uw -> uwOrder.getUwItemId().equals(uw.getB2bRefrenceItemId())).findFirst().orElse(null));
            }
        }
    }

}
