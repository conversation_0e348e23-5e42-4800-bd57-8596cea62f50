package com.lenskart.returnconsumer.consumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.ccautils.constants.CosmosConstants;
import com.lenskart.ccautils.util.CosmosEventPushUtil;
import com.lenskart.returncommon.model.enums.ReturnSources;
import com.lenskart.returncommon.model.enums.ReturnStatus;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnEvent;
import com.lenskart.returnrepository.repository.ReturnCourierDetailRepository;
import com.lenskart.returnrepository.repository.ReturnEventRepository;
import com.lenskart.returnservice.factory.cosmospayloadfactory.CosmosEventPayloadFactory;
import com.lenskart.returnservice.service.IEventDeduplicationService;
import com.lenskart.returnservice.service.IReturnOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.lenskart.returncommon.utils.Constant.RETURN_TOPICS.COSMOS_EVENT_QUEUE;

/* Created by rajiv on 12/02/25 */
@Slf4j
@Service
public class CosmosEventConsumer {

    private ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private IReturnOrderService returnOrderService;

    @Autowired
    private CosmosEventPushUtil cosmosEventPushUtil;

    @Autowired
    private ReturnEventRepository returnEventRepository;

    @Autowired
    private CosmosEventPayloadFactory cosmosEventPayloadFactory;

    @Autowired
    private IEventDeduplicationService eventDeduplicationService;

    @Autowired
    private ReturnCourierDetailRepository returnCourierDetailRepository;

    @KafkaListener(id = COSMOS_EVENT_QUEUE, topics = "${cosmos.event.queue:cosmos-event-queue}", groupId = "${cosmos.event.queue.group:cosmos-event-queue-group}", containerFactory = "kafkaPoolListenerContainerFactory")
    public void consume(String message, Acknowledgment ack) {
        try {
            log.info("[CosmosEventConsumer][consume] request consumed : " + message + " , by : " + Thread.currentThread().getName());
            processMessage(message);
        } catch (Exception exception) {
            log.error("[CosmosEventConsumer][consume] exception found : " + exception);
        }finally {
            ack.acknowledge();
        }
    }

    private void processMessage(String message) {
        ReturnEvent returnEvent = null;
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[CosmosEventConsumer][processMessage] empty message received");
            } else {
                returnEvent = mapper.readValue(message, ReturnEvent.class);
                if (returnEvent != null) {
                    pushEventToCosmos(returnEvent.getReturnId(), returnEvent.getEvent(), returnEvent.getReturnRequestId());
                }
            }
        } catch (Exception exception) {
            log.error("[CosmosEventConsumer][processMessage] exception found : " + exception);
        }
    }


    private void pushEventToCosmos(Integer returnId, String event, Integer requestId) {
        try {
            String status = returnOrderService.getReturnOrderStatusById(returnId);
            Integer incrementId = returnOrderService.getOrderIdByReturnId(returnId);
            ReturnDetail returnOrder = returnOrderService.getReturnDetailByReturnIdAndStatus(returnId, status);
            if(returnOrder != null){
                String reverseCourier = null;
                ReturnCourierDetail returnCourierDetail = returnCourierDetailRepository.findTopByReturnId(returnOrder.getId());
                if(returnCourierDetail != null){
                    reverseCourier = returnCourierDetail.getReverseCourier();
                }
                String cosmosEvent = getCosmosEvent(status, returnOrder.getSource(), returnOrder.getReturnMethod());
                Map<String, Object> payload = CosmosEventPayloadFactory.getEventPayload(cosmosEvent, status, returnId, reverseCourier);
                if (payload != null) {
                    log.info("[pushEventToCosmos] pushing cosmos event: {}, for returnID :{}, payload: {}", cosmosEvent, returnId, payload);
                    boolean pushCosmosEvent = eventDeduplicationService.pushEvent(cosmosEvent + "_" + returnId);
                    List<ReturnEvent> returnEvents = returnEventRepository.findByReturnId(returnId);
                    boolean repeatedCosmosEvent = returnEvents.stream()
                            .map(ReturnEvent::getEvent)
                            .anyMatch(ev -> ev.equalsIgnoreCase("COSMOS_" + cosmosEvent));
                    if(pushCosmosEvent && !repeatedCosmosEvent){
                        log.info("[pushEventToCosmos] returnId : {}, isEventPushed : {}", returnId, pushCosmosEvent);
                        cosmosEventPushUtil.pushEventToCosmosUsingReturnIds(payload, cosmosEvent, incrementId, Collections.singletonList(returnId), null);
                        ReturnEvent returnEvent = new ReturnEvent();
                        returnEvent.setReturnId(returnId);
                        returnEvent.setReturnRequestId(requestId);
                        returnEvent.setEvent("COSMOS_" + cosmosEvent);
                        returnEvent.setRemarks(payload.toString());
                        returnEvent.setCreatedAt(new Date());
                        returnEventRepository.save(returnEvent);
                    }
                }
                log.info("[pushEventToCosomos] Successfully pushed event to cosmos for event:{} and returnId: {}, payload:{} ", event, returnId, payload);
            }
            log.info("[pushEventToCosomos] event:{} and returnId: {} ", event, returnId);
        } catch (Exception exception) {
            log.error("[pushEventToCosomos] error occured for returnId:{} and event:{}", returnId, event,exception);
        }
    }

    private static final Map<String, String> COSMOS_EVENT_MAPPING = Map.ofEntries(
            Map.entry(ReturnStatus.CANCELLED.getStatus(), CosmosConstants.Events.RETURN_CANCELLED.eventName),
            Map.entry(ReturnStatus.CUSTOMER_CANCELLED.getStatus(), CosmosConstants.Events.RETURN_CANCELLED.eventName),
            Map.entry(ReturnStatus.RETURN_REJECTED.getStatus(), CosmosConstants.Events.RETURN_REJECTED.eventName),
            Map.entry(ReturnStatus.RETURN_REJECTED_HANDOVER_PENDING.getStatus(), CosmosConstants.Events.RETURN_REJECTED.eventName),
            Map.entry(ReturnStatus.RETURN_REJECTED_HANDOVER_DONE.getStatus(), CosmosConstants.Events.RETURN_REJECTED_HANDOVER_DONE.eventName),
            Map.entry(ReturnStatus.RETURN_ACCEPTED.getStatus(), CosmosConstants.Events.RETURN_APPROVED.eventName),
            Map.entry(ReturnStatus.RETURN_EXCHANGE.getStatus(), CosmosConstants.Events.RETURN_EXCHANGED.eventName),
            Map.entry(ReturnStatus.REFERENCE_ID_ISSUED.getStatus(), CosmosConstants.Events.PICKUP_SCHEDULED.eventName),
            Map.entry(ReturnStatus.RETURN_NEED_APPROVAL.getStatus(), CosmosConstants.Events.RETURN_NEED_APPROVAL.eventName),
            Map.entry(ReturnStatus.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.getStatus(), CosmosConstants.Events.RETURN_NEED_APPROVAL_FROM_WAREHOUSE.eventName),
            Map.entry(ReturnStatus.NEW_REVERSE_PICKUP.getStatus(), CosmosConstants.Events.NEW_RETURN_PICKUP.eventName),
            Map.entry(ReturnStatus.RETURN_EXPECTED_POS.getStatus(), CosmosConstants.Events.RETURN_EXPECTED_POS.eventName),
            Map.entry(ReturnStatus.RETURN_EXPECTED_WH.getStatus(), CosmosConstants.Events.RETURN_EXPECTED_WH.eventName),
            Map.entry(ReturnStatus.AWB_ASSIGNED.getStatus(), CosmosConstants.Events.RETURN_PICKED.eventName),
            Map.entry("pickup_regd_failed", CosmosConstants.Events.PICKUP_FAILED.eventName)
    );


    private String getCosmosEvent(String event, String source, String returnMethod) {
        String returnEvent = "";
        if(event != null){
            String eventName = COSMOS_EVENT_MAPPING.get(event.toLowerCase());
            if (eventName != null) {
                return eventName;
            }
            if ("return_received".equalsIgnoreCase(event)) {
                if (ReturnSources.POS.getSource().equalsIgnoreCase(source) && "StoreReceiving".equalsIgnoreCase(returnMethod)) {
                    returnEvent =  CosmosConstants.Events.RETURN_RECEIVED_AT_STORE.eventName;
                } else if (ReturnSources.WAREHOUSE.getSource().equalsIgnoreCase(source) && "DirectReceiving".equalsIgnoreCase(returnMethod)) {
                    returnEvent =  CosmosConstants.Events.RETURN_RECEIVED_AT_WAREHOUSE.eventName;
                }
            }
        }
        log.info("[getCosmosEvent] event : {}, returnEvent : {}", event, returnEvent);
        return returnEvent;
    }
}
