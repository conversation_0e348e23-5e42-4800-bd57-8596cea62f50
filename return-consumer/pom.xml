<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.lenskart</groupId>
		<artifactId>return</artifactId>
		<version>0.1.86</version>
	</parent>
	<artifactId>return-consumer</artifactId>
	<version>0.0.4-SNAPSHOT</version>
	<name>return-consumer</name>
	<packaging>jar</packaging>
	<description>return api project</description>
	<repositories>
		<repository>
			<id>internal</id>
			<url>http://archiva-new.prod.internal:8080/repository/internal/</url>
		</repository>
	</repositories>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-consul-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-consul-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-vault-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback.contrib</groupId>
			<artifactId>logback-json-classic</artifactId>
			<version>0.1.5</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback.contrib</groupId>
			<artifactId>logback-jackson</artifactId>
			<version>0.1.5</version>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing-bridge-brave</artifactId>
		</dependency>
		<dependency>
			<groupId>com.lenskart</groupId>
			<artifactId>return-service</artifactId>
			<version>${return.service.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.lenskart</groupId>-->
<!--			<artifactId>graceful-terminator</artifactId>-->
<!--			<version>1.0</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.lenskart</groupId>
			<artifactId>ces-graceful-shutdown</artifactId>
			<version>4.0.0</version>
		</dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
        </dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>4.5.14</version>
			</dependency>
			<dependency>
				<groupId>io.zipkin.aws</groupId>
				<artifactId>brave-propagation-aws</artifactId>
				<version>0.23.5</version>
			</dependency>
			<dependency>
				<groupId>org.jsondoc</groupId>
				<artifactId>spring-boot-starter-jsondoc</artifactId>
				<version>1.2.17</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<source>17</source>
					<target>17</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.30</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>3.4.1</version>

				<executions>
					<execution>
						<id>enforce-no-circular-dependencies</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<dependencyConvergence/>
								<bannedDependencies>
									<excludes>
										<exclude>com.lenskart:return-job</exclude>
										<exclude>com.lenskart:return-api</exclude>
									</excludes>
								</bannedDependencies>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
