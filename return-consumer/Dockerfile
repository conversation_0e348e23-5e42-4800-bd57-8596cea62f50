# build
FROM public.ecr.aws/z5y1f1y8/maven:3.8.7-amazoncorretto-17 as builder

ARG TARGETPLATFORM
ARG BUILDPLATFORM
RUN echo "Building on $BUILDPLATFORM, for $TARGETPLATFORM"

COPY ./pom.xml /return-consumer/
WORKDIR /return-consumer/
COPY ./.mvn /return-consumer/.mvn
RUN aws s3 cp s3://deployment.lenskartprod.internal/settings.xml /root/.m2/

RUN aws s3 cp s3://deployment.lenskartprod.internal/newrelic-java-latest.zip .
RUN unzip newrelic-java-latest.zip

COPY . .
RUN --mount=type=cache,target=/root/.m2 mvn clean install -U -DskipTests --settings .mvn/custom-settings.xml

# run
FROM public.ecr.aws/z5y1f1y8/maven:3.8.7-amazoncorretto-17

RUN mkdir /return-consumer

COPY --from=builder /return-consumer/target/*.jar /return-consumer/
COPY --from=builder /return-consumer/newrelic/ /return-consumer/newrelic/
RUN chown -R 1000 /return-consumer && chmod 777 return-consumer

EXPOSE 8080
WORKDIR /return-consumer/
ENTRYPOINT ["sh", "-c", "java -Djava.security.egd=file:/dev/./urandom -javaagent:/return-consumer/newrelic/newrelic.jar -Dserver.port=8080 -Dspring.profiles.active=$PROFILE $JVM_ARGS -Duser.timezone=Asia/Kolkata -jar /return-consumer/*.jar"]
