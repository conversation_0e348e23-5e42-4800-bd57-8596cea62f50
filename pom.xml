<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.1.5</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<packaging>pom</packaging>
	<groupId>com.lenskart</groupId>
	<artifactId>return</artifactId>
	<version>0.1.86</version>
	<name>return</name>
	<description>headless return project</description>
	<properties>
		<java.version>17</java.version>
		<spring-cloud.version>2022.0.4</spring-cloud.version>
		<sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
		<sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
		<sonar.language>java</sonar.language>
		<sonar.qualitygate.wait>true</sonar.qualitygate.wait>
		<return.common.version>0.1.86</return.common.version>
		<return.repository.version>0.1.86</return.repository.version>
		<return.service.version>0.1.86</return.service.version>
	</properties>
	<modules>
		<module>return-common</module>
		<module>return-repository</module>
		<module>return-service</module>
		<module>return-api</module>
		<module>return-consumer</module>
		<module>return-job</module>
	</modules>
	<repositories>
		<repository>
			<id>internal</id>
			<url>http://archiva-new.prod.internal:8080/repository/internal/</url>
		</repository>
	</repositories>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.7</version>
				<executions>
					<execution>
						<id>jacoco-initialize</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>jacoco-site</id>
						<phase>package</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<distributionManagement>
		<repository>
			<id>internal</id>
			<url>http://archiva-new.prod.internal:8080/repository/internal/</url>
		</repository>
	</distributionManagement>

</project>
