CREATE TABLE delight_action
(
    id                 INT AUTO_INCREMENT PRIMARY KEY,
    return_id          INT,
    delight_action     VARCHAR(255),
    delight_method     VARCHAR(255),
    created_at         DATETIME,
    comments           TEXT,
    warehouse_comments TEXT,
    KEY `idx_return_id` (`return_id`)
);

CREATE TABLE refund_rules
(
    id                        INT AUTO_INCREMENT PRIMARY KEY,
    reverse_type              VA<PERSON>HAR(255),
    nav_channel               VARCHAR(255),
    return_initiated_source   VARCHAR(255),
    trigger_point             VARCHAR(255),
    is_pseudo_gatepass        BOOLEAN      DEFAULT false,
    is_branded                BOOLEAN      DEFAULT false,
    is_qc_pass                BOOLEAN,
    category                  VARCHAR(255),
    warranty_from             INT          DEFAULT 0,
    warranty_to               INT          DEFAULT 0,
    is_accessory_missing      BOOLEAN      DEFAULT false,
    is_last_piece             BOOLEAN      DEFAULT false,
    is_lens_only              BOOLEAN      DEFAULT false,
    country_code              VA<PERSON><PERSON><PERSON>(255),
    insurance_policy          BOOLEAN,
    amount_from               BIGINT,
    amount_till               BIGINT,
    blacklisted_phone_numbers BOOLEAN,
    blacklisted_pincodes      B<PERSON><PERSON><PERSON><PERSON>,
    is_returnable             B<PERSON><PERSON>EAN      DEFAULT true,
    action                    VARCHAR(255),
    do_refund                 BOOLEAN      DEFAULT true,
    refund_method             VARCHAR(255),
    draft_return_method       VARCHAR(255),
    exchange_allowed          INT,
    exchange_order_dispatch   VARCHAR(255),
    return_reasons            VARCHAR(255),
    payment_method            VARCHAR(255),
    return_eligibility_period INT          DEFAULT 30,
    refund_dispatch           VARCHAR(255) DEFAULT 'CourierPickup',
    override_warranty_period  BOOLEAN,
    KEY `idx_refund_rule` (`reverse_type`, `return_initiated_source`, `trigger_point`, `is_pseudo_gatepass`,
                           `is_branded`, `is_qc_pass`, `warranty_from`, `warranty_to`, `is_accessory_missing`,
                           `is_last_piece`, `is_lens_only`, `amount_from`, `amount_till`, `blacklisted_phone_numbers`,
                           `exchange_allowed`, `draft_return_method`)
);


CREATE TABLE reship_item_detail
(
    id                    INT AUTO_INCREMENT PRIMARY KEY,
    increment_id          INT,
    uw_item_id            INT,
    reship_type           VARCHAR(15),
    create_at             DATETIME,
    unicom_order_code     VARCHAR(50),
    product_delivery_type VARCHAR(50),
    is_received           INT DEFAULT 0,
    return_id             INT
);

CREATE TABLE return_courier_detail
(
    id                          INT AUTO_INCREMENT PRIMARY KEY,
    return_id                   INT,
    created_at                  DATETIME,
    reverse_courier             VARCHAR(127),
    reverse_awb_number          VARCHAR(40),
    reverse_pickup_reference_id VARCHAR(50),
    pickup_estimate             DATETIME,
    KEY `idx_return_id` (`return_id`),
    KEY `reverse_awb_number` (`reverse_awb_number`)
);

CREATE TABLE return_order
(
    id                     INT AUTO_INCREMENT PRIMARY KEY,
    request_id             INT,
    increment_id           INT,
    group_id               INT,
    return_create_datetime DATETIME,
    return_type            ENUM ('rto', 'reverse', 'awaited_rto') NOT NULL,
    return_method          VARCHAR(45),
    is_insurance           BOOLEAN,
    is_qc_at_doorstep      INT,
    is_auto_cancel_enable  BOOLEAN,
    receiving_flag         VARCHAR(5),
    source                 VARCHAR(50),
    KEY `increment_id` (`increment_id`),
    KEY `rto_dt` (`return_create_datetime`),
    KEY `return_type` (`return_type`),
    KEY `receiving_flag` (`receiving_flag`),
    KEY `idx_group_id` (`group_id`)
);

CREATE TABLE return_event
(
    id                INT AUTO_INCREMENT PRIMARY KEY,
    return_request_id INT,
    return_id         INT,
    event             VARCHAR(50),
    remarks           TEXT,
    created_at        DATETIME,
    KEY `idx_return_id` (`return_id`),
    KEY `idx_return_request_id` (`return_request_id`),
    KEY `idx_event` (`event`)
);


CREATE TABLE return_order_item
(
    id                     INT AUTO_INCREMENT PRIMARY KEY,
    return_id              INT,
    uw_item_id             INT,
    item_id                INT,
    product_id             BIGINT,
    qc_status              VARCHAR(40),
    return_create_datetime DATETIME,
    KEY `item_id` (`item_id`),
    KEY `return_create_datetime` (`return_create_datetime`),
    KEY `return_id` (`return_id`),
    KEY `uw_item_id` (`uw_item_id`)
);

CREATE TABLE return_refund_rule
(
    id                                     INT AUTO_INCREMENT PRIMARY KEY,
    rule_id                                VARCHAR(255),
    uw_item_id                             INT,
    return_id                              INT,
    created_at                             DATETIME,
    request                                TEXT,
    trigger_point                          VARCHAR(20),
    decision_values                        TEXT,
    return_refund_rule_response            TEXT,
    rule_called_from                       VARCHAR(110),
    return_refund_rule_overridden_response TEXT,
    KEY `idx_uw_item_id_trigger_point` (`uw_item_id`, `trigger_point`),
    KEY `idx_return_id` (`return_id`),
    KEY `idx_created_at` (`created_at`)
);

CREATE TABLE return_request
(
    id               INT AUTO_INCREMENT PRIMARY KEY,
    increment_id     INT,
    identifier_type  VARCHAR(50),
    identifier_value VARCHAR(50),
    return_reason    VARCHAR(50),
    source           VARCHAR(50),
    created_at       DATETIME,
    return_intention VARCHAR(50),
    KEY `identifier_type` (`identifier_type`),
    KEY `identifier_value` (`identifier_value`),
    KEY `created_at` (`created_at`),
    KEY `increment_id` (`increment_id`)
);


CREATE TABLE secondry_return_reason
(
    secondary_reason_id INT AUTO_INCREMENT PRIMARY KEY,
    reason              VARCHAR(255),
    created_at          DATETIME,
    updated_at          DATETIME,
    type                VARCHAR(50),
    dispensing_required VARCHAR(10)
);


CREATE TABLE `system_preference`
(
    `id`    bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `key`   varchar(50) NOT NULL,
    `value` text        NOT NULL,
    `group` varchar(50) NOT NULL,
    `type`  varchar(50) NOT NULL,
    KEY `key` (`key`, `group`)
);

