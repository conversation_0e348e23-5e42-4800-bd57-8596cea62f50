INSERT INTO `communication_template` (`id`, `conditions`, `event_type`, `payload_name`, `template_name`)
VALUES
    ('2', '{\n    \"courier\": \"Self courier\"\n}', 'RETURN_INITIATED', 'master', 'self_courier_warehouse_exchange_master'),
    ('4', '{\n    \"courier\": \"LKart\",\n    \"status\": \"cancelled\"\n}', 'RETURN_INITIATED', 'master', 'dispensing_issue_resolved_master'),
    ('5', '{\n    \"courier\": \"LKart\",\n    \"returnMethod\": \"StoreReceiving\"\n}', 'RETURN_INITIATED', 'master', 'dispensing_picked_up_master'),
    ('6', '{\n    \"courier\": \"LKart\",\n    \"flow\": \"dispensing_assigned_master\"\n}', 'RETURN_INITIATED', 'master', 'dispensing_assigned_master'),
    ('7', '{\n    \"isExchangeCreated\": \"true\"\n}', 'EXCHANGE_CREATION', 'master', 'return_intitiate_vsm_exchange_SAMEPRODUCT'),
    ('8', '{\n    \"isExchangeCreated\": \"false\"\n}', 'EXCHANGE_CREATION', 'master', 'return_initiation_rpu_no_method_master'),
    ('10', '{\n    \"isExchangeCreated\": \"false\"\n}', 'RETURN_INITIATED', 'master', 'return_initiation_rpu_no_method_master'),
    ('12', '{\n    \"returnMethod\": \"RPU\"\n}', 'RETURN_INITIATED', 'master', 'return_initiation_pickup_from_home emailonly'),
    ('14', '{\n    \"refundMethod\": \"EXG_WLT_STR_NFT_SRC\"\n}', 'RETURN_INITIATED', 'master', 'action_required_on_return_refund_exchange_master email'),
    ('15', '{\n    \"refundMethod\": \"exchange\"\n}', 'RETURN_INITIATED', 'master', 'action_required_on_return_exchange_master email'),
    ('16', '{\n    \"refundMethod\": \"WLT_STR_NFT_SRC\"\n}', 'RETURN_INITIATED', 'master', 'action_required_on_return_refund_master email'),
    ('17', '{\n    \"courier\": \"!LKart\"\n}', 'RETURN_UPDATE', 'master', 'return_cancelled_master'),
    ('18', '{\n    \"courier\": \"LKart\",\n    \"status\": \"cancelled\"\n}', 'RETURN_UPDATE', 'master', 'dispensing_issue_resolved_master'),
    ('19', '{\n    \"courier\": \"LKart\",\n    \"returnMethod\": \"StoreReceiving\"\n}', 'RETURN_UPDATE', 'master', 'dispensing_picked_up_master'),
    ('20', '{\n    \"courier\": \"LKart\",\n    \"flow\": \"dispensing_assigned_master\"\n}', 'RETURN_UPDATE', 'master', 'dispensing_assigned_master'),
    ('21', '{\n    \"status\": \"return_rejected_handover_done\"\n}', 'RETURN_UPDATE', 'master', 'return_rejected_handover_done_master'),
    ('22', '{\n    \"status\": \"return_rejected_handover_done\",\n    \"country\": \"SG\"\n}', 'RETURN_UPDATE', 'master', 'product_handover_back_to_user_sg_master'),
    ('23', '', 'REFERENCE_ID_ISSUED', 'master', 'return_courier_assigned_master email'),
    ('24', '{\n    \"country\": \"SG\"\n}', 'RETURN_NEED_APPROVAL', 'master', 'delight_need_approval_sg_master'),
    ('25', '', 'RETURN_NEED_APPROVAL', 'master', 'return_need_approval_delight_master'),
    ('26', '{\n    \"status\": \"cancelled\"\n}', 'RETURN_STORE_RECEIVING', 'master', 'dispensing_issue_resolved_master'),
    ('27', '{\n    \"returnMethod\": \"StoreReceiving\"\n}', 'RETURN_STORE_RECEIVING', 'master', 'dispensing_picked_up_master'),
    ('28', '{\n    \"flow\": \"dispensing_assigned_master\"\n}', 'RETURN_STORE_RECEIVING', 'master', 'dispensing_assigned_master'),
    ('30', '{\n    \"courier\": \"Self courier\"\n}', 'awb_assigned', 'master', 'return_pickup_done_awb_assigned_master'),
    ('38', '{\n    \"returnMethod\": \"returntoNearbyStore\"\n}', 'RETURN_INITIATED', 'master', 'return_initiation_return_to_store_master'),
    ('54', '', 'DELIGHT_NEED_APPROVAL', 'master', 'delight_need_approval emailonly'),
    ('56', '{\n    \"refundMethod\": \"storecredit\"\n}', 'RETURN_SUCCESS_MESSAGE_POS', 'master', 'pos_return_succ_wh_recvng_storecredit_master'),
    ('57', '{\n    \"refundMethod\": \"cashfree\"\n}', 'RETURN_SUCCESS_MESSAGE_POS', 'master', 'pos_return_succ_wh_recvng_cashfree_master'),
    ('58', '{\n    \"refundMethod\": \"source\"\n}', 'RETURN_SUCCESS_MESSAGE_POS', 'master', 'pos_return_succ_wh_recvng_source_master'),
    ('60', '{\n    \"refundMethod\": \"EXG_WLT_STR_NFT_SRC\"\n}', 'awb_assigned', 'master', 'action_required_on_return_refund_exchange_master email'),
    ('62', '{\n    \"refundMethod\": \"exchange\"\n}', 'awb_assigned', 'master', 'action_required_on_return_exchange_master email'),
    ('64', '{\n    \"refundMethod\": \"WLT_STR_NFT_SRC\"\n}', 'awb_assigned', 'master', 'action_required_on_return_refund_master email');
