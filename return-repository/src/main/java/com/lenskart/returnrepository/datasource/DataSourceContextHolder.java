package com.lenskart.returnrepository.datasource;

import org.springframework.util.Assert;

public class DataSourceContextHolder {
    private static final ThreadLocal<String> CONTEXT = new ThreadLocal<>();

    private DataSourceContextHolder() {
    }

    public static void setDatabaseName(String databaseName) {
        Assert.notNull(databaseName, "clientDatabase cannot be null");
        CONTEXT.set(databaseName);
    }

    public static String getDatabaseName() {
        return CONTEXT.get();
    }

    public static void removeDatabaseName() {
        CONTEXT.remove();
    }
}
