package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnDetailReason;
import com.lenskart.returnrepository.entity.ReturnReason;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface ReturnReasonOldRepository extends CrudRepository<ReturnReason, Integer> {
    List<ReturnReason> findByReturnId(Integer returnId);
    ReturnReason findTop1ByUwItemIdAndReturnIdOrderByCreatedAtDesc(Integer uwItemId, Integer returnId);

    List<ReturnReason> findByReturnIdIn(List<Integer> returnIds);
    List<ReturnReason> findByOrderId(Integer orderId);
}
