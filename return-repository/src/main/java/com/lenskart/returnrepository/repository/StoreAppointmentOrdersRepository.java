package com.lenskart.returnrepository.repository;


import com.lenskart.returnrepository.entity.StoreAppointmentOrders;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.Date;

public interface StoreAppointmentOrdersRepository extends CrudRepository<StoreAppointmentOrders, Integer> {

    @Query(value = "select * from store_appointment_orders s where  s.identifier_value = ?1 and s.source=?2 and s.created_at between ?3 and ?4 order by s.created_at desc limit 1", nativeQuery = true)
    StoreAppointmentOrders findAppointmentByUwItemIdAndSource(Integer identifierValue, String source, Date fromDate, Date toDate);

    @Query(value = "select * from store_appointment_orders s where s.identifier_value = ?1 limit 1", nativeQuery = true)
    StoreAppointmentOrders findAppointmentByIdentifierValue(Integer identifierValue);
}
