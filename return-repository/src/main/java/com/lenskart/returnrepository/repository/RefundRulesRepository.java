package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.RefundRules;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface RefundRulesRepository extends CrudRepository<RefundRules, Integer> {

    List<RefundRules> findAll();

    @Query(value = "SELECT * FROM refund_rules WHERE reverse_type=?1 AND return_initiated_source=?2 AND trigger_point=?3 AND is_psuedo_gatepass=?4 " +
            "AND is_branded = ?5 AND is_qc_pass = ?6 AND ?7 >= waranty_from AND ?7 <= waranty_to AND  is_accessory_missing = ?8 AND  is_last_piece = ?9 " +
            "AND  is_lens_only = ?10 AND ?11 >= amount_from AND ?11 <= amount_till AND blacklisted_phone_numbers = ?12 AND ?13 <= exchange_allowed " +
            "AND blacklisted_pincodes = ?18 AND country_code = ?19 AND insurance_policy = ?20 AND draft_return_method = ?14 AND FIND_IN_SET(?15, nav_channel) > 0 " +
            "AND FIND_IN_SET(?16, return_reasons) > 0 AND FIND_IN_SET(?17, category) > 0 AND FIND_IN_SET(?21, payment_method) > 0 AND ?22 >= customer_score_from AND ?22 <= customer_score_to AND ?23 >= store_score_from AND ?23 <= store_score_to ORDER BY id " +
            "ASC LIMIT 1", nativeQuery = true)
    RefundRules findRuleTriggered(String reverse_type, String return_initiated_source, String trigger_point,
                                  boolean is_pseudo_gatepass, boolean is_branded, boolean is_qc_pass,
                                  int warranty, boolean is_accessory_missing, boolean is_last_piece, boolean is_lens_only,
                                  int amount, boolean blacklisted_phone_numbers, int exchange_allowed,
                                  String draft_return_method, String nav_channel, String returnReasons, String category, boolean isBlacklistedPincode,
                                  String countryCode, boolean insurancePolicy, String paymentMethod, double customerScore, double storeScore);

    @Query(value = "SELECT * FROM refund_rules WHERE reverse_type=?1 AND return_initiated_source=?2 AND trigger_point=?3 AND is_psuedo_gatepass=?4 " +
            "AND is_branded = ?5 AND is_qc_pass = ?6 AND ?7 > waranty_from AND ?7 < waranty_to AND  is_accessory_missing = ?8 AND  is_last_piece = ?9 " +
            "AND  is_lens_only = ?10 AND ?11 >= amount_from AND ?11 <= amount_till AND blacklisted_phone_numbers = ?12 AND ?13 < exchange_allowed " +
            "AND blacklisted_pincodes = ?18 AND country_code = ?19 AND insurance_policy = ?20 AND draft_return_method = ?14 AND FIND_IN_SET(?15, nav_channel) > 0 " +
            "AND FIND_IN_SET(?16, return_reasons) > 0 AND FIND_IN_SET(?17, category) > 0 AND FIND_IN_SET(?21, payment_method) > 0 AND override_warranty_period=0 AND ?22 >= customer_score_from AND ?22 <= customer_score_to AND ?23 >= store_score_from AND ?23 <= store_score_to ORDER BY id " +
            "ASC LIMIT 1", nativeQuery = true)
    RefundRules findRuleTriggeredNotOverrideWarranty(String reverse_type, String return_initiated_source, String trigger_point,
                                                     boolean is_pseudo_gatepass, boolean is_branded, boolean is_qc_pass,
                                                     int warranty, boolean is_accessory_missing, boolean is_last_piece, boolean is_lens_only,
                                                     int amount, boolean blacklisted_phone_numbers, int exchange_allowed,
                                                     String draft_return_method, String nav_channel, String returnReasons, String category, boolean isBlacklistedPincode,
                                                     String countryCode, boolean insurancePolicy, String paymentMethod,  double customerScore, double storeScore);
}
