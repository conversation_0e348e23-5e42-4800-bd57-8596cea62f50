package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnDetailAddressUpdate;
import com.lenskart.returnrepository.entity.ReturnOrderAddressUpdate;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface ReturnOrderAddressUpdateOldRepository extends CrudRepository<ReturnOrderAddressUpdate, Integer> {

    @Query(value = "SELECT postcode FROM return_order_address_update where increment_id =?1 ORDER BY created_at DESC LIMIT 1", nativeQuery = true)
    Integer findTopPostcodeByOrderIdAndByCreatedAtDesc(Integer orderId);

    @Query(value = "SELECT * FROM return_order_address_update where group_id=?1 order by created_at desc limit 1", nativeQuery = true)
    ReturnOrderAddressUpdate findByGroupId(Long groupId);

    @Query(value = "SELECT * FROM return_order_address_update where increment_id =?1 ORDER BY created_at DESC LIMIT 1", nativeQuery = true)
    ReturnOrderAddressUpdate findByIncrement_id(Integer incrementId);

    @Query(value = "SELECT * FROM return_order_address_update where group_id=?1 order by id desc limit 1", nativeQuery = true)
    ReturnOrderAddressUpdate findTop1ByGroupIdOrderByIdDesc(Long groupId);

    @Query(value = "SELECT * FROM return_order_address_update WHERE address_type=?2 and group_id=?1 ORDER BY created_at DESC LIMIT 1", nativeQuery = true)
    ReturnOrderAddressUpdate getReturnOrderAddressUpdate(Long groupId, String addressType);
}
