package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.PrimaryReturnReasons;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface PrimaryReturnReasonsRepository extends CrudRepository<PrimaryReturnReasons,Integer> {

    public PrimaryReturnReasons findByPrimaryReasonId(Integer reasonId);
    PrimaryReturnReasons findByReason(String primaryReason);

    @Query(value = "select distinct(reason) from primary_return_reason", nativeQuery = true)
    List<String> findAllDistinct();
    PrimaryReturnReasons findTopByReason(String primaryReason);
}
