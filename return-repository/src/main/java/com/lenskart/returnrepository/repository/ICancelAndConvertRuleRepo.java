package com.lenskart.returnrepository.repository;

import com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto;
import com.lenskart.returnrepository.entity.CancelAndConvertRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ICancelAndConvertRuleRepo extends JpaRepository<CancelAndConvertRule, Integer> {

    @Query("select new com.lenskart.returncommon.model.dto.CancelAndConvertRuleDto(e.id, e.paymentMode, e.conditions, e.action) from CancelAndConvertRule e where e.active = true ")
    List<CancelAndConvertRuleDto> getAllRules();
}
