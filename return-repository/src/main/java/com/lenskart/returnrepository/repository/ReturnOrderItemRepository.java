package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnDetailItem;
import com.lenskart.returnrepository.entity.ReturnOrderItem;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface ReturnOrderItemRepository extends CrudRepository<ReturnOrderItem, Integer> {

    @Query(value = "select uw_item_id from return_order_item where return_id=?1 limit 1", nativeQuery = true)
    Integer findUwItemIdByReturnId(Integer returnId);

    @Query(value = "SELECT uw_item_id FROM return_order_item a JOIN return_order b ON a.return_id = b.return_id WHERE b.order_no =?1", nativeQuery = true)
    List<String> findUwItemIdByIncrementId(Integer incrementId);

    @Query(value = "SELECT b.return_id FROM return_order_item a JOIN return_order b ON a.return_id = b.return_id WHERE b.`return_type` =?1 and a.uw_item_id=?2 order by a.return_create_datetime desc limit 1", nativeQuery = true)
    Integer findReturnIdByReturnTypeUwItemID(String returnType,Integer uwItemId);

    List<ReturnOrderItem> findByUwItemId(Integer uwItemId);

    List<ReturnOrderItem> findByUwItemIdIn(List<Integer> uwItemIds);

    ReturnOrderItem findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId);

    @Query(value = "SELECT uw_item_id FROM return_order_item WHERE return_id = ?1 ORDER BY return_create_datetime DESC LIMIT 1 ", nativeQuery = true)
    Integer findTop1UwItemIdByReturnIdOrderByReturnCreateDatetimeDesc(Integer returnId);

    @Query(value = "SELECT return_id FROM return_order_item WHERE uw_item_id = ?1 AND return_id IS NOT NULL ORDER BY return_create_datetime DESC LIMIT 1 ", nativeQuery = true)
    Integer findTop1ReturnIdByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId);

    List<ReturnOrderItem> findByReturnId(Integer returnId);

    List<ReturnOrderItem> findByUwItemIdInAndStatusInAndReturnType(List<Integer> uwItems, List<String> statuses, String returnType);

    List<ReturnOrderItem> findByUwItemIdInAndStatusIn(List<Integer> uwItems, List<String> statuses);

    ReturnOrderItem findTop1ByUwItemIdOrderByIdDesc(Integer uwItemId);

    @Query(value = "SELECT return_id FROM return_order_item WHERE uw_item_id in (?1) and status in (?2)", nativeQuery = true)
    List<Integer> findReturnIdByUwItemIdInAndStatusIn(List<Integer> uwItems, List<String> statuses);

    @Modifying
    @Query(value = "update return_order_item set status = ?1,action_datetime = now() WHERE return_id= ?2 and uw_item_id=?3 ", nativeQuery = true)
    void updateItemReturnStatusAndActionDatetime(String status, Integer returnId, Integer uwItemId);

    @Query(value="SELECT roi.product_id FROM return_order_item roi JOIN return_order ro ON ro.return_id =roi.return_id WHERE ro.group_id=?1", nativeQuery = true)
    List<Integer> findProductIdByGroupId(Integer groupId);

    @Query(value="SELECT ro.order_no as orderNo,roi.id as returnItemId,roi.uw_item_id as uwItemId,roi.product_id as productId FROM return_order_item roi JOIN return_order ro ON ro.return_id=roi.return_id WHERE ro.group_id=?1",nativeQuery = true)
    List<Object[]> findReturnItemDetailByGroupId(Integer groupId);

    ReturnOrderItem findTopByReturnId(Integer returnId);

    @Query(value ="SELECT count(roi.return_id) as returnCount FROM return_order_item roi JOIN return_order ro ON ro.return_id = roi.return_id join orders o on o.item_id=roi.item_id join products p on o.product_id=p.product_id where o.customer_id= ?1 and p.classification!=11356 and ro.status in ('return_refunded','return_received','return_received_action_pending') and ro.return_create_datetime>DATE_SUB(NOW(), INTERVAL ?2 DAY)", nativeQuery = true)
    int findAllIdByCustomerIdAndDayDIff(Long customerId,int numberOfDays);

    ReturnOrderItem findTop1ByItemIdOrderByIdDesc(Integer itemId);
    @Query(value = "SELECT return_id FROM return_order_item WHERE uw_item_id in (?1)", nativeQuery = true)
    List<Integer> findReturnIdByUwItemId(List<Integer> uwItems);

    @Query(value = "SELECT * FROM return_order_item WHERE uw_item_id in (?1)", nativeQuery = true)
    List<ReturnOrderItem> findReturnIdByUwItemIdV2(List<Integer> uwItems);
    @Query(value = "SELECT uw_item_id FROM return_order_item WHERE return_id = (?1) order by id desc", nativeQuery = true)
    List<Integer> findUWItemIdByReturnId(Integer returnId);
    @Query(value = "SELECT count(return_id) from return_order_item where uw_item_id =?1",nativeQuery = true)
    Integer getReturnOrderItemCountByUwItemId(Integer uwItemId);
    @Query(value = "select count(ro.return_id) from return_order ro join orders o on o.increment_id = ro.order_no where o.magento_item_id = ?1",nativeQuery = true)
    Integer getReturnIdCountByMagentoItemId(Integer magentoItemId);
    @Query(value = "SELECT return_id FROM return_order_item WHERE uw_item_id = ?1 order by return_id desc limit 1", nativeQuery = true)
    Integer findTop1ReturnIdByUwItemId(Integer uwItemId);

    @Query(value = "select ro.receiving_flag from return_order_item roi join return_order ro on ro.return_id = roi.return_id where roi.uw_item_id in (?1) and ro.return_type =?3 and roi.status in (?2)",nativeQuery = true)
    List<String> findReturnOrderReceivingFlagByByUwItemId(List<Integer> itemIds, List<String> returnItemStatus, String reverse);

    List<ReturnOrderItem> findByReturnIdIn(List<Integer> returnId);

    @Query(value = "SELECT roi.* FROM return_order_item roi \n" +
            "JOIN return_order ro ON roi.return_id = ro.return_id \n" +
            "WHERE ro.group_id IN (?1) ", nativeQuery = true)
    List<ReturnOrderItem> findByGroupIdIn(List<Long> groupId);

    @Query(value = "select rdi.reason_for_return, rdi.qc_status, rdi.qc_fail_reason, rdi.qc_comment, rdi.product_id, (?7) as classification, (?6) as sku, (?5) as product_url, \n" +
            "(?4) as product_image, (?3) as `value`, (?2) as website\n" +
            "from return_order_item rdi where rdi.uw_item_id=?1 \n" +
            "order by rdi.return_create_datetime desc limit 1", nativeQuery = true)
    Map<String, Object> getReturnItemProductInfo(Integer uwItemId, String website, String value, String productImage, String productUrl, String sku, Integer classification);

    @Query(value = "SELECT * FROM return_order_item WHERE id > :lastId ORDER BY id ASC LIMIT :batchSize", nativeQuery = true)
    List<ReturnOrderItem> findReturnOrderItemBatch(@Param("lastId") int lastId, @Param("batchSize") int batchSize);
}