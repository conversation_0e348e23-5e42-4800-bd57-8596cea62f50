package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.NprCommunicationHistory;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface INprCommunicationHistoryRepository extends CrudRepository<NprCommunicationHistory, Integer> {
    List<NprCommunicationHistory> findByReturnId(Integer returnId);

    @Query(value = "SELECT * FROM npr_communication_history WHERE return_id = ?1 ORDER BY created_at DESC", nativeQuery = true)
    List<NprCommunicationHistory> findByReturnIdOrderByCreatedAtDesc(Integer returnId);

    @Query(value = "SELECT * FROM npr_communication_history WHERE unique_id = ?1", nativeQuery = true)
    NprCommunicationHistory findByUniqueId(String uniqueId);


    @Transactional
    @Modifying
    @Query(value = "update npr_communication_history SET  user_action = ?1, source = ?2, updated_at = current_date() where unique_id = ?3", nativeQuery = true)
    void updateResponseForUniqueId( String userAction,String source, String uniqueId);
}
