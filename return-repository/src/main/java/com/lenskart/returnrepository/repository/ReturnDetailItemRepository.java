package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnDetailItem;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface ReturnDetailItemRepository extends JpaRepository<ReturnDetailItem, Integer> {
    List<ReturnDetailItem> findByUwItemId(Integer uwItemId);

    List<ReturnDetailItem> findByUwItemIdInOrderByIdDesc(List<Integer> uwItemIds);
    ReturnDetailItem findTop1ByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId);
    ReturnDetailItem findTopByReturnId(Integer returnId);
    List<ReturnDetailItem> findByReturnId(Integer returnId);

    List<ReturnDetailItem> findByReturnIdIn(List<Integer> returnId);
    ReturnDetailItem findTop1ByUwItemIdOrderByIdDesc(Integer uwItemId);

    @Query(value = "SELECT a.return_id FROM return_detail_item a JOIN return_detail b ON a.return_id = b.id WHERE a.uw_item_id=?2 and b.return_type =?1 order by a.return_create_datetime desc limit 1", nativeQuery = true)
    Integer findReturnIdByReturnTypeUwItemID(String returnType,Integer uwItemId);

    @Query(value = "SELECT roi.* FROM return_detail_item roi \n" +
            "JOIN return_detail ro ON roi.return_id = ro.id \n" +
            "WHERE roi.uw_item_id IN (?1) " +
            "  AND ro.return_type IN (?2) " +
            "  AND  roi.status = (?3);", nativeQuery = true)
    List<ReturnDetailItem> findByUwItemIdInAndStatusInAndReturnType(List<Integer> itemIds, List<String> returnType, String event);

    @Query(value = "SELECT return_id FROM return_detail_item WHERE uw_item_id = ?1 AND return_id IS NOT NULL ORDER BY return_create_datetime DESC LIMIT 1 ", nativeQuery = true)
    Integer findTop1ReturnIdByUwItemIdOrderByReturnCreateDatetimeDesc(Integer uwItemId);
    @Query(value = "select ro.receiving_flag from return_detail_item roi join return_detail ro on ro.id = roi.return_id where roi.uw_item_id in (?1) and ro.return_type =?3 and roi.status in (?2)",nativeQuery = true)
    List<String> findReturnOrderReceivingFlagByByUwItemId(List<Integer> uwItems,List<String> status , String returnType);
    @Query(value = "SELECT uw_item_id FROM return_detail_item WHERE return_id =?1 limit 1",nativeQuery = true)
    Integer findUwItemIdByReturnId(Integer returnId);
    @Query(value = "select count(return_id) from return_detail_item rdi\n" +
            "where uw_item_id = ?1", nativeQuery = true)
    Integer getReturnOrderItemCountByUwItemId(Integer uwItemId);

    @Query(value = "SELECT rr.source from return_detail_item rdi\n" +
            "inner join return_detail rd ON rdi.return_id = rd.id\n" +
            "inner join return_request rr ON rd.request_id = rr.id\n" +
            "WHERE rdi.uw_item_id = ?1 order by rdi.return_create_datetime desc limit 1", nativeQuery = true)
    String getReturnSourceFromUwItemId(Integer uwItemId);


    @Query(value = "SELECT uwItemId FROM ReturnDetailItem WHERE returnId = (?1) order by id desc")
    List<Integer> findUWItemIdByReturnId(Integer returnId);

    @Query(value = "SELECT roi.* FROM return_detail_item roi \n" +
            "JOIN return_detail ro ON roi.return_id = ro.id \n" +
            "WHERE ro.group_id IN (?1) ", nativeQuery = true)
    List<ReturnDetailItem> findByGroupIdIn(List<Long> groupId);

    @Query(value = "select (?8) as reason_for_return, rdi.qc_status, rdi.qc_status as qc_fail_reason, rdi.qc_comment, rdi.product_id, " +
            "(?7) as classification, (?6) as sku, (?5) as product_url, (?4) as product_image, (?3) as `value`, (?2) as website " +
            "from return_detail_item rdi where rdi.uw_item_id=?1 order by rdi.return_create_datetime desc limit 1", nativeQuery = true)
    Map<String, Object> getReturnItemProductInfo(Integer uwItemId, String website, String value, String productImage, String productUrl, String sku, Integer classification, String returnReason);

    @Modifying
    @Transactional
    @Query("UPDATE ReturnDetailItem roi SET roi.status = :newStatus, roi.csohUpdatedFlag = :csohUpdatedFlag " +
            "WHERE roi.id = :id AND roi.status = :status")
    int updateStatusAndCsohUpdatedFlagWhereIdAndStatus(
            @Param("newStatus") String newStatus,
            @Param("csohUpdatedFlag") Integer csohUpdatedFlag,
            @Param("id") Integer id,
            @Param("status") String status);

    @Modifying
    @Transactional
    @Query("UPDATE ReturnDetailItem roi SET roi.status = :newStatus, roi.csohUpdatedFlag = :csohUpdatedFlag " +
            "WHERE roi.id = :id AND roi.status NOT IN (:statusNotIn)")
    int updateStatusAndCsohUpdatedWhereIdAndStatusNotIn(
            @Param("newStatus") String newStatus,
            @Param("csohUpdatedFlag") Integer csohUpdatedFlag,
            @Param("id") Integer id,
            @Param("statusNotIn") List<String> statusNotIn);
}
