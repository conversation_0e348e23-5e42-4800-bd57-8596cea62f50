package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.SecondaryReturnReason;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Set;

public interface SecondaryReturnReasonRepository extends CrudRepository<SecondaryReturnReason,Integer> {

    SecondaryReturnReason findBySecondaryReasonId(Integer reasonId);

    @Query(value = "select * from secondry_return_reason where srid in (?1) and dispensing_required='Y'", nativeQuery = true)
    List<SecondaryReturnReason> getDispensingRequiredReasonIdsAmong(Set<Integer> reasonIdSet);

    SecondaryReturnReason findByReason(String reason);

    @Query(value = "select distinct(reason) from secondry_return_reason", nativeQuery = true)
    List<String> findAllDistinct();
    SecondaryReturnReason findTopByReason(String reason);
}
