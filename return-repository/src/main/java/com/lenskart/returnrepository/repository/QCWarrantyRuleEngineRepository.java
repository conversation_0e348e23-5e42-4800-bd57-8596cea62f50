package com.lenskart.returnrepository.repository;


import com.lenskart.returnrepository.entity.QCWarrantyRuleEngine;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface QCWarrantyRuleEngineRepository extends CrudRepository<QCWarrantyRuleEngine,Integer> {
    @Query(value = "SELECT count(*) FROM qc_warranty_rule_engine",nativeQuery = true)
    public Integer getRuleCount();
}
