package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnRequest;
import org.springframework.data.repository.CrudRepository;

public interface ReturnRequestRepository extends CrudRepository<ReturnRequest,Integer> {
    ReturnRequest findTop1ByIdentifierValueOrderByCreatedAtDesc(String identifer);
    ReturnRequest findTop1ByIncrementIdOrderByCreatedAtDesc(Integer incrementId);
}
