package com.lenskart.returnrepository.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lenskart.returnrepository.entity.FinanceSyncReturnTrackingEvent;


@Repository
public interface FinanceSyncReturnTrackingRepository extends JpaRepository<FinanceSyncReturnTrackingEvent, Integer> {

    @Query(value = "SELECT * FROM `fl_return_tracking` WHERE status = 'FAILED' AND retry_count < ?1 and created_at <= DATE_SUB(NOW(), INTERVAL ?2 MINUTE) limit ?3", nativeQuery = true)
    List<FinanceSyncReturnTrackingEvent> findFinanceSyncReturnTrackingEventsByStatusFailedAndRetryCountLessThan(int maxRetryCount, int timeInterval, int limit);

    List<FinanceSyncReturnTrackingEvent> findByReturnId(Integer orderId);

    List<FinanceSyncReturnTrackingEvent> findByUwItemId(Integer orderId);

}