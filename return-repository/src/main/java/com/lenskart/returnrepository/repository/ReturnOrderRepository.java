package com.lenskart.returnrepository.repository;

import com.lenskart.returncommon.model.dto.ReturnDetailsVSM;
import com.lenskart.returnrepository.entity.ReturnDetail;
import com.lenskart.returnrepository.entity.ReturnOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

public interface ReturnOrderRepository extends CrudRepository<ReturnOrder, Integer>, JpaSpecificationExecutor<ReturnOrder> {

    @Query(value = "SELECT * from return_order  where order_no=?1 AND ( status='new_reverse_pickup' OR status='reference_id_issued' OR status='awb_assigned')", nativeQuery = true)
    List<ReturnOrder> getReversePickData(int orderId);

    @Query(value = "SELECT * from return_order WHERE group_id=?1 AND return_create_datetime>?2 ", nativeQuery = true)
    List<ReturnOrder> findBygroupIdAndDate(long groupId, Date twoMonthsBackFromCurrDate);

    ReturnOrder findByReturnId(Integer returnId);
    ReturnOrder findByReturnIdAndOrderNo(Integer returnId, Integer orderNo);

    @Query(value="select * from return_order where return_id=?1", nativeQuery = true)
    ReturnOrder findReturnDetailByReturnId(Integer returnId);

    List<ReturnOrder> findByReturnIdIn(List<Integer> returnIds);

    List<ReturnOrder> findByOrderNo(Integer orderNo);

    List<ReturnOrder> findByOrderNoAndUnicomOrderCodeOrderByReturnCreateDatetimeDesc(Integer incrementId, String unicomOrderCode);

    ReturnOrder findTop1ByOrderNoAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(Integer incrementId, String unicomOrderCode, String returnType);

    List<ReturnOrder> findByOrderNoOrderByReturnCreateDatetimeDesc(Integer incrementId);


    @Query(value = "SELECT * from return_order status='return_pending_approval' and DATEDIFF(ro.return_create_datetime,ss.Delivered_date)>?1 ", nativeQuery = true)
    List<ReturnOrder> getOrdersNotReturnedFor3Months(int days);

    @Modifying
    @Transactional
    @Query(value="update return_order o SET o.status = ?3 where o.order_no=?1 and o.return_id=?2", nativeQuery = true)
    void updateStatus(Integer incrementId, Integer returnId, String status);

    ReturnOrder findTop1ByUnicomOrderCodeAndStatus(String unicomOrderCOde,String status);

    List<ReturnOrder> findAllByGroupIdOrderByReturnCreateDatetimeDesc(long groupId);

    List<ReturnOrder> findByGroupIdAndReversePickupFlag(long groupId, int reversePickupFlag);

    @Modifying
    @Transactional
    @Query(value="update return_order o SET o.return_label_generated=?2 where o.group_id=?1", nativeQuery = true)
    void updateReturnLabelFlag(Long groupId, boolean flag);

    ReturnOrder findByOrderNoAndReturnIdAndStatus(Integer orderNo, Integer returnId, String status);

    ReturnOrder findByReturnIdAndStatus(Integer returnId, String status);

    @Modifying
    @Transactional
    @Query(value="update return_order o SET o.status=?1, reverse_pickup_reference_id = ?2, is_qc_at_doorstep = ?5 where o.group_id=?3 and order_no=?4", nativeQuery = true)
    void updateStatusAndReversePickupReferenceByGroupId(String status, String referenceNumber, Long groupId, Integer orderNo, int isQcAtDoorstep);

    List<ReturnOrder> findAllByGroupIdAndOrderNo(long groupId, Integer orderId);

    @Query(value="select * from return_order where order_no in (?1) and status=?2 order by return_create_datetime desc", nativeQuery = true)
    List<ReturnOrder> getReturnOrdersByStatus(List<Integer> orderIds, String status);

    List<ReturnOrder> findAllByGroupIdAndOrderNoAndStatusIn(long groupId, Integer orderId, List<String> statuses);

    @Query(value = "select return_id from return_order where order_no=?1",nativeQuery = true)
    List<Integer> findReturnIdsByOrderNo(Integer orderNo);
    @Query(value = "select count(return_id) from return_order where increment_id =?1",nativeQuery = true)
    Integer getReturnCountByIncrementId(Integer incrementId);

    @Query(value = "select count(return_id) from return_order where return_id =?1",nativeQuery = true)
    Integer getReturnCountByReturnId(Integer returnId);

    @Query(value = "select group_id from return_order where return_id =?1",nativeQuery = true)
    Long findGroupIdByReturnId(Integer returnId);

    List<ReturnOrder> findByOrderNoIn(List<Integer> orderIds);

    @Query(value = "select ro.* from return_order ro join return_order_item roi on ro.id = roi.return_id where ro.return_create_datetime between ?1 and ?2 AND ro.auto_cancel_flag = 1 AND roi.status IN ('new_reverse_pickup','reference_id_issued') limit ?3",nativeQuery = true)
    List<ReturnOrder> getReturnsForAutoCancellation(Date startDate, Date endDate, Integer batchSizeForReturnAutoCancelJob);

    List<ReturnOrder> findByGroupId(long groupId);

    List<ReturnOrder> findByGroupIdIn(List<Long> groupIds);

    @Query(value = "select * from return_order where order_no = ?1 and reverse_courier = ?2 and return_create_datetime between ?3 and ?4 order by return_create_datetime desc limit ?5 offset ?6 ",nativeQuery = true)
    List<ReturnOrder> getReturnsByOrderAndCourier(Integer orderNo, String reverseCourier, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    @Query(value = "select * from return_order where order_no = ?1 and status = ?2 and return_create_datetime between ?3 and ?4 order by return_create_datetime desc limit ?5 offset ?6 ",nativeQuery = true)
    List<ReturnOrder> getReturnOrdersByStatusAndOrder(Integer orderNo, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    @Query(value = "select * from return_order where order_no = ?1 and return_create_datetime between ?2 and ?3 order by return_create_datetime desc limit ?4 offset ?5 ",nativeQuery = true)
    List<ReturnOrder> getReturnsByOrder(Integer orderNo, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    @Query(value = "select * from return_order where order_no = ?1 and reverse_courier = ?2 and status = ?3 and return_create_datetime between ?4 AND ?5 order by return_create_datetime desc limit ?6 offset ?7 ",nativeQuery = true)
    List<ReturnOrder> getReturnOrdersByStatusOrderAndCourier(Integer orderNo, String reverseCourier, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    @Query(value = "select * from return_order where reverse_courier = ?1 and status = ?2 and return_create_datetime between ?3 and ?4 order by return_create_datetime desc limit ?5 offset ?6 ",nativeQuery = true)
    List<ReturnOrder> getReturnsByCourierAndStatus(String reverseCourier, String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    @Query(value = "select * from return_order where reverse_courier = ?1 and return_create_datetime between ?2 and ?3 order by return_create_datetime desc limit ?4 offset ?5 ",nativeQuery = true)
    List<ReturnOrder> getReturnsByCourier(String reverseCourier, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    @Query(value = "select * from return_order where status = ?1 and return_create_datetime between ?2 and ?3 order by return_create_datetime desc limit ?4 offset ?5 ",nativeQuery = true)
    List<ReturnOrder> getReturnsByStatus(String status, Date fromTime, Date toTime, Integer pageSize, Integer pageNo);

    @Query(value = "select ro.return_id, ro.order_no as order_no,ro.source,ro.override_comment as override_comment, ro.return_type, ro.reverse_awb, ro.reverse_courier, ro.status as `status`, ro.return_create_datetime, ro.group_id, ro.last_update_datetime, ro.reverse_pickup_flag as reverse_pickup_flag, roi.uw_item_id,ro.return_method,ro.is_qc_at_doorstep\n" +
            "from return_order ro JOIN return_order_item roi\n" +
            "ON ro.return_id=roi.return_id\n" +
            "where ro.order_no=?1 AND roi.uw_item_id = ?2\n" +
            "order by roi.`return_create_datetime` desc\n" +
            "limit 1",nativeQuery = true)
    Map<String,Object> getReturnOrderResponse(Integer incrementId, Integer uwItemId);

    @Query(value = "select ro.* from return_order_item roi inner join return_order ro " +
            "ON roi.return_id = ro.`return_id` " +
            "AND ro.status NOT IN ('customer_cancelled','cancelled') " +
            "where roi.`uw_item_id` = ?1 order by roi.`return_create_datetime` desc limit 1", nativeQuery = true)
    ReturnOrder getReturnOrderBasedOnUwItemIdAndStatus(Integer uwItemId);

    @Query("""
       SELECT new com.lenskart.returncommon.model.dto.ReturnDetailsVSM(
                  ro.orderNo, ro.returnId, rdr.primaryReason, rdr.secondaryReason,
                  (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.returnCreateDatetime)) / 3600,
                  ro.receivingFlag, roi.status, ro.lastUpdateDatetime, ro.returnType,
                  ro.agentEmail, ro.reverseAwb, ro.status, roi.qcStatus,
                  roi.returnCreateDatetime, roi.uwItemId
              )
       FROM ReturnOrder ro
       JOIN ReturnOrderItem roi ON ro.returnId = roi.returnId
       LEFT JOIN ReturnReason rdr ON ro.returnId = rdr.returnId
       WHERE (:returnStatus IS NULL OR ro.status = :returnStatus)
       AND (ro.status IN (:eventTypes))
       AND (:returnStatus IS NULL OR (
               (:optDate IS NULL AND ro.status = 'return_closed')
               OR (:optDate = 'All' AND ro.status = :returnStatus)
               OR (:optDate = 'lastFollowup' AND ro.status = :returnStatus)
               OR (:optDate = 'statusChange' AND ro.status = :returnStatus)
               OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND ro.status = 'return_closed')
           ))
           AND (
           (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR ro.returnCreateDatetime BETWEEN :fromDate AND :toDate))
           OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.returnCreateDatetime BETWEEN :fromDate AND :toDate))
           OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.lastFollowupDatetime BETWEEN :fromDate AND :toDate))
           OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.returnCreateDatetime BETWEEN :fromDate AND :toDate))
           OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR ro.returnCreateDatetime BETWEEN :fromDate AND :toDate))
       )
       AND (
               (:returnType IS NULL AND ro.returnType IN ('awaited_rto', 'reverse', 'rto'))
               OR ((:returnType = 'Cod' AND ro.returnType = 'rto') OR ro.returnType = :returnType)
       )
       AND (:orderNo IS NULL OR ro.orderNo = :orderNo)
       AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qcStatus = :qcStatus))
       AND (
               (:agentEmail IS NULL AND ro.agentEmail IN ('', '0', 'Unassigned'))
               OR (
                   (:agentEmail = 'Unassigned' AND ro.agentEmail IN ('', '0', 'Unassigned'))
                   OR (:agentEmail = 'Assigned' AND ro.agentEmail NOT IN ('', '0', 'Unassigned'))
                   OR (ro.agentEmail = :agentEmail)
               )
       )
       AND (
               :reportFilterFld1 IS NULL
               OR :reportFilterFld1 = 'POS_revalidation' AND (ro.status = 'return_need_approval' OR ro.status = 'return_need_approval_from_whse')
               OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (ro.status = 'return_received' AND ro.status = 'reverse')
               OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (ro.status = 'return_received' AND ro.returnType = 'rto' AND ro.receivingFlag = 'yes')
               OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.returnType = 'rto' AND ro.receivingFlag = 'yes' AND roi.status = 'initiated_stockin')
               OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.returnType = 'reverse' AND ro.receivingFlag = 'yes' AND roi.status = 'initiated_stockin')
               OR :reportFilterFld1 = 'Return_Follow_Up' AND (ro.status = 'return_under_followup')
       )
       AND (
               :reportFilterFld2 IS NULL
               OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.returnCreateDatetime)) / 3600 <= 12
               OR :reportFilterFld2 = 'assign' AND (ro.agentEmail IS NOT NULL AND ro.agentEmail != '' AND ro.agentEmail != '0' AND ro.agentEmail != 'Unassigned')
       )
       AND (:bulkType IS NULL OR ro.bulkType = :bulkType)
       AND (:isInsurance IS NULL OR ro.isInsurance = :isInsurance)
       AND (:returnReasonsPrimary IS NULL OR rdr.primaryReason = :returnReasonsPrimary)
       AND (:returnReasonsSecondary IS NULL OR rdr.secondaryReason = :returnReasonsSecondary)
       AND (:followedUp IS NULL OR (
            (:followedUp = 'Yes' AND ro.reversePuFollowupCnt > 0)
            OR (:followedUp = 'No' AND ro.reversePuFollowupCnt = 0)
            OR (:followedUp = '' AND ro.reversePuFollowupCnt >= 0)))
       GROUP BY ro.returnId
       ORDER BY CASE WHEN :returnStatus = 'return_refund_rejected' THEN ro.returnCreateDatetime
                      ELSE roi.returnCreateDatetime END
       """
    )
    List<ReturnDetailsVSM> fetchReturnOrders(
            @Param("returnStatus") String returnStatus,
            @Param("optDate") String optDate,
            @Param("returnType") String returnType,
            @Param("orderNo") Integer orderNo,
            @Param("qcStatus") String qcStatus,
            @Param("agentEmail") String agentEmail,
            @Param("bulkType") String bulkType,
            @Param("fromDate") Date fromDate,
            @Param("toDate") Date toDate,
            @Param("returnReasonsPrimary") String returnReasonsPrimary,
            @Param("returnReasonsSecondary") String returnReasonsSecondary,
            @Param("isInsurance") Boolean isInsurance,
            @Param("followedUp") String followedUp,
            @Param("reportFilterFld1") String reportFilterFld1,
            @Param("reportFilterFld2") String reportFilterFld2,
            @Param("eventTypes") List<String> eventTypes);

    @Query(value = "select ro.* from return_order ro \n" +
            "where ro.return_create_datetime between ?2 AND ?3 \n" +
            "AND ro.facility_code = ?1\n" +
            " order by ro.return_create_datetime desc ", nativeQuery = true)
    List<ReturnOrder> getReturnOrdersByFacilityAndDateRange(String facility, Date startTime, Date endTime, Integer pageSize, Integer offset);
}