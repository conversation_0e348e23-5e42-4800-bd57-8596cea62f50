package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnDetailAddressUpdate;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface ReturnOrderAddressUpdateRepository extends CrudRepository<ReturnDetailAddressUpdate, Integer> {

    @Query(value = "SELECT postcode FROM return_detail_address_update where increment_id =?1 ORDER BY created_at DESC LIMIT 1", nativeQuery = true)
    Integer findTopPostcodeByOrderIdAndByCreatedAtDesc(Integer orderId);

    @Query(value = "SELECT * FROM return_detail_address_update where group_id=?1 order by created_at desc limit 1", nativeQuery = true)
    ReturnDetailAddressUpdate findByGroupId(Long groupId);

    @Query(value = "SELECT * FROM return_detail_address_update where increment_id =?1 ORDER BY created_at DESC LIMIT 1", nativeQuery = true)
    ReturnDetailAddressUpdate findByIncrement_id(Integer incrementId);

    @Query(value = "SELECT * FROM return_detail_address_update where group_id=?1 order by id desc limit 1", nativeQuery = true)
    ReturnDetailAddressUpdate findTop1ByGroupIdOrderByIdDesc(Long groupId);

    @Query(value = "SELECT * FROM return_detail_address_update WHERE address_type=?2 and group_id=?1 ORDER BY created_at DESC LIMIT 1", nativeQuery = true)
    ReturnDetailAddressUpdate getReturnOrderAddressUpdate(Long groupId, String addressType);

    @Query(value = "SELECT * FROM return_detail_address_update where increment_id =?1 and firstname is not null ORDER BY created_at DESC LIMIT 1", nativeQuery = true)
    ReturnDetailAddressUpdate findByIncrement_idAndFirstNameExists(Integer incrementId);
}
