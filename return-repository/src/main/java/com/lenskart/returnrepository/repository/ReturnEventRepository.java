package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnEvent;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface ReturnEventRepository extends CrudRepository<ReturnEvent, Integer> {

    ReturnEvent findTopByReturnIdAndEventInOrderByIdDesc(Integer returnId, List<String> eventTypes);

    List<ReturnEvent> findByReturnId(Integer returnId);

    List<ReturnEvent> findByReturnIdOrderByIdDesc(Integer returnId);

    ReturnEvent findTopByReturnIdAndEventOrderByIdDesc(Integer returnId, String event);

    List<ReturnEvent> findByReturnIdAndEventOrderByIdDesc(Integer returnId, String event);

    ReturnEvent findTopByReturnIdOrderByIdDesc(Integer returnId);

    List<ReturnEvent> findByReturnIdAndEventIn(Integer returnId, List<String> events);
    List<ReturnEvent> findByReturnRequestId(Integer requestId);

    List<ReturnEvent> findByEventInAndCreatedAtBetween(List<String> event, Date from, Date to);
    List<ReturnEvent> findByEventAndCreatedAtBetween(String event, Date from, Date to);

    @Query(value = """
            SELECT re.*
            FROM return_event re
            JOIN (
                SELECT return_id, MAX(id) AS max_id
                FROM return_event
                WHERE return_id IN :returnIds
                  AND event IN :eventTypes
                GROUP BY return_id
            ) latest ON re.id = latest.max_id
            
            """, nativeQuery = true)
    List<ReturnEvent> findLatestEventsByReturnIdsAndEventTypes(@Param("returnIds") List<Integer> returnIds, @Param("eventTypes") List<String> eventTypes);

}
