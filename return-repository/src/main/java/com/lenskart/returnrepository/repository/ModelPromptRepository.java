package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ModelPrompt;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ModelPromptRepository extends JpaRepository<ModelPrompt, Long> {

    // Custom query to fetch only active models and their prompts
    @Query("SELECT mp FROM ModelPrompt mp WHERE mp.isActive = true")
    List<ModelPrompt> findAllActive();

    @Modifying
    @Transactional
    @Query("UPDATE ModelPrompt mp SET mp.prompt = :prompt " + "WHERE mp.id = :id")
    int updateModelPrompt(@Param("prompt") String prompt, @Param("id") Integer id);
}