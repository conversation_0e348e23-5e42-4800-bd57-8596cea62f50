package com.lenskart.returnrepository.repository;

import com.lenskart.returncommon.model.dto.ReturnDetailsVSM;
import com.lenskart.returnrepository.entity.ReturnDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ReturnDetailRepository extends CrudRepository<ReturnDetail, Integer>, JpaSpecificationExecutor<ReturnDetail> {

    @Query(value = "SELECT * from return_detail WHERE group_id=?1 AND return_create_datetime>?2 ", nativeQuery = true)
    List<ReturnDetail> findByGroupIdAndDate(Long groupId, Date twoMonthsBackFromCurrDate);
    List<ReturnDetail> findByIncrementId(Integer orderId);

    List<ReturnDetail> findByIncrementIdIn(List<Integer> orderIds);

    @Query(value="select * from return_detail where increment_id in (?1) order by return_create_datetime desc", nativeQuery = true)
    List<ReturnDetail> getReturnOrders(List<Integer> orderIds);

    @Query(value = "select ro.* from return_detail ro join return_detail_item roi on ro.id = roi.return_id where ro.return_create_datetime between ?1 and ?2 AND ro.auto_cancel_flag = 1 AND roi.status IN ('new_reverse_pickup','reference_id_issued') limit ?3",nativeQuery = true)
    List<ReturnDetail> getReturnsForAutoCancellation(Date startDate, Date endDate , Integer batchSizeForReturnAutoCancelJob);

    @Query(value ="select * from return_detail where id in (?1)",nativeQuery = true)
    List<ReturnDetail> findByReturnIdIn(List<Integer> returnId);

    ReturnDetail findTop1ByIncrementIdAndUnicomOrderCodeAndReturnTypeOrderByReturnCreateDatetimeDesc(Integer incrementId, String unicomOrderCode, String returnType);

    List<ReturnDetail> findAllByGroupIdAndIncrementId(long groupId, Integer incrementId);

    @Query(value = "select count(id) from return_detail where increment_id =?1",nativeQuery = true)
    Integer getReturnCountByIncrementId(Integer incrementId);

    @Query(value = "select count(id) from return_detail where id =?1",nativeQuery = true)
    Integer getReturnCountByReturnId(Integer returnId);
    List<ReturnDetail> findByGroupId(long groupId);

    @Transactional
    @Modifying
    @Query(value="update return_detail SET receiving_flag=?3  where increment_id=?1 and id=?2 ", nativeQuery = true)
    void updateReceivingFlag(Integer incrementId, Integer returnId, String receivingFlag);

    @Query(value = "select id from ReturnDetail where incrementId =?1")
    List<Integer> findReturnIdsByIncrementId(Integer orderNo);

    @Query(value = "select groupId from ReturnDetail where id =?1")
    Long findGroupIdByReturnId(Integer b2bReturnId);

    @Query(value ="select * from return_detail where group_id in (?1)",nativeQuery = true)
    List<ReturnDetail> findByGroupIdIn(List<Long> groupId);

    List<ReturnDetail> findAllByGroupIdOrderByReturnCreateDatetimeDesc(long groupId);

    @Query(value="select * from return_detail where increment_id = ?1 LIMIT ?2 OFFSET ?3", nativeQuery = true)
    List<ReturnDetail> getReturnOrdersByPaging(Integer orderNo, Integer pageSize, Integer offset);

    @Query(value = "select ro.id as return_id, ro.increment_id as order_no,ro.source,'' as override_comment, ro.return_type, rcd.reverse_awb_number as reverse_awb, rcd.reverse_courier, (?3) as `status`, ro.return_create_datetime, ro.group_id, (?4) as last_update_datetime, (?5) as reverse_pickup_flag, roi.uw_item_id,ro.return_method,ro.is_qc_at_doorstep \n " +
            "from return_detail ro JOIN return_detail_item roi \n" +
            "ON ro.id=roi.return_id \n" +
            "left join return_courier_detail rcd \n" +
            "ON roi.return_id = rcd.return_id \n " +
            "WHERE ro.increment_id=?1 AND roi.uw_item_id = ?2 " +
            "order by roi.`return_create_datetime` desc \n" +
            "limit 1",nativeQuery = true)
    Map<String,Object> getReturnOrderResponse(Integer incrementId, Integer uwItemId, String status, Date lastUpdateTime, Integer reversePickupFlag);

    @Query(value = "select * from return_detail rd \n" +
            "order by rd.`return_create_datetime` desc \n" +
            "limit ?1 offset ?2", nativeQuery = true)
    List<ReturnDetail> getLatestReturns(int pageSize, int offset);

    @Query(value = "select ro.* from `return_courier_detail` rcd inner join return_detail ro " +
            "ON rcd.return_id = ro.id " +
            "where rcd.`reverse_courier` = ?1\n" +
            "LIMIT ?2 OFFSET ?3", nativeQuery = true)
    List<ReturnDetail> getReturnsByCourierAndPaging(String reverseCourier, Integer pageSize, int offset);

    @Query(value = "select ro.* from return_courier_detail rcd inner join return_detail ro " +
            "ON rcd.return_id = ro.id\n " +
            "where rcd.reverse_courier = ?1 \n" +
            "AND ro.return_create_datetime between ?2 AND ?3 order by ro.return_create_datetime desc  LIMIT ?4 OFFSET ?5", nativeQuery = true)
    List<ReturnDetail> getReturnsByCourier(String reverseCourier, Date startTime, Date endTime, Integer pageSize, Integer offset);

    @Query(value = "select ro.* from return_courier_detail rcd inner join return_detail ro\n" +
            " ON rcd.return_id = ro.id\n" +
            " inner join return_event re\n" +
            " ON ro.id = re.return_id\n" +
            " where rcd.reverse_courier = ?1\n" +
            " AND re.event = ?2\n" +
            " AND ro.return_create_datetime between ?3 AND ?4\n" +
            " LIMIT ?5 OFFSET ?6", nativeQuery = true)
    List<ReturnDetail> getReturnsByCourierAndStatus(String reverseCourier, String status, Date startTime, Date endTime, Integer pageSize, Integer offset);

    @Query(value = "select ro.* from return_courier_detail rcd inner join return_detail ro\n" +
            " ON rcd.return_id = ro.id\n" +
            " inner join return_event re\n" +
            " ON ro.id = re.return_id\n" +
            " where ro.increment_id = ?1\n" +
            " AND rcd.reverse_courier = ?2\n" +
            " AND re.event = ?3\n" +
            " AND ro.return_create_datetime between ?4 AND ?5 \n" +
            " order by ro.return_create_datetime desc \n" +
            " LIMIT ?6 OFFSET ?7", nativeQuery = true)
    List<ReturnDetail> getReturnOrdersByStatusOrderAndCourier(Integer orderNo, String reverseCourier, String event, Date startTime, Date endTime, Integer pageSize, Integer offset);

    @Query(value = "select ro.* from return_courier_detail rcd inner join return_detail ro\n" +
            " ON rcd.return_id = ro.id\n" +
            " where ro.increment_id = ?1\n" +
            " AND rcd.reverse_courier = ?2\n" +
            " AND ro.return_create_datetime between ?3 AND ?4\n" +
            " order by ro.return_create_datetime desc\n" +
            " LIMIT ?5 OFFSET ?6", nativeQuery = true)
    List<ReturnDetail> getReturnsByOrderAndCourier(Integer orderNo, String reverseCourier, Date startTime, Date endTime, Integer pageSize, Integer offset);

    @Query(value = "select ro.* from return_courier_detail rcd inner join return_detail ro\n" +
            " ON rcd.return_id = ro.id\n" +
            " inner join return_event re\n" +
            " ON ro.id = re.return_id\n" +
            " where ro.increment_id = ?1\n" +
            " AND re.event = ?2\n" +
            " AND ro.return_create_datetime between ?3 AND ?4 \n" +
            " order by ro.return_create_datetime desc \n" +
            " LIMIT ?5 OFFSET ?6", nativeQuery = true)
    List<ReturnDetail> getReturnOrdersByStatusAndOrder(Integer orderNo, String event, Date startTime, Date endTime, Integer pageSize, Integer offset);
    @Query(value = "select ro.* from return_detail ro\n" +
            " where ro.increment_id = ?1\n" +
            " AND ro.return_create_datetime between ?2 AND ?3\n" +
            "  order by ro.return_create_datetime desc \n" +
            " LIMIT ?4 OFFSET ?5", nativeQuery = true)
    List<ReturnDetail> getReturnsByOrder(Integer orderNo, Date startTime, Date endTime, Integer pageSize, Integer offset);

    @Query(value = " select ro.* from return_detail ro\n" +
            " where ro.return_create_datetime between ?1 AND ?2\n" +
            " LIMIT ?3 OFFSET ?4", nativeQuery = true)
    List<ReturnDetail> getReturnsByStatus(Date startTime, Date endTime, Integer pageSize, Integer offset);

    @Query(value = "select ro.* from return_detail_item roi inner join return_detail ro " +
            "ON roi.return_id = ro.id " +
            "inner join return_event re " +
            "ON ro.id = re.return_id " +
            "AND re.event NOT IN ('customer_cancelled','cancelled') " +
            "where roi.`uw_item_id` = ?1 order by roi.`return_create_datetime` desc limit 1", nativeQuery = true)
    ReturnDetail getReturnOrderBasedOnUwItemIdAndStatus(Integer uwItemId);



    @Query("""
           SELECT new com.lenskart.returncommon.model.dto.ReturnDetailsVSM(
                      ro.incrementId, ro.id, rdr.primaryReason, rdr.secondaryReason,
                      (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.returnCreateDatetime)) / 3600,
                      ro.receivingFlag, roi.status, re.createdAt, ro.returnType,
                      ro.agentEmail, rcr.reverseAwbNumber, re.event, roi.qcStatus,
                      roi.returnCreateDatetime, roi.uwItemId
                  )
           FROM ReturnDetail ro
           JOIN ReturnDetailItem roi ON ro.id = roi.returnId
           JOIN ReturnEvent re ON ro.id = re.returnId AND re.id = (SELECT MAX(sub.id) FROM ReturnEvent sub where sub.returnId = ro.id and sub.event IN (:eventTypes))
           LEFT JOIN ReturnDetailReason rdr ON ro.id = rdr.returnId
           LEFT JOIN ReturnCourierDetail rcr ON rcr.returnId = ro.id
           WHERE (:returnStatus IS NULL OR re.event = :returnStatus)
           AND (:returnStatus IS NULL OR (
               (:optDate IS NULL AND re.event = 'return_closed')
               OR (:optDate = 'All' AND re.event = :returnStatus)
               OR (:optDate = 'lastFollowup' AND re.event = :returnStatus)
               OR (:optDate = 'statusChange' AND re.event = :returnStatus)
               OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND re.event = 'return_closed')
           ))
           AND (
           (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR ro.returnCreateDatetime BETWEEN :fromDate AND :toDate))
           OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.returnCreateDatetime BETWEEN :fromDate AND :toDate))
           OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.lastFollowupDatetime BETWEEN :fromDate AND :toDate))
           OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR re.createdAt BETWEEN :fromDate AND :toDate))
           OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR ro.returnCreateDatetime BETWEEN :fromDate AND :toDate))
           )
           AND (
               (:returnType IS NULL AND ro.returnType IN ('awaited_rto', 'reverse', 'rto'))
               OR ((:returnType = 'Cod' AND ro.returnType = 'rto') OR ro.returnType = :returnType)
           )
           AND (:orderNo IS NULL OR ro.incrementId = :orderNo)
           AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qcStatus = :qcStatus))
           AND (
               (:agentEmail IS NULL AND ro.agentEmail IN ('', '0', 'Unassigned'))
               OR (
                   (:agentEmail = 'Unassigned' AND ro.agentEmail IN ('', '0', 'Unassigned'))
                   OR (:agentEmail = 'Assigned' AND ro.agentEmail NOT IN ('', '0', 'Unassigned'))
                   OR (ro.agentEmail = :agentEmail)
               )
           )
           AND (
               :reportFilterFld1 IS NULL
               OR :reportFilterFld1 = 'POS_revalidation' AND (re.event = 'return_need_approval' OR re.event = 'return_need_approval_from_whse')
               OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (re.event = 'return_received' AND re.event = 'reverse')
               OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (re.event = 'return_received' AND ro.returnType = 'rto' AND ro.receivingFlag = 'yes')
               OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (re.event = 'return_received' AND ro.returnType = 'rto' AND ro.receivingFlag = 'yes' AND roi.status = 'initiated_stockin')
               OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (re.event = 'return_received' AND ro.returnType = 'reverse' AND ro.receivingFlag = 'yes' AND roi.status = 'initiated_stockin')
               OR :reportFilterFld1 = 'Return_Follow_Up' AND (re.event = 'return_under_followup')
           )
           AND (
               :reportFilterFld2 IS NULL
               OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.returnCreateDatetime)) / 3600 <= 12
               OR :reportFilterFld2 = 'assign' AND (ro.agentEmail IS NOT NULL AND ro.agentEmail != '' AND ro.agentEmail != '0' AND ro.agentEmail != 'Unassigned')
           )
           AND (:bulkType IS NULL OR ro.bulkType = :bulkType)
           AND (:isInsurance IS NULL OR ro.isInsurance = :isInsurance)
           AND (:returnReasonsPrimary IS NULL OR rdr.primaryReason = :returnReasonsPrimary)
           AND (:returnReasonsSecondary IS NULL OR rdr.secondaryReason = :returnReasonsSecondary)
           AND (:followedUp IS NULL OR (:followedUp = 'Yes' AND ro.reversePuFollowupCnt > 0)
                OR (:followedUp = 'No' AND ro.reversePuFollowupCnt = 0)
                OR (:followedUp = '' AND ro.reversePuFollowupCnt >= 0))
           GROUP BY ro.id
           ORDER BY CASE WHEN :returnStatus = 'return_refund_rejected' THEN ro.returnCreateDatetime
                          ELSE roi.returnCreateDatetime END
           """)
    List<ReturnDetailsVSM> fetchReturnOrders(
            @Param("returnStatus") String returnStatus,
            @Param("optDate") String optDate,
            @Param("returnType") String returnType,
            @Param("orderNo") Integer orderNo,
            @Param("qcStatus") String qcStatus,
            @Param("agentEmail") String agentEmail,
            @Param("bulkType") String bulkType,
            @Param("fromDate") Date fromDate,
            @Param("toDate") Date toDate,
            @Param("returnReasonsPrimary") String returnReasonsPrimary,
            @Param("returnReasonsSecondary") String returnReasonsSecondary,
            @Param("isInsurance") Boolean isInsurance,
            @Param("followedUp") String followedUp,
            @Param("reportFilterFld1") String reportFilterFld1,
            @Param("reportFilterFld2") String reportFilterFld2,
            @Param("eventTypes") List<String> eventTypes);


    @Query(value = "select rd1.* from return_detail rd1 inner join return_detail rd2  ON rd1.group_id = rd2.group_id where rd2.id = ?1 AND rd1.id != rd2.id", nativeQuery = true)
    ReturnDetail getAlternateB2BReturnDetails(Integer returnId);

    @Query(value = "select ro.* from return_detail ro \n" +
            "where ro.return_create_datetime between ?2 AND ?3 \n" +
            "AND ro.facility_code = ?1\n" +
            "order by ro.return_create_datetime desc ", nativeQuery = true)
    List<ReturnDetail> getReturnOrdersByFacilityAndDateRange(String facility, Date startTime, Date endTime, Integer pageSize, Integer offset);

    Page<ReturnDetail> findByReturnCreateDatetimeBetween(Date startDate, Date endDate, Pageable pageable);

    @Query(value = "SELECT b.* FROM return_detail_item a JOIN return_detail b ON a.return_id = b.id WHERE a.uw_item_id=?2 and b.return_type IN (?1) order by a.return_create_datetime desc limit 1", nativeQuery = true)
    ReturnDetail findReturnIdByReturnTypeInAndUwItemID(List<String> returnType, Integer uwItemId);

    ReturnDetail findFirstByIdInOrderByReturnCreateDatetimeDesc(List<Integer> returnIds);
}
