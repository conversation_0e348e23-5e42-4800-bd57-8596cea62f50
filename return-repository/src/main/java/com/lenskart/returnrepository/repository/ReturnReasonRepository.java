package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnDetailReason;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Map;

public interface ReturnReasonRepository extends CrudRepository<ReturnDetailReason, Integer> {

    @Override
    @SuppressWarnings("unchecked")
    public ReturnDetailReason save(ReturnDetailReason returnReason);

    List<ReturnDetailReason> findByUwItemIdOrderByCreatedAtDesc(Integer orderId);

    List<ReturnDetailReason> findByUwItemId(Integer uwItemId);

    @Query(value ="select * from return_detail_reason where uw_item_id =?1 group by return_id", nativeQuery = true)
    List<ReturnDetailReason> findUniqueByUwItemId(Integer uwItemId);

    List<ReturnDetailReason> findByOrderId(Integer orderId);

    public List<ReturnDetailReason> findTopByUwItemIdOrderByCreatedAtDesc(int parseInt);

    ReturnDetailReason findTop1ByUwItemIdOrderByCreatedAtDesc(Integer uwItemId);

    @Query(value = "select * \n" +
            "from return_detail_reason r \n" +
            "where r.uw_item_id in (?1) order by r.created_at desc", nativeQuery = true)
    List<ReturnDetailReason> getReturnReasonsByItemId(List<Integer> uwItemIds);

    List<ReturnDetailReason> findByReturnId(Integer returnId);

    List<ReturnDetailReason> findByReturnIdIn(List<Integer> returnIds);

    @Query(value = "select rds.`primary_reason` , rds.`secondary_reason`\n" +
            "from return_detail rd  inner join return_detail_reason rds\n" +
            "ON rd.id = rds.`return_id`\n" +
            "where rd.id = ?1", nativeQuery = true)
    Map<String, String> getReasons(Integer returnId);

    ReturnDetailReason findTop1ByUwItemIdAndReturnIdOrderByCreatedAtDesc(Integer uwItemId, Integer returnId);
}
