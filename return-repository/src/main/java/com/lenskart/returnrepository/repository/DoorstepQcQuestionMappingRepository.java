package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.DoorstepQcQuestionMapping;
import org.springframework.data.repository.CrudRepository;

public interface DoorstepQcQuestionMappingRepository extends CrudRepository<DoorstepQcQuestionMapping, Integer> {


    DoorstepQcQuestionMapping findTopByClassificationAndReasonAndPeriodOrderByUpdatedAtDesc(int classification, int reasonId, String period);
}
