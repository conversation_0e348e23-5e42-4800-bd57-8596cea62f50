package com.lenskart.returnrepository.repository;

import com.lenskart.returncommon.model.dto.ReturnCourierDto;
import com.lenskart.returnrepository.entity.ReturnCourierDetail;
import com.lenskart.returnrepository.entity.ReturnDetail;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ReturnCourierDetailRepository extends CrudRepository<ReturnCourierDetail, Integer> {
    ReturnCourierDetail findTopByReturnId(Integer returnId);

    ReturnCourierDetail findTopById(Integer Id);

    ReturnCourierDetail findTopByReturnIdOrderByIdDesc(Integer returnId);

    ReturnCourierDetail findTopByReturnIdAndReverseCourierOrderByIdDesc(Integer returnId, String reverseCourier);

    List<ReturnCourierDetail> findByReturnIdIn(List<Integer> returnIds);

    @Query(value = "SELECT \n" +
            "rd.id as return_id, \n" +
            "rd.increment_id as order_no, \n" +
            "rcd.reverse_awb_number as reverse_awb, \n" +
            "rcd.reverse_courier as courier, \n" +
            "rd.return_create_datetime as created_at, \n" +
            "rcd.reverse_pickup_reference_id as reverse_pickup_reference_id, \n" +
            "null as reverse_pickup_flag, \n" +
            "rdi.uw_item_id \n" +
            "from return_detail rd JOIN return_detail_item rdi on rd.id = rdi.return_id \n" +
            "JOIN return_courier_detail rcd on rd.id = rcd.return_id \n" +
            "where rd.group_id = ?1 \n" +
            "AND rd.return_create_datetime > ?2", nativeQuery = true)
    List<Map<String, Object>> findCourierDetailsBasedByGroupId(Long groupId, Date startDate);

    @Query(value = "SELECT rd.increment_id as order_no,rc.reverse_courier as courier_name FROM return_courier_detail rc \n" +
            "INNER JOIN return_detail rd ON rd.id = rc.return_id\n" +
            "WHERE rc.reverse_awb_number = ?1\n" +
            "AND rd.return_type = ?2\n" +
            "order by rc.id desc \n" +
            "LIMIT 1", nativeQuery = true)
    Map<String,Object> findReturnTypeByReverseAwbNumber(String trackingNumber, String returnType);

    ReturnCourierDetail findTopByReverseAwbNumber(String reverseAwb);
}
