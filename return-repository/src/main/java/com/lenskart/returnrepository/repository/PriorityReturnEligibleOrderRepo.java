package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.PriorityReturnEligibleOrder;
import org.springframework.data.jpa.repository.JpaRepository;

public interface PriorityReturnEligibleOrderRepo extends JpaRepository<PriorityReturnEligibleOrder, Integer> {
    PriorityReturnEligibleOrder findTopByIncrementIdAndMagentoItemIdAndActiveIsTrue(Integer incrementId, Integer magentoItemId);
}
