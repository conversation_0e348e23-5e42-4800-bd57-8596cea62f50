package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.SystemPreference;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface SystemPreferenceRepository extends CrudRepository<SystemPreference, Long> {
    SystemPreference findTopByGroupAndKey(String group, String key);

    SystemPreference findAllByKey(String key);

    List<SystemPreference> findAllByGroup(String group);

    SystemPreference findByKey(String key);

    SystemPreference findByGroup(String group);
    List<SystemPreference> findAllByGroupIn(List<String> groups);

    SystemPreference findByGroupAndKey(String group, String key);
}
