package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ClaimDecisionRule;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ClaimDecisionRuleRepository extends CrudRepository<ClaimDecisionRule, Integer> {

    @Query(value = """
        SELECT warranty_period_in_days
        FROM claim_decision_rules
        WHERE category = ?1
          AND expected_condition_key = 'list_issue_type'
          AND FIND_IN_SET(?2, expected_condition_value) > 0
        LIMIT 1
        """, nativeQuery = true)
    String findWarrantyPeriodByCategoryAndReason(String category, String reason);
}