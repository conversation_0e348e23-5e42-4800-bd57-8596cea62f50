package com.lenskart.returnrepository.repository;


import com.lenskart.returnrepository.entity.FrameBrokenApproval;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Date;
import java.util.List;

public interface FrameBrokenApprovalRepo extends JpaRepository<FrameBrokenApproval, Integer> {

    FrameBrokenApproval findTopByItemIdAndStatusOrderByIdDesc(Integer itemId, String status);
    List<FrameBrokenApproval> findByItemIdInAndStatusOrderByIdDesc(List<Integer> itemId, String status);
    Integer countByCustomerIdAndStatusAndCreatedAtBetween(Long customerId, String status, Date createdAtAfter, Date createdAtBefore);
    Integer countByCustomerIdAndStatusAndCreatedAtBetweenAndRepairIdIsNull(Long customerId, String status, Date createdAtAfter, Date createdAtBefore);
    Integer countByMobileAndStatusAndCreatedAtBetweenAndRepairIdIsNull(String mobile, String status, Date createdAtAfter, Date createdAtBefore);
    Integer countByMobileAndStatusAndCreatedAtBetween(String mobile, String status, Date createdAtAfter, Date createdAtBefore);

}
