package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnHistory;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ReturnHistoryRepository extends CrudRepository<ReturnHistory, Integer> {
    ReturnHistory findTopByEntityIdAndCurrentStatus(Integer entityId, String currentStatus);

    List<ReturnHistory> findByEntityId(Integer entityId);

    @Query(value = "select * from return_history where entity_id = :entityId and entity_type = :entityType and current_status in (:currentStatus) order by created_at ASC limit 1", nativeQuery = true)
    ReturnHistory findTopByEntityIdAndEntityTypeAndCurrentStatusInOrderByCreatedAtAsc(@Param("entityId") Integer returnId,
                                                                                      @Param("entityType") String entityType,
                                                                                      @Param("currentStatus") List<String> currentStatus);

    @Query(value = "select * from return_history where entity_id = ?1 order by created_at DESC limit 1", nativeQuery = true)
    ReturnHistory findTopByEntityIdOrderByCreatedAtDesc(Integer returnId);
}