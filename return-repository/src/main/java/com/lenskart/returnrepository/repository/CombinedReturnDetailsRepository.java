package com.lenskart.returnrepository.repository;

import com.lenskart.returncommon.model.dto.ReturnDetailsVSM;
import com.lenskart.returnrepository.entity.ReturnDetail;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.SqlResultSetMapping;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface CombinedReturnDetailsRepository extends CrudRepository<ReturnDetail, Integer> {

    @Query(value = """
    SELECT ro.increment_id AS orderNo, ro.id AS returnId, rdr.primary_reason AS primaryReason, 
           rdr.secondary_reason AS secondaryReason, 
           (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 AS time,
           ro.receiving_flag AS receivingFlag, roi.status AS roiStatus, re.created_at AS statusChangeDate, ro.return_type AS returnType,
           rr.agent_id AS agentEmail, rcr.reverse_awb_number AS reverseAwb, re.event AS status, roi.qc_status AS qcStatus,
           roi.return_create_datetime AS returnCreateDatetime, roi.uw_item_id AS uwItemId, roi.classification AS classification
    FROM return_detail ro
    JOIN return_detail_item roi ON ro.id = roi.return_id
    JOIN return_event re ON ro.id = re.return_id 
    AND re.id = (SELECT MAX(sub.id) FROM return_event sub WHERE sub.return_id = ro.id AND sub.event IN (:eventTypes))
    LEFT JOIN return_detail_reason rdr ON ro.id = rdr.return_id
    LEFT JOIN return_request rr ON rr.id = ro.request_id
    LEFT JOIN return_courier_detail rcr ON rcr.return_id = ro.id
    LEFT JOIN return_detail_address_update ra ON ra.group_id = ro.group_id
    WHERE (:returnStatus IS NULL OR re.event = :returnStatus)
    AND (:returnStatus IS NULL OR (
            (:optDate IS NULL AND re.event = 'return_closed')
            OR (:optDate = 'All' AND re.event = :returnStatus)
            OR (:optDate = 'lastFollowup' AND re.event = :returnStatus)
            OR (:optDate = 'statusChange' AND re.event = :returnStatus)
            OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND re.event = 'return_closed')
    ))
    AND (
        (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR
            CASE
                WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                ELSE roi.return_create_datetime
            END BETWEEN :fromDate AND :toDate)
        )
        OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR
            CASE
                WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                ELSE roi.return_create_datetime
            END BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.last_followup_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR re.created_at BETWEEN :fromDate AND :toDate))
        OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR
            CASE
                WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                ELSE roi.return_create_datetime
            END BETWEEN :fromDate AND :toDate))
    )
    AND (
        (:returnType IS NULL AND
            (ro.return_type IN ('awaited_rto', 'reverse')
            OR (ro.return_type = 'rto' AND roi.method != 'cashondelivery')
            OR (ro.return_type = 'rto' AND roi.method = 'cashondelivery'))
        )
        OR (:returnType = 'rto' AND ro.return_type = 'rto' AND roi.method != 'cashondelivery')
        OR (:returnType = 'Cod' AND ro.return_type = 'rto' AND roi.method = 'cashondelivery')
        OR (:returnType IN ('awaited_rto', 'reverse') AND ro.return_type = :returnType)
    )
    AND (:orderNo IS NULL OR ro.increment_id = :orderNo)
    AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qc_status = :qcStatus))
    AND (
            (:agentEmail IS NULL AND rr.agent_id IN ('', '0', 'Unassigned'))
            OR (
                (:agentEmail = 'Unassigned' AND rr.agent_id IN ('', '0', 'Unassigned'))
                OR (:agentEmail = 'Assigned' AND rr.agent_id NOT IN ('', '0', 'Unassigned'))
                OR (rr.agent_id = :agentEmail)
            )
    )
    AND (
            :reportFilterFld1 IS NULL
            OR :reportFilterFld1 = 'POS_revalidation' AND (re.event = 'return_need_approval' OR re.event = 'return_need_approval_from_whse')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (re.event = 'return_received' AND re.event = 'reverse')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (re.event = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (re.event = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (re.event = 'return_received' AND ro.return_type = 'reverse' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'Return_Follow_Up' AND (re.event = 'return_under_followup')
    )
    AND (
            :reportFilterFld2 IS NULL
            OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 <= 12
            OR :reportFilterFld2 = 'assign' AND (rr.agent_id IS NOT NULL AND rr.agent_id != '' AND rr.agent_id != '0' AND rr.agent_id != 'Unassigned')
    )
    AND (:isInsurance IS NULL OR ro.is_insurance = :isInsurance)
    AND (:country IS NULL OR ra.country_id = :country)
    AND (:classification IS NULL OR roi.classification = :classification)
    AND (:franchiseList IS NULL OR roi.is_franchise IN (:franchiseList))
    AND (:returnReasonsPrimary IS NULL OR rdr.primary_reason = :returnReasonsPrimary)
    AND (:returnReasonsSecondary IS NULL OR rdr.secondary_reason = :returnReasonsSecondary)
    AND ((:paymentMethods IS NULL AND roi.method IS NOT NULL) OR roi.method IN (:paymentMethods))
    AND (:orderType IS NULL
        OR (:orderType = 'bulk' AND ro.bulk_type = :orderType)
        OR (:orderType = 'normal' AND ro.bulk_type = ''
            AND roi.method NOT IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                                   'purchaseorder', 'storepaytm', 'storeairtel'))
        OR (:orderType = 'franchisecredit-b2b' AND :returnType != 'Cod'
            AND roi.method IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                               'storepaytm', 'storeairtel')
            AND roi.channel = 'B2B')
        OR (:orderType = 'franchisecredit-franchiseBulk' AND :returnType != 'Cod'
            AND roi.method = 'franchisecredit'
            AND roi.channel = 'FranchiseBulk')
        OR (:orderType = 'purchaseorder' AND :returnType != 'Cod'
            AND roi.method = 'purchaseorder')
    )
    AND (:followedUp IS NULL OR (
        (:followedUp = 'Yes' AND ro.reverse_pu_followup_cnt > 0)
        OR (:followedUp = 'No' AND ro.reverse_pu_followup_cnt = 0)
        OR (:followedUp = '' AND ro.reverse_pu_followup_cnt >= 0)))
    GROUP BY ro.id
    ORDER BY CASE WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                  ELSE roi.return_create_datetime END
""", nativeQuery = true)
    Page<ReturnDetailsVSM> fetchReturnOrdersNew(
            @Param("returnStatus") String returnStatus,
            @Param("optDate") String optDate,
            @Param("fromDate") Date fromDate,
            @Param("toDate") Date toDate,
            @Param("returnType") String returnType,
            @Param("orderNo") Integer orderNo,
            @Param("qcStatus") String qcStatus,
            @Param("agentEmail") String agentEmail,
            @Param("orderType") String orderType,
            @Param("returnReasonsPrimary") String returnReasonsPrimary,
            @Param("returnReasonsSecondary") String returnReasonsSecondary,
            @Param("isInsurance") Boolean isInsurance,
            @Param("followedUp") String followedUp,
            @Param("reportFilterFld1") String reportFilterFld1,
            @Param("reportFilterFld2") String reportFilterFld2,
            @Param("eventTypes") List<String> eventTypes,
            @Param("classification") Integer classification,
            @Param("franchiseList") List<String> franchiseList,
            @Param("country") String country,
            @Param("paymentMethods") List<String> paymentMethods,
            Pageable pageable);

    @Query(value = """
    SELECT ro.order_no AS orderNo, ro.return_id AS returnId, rdr.primary_reason AS primaryReason, 
           rdr.secondary_reason AS secondaryReason, 
           (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 AS timeDiff,
           ro.receiving_flag AS receivingFlag, roi.status AS roiStatus, ro.last_update_datetime AS statusChangeDate, ro.return_type AS returnType,
           ro.agent_email AS agentEmail, ro.reverse_awb AS reverseAwb, ro.status AS status, roi.qc_status AS qcStatus,
           roi.return_create_datetime AS returnCreateDatetime, roi.uw_item_id AS uwItemId, roi.classification AS classification
    FROM return_order ro
    JOIN return_order_item roi ON ro.return_id = roi.return_id
    LEFT JOIN return_reason rdr ON ro.return_id = rdr.return_id
    LEFT JOIN return_order_address_update ra ON ra.group_id = ro.group_id
    WHERE (:returnStatus IS NULL OR ro.status = :returnStatus)
    AND (ro.status IN (:eventTypes))
    AND (:returnStatus IS NULL OR (
            (:optDate IS NULL AND ro.status = 'return_closed')
            OR (:optDate = 'All' AND ro.status = :returnStatus)
            OR (:optDate = 'lastFollowup' AND ro.status = :returnStatus)
            OR (:optDate = 'statusChange' AND ro.status = :returnStatus)
            OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND ro.status = 'return_closed')
    ))
    AND (
        (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR
            CASE
                WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                ELSE roi.return_create_datetime
            END BETWEEN :fromDate AND :toDate)
        )
        OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR
            CASE
                WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                ELSE roi.return_create_datetime
            END BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.last_followup_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR
            CASE
                WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                ELSE roi.return_create_datetime
            END BETWEEN :fromDate AND :toDate))
    )
    AND (
        (:returnType IS NULL AND
            (ro.return_type IN ('awaited_rto', 'reverse')
            OR (ro.return_type = 'rto' AND roi.method != 'cashondelivery')
            OR (ro.return_type = 'rto' AND roi.method = 'cashondelivery'))
        )
        OR (:returnType = 'rto' AND ro.return_type = 'rto' AND roi.method != 'cashondelivery')
        OR (:returnType = 'Cod' AND ro.return_type = 'rto' AND roi.method = 'cashondelivery')
        OR (:returnType IN ('awaited_rto', 'reverse') AND ro.return_type = :returnType)
    )
    AND (:orderNo IS NULL OR ro.order_no = :orderNo)
    AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qc_status = :qcStatus))
    AND (
            (:agentEmail IS NULL AND ro.agent_email IN ('', '0', 'Unassigned'))
            OR (
                (:agentEmail = 'Unassigned' AND ro.agent_email IN ('', '0', 'Unassigned'))
                OR (:agentEmail = 'Assigned' AND ro.agent_email NOT IN ('', '0', 'Unassigned'))
                OR (ro.agent_email = :agentEmail)
            )
    )
    AND (
            :reportFilterFld1 IS NULL
            OR :reportFilterFld1 = 'POS_revalidation' AND (ro.status = 'return_need_approval' OR ro.status = 'return_need_approval_from_whse')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (ro.status = 'return_received' AND ro.status = 'reverse')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (ro.status = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.return_type = 'reverse' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'Return_Follow_Up' AND (ro.status = 'return_under_followup')
    )
    AND (
            :reportFilterFld2 IS NULL
            OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 <= 12
            OR :reportFilterFld2 = 'assign' AND (ro.agent_email IS NOT NULL AND ro.agent_email != '' AND ro.agent_email != '0' AND ro.agent_email != 'Unassigned')
    )
    AND (:isInsurance IS NULL OR ro.is_insurance = :isInsurance)
    AND (:country IS NULL OR ra.country_id = :country)
    AND (:classification IS NULL OR roi.classification = :classification)
    AND (:franchiseList IS NULL OR roi.is_franchise IN (:franchiseList))
    AND (:returnReasonsPrimary IS NULL OR rdr.primary_reason = :returnReasonsPrimary)
    AND (:returnReasonsSecondary IS NULL OR rdr.secondary_reason = :returnReasonsSecondary)
    AND ((:paymentMethods IS NULL AND roi.method IS NOT NULL) OR roi.method IN (:paymentMethods))
    AND (:orderType IS NULL
        OR (:orderType = 'bulk' AND ro.bulktype = :orderType)
        OR (:orderType = 'normal' AND ro.bulktype = ''
            AND roi.method NOT IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                                   'purchaseorder', 'storepaytm', 'storeairtel'))
        OR (:orderType = 'franchisecredit-b2b' AND :returnType != 'Cod'
            AND roi.method IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                               'storepaytm', 'storeairtel')
            AND roi.channel = 'B2B')
        OR (:orderType = 'franchisecredit-franchiseBulk' AND :returnType != 'Cod'
            AND roi.method = 'franchisecredit'
            AND roi.channel = 'FranchiseBulk')
        OR (:orderType = 'purchaseorder' AND :returnType != 'Cod'
            AND roi.method = 'purchaseorder')
    )
    AND (:followedUp IS NULL OR (
        (:followedUp = 'Yes' AND ro.reverse_pu_followup_cnt > 0)
        OR (:followedUp = 'No' AND ro.reverse_pu_followup_cnt = 0)
        OR (:followedUp = '' AND ro.reverse_pu_followup_cnt >= 0)))
    GROUP BY ro.return_id
    ORDER BY CASE WHEN :returnStatus = 'return_refund_rejected' THEN ro.return_create_datetime
                  ELSE roi.return_create_datetime END
""", nativeQuery = true)
    Page<ReturnDetailsVSM> fetchReturnOrdersOld(
            @Param("returnStatus") String returnStatus,
            @Param("optDate") String optDate,
            @Param("fromDate") Date fromDate,
            @Param("toDate") Date toDate,
            @Param("returnType") String returnType,
            @Param("orderNo") Integer orderNo,
            @Param("qcStatus") String qcStatus,
            @Param("agentEmail") String agentEmail,
            @Param("orderType") String orderType,
            @Param("returnReasonsPrimary") String returnReasonsPrimary,
            @Param("returnReasonsSecondary") String returnReasonsSecondary,
            @Param("isInsurance") Boolean isInsurance,
            @Param("followedUp") String followedUp,
            @Param("reportFilterFld1") String reportFilterFld1,
            @Param("reportFilterFld2") String reportFilterFld2,
            @Param("eventTypes") List<String> eventTypes,
            @Param("classification") Integer classification,
            @Param("franchiseList") List<String> franchiseList,
            @Param("country") String country,
            @Param("paymentMethods") List<String> paymentMethods,
            Pageable pageable);


    //original condition
    //AND ((:paymentMethods IS NULL AND roi.method IS NOT NULL) OR roi.method IN (:paymentMethods))


    @Query(value = """
SELECT orderNo, returnId, primaryReason, secondaryReason, timeDiff, receivingFlag, roiStatus, statusChangeDate, returnType,
agentEmail,  reverseAwb, status, qcStatus, returnCreateDatetime, uwItemId, classification FROM (
    SELECT ro.increment_id AS orderNo, ro.id AS returnId, rdr.primary_reason AS primaryReason, 
           rdr.secondary_reason AS secondaryReason, 
           (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 AS timeDiff,
           ro.receiving_flag AS receivingFlag, roi.status AS roiStatus, re.created_at AS statusChangeDate, ro.return_type AS returnType,
           rr.agent_id AS agentEmail, rcr.reverse_awb_number AS reverseAwb, re.event AS status, roi.qc_status AS qcStatus,
           roi.return_create_datetime AS returnCreateDatetime, roi.uw_item_id AS uwItemId, roi.classification AS classification
    FROM return_detail ro
    JOIN return_detail_item roi ON ro.id = roi.return_id
    JOIN return_event re ON ro.id = re.return_id 
    AND re.id = (SELECT MAX(sub.id) FROM return_event sub WHERE sub.return_id = ro.id AND sub.event IN (:eventTypes))
    LEFT JOIN return_detail_reason rdr ON ro.id = rdr.return_id
    LEFT JOIN return_request rr ON rr.id = ro.request_id
    LEFT JOIN return_courier_detail rcr ON rcr.return_id = ro.id
    LEFT JOIN return_detail_address_update ra ON ra.group_id = ro.group_id
    WHERE (:returnStatus IS NULL OR re.event = :returnStatus)
    AND (:returnStatus IS NULL OR (
            (:optDate IS NULL AND re.event = 'return_closed')
            OR (:optDate = 'All' AND re.event = :returnStatus)
            OR (:optDate = 'lastFollowup' AND re.event = :returnStatus)
            OR (:optDate = 'statusChange' AND re.event = :returnStatus)
            OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND re.event = 'return_closed')
    ))
    AND (
        (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.last_followup_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR re.created_at BETWEEN :fromDate AND :toDate))
        OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
    )
    AND (
        (:returnType IS NULL AND
            (ro.return_type IN ('awaited_rto', 'reverse')
            OR (ro.return_type = 'rto' AND roi.method != 'cashondelivery')
            OR (ro.return_type = 'rto' AND roi.method = 'cashondelivery'))
        )
        OR (:returnType = 'rto' AND ro.return_type = 'rto' AND roi.method != 'cashondelivery')
        OR (:returnType = 'Cod' AND ro.return_type = 'rto' AND roi.method = 'cashondelivery')
        OR (:returnType IN ('awaited_rto', 'reverse') AND ro.return_type = :returnType)
    )
    AND (:orderNo IS NULL OR ro.increment_id = :orderNo)
    AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qc_status = :qcStatus))
    AND (
            (:agentEmail IS NULL AND rr.agent_id IN ('', '0', 'Unassigned'))
            OR (
                (:agentEmail = 'Unassigned' AND rr.agent_id IN ('', '0', 'Unassigned'))
                OR (:agentEmail = 'Assigned' AND rr.agent_id NOT IN ('', '0', 'Unassigned'))
                OR (rr.agent_id = :agentEmail)
            )
    )
    AND (
            :reportFilterFld1 IS NULL
            OR :reportFilterFld1 = 'POS_revalidation' AND (re.event = 'return_need_approval' OR re.event = 'return_need_approval_from_whse')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (re.event = 'return_received' AND re.event = 'reverse')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (re.event = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (re.event = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (re.event = 'return_received' AND ro.return_type = 'reverse' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'Return_Follow_Up' AND (re.event = 'return_under_followup')
    )
    AND (
            :reportFilterFld2 IS NULL
            OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 <= 12
            OR :reportFilterFld2 = 'assign' AND (rr.agent_id IS NOT NULL AND rr.agent_id != '' AND rr.agent_id != '0' AND rr.agent_id != 'Unassigned')
    )
    AND (:isInsurance IS NULL OR ro.is_insurance = :isInsurance)
    AND (:country IS NULL OR ra.country_id = :country)
    AND (:classification IS NULL OR roi.classification = :classification)
    AND (:franchiseList IS NULL OR roi.is_franchise IN (:franchiseList))
    AND (:returnReasonsPrimary IS NULL OR rdr.primary_reason = :returnReasonsPrimary)
    AND (:returnReasonsSecondary IS NULL OR rdr.secondary_reason = :returnReasonsSecondary)
    AND ((:paymentMethods IS NULL) OR roi.method IN (:paymentMethods))
    AND (:orderType IS NULL
        OR (:orderType = 'bulk' AND ro.bulk_type = :orderType)
        OR (:orderType = 'normal' AND ro.bulk_type = ''
            AND roi.method NOT IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                                   'purchaseorder', 'storepaytm', 'storeairtel'))
        OR (:orderType = 'franchisecredit-b2b' AND :returnType != 'Cod'
            AND roi.method IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                               'storepaytm', 'storeairtel')
            AND roi.channel = 'B2B')
        OR (:orderType = 'franchisecredit-franchiseBulk' AND :returnType != 'Cod'
            AND roi.method = 'franchisecredit'
            AND roi.channel = 'FranchiseBulk')
        OR (:orderType = 'purchaseorder' AND :returnType != 'Cod'
            AND roi.method = 'purchaseorder')
    )
    AND (:followedUp IS NULL OR (
        (:followedUp = 'Yes' AND ro.reverse_pu_followup_cnt > 0)
        OR (:followedUp = 'No' AND ro.reverse_pu_followup_cnt = 0)
        OR (:followedUp = '' AND ro.reverse_pu_followup_cnt >= 0)))
    GROUP BY ro.id
                  
UNION

    SELECT ro.order_no AS orderNo, ro.return_id AS returnId, rdr.primary_reason AS primaryReason, 
           rdr.secondary_reason AS secondaryReason, 
           (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 AS timeDiff,
           ro.receiving_flag AS receivingFlag, roi.status AS roiStatus, ro.last_update_datetime AS statusChangeDate, ro.return_type AS returnType,
           ro.agent_email AS agentEmail, ro.reverse_awb AS reverseAwb, ro.status AS status, roi.qc_status AS qcStatus,
           roi.return_create_datetime AS returnCreateDatetime, roi.uw_item_id AS uwItemId, roi.classification AS classification
    FROM return_order ro
    JOIN return_order_item roi ON ro.return_id = roi.return_id
    LEFT JOIN return_reason rdr ON ro.return_id = rdr.return_id
    LEFT JOIN return_order_address_update ra ON ra.group_id = ro.group_id
    WHERE (:returnStatus IS NULL OR ro.status = :returnStatus)
    AND (ro.status IN (:eventTypes))
    AND (:returnStatus IS NULL OR (
            (:optDate IS NULL AND ro.status = 'return_closed')
            OR (:optDate = 'All' AND ro.status = :returnStatus)
            OR (:optDate = 'lastFollowup' AND ro.status = :returnStatus)
            OR (:optDate = 'statusChange' AND ro.status = :returnStatus)
            OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND ro.status = 'return_closed')
    ))
    AND (
        (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.last_followup_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
    )
    AND (
        (:returnType IS NULL AND
            (ro.return_type IN ('awaited_rto', 'reverse')
            OR (ro.return_type = 'rto' AND roi.method != 'cashondelivery')
            OR (ro.return_type = 'rto' AND roi.method = 'cashondelivery'))
        )
        OR (:returnType = 'rto' AND ro.return_type = 'rto' AND roi.method != 'cashondelivery')
        OR (:returnType = 'Cod' AND ro.return_type = 'rto' AND roi.method = 'cashondelivery')
        OR (:returnType IN ('awaited_rto', 'reverse') AND ro.return_type = :returnType)
    )
    AND (:orderNo IS NULL OR ro.order_no = :orderNo)
    AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qc_status = :qcStatus))
    AND (
            (:agentEmail IS NULL AND ro.agent_email IN ('', '0', 'Unassigned'))
            OR (
                (:agentEmail = 'Unassigned' AND ro.agent_email IN ('', '0', 'Unassigned'))
                OR (:agentEmail = 'Assigned' AND ro.agent_email NOT IN ('', '0', 'Unassigned'))
                OR (ro.agent_email = :agentEmail)
            )
    )
    AND (
            :reportFilterFld1 IS NULL
            OR :reportFilterFld1 = 'POS_revalidation' AND (ro.status = 'return_need_approval' OR ro.status = 'return_need_approval_from_whse')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (ro.status = 'return_received' AND ro.status = 'reverse')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (ro.status = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.return_type = 'reverse' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'Return_Follow_Up' AND (ro.status = 'return_under_followup')
    )
    AND (
            :reportFilterFld2 IS NULL
            OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 <= 12
            OR :reportFilterFld2 = 'assign' AND (ro.agent_email IS NOT NULL AND ro.agent_email != '' AND ro.agent_email != '0' AND ro.agent_email != 'Unassigned')
    )
    AND (:isInsurance IS NULL OR ro.is_insurance = :isInsurance)
    AND (:country IS NULL OR ra.country_id = :country)
    AND (:classification IS NULL OR roi.classification = :classification)
    AND (:franchiseList IS NULL OR roi.is_franchise IN (:franchiseList))
    AND (:returnReasonsPrimary IS NULL OR rdr.primary_reason = :returnReasonsPrimary)
    AND (:returnReasonsSecondary IS NULL OR rdr.secondary_reason = :returnReasonsSecondary)
    AND ((:paymentMethods IS NULL) OR roi.method IN (:paymentMethods))
    AND (:orderType IS NULL
        OR (:orderType = 'bulk' AND ro.bulktype = :orderType)
        OR (:orderType = 'normal' AND ro.bulktype = ''
            AND roi.method NOT IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                                   'purchaseorder', 'storepaytm', 'storeairtel'))
        OR (:orderType = 'franchisecredit-b2b' AND :returnType != 'Cod'
            AND roi.method IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                               'storepaytm', 'storeairtel')
            AND roi.channel = 'B2B')
        OR (:orderType = 'franchisecredit-franchiseBulk' AND :returnType != 'Cod'
            AND roi.method = 'franchisecredit'
            AND roi.channel = 'FranchiseBulk')
        OR (:orderType = 'purchaseorder' AND :returnType != 'Cod'
            AND roi.method = 'purchaseorder')
    )
    AND (:followedUp IS NULL OR (
        (:followedUp = 'Yes' AND ro.reverse_pu_followup_cnt > 0)
        OR (:followedUp = 'No' AND ro.reverse_pu_followup_cnt = 0)
        OR (:followedUp = '' AND ro.reverse_pu_followup_cnt >= 0)))
    GROUP BY ro.return_id
) AS combinedResults ORDER BY returnCreateDatetime
""", countQuery = """
SELECT COUNT(*) FROM (
    SELECT ro.id
    FROM return_detail ro
    JOIN return_detail_item roi ON ro.id = roi.return_id
    JOIN return_event re ON ro.id = re.return_id 
    AND re.id = (SELECT MAX(sub.id) FROM return_event sub WHERE sub.return_id = ro.id AND sub.event IN (:eventTypes))
    LEFT JOIN return_detail_reason rdr ON ro.id = rdr.return_id
    LEFT JOIN return_request rr ON rr.id = ro.request_id
    LEFT JOIN return_courier_detail rcr ON rcr.return_id = ro.id
    LEFT JOIN return_detail_address_update ra ON ra.group_id = ro.group_id
    WHERE (:returnStatus IS NULL OR re.event = :returnStatus)
    AND (:returnStatus IS NULL OR (
            (:optDate IS NULL AND re.event = 'return_closed')
            OR (:optDate = 'All' AND re.event = :returnStatus)
            OR (:optDate = 'lastFollowup' AND re.event = :returnStatus)
            OR (:optDate = 'statusChange' AND re.event = :returnStatus)
            OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND re.event = 'return_closed')
    ))
    AND (
        (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.last_followup_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR re.created_at BETWEEN :fromDate AND :toDate))
        OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
    )
    AND (
        (:returnType IS NULL AND
            (ro.return_type IN ('awaited_rto', 'reverse')
            OR (ro.return_type = 'rto' AND roi.method != 'cashondelivery')
            OR (ro.return_type = 'rto' AND roi.method = 'cashondelivery'))
        )
        OR (:returnType = 'rto' AND ro.return_type = 'rto' AND roi.method != 'cashondelivery')
        OR (:returnType = 'Cod' AND ro.return_type = 'rto' AND roi.method = 'cashondelivery')
        OR (:returnType IN ('awaited_rto', 'reverse') AND ro.return_type = :returnType)
    )
    AND (:orderNo IS NULL OR ro.increment_id = :orderNo)
    AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qc_status = :qcStatus))
    AND (
            (:agentEmail IS NULL AND rr.agent_id IN ('', '0', 'Unassigned'))
            OR (
                (:agentEmail = 'Unassigned' AND rr.agent_id IN ('', '0', 'Unassigned'))
                OR (:agentEmail = 'Assigned' AND rr.agent_id NOT IN ('', '0', 'Unassigned'))
                OR (rr.agent_id = :agentEmail)
            )
    )
    AND (
            :reportFilterFld1 IS NULL
            OR :reportFilterFld1 = 'POS_revalidation' AND (re.event = 'return_need_approval' OR re.event = 'return_need_approval_from_whse')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (re.event = 'return_received' AND re.event = 'reverse')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (re.event = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (re.event = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (re.event = 'return_received' AND ro.return_type = 'reverse' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'Return_Follow_Up' AND (re.event = 'return_under_followup')
    )
    AND (
            :reportFilterFld2 IS NULL
            OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 <= 12
            OR :reportFilterFld2 = 'assign' AND (rr.agent_id IS NOT NULL AND rr.agent_id != '' AND rr.agent_id != '0' AND rr.agent_id != 'Unassigned')
    )
    AND (:isInsurance IS NULL OR ro.is_insurance = :isInsurance)
    AND (:country IS NULL OR ra.country_id = :country)
    AND (:classification IS NULL OR roi.classification = :classification)
    AND (:franchiseList IS NULL OR roi.is_franchise IN (:franchiseList))
    AND (:returnReasonsPrimary IS NULL OR rdr.primary_reason = :returnReasonsPrimary)
    AND (:returnReasonsSecondary IS NULL OR rdr.secondary_reason = :returnReasonsSecondary)
    AND ((:paymentMethods IS NULL) OR roi.method IN (:paymentMethods))
    AND (:orderType IS NULL
        OR (:orderType = 'bulk' AND ro.bulk_type = :orderType)
        OR (:orderType = 'normal' AND ro.bulk_type = ''
            AND roi.method NOT IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                                   'purchaseorder', 'storepaytm', 'storeairtel'))
        OR (:orderType = 'franchisecredit-b2b' AND :returnType != 'Cod'
            AND roi.method IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                               'storepaytm', 'storeairtel')
            AND roi.channel = 'B2B')
        OR (:orderType = 'franchisecredit-franchiseBulk' AND :returnType != 'Cod'
            AND roi.method = 'franchisecredit'
            AND roi.channel = 'FranchiseBulk')
        OR (:orderType = 'purchaseorder' AND :returnType != 'Cod'
            AND roi.method = 'purchaseorder')
    )
    AND (:followedUp IS NULL OR (
        (:followedUp = 'Yes' AND ro.reverse_pu_followup_cnt > 0)
        OR (:followedUp = 'No' AND ro.reverse_pu_followup_cnt = 0)
        OR (:followedUp = '' AND ro.reverse_pu_followup_cnt >= 0)))
    GROUP BY ro.id
                  
UNION

    SELECT ro.return_id
    FROM return_order ro
    JOIN return_order_item roi ON ro.return_id = roi.return_id
    LEFT JOIN return_reason rdr ON ro.return_id = rdr.return_id
    LEFT JOIN return_order_address_update ra ON ra.group_id = ro.group_id
    WHERE (:returnStatus IS NULL OR ro.status = :returnStatus)
    AND (ro.status IN (:eventTypes))
    AND (:returnStatus IS NULL OR (
            (:optDate IS NULL AND ro.status = 'return_closed')
            OR (:optDate = 'All' AND ro.status = :returnStatus)
            OR (:optDate = 'lastFollowup' AND ro.status = :returnStatus)
            OR (:optDate = 'statusChange' AND ro.status = :returnStatus)
            OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND ro.status = 'return_closed')
    ))
    AND (
        (:optDate IS NULL AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'All' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'lastFollowup' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.last_followup_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate = 'statusChange' AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
        OR (:optDate NOT IN ('All', 'lastFollowup', 'statusChange') AND (:fromDate IS NULL OR :toDate IS NULL OR ro.return_create_datetime BETWEEN :fromDate AND :toDate))
    )
    AND (
        (:returnType IS NULL AND
            (ro.return_type IN ('awaited_rto', 'reverse')
            OR (ro.return_type = 'rto' AND roi.method != 'cashondelivery')
            OR (ro.return_type = 'rto' AND roi.method = 'cashondelivery'))
        )
        OR (:returnType = 'rto' AND ro.return_type = 'rto' AND roi.method != 'cashondelivery')
        OR (:returnType = 'Cod' AND ro.return_type = 'rto' AND roi.method = 'cashondelivery')
        OR (:returnType IN ('awaited_rto', 'reverse') AND ro.return_type = :returnType)
    )
    AND (:orderNo IS NULL OR ro.order_no = :orderNo)
    AND (:qcStatus IS NULL OR (:qcStatus IN ('Pass', 'Fail') AND roi.qc_status = :qcStatus))
    AND (
            (:agentEmail IS NULL AND ro.agent_email IN ('', '0', 'Unassigned'))
            OR (
                (:agentEmail = 'Unassigned' AND ro.agent_email IN ('', '0', 'Unassigned'))
                OR (:agentEmail = 'Assigned' AND ro.agent_email NOT IN ('', '0', 'Unassigned'))
                OR (ro.agent_email = :agentEmail)
            )
    )
    AND (
            :reportFilterFld1 IS NULL
            OR :reportFilterFld1 = 'POS_revalidation' AND (ro.status = 'return_need_approval' OR ro.status = 'return_need_approval_from_whse')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse' AND (ro.status = 'return_received' AND ro.status = 'reverse')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO' AND (ro.status = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes')
            OR :reportFilterFld1 = 'warehouse_received_-_RTO_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.return_type = 'rto' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'warehouse_received_-_reverse_(Putaway_pending)' AND (ro.status = 'return_received' AND ro.return_type = 'reverse' AND ro.receiving_flag = 'yes' AND roi.status = 'initiated_stockin')
            OR :reportFilterFld1 = 'Return_Follow_Up' AND (ro.status = 'return_under_followup')
    )
    AND (
            :reportFilterFld2 IS NULL
            OR :reportFilterFld2 = 'time' AND (UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(roi.return_create_datetime)) / 3600 <= 12
            OR :reportFilterFld2 = 'assign' AND (ro.agent_email IS NOT NULL AND ro.agent_email != '' AND ro.agent_email != '0' AND ro.agent_email != 'Unassigned')
    )
    AND (:isInsurance IS NULL OR ro.is_insurance = :isInsurance)
    AND (:country IS NULL OR ra.country_id = :country)
    AND (:classification IS NULL OR roi.classification = :classification)
    AND (:franchiseList IS NULL OR roi.is_franchise IN (:franchiseList))
    AND (:returnReasonsPrimary IS NULL OR rdr.primary_reason = :returnReasonsPrimary)
    AND (:returnReasonsSecondary IS NULL OR rdr.secondary_reason = :returnReasonsSecondary)
    AND ((:paymentMethods IS NULL) OR roi.method IN (:paymentMethods))
    AND (:orderType IS NULL
        OR (:orderType = 'bulk' AND ro.bulktype = :orderType)
        OR (:orderType = 'normal' AND ro.bulktype = ''
            AND roi.method NOT IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                                   'purchaseorder', 'storepaytm', 'storeairtel'))
        OR (:orderType = 'franchisecredit-b2b' AND :returnType != 'Cod'
            AND roi.method IN ('franchisecredit', 'storecard', 'storecash', 'storemisc', 'storemomoe',
                               'storepaytm', 'storeairtel')
            AND roi.channel = 'B2B')
        OR (:orderType = 'franchisecredit-franchiseBulk' AND :returnType != 'Cod'
            AND roi.method = 'franchisecredit'
            AND roi.channel = 'FranchiseBulk')
        OR (:orderType = 'purchaseorder' AND :returnType != 'Cod'
            AND roi.method = 'purchaseorder')
    )
    AND (:followedUp IS NULL OR (
        (:followedUp = 'Yes' AND ro.reverse_pu_followup_cnt > 0)
        OR (:followedUp = 'No' AND ro.reverse_pu_followup_cnt = 0)
        OR (:followedUp = '' AND ro.reverse_pu_followup_cnt >= 0)))
    GROUP BY ro.return_id
) AS combinedResults
""", nativeQuery = true)
    Page<Object[]> fetchCombinedReturnOrders(
            @Param("returnStatus") String returnStatus,
            @Param("optDate") String optDate,
            @Param("fromDate") Date fromDate,
            @Param("toDate") Date toDate,
            @Param("returnType") String returnType,
            @Param("orderNo") Integer orderNo,
            @Param("qcStatus") String qcStatus,
            @Param("agentEmail") String agentEmail,
            @Param("orderType") String orderType,
            @Param("returnReasonsPrimary") String returnReasonsPrimary,
            @Param("returnReasonsSecondary") String returnReasonsSecondary,
            @Param("isInsurance") Boolean isInsurance,
            @Param("followedUp") String followedUp,
            @Param("reportFilterFld1") String reportFilterFld1,
            @Param("reportFilterFld2") String reportFilterFld2,
            @Param("eventTypes") List<String> eventTypes,
            @Param("classification") Integer classification,
            @Param("franchiseList") List<String> franchiseList,
            @Param("country") String country,
            @Param("paymentMethods") List<String> paymentMethods,
            Pageable pageable
    );

}
