package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReverseDetailTrackingEvent;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.Date;
import java.util.List;

public interface ReverseTrackingEventRepository extends CrudRepository<ReverseDetailTrackingEvent, Integer> {
    List<ReverseDetailTrackingEvent> findByReturnId(Integer entityId);
    @Query(value = "select r.create_datetime from reverse_tracking_event r join return_order o on o.return_id=r.return_id where o.auto_cancel_flag=1 and o.status in (?1) and r.return_id=?2 and r.reverse_mapped_status in (?3) ",nativeQuery = true)
    List<Date> findPickupFailedByReturnIdAndReverseMappedStatus(List<String> returnStatuses, Integer returnId, List<String> pickupFailedStatuses);
    ReverseDetailTrackingEvent findTop1ByReverseAwb(String trackingNo);
}