package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ExchangeEventsTracker;
import org.springframework.data.repository.CrudRepository;

public interface ExchangeEventTrackerRepository extends CrudRepository<ExchangeEventsTracker, Integer> {
    ExchangeEventsTracker findTopByMasterUwItemId(Integer uwItemId);
    ExchangeEventsTracker findTopByMasterMagentoItemId(Long magentoId);
    ExchangeEventsTracker findTopByMasterUnicomOrderCode(String unicomCode);
    ExchangeEventsTracker findTopByExchangeOrderId(Integer orderId);
}
