package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnReasonsMapping;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface IExchangeReturnReasonRepository extends CrudRepository<ReturnReasonsMapping,Integer> {
    @Query(value = "select * from reasons_mapping as rm where rm.platform=?1 and rm.classification in(select id from classification as cl where cl.value=?2)", nativeQuery = true)
    public List<ReturnReasonsMapping> getReasonsByPlatformAndClassificationVal(String platForm, String classification);

    @Query(value = "select * from reasons_mapping as rm where rm.platform=?1 and rm.classification=?2", nativeQuery = true)
    public List<ReturnReasonsMapping> getReturnReasonsMappingsByPlatformAndClassification(String platForm, Integer classification);
}
