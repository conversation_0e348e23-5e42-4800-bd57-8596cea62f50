package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnRefundRule;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface ReturnRefundRuleRepository extends CrudRepository<ReturnRefundRule, Integer> {
    List<ReturnRefundRule> findByUwItemId(Integer uwItemId);

    ReturnRefundRule findTop1ByUwItemIdAndTriggerPointOrderByCreatedAtDesc(Integer uwItemId, String triggerPoint);

    ReturnRefundRule findTopByUwItemIdAndReturnIdOrderByCreatedAtDesc(Integer uwItemId, Integer returnId);

    ReturnRefundRule findTop1ByUwItemIdOrderByCreatedAtDesc(Integer uwItemId);

    @Query(value = "select * from return_refund_rule where uw_item_id=?1 and return_id=?2 and  (rule_called_from not in (?3) or rule_called_from IS NULL) order by created_at desc limit 1", nativeQuery = true)
    ReturnRefundRule findTop1ByUwItemIdAndReturnIdAndRuleCalledFromNotInOrderByCreatedAtDesc(Integer uwItemId, Integer returnId, List<String> ruleToExclude);

    @Modifying
    @Transactional
    @Query(value = "UPDATE return_refund_rule SET return_id =?2, created_at=Now() WHERE uw_item_id=?3 AND id=?1 AND trigger_point=?4 AND NOT (rule_called_from <=> 'statusUpdateSyncApi')", nativeQuery = true)
    void updateReturnIdForUwItemIdAndId(Integer id, Integer returnId, Integer uwItemId, String triggerPoint);

    List<ReturnRefundRule> findByReturnId(Integer returnId);
    ReturnRefundRule findTop1ByReturnIdAndTriggerPointOrderByCreatedAtDesc(Integer returnId, String triggerPoint);
}
