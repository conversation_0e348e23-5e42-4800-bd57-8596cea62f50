package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.D365ReturnTracking;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;


public interface D365ReturnTrackingRepository extends CrudRepository<D365ReturnTracking, Integer> {
    List<D365ReturnTracking> findByReturnId(Integer returnId);

    @Query(value = "select * from d365_return_tracking d where d.created_at between ?1 and ?2 and d.d365_flag in (?4) and d.retry_count < 2 ORDER BY d.`created_at` desc  limit ?3", nativeQuery = true)
    List<D365ReturnTracking> findDataForReturnRetryForOldData(String fromDate, String toDate, int limit, List<Integer> d365Flag);

    @Query(value = "select * from d365_return_tracking d where d.created_at between ?1 and ?2 and d.pslip_created in (?4) and d.pslip_retry_count<2 ORDER BY d.`created_at` desc  limit ?3", nativeQuery = true)
    List<D365ReturnTracking> findDataForPSlipRetryForOldData(String fromDate, String toDate, int recordsPerExecution, List<Integer> d365Flags);
}
