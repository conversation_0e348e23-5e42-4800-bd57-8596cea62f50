package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReturnHeadingSubheadingMapping;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

public interface ReturnHeadingSubheadingMappingRepository extends CrudRepository<ReturnHeadingSubheadingMapping, Integer> {

    @Query(value = "SELECT r.* FROM return_heading_subheading_mapping r WHERE " +
            "r.return_source = :returnSource AND " +
            "r.return_status = :returnStatus AND " +
            "r.dispatch_point = :dispatchPoint AND " +
            "r.refund_method = :refundMethod AND " +
            "r.refund_mode = :refundMode AND " +
            "r.exchange_expired = :exchangeExpired AND " +
            "r.exchange_created = :exchangeCreated AND " +
            "r.exchange_order_dispatched = :exchangeOrderDispatched AND " +
            "r.refund_status = :refundStatus", nativeQuery = true)
    ReturnHeadingSubheadingMapping findByParams(
            @Param("returnSource") String returnSource,
            @Param("returnStatus") String returnStatus,
            @Param("dispatchPoint") String dispatchPoint,
            @Param("refundMethod") String refundMethod,
            @Param("refundMode") String refundMode,
            @Param("exchangeExpired") Boolean exchangeExpired,
            @Param("exchangeCreated") Boolean exchangeCreated,
            @Param("exchangeOrderDispatched") Boolean exchangeOrderDispatched,
            @Param("refundStatus") String refundStatus
    );

    @Query(value = "SELECT * FROM return_heading_subheading_mapping WHERE return_status = ?2 and refund_status = ?3 and return_source = ?1 and refund_method = ?4 and refund_mode = ?5", nativeQuery = true)
    ReturnHeadingSubheadingMapping getReturnHeadingSubheadingMapping(String returnSource, String returnStatus, String refundStatus, String refundMethod, String refundMode);

}
