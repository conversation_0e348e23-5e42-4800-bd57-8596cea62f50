package com.lenskart.returnrepository.repository;

import com.lenskart.returnrepository.entity.ReversePickupPincode;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;

public interface ReversePickupPincodeRepository extends CrudRepository<ReversePickupPincode,String> {
    @Query(value = "SELECT * from reverse_pickup_pincode WHERE pincode=?1 AND status='Active' AND is_qc_at_doorstep=?4 and courier NOT IN ?2 order by priority Asc limit 1 offset ?3", nativeQuery = true)
    public ReversePickupPincode findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAsc(Integer pincode, List<String> excludedCouriers, int offset, int qcFlag);
    @Query(value = "SELECT * from reverse_pickup_pincode WHERE pincode=?1 AND status='Active' and courier NOT IN ?2 order by priority Asc limit 1 offset ?3", nativeQuery = true)
    public ReversePickupPincode findTopByPincodeAndStatusAndQcDoorStepAndCourierNotInOrderByPriorityAscNoQCCheck(Integer pincode, List<String> excludedCouriers, int offset);

    @Query(value = "SELECT * from reverse_pickup_pincode WHERE pincode=?1 AND status='Active' and courier=?2 order by priority Asc limit 1", nativeQuery = true)
    public ReversePickupPincode findTopByPincodeAndStatusAndCourierOrderByPriorityAsc(Integer pincode, String courier);
    ReversePickupPincode findTopByCourier(String everseCourier);
    @Query(value = "SELECT * from reverse_pickup_pincode WHERE pincode=?1 AND status='Active' and courier='LKart'",nativeQuery = true)
    List<ReversePickupPincode> findActiveLKartCouriers(int pincode);
    @Query(value = "SELECT * from reverse_pickup_pincode WHERE pincode=?1 AND status='Active' order by priority Asc limit 2",nativeQuery = true)
    List<ReversePickupPincode> findActiveReversePickupCouriers(int pincode);

    @Query(value = "select distinct courier from reverse_pickup_pincode order by courier", nativeQuery = true)
    List<String> getReversePickupPincodeCourier();

    Optional<ReversePickupPincode> findByCourierAndPincode(String courier, Integer pincode);

}
