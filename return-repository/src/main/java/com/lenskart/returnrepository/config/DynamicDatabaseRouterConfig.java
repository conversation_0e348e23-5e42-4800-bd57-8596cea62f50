package com.lenskart.returnrepository.config;

import com.lenskart.returnrepository.datasource.DynamicRoutingDataSource;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * keeping spring.jpa.open-in-view: false
 * Setting spring.jpa.open-in-view to false can be a valid approach when working with multiple repositories and switching data sources at runtime, especially in a legacy codebase.
 * It can help manage resources more efficiently by closing the session after each transaction, preventing resource leaks.
 * Now Spring Boot does not automatically manage the JPA session and transaction for you.
 * You would need to explicitly open and close transactions as needed in your service layer or controllers.
 * This can be more efficient in terms of database resource usage but may require careful management of transactions to avoid issues like LazyInitializationExceptions.
 */
@Slf4j
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = {"com.lenskart.returnrepository.repository","com.lenskart.platform.fl.utils.jpa.repository"}, entityManagerFactoryRef = "entityManager")
public class DynamicDatabaseRouterConfig {

    public static final String PROPERTY_PREFIX = "spring.datasource.";
    public static final String HIKARI = "hikari.";


    @Autowired
    Environment environment;

    @Bean
    @Primary
    public DataSource dataSource() {
        Map<Object, Object> targetDataSources = getTargetDataSources();
        return new DynamicRoutingDataSource((DataSource) targetDataSources.get("default"), targetDataSources);
    }

    @Bean(name = "entityManager")
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryBean(EntityManagerFactoryBuilder builder) {
        return builder.dataSource(dataSource()).packages("com.lenskart.returnrepository.entity", "com.lenskart.platform.fl.utils.jpa.entity").build();
    }

    private Map<Object, Object> getTargetDataSources() {
        List<String> databaseNames = environment.getProperty("spring.database-names.list", List.class);
        log.info("databaseNames  :{}", databaseNames);
        Map<Object, Object> targetDataSourceMap = new HashMap<>();
        for (String dbName : databaseNames) {
            DataSourceProperties dataSourceProperties = new DataSourceProperties();
            dataSourceProperties.setDriverClassName(getDbProperty(dbName, "driverClassName"));
            dataSourceProperties.setUrl(getDbProperty(dbName, "url"));
            dataSourceProperties.setUsername(getDbProperty(dbName, "username"));
            dataSourceProperties.setPassword(getDbProperty(dbName, "password"));

            HikariDataSource dataSource = dataSourceProperties
                    .initializeDataSourceBuilder()
                    .type(HikariDataSource.class)
                    .build();

            String hikariMaxLifeTime = getDbProperty(dbName, HIKARI + "maxLifeTime");
            if (!StringUtils.isBlank(hikariMaxLifeTime)) {
                dataSource.setMaxLifetime(Long.parseLong(hikariMaxLifeTime));
            }

            String maximumPoolSize = getDbProperty(dbName, HIKARI + "maximumPoolSize");
            if (!StringUtils.isBlank(maximumPoolSize)) {
                dataSource.setMaximumPoolSize(Integer.parseInt(maximumPoolSize));
            }

            String idleTimeout = getDbProperty(dbName, HIKARI + "idleTimeout");
            if (!StringUtils.isBlank(idleTimeout)) {
                dataSource.setIdleTimeout(Long.parseLong(idleTimeout));
            }

            String connectionTimeout = getDbProperty(dbName, HIKARI + "connectionTimeout");
            if (!StringUtils.isBlank(connectionTimeout)) {
                dataSource.setConnectionTimeout(Long.parseLong(connectionTimeout));
            }

            String minimumIdle = getDbProperty(dbName, HIKARI + "minimumIdle");
            if (!StringUtils.isBlank(minimumIdle)) {
                dataSource.setMinimumIdle(Integer.parseInt(minimumIdle));
            }

            String validationTimeout = getDbProperty(dbName, HIKARI + "validationTimeout");
            if (!StringUtils.isBlank(validationTimeout)) {
                dataSource.setValidationTimeout(Long.parseLong(validationTimeout));
            }

            String registerMbeans = getDbProperty(dbName, HIKARI + "registerMbeans");
            if (!StringUtils.isBlank(registerMbeans)) {
                dataSource.setRegisterMbeans(Boolean.parseBoolean(registerMbeans));
            }

            targetDataSourceMap.put(dbName, dataSource);
        }
        targetDataSourceMap.put("default", targetDataSourceMap.get(databaseNames.get(0)));
        return targetDataSourceMap;
    }

    private String getDbProperty(String dbName, String propertyName) {
        try {
            return environment.getProperty(PROPERTY_PREFIX + dbName + "." + propertyName);
        } catch (Exception e) {
            return null;
        }
    }
}
