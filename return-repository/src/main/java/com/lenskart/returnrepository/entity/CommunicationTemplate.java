package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Data
@Entity
@Table(name = "communication_template")
public class CommunicationTemplate implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "event_type", nullable = false)
    private String eventType;

    @Column(name = "conditions", nullable = false)
    private String conditions;

    @Column(name = "template_name", nullable = false)
    private String templateName; //map with template_name in sms_template_new from inventory db

    @Column(name = "payload_name", nullable = false)
    private String payloadName;

    @Transient
    private Map<String, String> conditionsMap;

    @Transient
    public boolean conditionsMet(Map<String, String> contextMap) {
        conditionsMap = getConditionsMap();
        if (conditionsMap == null) {
            return false;
        } else if (conditionsMap.isEmpty() && CollectionUtils.isEmpty(contextMap)) {
            return true;
        } else if (conditionsMap.isEmpty() || CollectionUtils.isEmpty(contextMap)) {
            return false;
        }
        for (Map.Entry<String, String> entry : conditionsMap.entrySet()) {
            if (entry != null && !CollectionUtils.isEmpty(contextMap) && entry.getKey() != null) {
                String condition = contextMap.getOrDefault(entry.getKey(), null);
                if (condition == null || !condition.equalsIgnoreCase(entry.getValue())) {
                    return false; // Conditions not met
                }
            } else {
                return false;
            }
        }
        return true; // All conditions met
    }

    @Transient
    public Map<String, String> getConditionsMap() {
        if (conditions == null || conditions.isEmpty()) {
            return Collections.emptyMap();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(conditions, new TypeReference<Map<String, String>>() {
            });
        } catch (IOException e) {
            log.error("Exception occurred while getting conditionsMap, error:{}", e.getMessage());
            return null;
        }
    }
}
