package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

@Entity
@Table(name = "return_detail_item")
@Data
public class ReturnDetailItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer returnId;
    private Integer uwItemId;
    private Integer itemId;
    private Long productId;
    private String status;
    private String qcStatus;
    private String qcComment;
    private Date returnCreateDatetime;
    private String isFranchise;
    private Integer classification;
    private String productDeliveryType;
    private String method;
    private String channel;
    private String reasonForReturn;
    private String qcFailReason;
    private String tbybPrescription;
    private int itemSelectedFlag;
    private Integer csohUpdatedFlag = 0;
    @UpdateTimestamp
    private Date updatedAt;

}