package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(
        name = "return_reason"
)
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReturnReason implements Serializable {
    private static final long serialVersionUID = -6552830870569270528L;
    @Id
    @Column(
            name = "returnreasonid",
            unique = true,
            nullable = false
    )
    @JsonProperty("returnReasonId")
    private Integer returnReasonId;
    @Column(
            name = "order_id",
            nullable = false
    )
    private Integer orderId;
    @Column(
            name = "uw_item_id",
            nullable = false
    )
    private Integer uwItemId;
    @Column(
            name = "source",
            nullable = false,
            columnDefinition = "enum('reverselabel','reversepickuprequest','followuppanel','awaited_rto')"
    )
    private String source;
    @Column(
            name = "primary_reason",
            nullable = false
    )
    private String primaryReason;
    @Column(
            name = "secondary_reason",
            nullable = false
    )
    private String secondaryReason;
    @Column(
            name = "user",
            nullable = false
    )
    private String user;
    @Column(
            name = "created_at",
            nullable = false
    )
    private Date createdAt;
    @Column(
            name = "return_id",
            nullable = false
    )
    private Integer returnId;
    @Column(
            name = "type"
    )
    private String type;
}
