package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "return_event")
@Data
public class ReturnEvent implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer returnRequestId;
    private Integer returnId;
    private String event;
    private String remarks;
    private Date createdAt;
    private String source;
}