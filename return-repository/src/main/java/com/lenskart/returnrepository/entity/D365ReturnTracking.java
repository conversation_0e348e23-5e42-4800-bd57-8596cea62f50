package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;


import java.util.Date;

@Data
@Entity
@Table(
        name = "d365_return_tracking"
)
public class D365ReturnTracking {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "return_id")
    private Integer returnId;
    @Column(name = "increment_id")
    private Integer incrementId;
    @Column(name = "old_status")
    private String oldStatus;
    @Column(name = "new_status")
    private String newStatus;
    @Column(name = "source")
    private String source;
    @Column(name = "created_at")
    private Date createdAt;
    @Column(name = "updated_at")
    private Date updatedAt;
    @Column(name = "pslip_updated_at")
    private Date pslipRetryUpdatedAt;
    @Column(name = "return_updated_at")
    private Date returnRetryUpdatedAt;
    @Column(name = "d365_flag")
    private Integer d365Flag;
    @Column(name = "retry_count")
    private Integer retryCount;
    @Column(name = "pslip_created")
    private Integer pslipCreated;
    @Column(name = "pslip_retry_count")
    private Integer pSlipRetryCount;
    @Column(name = "tjournal_created")
    private Integer tjournalCreated;
    @Column(name = "return_sync_message")
    private String returnSyncMessage;
    @Column(name = "pslip_sync_message")
    private String pslipSyncMessage;
    @Column(name = "mov_journal_flag")
    private Integer movJournalFlag;
}
