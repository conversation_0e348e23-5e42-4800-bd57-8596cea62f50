package com.lenskart.returnrepository.entity;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "store_appointment_orders")
public class StoreAppointmentOrders {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    @Column(name = "id", nullable = false, unique = true)
    private Integer id;

    @JsonProperty("order_id")
    @Column(name = "order_id", nullable = false)
    private Integer orderId;

    @JsonProperty("identifier_type")
    @Column(name = "identifier_type", nullable = false)
    private String identifierType;

    @JsonProperty("identifier_value")
    @Column(name = "identifier_value", nullable = false)
    private Integer identifierValue;

    @JsonProperty("source")
    @Column(name = "source", nullable = false)
    private String source;


    @JsonProperty("created_at")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = true)
    private Date createdAt;

    @JsonProperty("rule_response")
    @Column(name = "rule_response", nullable = false)
    private String returnRefundRuleResponse;
}
