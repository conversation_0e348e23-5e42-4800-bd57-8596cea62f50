package com.lenskart.returnrepository.entity;

import lombok.Data;
import lombok.ToString;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Data
@ToString
@Table(name = "return_heading_subheading_mapping")
public class ReturnHeadingSubheadingMapping{
    @Column(
            name = "id",
            nullable = false,
            unique = true
    )
    @Id
    private Integer id;

    @Column(name = "return_source")
    private String returnSource;

    @Column(name = "return_status")
    private String returnStatus;

    @Column(name = "refund_status")
    private String refundStatus = "na";

    @Column(name = "refund_method")
    private String refundMethod="na";

    @Column(name = "refund_mode")
    private String refundMode="na";

    @Column(name = "exchange_created")
    private Boolean exchangeCreated=false;

    @Column(name = "exchange_expired")
    private Boolean exchangeExpired=false;

    @Column(name = "exchange_order_dispatched")
    private Boolean exchangeOrderDispatched=false;

    @Column(name = "dispatch_point")
    private String dispatchPoint="AnyReceivingPoint";

    @Column(name = "top_header")
    private String topHeader;

    @Column(name = "top_sub_header")
    private String topSubHeader;

    @Column(name = "created_at")
    private Date createdAt;


}
