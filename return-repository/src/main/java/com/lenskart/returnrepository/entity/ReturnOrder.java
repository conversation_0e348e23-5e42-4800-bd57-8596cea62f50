package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lenskart.returncommon.utils.UnixTimestampToDateDeserializer;
import jakarta.persistence.*;

import lombok.Data;
import org.jsondoc.core.annotation.ApiObject;

@Data
@Entity
@Table(
        name = "return_order"
)
@ApiObject
public class ReturnOrder {
    @Id
    @JsonProperty("return_id")
    @Column(
            name = "return_id",
            nullable = false,
            unique = true
    )
    private Integer returnId;
    @JsonProperty("order_no")
    @Column(
            name = "order_no",
            nullable = false
    )
    private Integer orderNo;
    @JsonProperty("return_type")
    @Column(
            name = "return_type",
            nullable = true,
            columnDefinition = "enum('reverse', 'rto')"
    )
    private String returnType;
    @JsonProperty("courier_flag")
    @Column(
            name = "courier_flag",
            nullable = false
    )
    private int courierFlag;
    @JsonProperty("courier_amount")
    @Column(
            name = "courier_amount",
            nullable = true
    )
    private Double courierAmount;
    @JsonProperty("reverse_courier")
    @Column(
            name = "reverse_courier",
            nullable = true
    )
    private String reverseCourier;
    @JsonProperty("reverse_awb")
    @Column(
            name = "reverse_awb",
            nullable = true
    )
    private String reverseAwb;
    @JsonProperty("reverse_pickup_reference_id")
    @Column(
            name = "reverse_pickup_reference_id",
            nullable = true
    )
    private String reversePickupReferenceId;
    @JsonProperty("reverse_pickup_flag")
    @Column(
            name = "reverse_pickup_flag",
            nullable = true
    )
    private int reversePickupFlag;
    @JsonProperty("return_form_received")
    @Column(
            name = "return_form_received",
            nullable = true
    )
    private int returnFormReceived;
    @JsonProperty("return_create_datetime")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(
            name = "return_create_datetime",
            nullable = true
    )
    private Date returnCreateDatetime;
    @JsonProperty("status")
    @Column(
            name = "status",
            nullable = true,
            columnDefinition = "enum('return_received','return_under_followup','return_followed_up','return_override','return_pending_approval','return_refunded','return_reship','new_reverse_pickup','reference_id_issued','awb_assigned','cancelled','return_closed','sc_refunded_receiving_pending','rto_cod_stockin','return_refund_rejected','return_exchange','user_cancel_pending','cancel_pending','easy_refund_given_pickup_cancelled','partial_easy_refund_given_pickup_cancelled','vendor_sync_pending','auto_sync_failed','customer_cancelled','return_partial_refund') default 'return_received' "
    )
    private String status;
    @JsonProperty("items_refund_amount")
    @Column(
            name = "items_refund_amount",
            nullable = false
    )
    private Double itemsRefundAmount;
    @JsonProperty("courier_refund_amount")
    @Column(
            name = "courier_refund_amount",
            nullable = false
    )
    private Double courierRefundAmount;
    @JsonProperty("emi_refund_amount")
    @Column(
            name = "emi_refund_amount",
            nullable = false
    )
    private Double emiRefundAmount;
    @JsonProperty("shipping_refund_amount")
    @Column(
            name = "shipping_refund_amount",
            nullable = false
    )
    private Double shippingRefundAmount;
    @JsonProperty("total_refund_amount")
    @Column(
            name = "total_refund_amount",
            nullable = false
    )
    private Double totalRefundAmount;
    @JsonProperty("qc_status")
    @Column(
            name = "qc_status",
            nullable = true,
            columnDefinition = "enum('Pass','Fail')"
    )
    private String qcStatus;
    @JsonProperty("override_comment")
    @Column(
            name = "override_comment",
            nullable = true
    )
    private String overrideComment;
    @JsonProperty("reverse_pu_followup_cnt")
    @Column(
            name = "reverse_pu_followup_cnt",
            nullable = false
    )
    private Integer reversePuFollowupCnt;
    @JsonProperty("receiving_flag")
    @Column(
            name = "receiving_flag",
            nullable = true,
            columnDefinition = "enum('yes','no')"
    )
    private String receivingFlag;
    @JsonProperty("bulkType")
    @Column(
            name = "bulktype",
            nullable = true,
            columnDefinition = "enum('bulk','')"
    )
    private String bulkType;
    @JsonProperty("agent_email")
    @Column(
            name = "agent_email",
            nullable = false
    )
    private String agentEmail;
    @JsonProperty("easy_refund_given")
    @Column(
            name = "easy_refund_given",
            nullable = false
    )
    private String easyRefundGiven;
    @JsonProperty("easy_refund_not_given_reason")
    @Column(
            name = "easy_refund_not_given_reason",
            nullable = false
    )
    private String easyRefundNotGivenReason;
    @JsonProperty("next_followup_datetime")
    @Column(
            name = "next_followup_datetime",
            nullable = false
    )
    private Date nextFollowupDatetime;
    @JsonProperty("last_followup_datetime")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(
            name = "last_followup_datetime",
            nullable = false
    )
    private Date lastFollowupDatetime;
    @JsonProperty("unicom_ordercode")
    @Column(
            name = "unicom_ordercode",
            nullable = true
    )
    private String unicomOrderCode;
    @JsonProperty("last_update_datetime")
    @Column(
            name = "last_update_datetime",
            nullable = true
    )
    private Date lastUpdateDatetime;
    @JsonProperty("source")
    @Column(
            name = "source",
            nullable = true
    )
    private String source;
    @JsonProperty("group_id")
    @Column(
            name = "group_id",
            nullable = true
    )
    private Long groupId;
    @JsonProperty("return_method")
    @Column(
            name = "return_method",
            nullable = true
    )
    private String returnMethod;
    @JsonProperty("facility_code")
    @Column(
            name = "facility_code",
            nullable = true
    )
    private String facilityCode;
    @JsonProperty("new_flow_flag")
    @Column(
            name = "new_flow_flag",
            nullable = true
    )
    private Integer NewFlowFlag = 0;
    @JsonProperty("return_label_generated")
    @Column(
            name = "return_label_generated",
            nullable = true
    )
    private boolean returnLabelGenerated = false;
    @JsonProperty("is_insurance")
    @Column(
            name = "is_insurance",
            nullable = true
    )
    private Boolean isInsurance;
    @JsonProperty("pickup_estimate")
    @Column(
            name = "pickup_estimate",
            nullable = true
    )
    private Date pickupEstimate;
    @JsonProperty("dto_centre")
    @Column(
            name = "dto_centre",
            nullable = true
    )
    private String dtoCentre;
    @JsonProperty("is_qc_at_doorstep")
    @Column(
            name = "is_qc_at_doorstep"
    )
    private Integer isQcAtDoorstep;
//    @JsonProperty("isCourierReassigned")
//    @Column(
//            name = "isCourierReassigned",
//            nullable = true
//    )
//    private Boolean isCourierReassigned;
    @JsonProperty("auto_cancel_flag")
    @Column(
            name = "auto_cancel_flag",
            nullable = true
    )
    private Boolean isAutoCancelEnable;
}