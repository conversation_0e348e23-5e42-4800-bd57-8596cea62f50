package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

@Entity
@Table(name = "return_refund_rule")
@Data
public class ReturnRefundRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String ruleId;
    private Integer uwItemId;
    private Integer returnId;
    @CreationTimestamp
    private Date createdAt;
    @UpdateTimestamp
    private Date updatedAt;
    private String request;
    private String triggerPoint;
    private String decisionValues;
    private String returnRefundRuleResponse;
    private String ruleCalledFrom;
    private String returnRefundRuleOverriddenResponse;
}
