package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "claim_decision_rules")
public class ClaimDecisionRule {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "claim_type")
    private String claimType;

    @Column(name = "same_item_return_limit")
    private Integer sameItemReturnLimit;

    @Column(name = "store_callback_requested")
    private Boolean storeCallbackRequested;

    @Column(name = "image_has_eyeglass")
    private Boolean imageHasEyeglass;

    @Column(name = "original_image_matched")
    private Boolean originalImageMatched;

    @Column(name = "customer_type")
    private String customerType;

    @Column(name = "warranty_period_in_days")
    private String warrantyPeriodInDays;

    @Column(name = "category")
    private String category;

    @Column(name = "frame_material")
    private String frameMaterial;

    @Column(name = "frame_type")
    private String frameType;

    @Column(name = "expected_condition_key")
    private String expectedConditionKey;

    @Column(name = "expected_condition_value")
    private String expectedConditionValue;

    @Column(name = "decision")
    private String decision;

    @Column(name = "priority")
    private Integer priority;
}