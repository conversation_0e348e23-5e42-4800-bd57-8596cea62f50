package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Data;

import java.sql.Timestamp;

@Entity
@Table(
        name = "return_order_address_update"
)
@Data
public class ReturnOrderAddressUpdate {
    @Id
    @JsonProperty("id")
    @Column(
            name = "id",
            nullable = false,
            unique = true
    )
    Integer id;
    @JsonProperty("increment_id")
    @Column(
            name = "increment_id",
            nullable = false,
            unique = true
    )
    Integer increment_id;
    @JsonProperty("group_id")
    @Column(
            name = "group_id",
            nullable = false
    )
    Integer groupId;
    @JsonProperty("firstname")
    @Column(
            name = "firstname",
            nullable = false
    )
    String firstName;
    @JsonProperty("lastname")
    @Column(
            name = "lastname",
            nullable = false
    )
    String lastName;
    @JsonProperty("street1")
    @Column(
            name = "street1",
            nullable = false
    )
    String street1;
    @JsonProperty("street2")
    @Column(
            name = "street2",
            nullable = false
    )
    String street2;
    @JsonProperty("city")
    @Column(
            name = "city",
            nullable = false
    )
    String city;
    @JsonProperty("email")
    @Column(
            name = "email",
            nullable = false
    )
    String email;
    @JsonProperty("telephone")
    @Column(
            name = "telephone",
            nullable = false
    )
    String telephone;
    @JsonProperty("state")
    @Column(
            name = "state",
            nullable = false
    )
    String state;
    @JsonProperty("postcode")
    @Column(
            name = "postcode",
            nullable = false
    )
    Integer postcode;
    @JsonProperty("country_id")
    @Column(
            name = "country_id",
            nullable = false
    )
    String country;
    @JsonProperty("created_at")
    @Column(
            name = "created_at",
            columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    )
    Timestamp createdAt;
}
