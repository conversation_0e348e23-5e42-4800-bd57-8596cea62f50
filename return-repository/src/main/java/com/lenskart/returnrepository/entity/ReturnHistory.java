package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "return_history")
@Data
public class ReturnHistory {

    @Id
    @JsonProperty("id")
    @Column(name = "id", nullable = false, unique = true)
    private Integer id;

    @JsonProperty("entity_id")
    @Column(name = "entity_id", nullable = false)
    private Integer entityId;

    @JsonProperty("entity_type")
    @Column(name = "entity_type", nullable = true, columnDefinition = "enum('return_order', 'return_order_item', 'refund')")
    private String entityType;

    public interface ENTITY_TYPE {
        String RETURN_ORDER = "return_order";
        String RETURN_ORDER_ITEM = "return_order_item";
        String REFUND = "refund";
    }

    @JsonProperty("comment")
    @Column(name = "comment", nullable = true)
    private String comment;

    @JsonProperty("added_by")
    @Column(name = "added_by", nullable = false)
    private Integer addedBy;

    @JsonProperty("current_status")//'return_received','return_under_followup','return_followed_up','return_override','return_pending_approval','return_refunded','return_reship','refund_pending','refund_cheque_created','refund_cheque_sent','refund_complete','new_reverse_pickup','reference_id_issued','awb_assigned','cancelled','return_closed','sc_refunded_receiving_pending','initiated_return_reship','initiated_stockin','unicom_return_reship','unicom_stockin','awaiting_neft_info','return_refund_rejected'
    @Column(name = "current_status", nullable = true, columnDefinition = "enum('return_received','return_under_followup','return_followed_up','return_override','return_pending_approval','return_refunded','return_reship','refund_pending','refund_cheque_created','refund_cheque_sent','refund_complete','new_reverse_pickup','reference_id_issued','awb_assigned','cancelled','return_closed','sc_refunded_receiving_pending','initiated_return_reship','initiated_stockin','unicom_return_reship','unicom_stockin','awaiting_neft_info','return_refund_rejected','return_received_action_pending','pickup_regd_failed')")
    private String currentStatus;

    public interface CURRENT_STATUS {
        String RETURN_RECEIVED = "return_received";
        String RETURN_RECEIVED_ACTION_PENDING = "return_received_action_pending";
    }

    @JsonProperty("source")
    @Column(name = "source", nullable = true)
    private String source;

    @JsonProperty("courier")
    @Column(name = "courier", nullable = true)
    private String courier;

    @JsonProperty("reverse_pickup_reference_id")
    @Column(name = "reverse_pickup_reference_id", nullable = true)
    private String reversePickupReferenceId;


    @JsonProperty("reverse_awb")
    @Column(name = "reverse_awb", nullable = true)
    private String reverseAwb;

    @JsonProperty("created_at")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = true)
    private Date createdAt;
}

