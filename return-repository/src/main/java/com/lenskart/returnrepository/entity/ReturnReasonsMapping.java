package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(
        name = "reasons_mapping"
)
@Data
public class ReturnReasonsMapping {
    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    @Column(
            name = "id",
            unique = true,
            nullable = false
    )
    private Integer id;

    @Column(
            name = "platform"
    )
    private String platform;

    @Column(
            name = "classification"
    )
    private Integer classificationId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name="primary_reason_id")
    private PrimaryReturnReasons primaryReturnReasons;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name="secondary_reason_id")
    private SecondaryReturnReason secondaryReturnReasons;

    @Column(
            name = "create_time"
    )
    private Date createdAt;

    @Column(
            name = "is_insurance_reason"
    )
    private Boolean isInsuranceReason;
}
