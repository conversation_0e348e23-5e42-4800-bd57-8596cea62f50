package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "reverse_detail_tracking_event")
public class ReverseDetailTrackingEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "return_id", nullable = false)
    private Integer returnId;
    @Column(name = "order_id", nullable = false)
    private Integer orderId;
    @Column(name = "reverse_awb", nullable = false)
    private String reverseAwb;
    @Column(name = "tracking_remark", nullable = false)
    private String trackingRemark;
    @Column(name = "tracking_status_code", nullable = false)
    private Integer trackingStatusCode;
    @Column(name = "pickup_attempted")
    private String pickupAttempted;
    @Column(name = "non_pickup_reason")
    private String nonPickupReason;
    @Column(name = "reverse_mapped_status")
    private String reverseMappedStatus;
    @Column(name = "event_date_time")
    private Date eventDateTime;
    @Column(name = "pickup_date_time")
    private Date pickupDateTime;
    @Column(name = "create_datetime")
    private Date createDatetime;
    @Column(name = "notification_event_id")
    private Integer notificationEventId;
}
