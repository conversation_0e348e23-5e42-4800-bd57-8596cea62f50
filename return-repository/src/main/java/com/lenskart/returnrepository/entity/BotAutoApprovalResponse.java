package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

@Entity
@Table(name = "ai_model_approval_response")
@Data
public class BotAutoApprovalResponse {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "item_id")
    private String itemId;

    @Column(name = "reason_for_return")
    private String reasonForReturn;

    @Column(name = "magento_id")
    private String magentoId;

    @Column(name = "product_id")
    private String productId;

    @Column(name = "delight_action")
    private String delightAction;

    @Column(name = "user_comment")
    private String userComment;

    @Column(name = "actual_image")
    private String actualImage;

    @Column(name = "customer_image")
    private String customerImage;

    @Column(name = "meta_data")
    private String metaData;

    @Column(name = "prompt")
    private String prompt;

    @Column(name = "model_result")
    private String modelResult;

    @Column(name = "model_decision")
    private String modelDecision;

    @Column(name = "model_name")
    private String modelName;

    @CreationTimestamp
    @Column(name = "created_at")
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private Date updatedAt;
}

