package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(
        name = "approval_reasons"
)
@Data
public class ApprovalReasons implements Serializable {

    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    @Column(
            name = "id",
            unique = true,
            nullable = false
    )
    private Integer id;

    @Column(
            name = "reason"
    )
    private String reason;


    @Column(
            name = "create_time"
    )
    private Date createdTime;

    @Column(
            name = "update_time"
    )
    private Date updateTime;
}
