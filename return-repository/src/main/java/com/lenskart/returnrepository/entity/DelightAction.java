package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(
        name = "delight_action"
)
@Data
public class DelightAction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer returnId;
    private String delightAction;
    private String delightMethod;
    private Date createdAt;
    private String comments;
    private String warehouseComments;
}
