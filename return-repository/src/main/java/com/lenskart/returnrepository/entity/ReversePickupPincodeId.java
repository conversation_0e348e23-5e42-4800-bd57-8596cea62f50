package com.lenskart.returnrepository.entity;

import java.io.Serializable;
import java.util.Objects;

public class ReversePickupPincodeId implements Serializable {
    private String courier;
    private Integer pincode;

    // default constructor
    public ReversePickupPincodeId() {}

    public ReversePickupPincodeId(String courier, Integer pincode) {
        this.courier = courier;
        this.pincode = pincode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ReversePickupPincodeId)) return false;
        ReversePickupPincodeId that = (ReversePickupPincodeId) o;
        return Objects.equals(courier, that.courier) &&
                Objects.equals(pincode, that.pincode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(courier, pincode);
    }
}
