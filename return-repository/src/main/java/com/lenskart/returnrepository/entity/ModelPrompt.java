package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "model_prompt")
@Data
public class ModelPrompt {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "model_name", nullable = false)
    private String modelName;

    @Column(name = "prompt", nullable = false, columnDefinition = "TEXT")
    private String prompt;

    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;

}