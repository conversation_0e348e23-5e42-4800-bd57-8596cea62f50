package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(
        name = "qc_and_warranty_rule"
)
@Data
public class QcAndWarrantyRule implements Serializable {
    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    private Long id;
    @Column(
            name = "rule_type"
    )
    private String ruleType;
    @Column(
            name = "refund_intent"
    )
    private Boolean refundIntent;
    @Column(
            name = "item_value_from"
    )
    private Boolean itemValueFrom;
    @Column(
            name = "item_value_to"
    )
    private Boolean itemValueTo;
    @Column(
            name = "is_branded"
    )
    private Boolean isBranded;
    @Column(
            name = "is_blacklisted"
    )
    private Boolean isBlacklisted;
    @Column(
            name = "from_days"
    )
    private Boolean fromDays;
    @Column(
            name = "to_days"
    )
    private Boolean toDays;
    @Column(
            name = "customer_profiling_score_from"
    )
    private Boolean customerProfilingScoreFrom;
    @Column(
            name = "customer_profiling_score_to"
    )
    private Boolean customerProfilingScoreTo;
    @Column(
            name = "return_count_greater_than"
    )
    private Boolean returnCountGreaterThan;
    @Column(
            name = "return_percentage"
    )
    private Boolean returnPercentage;
    @Column(
            name = "refund_dispatch_point"
    )
    private Boolean refundDispatchPoint;
}
