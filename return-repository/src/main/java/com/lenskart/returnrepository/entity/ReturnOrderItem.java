package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import jakarta.persistence.*;

@Data
@Entity
@Table(name = "return_order_item")
public class ReturnOrderItem {
    @Id
    @JsonProperty("id")
    @Column(
            name = "id",
            nullable = false,
            unique = true
    )
    private Integer id;
    @JsonProperty("return_id")
    @Column(
            name = "return_id",
            nullable = true
    )
    private Integer returnId;
    @JsonProperty("return_type")
    @Column(
            name = "return_type",
            nullable = false,
            columnDefinition = "enum('reverse', 'rto')"
    )
    private String returnType;
    @JsonProperty("item_id")
    @Column(
            name = "item_id",
            nullable = false
    )
    private Integer itemId;
    @JsonProperty("uw_item_id")
    @Column(
            name = "uw_item_id",
            nullable = true
    )
    private Integer uwItemId;
    @JsonProperty("barcode_id")
    @Column(
            name = "barcode_id",
            nullable = false
    )
    private Integer barcodeId = 0;
    @JsonProperty("product_id")
    @Column(
            name = "product_id",
            nullable = false
    )
    private Integer productId = 0;
    @JsonProperty("qc_status")
    @Column(
            name = "qc_status",
            nullable = false
    )
    private String qcStatus = " ";
    @JsonProperty("qc_fail_reason")
    @Column(
            name = "qc_fail_reason",
            nullable = true
    )
    private String qcFailReason;
    @JsonProperty("qc_comment")
    @Column(
            name = "qc_comment",
            nullable = false
    )
    private String qcComment = " ";
    @JsonProperty("reason_for_return")
    @Column(
            name = "reason_for_return",
            nullable = false
    )
    private String reasonForReturn = " ";
    @JsonProperty("refund_amount")
    @Column(
            name = "refund_amount",
            nullable = false
    )
    private Integer refundAmount = 0;
    @JsonProperty("store_cr_amount")
    @Column(
            name = "store_cr_amount",
            nullable = true
    )
    private Integer storeCrAmount;
    @Column(
            name = "status",
            nullable = false,
            columnDefinition = "enum('return_received','return_under_followup','return_followed_up','return_override','return_pending_approval','return_refunded','return_reship','new_reverse_pickup','reference_id_issued','awb_assigned','cancelled','return_closed','sc_refunded_receiving_pending','rto_cod_stockin','return_refund_rejected','return_exchange','user_cancel_pending','cancel_pending','easy_refund_given_pickup_cancelled','partial_easy_refund_given_pickup_cancelled','vendor_sync_pending','auto_sync_failed','customer_cancelled') default 'return_received' "
    )
    private String status = " ";
    @JsonProperty("csoh_updated_flag")
    @Column(
            name = "csoh_updated_flag",
            nullable = false
    )
    private Integer csohUpdatedFlag = 0;
    @JsonProperty("return_created_by_uid")
    @Column(
            name = "return_created_by_uid",
            nullable = false
    )
    private Integer returnCreatedByUid;
    @JsonProperty("return_create_datetime")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(
            name = "return_create_datetime",
            nullable = false
    )
    private Date returnCreateDatetime;
    @JsonProperty("action_datetime")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(
            name = "action_datetime",
            nullable = true
    )
    private Date actionDatetime;
    @JsonProperty("item_selected_flag")
    @Column(
            name = "item_selected_flag",
            nullable = true
    )
    private int itemSelectedFlag;
    @JsonProperty("tbyb_prescription")
    @Column(
            name = "tbyb_prescription",
            nullable = true
    )
    private String tbybPrescription;
    @JsonProperty("shelf")
    @Column(
            name = "shelf",
            nullable = true
    )
    private String shelf;
    @JsonProperty("logistics_action")
    @Column(
            name = "logistics_action",
            nullable = true
    )
    private String logisticsAction;
    @JsonProperty("is_franchise")
    @Column(
            name = "is_franchise",
            nullable = true
    )
    private String isFranchise;
    @JsonProperty("method")
    @Column(
            name = "method",
            nullable = true
    )
    private String method;
    @JsonProperty("channel")
    @Column(
            name = "channel",
            nullable = true
    )
    private String channel;
    @JsonProperty("product_delivery_type")
    @Column(
            name = "product_delivery_type",
            nullable = true
    )
    private String productDeliveryType;
    @JsonProperty("classification")
    @Column(
            name = "classification",
            nullable = true
    )
    private Integer classification;

}