package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "return_courier_detail")
@Data
public class ReturnCourierDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer returnId;
    private Date createdAt;
    private String reverseCourier;
    private String reverseAwbNumber;
    private String reversePickupReferenceId;
    private Date pickupEstimate;
}
