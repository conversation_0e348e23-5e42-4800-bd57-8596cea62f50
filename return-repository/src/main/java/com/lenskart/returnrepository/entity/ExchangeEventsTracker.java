package com.lenskart.returnrepository.entity;

import com.lenskart.returncommon.model.enums.EventStatus;
import com.lenskart.returncommon.model.enums.EventType;
import lombok.Data;
import jakarta.persistence.*;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "exchange_event_tracker")
public class ExchangeEventsTracker {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private EventType type;

    @Column(name = "exchange_order_id", nullable = false)
    private Long exchangeOrderId;

    @Column(name = "master_magento_item_id", nullable = false)
    private Long masterMagentoItemId;

    @Column(name = "master_uw_item_id", nullable = false)
    private Integer masterUwItemId;

    @Column(name = "master_unicom_order_code", nullable = false, length = 100)
    private String masterUnicomOrderCode;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private EventStatus status;

    @Column(name = "created_at", nullable = false)
    private LocalDate createdAt;
}
