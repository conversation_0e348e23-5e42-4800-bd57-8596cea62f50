package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "secondry_return_reason")
@Data
public class SecondaryReturnReason {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "srid")
    private Integer secondaryReasonId;
    private String reason;
    @Column(name = "create_time")
    private Date createdAt;
    @Column(name = "update_time")
    private Date updatedAt;
    private String type;
    @Column(name = "dispensing_required")
    private String dispensingRequired;
}
