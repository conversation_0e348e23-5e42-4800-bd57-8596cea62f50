package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "reverse_courier_mapping")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReverseCourierMapping {

    @Id
    @Column(name = "id", nullable = false, unique = true)
    Integer id;

    @Column(name = "courier", nullable = false)
    String courier;

    @Column(name = "cp_id")
    Integer cpId;

    @Column(name = "cp_name")
    String cpName;

    @Column(name = "updated_at")
    Date updatedAt;
}
