package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "frame_broken_approval")
@Data
public class FrameBrokenApproval {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer repairId;
    private Integer incrementId;
    private Integer itemId;
    private String ticketId;
    private BigDecimal gvAmountRequested;
    private String status;
    private String notes;
    private String mobile;
    private Long customerId;
    private String reason;
    @CreationTimestamp
    private Date createdAt;
    @UpdateTimestamp
    private Date updatedAt;
}

