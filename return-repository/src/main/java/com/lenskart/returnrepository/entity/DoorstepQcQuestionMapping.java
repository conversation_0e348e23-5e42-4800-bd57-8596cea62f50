package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name="doorstep_qc_question_mapping")
public class DoorstepQcQuestionMapping {

    @Column(name = "id")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "rvp_reason")
    private String rvpReason;

    @Column(name = "classification")
    private Integer classification;

    @Column(name = "reason")
    private Integer reason;

    @Column(name = "period", nullable = false, columnDefinition = "enum('return','warranty')")
    private String period;

    @Column(name = "special_instruction")
    private String specialInstruction;

    @Column(name = "is_qc_required")
    private boolean isQcRequired;

    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "updated_at")
    private Date updatedAt;

}
