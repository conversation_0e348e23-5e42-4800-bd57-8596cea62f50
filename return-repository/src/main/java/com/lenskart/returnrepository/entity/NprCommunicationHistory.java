package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "npr_communication_history")
public class NprCommunicationHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, unique = true)
    private Integer id;

    @Column(name = "return_id", nullable = false)
    private Integer returnId;

    @Column(name = "npr_status_code", nullable = false)
    private int nprStatusCode;

    @Column(name = "npr_status_description")
    private String nprStatusDescription;

    @Column(name = "pickup_attempt_no", nullable = false)
    private int pickupAttemptNo;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false)
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    @Column(name = "user_action")
    private String userAction;

    @Column(name = "phone_no")
    private String phoneNo;

    @Column(name = "product_id")
    private Integer productId;

    @Column(name = "unique_id")
    private String uniqueId;

    @Column(name = "user_action_status")
    private String userActionStatus;

    @Column(name = "comment")
    private String comment;

    @Column(name = "source")
    private String source;
}
