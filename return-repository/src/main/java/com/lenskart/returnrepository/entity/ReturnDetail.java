package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "return_detail")
@Data
public class ReturnDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer requestId;
    private Integer incrementId;
    private Long groupId;
    @Column(name = "agent_email", nullable = false, columnDefinition = "varchar(100) default 'Unassigned'")
    private String agentEmail = "Unassigned";
    private String unicomOrderCode;
    private Date returnCreateDatetime;
    private String returnType;
    private String returnMethod;
    private Boolean isInsurance;
    private Integer isQcAtDoorstep;
    private Boolean isAutoCancelEnable;
    private String receivingFlag;
    private String source;
    private String facilityCode;
    private String bulkType;
    private Integer reversePuFollowupCnt;
    private Date lastFollowupDatetime;
    @Transient
    private String status;
    @Transient
    private String reverseCourier;
    @Transient
    private Date lastUpdateDatetime;
    @Transient
    private String overrideComment;
}
