package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.ToString;

@Entity
@Table(name = "refund_rules")
@Data
@ToString
public class RefundRules {
    @Id
    private Integer id;
    private String reverseType;
    private String navChannel;
    private String returnInitiatedSource;
    private String triggerPoint;
    private Boolean isPsuedoGatepass = false;
    private Boolean isBranded = false;
    private Boolean isQcPass;
    private String category;
    private Integer warantyFrom = 0;
    private Integer warantyTo = 0;
    private Boolean isAccessoryMissing = false;
    private Boolean isLastPiece = false;
    private Boolean isLensOnly = false;
    private String countryCode;
    private Boolean insurancePolicy;
    private Long amountFrom = null;
    private Long amountTill = null;
    private Boolean blacklistedPhoneNumbers;
    private Boolean blacklistedPincodes;
    public Boolean isReturnable = true;
    private String action;
    private Boolean doRefund = true;
    private String refundMethod;
    private String draftReturnMethod;
    private Integer exchangeAllowed;
    private String exchangeOrderDispatch;
    private String returnReasons;
    private String paymentMethod;
    private Integer returnEligibilityPeriod = 30;
    private String refundDispatch = "CourierPickup";
    private Boolean overrideWarrantyPeriod;
    private Integer customerScoreFrom;
    private Integer customerScoreTo;
    private Integer storeScoreFrom;
    private Integer storeScoreTo;
    @Column(name = "l1_descriptor_code")
    private String l1DescriptorCode;
}
