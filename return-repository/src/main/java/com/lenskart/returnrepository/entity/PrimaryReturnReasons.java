package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(
        name = "primary_return_reason"
)
@Data
public class PrimaryReturnReasons implements Serializable {

    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    @Column(
            name = "prid",
            unique = true,
            nullable = false
    )
    private Integer primaryReasonId;

    @Column(
            name = "reason"
    )
    private String reason;


    @Column(
            name = "created_time"
    )
    private Date createdAt;

    @Column(
            name = "update_time"
    )
    private Date updatedAt;

    @Column(name = "type",nullable = true)
    private String type;

    @Transient
    private Boolean isInsuranceReason;
}
