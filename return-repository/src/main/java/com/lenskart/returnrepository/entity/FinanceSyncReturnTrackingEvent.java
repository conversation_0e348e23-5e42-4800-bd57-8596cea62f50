package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "fl_return_tracking")
public class FinanceSyncReturnTrackingEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "return_id")
    private Integer returnId;

    @Column(name = "uw_item_id")
    private Integer uwItemId;

    @Column(name = "identifier_type")
    private String identifierType;

    @Column(name = "status")
    private String status;

    @Column(name = "status_remarks")
    private String statusRemarks;

    @Column(name = "retry_count")
    @Builder.Default
    private Integer retryCount = 0;

    @CreationTimestamp
    @Column(name = "created_at")
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private Date updatedAt;

}
