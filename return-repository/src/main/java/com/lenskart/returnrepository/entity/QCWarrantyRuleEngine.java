package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;

@Entity
@Data
@Table(
        name = "qc_warranty_rule_engine",
        catalog = "inventory"
)
public class QCWarrantyRuleEngine implements Serializable {
    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    private Long id;
    @Column(
            name = "rule_type"
    )
    private String ruleType;
    @Column(
            name = "refund_intent"
    )
    private String refundIntent;
    @Column(
            name = "item_value_from"
    )
    private Double itemValueFrom;
    @Column(
            name = "item_value_to"
    )
    private Double itemValueTo;
    @Column(
            name = "is_branded"
    )
    private Boolean isBranded;
    @Column(
            name = "is_blacklisted"
    )
    private Boolean isBlacklisted;
    @Column(
            name = "from_days"
    )
    private Integer fromDays;
    @Column(
            name = "to_days"
    )
    private Integer toDays;
    @Column(
            name = "customer_profiling_score_from"
    )
    private Double customerProfilingScoreFrom;
    @Column(
            name = "customer_profiling_score_to"
    )
    private Double customerProfilingScoreTo;
    @Column(
            name = "return_count_greater_than"
    )
    private Integer returnCountGreaterThan;
    @Column(
            name = "return_percentage"
    )
    private Double returnPercentage;
    @Column(
            name = "refund_dispatch_point"
    )
    private String refundDispatchPoint;
    @Column(
            name = "active"
    )
    private Boolean active;
}
