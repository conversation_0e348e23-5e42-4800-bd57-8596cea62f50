package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Data
@Table(name = "return_status_timeline_mapping")
public class ReturnStatusTimelineMapping {
    @Column(
            name = "id",
            nullable = false,
            unique = true
    )
    @Id
    private Integer id;

    @Column(name = "status")
    private String status;

    @Column(name = "custumer_facing_status")
    private String customerFacingStatus;

    @Column(name = "timeline_header")
    private String timelineHeader = "";

    @Column(name = "timeline_subheader_predictive")
    private String timelineSubheaderPredective="";

    @Column(name = "timeline_subheader_actual")
    private String timelineSubheaderActual="";
}
