package com.lenskart.returnrepository.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "return_detail_sale_details")
public class ReturnDetailSaleDetails {
    @Id
    @Column(name = "return_id", nullable = false)
    private Integer returnId;

    @Column(name = "salesman_name")
    private String salesmanName;

    @Column(name = "salesman_number")
    private String salesmanNumber;

    @Column(name = "callback_required_to_salesman", columnDefinition = "TINYINT(1) DEFAULT 0")
    private Boolean callbackRequiredToSalesman;

    @Column(name = "store_facility_code")
    private String storeFacilityCode;
}
