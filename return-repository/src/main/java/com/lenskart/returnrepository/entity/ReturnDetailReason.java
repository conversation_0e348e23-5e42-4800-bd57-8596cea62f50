package com.lenskart.returnrepository.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(
        name = "return_detail_reason"
)
@Data
public class ReturnDetailReason implements Serializable {

    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    @Column(
            name = "returnreasonid",
            unique = true,
            nullable = false
    )
    private Integer returnReasonId;
    @Column(
            name = "order_id",
            nullable = false
    )
    private Integer orderId;
    @Column(
            name = "uw_item_id",
            nullable = false
    )
    private Integer uwItemId;
    @Column(
            name = "source",
            nullable = false,
            columnDefinition = "enum('reverselabel','reversepickuprequest','followuppanel','awaited_rto')"
    )
    private String source;
    @Column(
            name = "primary_reason",
            nullable = false
    )
    private String primaryReason;
    @Column(
            name = "secondary_reason",
            nullable = false
    )
    private String secondaryReason;
    @Column(
            name = "user",
            nullable = false
    )
    private String user;
    @Column(
            name = "created_at",
            nullable = false
    )
    private Date createdAt;
    @Column(
            name = "return_id",
            nullable = false
    )
    private Integer returnId;
    @Column(
            name = "type"
    )
    private String type;
}
