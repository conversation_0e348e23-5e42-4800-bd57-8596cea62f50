package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(
        name = "reship_item_detail"
)
@Data
public class ReshipItemDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer incrementId;
    private Integer uwItemId;
    @Column(name = "reship_type", nullable = true, columnDefinition = "enum('rto','reverse_reship') default 'rto' ")
    private String reshipType;
    private Date createAt;
    private String unicomOrderCode;
    private String productDeliveryType;
    private Integer isReceived = 0;
    private Integer returnId;
}
