package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.returncommon.model.enums.PaymentMode;
import com.lenskart.returncommon.model.enums.RuleConditionType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Data
@Entity
@Table(name = "cancel_and_convert_rule")
public class CancelAndConvertRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_mode", nullable = false)
    private PaymentMode paymentMode;

    @Column(name = "conditions", columnDefinition = "TEXT")
    private String conditions;  // Store conditions as a JSON string

    @Column(name = "action", nullable = false)
    private String action;

    @Column(name = "active", nullable = false)
    private Boolean active;

    @Transient
    private Map<RuleConditionType, Boolean> conditionsMap;

    public CancelAndConvertRule() {

    }

    public Map<RuleConditionType, Boolean> getConditionsMap() {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(conditions, new TypeReference<Map<RuleConditionType, Boolean>>() {
            });
        } catch (IOException e) {
            log.error("Exception occurred while getting conditionsMap", e);
            return null;
        }
    }

    public void setConditionsMap(Map<RuleConditionType, Boolean> conditionsMap) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            this.conditions = objectMapper.writeValueAsString(conditionsMap);
        } catch (JsonProcessingException e) {
            log.error("Exception occurred while setting conditionsMap", e);
        }
    }

}
