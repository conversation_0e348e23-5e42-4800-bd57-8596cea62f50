package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Data
@Entity
@IdClass(ReversePickupPincodeId.class)
@Table(name = "reverse_pickup_pincode")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReversePickupPincode {
    @Id
    @Column(name = "courier", nullable = false)
    String courier;

    @Id
    @Column(name = "pincode", nullable = false)
    Integer pincode;

    @Column(name = "priority", nullable = false)
    Integer priority;

    @Column(name = "status", nullable = false)
    String status;

    @Column(name = "tat", nullable = false)
    Integer tat;

    @Column(name = "cp_id")
    Integer cpId;

    @Column(name = "cp_name")
    String cpName;

    @Getter
    @Setter
    @Column(name = "is_qc_at_doorstep")
    Boolean isQcAtDoorstep;
}
