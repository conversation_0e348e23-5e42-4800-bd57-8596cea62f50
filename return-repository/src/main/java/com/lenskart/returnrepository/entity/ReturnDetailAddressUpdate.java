package com.lenskart.returnrepository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.persistence.*;
import java.sql.Timestamp;

@Entity
@Table(
        name = "return_detail_address_update"
)
@Data
public class ReturnDetailAddressUpdate {
    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    @Column(
            name = "id",
            nullable = false,
            unique = true
    )
    Integer id;
    @Column(
            name = "increment_id",
            nullable = false,
            unique = true
    )
    @JsonProperty("increment_id")
    Integer increment_id;
    @Column(
            name = "group_id",
            nullable = false
    )
    @JsonProperty("group_id")
    Integer groupId;
    @Column(
            name = "firstname",
            nullable = false
    )
    @JsonProperty("firstname")
    String firstName;
    @Column(
            name = "lastname",
            nullable = false
    )
    @JsonProperty("lastname")
    String lastName;
    @Column(
            name = "street1",
            nullable = false
    )
    String street1;
    @Column(
            name = "street2",
            nullable = false
    )
    String street2;
    @Column(
            name = "city",
            nullable = false
    )
    String city;
    @Column(
            name = "email",
            nullable = false
    )
    String email;
    @Column(
            name = "telephone",
            nullable = false
    )
    String telephone;
    @Column(
            name = "state",
            nullable = false
    )
    String state;
    @Column(
            name = "postcode",
            nullable = false
    )
    Integer postcode;

    @Column(
            name = "country_id",
            nullable = false
    )
    @JsonProperty("country_id")
    String country;
    @Column(
            name = "created_at",
            columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    )
    @JsonProperty("created_at")
    Timestamp createdAt;
}
