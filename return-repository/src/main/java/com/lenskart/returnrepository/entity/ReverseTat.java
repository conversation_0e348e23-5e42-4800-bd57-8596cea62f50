package com.lenskart.returnrepository.entity;


import lombok.Data;

import jakarta.persistence.*;

@Entity
@Table(name="reverse_tat")
@Data
public class ReverseTat {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, unique = true)
    Integer id;

    @Column(name = "pincode")
    String pincode;

    @Column(name = "reverse_pickup_days")
    Integer reversePickupDays;

    @Column(name = "reverse_delivery_days")
    Integer reverseDeliveryDays;
}
