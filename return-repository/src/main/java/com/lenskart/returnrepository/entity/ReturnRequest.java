package com.lenskart.returnrepository.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "return_request")
@Data
public class ReturnRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer incrementId;
    private String identifierType;
    private String identifierValue;
    private String returnReason;
    private String source;
    private String agentId;
    private Date createdAt;
    private String returnIntention;
    private String returnCreationRequest;
    @Column(name = "exchange_dispatch")
    private String exchangeDispatch;
    @Column(name = "refund_dispatch")
    private String refundDispatch;
}