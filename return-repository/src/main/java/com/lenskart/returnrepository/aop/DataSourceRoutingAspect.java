package com.lenskart.returnrepository.aop;

import com.lenskart.returnrepository.annotation.Database;
import com.lenskart.returnrepository.datasource.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
@Order(-10)
public class DataSourceRoutingAspect {

    @Pointcut("execution(* org.springframework.data.repository.CrudRepository+.*(..))")
    public void interceptRepositoryMethods() {
    }

    @Around("interceptRepositoryMethods()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Class<?> repositoryInterface = joinPoint.getTarget().getClass();
        Database annotation = AnnotationUtils.findAnnotation(repositoryInterface, Database.class);

        if (annotation != null) {
            DataSourceContextHolder.setDatabaseName(annotation.value());
            log.debug("Using [{}] DataSource", annotation.value());
        } else {
            log.debug("Using [default] DataSource");
        }

        try {
            return joinPoint.proceed(); // Proceed with the intercepted method
        } finally {
            if (null != DataSourceContextHolder.getDatabaseName()) {
                DataSourceContextHolder.removeDatabaseName();
            }
        }
    }
}
