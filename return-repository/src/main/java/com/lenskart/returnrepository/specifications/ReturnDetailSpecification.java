package com.lenskart.returnrepository.specifications;

import com.lenskart.returnrepository.entity.*;
import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class ReturnDetailSpecification {

    public static Specification<ReturnDetail> getFilteredReturns(String status, Integer orderNo, String reverseCourier, String facility, Date startDate, Date endDate, List<String> eventTypes) {

        return (Root<ReturnDetail> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Join with return_courier_detail
            Root<ReturnCourierDetail> rcd = query.from(ReturnCourierDetail.class);
            predicates.add(cb.equal(rcd.get("returnId"), root.get("id"))); // Ensuring correct join condition

            Root<ReturnEvent> re = query.from(ReturnEvent.class);

            // Subquery to find the latest event for each returnId
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<ReturnEvent> subRoot = subquery.from(ReturnEvent.class);
            subquery.select(cb.max(subRoot.get("id"))) // Latest event per returnId
                    .where(
                            cb.equal(subRoot.get("returnId"), root.get("id")), // Match returnId
                            subRoot.get("event").in(eventTypes)  // Filter by allowed event types
                    );

            // Ensure we're selecting only the latest event for each returnId
            predicates.add(cb.equal(re.get("id"), subquery));


            if (facility != null) {
                predicates.add(cb.equal(root.get("facilityCode"), facility));
            }

            if (orderNo != null) {
                predicates.add(cb.equal(root.get("incrementId"), orderNo));
            }

            if (reverseCourier != null) {
                predicates.add(cb.equal(rcd.get("reverseCourier"), reverseCourier));
            }

            if (startDate != null && endDate != null) {
                predicates.add(cb.between(root.get("returnCreateDatetime"), startDate, endDate));
            }

            if (status == null || status.isEmpty()) {
                predicates.add(re.get("event").in(
                        "new_reverse_pickup".toUpperCase(), "vendor_sync_pending".toUpperCase(), "auto_sync_failed".toUpperCase(),
                        "reference_id_issued".toUpperCase(), "awb_assigned".toUpperCase(), "sc_refunded_receiving_pending".toUpperCase(),
                        "customer_cancelled".toUpperCase(), "easy_refund_given_pickup_cancelled".toUpperCase(),
                        "partial_easy_refund_given_pickup_cancelled".toUpperCase()
                ));
//                LocalDateTime tenDaysAgo = LocalDateTime.now().minusDays(10);
//                Date tenDaysAgoDate = Date.from(tenDaysAgo.atZone(ZoneId.systemDefault()).toInstant());

//                predicates.add(cb.greaterThan(root.get("returnCreateDatetime"), tenDaysAgoDate));
            } else if ("new_reverse_pickup".equals(status)) {
                predicates.add(re.get("event").in("new_reverse_pickup".toUpperCase(), "sc_refunded_receiving_pending".toUpperCase()));
            } else if ("new_reverse_pickup_for_hto".equals(status)) {
                predicates.add(cb.equal(re.get("event"), "new_reverse_pickup".toUpperCase()));
            } else {
                predicates.add(cb.equal(re.get("event"), status.toUpperCase()));
            }

            query.orderBy(cb.desc(root.get("returnCreateDatetime")));
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<ReturnOrder> getFilteredReturnOrders(String status, Integer orderNo, String reverseCourier, String facility, Date startDate, Date endDate) {

        return (Root<ReturnOrder> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (facility != null) {
                predicates.add(cb.equal(root.get("facilityCode"), facility));
            }

            if (orderNo != null) {
                predicates.add(cb.equal(root.get("orderNo"), orderNo));
            }

            if (reverseCourier != null) {
                predicates.add(cb.equal(root.get("reverseCourier"), reverseCourier));
            }

            if (startDate != null && endDate != null) {
                predicates.add(cb.between(root.get("returnCreateDatetime"), startDate, endDate));
            }

            if (status == null || status.isEmpty()) {
                predicates.add(root.get("status").in(
                        "new_reverse_pickup", "vendor_sync_pending", "auto_sync_failed",
                        "reference_id_issued", "awb_assigned", "sc_refunded_receiving_pending",
                        "customer_cancelled", "easy_refund_given_pickup_cancelled",
                        "partial_easy_refund_given_pickup_cancelled"
                ));
                predicates.add(cb.equal(root.get("reversePickupFlag"), 1));
//                LocalDateTime tenDaysAgo = LocalDateTime.now().minusDays(10);
//                Date tenDaysAgoDate = Date.from(tenDaysAgo.atZone(ZoneId.systemDefault()).toInstant());

//                predicates.add(cb.greaterThan(root.get("returnCreateDatetime"), tenDaysAgoDate));
            } else if ("new_reverse_pickup".equals(status)) {
                predicates.add(root.get("status").in("new_reverse_pickup", "sc_refunded_receiving_pending"));
                predicates.add(cb.equal(root.get("reversePickupFlag"), 1));
            } else if ("new_reverse_pickup_for_hto".equals(status)) {
                predicates.add(cb.equal(root.get("status"), "new_reverse_pickup"));
                predicates.add(cb.notEqual(root.get("easyRefundGiven"), "yes"));
            } else {
                predicates.add(cb.equal(root.get("status"), status));
                predicates.add(cb.equal(root.get("reversePickupFlag"), 1));
            }


            query.orderBy(cb.desc(root.get("returnCreateDatetime")));
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}

