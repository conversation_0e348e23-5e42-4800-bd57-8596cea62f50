<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.lenskart</groupId>
		<artifactId>return</artifactId>
		<version>0.1.86</version>
	</parent>
	<artifactId>return-repository</artifactId>
	<version>${return.repository.version}</version>
	<name>return-repository</name>
	<packaging>jar</packaging>
	<description>return repository project</description>
	<repositories>
		<repository>
			<id>internal</id>
			<url>http://archiva-new.prod.internal:8080/repository/internal/</url>
		</repository>
	</repositories>
	<properties>
		<spring-boot.repackage.skip>true</spring-boot.repackage.skip>
	</properties>
	<dependencies>
		<dependency>
			<groupId>javax.persistence</groupId>
			<artifactId>javax.persistence-api</artifactId>
			<version>2.2</version>
		</dependency>
		<dependency>
			<groupId>jakarta.persistence</groupId>
			<artifactId>jakarta.persistence-api</artifactId>
			<version>3.1.0</version>
		</dependency>
		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>javax.transaction-api</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>3.10.6</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>6.1.7.Final</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-ehcache</artifactId>
			<version>5.6.15.Final</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.lenskart</groupId>
			<artifactId>return-common</artifactId>
			<version>${return.common.version}</version>
		</dependency>

	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.hibernate.orm</groupId>
				<artifactId>hibernate-core</artifactId>
				<version>6.1.7.Final</version>
			</dependency>
			<dependency>
				<groupId>org.jsondoc</groupId>
				<artifactId>spring-boot-starter-jsondoc</artifactId>
				<version>1.2.17</version>
			</dependency>
			<dependency>
				<groupId>org.hibernate</groupId>
				<artifactId>hibernate-core</artifactId>
				<version>5.6.15.Final</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<source>17</source>
					<target>17</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.30</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>3.4.1</version>

				<executions>
					<execution>
						<id>enforce-no-circular-dependencies</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<dependencyConvergence/>
								<bannedDependencies>
									<excludes>
										<exclude>com.lenskart:return-api</exclude>
										<exclude>com.lenskart:return-consumer</exclude>
										<exclude>com.lenskart:return-job</exclude>
										<exclude>com.lenskart:return-service</exclude>
									</excludes>
								</bannedDependencies>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
